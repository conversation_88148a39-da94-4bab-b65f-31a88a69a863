# 链接目录

目录下放置系统中会使用的各种链接，包括站内路由地址的引用和站外地址的链接。

以模块为粒度进行组织，如在 `welcome.ts` 中放置和 Welcome 模块有关的内容。

目前链接还不多，暂时不做细分。等链接增加后再拆分文件。

## 使用方法

新增路由

```ts
docs: createTemplate('/docs')
```

使用路由

```tsx
import { links } from 'src/links';

{/* 使用字面量 */}
<Route path={urls.app.raw()} element={<Console />}>
<Link to={urls.docs.raw()}>文档</Link>

{/* 拼接动态路由路径:   app/flow/123  */}
<Link to={urls.agentFlowEdit.fill({id: 123})}>编辑智能体</Link>

{/* 拼接路径： app/test */}
<Link to={urls.app.concat('test')}>文档</Link>

{/* 返回所有子路径： app/* */}
<Link to={urls.app.splat()}>文档</Link>
```
