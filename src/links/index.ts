/**
 * v2 版本的路由
 */
import {createTemplate} from './template';

/**
 * 路由命名规则：[大类][小类][动作][ID]，如 agentPromptEdit，/agent/prompt/edit/:id
 */
export default {
    /**
     * 首页
     */
    root: createTemplate('/'),

    /**
     * 体验中心
     */
    center: createTemplate('/center'),
    centerSearch: createTemplate('/center/search'),
    centerSearchWithKey: createTemplate('/center/search/:key'),
    centerAgentPreview: createTemplate('/center/agent/preview/:id'), // 体验中心 agent 对话预览

    /**
     * 榜单
     */
    rank: createTemplate('/rank/:id'),

    /**
     * 插件商店
     */
    centerPlugin: createTemplate('/center/plugin'),
    centerPluginDetail: createTemplate('/center/plugin/:id'),

    /**
     * 智能体 Agent
     */
    agent: createTemplate('/agent'),
    /** 我的智能体列表 动态tab 路由, tab = 'codeless' | 'lowcode' | 'qianfan'，codeless-零代tab，lowcode-低代码tab，qianfan-千帆 */
    agentList: createTemplate('/agent/list/:tab'),
    agentCreate: createTemplate('/agent/create'),
    agentPreview: createTemplate('/agent/preview/:id'), // 用户已上线 agent 对话预览

    /**
     * 智能体 - prompt 智能体
     */
    /** 智能体编辑页 */
    agentPromptEdit: createTemplate('/agent/prompt/edit'),
    /** 智能体快速创建页 */
    agentPromptQuickStart: createTemplate('/agent/prompt/quickStart'),
    // 创建城市智能体活动页
    agentCreateCityAgent: createTemplate('/agent/createCityAgent'),
    // 城市智能体列表页
    cityAgentList: createTemplate('/agent/cityAgentList'),

    // 智能体详情页 动态路由: tab = 'overview' | 'output'，overview-概览tab, output-对外部署tab
    // tab = 'overview' 零代码概览页 已于2024.4 下线
    agentPromptDetail: createTemplate('/agent/prompt/:tab/:id'),
    agentPromptConfig: createTemplate('/agent/prompt/config/:id'), // 智能体公开配置查看
    // 移动端扫 PC 端二维码后打开的录制页
    agentCreateAudio: createTemplate('/agent/createAudio'),
    // 新版语音录制页，供平台外使用，需登录后才可使用
    createAudioV2: createTemplate('/createAudioV2'),
    createDynamicV2: createTemplate('/createDynamicV2'),

    /**
     * 智能体 - flow 编排智能体
     */
    agentFlow: createTemplate('/agent/flow'),
    agentFlowEdit: createTemplate('/agent/flow/edit/:appId'), // 应用编排
    // 智能体详情页, 动态路由: tab = 'overview' | 'version' | 'output'，overview-预览tab, version-版本tab，output-对外部署tab
    agentFlowDetail: createTemplate('/agent/flow/:tab/:id'),
    // （待下线）
    agentFlowVersion: createTemplate('/agent/flow/:id/version'),

    /**
     * 智能体 - 千帆
     */
    agentQianfan: createTemplate('/agent/qianfan'),
    // 千帆智能体详情页（目前只有 "分析"）
    agentQianfanDetail: createTemplate('/agent/qianfan/overview/:id'),

    // 最近使用的智能体列表
    agentRecent: createTemplate('/agent/recent'),

    agentAISDK: createTemplate('/aisdk'), // 智能客服

    /**
     * 付费咨询
     */
    counseling: createTemplate('/counseling'),

    /**
     * 插件 Plugin
     */
    plugin: createTemplate('/plugin'), // 插件列表
    pluginPreview: createTemplate('/bot'), // 插件对话预览
    // 插件详情页（能力与数据复用）, 动态路由: type = 'ability' | 'data'; tab = 'overview' | 'version'
    pluginDetail: createTemplate('/plugin/:type/:tab/:id'),
    // 插件版本页（能力与数据复用）, 动态路由: type = 'ability' | 'data'（待下线）
    pluginVersion: createTemplate('/plugin/:type/version/:id'),

    /**
     * 插件 - 能力插件
     */
    pluginAbilityList: createTemplate('/plugin/ability'),
    pluginAbilityCreate: createTemplate('/plugin/ability/create'),
    pluginAbilityEdit: createTemplate('/plugin/ability/edit/:id'),
    pluginAbilityPreviewRelease: createTemplate('/plugin/ability/preview/release/:pluginSessionId'),
    pluginAbilityPreviewAudit: createTemplate('/plugin/ability/preview/audit/:pluginSessionId'),
    pluginAbilityPreviewShare: createTemplate('/plugin/ability/preview/share/:pluginSessionId'),

    /**
     *  插件 - 数据插件
     */
    pluginDataList: createTemplate('/plugin/data'),
    pluginDataPreviewRelease: createTemplate('/plugin/data/preview/release/:pluginSessionId'),
    pluginDataPreviewAudit: createTemplate('/plugin/data/preview/audit/:pluginSessionId'),

    workflowList: createTemplate('/workflow/list'),
    workFlowEdit: createTemplate('/workflow/edit/:workflowId'), // 工作流编排

    /**
     * 插件 - 应用类插件（仅包含通用框架自主开发应用，待下线）
     */
    pluginAppList: createTemplate('/plugin/app'), // 应用列表
    // 动态路由: tab = 'overview' | 'version'
    pluginAppDetail: createTemplate('/plugin/app/:tab/:id'),
    // 待下线
    pluginAppVersion: createTemplate('/plugin/app/version/:id'),

    /**
     * 知识库管理
     */
    dataset: createTemplate('/dataset'),
    datasetList: createTemplate('/dataset/list'),
    datasetCreate: createTemplate('/dataset/create'),
    datasetFileDetail: createTemplate('/dataset/detail/file/:id'),
    datasetParagraphDetail: createTemplate('/dataset/detail/paragraph/:id'),
    datasetUpdate: createTemplate('/dataset/update/:id'),
    datasetFileUpdate: createTemplate('/dataset/file/update/:id'),
    datasetWangpanAuth: createTemplate('/wangpan-auth'),

    /**
     * 收益管理
     */
    income: createTemplate('/income'),
    /** 收益详情 动态路由 type='distribution' | 'leads'| 'reward'| 'promo' */
    incomeDetail: createTemplate('/income/detail/:type'),
    /** 提现详情 */
    incomePay: createTemplate('/income/pay'),

    /**
     * 文档
     */
    docs: createTemplate('/docs'),

    /**
     * 协议
     */
    agreement: createTemplate('/agreement'),

    /**
     * 资质管理&账号中心，entity路由待认证升级后下线
     */
    entity: createTemplate('/entity'),
    entityUpdate: createTemplate('/entity/update/:id'),
    // 账号中心
    account: createTemplate('/account'),
    // 机构类型选择
    accountOrgList: createTemplate('/account/org/list'),
    // 机构认证提交
    accountOrgSubmit: createTemplate('/account/org/submit'),
    // 机构真实性认证
    accountOrgAuth: createTemplate('/account/org/auth'),
    /**
     * 赛事平台
     */
    activity: createTemplate('/activity'),
    // 赛事中心
    activityCenter: createTemplate('/activity/center'),
    // 赛事详情
    nvidiaActivityDetail: createTemplate('/activity/detail'),
    // 表单-提交作品
    activitySubmitWork: createTemplate('/activity/submit/work'),
    // 表单-决赛作品
    activityFinalWork: createTemplate('/activity/submit/final-work'),
    // 表单-立即报名
    activityDeveloperApply: createTemplate('/activity/submit/developer-apply'),
    // 表单-专家应征
    activityExpertApply: createTemplate('/activity/submit/expert-apply'),
    // 我要组队页面
    activityTeam: createTemplate('/activity/team'),
    // 通用活动中心首页-活动列表
    activityList: createTemplate('/activity/list'),
    // 通用活动中心-活动详情页
    activityDetail: createTemplate('/activity/detail/:id'),
    // 通用活动中心-表单详情页
    activityFormDetail: createTemplate('/activity/form/detail'),
    // 通用活动中心-参与记录
    activityRecord: createTemplate('/activity/record'),
    /**
     * 教育名师招募
     */
    activityMasterRecruitment: createTemplate('/activity/masterRecruitment'),

    // 任务中心-任务记录列表
    activityTaskRecord: createTemplate('/activity/task/record'),
    /**
     * 开发者赚钱季大赛
     */
    activityDp: createTemplate('/activity/developerEarning'),
    // 赛事中心
    activityDpCenter: createTemplate('/activity/developerEarning/center'),
    // 赛事详情
    activityDpDetail: createTemplate('/activity/developerEarning/detail/'),
    // 表单-提交作品
    activityDpSubmitWork: createTemplate('/activity/developerEarning/submit/work'),
    // 表单-立即报名
    activityDpDeveloperApply: createTemplate('/activity/developerEarning/submit/developer-apply'),

    // 插件编辑（旧-已废弃）
    pluginEditOld: createTemplate('/plugin/edit/:id'),
    // 插件编辑（旧-已废弃）
    pluginCreateOld: createTemplate('/plugin/edit'),

    /** 对服务商授权页面 */
    bindTpAuthorization: createTemplate('/authorize'),

    /** 服务商的授权管理 */
    authorizeManagement: createTemplate('/authorize/management'),

    /** 消息中心 */
    noticeCenter: createTemplate('/notice'),
    /** 消息列表 */
    noticeList: createTemplate('/notice/list/:tab'),

    /** super APP内嵌h5 */
    superAppH5: createTemplate('/edupage'),
    /** 视频脚本页 */
    videoScript: createTemplate('/edupage/video-script'),
    teacherInfoConfig: createTemplate('/edupage/teacher-info-config'),

    /**
     * 统一错误页
     */
    error: createTemplate('/error'),

    /**
     * 视频设置
     */
    videoSettings: createTemplate('/video/settings/:appId'),
};
