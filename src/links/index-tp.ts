/**
 * @file 服务商平台路由
 * <AUTHOR>
 */
import {createTemplate} from './template';

/**
 * 路由命名规则：[大类][小类][动作][ID]，如 agentPromptEdit，/agent/prompt/edit/:id
 */
export default {
    /** 首页 */
    root: createTemplate('/tp'),

    /** 商家列表 */
    customerList: createTemplate('/tp/customer/list'),

    /** 统一错误页 */
    error: createTemplate('/error'),

    /**
     * 资质管理
     */
    entity: createTemplate('/entity'),
    entityUpdate: createTemplate('/entity/update/:id'),
    // 账号中心
    account: createTemplate('/account'),
};
