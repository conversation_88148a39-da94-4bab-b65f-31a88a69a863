/**
 * copy from: https://console.cloud.baidu-int.com/devops/icode/repos/baidu/intelligent-dev/front-app/blob/master/src/urls/template.ts
 */

import {generatePath} from 'react-router-dom';

type Params = Record<string, string>;

type AutoParams<S> = S extends `${string}:${infer B}/${infer C}`
    ? {[K in B]: string} & AutoParams<C>
    : S extends `${string}:${infer B}`
    ? {[K in B]: string}
    : {}; // eslint-disable-line @typescript-eslint/ban-types

export interface UrlTemplate<P extends Params | unknown> {
    raw(): string;

    fill(params: P): string;

    concat(path: string): string;

    splat(): string;
}

export const createTemplate = <T extends string>(template: T): UrlTemplate<AutoParams<T>> => {
    return {
        raw() {
            return template;
        },
        fill(params) {
            // @ts-expect-error `react-router-dom` 无法正确推导 `PathParams` 类型
            return generatePath(template, params);
        },
        concat(path) {
            return template + path;
        },
        splat() {
            return `${template}/*`;
        },
    };
};
