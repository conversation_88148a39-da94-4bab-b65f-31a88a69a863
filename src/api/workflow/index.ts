/**
 * @file 画布页的主要接口
 * <AUTHOR>
 */

import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {createInterface, request} from '../request';
import {
    ErrorInfo,
    FlowDetailResponse,
    SaveFlowParams,
    SaveFlowResponse,
    WorkflowProgress,
    VersionType,
} from './interface';

const CODE_TEST_TIMEOUT = 30000;

export const getWorkflowProcess = (workflowId: string, executeId: string): Promise<WorkflowProgress> =>
    request('GET', `/workflow/getProcess?workflowId=${workflowId}&executeId=${executeId}`);

export const debugSingleNode = createInterface<
    {workflowId: string; nodeId: string; input: Record<string, any>; xAgentHistory?: any},
    {
        executeId: string;
    }
>('POST', `/workflow/nodeDebug`);

export const runWorkflow = createInterface<
    {workflowId: string; input: Record<string, any>; xAgentHistory?: any},
    {
        executeId: string;
    }
>('POST', `/workflow/run`);

export const validateWorkflow = (flowJson: string): Promise<ErrorInfo[]> =>
    request('POST', `/workflow/validate`, {flowJson});

export const saveFlow = createInterface<SaveFlowParams, SaveFlowResponse>('POST', '/workflow/save');

export const getFlowDetail = createInterface<{workflowId: string; VersionType?: VersionType}, FlowDetailResponse>(
    'GET',
    '/workflow/detail'
);

export const getModelList = createInterface<void, string[]>('GET', '/workflow/model/list');

export const publishWorkflow = createInterface<{workflowId: string}, {status: 0 | 1; publishTime: number}>(
    'POST',
    SECURE_URL_ENUM.WorkflowPublish
);

export const getMessageList = createInterface<
    {agentId: string; count: number},
    Array<{query: any; answer: any; answerText: string; queryText: string}>
>('GET', '/workflow/getMessageList');

// 代码节点 IDE 运行测试
export const runCodeTest = createInterface<{code: string; input: string; language: number}, {output: string}>(
    'POST',
    '/workflow/codeTest',
    {
        timeout: CODE_TEST_TIMEOUT,
    }
);

// 导入工作流时获取工作流的 flowJson
export const importFlowJson = createInterface<{workflowId: string}, {flowJson: string}>(
    'GET',
    '/workflow/importFlowJson',
    {
        timeout: CODE_TEST_TIMEOUT,
    }
);
