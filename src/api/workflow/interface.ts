export enum WorkflowExecuteStatus {
    UNDO = 0,
    DOING = 1,
    SUCCESS = 2,
    FAIL = 3,
}

export interface TokenUsage {
    inputTokens: number;
    outputTokens: string;
    totalTokens: object;
}

export enum WorkflowNodeType {
    START = 'start',
    LLM = 'llm',
    HTTP = 'http',
    END = 'end',
    KNOWLEDGE = 'knowledge',
    TOOL = 'tool',
    BRANCH = 'branch',
    TEXT = 'text',
    CODE = 'code',
    VARIABLE = 'variable',
    MESSAGE = 'message',
    WORKFLOW = 'workflow_tool',
    SUGGESTION = 'suggestion',
    INTENTION = 'intention',
    PARAMS_EXTRACT = 'params_extract',
}

export interface NodeResult {
    nodeId: string;
    nodeName: string;
    nodeStatus: WorkflowExecuteStatus;
    nodeType: WorkflowNodeType;
    nodeExeCost: string;
    tokenUsage: TokenUsage;
    input: object;
    output: object;
    errorInfo: string;
}

export interface WorkflowProgress {
    executeId: string;
    executeStatus: WorkflowExecuteStatus;
    executeCost: string;
    executeMessage: string;
    tokenUsage: string;
    nodeResults: NodeResult[];
}

export enum ErrorType {
    node = 1, // 边错误
    edge = 2, // 节点错误
}

export interface NodeErrorInfo {
    type: ErrorType.node;
    message: string;
    nodeError: {
        nodeId: string;
    };
}

export interface EdgeErrorInfo {
    type: ErrorType.edge;
    message: string;
    edgeError: {
        target: string;
        source: string;
        sourceHandle: string;
    };
}

export type ErrorInfo = NodeErrorInfo | EdgeErrorInfo;

export interface SaveFlowParams {
    workflowId: string;
    name: string;
    description: string;
    icon?: string;
    flowJson: string;
    versionCodeOnSave: number;
}

export enum StyleStatus {
    UNCONFIGURED = 1,
    CONFIGURED = 2,
    NEED_UPDATE = 3,
}

export interface SaveFlowResponse {
    workflowId: string;
    versionCodeOnSave: number;
    saveTime: number;
    styleStatus?: StyleStatus;
}

export enum WorkFlowPublishStatus {
    developing = 1,
    published = 3,
}

export const WorkFlowPublishStatusText = {
    [WorkFlowPublishStatus.developing]: '开发中',
    [WorkFlowPublishStatus.published]: '已发布',
};

export enum WorkflowTag {
    Personal,
    Official,
}

export interface FlowDetailResponse {
    name: string;
    description: string;
    icon?: string;
    flowJson: string;
    status: WorkFlowPublishStatus;
    versionCodeOnSave: number;
    saveTime: number;
    workflowTag: WorkflowTag;
    publishTime: number;
    styleStatus?: StyleStatus;
}

// 工作流版本管理
export enum VersionType {
    PublishVersion = 1,
    EditVersion = 3,
}
