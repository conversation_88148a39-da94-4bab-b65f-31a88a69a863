/**
 * @file 线索收集模块主要接口
 * <AUTHOR>
 */

import {CustomOptions, request} from '@/api/request';
import {
    GetLeadsCountRes,
    GetLeadsDetailListParams,
    LeadsDetail,
    PhoneLeadsDetail,
    LeadsDetailListRes,
    PhoneLeadsDetailListRes,
    GetLeadsCountParams,
    UpdateLeadsDetailParams,
    UpdatePhoneDetailParams,
    LeadsDownloadParams,
    LeadsDownloadRes,
    PlaintextPermissionRes,
    LeadsTag,
    VerifyPhoneCodeParams,
    SendPhoneVerifyCodeParams,
    CheckLeadsExistRes,
    OpenPlaintextRes,
    CheckMedicalClueExistRes,
    CheckPhoneVerifyCodeParams,
    CheckPhoneVerifyRes,
} from './interface';

export default {
    /** 查询用户是否创建过线索表单 */
    checkLeadsExist: (): Promise<CheckLeadsExistRes> => request('GET', '/leads/checkSolutionExist'),
    /** 查询用户是否创建过医疗线索表单 */
    checkMedicalClueExist: (): Promise<CheckMedicalClueExistRes> => request('GET', '/leads/checkMedicalClueExist'),
    /** 获取表单线索收集图表数据 */
    getLeadsChartData: (params: GetLeadsCountParams): Promise<GetLeadsCountRes> =>
        request('GET', '/leads/count', params),
    /** 获取来电线索收集图表数据 */
    getPhoneLeadsChartData: (params: GetLeadsCountParams): Promise<GetLeadsCountRes> =>
        request('GET', '/phone/record/trend', params),
    /** 获取线索收集信息列表数据 */
    getLeadsDetailList: (params: GetLeadsDetailListParams): Promise<LeadsDetailListRes> =>
        request('GET', '/leads/list', params),
    /** 获取来电线索收集信息列表数据 */
    getPhoneLeadsDetailList: (params: GetLeadsDetailListParams): Promise<PhoneLeadsDetailListRes> =>
        request('GET', '/phone/record', params),
    /** 获取线索标签列表数据 */
    getLeadsTags: (): Promise<LeadsTag[]> => request('GET', '/business/leads/tagList'),
    /** 修改线索详情 */
    updateLeadsDetail: (params: UpdateLeadsDetailParams): Promise<LeadsDetail> =>
        request('POST', '/leads/update', params),
    /** 修改来电线索详情 */
    updatePhoneLeadsDetail: (params: UpdatePhoneDetailParams): Promise<PhoneLeadsDetail> =>
        request('POST', '/phone/record/update', params),
    /** 查询明文查看权限是否在有效期内 */
    getLeadsPlaintextPermission: (options?: CustomOptions): Promise<PlaintextPermissionRes> =>
        request('POST', '/sms/code/checkValid', {}, options || {}),
    /** 发送短信验证码 */
    sendPhoneVerifyCode: (params: SendPhoneVerifyCodeParams) =>
        request('POST', '/sms/code/send', params, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }),
    /** 校验短信验证码&开启明文 */
    openLeadsPlaintext: (params: VerifyPhoneCodeParams): Promise<OpenPlaintextRes> =>
        request('POST', '/sms/code/verify', params, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }),
    /** 判断手机号是否需要校验 */
    checkPhoneVerify: (params: CheckPhoneVerifyCodeParams): Promise<CheckPhoneVerifyRes> =>
        request('POST', '/sms/code/verifyPhone', params, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }),
    /** 下载线索列表数据 */
    downloadLeadsList: (params: LeadsDownloadParams): Promise<LeadsDownloadRes> =>
        request('GET', '/leads/detail/download', params),

    /** 下载来电线索列表数据 */
    downloadPhoneLeadsList: (params: LeadsDownloadParams): Promise<string> => request('GET', '/phone/export', params),
};
