/**
 * @file 线索收集模块接口interface
 * <AUTHOR>
 */

/** 查询线索天维度统计信息请求参数 */
export interface GetLeadsCountParams {
    /** 起始时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的0点 */
    startTime: number;
    /** 终止时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的24点 */
    endTime: number;
}

export interface LeadsCount {
    /** 日期字符串 eg: 2024/10/01 */
    date: string;
    /** 线索数量 */
    count: number;
}

/** 查询线索天维度统计信息返回 */
export interface GetLeadsCountRes {
    /** 总数 */
    total: number;
    /** 线索天维度统计信息列表 */
    everyDayCount: LeadsCount[];
}

/** 查询线索详情列表请求参数可选参数key枚举 */
export enum FilterParamsKeys {
    /** 线索表单名称（模糊查询） */
    SolutionName = 'solutionName',
    /** 线索备注（模糊查询） */
    Remark = 'remark',
    /** 用户联系电话（精确查找） */
    Phone = 'phone',
    /** 线索标签 */
    TagName = 'tagName',
    /** 用户IP省市（模糊查询） */
    IpAddress = 'ipAddress',
    /** 线索内容（模糊查询） */
    Content = 'content',
}

/** 查询线索详情列表请求参数可选参数类型定义 */
export interface FilterParams {
    /** 线索表单名称（模糊查询） */
    [FilterParamsKeys.SolutionName]?: string;
    /** 线索备注（模糊查询） */
    [FilterParamsKeys.Remark]?: string;
    /** 用户联系电话（精确查找） */
    [FilterParamsKeys.Phone]?: string;
    /** 线索标签 */
    [FilterParamsKeys.TagName]?: string;
    /** 用户IP省市（模糊查询） */
    [FilterParamsKeys.IpAddress]?: string;
    /** 线索内容（模糊查询） */
    [FilterParamsKeys.Content]?: string;
}

/** 查询线索详情列表请求参数 */
export interface GetLeadsDetailListParams extends FilterParams {
    /** 起始时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的0点 */
    startTime: number;
    /** 终止时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的24点 */
    endTime: number;
    /** 分页页码 */
    pageNo: number;
    /** 分页个数 */
    pageSize: number;
    /** 密文标识，是否查看未加密手机号 true-查看明文 false-查看密文 */
    plainTextFlag: boolean;
}

/** 来电线索详情 */
export interface PhoneLeadsDetail {
    /** 智能体Id */
    appId: string;
    /** 电话Id */
    phoneId: number;
    /** 线索记录Id */
    recordId: number;
    /** 智能体名称 */
    appName: string;
    /** 主叫号码 */
    ani: string;
    /** 被叫号码 */
    dnis: string;
    /** 录音链接 */
    recUrl: string;
    /** 通话开始时间 */
    startTime: string;
    /** 回访时间 */
    visitTime: string;
    /** 通话时长(秒) */
    duration: number;
    /** 标签名称 */
    tagName: string;
    /** 备注 */
    remark: string;
}

/** 线索详情 */
export interface LeadsDetail {
    /** 智能体Id */
    appId: string;
    /** 线索Id */
    leadsId: number;
    /** 线索表单Id */
    solutionId: number;
    /** 线索表单名称 */
    solutionName: string;
    /** 关联智能体 */
    appName: string;
    /** 线索提交时间（毫秒级） */
    submitTime: number;
    /** 用户联系电话 */
    phone: string;
    /** 线索内容 */
    content: string;
    /** 线索备注 */
    remark: string;
    /** 线索标签 */
    tagName: string;
    /** 回访时间(毫秒级) */
    visitTime: number;
    /** 来源渠道 */
    source: string;
    /** 用户IP省市 */
    ipAddress: string;
    /** 用户提问内容 */
    query: string;
}

/** 查询线索详情列表返回数据 */
export interface LeadsDetailListRes {
    pageNo: number;
    pageSize: number;
    total: number;
    /** 明细列表 */
    dataList: LeadsDetail[];
}

/** 查询来电线索详情列表返回数据 */
export interface PhoneLeadsDetailListRes {
    pageNo: number;
    pageSize: number;
    total: number;
    /** 明细列表 */
    list: PhoneLeadsDetail[];
}

/** 编辑线索标签/回放时间/备注请求参数 */
export interface UpdateLeadsDetailParams {
    /** 线索Id */
    leadsId: number;
    /** 密文标识，是否查看未加密手机号 true-查看明文 false-查看密文 */
    plainTextFlag: boolean;
    /** 线索标签 */
    tagName?: string;
    /** 回访时间 */
    visitTime?: number;
    /** 线索备注 */
    remark?: string;
}

/** 编辑来电线索标签/回放时间/备注请求参数 */
export interface UpdatePhoneDetailParams {
    /** 线索ID */
    id: number;
    /** 线索标签 */
    tagName?: string;
    /** 线索备注 */
    remark?: string;
    /** 密文标识，是否查看未加密手机号 true-查看明文 false-查看密文 */
    plainTextFlag: boolean;
}

/** 线索标签 */
export interface LeadsTag {
    /** 标签名称 */
    tagName: string;
    /** 标签颜色 */
    textColor: string;
    /** 标签背景色 */
    btnColor: string;
}

/** 发送/校验短信验证码业务类型枚举 */
export enum VerifyCodeBizType {
    /** 线索 */
    Leads = 'leads',
}

/** 发送短信验证码请求参数 */
export interface SendPhoneVerifyCodeParams {
    /** 用户联系电话 */
    phone: string;
    /**
     * 发送短信验证码业务类型枚举
     * 不传即通用短信发送服务，传了会根据不同biz业务做处理
     */
    bizType?: VerifyCodeBizType;
}

/** 验证短信验证码请求参数 */
export interface VerifyPhoneCodeParams {
    /** 用户联系电话 */
    phone: string;
    /** 验证码 */
    verifyCode: string;
    /**
     * 校验短信验证码业务类型枚举
     * 不传即通用短信验证码服务，传了会根据不同biz业务做处理
     */
    bizType?: VerifyCodeBizType;
}

/** 开启明文返回 */
export interface OpenPlaintextRes {
    /** 开启明文有效期（单位h） */
    validTime: number;
}

/** 判断手机号是否需要校验 请求参数 */
export interface CheckPhoneVerifyCodeParams {
    /** 用户联系电话 */
    phone: string;
    /**
     * 发送短信验证码业务类型枚举
     * 不传即通用短信发送服务，传了会根据不同biz业务做处理
     */
    bizType?: VerifyCodeBizType;
}

/** 判断手机号是否需要校验 返回 */
export interface CheckPhoneVerifyRes {
    /** 是否需要校验 true 校验；false 不校验 */
    isVerify: boolean;
    /** 开启明文有效期（单位h） */
    validTime: number;
}

/** 线索下载请求参数 */
export type LeadsDownloadParams = Omit<GetLeadsDetailListParams, 'pageNo' | 'pageSize'> & {
    pageNo?: number;
    pageSize?: number;
};

/** 线索下载请求返回数据 */
export interface LeadsDownloadRes {
    /** 文件下载地址 */
    downloadUrl: string;
}

/** 查询明文权限是否有效返回数据 */
export interface PlaintextPermissionRes {
    /** 明文权限 */
    valid: boolean;
}

/** 查询用户是否创建过线索表单返回数据 */
export interface CheckLeadsExistRes {
    /** 是否创建过线索表单 */
    hasLeadsSolution: boolean;
}

/** 查询用户是否创建过医疗线索表单返回数据 */
export interface CheckMedicalClueExistRes {
    /** 是否创建过医疗线索表单 */
    hasMedicalClue: boolean;
}
