import {ShareTag} from '../agentEdit/interface';
import {AllAuditStatus} from '../agentList/interface';

export interface GetAgentPreviewParams {
    appId: string;
}

export enum BuildType {
    /** 低代码 */
    LowCode = 1,
    /** 全代码 */
    FullCode = 2,
    /** 无代码 */
    Codeless = 3,
    /** 千帆 */
    Qianfan = 4,
}

export interface GetAgentPreviewResponse {
    /**
     * 是否作者本人
     */
    isOwner: boolean;
    /**
     * 应用ID
     */
    appId: string;
    /**
     * Agent Logo URL
     */
    logoUrl: string;
    /**
     * Agent  图标的文本信息
     */
    logoText: string;
    /**
     * Agent名称
     */
    name: string;
    /**
     * Agent描述
     */
    description: string;
    /**
     * 预览链接 URL
     */
    previewUrl: string;
    /**
     * Agent 类型
     * 1.低代码，2. 全代码，3.零代码，4 千帆
     */
    buildType: BuildType;
    /**
     * Agent 来源
     * 1.小程序平台，2灵境平台，3灵感中心，4 千帆
     */
    pluginSource: number;
    /**
     * 使用数量
     */
    useNum: string;

    /**
     * 是否公开配置 0 不公开、1 公开
     */
    shareTag: ShareTag;

    /**
     * 包状态
     */
    packageStatus: AllAuditStatus;
}
