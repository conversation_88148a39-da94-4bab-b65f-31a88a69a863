/**
 * @file 我的智能体预览
 * <AUTHOR>
 */

import {GetAgentPreviewParams, GetAgentPreviewResponse} from '@/api/myAgentPreview/interface';
import {createInterface} from '@/api/request';

export const getAgentPreview = createInterface<GetAgentPreviewParams, GetAgentPreviewResponse>(
    'GET',
    '/experhub/search/agent/detail',
    {
        forbiddenToast: true,
    }
);

export default {
    /** 我的智能体预览信息详情获取 */
    getAgentPreview: createInterface<GetAgentPreviewParams, GetAgentPreviewResponse>(
        'GET',
        '/experhub/search/agent/detail'
    ),
};
