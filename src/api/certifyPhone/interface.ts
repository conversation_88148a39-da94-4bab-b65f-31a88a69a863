import {BusinessComponentUIInfo, VirtualPhoneInfo} from '../business/interface';

/** 认证手机号审核状态 */
export enum PhoneAuthStatus {
    /** 审核中 */
    Auditing = 1,
    /** 审核不通过 */
    Failed = 2,
    /** 审核成功 */
    Success = 3,
}

/** 认证手机号信息 */
export interface CertifyPhoneInfo {
    /** 手机号唯一Id */
    id: number;
    /** 手机号 */
    phoneNum: string;
    /** 手机号实名认证号主姓名 */
    realName: string;
    /** 身份证号 */
    idCardNum: string;
    /** 半身照 */
    photo: string;
    /** 审核状态 枚举值：1-审核中 2-审核不通过 3-审核通过 */
    status: PhoneAuthStatus;
    /** 审核不通过原因 */
    reason: string;
    /** 使用该虚拟手机号的其他智能体 */
    usedThisPhoneOtherAgents?: AgentVirtualPhoneInfo[];
}

/** 添加认证手机号请求参数 */
export interface AddPhoneParams
    extends Required<Pick<CertifyPhoneInfo, 'phoneNum' | 'realName' | 'idCardNum' | 'photo'>> {
    /** 验证码 */
    code: string;
}

/** 更新认证手机号请求参数 */
export interface UpdatePhoneParams
    extends Required<Pick<CertifyPhoneInfo, 'id' | 'phoneNum' | 'realName' | 'idCardNum' | 'photo'>> {
    /** 验证码 */
    code?: string;
}

/** 添加/编辑认证手机号请求返回结果 */
export type UpdatePhoneRes = Pick<CertifyPhoneInfo, 'id'>;

/** 线上版本类型 */
export const VERSION_TYPE_ONLINE = 1;

/** 智能体虚拟号码信息 */
export interface AgentVirtualPhoneInfo {
    /** 智能体Id */
    appId: string;
    /** 智能体名称 */
    appName: string;
    /** 版本状态类型 后端会过滤掉下线、已删除的版本 前端只需关心线上版本1 和 草稿版本（剩下状态都是草稿版本的） */
    versionType: number;
    /** 商业化组件Id */
    componentDetailId: BusinessComponentUIInfo['componentDetailId'];
    /** 是否开启了电话线索 */
    hasPhoneLeads: boolean;
    /** 是否线上开启了虚拟号码 */
    virtualPhoneInfo: VirtualPhoneInfo | null;
}
