import {CustomOptions, request} from '../request';
import {AddPhoneParams, AgentVirtualPhoneInfo, CertifyPhoneInfo, UpdatePhoneParams, UpdatePhoneRes} from './interface';

export default {
    /** 获取认证手机号列表，支持按id查询单个认证手机号信息 */
    getCertifyPhoneList: (id?: number): Promise<CertifyPhoneInfo[]> => request('GET', '/phone/get', id ? {id} : {}),

    /** 添加认证手机号 */
    addCertifyPhone: (params: AddPhoneParams): Promise<UpdatePhoneRes> => {
        return request('POST', '/phone/add', params);
    },

    /** 修改认证手机号 */
    updateCertifyPhone: (params: UpdatePhoneParams): Promise<void> => {
        return request('POST', '/phone/upd', params);
    },

    /** 删除认证手机号 后端会校验户下所有商业化组件（电话线索）是否绑定了该虚拟号比较耗时，超时时间设置15s */
    deleteCertifyPhone: (params: {id: number; appId: string}): Promise<void> => {
        return request('GET', '/phone/del', params, {timeout: 15000});
    },

    /** 获取用户下所有商业化组件（电话线索）绑定的虚拟号数据，比较耗时，超时时间设置15s */
    getAllUsedVirtualPhone: (apiOptions?: CustomOptions): Promise<AgentVirtualPhoneInfo[]> => {
        return request('GET', '/phone/getUsingPhone', {}, {apiOptions, timeout: 15000});
    },
};
