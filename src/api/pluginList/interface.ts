import {AuditStatus} from '@/modules/agentList/interface';
import {AIServiceType, PluginPublishType} from '../pluginCenter/interface';

/**
 * AI插件Id
 */
export type PluginId = string;

/**
 * 插件类型
 */
export type PluginType = AIServiceType.Ability | AIServiceType.Data;

/**
 * AI插件列表分页接口参数信息
 */
export interface PluginListParams {
    pageNo: number;
    pageSize: number;
    /**
     * 插件类型，能力插件传1，数据插件传2，不传接口默认1
     */
    type?: PluginType;
}

/**
 * AI插件列表返回信息
 */
export interface PluginListRes {
    total: number;
    pageNo: number;
    pageSize: number;
    pluginList: PluginInfo[];
}

/**
 * AI插件信息
 */
export interface PluginInfo {
    /**
     * 插件Id
     */
    pluginId: PluginId;
    /**
     * 插件名称
     */
    pluginName: string;
    /**
     * 插件简介
     */
    pluginDesc: string;
    /**
     * 插件头像logo
     */
    logo: string;
    /**
     * 插件类型
     */
    pluginType: PluginType;
    /**
     * 插件状态（新增字段名）
     */
    displayStatus: PluginStatus;
    /**
     * 插件状态
     */
    pluginStatus: AuditStatus;
    /**
     * 是否上架插件商店
     */
    pluginScope: PluginPublishType;
    /**
     * 插件开发类型
     */
    buildType: PluginBuildType;
    /**
     * 应用密钥
     */
    secretKey?: string;
}

/**
 * 插件创建类型枚举
 * 1-自主开发
 */
export enum PluginBuildType {
    /**
     * 自主开发
     */
    SelfDevelop = 1,
}

export const PLUGIN_BUILD_TYPE_NAMES = {
    [PluginBuildType.SelfDevelop]: '自主开发',
};

/**
 * 插件状态枚举
 */
export enum PluginStatus {
    /**
     * 开发中
     */
    Developing = 10,
    /**
     * 修改中
     */
    Modifying = 11,
    /**
     *已上线
     */
    Online = 30,
    /**
     * 已下线
     */
    Offline = 40,
    /**
     * 审核中
     */
    Auditing = 20,
    /**
     * 审核通过
     */
    AuditPass = 21,
    /**
     * 审核失败
     */
    AuditFail = 22,
}

export const PLUGIN_STATUS_TEXTS = {
    [PluginStatus.Developing]: '开发中',
    [PluginStatus.Modifying]: '修改中',
    [PluginStatus.Online]: '已上线',
    [PluginStatus.Offline]: '已下线',
    [PluginStatus.Auditing]: '审核中',
    [PluginStatus.AuditPass]: '审核通过',
    [PluginStatus.AuditFail]: '审核失败',
} as const;
