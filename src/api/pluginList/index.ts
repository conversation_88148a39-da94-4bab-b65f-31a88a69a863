import {createInterface} from '../request';
import {AIServiceType} from '../pluginCenter/interface';
import {PluginListParams, PluginListRes} from './interface';

const getPluginList = createInterface<PluginListParams, PluginListRes>('GET', '/plugin/getMyPlugins');

export default {
    // 获取能力插件列表
    getAbilityPluginList: async ({pageNo = 1, pageSize}: PluginListParams): Promise<PluginListRes> =>
        await getPluginList({pageNo, pageSize, type: AIServiceType.Ability}),
    // 获取数据插件列表
    getDataPluginList: async ({pageNo = 1, pageSize}: PluginListParams): Promise<PluginListRes> =>
        await getPluginList({pageNo, pageSize, type: AIServiceType.Data}),
};
