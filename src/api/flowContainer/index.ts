/**
 * @file 画布容器
 * <AUTHOR>
 */

import {FlowJson} from '@/store/flow/flowJson';
import {createInterface} from '../request';
import {AppInfo} from './interface';

export interface SubmitAuditParams {
    /**
     * 应用ID
     */
    appId: string;
    /**
     * 版本号(低码传入-用户自定义；自主开发传versionCode)
     */
    version?: string;
    /**
     * 版本功能描述
     */
    content?: string;
    /**
     * 编辑配置项(低码传入-用于比较是否最新编辑版本)
     */
    flowJson: FlowJson;
    code?: string;
}

/**
 * 提交获取审核步骤
 */

export interface AuditStep {
    /**
     * 审核步骤
     */
    step: number;
    /**
     * 版本号
     */
    version: string;
}

// 对外暴露接口
export default {
    getAppInfo: createInterface<{appId: string}, AppInfo>('GET', '/app/getMyAppById'),
    // 获取审核步骤
    checkSubmitAudit: createInterface<{appId: string}, AuditStep>('GET', '/version/submit'),
    // 提交审核
    submitAuditVersion: createInterface<SubmitAuditParams, void>('POST', '/version/audit', {
        forbiddenToast: true,
    }),
};
