/**
 * @file 画布容器-接口数据模型
 * <AUTHOR>
 */

import {AuditStatus, ServerAuditStatus} from '@/modules/agentList/interface';

/**
 * 应用信息
 */
export interface AppInfo {
    appId: string;
    appName: string;
    appDesc: string;
    buildType: number;
    frameworkType: number;
    appAvatar: string;
    /**
     * 应用状态 同智能体状态
     */
    status: AuditStatus & ServerAuditStatus;
    /**
     * agent web化url
     */
    previewUrl: string;
}

export enum SubmitAuditStep {
    Default = 0,
    // 审核未通过
    Fail = 1,
    // 审核中
    Checking = 2,
    // 应用设置未完成
    Unfinished = 3,
    // 完成应用设置
    Finished = 4,
}

export interface AuditStep {
    /**
     * 审核步骤
     */
    step: SubmitAuditStep;
    /**
     * 版本号
     */
    version: string;
}
