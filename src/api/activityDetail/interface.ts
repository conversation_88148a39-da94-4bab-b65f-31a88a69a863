/**
 * @file 活动详情页数据 interface
 * <AUTHOR>
 */
import {ActivityType, ActivityStatus} from '@/api/activityList/interface';

// 活动未找到错误码(用户修改 url 中的活动 id 场景)
export const ACTIVITY_NOT_FOUND = 60001;

export interface ActivityDetailContent {
    // 活动小标题
    title: string;
    // 富文本内容
    detail: string;
}

// 各阶段的按钮状态
export enum ActivityButtonStatus {
    // 未设置按钮
    NotSet = -1,
    // 未完成
    Incomplete = 0,
    // 已完成
    Completed = 1,
}

// 重复提交状态
type ActivityButtonSubmitStatus = 1 | 0 | null;

export interface ActivityDetailData {
    // 活动id
    id: number;
    // 活动名称
    title: string;
    // 活动简介
    subTitle: string;
    // 活动开始时间
    beginTime: number;
    // 活动结束时间
    endTime: number;
    // 活动失效时间
    invalidTime: number;
    // 活动状态
    status: ActivityStatus;
    // 活动封面
    coverImg: string;
    // 是否置顶
    showTop: number;
    // 活动类型
    type: ActivityType;
    // 活动内容
    content: ActivityDetailContent[];

    // 由于服务端针对不同阶段的按钮(button1/2/3)分别入库记录，因此分开字段，没有使用数组
    // 不同阶段的按钮标题
    button1Name: string;
    button2Name: string;
    button3Name: string;
    // 不同阶段的按钮跳转地址
    button1Url: string;
    button2Url: string;
    button3Url: string;
    // 不同阶段的按钮跳转地址对应的 formId
    button1FormId: string;
    button2FormId: string;
    button3FormId: string;
    // 不同阶段的按钮状态
    button1Status: ActivityButtonStatus;
    button2Status: ActivityButtonStatus;
    button3Status: ActivityButtonStatus;
    // 不同阶段的按钮是否需要重复提交 --1 允许重复提交，0，不允许重复提交，空/null 没有配置过
    isButton1RepeatSubmit: ActivityButtonSubmitStatus;
    isButton2RepeatSubmit: ActivityButtonSubmitStatus;
    isButton3RepeatSubmit: ActivityButtonSubmitStatus;
}
