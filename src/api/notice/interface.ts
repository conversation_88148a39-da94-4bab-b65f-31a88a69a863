/**
 * @file 平台公告
 * <AUTHOR>
 */

/** 气泡名称 */
export enum NoticeName {
    /** 公告气泡 */
    NoticePop = 'noticePop',
    /** 公告红点 */
    TipPop = 'tipPop',
}

export interface NoticePopupParams {
    /** 气泡名称 */
    name: NoticeName;
}

// 公告类型
export enum NoticeType {
    Notice = 0,
    Inform = 1,
}

// 公告状态
export enum NoticeStatusType {
    Unread = 0,
    Read = 1,
}

// 公告列表展示的公告信息
export interface NoticeListItem {
    // noticeId
    noticeId: string;
    // 公告id
    id: string;
    // 标题
    title: string;
    // 摘要
    summary: string;
    // 发布时间
    publishTime: number;
    // 公告已读未读状态
    status: NoticeStatusType;
    // 公告类型
    noticeType: NoticeType;
}

// 公告列表接口返回
export interface NoticeListResponse {
    pageNo: number;
    pageSize: number;
    total: number;
    dataList: NoticeListItem[];
}

/** 公告内容 */
export interface NoticeContent {
    /** 通知表的 notice_id 字段(打点使用) */
    id: string;
    // /** 通知表的 notice_id 字段 */
    noticeId: string;
    /** 标题 */
    title: string;
    /** 摘要 */
    summary: string;
    /** 详细落地页 */
    detailUrl: string;
    /** 按钮 允许多个，也可为空 */
    buttons: Array<{
        text: string; // 按钮文案
        linkUrl: string; // 跳转链接
    }>;
    /** 占位图 允许多个，也可为空 */
    images: string[];
    /** 最新通知发布时间 */
    publishTime: number;
    /** 公告类型 */
    noticeType: NoticeType;
}

/** 公告状态 */
export interface NoticeStatus {
    /** 是否展示弹窗 */
    isShow: boolean;
    /** 弹窗名称 */
    name: string;
}

/** 公告气泡 */
export interface NoticePopup extends NoticeStatus {
    /** 弹窗内容 */
    notice: NoticeContent;
}

/** 红点点击接口返回 */
export interface NoticeTipClick extends NoticeStatus {
    /**  弹窗内容 */
    content: string;
}
