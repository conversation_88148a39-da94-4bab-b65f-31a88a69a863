/**
 * @file 平台公告
 * <AUTHOR>
 * @update 2024/10/14
 */

import {createInterface} from '@/api/request';
import {
    NoticePopupParams,
    NoticePopup,
    NoticeTipClick,
    NoticeType,
    NoticeListResponse,
    NoticeContent,
} from './interface';

// 对外暴露接口
export default {
    // 公告弹窗
    noticePopup: createInterface<NoticePopupParams, NoticePopup>('GET', '/notice/popup'),
    // 红点点击事件
    noticeTipClick: createInterface<void, NoticeTipClick>('GET', '/notice/popup/tip/click'),
    // 用户公告列表
    getNoticeList: createInterface<{noticeType?: NoticeType; pageNo: number; pageSize: number}, NoticeListResponse>(
        'GET',
        '/notice/list',
        {timeout: 20 * 1000}
    ),
    // 公告概览信息
    getNoticeSummary: createInterface<{noticeId: string}, NoticeContent>('GET', '/notice/summary'),
    // 未读公告消息红点提示
    getNoticeTip: createInterface<void, boolean>('GET', '/notice/redTips'),
};
