import {WorkflowNodeType} from '@/api/workflow/interface';
import {CardTag, MedalInfo} from '@/modules/center/interface';

export interface AgentInfoParams {
    appid: string;
}

export interface AgentInfoResponse {
    // 智能体名称
    name?: string;

    // 智能体logo url
    logoUrl?: string;

    // 智能体创建者用户名
    userName?: string;

    /** 智能体 勋章 */
    agentMedalInfo: MedalInfo[] | null;
    /** 开发者 勋章 */
    userMedalInfo: MedalInfo[] | null;

    // 智能体创建时间
    publishTime?: string;

    // 智能体收藏数
    favoriteCount?: number;

    // 智能体使用数
    useNum?: number;

    // 判断UseNum字段是否展示(资源智能体UseNum使用人数字段)
    showUseNum?: boolean;

    // 智能体对话数
    dialogueCount?: number;

    // 智能体描述
    description?: string;

    // 智能体推荐会话
    recommendedConversations?: RecommendedConversation[];

    // 使用的模型
    model?: string;

    // 系统推荐的插件列表
    recommendedPlugins?: LogoContent[];

    // 系统推荐的工作流列表
    recommendedWorkflows?: LogoContent[];

    // 智能体拥有的数字形象
    digitalFigure?: string[];

    // 用户的私有资产信息，包括插件、工作流、知识库等
    privateAssets?: PrivateAssets[];

    // 是否是公共配置，0：否，1：是
    shareTag?: 0 | 1;

    // 是否被关注
    isFavorited?: boolean;

    // 是否是用户自己创建的
    isOwner?: boolean;

    // 智能体状态
    packageStatus?: number;

    // 是否灰度
    gray: boolean;

    // 工作流节点信息
    workflowInfo?: WorkflowInfo;
    tags?: CardTag[];

    // 垂类认证信息
    verticalCertificationInfo?: VerticalCertificationInfo[];
}

// 垂类认证分类
export enum VerticalCertificationTagCateCode {
    Q2C_LAWYER_CREDIT = 'Q2C_LAWYER_CREDIT',
    DOCTOR_CREDIT_CATE = 'DOCTOR_CREDIT_CATE',
}

export interface VerticalCertificationInfo {
    // tag category code 垂类认证分类
    tagCateCode: VerticalCertificationTagCateCode;
    // tag category name 垂类认证分类名称
    tagCateName: string;
    // tag value 垂类认证值
    tagValue: string;
}

export interface RecommendedConversation {
    msgId: string;
    content: string;
}

export interface LogoContent {
    name: string;
    logoUrl: string;
}

export interface PrivateAssets {
    type: string;
    count: number;
}

export enum packageStatus {
    /**
     * 未开发
     */
    UNDEVELOPED = 1,
    /**
     * 开发中
     */
    DEVELOPING = 2,

    /**
     * 审核中
     */
    UNDER_REVIEW = 3,

    /**
     * 审核成功
     */
    APPROVED = 4,

    /**
     * 审核失败
     */
    REJECTED = 5,

    /**
     * 已上线
     */
    ONLINE = 6,

    /**
     * 已下线
     */
    OFFLINE = 7,

    /**
     *  强制下线
     */
    FORCED_OFFLINE = 8,

    /**
     *  修改中
     */
    MODIFICATION = 9,

    /**
     *  二次审核
     */
    SECOND_REVIEW = 10,

    /**
     *  二次审核失败
     */
    SECOND_REJECTED = 11,

    /**
     *  二次审核成功
     */
    SECOND_APPROVED = 12,
}

// 工作流节点信息
export type WorkflowInfo = Record<WorkflowNodeType, number>;
