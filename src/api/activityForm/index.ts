/**
 * @file
 * <AUTHOR>
 */
import type {AxiosProgressEvent} from 'axios';
import {createInterface, request} from '@/api/request';
import {ActivityFormDetail, ActivityFormItemAgentListItem, ActivityFormItemValue} from '@/api/activityForm/interface';
import {FileUploadResponse} from '@/api/activity/interface';

export default {
    /** 获取表单详情 */
    getFormDetail: createInterface<{formId: string}, ActivityFormDetail>('GET', '/experhub/form/detail'),
    /** 查询当前用户满足问卷题目要求(O端配置的首次发布/创建时间限制)的智能体列表 */
    getFormAgentList: createInterface<{formId: string; name: string}, ActivityFormItemAgentListItem[]>(
        'GET',
        '/experhub/form/listAgent'
    ),
    /** 提交表单数据 */
    formUpload: ({
        formId,
        name,
        file,
        onUploadProgress,
    }: {
        formId: string;
        name: string;
        file: File;
        onUploadProgress: (e: AxiosProgressEvent) => void;
    }): Promise<FileUploadResponse> => {
        return request(
            'POST',
            '/experhub/form/upload',
            {
                formId,
                name,
                file,
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                forbiddenToast: true,
                timeout: 1000 * 360,
                onUploadProgress: onUploadProgress,
            }
        );
    },
    /** 提交表单数据 */
    submitForm: createInterface<
        {formId: string; activityId?: string | null; channel?: string | null; formData: ActivityFormItemValue[]},
        ActivityFormItemAgentListItem[]
    >('POST', '/experhub/form/submit'),
};
