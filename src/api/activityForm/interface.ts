/**
 * @file
 * <AUTHOR>
 */

// 表单项类型
export const ActivityFormItemType = {
    Radio: 'radio',
    Checkbox: 'checkbox',
    Select: 'select',
    Textarea: 'textarea',
    Text: 'text',
    // 日期选择器
    Datetime: 'datetime',
    // 排序题
    CheckboxOrder: 'checkboxorder',
    // 文件上传
    InputFile: 'inputfile',
    // 智能体选择
    AgentSelect: 'agentselect',
    // 组合组件
    Combo: 'combo',
    // 日期区间选择器
    DateRange: 'daterange',
};

// 表单项选项（多选、单选、下拉框等）
export interface ActivityFormItemOption {
    // 选项 label 和 value
    value: string;
    // 是否为默认值
    isDefault?: boolean;
    // 是否需要输入
    needInput?: boolean;
    // 需要输入时输入框的占位符
    placeholder?: string;
    // 输入框最少输入长度
    min?: number;
    // 输入框最大输入长度
    max?: number;
}

// 条件操作符类型
export enum ActivityFormItemConditionOperatorType {
    // 大于
    gt = '>',
    // 小于
    lt = '<',
}

export interface ActivityFormItemTimeConditionItem {
    // ActivityFormItemConditionOperatorType 的值('>' 或 '<')
    opera: ActivityFormItemConditionOperatorType.gt | ActivityFormItemConditionOperatorType.lt;
    // 时间戳
    time: number;
}

// 选择智能体条件
export interface ActivityFormItemCondition {
    // 首次创建时间
    firstCreateTime?: ActivityFormItemTimeConditionItem[];
    // 首次发布时间
    firstPublishTime?: ActivityFormItemTimeConditionItem[];
}

// 表单项数据
export interface ActivityFormItem {
    // 类型：ActivityFormItemType 的值
    type: string;
    // 字段名称
    name: string;
    // 标题
    label: string;
    // 占位符
    placeholder?: string;
    // 输入框最少输入长度
    min?: number;
    // 输入框最大输入长度
    max?: number;
    // 输入框校验正则表达式
    regular?: string;
    // 选项列表
    options?: ActivityFormItemOption[];
    // 是否多选
    multiple?: boolean;
    // 选择智能体条件
    condition?: ActivityFormItemCondition;
    // 最少选择数量
    requireMin?: number;
    // 最多选择数量
    requireMax?: number;
    // 是否必填
    required?: boolean;
    // 文件大小最大限制
    maxSize?: number;
    // 文件类型限制
    uploadType?: string;
    // Combo 类型组件可以嵌套其他表单
    body?: ActivityFormItem[];
}

// 表单状态
export enum ActivityFormStatus {
    // 草稿
    Draft = 1,
    // 未开始
    NotStarted,
    // 进行中
    InProgress,
    // 已结束
    Completed,
}

export interface ActivityFormDetail {
    // 表单ID
    formId: string;
    // 表单名称
    title: string;
    // 表单副标题
    subTitle: string;
    // 表单状态
    status: ActivityFormStatus;
    // 开始时间
    beginTime: number;
    // 结束时间
    endTime: number;
    // 表单类型
    formType: number;
    // 运营配置的二维码图片列表或者其他营销图片
    img: Array<{url: string; desc: string}>;
    // 表单项配置列表
    content: ActivityFormItem[];
}

export interface ActivityFormItemAgentListItem {
    // 智能体名称
    name: string;
    // 智能体ID
    appId: string;
}

export interface ActivityFormItemValue {
    // 字段名称
    name: string;
    // 字段值
    value?: any;
    // Radio 和 Checkbox 类型的题额外输入的值
    inputValue?: string;
    // 用于存储 Checkbox 类型的题的 inputValue，不会提交到服务端，在提交前 format 过程中使用
    inputValues?: Record<string, string>;
}
