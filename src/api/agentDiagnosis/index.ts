import {createInterface} from '../request';
import {request} from '../request';
import {
    AgentDiagnosisInfo,
    AgentDiagnosisIssue,
    AgentDiagnosisReport,
    AgentDiagnosisIssueSample,
    FeedbackData,
    AgentStatisticsInfo,
} from './interface';

type AgentDiagnosisIssueResponse = Omit<AgentDiagnosisIssue, 'sampleList'> & AgentDiagnosisIssueSample;

type AgentDiagnosisReportResponse = Omit<AgentDiagnosisReport, 'agentDiagnosisIssue'> & {
    agentDiagnosisIssue: AgentDiagnosisIssueResponse[];
};

/** 接口返回的agent对话质量问题数据结构 */
type AgentDiagnosisInfoResponse = Omit<AgentDiagnosisInfo, 'diagnosisResult'> & {
    diagnosisResult: AgentDiagnosisReportResponse;
};

export default {
    /** 获取智能体诊断报告 */
    getAgentDiagnosis: ({appId}: {appId: string}): Promise<AgentDiagnosisInfo> =>
        request('POST', '/tuning/analyze', {appId}).then((res: AgentDiagnosisInfoResponse) => {
            /**
             * 将接口返回的对话问题 agentDiagnosisIssue 按问题类型 agentDiagnosisIssue.agentDiagnosisIssueLabel 进行分类聚合转成前端交互需要的结构
             * 为什么不是上游直接转换成前端交互要用的数据结构，诊断也是大模型处理的字符串，后端做也要通过字符串做聚合，上线才发现需要聚合，后端改动成本大（agent server & B server都要改），所以前端来处理
             * 转换前结构：
             * {..., "agentDiagnosisIssue": [{"agentDiagnosisIssueLabel": "内容相关度","analysis": "","sample": [],"optimizeSection": []},{"agentDiagnosisIssueLabel": "内容相关度","analysis": "","sample": [],"optimizeSection": []}], ...}
             * 转换后结构：
             * {..., "agentDiagnosisIssue": [{"agentDiagnosisIssueLabel": "内容相关度","analysis": "","sampleList": [{"sample": [],"optimizeSection": []},{"sample": [],"optimizeSection": []}]}, ...}
             */
            const groupIssueListByIssueLabel: {[key: string]: AgentDiagnosisIssue} = {};

            if (res.diagnosisResult?.agentDiagnosisIssue) {
                res.diagnosisResult.agentDiagnosisIssue.forEach(
                    ({agentDiagnosisIssueLabel, analysis, sample, optimizeSection}) => {
                        if (!groupIssueListByIssueLabel[agentDiagnosisIssueLabel]) {
                            groupIssueListByIssueLabel[agentDiagnosisIssueLabel] = {
                                agentDiagnosisIssueLabel,
                                analysis,
                                sampleList: [],
                            };
                        }

                        // 聚合过程中取第一个有问题说明的analysis
                        if (!groupIssueListByIssueLabel[agentDiagnosisIssueLabel].analysis) {
                            groupIssueListByIssueLabel[agentDiagnosisIssueLabel].analysis = analysis;
                        }

                        groupIssueListByIssueLabel[agentDiagnosisIssueLabel].sampleList.push({
                            sample,
                            optimizeSection,
                        });
                    }
                );
            }

            return {
                ...res,
                diagnosisResult: res.diagnosisResult
                    ? {
                          ...res.diagnosisResult,
                          agentDiagnosisIssue: Object.values(groupIssueListByIssueLabel),
                      }
                    : null,
            };
        }),
    diagnosisFeedback: (feedbackData: FeedbackData): Promise<void> => {
        return request('POST', '/agent/feedback', feedbackData);
    },
    /** 查询智能体对话次数和使用人数 */
    getAgentStatisticsInfo: createInterface<{appId: string}, AgentStatisticsInfo>('GET', '/agent/statistics/info'),
};
