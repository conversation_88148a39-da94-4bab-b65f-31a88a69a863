/**
 * 智能体诊断报告
 */

import {AgentPermission} from '../agentEdit/interface';
import {EngineType} from '../agentEdit/interface';

/** Agent状态 */
export enum AgentStatus {
    /** 未上线-开发中 */
    Developing = 1,
    /** 未上线-审核中 */
    Auditing = 2,
    /** 未上线-审核失败 */
    AuditFail = 3,
    /** 已上线 */
    Online = 4,
    /** 已下线 */
    Offline = 5,
}

/** 智能体质量评估状态 */
export enum AgentEvaluateStatus {
    /** 未评估 */
    NoEvaluated = 0,
    /** 评估中 */
    Evaluating = 1,
}

/** 智能体诊断报告信息 */
export interface AgentDiagnosisInfo {
    /** 智能体状态 */
    agentStatus: AgentStatus;
    /** Agent访问权限 */
    agentPermission: AgentPermission;
    /** 智能体质量评估状态 */
    evaluateStatus: AgentEvaluateStatus;
    /** 报告时间 */
    reportTime: number;
    /** 智能体诊断报告 */
    diagnosisResult: AgentDiagnosisReport | null;
    /** 当前诊断报告的id */
    reportId: string;
    /** 在线模型 */
    onlineModel?: EngineType;
}

/** 耗时类型对应的展示文案 */
export enum AgentDiagnosisTimeCostTypeText {
    'model_thought' = '模型思考',
    'memory' = '长期记忆',
    'workflow' = '工作流',
    'knowledge_retrieval' = '知识库',
    'web_search' = '联网搜索',
}

/** 智能体诊断报告 */
export interface AgentDiagnosisReport {
    /** 上一次待优化项列表 */
    lastIssueKeys: string[];
    /** 当前待优化项列表 */
    issueKeys: string[];
    /** 分发场景，空展示“平台官方上架”，非空展示“百度搜索分发推荐 */
    agentDistributeScene: string;
    /** 智能体基本信息的 优化建议(是否有信息缺失) */
    agentDiagnosisBasicInfoOmissionIssue: string[];
    /** 耗时 优化建议 */
    agentDiagnosisTimeCostIssue: string[];
    /** 信息冲突（名称、开场白等存在设定冲突） 优化建议 */
    infoConflictIssue: string[];
    /** 是否展示信息冲突相关项 */
    showInfoConflictIssue: boolean;
    /** 配置完整度（是否配置完成任务所需的插件和能力） 优化建议 */
    configCompleteIssue: string[];
    /** 是否展示配置完整度相关项 */
    showConfigCompleteIssue: boolean;
    /** 平均耗时，ms 单位 */
    agentDiagnosisTimeCost: number;
    /** 耗时详细信息 */
    agentDiagnosisTimeCostDetail: Array<{
        /** 耗时阶段的类型（枚举：模型思考、长期记忆、工作流、知识库、联网搜索、具体的某个插件） */
        type: keyof typeof AgentDiagnosisTimeCostTypeText & 'self_plugin' & 'offical_plugin';
        /** 阶段耗时 单位ms */
        avgCost: number;
        /** 仅工作流/插件有名称 */
        name: string | null;
    }>;
    /** 回答的稳定性问题，下游暂不支持，因此目前该变量永远为空数组 */
    agentDiagnosisAnswerStabilityIssue: string[];
    /** agent对话质量问题 */
    agentDiagnosisIssue?: AgentDiagnosisIssue[];
    /** 工具耗时优化项 */
    agentDiagnosisPluginIssue?: AgentPluginIssue[];
    /** 在线模型 */
    onlineModel?: string;
}

/** agent对话质量问题 */
export interface AgentDiagnosisIssue {
    /** 诊断问题分类title（内容相关度、问答事实准确度、信息时效性等） */
    agentDiagnosisIssueLabel: string;
    /** 诊断问题分类描述 */
    analysis: string;
    /** 按分类聚合的诊断问题列表 */
    sampleList: AgentDiagnosisIssueSample[];
}

/** 诊断问题 */
export interface AgentDiagnosisIssueSample {
    /** 多轮对话QA列表 */
    sample: AgentQueryResponse[];
    /** agent质量优化建议 */
    optimizeSection: AgentOptimizeSuggestions[];
}

/** 对话测试问题信息 */
interface AgentQueryResponse {
    /** query 问题 */
    query: string;
    /** query 回答 */
    response: string;
}

/** agent质量优化建议 */
interface AgentOptimizeSuggestions {
    /** agent 优化建议 */
    suggestion: string;
    /** agent优化步骤 */
    instruction: string;
}

/**
 * 插件类型
 */
export enum PluginCategory {
    /** 自研插件，无官方标识 */
    General = 0,
    /** 官方插件标识 */
    Official = 1,
}

/** 插件工具优化项 */
export interface AgentPluginIssue {
    pluginId: string;
    /** 插件名称 */
    pluginName: string;
    /** 插件logo */
    pluginLogo: string;
    /** 插件类型 */
    pluginCategory: PluginCategory;
    /** 插件运行耗时-毫秒 */
    pluginTimeConsumeTrack: number;
    /** 优化建议 */
    suggestion: string;
}

/** 反馈入口枚举 */
export enum FeedbackEntry {
    Dashboard = 0,
    DiagnosisReport,
    OptimizationSuggestion,
    DatasetFeedback, // 知识库召回侧边栏反馈按钮
}

/** 反馈的场景信息 */
export interface FeedbackContext {
    /** agent id */
    appId: string;
    /** 诊断报告id */
    reportId?: string;
    /** 反馈类型 */
    type: FeedbackEntry;
    /** case标识, 格式为 "issueIndex-sampleId" */
    caseId?: string;
}

/** 反馈的完整数据 */
export type FeedbackData = {
    /** 用户选择的反馈选项 */
    options: string[];
    /** 用户输入的反馈内容 */
    content?: string;
} & FeedbackContext;

export interface GetAgentStatisticsInfoParams {
    appId: string;
}

export interface AgentStatisticsInfo {
    /** 最近使用数量 */
    totalPv: number;
    /** 帮助人数数量 */
    totalUv: number;
    /** 总流量 */
    totalTraffic: number;
}

/** 智能体质量分析展示的四种状态类型 */
export enum AnalysisResultType {
    /** 可分发 */
    CanDistribute = 1,
    /** 不可分发-有优化项 */
    HasOptimization = 2,
    /** 不可分发-无优化项 */
    NoOptimization = 3,
    /** 优化项减少 */
    OptimizationReduce = 4,
}

/** 智能体质量分析维度类型 */
export enum AnalysisDimensionType {
    /** 配置完整度 */
    ConfigCompleteness = 1,
    /** 耗时与稳定性 */
    ConsumeTimeAndStable = 2,
    /** 问答效果 */
    ReplyEffect = 3,
}
