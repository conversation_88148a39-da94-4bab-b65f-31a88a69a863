import {createInterface} from '../request';
import {
    GetCustomerListRequest,
    GetCustomerListResponse,
    GetTpInfoResponse,
    GetTpUnbindInfoResponse,
    HandleUnbindRequest,
} from './interface';

export default {
    /** 获取服务商信息 */
    getTpInfo: createInterface<void, GetTpInfoResponse>('GET', '/thirdpart/tp/info'),

    /** 获取绑定的商家列表 */
    getCustomerList: createInterface<GetCustomerListRequest, GetCustomerListResponse>('GET', '/thirdpart/tp/list', {
        /** 由于 server 不支持查询账号名称，因此扩大 pageSize，尽量将数据平摊，让用户自己查询名称，于是需要增加超时时间 */
        timeout: 20 * 1000,
    }),

    /** 获取授权二维码 */
    getQrCode: createInterface<void, string>('GET', '/thirdpart/tp/getQrCode'),

    /** 申请解绑 */
    sendUnbind: createInterface<{tpBindId: number}, void>('POST', '/thirdpart/tp/unbind'),

    /** 处理解绑请求 (同意 || 不同意) */
    handleUnbind: createInterface<HandleUnbindRequest, void>('POST', '/thirdpart/tp/handleUnbind'),

    /** 获取是否有被拒绝的解绑申请 */
    getTpUnbindInfo: createInterface<void, GetTpUnbindInfoResponse>('GET', '/thirdpart/tp/unbindInfo'),

    /** 关闭弹窗 */
    closeTpPopup: createInterface<void, string>('POST', '/thirdpart/tp/closePopup'),
};
