import {CustomerType} from '@/modules/tpCustomerList/constant';

type NumberStatus = 0 | 1;

export interface GetTpInfoResponse {
    /** 是否是服务商 1 是、0 不是 */
    tpFlag: NumberStatus;
}

export interface GetTpUnbindInfoResponse {
    isReject: boolean;
    /** 是否展示提示弹窗 */
    isShow: boolean;
}

export interface GetCustomerListRequest {
    /** 检索关键词 */
    keyword?: string;
    /** 页码 */
    pageNo: number;
    /** 每页条数 */
    pageSize: number;
}

export interface GetCustomerListResponse {
    total: number;
    pageNo: number;
    pageSize: number;
    bindList: BindCustomerItem[];
}

/** 解绑状态（服务商视角） */
export enum TpUnbindStatus {
    /** 未申请解绑 */
    unbindNone = 0,
    /** 待理处（用户侧申请解绑中） */
    pending = 1,
    /** 解绑申请中（发起解绑，申请解绑中） */
    unbindApplying = 2,
    /** 解绑被拒绝（商家侧拒绝解绑） */
    rejected = 3,
    /** 拒绝商家解绑（拒绝商家发起的解绑） */
    disagree = 4,
}

/** 绑定的商家信息 */
export interface BindCustomerItem {
    /** 服务商和商家绑定关系ID */
    tpBindId: number;

    /** 服务商 id */
    tpId: number;

    /** 商家主体 id */
    customerId: number;

    /** 平台生成的用户 id */
    userInfoId: number;

    /** 商家账号 */
    customerAccount: string;

    /** 商家类型 */
    customerType: CustomerType;

    /** 商家名称 */
    customerName: string;

    /** 认证状态（是否是真实商家） 1 认证、0 未认证 */
    realFlag: NumberStatus;

    /** 授权状态  1 授权、0 已解除授权（未授权） */
    authFlag: NumberStatus;

    /** 解绑状态 */
    unbindStatus: TpUnbindStatus;

    /** 发起解绑申请时间 */
    unbindTime: number;

    /** 拒绝申请时间 */
    rejectTime: number;
}

export enum HandleUnbindType {
    /** 同意 */
    Agree = 1,
    /** 不同意 */
    Disagree = 2,
}

/** 处理解绑请求参数 */
export interface HandleUnbindRequest {
    tpBindId: number;
    type: HandleUnbindType;
}
