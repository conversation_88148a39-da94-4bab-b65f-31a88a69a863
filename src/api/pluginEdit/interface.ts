/**
 * @file 插件创建类型
 * <AUTHOR>
 */
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {PluginPublishType} from '@/api/pluginCenter/interface';
import {UIMeta, StyleFormData, StyleBindInfo} from '@/components/BindStyle/interface';

export type PluginId = string;

// 定义排除类型：将U从T中剔除, keyof 会取出T与U的所有键, 限定P的取值范围为T中的所有键, 并将其类型设为never
type Without<T, U> = {[P in Exclude<keyof T, keyof U>]?: never};

// 定义互斥类型，T或U只有一个能出现（互相剔除时，被剔除方必须存在）
type OR<T, U> = (Without<T, U> & U) | (Without<U, T> & T);

export enum PluginInfoEditType {
    Domain = 'domain',
    FileList = 'fileList',
}

export interface CheckFileParams {
    sessionId: string;
    fileList: PluginCreateFileInfo['fileList'];
    logoBosUrl?: string;
    pluginId?: PluginId;
}

/** 插件创建信息 */
export interface PluginCreateBaseInfo {
    /** 会话 id */
    sessionId: string;
    /** token */
    token?: string;
    /** public Key */
    publicKey?: string;
    /** 是否为本地插件 */
    localServer: boolean;
    /** 插件 id，首次创建为空，编辑时不为空 */
    pluginId?: string;
    /** 上次编辑时间 */
    lastSaveTime?: number;
    /** 工具绑定样式数据 */
    operationBindInfo: OperationBindInfoItem[];
}

export interface PluginCreateDomainInfo extends PluginCreateBaseInfo {
    /** 域名 */
    domain: string;
}

export interface PluginCreateFileInfo extends PluginCreateBaseInfo {
    /** 域名 */
    fileList: string[];
}

export type PluginCreateInfo = OR<PluginCreateFileInfo, PluginCreateDomainInfo>;
// export type PluginCreateInfo = PluginCreateFileInfo | PluginCreateDomainInfo;

export type PluginSubmitInfo = PluginCreateInfo & {
    /** 提审说明信息 */
    versionInfo: string;
    /** 创建页空，编辑页为插件ID */
    pluginId?: PluginId;
    /** 绑定的样式数据 */
    operationBindInfo: OperationBindInfoItem[];
};

/** 插件鉴权类型 */
export enum Auth {
    NONE = 1,
    SERVICE_HTTP = 2,
}

export enum SourceType {
    Domain = 1,
    FileList = 2,
}

export const AuthText: Record<Auth, string> = {
    [Auth.NONE]: '无鉴权(none)',
    [Auth.SERVICE_HTTP]: '服务鉴权(service_http)',
};

export enum FileType {
    AiPlugin = 1, // 插件配置文件
    Openapi = 2, // openapi文件
    Example = 3, // 示例文件
    MsgContent = 4, // 消息内容文件
    Output = 5, // 输出文件
    Ui = 6, // ui文件
}

export enum FileName {
    AiPlugin = 'ai-plugin.json',
    MsgContent = 'msg_content.yaml',
    Openapi = 'openapi.yaml',
    Example = 'example.yaml',
    Output = 'output.yaml',
    Ui = 'ui.json',
}

export const fileNameInfoObj: Record<
    string,
    {type: FileType; detailName: string; validityName: string; fileName: string; log: string}
> = {
    [FileName.AiPlugin]: {
        type: FileType.AiPlugin,
        detailName: 'aiPluginJson',
        validityName: 'aiPlugin',
        fileName: FileName.AiPlugin,
        log: EVENT_TRACKING_CONST.AbilityPluginCheckAiPluginJsonTab,
    },
    [FileName.MsgContent]: {
        type: FileType.MsgContent,
        detailName: 'msgContentYaml',
        validityName: 'msgContent',
        fileName: FileName.MsgContent,
        log: EVENT_TRACKING_CONST.AbilityPluginCheckMsgContentYamlTab,
    },
    [FileName.Openapi]: {
        type: FileType.Openapi,
        detailName: 'openapiYaml',
        validityName: 'openapi',
        fileName: FileName.Openapi,
        log: EVENT_TRACKING_CONST.AbilityPluginCheckOpenApiYamlTab,
    },
    [FileName.Example]: {
        type: FileType.Example,
        detailName: 'exampleYaml',
        validityName: 'example',
        fileName: FileName.Example,
        log: EVENT_TRACKING_CONST.AbilityPluginCheckExampleYamlTab,
    },
    [FileName.Output]: {
        type: FileType.Output,
        detailName: 'outputYaml',
        validityName: 'output',
        fileName: FileName.Output,
        log: EVENT_TRACKING_CONST.AbilityPluginCheckOutputYamlTab,
    },
    [FileName.Ui]: {
        type: FileType.Ui,
        detailName: 'uiJson',
        validityName: 'ui',
        fileName: FileName.Ui,
        // // 本期暂不统计
        log: '',
    },
};

export const configFileGetters: Array<{
    fileName: FileName;
    getFromAiPluginJson: (config: any) => string | undefined;
}> = [
    {
        fileName: FileName.MsgContent,
        getFromAiPluginJson: config => config.msg_content?.url,
    },
    {
        fileName: FileName.Openapi,
        getFromAiPluginJson: config => config.api?.url,
    },
    {
        fileName: FileName.Example,
        getFromAiPluginJson: config => config.examples?.url,
    },
    {
        fileName: FileName.Ui,
        getFromAiPluginJson: config => config.ui?.url,
    },
];

/** 文件上传 */
export interface FileInfo {
    fileName: string;
    fileId: string;
}

/** 插件校验 */
export interface PluginValidityBaseInfo {
    /** 总体校验结果, 0为成功，非0为失败 */
    result: number;

    /** 总体校验校验结果提示 */
    resultMsg?: string;

    /** 鉴权方式 */
    auth: Auth;

    /** manifest 中的版本号 */
    version: string;

    /** 标识是否为本地插件 */
    localServer: boolean;
}

export interface PluginValidityOrigin extends PluginValidityBaseInfo {
    /** ai-plugin.json的内容 */
    aiPlugin?: {
        aiPluginResult: number;
        aiPluginErrorMsg: string[];
        aiPluginDetail: string;
    };

    /** openapi.yaml的校验结果 */
    openapi?: {
        openapiResult: number;
        openapiErrorMsg: string[];
        openapiDetail: string;
    };

    /** example.yaml的校验结果 */
    example?: {
        exampleResult: number;
        exampleErrorMsg: string[];
        exampleDetail: string;
    };

    /** msg_content.yaml的校验结果 */
    msgContent?: {
        msgContentResult: number;
        msgContentErrorMsg: string[];
        msgContentDetail: string;
    };

    /** output.yaml的校验结果 */
    output?: {
        outputResult: number;
        outputErrorMsg: string[];
        outputDetail: string;
    };

    /** ui.json的校验结果 */
    ui?: {
        uiResult: number;
        uiErrorMsg: string[];
        uiDetail: string;
    };
}

export interface ValidityResultInfo {
    detail: string;
    errorMsg: string[];
    result: number;
    name: string;
    log: string;
}

export interface PluginValidity extends PluginValidityBaseInfo {
    fileInfo: ValidityResultInfo[];
}

// 新增pluginScope字段，用于能力插件获取非首次提交时上一次的公开范围
export type PluginLatestVersionOrigin = PluginCreateBaseInfo & {
    auth: Auth;
    version: PluginValidity['version'];
    sourceType: SourceType;
    pluginScope: PluginPublishType;
    fileList?: Array<{
        id: string;
        name: string;
        content: string;
    }>;
    domain?: string;
};

export type PluginLatestVersion = PluginLatestVersionOrigin & {
    validityInfo: PluginValidity;
};

export interface GetPreviewDataResponse {
    appId: string;
    pluginName: string;
    isLocal: boolean;
    uiJson: string;
    expireTimeStamp: number;
}

export enum PluginPreviewEnv {
    // 审核版
    Audit = 1,
    // 线上版
    Release = 2,
    // 分享版
    Share = 3,
}

export interface GetPluginSessionIdParams {
    pluginId: PluginId;
    env: PluginPreviewEnv;
    versionCode?: number;
}

export interface StyleJsonObject {
    // 工具绑定的样式 scheme 的表单数据，仅在平台上使用，用于表单回填
    formData: StyleFormData;
    // 工具绑定的样式 scheme 的 uiMeta 数据，用于合并到 ui.json 文件中
    uiMeta: UIMeta;
}

export interface OperationBindInfoItem extends StyleBindInfo {
    // 工具 id
    operationId: string;
    // 是否绑定样式
    bindStyle: boolean;
    // 渲染 ui.json 时传入 sdk 的 data 的字段，在 openapi.yaml 中定义为 x-return-raw
    dataSource: string;
}

export enum StyleUpdateTag {
    NoUpdate = 0,
    Update = 1,
    InterfaceUpdate = 2,
    NoCompatible = 3,
}

export interface PluginStyle {
    // 样式 id
    styleId: number;
    // 样式名称
    styleName: string;
    // 样式版本
    styleVersion: string;
    // 样式 scheme 的 JSON 字符串
    styleJson: string;
    // 样式缩略图
    styleTemplateImage: string;
    // 样式预览图
    stylePreviewImage: string;

    // JSON.parse(styleJson) 后的对象
    styleJsonObject: Record<string, any>;

    styleDesc: string;

    updateTag: StyleUpdateTag;
}
