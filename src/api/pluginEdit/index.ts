/**
 * @file 插件创建&编辑
 * <AUTHOR>
 */

import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {PluginId} from '../pluginList/interface';
import {createInterface, request} from '../request';
import {
    PluginLatestVersion,
    PluginCreateInfo,
    CheckFileParams,
    PluginCreateDomainInfo,
    PluginSubmitInfo,
    PluginValidity,
    PluginValidityOrigin,
    fileNameInfoObj,
    PluginLatestVersionOrigin,
    SourceType,
    GetPreviewDataResponse,
    GetPluginSessionIdParams,
    PluginStyle,
    OperationBindInfoItem,
} from './interface';

/**
 * 将后端返回的数据格式化，例如:
 * {
 *      aiPlugin?: {
 *          aiPluginResult: number;
 *          aiPluginErrorMsg: string[];
 *          aiPluginDetail: string;
 *      }
 * }
 * 转化为:
 * [
 *      {
 *          name: string;
 *          result: number;
 *          errorMsg: string[];
 *          detail: string;
 *      }
 * ]
 * @param validityInfo PluginValidityOrigin
 * @returns ValidityResultInfo[]
 */
const formatValidityInfo = (validityInfo: any): PluginValidity['fileInfo'] => {
    const res: PluginValidity['fileInfo'] = [];
    Object.values(fileNameInfoObj).forEach(({validityName, fileName, log}) => {
        const validity = validityInfo[validityName];
        if (validity) {
            res.push({
                name: fileName,
                detail: validityInfo[validityName][validityName + 'Detail'],
                errorMsg: validityInfo[validityName][validityName + 'ErrorMsg'],
                result: validityInfo[validityName][validityName + 'Result'],
                log,
            });
        }
    });
    return res;
};

/**
 * 获取插件详情（域名上传）后格式化为视图类型数据
 */
function getFormatDomainValidityInfo(data: PluginLatestVersionOrigin): PluginValidity {
    const {auth, version, localServer} = data;
    const res: PluginValidity = {
        result: 0,
        auth,
        version,
        localServer,
        fileInfo: [],
    };
    Object.entries(fileNameInfoObj).forEach(([fileName, info]) => {
        const detail = data[info.detailName as keyof PluginLatestVersionOrigin] as string;
        if (detail) {
            res.fileInfo.push({
                detail,
                result: 0,
                errorMsg: [],
                name: fileName,
                log: info.log,
            });
        }
    });
    return res;
}

/**
 * 获取插件详情（文件上传）后格式话为视图类型数据
 */
function getFormatFileValidityInfo(data: PluginLatestVersionOrigin): PluginValidity {
    const {auth, version, fileList, localServer} = data;
    const res: PluginValidity = {
        result: 0,
        auth,
        version,
        localServer,
        fileInfo: fileList!.map(file => ({
            detail: file.content,
            errorMsg: [] as string[],
            result: 0,
            name: fileNameInfoObj[file.name].fileName,
            log: fileNameInfoObj[file.name].log,
        })),
    };
    return res;
}

const api = {
    /** 插件开发版获取 pluginSessionId */
    getSession: createInterface<void, {sessionId: string}>('GET', '/plugin/session'),

    /** 插件审核、线上、分享版获取 pluginSessionId  */
    getPluginSessionId: (params: GetPluginSessionIdParams): Promise<string> =>
        request('GET', '/plugin/preview/session', params),

    /** domain 校验 */
    createCheckDomain: async (params: {
        sessionId: string;
        domain: PluginCreateDomainInfo['domain'];
        pluginId?: PluginId;
    }): Promise<PluginValidity> => {
        const res: PluginValidityOrigin = await request('POST', '/plugin/check/domain', params);
        return {...res, fileInfo: formatValidityInfo(res)};
    },
    /** local domain 校验 */
    createCheckLocalDomain: async (params: FormData): Promise<PluginValidity> => {
        const res = await request('POST', '/plugin/check/localdomain', params, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return {...res, fileInfo: formatValidityInfo(res)};
    },
    /** 文件校验 */
    createCheckFile: async (params: CheckFileParams): Promise<PluginValidity> => {
        // 文件校验可能超时，增加超时时间到10s
        const res: PluginValidityOrigin = await request('POST', '/plugin/check/file', params, {timeout: 10000});
        return {...res, fileInfo: formatValidityInfo(res)};
    },
    /** 创建插件 */
    pluginCreate: createInterface<PluginCreateInfo, {pluginId: PluginId; sessionId: string}>(
        'POST',
        SECURE_URL_ENUM.PluginAbilitySave
    ),
    /** 提审插件 */
    pluginSubmit: createInterface<PluginSubmitInfo, {pluginId: PluginId; sessionId: string}>(
        'POST',
        SECURE_URL_ENUM.PluginAbilitySubmit
    ),
    /** 获取插件信息 */
    getLatestVersion: async (params: {pluginId: string; sessionId: string}): Promise<PluginLatestVersion> => {
        const res: PluginLatestVersionOrigin = await request('GET', '/plugin/version/getLatestVersion', params).catch(
            () => {}
        );
        if (res?.sourceType === SourceType.Domain) {
            return {...res, validityInfo: getFormatDomainValidityInfo(res)};
        } else {
            return {...res, validityInfo: getFormatFileValidityInfo(res)};
        }
    },
    getPreviewUrl: createInterface<
        {
            authConfig?: {
                publicKey?: string;
                token?: string;
            };
            sessionId: string;
        },
        {previewUrl: string}
    >('POST', '/plugin/test/preview'),
    upload2bos: async (params: FormData): Promise<{fileId: string; fileUrl: string}> => {
        return await request('POST', '/plugin/file/upload2bos', params, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },
    getShareUrl: createInterface<{pluginId: PluginId}, {previewUrl: string}>('GET', '/plugin/test/share/preview'),
    getPreviewData: createInterface<
        {
            pluginSessionId: string;
            authConfig?: {token?: string; publicKey?: string};
            operationBindInfo?: OperationBindInfoItem[];
        },
        GetPreviewDataResponse
    >('POST', '/plugin/preview/data'),
    // 获取官方样式列表
    getOfficialPluginStyle: async (): Promise<PluginStyle[]> => {
        const res = await request('GET', '/plugin/style/getOfficialPluginStyle');
        res.forEach((item: PluginStyle) => {
            try {
                item.styleJsonObject = JSON.parse(item.styleJson || '{}');
            } catch (e) {
                console.error('getOfficialPluginStyle JSON解析失败:', item, e);
                item.styleJsonObject = {};
            }
        });
        return res;
    },
    // 获取当前插件绑定的样式列表
    getOperationStyle: async (params: {pluginId: string}): Promise<Record<string, OperationBindInfoItem>> => {
        const res = await request('GET', '/plugin/getOperationStyle', params);
        const operationBindInfoItemMap: Record<string, OperationBindInfoItem> = {};
        res?.forEach((item: OperationBindInfoItem) => {
            operationBindInfoItemMap[item.operationId] = item;
            try {
                item.styleJsonObject = JSON.parse(item.styleJson || '{}');
            } catch (e) {
                console.error('getOperationStyle JSON解析失败:', item, e);
            }
        });

        return operationBindInfoItemMap;
    },
    pluginBindAgent: createInterface<{pluginId: PluginId}, {isBindAgent: boolean}>('GET', '/plugin/agent/bind'),
};

export default api;
