export interface QuestionnaireForm {
    name: string;
    phone: number;
    email: string;
    organizationName: string;
    organizationSize: 'large' | 'medium' | 'small' | 'tiny' | 'micro';
    industry:
        | 'expressAndPost'
        | 'education'
        | 'publicAffairs'
        | 'finance'
        | 'transportation'
        | 'realEstate'
        | 'lifestyle'
        | 'itTech'
        | 'food'
        | 'travel'
        | 'politics'
        | 'entertainment'
        | 'efficiency'
        | 'eCommerce'
        | 'merchant'
        | 'businessServices'
        | 'publicWelfare'
        | 'social'
        | 'sports'
        | 'automotive'
        | 'information';
    aiKnowledge?: 'noUnderstanding' | 'basicUnderstanding' | 'intermediateUnderstanding' | 'advancedUnderstanding';
    cooperationType?: 'data' | 'capability' | 'application';
    developmentMethod?: 'visual' | 'manual' | 'prompt';
}
