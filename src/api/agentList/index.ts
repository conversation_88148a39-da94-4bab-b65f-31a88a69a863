/**
 * @file 我的智能体列表页的主要接口
 * <AUTHOR>
 */
import {
    GetAgentListResponse,
    GetAgentListParams,
    AgentInfo,
    AgentType,
    AgentSource,
    GetAgentListExtParams,
    GetAgentListExtResponse,
    AgentCount,
    serverToClientLabel,
    ExtDataType,
} from '@/api/agentList/interface';
import {createInterface, request} from '@/api/request';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {AuditStatus, DEFAULT_PAGINATION_SETTINGS, serverToClientStatus} from '@/modules/agentList/interface';
import {CommonAuditingNotice} from '@/modules/agentList/utils';

export const GET_AGENT_LIST_API = '/agent/list';

// 获取 agent 列表，列表页接口可能超时，和RD沟通设置超时时间9s
const getAgentList = async (params: GetAgentListParams) => {
    const res = await request('GET', GET_AGENT_LIST_API, params, {timeout: 9000});
    res.agentList.forEach((item: any) => {
        // 往期需求迭代中，没有保留原 status 字段，所以新增一个字段，后续应避免这种覆盖原字段的操作
        item.originStatus = item.status;
        item.labelStatus = serverToClientLabel(item.status);
        item.status = serverToClientStatus(item.status);
        /** 体验中心如果智能体为审核中状态，直接设置 message 并渲染 */
        if (item.status === AuditStatus.Auditing) {
            item.message = CommonAuditingNotice;
        }
    });
    return res;
};

const getAgentDetail = createInterface<{appId: string}, AgentInfo>('GET', '/agent/detail');

// 获取 agent 列表数量
const getAgentListNum = createInterface<object, AgentCount>('GET', '/agent/limit');

interface LingjingAgentListRes {
    /** 零代码返回列表数据 */
    codelessListRes: GetAgentListResponse;
    /** 工作流模式返回列表数据 */
    workflowListRes: GetAgentListResponse;
    /** 低代码返回列表数据 */
    lowCodeListRes: GetAgentListResponse;
    /** 用户是否来源于千帆 */
    isUserFromQianFan: boolean;
}

const getAgentListExt = createInterface<GetAgentListExtParams, GetAgentListExtResponse>('GET', '/agent/list/ext', {
    forbiddenToast: true,
    timeout: 10000,
});

export default {
    /** 获取 agent 列表数据 */
    getAgentList: async (params: GetAgentListParams): Promise<GetAgentListResponse> => getAgentList(params),

    /** 获取 agent 列表扩展数据 */
    getAgentListExt,

    // 获取零代码agent和低代码agent首页列表数据
    getLingjingFirstPageAgentList: async (params: GetAgentListParams): Promise<LingjingAgentListRes> => {
        return Promise.all([
            getAgentList({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.Codeless,
                pageNo: 1,
                pageSize: params.pageSize,
            }),
            getAgentList({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.Codeless,
                modeType: AgentModeType.Workflow,
                pageNo: 1,
                pageSize: params.pageSize,
            }),
            getAgentList({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.LowCode,
                pageNo: 1,
                pageSize: params.pageSize,
            }),
        ]).then(
            ([codelessListRes, workflowListRes, lowCodeListRes]: [
                GetAgentListResponse,
                GetAgentListResponse,
                GetAgentListResponse,
            ]) => {
                return {
                    codelessListRes,
                    workflowListRes,
                    lowCodeListRes,
                    isUserFromQianFan: codelessListRes.isFromQianFan,
                };
            }
        );
    },

    getLingjingFirstPageAgentExt: async (): Promise<{
        codelessExt: GetAgentListExtResponse;
        lowCodeExt: GetAgentListExtResponse;
        workflowExt: GetAgentListExtResponse;
    }> => {
        return Promise.all([
            getAgentListExt({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.Codeless,
                ...DEFAULT_PAGINATION_SETTINGS,
                type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution},${ExtDataType.tags},${ExtDataType.Dynamic},${ExtDataType.XmiAuditInfo}`,
            }),
            getAgentListExt({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.LowCode,
                ...DEFAULT_PAGINATION_SETTINGS,
                type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution},${ExtDataType.tags},${ExtDataType.Dynamic}`,
            }),
            getAgentListExt({
                agentSource: AgentSource.LingJing,
                agentType: AgentType.Codeless,
                ...DEFAULT_PAGINATION_SETTINGS,
                type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution},${ExtDataType.tags},${ExtDataType.Dynamic}`,
                agentModeType: AgentModeType.Workflow,
            }),
        ]).then(
            ([codelessExt, lowCodeExt, workflowExt]: [
                GetAgentListExtResponse,
                GetAgentListExtResponse,
                GetAgentListExtResponse,
            ]) => {
                return {
                    codelessExt,
                    lowCodeExt,
                    workflowExt,
                };
            }
        );
    },

    /** 删除 agent */
    deleteAgent: (appId: string): Promise<void> => {
        return request('POST', '/agent/delete', {}, {params: {appId}});
    },
    /** 获取 agent 详情 */
    getAgentDetail: async (appId: string): Promise<AgentInfo> => await getAgentDetail({appId}),

    // 获取 agent 列表数量
    getAgentListNum: async (): Promise<AgentCount> => await getAgentListNum({}),
};
