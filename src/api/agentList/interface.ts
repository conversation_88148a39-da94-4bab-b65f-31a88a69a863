import {AgentData} from '@/modules/agentList/interface';
import {AuditStatus, ServerAuditStatus} from '@/modules/agentList/interface';
import {WxAuthInfo, WxAuditStatus, XmiDeployInfo, AgentPublishChannelType} from '@/api/agentDeploy/interface';
import {DynamicFigureInfo, ShareTag} from '@/api/agentEdit/interface';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {CardTag} from '@/modules/center/interface';

/** Agent类型 */
export enum AgentType {
    /** 零代码 */
    Codeless = 1,
    /** 低代码 */
    LowCode = 2,
}

/** Agent来源类型 */
export enum AgentSource {
    /** 灵境平台 */
    LingJing = 1,
    /** 千帆 */
    QianFan = 2,
}

/**
 * Agent列表传参
 * 零代码列表传参：agentSource=1&agentType=1
 * 低代码列表传参：agentSource=1&agentType=2
 * 千帆列表传参：agentSource=2&agentType=1
 * 工作流列表传参：agentSource=1&agentType=1&modeType=1
 */
export interface GetAgentListParams {
    /** Agent类型 */
    agentType?: AgentType;
    /** Agent来源类型 */
    agentSource?: AgentSource;
    /** 每页显示的智能体数量 */
    pageSize?: number;
    /** 当前页码 */
    pageNo?: number;
    /** 模式类型 */
    modeType?: AgentModeType;
}

export interface GetAgentListResponse {
    /** agent 记录总数 */
    total: number;

    /** 当前页码 */
    pageNo: number;

    /** 每页显示的插件数量 */
    pageSize?: number;

    /** 用户是否来源于千帆 */
    isFromQianFan: boolean;

    /** agent 数据 */
    agentList: AgentData[];
}

/** 扩展参数类型 */
export enum ExtDataType {
    Wx = 1,

    /** 智能体调优报告和分发状态 */
    TuningReportAndDistribution = 2,

    /** 智能体头衔标签 */
    tags = 3,

    /** 数字人生成状态信息 */
    Dynamic = 4,

    /** 小米发布部署信息 */
    XmiAuditInfo = 5,
}

/**
 * 分发标签，将展示在我的智能体卡片上
 */
enum DistributeTag {
    /** 质量“一般”的智能体，进入搜索分发 */
    Search = '百度搜索分发',
    /** 质量“良好/优质”的智能体，进入全域分发 */
    Global = '百度全域推荐',
}

export interface AgentListExtInfo {
    /** 智能体ID */
    appId?: string;
    wxInfo?: {
        auditStatus: WxAuditStatus;
        auth: boolean;
    };
    distributeInfo?: {
        tag: DistributeTag;
    };
    redDotInfo?: {
        /** 智能体卡片-更多-分析按钮 是否展示红点 */
        analysisRedDot: boolean;

        /** 智能体卡片-编辑按钮 是否展示红点 */
        editSimilarRedDot: boolean;

        /** 智能体卡片-编辑按钮 是否展示红点(用于智能体抄袭/重复) */
        editPlagiarizeRedDot: boolean;

        /** 智能体卡片-编辑按钮 是否展示红点(用于小米审核信息) 不在可发布小米白名单返回false */
        editXmiAuditRedDot: boolean;

        /** 智能体卡片-分析按钮，是否展示知识库检索分析报告红点 */
        datasetAnalysisRedDot: boolean;
    };
    tags?: CardTag[];
    /** 智能体卡片-是否展示草稿时间 */
    isShowTime?: boolean;
    /** 智能体卡片-草稿时间 */
    latestTime: number | null;

    /** 调优带来的对话数 */
    conversationCount: number;

    /** 待调优的 QA 对数量 */
    qaCount: number;

    /** 数字人审核状态 */
    dynamicDigital?: Pick<DynamicFigureInfo, 'msg' | 'estimateGenDuration' | 'status'>;

    /** 小米发布审核状态 不在可发布小米白名单返回null */
    xmiDeployInfo?: XmiDeployInfo | null;
}

export interface GetAgentListExtParams extends GetAgentListParams {
    type: string;
    agentModeType?: AgentModeType;
}

export type GetAgentListExtResponse = AgentListExtInfo[];

/**
 * 智能体使用范围
 */
export enum ScopeStatus {
    /** 仅自己可访问 */
    Private = 1,
    /** 仅链接可访问 */
    Link = 2,
    /** 公开访问 */
    Public = 3,
}

export const AGENT_TYPE_NAME_MAP = {
    [AgentType.Codeless]: '零代码',
    [AgentType.LowCode]: '低代码',
};

export const AGENT_TYPE_WORKFLOW_NAME = '工作流';

export const SCOPE_STATUS_NAME = {
    [ScopeStatus.Public]: '公开',
    [ScopeStatus.Private]: '私有',
    [ScopeStatus.Link]: '仅链接',
};

export interface AgentInfo {
    /**  智能体ID */
    appId: string | null;
    /**  智能体名称 */
    name: string;
    /**  智能体头像 */
    logoUrl: string;
    /**  开场白 */
    description: string;
    /**
     * 这是个计算属性，每次更新时需要根据 agentJson 计算出来
     * link-仅链接可访问，private-仅自己可访问，public-公开访问
     */
    permission?: ScopeStatus;
    /**
     * 智能体状态
     * 历史原因，该字段的值在 src/api/request.ts 被拦截处理了
     * TODO：不应该request拦截层直接处理后端返回的status字段，而是应该依据后端返回的status字段值新增前端需要的字段，待技术优化
     */
    status?: AuditStatus | ServerAuditStatus;
    /**
     * 智能体状态
     * 前端新增的字段，取值等同于接口返回的status字段
     * 暂时在 src/api/request.ts 拦截处理
     */
    originalStatus?: AuditStatus | ServerAuditStatus;
    message?: string;
    /**
     * 已下线插件数量
     */
    offlinePluginNum?: number;
    /**
     * agent web化url
     */
    previewUrl: string;
    /** 是否公开配置 0 不公开、1 公开 */
    shareTag: ShareTag;
    /**  微信授权信息  */
    wxAuthList?: WxAuthInfo[];
    /** 是否首次部署小程序  */
    firstDeployWxMiniProgram: boolean;
    /**  部署渠道  */
    deployChannels?: AgentPublishChannelType[];
    /** 小米部署信息 */
    xmiDeployInfo?: XmiDeployInfo;
}

export interface AgentCount {
    /** 已经创建的智能体数量 */
    appNum: number;
    /** 该用户智能体数量上限 */
    appLimit: number;
}

/** 服务端返回的原始状态码，为现有的 ServerAuditStatus & AuditStatus  */
export enum AllAuditStatus {
    /** 开发中 */
    Developing = 2,
    /** 审核中，审核成功也合并到审核中，前端不外露审核成功，只会从审核中 -> 已上线 */
    Auditing = 3,
    /** 审核成功 */
    AuditSuccess = 4,
    /** 审核不通过 */
    AuditFailed = 5,
    /** 已上线 */
    Online = 6,
    /** 已下线 */
    Offline = 7,
    /** 强制下线 */
    ForcedOffline = 8,
    /** 修改中 */
    Editing = 9,
    /** 二次审核中 */
    SecondAuditing = 10,
    /** 二次审核失败 */
    SecondAuditFailed = 11,
    /** 二次审核成功 */
    SecondAuditSuccess = 12,
}

/** 智能体编辑页和智能体列表显示的类型 */
export enum AgentStatusLabel {
    /** 已发布 */
    PUBLISHED = 1,
    /** 已上线 */
    ONLINE = 2,
    /** 已下线 */
    OFFLINE = 3,
    /** 草稿 */
    DRAFTING = 4,
}

const LabelStatusMap: Record<AllAuditStatus, AgentStatusLabel> = {
    /** 草稿 */
    [AllAuditStatus.Developing]: AgentStatusLabel.DRAFTING,

    /** 已发布 */
    [AllAuditStatus.Auditing]: AgentStatusLabel.PUBLISHED,
    [AllAuditStatus.AuditSuccess]: AgentStatusLabel.PUBLISHED,
    [AllAuditStatus.AuditFailed]: AgentStatusLabel.PUBLISHED,

    /** 已上线 */
    [AllAuditStatus.Online]: AgentStatusLabel.ONLINE,
    [AllAuditStatus.Editing]: AgentStatusLabel.ONLINE,
    [AllAuditStatus.SecondAuditing]: AgentStatusLabel.ONLINE,
    [AllAuditStatus.SecondAuditFailed]: AgentStatusLabel.ONLINE,
    [AllAuditStatus.SecondAuditSuccess]: AgentStatusLabel.ONLINE,

    /** 已下线 */
    [AllAuditStatus.Offline]: AgentStatusLabel.OFFLINE,
    [AllAuditStatus.ForcedOffline]: AgentStatusLabel.OFFLINE,
};

/** 服务端返回的状态映射客户端显示的状态，默认草稿态 */
export const serverToClientLabel = (status: AllAuditStatus): AgentStatusLabel => {
    return LabelStatusMap[status] || AgentStatusLabel.DRAFTING;
};
