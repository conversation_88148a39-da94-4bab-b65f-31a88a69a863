import {request} from '@/api/request';
import {PluginFunctionParam, PluginFunctionsRes, PluginListDomain} from '../agentEdit/interface';
import {FavoriteStatus} from '../center/interface';
import {PluginListParams, PluginListRes} from '../pluginList/interface';

/** 【插件商店】服务端 Tabs 类型 */
export interface CenterPluginTab {
    id: number;
    description: string;
}

/** 【插件商店】获取所有 Tabs （不过滤） */
export const getAllPluginStoreTags = (): Promise<CenterPluginTab[]> =>
    request('GET', '/experhub/plugin/view/tab/tags?type=2');

/** 【插件商店】列表排序规则 */
export enum CenterPluginOrderType {
    NEWEST = 1,
    HOTEST = 2,
}

/** 【插件商店】列表来源 */
export enum CenterPluginResource {
    ALL = 0,
    OFFICIAL = 1,
    DEVELOPER = 2,
}

/** 【插件商店】列表查询参数 */
export interface CenterPluginParams {
    /** 标签 Id */
    tagId: string;
    orderType: CenterPluginOrderType;
    /** 关键词 */
    search: string;
    resource: CenterPluginResource;
    pageNo: number;
    pageSize: number;
}

/** 【插件商店】列表项详情信息 */
export interface CenterPluginListItem {
    /** 名称 */
    name: string;
    /** 描述 */
    description: string;
    /** 图标 */
    logoUrl: string;
    /** 插件Id */
    appId: string;
    /** 使用人数 */
    agentNum: number;
    /** 收藏人数 */
    favorNum: number;
    /** 开发者名称 */
    developerName: string;
    /** 是否已收藏 */
    isFavorited: boolean;
    /** 最新发布时间 */
    latestPublishTime: string;
    /** 标签 */
    tabName: string[];
    /** 是否为官方插件 */
    isOfficial: 0 | 1;
}

/** 【插件商店】获取插件商店列表 */
export const getPluginStoreList = (
    params: CenterPluginParams
): Promise<{
    total: number;
    pageNo: number;
    pageSize: number;
    plugins: CenterPluginListItem[];
    tabList: CenterPluginTab[];
}> => request('GET', '/experhub/plugin/search/list', params);

/** 【插件商店】通过插件 ID 更新插件收藏状态 */
export const updatePluginFavoriteStatus = (appId: string, status: FavoriteStatus): Promise<boolean> =>
    request('POST', '/experhub/favorites/update', {appId, status, favorType: 2});

/** 【插件商店】 Functions 输出参数类型 */
export enum PluginOutputDataType {
    String = 'string',
    Integer = 'integer',
    Float = 'float',
    Boolean = 'boolean',
    Object = 'object',
    Array = 'array',
}

/** 【插件商店】 Functions 输出协议 */
export interface PluginOutputSchema {
    name: string;
    description: string;
    required: boolean;
    type: PluginOutputDataType;
    schema?: PluginOutputSchema[];
}

/** 【插件商店】插件详情 */
export interface CenterPluginDetail {
    name: string;
    description: string;
    logoUrl: string;
    appId: string;
    favorNum: number;
    developerName: string;
    developerAvatar: string;
    isFavorited: boolean;
    latestPublishTime: string;
    /** 插件方法列表 */
    functionList: Array<{
        operationId: string;
        functionDesc: string;
        paramList: PluginFunctionParam[];
        outputs: PluginOutputSchema[];
    }>;
    agentNum: number;
    isOfficial: 0 | 1;
}

/** 【插件商店】通过插件 ID 获取插件详情信息 */
export const getPluginStoreDetail = (appId: string): Promise<CenterPluginDetail> =>
    request('GET', `/experhub/plugin/detail?appId=${appId}`);

enum AllData {
    All = 1,
    hasData = 2,
}
/** 发布时获取插件 Tabs */
export const getMyPluginPublishTags = (allData: boolean): Promise<CenterPluginTab[]> =>
    request('GET', `plugin/tags?type=${allData ? AllData.All : AllData.hasData}`);

/** 【添加插件弹窗】筛选类型 */
export enum MyPluginListType {
    MY = 'my',
    FAVOURITE = 'favourite',
    STORE = 'store',
}

/** 【添加插件弹窗】列表查询参数 */
export interface SelectPluginListSearchParams {
    pageNo: number;
    pageSize: number;
    keyword: string;
    /** 弹窗所处页面 */
    domain: PluginListDomain;
    /** 排序规则 */
    orderType: CenterPluginOrderType;
    /** 标签 */
    tagId: string;
    /** 列表类型（仅前端使用） */
    listType: MyPluginListType;
    /** 筛选流式插件 */
    filterStream: boolean;
}

/** 【添加插件弹窗】获取插件商店列表 */
export const getCenterPluginFunctions = (params: SelectPluginListSearchParams): Promise<PluginFunctionsRes> =>
    request('GET', '/plugin/queryPluginCenter', params);

/** 【添加插件弹窗】获取收藏插件列表 */
export const getFavouritePluginFunctions = (params: SelectPluginListSearchParams): Promise<PluginFunctionsRes> =>
    request('GET', '/plugin/favorites', params);

/** 【添加插件弹窗】获取我的插件列表 */
export const getMyPluginList = (params: PluginListParams): Promise<PluginListRes> =>
    request('GET', '/plugin/getMyPlugins', params);
