/**
 * @file 气泡交互组件（MessageUI）编译服务相关的请求
 * @doc 接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/zltnvqy9rltwUU
 * <AUTHOR>
 *
 */

import {createInterface} from '@/api/request';
import messageUIAxios from '@/api/messageUI/createAxios';
import type {CompileMessageUIParams, QueryMessageUIResParams} from '@/api/messageUI/interface';

// 对外暴露接口
export default {
    // 构建前请求可视化 UI 组件编译服务获取 taskId
    compileMessageUI: createInterface<CompileMessageUIParams, {taskId: string}>('POST', '/appflow/compile'),

    // 查询可视化 UI 组件编译结果（涉及请求重试，使用自定义的重试 axios 实例）
    queryMessageUIRes: (params: QueryMessageUIResParams) => messageUIAxios.get('/appflow/compile/query', params),
};
