/**
 * @file 气泡交互组件（MessageUI）请求相关的类型定义
 * <AUTHOR>
 *
 */
import {AxiosResponse} from 'axios-interface';

export enum CompileErrorMessage {
    NOT_FINISHED = '编译未完成',
    FAILED = '编译失败',
}

export interface CompileError extends Error {
    message: CompileErrorMessage;
    config: AxiosResponse['config'];
    response: AxiosResponse;
}

/** 可视化组件编译服务的请求参数 */
export interface CompileMessageUIParams {
    appId: string | undefined;
    componentConfig: UINode[];
}

export interface QueryMessageUIResParams {
    params: {
        taskId: string;
    };

    customConfig: {
        forbiddenToast: boolean;
    };
}

export interface UINode {
    /** 套件唯一 id */
    id: string;

    /** 套件中使用的可视化组件列表 */
    body: Array<{type: string}>;

    /** 可视化组件编译服务返回的版本号 */
    version?: number;
}

/** 编译可视化组件并上传到 BOS 后的描述信息 */
export interface MessageUICompileResUrl {
    [nodeId: string]: {
        moduleId: string;
        js: string;
        css: string;
        version: number;
    };
}
