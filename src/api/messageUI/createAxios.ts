/**
 * @file 创建 MessageUI 组件编译结果查询接口的 axios 实例
 * <AUTHOR>
 *
 */

import {AxiosResponse, AxiosError} from 'axios-interface';
import createRetryAxios from '@/api/createRetryAxios';
import {CompileErrorMessage, CompileError} from '@/api/messageUI/interface';

const createCompileError = (message: CompileErrorMessage, response: AxiosResponse): CompileError => {
    const error = new Error(message) as CompileError;
    error.config = response.config;
    error.response = response;

    return error;
};

const responseInterceptor = (response: AxiosResponse): AxiosResponse | CompileError => {
    const {data} = response;

    switch (data.errno) {
        // 编译成功
        case 0:
            return response;
        // 编译中
        case -1:
            throw createCompileError(CompileErrorMessage.NOT_FINISHED, response);
        // 编译失败
        case 1:
            throw createCompileError(CompileErrorMessage.FAILED, response);
        // 其他情况默认返回原始 response
        default:
            return response;
    }
};

// 仅在查询接口返回编译未完成时请求重试
const retryCondition = (error: AxiosError): boolean => {
    return error.message === CompileErrorMessage.NOT_FINISHED;
};

// 重试次数待定
const retries = 30;

// 重试延迟时间为 100ms
const retryDelay = () => 100;

const messageUIAxios = createRetryAxios({
    retryCondition,
    retries,
    retryDelay,
    responseInterceptor: responseInterceptor as (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>,
});

export default messageUIAxios;
