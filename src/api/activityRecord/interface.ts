/**
 *  @file 通用化中心--参与活动列表-公共的 TS 类型
 * <AUTHOR>
 */
import {ActivityStatus, ActivityType} from '@/api/activityList/interface';

export interface ActivitiesRecordInfo {
    id: number;
    /** 主标题 */
    title: string;
    /** 副标题 */
    subTitle: string;
    /** 2未开始、3进行中、4已结束、5已下线 */
    status: ActivityStatus;
    coverImg: string;
    /** 0.全部 1.训练营、2.主题活动、3.灵感中心 */
    type: ActivityType;
    /** 是否置顶 1是。0 否 */
    showTop?: number;
    /** 是否 新 1是。0 否 */
    showNew?: number;
    /** 截止时间 */
    endTime?: number | null;
    /** 参与活动时间 */
    participateInTime?: number | null;
}

// 默认分页参数
export const DEFAULT_PAGINATION_SETTINGS = {
    pageSize: 20,
    pageNo: 1,
};

/**
 * 参与活动记录-列表返回数据结构
 */
export interface GetRecordListResponse {
    /** 参与活动的记录总数 */
    total: number;

    /** 当前页码 */
    pageNo: number;

    /** 每页显示的记录数量 */
    pageSize: number;

    /** 参与活动记录数据 */
    activitys: ActivitiesRecordInfo[];
}
