/**
 * 网页链接和二维码请求query
 */

import {AppId} from './interface';

/** 查询智能体短链接和二维码参数 */
export interface AgentShareLinkParams {
    /** agentId */
    appId: AppId;
    /** 渠道值 */
    channel: string;
    /** 是否需要二维码，默认false，二维码含有灵境logo */
    qrCode?: boolean;
    /** 二维码宽度，范围是[200, 1280]，不给默认384px(128px*3倍图) */
    width?: number;
}

/** 查询智能体短链接和二维码返回结果 */
export interface AgentShareLinkInfo {
    /** agentId */
    appId: AppId;
    /** 网页链接 */
    shareUrl: string;
    /** 二维码图片url */
    qrCode?: string;
}

export interface AgentAppIdParams {
    /** agentId */
    appId: AppId;
}

/** 查询/更新API调用ID和密钥信息 */
export interface AgentAssistantInfo {
    /** agentId，即 assistant_id */
    appId: AppId;
    /** API调用密钥，即 assistant_key */
    secret: string;
}
