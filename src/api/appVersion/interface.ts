/**
 * @file 应用概览（应用详情+版本）-数据模型
 * <AUTHOR>
 */

import {AxiosResponse} from 'axios-interface';
import {MedalInfo} from '@/modules/center/interface';

export type AppId = string;

export interface Page {
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页宽
     */
    pageSize?: number;
}

export interface RequestResponse<T = any> extends AxiosResponse {
    errno: number;
    msg: string;
    requestId: string;
    data: T;
    timestamp: number;
}

/**
 * 查询单个应用入参
 */
export interface QueryAppParams {
    appId: AppId;
}

/**
 * 编辑应用入参
 */
export interface EditAppParams {
    /**
     * 应用Id
     */
    appId: AppId;
    /**
     * 应用名称
     */
    appName?: string;
    /**
     * 应用头像
     */
    appAvatar?: string;
    /**
     * 应用简介
     */
    appDesc?: string;
}

/**
 *  应用提审入参
 */
export interface SubmitAuditParams {
    /**
     * 应用ID
     */
    appId: AppId;
    /**
     * 版本号(低码传入-用户自定义；自主开发传versionCode)
     */
    version?: string;
    /**
     * 版本功能描述
     */
    content?: string;
    /**
     * 编辑配置项(低码传入-用于比较是否最新编辑版本)
     */
    // data?: flowjson
}

/**
 * 查询版本入参
 */
export interface GetVersionParams extends Page {
    /**
     * 应用ID
     */
    appId: AppId;
}

/**
 * 操作版本（公共的）入参(注：低代码提交审核在debug里面提交）
 */
export interface OperateVersionParams {
    /**
     * 应用ID
     */
    appId: AppId;
    /**
     * 版本号
     */
    code: string;
}

/**
 * 版本相关信息
 */
export interface VersionInfo {
    /**
     * 版本号（后端生成自增生成的）
     */
    code: string;
    /**
     * 版本号（用户上传的）
     */
    version: string;
    /**
     * 版本功能描述
     */
    content: string;
    /**
     * 发布时间
     */
    updateTime: string;
    /**
     * 发布人
     */
    operator: string;
    /**
     * 审核状态(当前版本状态)
     */
    status: VersionStatus;
    /**
     * 审核中(分级状态)
     */
    buildMsg?: string;
    /**
     * 审核失败原因
     */
    message?: string;
    /**
     * 是否可预览
     */
    canPreview?: boolean;
    /**
     * 是否可回滚
     */
    canRollback?: boolean;
    /**
     * 是否可提交审核
     */
    canAudit?: boolean;
    /**
     * 是否可撤回
     */
    canWithdraw?: boolean;
    /**
     * 强制下线原因(能力插件)
     */
    offlineMessage?: string;
    /**
     * 插件效果集文件信息&是否需要更新（能力&数据插件线上版本有该字段）
     * 若用户未上传评估集，此项为null
     */
    evaluateSet?: EvaluateFileInfo | null;
}

/**
 * 插件效果集文件信息&是否需要更新
 */
export interface EvaluateFileInfo {
    /**
     * 文件Id
     */
    fileId: string;
    /**
     * 文件名
     */
    fileName: string;
    /**
     * 文件Url
     */
    fileUrl: string;
    /**
     * 文件大小
     */
    fileSize: number;
    /**
     * 是否需要更新评估效果集（新版本上线时提示更新）
     */
    needUpdate: boolean;
}

/**
 * 版本状态
 */
export enum VersionStatus {
    /**
     * 已上线
     */
    Online = 1,
    /**
     * 开发中，还未提交审核
     */
    Developing = 3,
    /**
     * 审核中
     */
    Auditing = 4,
    /**
     * 审核失败
     */
    AuditFail = 5,
    /**
     * 审核通过
     * 审核流程优化，低代码没有通过状态，「审核通过」直接进入「发布中」
     */
    AuditPass = 6,
    /**
     * 已删除
     */
    Deleted = 9,
    /**
     * 发布中
     */
    Publishing = 11,
    /**
     * 已下线
     */
    Offline = 12,
    /**
     * 强制下线
     */
    ForcedOffline = 13,
}

/**
 * 历史版本列表
 */
export interface VersionList extends Page {
    /**
     * 总数据量
     */
    total: number;
    /**
     * 版本列表信息
     */
    dataList: VersionInfo[];
}

/**
 * 线上&审核&开发版本合集
 */
export interface OnlineAuditDevelopVersion {
    online?: VersionInfo;
    audit?: VersionInfo;
    develop?: VersionInfo;
}

/**
 * 预览链接
 */
export type PreviewLink = string;

export interface PreviewLinkInfo {
    appPreviewUrl: PreviewLink;
}

// 垂类标签枚举
enum TagCateCode {
    /**
     * 法律
     */
    lawyer = 'Q2C_LAWYER_CREDIT',
    /**
     * 医疗
     */
    doctor = 'DOCTOR_CREDIT_CATE',
    /**
     * 教育类
     */
    education = 'VERTICAL_CATE',
}

interface VerticalCertificationInfo {
    tagCateCode: TagCateCode;
    tagCateName: string;
    tagValue: string;
}
export interface AgentPosterInfo {
    appId: string;
    name: string;
    logoUrl: string;
    /* 开发者名称 */
    userName: string;
    /* 生成的分享链接 */
    previewUrl: string;
    /* 智能体简介 */
    description: string;
    /* 智能体形象 */
    digitalFigure: string;
    /* 开发者徽章 */
    developerBadges: MedalInfo[];
    /* 垂类信息 */
    verticalCertificationInfo: VerticalCertificationInfo[];
    /* 有无动态数字形象标识 */
    hasDynamicFigure: boolean;
}
