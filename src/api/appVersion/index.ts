/**
 * @file 应用概览（应用详情+版本）-数据接口
 * @doc 贴接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/_04Tw_UhRWyPvB
 * <AUTHOR>
 */

import {createInterface, request} from '../request';
import {AppInfo} from '../appList/interface';
import {
    EditAppParams,
    GetVersionParams,
    OnlineAuditDevelopVersion,
    OperateVersionParams,
    PreviewLinkInfo,
    QueryAppParams,
    SubmitAuditParams,
    VersionList,
    AgentPosterInfo,
} from './interface';
import {AgentShareLinkParams, AgentShareLinkInfo, AgentAssistantInfo, AgentAppIdParams} from './agentOutput';

// 对外暴露接口
export default {
    // 获取单个应用信息(包含审核信息)
    getLatestRecordByAppId: createInterface<QueryAppParams, AppInfo>('GET', '/audit/getLatestRecordByAppId'),
    // 编辑单个应用信息并且推审
    modifyApp: createInterface<EditAppParams, void>('POST', '/audit/pushAudit'),
    // 删除低代码智能体
    deleteApp: (appId: string): Promise<void> => {
        return request('POST', '/app/delete', {}, {params: {appId}});
    },
    // 获取应用审核和线上版本
    getOnlineAndAuditVersion: createInterface<QueryAppParams, OnlineAuditDevelopVersion>(
        'GET',
        '/version/getonlineauditdevelop'
    ),
    // 获取应用历史版本列表
    getHistoryVersions: createInterface<GetVersionParams, VersionList>('GET', '/version/gethistoricalversions'),
    // 对版本进行操作
    // /version/check
    submitAuditVersion: createInterface<SubmitAuditParams, void>('POST', '/version/audit', {forbiddenToast: true}),
    // 撤回申请
    withdrawAuditVersion: createInterface<OperateVersionParams, void>('POST', '/version/withdraw'),
    // 审核通过发布
    publishVersion: createInterface<OperateVersionParams, void>('POST', '/version/publish'),
    // 下线
    offlineVersion: createInterface<OperateVersionParams, void>('POST', '/version/offline'),
    // 回滚
    rollbackVersion: createInterface<OperateVersionParams, void>('POST', '/version/rollback'),
    // 删除
    deleteVersion: createInterface<OperateVersionParams, void>('POST', '/version/delete'),
    // 预览-生成可以调起C端框架的link
    previewVersion: createInterface<OperateVersionParams, PreviewLinkInfo>('GET', '/version/preview'),
    // 查询智能体短链接和二维码
    agentShareLinkInfo: createInterface<AgentShareLinkParams, AgentShareLinkInfo>('GET', '/agent/link'),
    // 查询智能体密钥
    getAgentSecretInfo: createInterface<AgentAppIdParams, AgentAssistantInfo>('GET', '/agent/secret/query'),
    // 重置智能体密钥
    resetAgentSecretInfo: (appId: string): Promise<AgentAssistantInfo> => {
        return request('POST', '/agent/secret/reset', {}, {params: {appId}});
    },

    // 智能体分享海报信息查询
    getAgentPosterInfo: createInterface<{appid: string}, AgentPosterInfo>('GET', '/experhub/agent/share'),
};
