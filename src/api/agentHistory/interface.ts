/**
 * @file agent 历史记录相关接口的数据接口
 * <AUTHOR>
 */
import {PluginSource} from '@/modules/center/interface';
import {CardData} from '@/modules/center/interface';

// 1 文本， 2 内置图标
export enum LabelValue {
    Text = 1,
    DefaultLogo = 2,
}

/**
 * 兼容灵感中心的 logo 数据格式
 */
export interface AgentLogo {
    logoUrl?: string;
    logoText?: {
        labelType: LabelValue;
        // 文本或者内置图标对应的id(1-6)
        labelValue: 1 | 2 | 3 | 4 | 5 | 6;
        color: 1 | 2 | 3 | 4 | 5 | 6;
    };
}

/**
 * 历史记录和主页卡片返回的 agent 数据格式
 */
export interface AgentInfo extends AgentLogo, CardData {
    // 首次新增历史记录时，没有 id 字段
    id?: number;
    appId: string;
    name: string;
    previewUrl: string;

    // 1灵感中心 2.灵境-对话式 3.灵境 agent
    pluginSource: PluginSource;
}

export type AgentHistory = AgentInfo[];
