/**
 * @file agent 历史记录相关接口
 * <AUTHOR>
 */

import {createInterface} from '../request';
import {AgentHistory} from './interface';

export const getAgentHistory = createInterface<void, AgentHistory>('GET', '/experhub/navigation/agents');

export const updateAgentHistory = createInterface<{appId: string}, void>('POST', '/experhub/navigation/updateHistory');

export const deleteAgentHistory = createInterface<{id: number}, void>('POST', '/experhub/navigation/history/delete');
