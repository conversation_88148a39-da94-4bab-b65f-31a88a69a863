/**
 * @file http error
 * <AUTHOR>
 */
import {message} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';

// server api response 通用错误信息
export const ERROR_RESPONSE_GENERAL_MSG: string = '服务器错误，请稍后再试';

// request 通用错误信息
export const ERROR_REQUEST_GENERAL_MSG: string = '网络异常，请检查网络连接是否正常';

// request 服务器繁忙通用错误信息
export const ERROR_REQUEST_BUSY_MSG: string = '服务器繁忙，请稍后再试';

// 安全校验错误
export const ERROR_SECURE_MSG: string = '操作过于频繁，先休息一下，稍后再试吧～';

interface ERROR_INFO {
    /**
     * error 类型
     */
    type?: string;
    /**
     * 错误码
     */
    code: number;
    /**
     * error 提示信息
     */
    msg: string;
}

// 前端 request error code 字符串枚举
export enum ErrorRequestCodeName {
    /** 昊天镜JS SDK 获取Jt Token errCode */
    NetWorkGetJTTokenError = 'NETWORK_GET_JT_TOKEN_ERROR',
}

// 前端 request error code 枚举
export enum ErrorRequestCode {
    NetworkUnknownError = -100,
    NetworkOfflineError = -10001,
    NetworkTimeoutError = -10002,
    NetworkHttpCodeError = -200,
    /** 昊天镜JS SDK 获取Jt Token 失败 */
    NetWorkGetJTTokenError = -20001,
    NetworkUnToastError = 0,
}

export enum ErrorResponseCode {
    // 用户访问权限异常
    UserAuthError = 1011,
    // 用户未登录
    UserNotLoggedIn = 10010,
    // 用户无访问权限
    UserNoPermission = 10020,
    // 用户账号异常，限制访问
    UserAccountException = 10002,
    // 用户访问参数错误
    UserAccessParameterError = 1002,
    // 应用不存在
    APPNOTEXIST = 20005,
    // 知识库接口，无数据的错误码
    NoData = 10009,
    // 智能体不存在
    AGENTNOTEXIST = 100003,
    // 智能体不存在
    PREVIEWAGENTNOTEXIST = 30001,
    // 智能体复制超出上限（上限是 50 个）
    AgentReachLimit = 100150,
    // 重复授权给服务商
    BindTpDuplicate = 10101,
    // 授权给多个服务商
    BindMultipleTp = 10102,
    // 服务商申请代运营的授权链接/二维码已过期
    TpAuthorizeLinkExpired = 10103,
    // 授权账号与服务商账号相同
    BindSameAccount = 10104,
    // 昊天镜安全校验失败
    JtSecurityCheckFailed = 1013,
    // 智能体创建超出个数上限
    AgentCreateReachLimit = 30006,
    // 智能体名称已存在
    AgentNameExist = 540036,
    // 语音包失效
    AudioPackageExpired = 30014,
    // 活动火爆错误码
    ACTIVITY_HOT = 80001,
}

/**
 * 前端 request error 全集
 * 为前端自定义，小于 0
 * 错误码规则：
 * 1. 所有错误码都小于 0
 * 2. 长度 3 位为大类错误码
 * 3. 长度 5 位为细分错误
 */
export const ERROR_REQUEST: {
    [ERROR_CODE: number]: ERROR_INFO;
} = {
    [ErrorRequestCode.NetworkUnknownError]: {
        // 未知的网络错误
        type: 'NETWORK_UNKNOWN_ERROR',
        code: ErrorRequestCode.NetworkUnknownError,
        msg: ERROR_REQUEST_GENERAL_MSG,
    },
    [ErrorRequestCode.NetworkOfflineError]: {
        // 无网，网络错误
        type: 'NETWORK_OFFLINE_ERROR',
        code: ErrorRequestCode.NetworkOfflineError,
        msg: ERROR_REQUEST_GENERAL_MSG,
    },

    [ErrorRequestCode.NetworkTimeoutError]: {
        // 请求超时
        type: 'NETWORK_TIMEOUT_ERROR',
        code: ErrorRequestCode.NetworkTimeoutError,
        msg: '请求超时，请稍后重试',
    },

    [ErrorRequestCode.NetworkHttpCodeError]: {
        // http 非 2xx 异常
        type: 'NETWORK_HTTP_CODE_ERROR',
        code: ErrorRequestCode.NetworkHttpCodeError,
        msg: ERROR_REQUEST_GENERAL_MSG,
    },
    [ErrorRequestCode.NetWorkGetJTTokenError]: {
        type: ErrorRequestCodeName.NetWorkGetJTTokenError,
        code: ErrorRequestCode.NetWorkGetJTTokenError,
        msg: ERROR_REQUEST_BUSY_MSG,
    },

    [ErrorRequestCode.NetworkUnToastError]: {
        // 请求被中断
        type: 'NETWORK_UN_TOAST_ERROR',
        code: ErrorRequestCode.NetworkUnToastError,
        msg: '请求被中断，请稍后重试',
    },
};

export function toastHttpErrorMessage(error: any, hideCode?: boolean) {
    if (error.errno === 0 || error.errno === undefined) {
        return;
    }

    const msg = error.msg || ERROR_RESPONSE_GENERAL_MSG;
    // todo: 待确认是否外露 code
    if (isMobileDevice()) {
        Toast.show({content: hideCode ? `${msg}` : `${msg}(${error.errno})`});
    } else {
        message.error(hideCode ? `${msg}` : `${msg}(${error.errno})`);
    }
}

// 前端 需要豁免异常上报的错误码
export const NormalServiceCodeConfig = [
    ErrorResponseCode.UserNotLoggedIn,
    ErrorResponseCode.AGENTNOTEXIST,
    ErrorResponseCode.PREVIEWAGENTNOTEXIST,
    ErrorResponseCode.NoData,
    ErrorResponseCode.UserNoPermission,
    ErrorResponseCode.UserAccountException,
    ErrorResponseCode.APPNOTEXIST,
    ErrorResponseCode.AgentReachLimit,
];

// 前端 需要豁免告警的url
export const ExemptWarningUrl = [
    '/agent/wx/auth/state',
    '/agent/ltm/list',
    '/build/result',
    '/workflow/getProcess',
    '/audit/text',
    '/audit/porn/url',
    '/dataset/wangpan/import/process',
];
