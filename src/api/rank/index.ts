/**
 * @file 榜单相关接口
 * <AUTHOR>
 */

import {BaseInfo, BaseInfoParam, RankInfo} from '@/api/rank/interface';
import {createInterface} from '@/api/request';

export default {
    /** 获取榜单基本信息与标签列表接口 */
    getBaseInfo: createInterface<BaseInfoParam, BaseInfo>('GET', '/experhub/leaderboard/baseInfo'),

    /** 获取指定标签的 agent 内容接口 */
    getAgentsByTag: createInterface<{tagId: string}, RankInfo>('GET', '/experhub/leaderboard/tag/agents'),
};
