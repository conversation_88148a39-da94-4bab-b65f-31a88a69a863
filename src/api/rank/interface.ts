export interface BaseInfoParam {
    leaderboardId: string;
}
export enum displayStyle {
    /** 排序 */
    sort = 1,
    /** 不排序 */
    unSort = 0,
}

export enum hasDescription {
    /** 不需要 */
    not = 0,
    /** 需要 */
    has = 1,
}

export interface Tags {
    /** 标签ID  */
    tagId: string;
    /** 标签名称  */
    tagName: string;
    /** 显示顺序  */
    displayOrder: number;
    /** 是否排序 1: 排序 0: 不排序 */
    displayStyle: displayStyle;
}

export interface BaseInfo {
    /** 排行榜ID */
    leaderboardId: string;
    /** 排行榜名称 */
    name: string;
    /** 榜单主题 */
    theme: string;
    /** 排行榜顶部图片 */
    mainImageUrl: string;
    /** pc顶部图片 */
    pcImageUrl: string;
    /** 排行榜背景色 */
    backgroundColor: string;
    /** 排行榜状体 */
    status: number;
    /** 是否有榜单描述 */
    hasEventDescription: hasDescription;
    /** 榜单描述 */
    eventDescriptionText: string;
    /** 榜单描述颜色 */
    eventDescriptionTextColor: string;
    /** 排行榜颜色开始 */
    leaderboardColorStart: string;
    /** 排行榜颜色结束 */
    leaderboardColorEnd: string;
    /** 排行榜按钮颜色开始 */
    buttonColorStart: string;
    /** 排行榜按钮颜色结束 */
    buttonColorEnd: string;
    /** 排行榜按钮文字颜色 */
    buttonTextColor: string;
    /** 排行榜名称颜色 */
    agentNameColor: string;
    /** 排行榜开发者名称颜色 */
    developerNameColor: string;
    /** 排行榜选中标签颜色 */
    tabSelectedColor: string;
    /** 排行榜未选中标签颜色 */
    tabUnselectedColor: string;
    /** 排行榜标签 */
    tags: Tags[];
}

export interface RankInfo {
    /** 标签ID */
    tagId: string;
    /** 标签名称 */
    tagName: string;
    /** 当前标签下的智能体 */
    apps: CardInfo[];
}

export interface CardInfo {
    /** 智能体ID */
    appId: string;
    /** 智能体logo */
    logoUrl: string;
    /** 智能体名称 */
    name: string;
    /** 智能体开发者名称 */
    developerName: string;
    /** 智能体预览链接 */
    previewUrl: string;
}
