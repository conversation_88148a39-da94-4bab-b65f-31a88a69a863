/**
 * @file 小红点提示相关接口
 * <AUTHOR>
 */

import {createInterface} from '../request';
import {GetRedDotParams, GetRedDotResponse, PostMarkReadParams} from './interface';

/** 标记小红点已读 */
export const postRedDotMarkRead = createInterface<PostMarkReadParams, null>('POST', '/agent/markread', {
    forbiddenToast: true,
});

/** 获取小红点数据 */
export const getRedDot = createInterface<GetRedDotParams, GetRedDotResponse>('GET', '/agent/redDot', {
    forbiddenToast: true,
});
