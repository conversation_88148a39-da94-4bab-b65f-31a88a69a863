/**
 * @file 心响app h5落地页相关关接口
 * api路径统一前缀/edu 已配置在xinxiangRequest
 */
import {AxiosProgressEvent} from 'axios';
import {Rule, RuleObject} from 'antd/es/form';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {request} from '../xinxiangRequest';
import {UploadPhotoResponse, AudioInfo, VideoInfo, VideoScriptData, VideoConfig} from './interface';

/**  上传头像 */
export const uploadPhoto = (
    file: FormData,
    onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
    signal?: AbortSignal
): Promise<UploadPhotoResponse> =>
    request('POST', 'pic/upload', file, {
        onUploadProgress,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 60 * 1000,
        signal,
    });

// 获取语音包列表
export const getSpeechList = (): Promise<AudioInfo[]> => request('GET', 'speech/list', {});

// 当前登录用户上传录音文件数据
export const uploadSpeech = (file: FormData, signal?: AbortSignal): Promise<AudioInfo> =>
    request('POST', 'speech/speechUpload', file, {
        forbiddenToast: true,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 10000,
        signal,
    });

// 视频生成
export const videoGenerate = (videoInfo: VideoInfo): Promise<void> => request('POST', 'video/generate', videoInfo);

// 视频脚本获取
export const getVideoScript = ({taskRunId}: {taskRunId: string}): Promise<VideoScriptData[]> =>
    request('GET', 'video/script', {taskRunId});

// 配置信息回显
export const getVideoConfig = (): Promise<VideoConfig> => request('GET', 'video/config', {});

/** 审核相关 */
export const getAuditTextRules = (prefixText: string, trigger?: string | string[]): Rule => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, text: string) => {
            if (text && text.trim()) {
                try {
                    const res = await request('POST', '/check/text', {text});
                    if (res.errno === 1) {
                        return Promise.reject(new Error(prefixText + res.errMsg));
                    }
                } catch (error: any) {
                    return Promise.reject(new Error(error.msg));
                }
            }
        },
    };
};

export const auditImage = async (imageUrl: string, prefixText?: string) => {
    const auditRes = await request('POST', '/check/image', {imageUrl}, {timeout: 15000});
    if (auditRes.errno === 1) {
        throw new Error((prefixText || '') + auditRes.errMsg);
    }
};
