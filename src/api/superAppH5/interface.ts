export interface UploadPhotoResponse {
    picUrl: string;
}

export enum SpeechType {
    UserSpeech = 1,
    OfficialSpeech = 2,
}

export enum SpeechStatus {
    Processing = 0,
    Success = 1,
    Failed = 2,
}

export enum SpeechTokenStatus {
    // 生效中
    Active = 0,
    // 已过期
    Expired = 1,
    // 已删除，移动端录音上传成功会删除
    Removed = 2,
}

export enum SpeechSource {
    TTS = 1,
    AIGC = 2,
}

export interface AudioInfo {
    id: number;
    speechName: string;
    // 我的语音和官方语音
    speechType: SpeechType;
    // 语音来源 1-TTS  2-AIGC
    speechSource: SpeechSource;
    speechUrl: string;
    // 官方语音 id，个人语音时值相同
    ttsId: string;
    // 个人语音 id，个人语音生成中或生成失败、或者为官方语音时为空字符串
    mid: string;
    // 语音状态
    status: SpeechStatus;
    // 语音生成失败原因
    msg: string;
    /** 语速，取值0-15，默认为5，调整值=0.1spd+0.5 */
    spd: number | null;
    /** 音调，取值0-15，默认为5，调整值=0.1pit+0.5 */
    pit: number | null;
}

export interface VideoInfo {
    taskRunId: string;
    speechId: number;
    name: string;
    pic: string;
}

export interface VideoScriptData {
    type: string;
    detailContent: string;
    ttsContent: string;
}

export interface VideoConfig {
    pic: string;
    name: string;
    speechId: number;
    speechName: string;
}
