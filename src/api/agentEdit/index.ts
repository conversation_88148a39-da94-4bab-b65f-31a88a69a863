/**
 * @file 创建/编辑智能体接口
 * @doc 贴接口文档 https://ku.baidu-int.com/d/-2RY4KKn7MMLdx
 * <AUTHOR>
 */

import {AxiosProgressEvent} from 'axios';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {defaultSuggestionPrompt} from '@/store/agent/initState';
import {AgentPublishChannelType} from '@/api/agentDeploy/interface';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {getThreeInOnePrompt} from '@/utils/agent-compatible';
import {request} from '../request';
import {
    AgentConfigResponse,
    AgentPermission,
    OfficialPluginResponse,
    PluginFunctionsRes,
    BatchPluginFunctionsParams,
    AgentConfigV2,
    ShareTag,
    AgentDataset,
    AgentPluginInfo,
    EngineType,
    AgentConfigResponseExtraData,
    WebSearchPluginId,
    WorkflowBindInfoItem,
    WorkflowBindParams,
    WorkflowBindStyleStatus,
    BaijiahaoInfo,
    WelcomeType,
    RecommendType,
    AgentConfigResponseWithOld,
    RagInterveneCallType,
    AgentUpdateResponse,
    AgentCreateResponse,
    AgentConfigRequest,
    UpdateAgentConfigRequest,
    UploadPhotoV2Response,
} from './interface';

export type {
    AgentPluginInfo,
    OfficialPlugin,
    AgentPluginFunction,
    AgentPluginFunctionIds,
    PluginFunctionParam,
} from './interface';

export {AgentPermission, PluginTag} from './interface';

// agentConfig 兼容逻辑，都应该聚合在此处
export const formatServerAgentConfig = (data: AgentConfigResponseWithOld): AgentConfigResponse => {
    // 存量智能体，添加数字形象默认配置
    data.agentInfo.digitalFigure = data.agentInfo.digitalFigure || null;

    // 存量智能体，添加自动追问默认配置
    data.agentJson.autoSuggestion = data.agentJson.autoSuggestion || {
        isEnabled: true,
        prompt: defaultSuggestionPrompt,
        promptEnabled: false,
    };
    // 存量智能体，添加长期记忆默认配置
    data.agentJson.longTermMemory = data.agentJson.longTermMemory || {
        isEnabled: true,
    };
    // 存量智能体，添加联网搜索配置
    data.agentJson.webSearch = data.agentJson.webSearch || {
        isEnabled: true,
    };

    // 存量智能体，添加动态开场白配置
    data.agentInfo.dynamicDescription = data.agentInfo.dynamicDescription || {
        isEnabled: true,
    };
    // 为兼容后端不能直接下线 welcomeType 和 recommendType，将存量数据直接重置为普通
    data.agentInfo.welcomeType = WelcomeType.ORDINARY;
    data.agentInfo.recommendType = RecommendType.ORDINARY;

    // prompt 三合一需求兼容逻辑，将 systemAssistant 转成 system，删掉 systemLabelStatus 和 agentMode 字段
    if (data.agentJson.agentMode === 'assistant') {
        data.agentJson.system = data.agentJson.systemAssistant
            ? getThreeInOnePrompt(data.agentJson.systemAssistant)
            : '';
    }

    // 兼容旧智能体 callType 为 null 的情况
    data.agentJson.ragIntervene =
        data.agentJson.ragIntervene?.callType === null
            ? {
                  ...data.agentJson.ragIntervene,
                  callType: RagInterveneCallType.Custom,
              }
            : data.agentJson.ragIntervene;

    // 移除兼容字段
    delete data.agentInfo.existingUserWelcome;
    delete data.agentInfo.newUserWelcome;
    delete data.agentInfo.recommendsPairs;
    delete data.agentInfo.recommendType;
    delete data.agentInfo.welcomeType;
    delete data.agentInfo.useFrameworkType;
    delete data.agentInfo.digitalEnabled;
    delete data.agentInfo.systemLabelStatus;

    delete data.agentJson.plugins;
    delete data.agentJson.officialPlugins;
    delete data.agentJson.agentMode;
    delete data.agentJson.systemAssistant;
    return data;
};

/**  获取来自百家号的信息（ 头像和名称 ） */
export const getBjhSourceInfo = (): Promise<BaijiahaoInfo> => request('GET', '/agent/source/bjh');

/**  获取已完成的知识库 */
export const getAgentDataset = (): Promise<AgentDataset[]> => request('GET', '/dataset/all/available');

/**
 * 获取智能体配置
 * warning 副作用：get 函数会调 preview post 接口保存数据
 * @param appId
 */
// eslint-disable-next-line complexity,max-statements
export const getAgentConfig = async (appId: string): Promise<[AgentConfigResponse, AgentConfigResponseExtraData]> => {
    const data: AgentConfigResponseWithOld = await request('GET', '/agent/conf', {appId});

    /**
     * 在当前方法中处理完智能体数据后，有些信息需要传递到智能体创建页，但不会将这些数据通过 preview 接口保存
     * 因此将这部分额外的数据单独返回并保存到全局 store 中，后续再有类似场景，可继续扩张 extraData 即可，该扩展数据不会破坏原本的智能体配置数据
     */
    const extraData: AgentConfigResponseExtraData = {
        // 当前配置的插件列表中存在 "搜索增强" 插件时需要调用 popup 接口，获取当前智能体是否提示 "「搜索增强」插件已升级为联网检索功能"
        hasWebSearchPlugin: data?.agentJson?.newPlugins?.some(
            (plugin: AgentPluginInfo) => plugin.pluginId === WebSearchPluginId
        ),
    };

    // 移除 "搜索增强" 插件，并设置 hasWebSearchPlugin 为 true
    if (extraData.hasWebSearchPlugin) {
        data.agentJson.newPlugins = data.agentJson.newPlugins.filter(
            (plugin: AgentPluginInfo) => plugin.pluginId !== WebSearchPluginId
        );
    }

    formatServerAgentConfig(data);

    return [data, extraData];
};

/**  预览智能体 */
export const testAndPreviewAgent = (agentConfig: UpdateAgentConfigRequest): Promise<AgentUpdateResponse> =>
    request('POST', SECURE_URL_ENUM.AgentUpdate, agentConfig, {
        forbiddenToast: true,
    });

/** 创建智能体 */
export const createAgent = (agentConfig: AgentConfigRequest): Promise<AgentCreateResponse> =>
    request('POST', SECURE_URL_ENUM.AgentCreate, agentConfig, {
        forbiddenToast: true,
    });

/**  发布智能体 */
export const publishTestedAgent = (
    appId: string,
    permission: AgentPermission,
    shareTag?: ShareTag,
    deployChannels?: AgentPublishChannelType[],
    publishWorkflow?: boolean
): Promise<AgentConfigV2> =>
    request('POST', SECURE_URL_ENUM.AgentPublish, {
        appId,
        permission,
        shareTag,
        deployChannels,
        publishWorkflow,
    });

/**  获取精选插件 */
export const getOfficialPlugin = (): Promise<OfficialPluginResponse> => request('GET', '/plugin/queryOfficialPlugin');

/**  根据pluginId获取workflowId */
export const getWorkflowId = (params: {pluginId: string}): Promise<string> =>
    request('GET', '/workflow/getWorkflowId', params);

/**  根据appId、pluginId获取工作流绑定信息 */
export const getBindInfo = (params: {appId: string; pluginId?: string}): Promise<WorkflowBindInfoItem[]> =>
    request('GET', '/workflow/getBindInfo', params);

/**  当前智能体工作流指定节点绑定样式 */
export const bindStyle = (params: WorkflowBindParams): Promise<boolean> =>
    request('POST', '/workflow/bindStyle', params);

/**  获取当前智能体工作流是否绑定样式 */
export const queryWorkflowBindStyleStatus = (params: {
    appId: string;
    pluginIds: string[];
}): Promise<WorkflowBindStyleStatus[]> => request('POST', '/workflow/queryWorkflowStyle', params);

/**  根据插件id批量获取插件Functions信息 */
export const batchPluginFunctions = (params: BatchPluginFunctionsParams): Promise<PluginFunctionsRes> =>
    request('POST', '/plugin/batchQueryPluginFunction', params);

/**  上传头像 */
export const uploadPhoto = (
    file: FormData,
    onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
    signal?: AbortSignal
): Promise<string> =>
    request('POST', '/app/uploadPhoto', file, {
        onUploadProgress,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 60 * 1000,
        signal,
    });

/**  上传头像V2 */
export const uploadPhotoV2 = (
    file: FormData,
    onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
    signal?: AbortSignal
): Promise<UploadPhotoV2Response> =>
    request('POST', 'app/uploadPhoto/v2', file, {
        onUploadProgress,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 60 * 1000,
        signal,
    });

/**  上传数字形象 */
export const uploadDigitalFigure = (file: FormData, signal?: AbortSignal): Promise<string> =>
    request('POST', '/agent/uploadImage', file, {
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 60 * 1000,
        signal,
    });

/** 获取可选模型列表 */
export const getModelList = (): Promise<EngineType[]> => request('GET', '/agent/model/list');

/** 黄反机审结果直接返回（非规则） */
export const checkYellowUrl = async (url: string) => {
    try {
        const res = await request('POST', '/audit/porn/url', {
            urls: [url],
            appId: new URLSearchParams(location.search).get('appId') || '',
        });

        // errno = 0 检测未通过，存在黄反url；errno = 1 检测通过
        if ('errno' in res && res.errno === 0) {
            return false;
        }
    } catch (error: any) {
        return false;
    }

    return true;
};

export interface PluginStyleStatus {
    pluginId: string;
    operationId: string;
    hasStyle: boolean;
    hasDefaultStyle: boolean;
    isPolish: boolean;
}

export const queryPluginStyleStatus = (params: {
    appId: string;
    functions: Array<{pluginId: string; operationId: string}>;
}): Promise<PluginStyleStatus[]> => {
    return request('POST', '/plugin/ui/status', params);
};

interface PluginOperationDetail {
    styleFormat: number;
    uiJson?: {
        dataSource?: string;
        styleId?: number;
        styleJson?: string;
        styleVersion?: string;
    };
    isUpdate: boolean;
    openApi: string;
}

export const queryPluginOperationDetail = (params: {
    appId: string;
    pluginId: string;
    operationId: string;
}): Promise<PluginOperationDetail> => {
    return request('GET', '/plugin/ui/query', params);
};

export const bindPluginStyle = (params: {
    appId: string;
    pluginId: string;
    operationId: string;
    dataSource?: string;
    styleId?: number;
    styleVersion?: string;
    styleJson?: string;
    isPolish?: boolean;
}): Promise<null> => request('POST', '/plugin/ui/config', params);

// 工作流润色状态返回值
export interface WorkflowPolishStatus {
    pluginId: string;
    operationId: string;
    workflowId: string;
    isPolish: boolean;
}

// 查询工作流润色状态
export const queryWorkflowPolishStatus = (
    params: {
        appId: string;
        functions: Array<{workflowId: string; operationId: string}>;
    },
    signal?: AbortSignal
): Promise<WorkflowPolishStatus[]> => {
    return request('POST', '/workflow/ui/status', params, {signal});
};

// 绑定工作流润色
export const bindWorkflowPolish = (params: {
    appId: string;
    workflowId: string;
    operationId: string;
    isPolish?: boolean;
}): Promise<null> => request('POST', '/workflow/ui/config', params);

// 判断小程序是否下线
export const checkOnline = async (url: string) => {
    try {
        const res = await request('POST', '/audit/online', {
            urls: [url],
            appId: new URLSearchParams(location.search).get('appId') || '',
        });

        // errno = 0 检测未通过，存在黄反url；errno = 1 检测通过
        if ('errno' in res && res.errno === 0) {
            return false;
        }
    } catch (error: any) {
        return false;
    }

    return true;
};
