/**
 * @file 复制智能体相关接口
 * @doc 接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/7f7vttx0vKFQB0#anchor-761c3210-221d-11ef-a9c1-5fc8f81cf0ab
 * <AUTHOR>
 */
import {createInterface, request} from '@/api/request';
import {formatServerAgentConfig} from '../agentEdit';
import {AgentConfigResponse} from '../agentEdit/interface';
import {
    DuplicateAgentParams,
    DuplicateAgentResponse,
    DuplicateValidationResponse,
    GetDuplicatedAgentConfParams,
} from './interface';

/** 是否可复制的前置校验 */
export const duplicateAgentValidate = (appId: string): Promise<DuplicateValidationResponse> =>
    request(
        'GET',
        '/agent/duplicateValidation',
        {appId},
        {
            forbiddenToast: true,
        }
    );

export default {
    /** 查看待复制的智能体配置 */
    getAgentConf: async (params: GetDuplicatedAgentConfParams): Promise<AgentConfigResponse> => {
        const data = await request('GET', '/agent/conf/duplicate', params);
        formatServerAgentConfig(data);
        return data;
    },

    /** 复制智能体 */
    duplicate: createInterface<DuplicateAgentParams, DuplicateAgentResponse>('POST', '/agent/duplicate', {
        forbiddenToast: true,
        timeout: 20000,
    }),
};
