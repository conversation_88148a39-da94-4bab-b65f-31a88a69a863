/**
 * @file 复制智能体相关接口的类型
 * <AUTHOR>
 */

import {AgentSpeech} from '../agentEdit/interface';

/**
 * 被复制的智能体来源
 */
export enum DuplicateAgentSource {
    /** 来自体验中心 */
    Center = 1,
    /** 来自我的智能体 */
    MyAgent = 2,
}

export interface GetDuplicatedAgentConfParams {
    appId: string;
}

/** 复制智能体接口的参数类型 */
export interface DuplicateAgentParams {
    /** 被复制的 agent appid */
    appId: string;

    /** 来源 */
    from: DuplicateAgentSource;

    /** 复制后的智能体名称 */
    name: string;
}

/** 复制智能体接口的返回数据类型 */
export interface DuplicateAgentResponse {
    /** 复制后的 appid */
    appId: string;
}

export interface PluginInfo {
    pluginName: string;
    logo: string;
}

export interface ShortcurInfo {
    text: string;
    type: string;
    icon: string;
}

export interface DuplicateValidationResponse {
    /** 知识库 */
    datasetNames: string[];
    /** 私有工作流 */
    workflows: PluginInfo[] | null;
    /** 失效插件 */
    invalidPlugins: PluginInfo[] | null;
    /** 自定义语音 */
    privateSpeech: AgentSpeech | null;
    /** 失效知识库 */
    invalidDatasetNames: string[];
    /** 商业化能力组件 */
    business: string[];
    /** 快捷指令组件 */
    shortcuts: ShortcurInfo[];
}
