import {AppId} from '@/api/appList/interface';
import {
    Q2CAuditStatus,
    QAInfluenceTagType,
    QAMediaTag,
    VideoAuditStatus,
    VideoGenerateStatus,
    VideoType,
} from '@/modules/agentPromptEditV2/pc/TuningTab/q2c/constant';
import {QaDetail} from '../agentEditV2/interface';

/**
 * 调优问题 Id
 */
export type TuningId = number;

/**
 * 平台视频 Id
 */
export type VideoId = number;

/**
 * 百家号视频 Id
 */
export type Nid = string;

export enum SubmitStatus {
    // 待提交
    Unsubmit = 0,
    // 已提交
    Submit = 1,
}

export enum BlockComponentType {
    /** 纯文本 */
    Markdown = 'markdown',

    /** 视频 +  文本 */
    VideoText = 'videoText',
}

// 1: 文本校对, 2:结构清晰, 3:内容增强, 4:内容扩充, 5:情感强化, 6:变短、提炼总结, 7:emoji
export enum TuningAction {
    Proofread = 1,
    Clarity = 2,
    Enhance = 3,
    Expand = 4,
    Amplify = 5,
    Summarize = 6,
    Emoji = 7,
}

export interface VideoAuditInfo {
    /** 视频 id */
    videoId: VideoId;
    /** 审核态 */
    status: VideoAuditStatus;
    /** 审核失败原因 */
    reason: string;
}

export interface VideoGenerateInfo {
    /** 视频类型 */
    videoType: VideoType;
    /** 生成状态 */
    generateStatus: VideoGenerateStatus;
    /** 生成状态错误信息 */
    generateMsg: string;
    msg: string;
    // 短答案
    shortAnswer: string;
    // 封面模板标识
    coverTemplateUrl: string;
    // 封面图
    coverUrl: string;
    // 人像图片 Url
    portraitUrl: string;
    // 视频脚本
    scripts: string;
}

export interface Q2CCommonParams {
    appId: AppId;
    tuningId: TuningId;
    answer: string;
}

export interface PreviewParams extends Q2CCommonParams {
    videoContent: VideoContentItem[];
}

export interface AITuningParms extends Q2CCommonParams {
    action?: TuningAction;
    selected?: string;
}

export interface VideoTextBlock {
    data: {
        videoList: Array<{
            url: string;
            poster: string;
            width: number;
            height: number;
            duration: number;
        }>;
        text: {
            value: string;
        };
    };
    component: BlockComponentType.VideoText;
}

export interface MarkdownBlock {
    data: {
        value: string;
    };
    component: BlockComponentType.Markdown;
}

// TODO: 兼容旧协议，0.6一期上线后删除
export interface OldPageABlocks {
    data: {
        /** answer 内容 */
        content: string;
        /** A页和搜索前端约定的标识位（平台暂时不用） */
        newSdk: string;
    };
    /** 数据类型，固定值 markdown */
    component: 'markdown';
}

/** 协议：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/oxr9E-gFQMjH3o */
export interface GetPageADetailResponse {
    /** query 内容 */
    title: string;
    displayData: {
        /** 推荐 agent 内容 */
        agentContent: Array<{
            /** agent 名称 */
            agentName: string;

            // TODO: 兼容旧协议，0.6一期上线后删除
            /** answer内容区 */
            blocks: Array<VideoTextBlock | MarkdownBlock> | OldPageABlocks;

            /** icon 下标 */
            icon: string;
            /** 智能体头像 */
            logo: string;
            /** 智能体标签内容 */
            tagList: string[];
            /** 引导问题信息 */
            guideList: {
                /** 继续问跳转的 url */
                url: string;
                /** 追问词 */
                text: string;
            };
        }>;
    };
}

/** 协议：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/oxr9E-gFQMjH3o */
export interface GetPageCDetailResponse {
    /** 答案区内容 */
    agentData: {
        blocks: MarkdownBlock[];
    };
    agentInfo: {
        name: string;
        /** 头像信息 */
        logo: {
            labelValue: string;
        };
    };

    // TODO: 兼容旧协议，0.6一期上线后删除
    /** 旧协议，答案区内容 */
    gendata: {
        component: Array<{
            /** 数据类型，固定值 markdown */
            componentName: 'markdown';
            data: string;
        }>;
    };
}

export interface AITuningParams {
    appId: AppId;
    tuningId: TuningId;
    answer: string;
}

export interface AITuningRes {
    answer: string;
}

export interface VideoContentItem {
    videoUrl: string;
    width: number;
    height: number;
    duration: number;
    videoId: VideoId;
}

export type QAVideoContentItem = VideoContentItem & VideoAuditInfo & VideoGenerateInfo;

export interface SubmitTuningParams extends Q2CCommonParams {
    question: string;
    videoContent?: VideoContentItem[];
}

export interface SaveTuningParams extends Q2CCommonParams {
    question: string;
    submit: SubmitStatus;
    videoContent?: VideoContentItem[];
}

// 问答对状态
export enum QAStatus {
    // 生成中
    Generating = 99,
    // 待提交
    Unsubmitted = 0,
    // 已提交
    Submitted = 7,
    // 已分发
    Distributed = 8,
}

export enum AnswerStatus {
    // 正确
    Correct = 0,
    // 无答案-其他异常
    NoAnswer = 95,
    // 无答案-UIJSON异常
    UIJsonNoAnswer = 951,
    // 获取异常-其他异常
    Error = 96,
    // 获取异常-限流异常
    RateLimitError = 961,
}

interface Page {
    pageNo?: number;
    pageSize?: number;
}

export interface QA extends Omit<QaDetail, 'tuningId' | 'status'> {
    tuningId: TuningId;
    status: QAStatus;
    saveTime: string;
    /** 影响力标签 */
    tagHot: string;

    /** 影响力标签类型 */
    tagHotType: QAInfluenceTagType;

    /** 内容标签 */
    tagContents: QAMediaTag[];

    /** 审核状态 */
    auditStatus: Q2CAuditStatus;

    /** 审核状态信息 */
    auditMsg: string;

    /** 视频内容 */
    videoContent?: QAVideoContentItem[];
}

export interface MarketQA {
    queryId: string;
    question: string;
    /** 影响力标签 */
    tagHot: string;
    /** 影响力标签类型 */
    tagHotType?: QAInfluenceTagType;
    /** 内容标签 */
    tagContents?: QAMediaTag[];
}

export const enum TuningType {
    /** 知识库调优 */
    Good = 1,
    /** 反馈调优 */
    Bad = 2,
    Q2C = 3,
}

export interface TuningListParams extends Page {
    appId: string;
    status: QAStatus[];
    tuningType: number;
    auditStatus?: Q2CAuditStatus[];
}

export interface SubmitMarketQAParams {
    appId: string;
    questionList: MarketQA[];
}

export interface ChangeQAParams {
    appId: string;
    tuningId: TuningId;
}

export interface QueryMarketQAParams {
    appId: string;
    keyword?: string;
}

export interface MarketQARes {
    remainedNum: number;
    marketList: MarketQA[];
}

export interface QueryAnswerParams {
    tuningId: TuningId;
}

export interface CoverParams {
    appId: AppId;
    tuningId: TuningId;
    coverUrl: string;
}

export interface CoverDetail {
    url: string;
    isCover: boolean;
}

export interface TuningDetail {
    status: QAStatus;
    tuningId: TuningId;
    appId: AppId;
    question: string;
    answer: string;
    saveTime: string;
    failStatus: AnswerStatus;
}

export interface ReAnswerParams {
    appId: AppId;
    question: string;
    tuningId: TuningId;
}

export interface BjhVideoListParams extends Page {
    search?: string;
}

export interface BjhVideoListRes {
    total: number;
    count: number;
    dataList: BjhVideoDetail[];
}

export interface BjhVideoDetail {
    status: VideoAuditStatus;
    videoType: VideoType;
    nid: Nid;
    title: string;
    videoUrl: string;
    coverUrl: string;
    duration: number;
    articleId: string;
    /** 平台上传的视频才会有此字段，数字人视频一定有此字段 */
    videoId?: VideoId;
}

export interface submitBjhVideoParams {
    tuningId: TuningId;
    videoUrl: string;
    title: string;
    coverUrl: string;
}

export interface submitBjhVideoRes {
    videoId: VideoId;
}

export interface getBjhVideoInfoParams {
    tuningId: TuningId;
    title: string;
    videoUrl?: string;
    downloadUrl?: string;
    nid?: Nid;
}

export interface getBjhVideoInfoRes {
    videoUrl: string;
    duration: number;
    height: number;
    width: number;
    coverUrl: string;
    videoId: VideoId;
}

interface VideoStatus {
    /** 视频 id */
    videoId: VideoId;
    /** 审核态 */
    status?: VideoAuditStatus;
    /** 审核失败原因 */
    reason?: string;
    /** 生成状态 */
    generateStatus?: VideoGenerateStatus;
    /** 生成状态错误信息 */
    generateMsg?: string;
    /** 视频 url */
    videoUrl?: string;
    /** 视频时长 */
    duration?: number;
}

export interface getBjhVideoStatusRes {
    tuningId: TuningId;
    videoStatuses: VideoStatus[];
}

export interface DigitalInitResponse {
    shortAnswer: string;
    coverTemplateList: Array<{
        coverTemplateName: string;
        coverTemplateUrl: string;
        coverExampleUrl: string;
    }>;
}

export interface GenerateVideoRequest {
    tuningId: TuningId;
    shortAnswer: string;
    coverUrl: string;
    scripts: string;
    title: string;
}

export interface GenerateVideoResponse {
    videoId: VideoId;
    coverUrl: string;
}

export interface CropImagesResponse {
    photoImage: string;
    coverWithoutPhotoImage: string;
}
