/**
 * @file 开发者问答调优 Q2C 相关接口
 * <AUTHOR>
 */

import {createInterface, request} from '@/api/request';
import {QaDetailListResponse} from '../agentEditV2/interface';
import {
    Q2CCommonParams,
    GetPageADetailResponse,
    GetPageCDetailResponse,
    AITuningRes,
    SubmitTuningParams,
    QA,
    TuningListParams,
    SubmitMarketQAParams,
    QueryMarketQAParams,
    MarketQARes,
    ChangeQAParams,
    QueryAnswerParams,
    CoverParams,
    CoverDetail,
    TuningId,
    TuningDetail,
    ReAnswerParams,
    SaveTuningParams,
    AITuningParms,
    BjhVideoListParams,
    BjhVideoListRes,
    submitBjhVideoParams,
    submitBjhVideoRes,
    getBjhVideoInfoParams,
    getBjhVideoInfoRes,
    getBjhVideoStatusRes,
    PreviewParams,
    DigitalInitResponse,
    CropImagesResponse,
    GenerateVideoRequest,
    GenerateVideoResponse,
    VideoId,
    QAVideoContentItem,
    Nid,
} from './interface';

/** 获取预览A页数据 */
export const getPageADetail = createInterface<PreviewParams, GetPageADetailResponse>('POST', '/tuning/detail/pageA');

/** 获取预览C页数据 */
export const getPageCDetail = createInterface<PreviewParams, GetPageCDetailResponse>('POST', '/tuning/detail/pageC');

/**
 * 提交编辑结果（进分发）
 */
export const submitTuning = createInterface<SubmitTuningParams, void>('POST', '/tuning/submitQA');

/**
 * 保存编辑结果（暂存，不进分发）
 */
export const saveTuning = createInterface<SaveTuningParams, void>('POST', '/tuning/save');

/**
 * AI 润色
 */
export const answerAITuning = (data: AITuningParms, signal?: AbortSignal): Promise<AITuningRes> => {
    return request('POST', '/tuning/save/ai', data, {
        timeout: 60 * 1000,
        signal,
    });
};

/**
 * 获取调优列表
 */
export const getTuningList = (data: TuningListParams, signal?: AbortSignal): Promise<QaDetailListResponse> => {
    return request('POST', '/tuning/list', data, {
        timeout: 20 * 1000,
        signal,
    });
};

/**
 * 获取可领取调优列表
 */
export const getMarketList = createInterface<QueryMarketQAParams, MarketQARes>('GET', '/tuning/market', {
    timeout: 25 * 1000,
});

/**
 * 领取调优问答
 */
export const claimMarketQA = createInterface<SubmitMarketQAParams, void>('POST', '/tuning/addMore');

/**
 * 换一换
 */
export const changeQA = createInterface<ChangeQAParams, QA>('POST', '/tuning/change', {
    timeout: 20 * 1000,
    forbiddenToast: true,
});

/**
 * 获取Q对应的A
 */
export const getAnswerByTuningId = createInterface<QueryAnswerParams, QA>('GET', '/tuning/getAnswer');

/**
 * 获取封面
 */
export const getCoverDetail = createInterface<Q2CCommonParams, CoverDetail[]>('POST', '/tuning/detail/covers');

/**
 * 提交封面
 */
export const setCover = createInterface<CoverParams, void>('POST', '/tuning/save/cover');

/**
 * 编辑器内容详情
 */
export const getTuningDetail = createInterface<{tuningId: TuningId}, TuningDetail>('GET', '/tuning/detail');

/**
 * 重新生成answer
 */
export const regenerateAnswer = (data: ReAnswerParams, signal?: AbortSignal): Promise<TuningDetail> => {
    return request('POST', '/tuning/reAnswer', data, {
        timeout: 90 * 1000,
        signal,
    });
};

/**
 * 获取百家号视频列表
 */
export const getBjhVideoList = createInterface<BjhVideoListParams, BjhVideoListRes>('POST', '/tuning/video/list');

/**
 * 百家号视频提交
 */
export const submitBjhVideo = createInterface<submitBjhVideoParams, submitBjhVideoRes>('POST', '/tuning/submitVideo', {
    timeout: 20 * 1000,
});

/**
 * 获取百家号视频信息，并结合了转存功能
 * 注意：
 * 请求参数 downloadUrl 为百家号视频链接，需要转存后使用，所以传入 downloadUrl 后会返回转存后的 videoUrl
 * 请求参数 videoUrl 为普通的 bos 链接，返回视频信息
 */
export const getBjhVideoInfo = createInterface<getBjhVideoInfoParams, getBjhVideoInfoRes>(
    'POST',
    '/tuning/video/info',
    {
        timeout: 20 * 1000,
    }
);

/**
 * 获取百家号视频审核状态
 */
export const getBjhVideoStatus = createInterface<{tuningId: TuningId}, getBjhVideoStatusRes>(
    'GET',
    '/tuning/video/status'
);

/**
 * 数字人视频生成初始化
 */
export const getDigitalInit = createInterface<{tuningId: TuningId}, DigitalInitResponse>(
    'POST',
    '/tuning/digital/init',
    {timeout: 15000}
);

/**
 * 根据 id 查询视频信息
 */
export const getVideoContent = createInterface<{videoId: VideoId}, QAVideoContentItem & {title?: string}>(
    'GET',
    '/tuning/video/content'
);

/**
 * 数字人裁剪图片查询接口
 */
export const getCropImages = createInterface<
    {tuningId: TuningId; coverTemplateUrl: string; shortAnswer: string},
    CropImagesResponse
>('POST', '/tuning/digital/queryCropImages', {timeout: 10000});

/**
 * 生成数字人视频接口
 */
export const generateVideo = createInterface<GenerateVideoRequest, GenerateVideoResponse>(
    'POST',
    '/tuning/digital/generateVideo'
);

/**
 * 生成数字人视频封面
 */
export const generateVideoCover = createInterface<Omit<GenerateVideoRequest, 'script'>, GenerateVideoResponse>(
    'POST',
    '/tuning/digital/generateCover'
);

/**
 * 提交数字人视频封面
 */
export const submitVideoCover = createInterface<{tuningId: TuningId; coverUrl: string}, GenerateVideoResponse>(
    'POST',
    '/tuning/video/submitCover'
);

/**
 * 视频化脚本生成
 */
export const generateScript = createInterface<{tuningId: TuningId}, {scripts: string}>(
    'POST',
    '/tuning/digital/script',
    {timeout: 30000}
);

/**
 * 真人视频封面列表接口
 */
export const getCoverList = createInterface<{tuningId: TuningId; videoUrl: string}, {coverUrlList: string[]}>(
    'POST',
    '/tuning/video/coverList',
    {timeout: 15000}
);

/**
 * 删除并下架视频 从所有问答和百家号中删除
 * 添加视频弹框处，videoId（平台上传的视频才会有值），所有没有videoId的时候，传nid(百家号视频id)
 * 问答处，有 videoId，所以只传 videoId
 */
export const deleteVideo = createInterface<{nid?: Nid; videoId?: VideoId}, void>('GET', '/tuning/video/delete');

/**
 * 数字人是否存在（数字人创建生成成功后，需要发布以后才会存在）
 */
export const getIsDigitalExist = createInterface<{appId: string}, boolean>('GET', '/tuning/digital/status');
