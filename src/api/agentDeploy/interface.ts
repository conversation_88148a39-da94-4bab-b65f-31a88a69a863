/**
 * @file 智能体部署相关数据定义
 * @doc 文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/mu-vdzgYgrwk3b
 * <AUTHOR>
 */
import {WechatOfficialType} from '@/utils/loggerV2/interface';
export type WxAppId = string; // 微信appid

/** 发布微信渠道类型 */
export enum WxAccountType {
    /** 微信服务号 */
    WxServer = 1,
    /** 微信订阅号 */
    WxSubscribe = 2,
    /** 微信小程序 */
    WxMiniApp = 3,
}

/** 发布小米渠道类型 */
export enum XmiChannelType {
    /** 小米应用商店 */
    XmiAppStore = 4,
}

/** 智能体发布渠道类型 */
export type AgentPublishChannelType = WxAccountType | XmiChannelType;

export interface WxAuthParam {
    appId: string;
    /** 微信账号类型 */
    type: WxAccountType;
}

export interface WxPrivacyUserInfo {
    developerName: string; // 开发者名称
    contact: string; // 开发者手机号 或 邮箱
}

export interface WxPrivacyParam {
    appId: string;
    user: WxPrivacyUserInfo;
}

export interface WxAuthInfoResponse {
    /** 微信appid */
    wxAppId: WxAppId;
    /** 微信授权状态 */
    auth: boolean;
}

export interface WxPrivacyInfoResponse {
    wxPrivacyInfo: {
        user: WxPrivacyUserInfo;
        updateDate: string;
        effectiveDate: string;
    };
    developerName: string;
}

export interface WxAuthBindParam extends WxAuthParam {
    /** 微信授权码 */
    wxAppId: WxAppId;
}

/** 微信授权二维码信息 */
export interface WxAuthQrCodeUrlResponse {
    /** 是否已经授权 */
    auth: boolean;
    /** 授权链接 */
    jumpUrl: string;
    /** 过期时间 */
    expireTime: number;
}

export enum WxAuditStatus {
    /** 待审核 */
    Waiting = -1,
    /** 审核成功 */
    Success = 0,
    /** 审核失败 */
    Failed = 1,
    /** 审核中 */
    Auditing = 2,
    /** 审核撤回 */
    Withdraw = 3,
    /** 审核延后 */
    Delay = 4,
}

export interface WxAuthInfo extends WxAuthInfoResponse {
    type: WxAccountType;
    /**  微信审核状态 */
    auditStatus: WxAuditStatus;
    /**  授权错误信息 */
    message: string;
    /** 是否设置隐私协议 */
    isSetPrivacy: boolean;
}

export const WxAccountLogMap = {
    [WxAccountType.WxServer]: WechatOfficialType.SERVER,
    [WxAccountType.WxSubscribe]: WechatOfficialType.SUBSCRIBE,
    [WxAccountType.WxMiniApp]: WechatOfficialType.MINIAPP,
};

/** 小米发布渠道审核状态 */
export enum XmiDeployAuditStatus {
    /** 审核中 */
    AUDITING = 0,
    /** 审核通过 */
    AUDIT_PASS = 1,
    /** 审核不通过 */
    AUDIT_FAIL = 2,
    /** 已下线 */
    OFFLINE = 3,
}

/** 小米发布渠道相关信息 */
export interface XmiDeployInfo {
    /** 授权状态 true-已授权，false-未授权 */
    auth: boolean;
    /** 审核状态 0-审核中，1-审核通过，2-审核不通过，3-已下线 */
    auditStatus: XmiDeployAuditStatus | null;
    /** 是否已部署 */
    deployed: boolean;
    /** 审核信息/审核失败原因
     * 示例1：已部署至小米应用商店
     * 示例2：小米应用商店发布失败，失败原因：xxxxx */
    message: string;
}
