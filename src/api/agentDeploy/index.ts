/**
 * @file 智能体部署相关接口
 * @doc 接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/r3xWVrx2yW/9acfc3bbedaf41
 * <AUTHOR>
 */

import {createInterface} from '@/api/request';
import {
    WxAuthParam,
    WxPrivacyParam,
    WxAuthBindParam,
    WxAuthInfoResponse,
    WxAuthQrCodeUrlResponse,
    WxPrivacyInfoResponse,
} from './interface';

export default {
    /**
     * 获取微信授权状态
     */
    getWxAuthState: createInterface<WxAuthParam, WxAuthInfoResponse>('GET', '/agent/wx/auth/state', {
        forbiddenToast: true,
    }),
    /**
     * 获取微信授权二维码进行授权
     */
    bindWxAccount: createInterface<WxAuthBindParam, WxAuthQrCodeUrlResponse>('POST', '/agent/wx/bindPreAuthCode', {
        forbiddenToast: true,
    }),
    /**
     * 解绑
     */
    unbindWxAccount: createInterface<WxAuthParam, void>('POST', '/agent/wx/unbind', {
        forbiddenToast: true,
    }),
    /**
     * 查询隐私协议 相关用户信息接口
     */
    getWxPrivacy: createInterface<{appId: string}, WxPrivacyInfoResponse>('GET', '/agent/wx/privacy', {
        forbiddenToast: true,
    }),
    /**
     * 设置隐私协议 相关用户信息接口
     */
    setWxPrivacy: createInterface<WxPrivacyParam>('POST', '/agent/wx/privacy/submit', {
        forbiddenToast: true,
    }),
};
