import {AxiosProgressEvent} from 'axios';
import {DynamicFigureInfo} from '@/api/agentEdit/interface';
import {request} from '../request';
import {MIME_TYPES} from './constant';

export const getDynamicFigureAccess = (
    appId: string
): Promise<{
    dynamicDigital: boolean;
}> => request('GET', `/feature/access/v2?featureName=dynamicDigital&appId=${appId}`);

/** 生成数字人配置 */
export const yieldDynamicFigure = (payload: {
    appId: string;
    url: string;
    duration: number;
    greenScreen: boolean;
    endSource?: string;
    proportion?: string;
}) => request('POST', '/dynamicFigure/create', payload, {forbiddenToast: true});

/** 删除数字人配置 */
export const deleteDynamicFigure = (payload: {appId: string; uniqueId?: string}) =>
    request('POST', '/dynamicFigure/del', payload);

/** 数字人视频生成状态 */
export enum DynamicVideoStatus {
    NEW = 1,
    MAKING = 2,
    DONE = 3,
    ERROR = -1,
}

/** 数字人生成视频预览方向 */
export const DynamicVideoDirection = {
    VERTICAL: 0 as const,
    HORIZONTAL: 1 as const,
} as const;

/** 数字人视频生成&查询 */
export const previewDynamicFigure = (
    appId: string
): Promise<{
    videoUrl: string;
    status: DynamicVideoStatus;
    reason: string;
    proportion?: string;
    screen: number;
}> => request('GET', '/tuning/agc/video/info', {appId});

/**
 * 获取分片上传签名
 *
 * 发送请求到指定接口获取分片上传的签名信息
 *
 * @returns 返回一个 Promise，解析为一个包含 uploadId 和 key 的对象
 */
const getMultipartUploadSign = (
    ext: string
): Promise<{
    uploadId: string;
    key: string;
}> => request('GET', `/dynamicFigure/getUploadId?ext=${ext}`);

/**
 * 上传文件分片
 *
 * @param payload 上传分片所需的参数
 * @param payload.uploadId 上传任务的唯一标识
 * @param payload.key 上传文件的Key
 * @param payload.file 文件对象
 * @param payload.number 当前分片的序号
 * @param payload.total 总分片数
 * @param onUploadProgress 上传进度回调函数
 * @returns 返回一个Promise，表示上传操作的结果
 */
const postMultipartFileSlice = (
    payload: {
        uploadId: string;
        key: string;
        file: File;
        number: number;
        total: number;
    },
    onUploadProgress: (progressEvent: AxiosProgressEvent) => void
): Promise<void> =>
    request('POST', '/dynamicFigure/upload', payload, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        forbiddenToast: true,
        timeout: 60000,
        onUploadProgress,
    });
const mergeMultipartFileSlice = (payload: {
    uploadId: string;
    key: string;
    total: number;
    contentType?: string;
}): Promise<string> => request('GET', '/dynamicFigure/merge', payload, {timeout: 60000});

/**
 * 将文件按指定大小分片
 *
 * @param file 要分片的文件对象
 * @param chunkSize 每个分片的大小（以字节为单位）
 * @returns 返回分片后的文件数组
 */
function sliceFile(file: File, chunkSize: number): File[] {
    const chunks: File[] = [];
    let startPos = 0;
    while (startPos < file.size) {
        chunks.push(new File([file.slice(startPos, startPos + chunkSize)], file.name));
        startPos += chunkSize;
    }
    return chunks;
}

/**
 * 根据文件名猜测文件内容的MIME类型
 *
 * @param filename 文件名
 * @returns 返回文件内容的MIME类型，如果无法确定则返回 'application/octet-stream'
 */
function guessContentType(filename: string) {
    let ext = filename.split('.').pop()?.toLowerCase();
    if (!ext || !ext.length) {
        return 'application/octet-stream';
    }

    if (ext.startsWith('.')) {
        ext = ext.substr(1);
    }
    return MIME_TYPES[ext.toLowerCase()] || 'application/octet-stream';
}

/**
 * 异步上传文件到服务器，支持多线程分块上传。
 *
 * @param file 要上传的文件对象
 * @param context 上传上下文，包含以下属性：
 * - abort: 是否取消上传
 * - onProcessChange: 上传进度变化时的回调函数，参数为上传进度百分比
 * - threadCount: 上传时使用的线程数
 * - chunkSize: 分块大小，单位为字节，默认为5MB
 * @returns 返回一个Promise，成功时解析为上传文件的URL
 * @throws 如果上传过程中发生错误，则Promise会被拒绝，并抛出错误信息
 */
export const multipartUploadFile = async (
    file: File,
    context: {
        abort: boolean;
        onProcessChange: (progress: number) => void;
        threadCount: number;
        chunkSize?: number;
    }
): Promise<string> => {
    const DEFAULT_CHUNK_SIZE = 1 * 1024 * 1024;
    const contentType = guessContentType(file.name);
    /** 获取签名 */
    const sign = await getMultipartUploadSign(file.name.split('.').pop() || '');

    const filesList = sliceFile(file, context.chunkSize || DEFAULT_CHUNK_SIZE);
    const totalLength = filesList.length;
    const totalList = new Array(totalLength).fill(0);

    /** 任务列表 */
    const tasksList = filesList.map((fileSlice, index) => async () => {
        const payload = {
            uploadId: sign.uploadId,
            key: sign.key,
            file: fileSlice,
            number: index + 1,
            total: totalLength,
        };
        try {
            await postMultipartFileSlice(payload, progressEvent => {
                totalList[index] = progressEvent.loaded;
                context.onProcessChange?.(totalList.reduce((a, b) => a + b, 0) / file.size);
            });
        } catch (error) {
            console.error(error);
            totalList[index] = 0;
            return Promise.reject(error);
        }
    });

    let currentTaskIndex = 0;
    /** 防止 context 改变后，请求中的任务继续执行 */
    let aborted = false;

    const retryList: Array<() => Promise<void>> = [];
    /** 生成执行器 */
    const asyncRunner = () =>
        new Promise<void>(async (resolve, reject) => {
            while (currentTaskIndex < tasksList.length) {
                if (context.abort || aborted) {
                    aborted = true;
                    reject(new Error('取消上传'));
                    break;
                }

                const task = tasksList[currentTaskIndex];
                currentTaskIndex++;
                try {
                    await task();
                } catch (error) {
                    console.error(error);
                    retryList.push(task);
                    // reject(error);
                }
            }

            resolve();
        });

    await Promise.all(new Array(context.threadCount).fill(0).map(() => asyncRunner()));

    /** 重试时使用单线程上传 */
    for (const task of retryList) {
        await task();
    }

    /** 合并文件 */
    const res = await mergeMultipartFileSlice({
        uploadId: sign.uploadId,
        key: sign.key,
        total: filesList.length,
        contentType,
    });

    return res;
};

/**
 * 数字人生产校验
 * @param appId 应用ID
 * @returns 返回一个Promise，表示校验的结果
 */

export const verifyProductDynamicFigure = (params: {appId: string}) =>
    request('GET', '/dynamicFigure/modifyCheck', params, {
        forbiddenToast: true,
    });

/**
 * 数字人查询
 * @param appId 智能体id
 * @param type 类型
 * @returns DynamicFigureInfo
 */
export const getDynamicFigure = (param: {appId: string; type: number}): Promise<DynamicFigureInfo> =>
    request('GET', '/dynamicFigure/query', param);
