export const enum GetPrivacyInfoStatus {
    /** 编辑中的智能体 */
    editing = 0,

    /** 线上智能体 */
    online = 1,
}

export interface GetPrivacyInfoParams {
    appId: string;
    status: GetPrivacyInfoStatus;
}

/**
 * 隐私设置允许被查看的选项
 */
export const enum PrivacyInfoOption {
    /** 对话数据 */
    conversation = 'conversation',
    /** 用户数据 */
    user = 'user',
    /** 反馈数据 */
    action = 'action',
    /** 流量数据 */
    traffic = 'traffic',
    /** 商业数据 */
    goods = 'goods',
}
export interface GetPrivacyInfoResponse {
    privacyInfo: {
        options: PrivacyInfoOption[];
        user: {
            /** 开发者名称 */
            developerName: string;
            /** 联系方式：电话或邮箱 */
            contact: string;
        };
        /** 更新日期 */
        updateDate: string;
        /** 生效日期 */
        effectiveDate: string;
    } | null;
    /** 使用的插件名称，形如【xxx】，【xxx】 */
    pluginNames: string | null;
    /** 使用的工作流名称，形如【xxx】，【xxx】 */
    workflowNames: string | null;
}

export interface PostPrivacySubmitParams {
    options: PrivacyInfoOption[];
    user?: {
        /** 开发者名称 */
        developerName: string;
        /** 联系方式：电话或邮箱 */
        contact: string;
    };
    appId: string;
}
