/**
 * @file 隐私设置相关接口
 * <AUTHOR>
 */

import {createInterface} from '../request';
import {PostPrivacySubmitParams, GetPrivacyInfoResponse, GetPrivacyInfoParams} from './interface';

/** 获取隐私设置信息 */
export const getPrivacyInfo = createInterface<GetPrivacyInfoParams, GetPrivacyInfoResponse>('GET', '/agent/privacy');

/** 提交隐私设置保存 */
export const postPrivacySubmit = createInterface<PostPrivacySubmitParams, void>('POST', '/agent/privacy/submit');
