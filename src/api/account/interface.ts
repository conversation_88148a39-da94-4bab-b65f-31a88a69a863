// 账号中心 & 实名认证
export enum AccountQualifyType {
    /** 个人 */
    Person = 1,
    /** 企业 */
    Enterprise = 2,
    /** 政府 */
    Gov = 3,
    /** 机构（事业单位） */
    Department = 4,
    /** 其他 */
    Other = 5,
}

/** 个人实名/真实性认证状态枚举 */
export enum PersonQualifyStatus {
    /** 未实名/未认证 */
    NONE = 0,
    /** 已实名/认证通过 */
    PASS = 1,
}

/** 主体资质/主体真实性认证 审核状态枚举 */
export enum QualifyStatus {
    /** 未认证/待审核 */
    NONE = 0,
    /** 认证中/审核中 */
    AUDIT = 1,
    /** 认证通过/审核通过 */
    PASS = 2,
    /** 认证不通过/审核未通过 */
    RETURN = 3,
}

export enum EnterpriseType {
    company = 1,
    personBusiness = 2,
}

/** 信誉真实性认证类型 */
export enum AuthType {
    DEFAULT = 0,
    /** 法人人脸识别 */
    LEGAL = 1,
    /** 对公打款 */
    REMIT = 2,
}

// 职业认证类型, 0-默认未进行职业认证 1-职业 2-医生 3-律师
export enum CareerType {
    Default = 0,
    Profession = 1,
    Doctor = 2,
    Lawyer = 3,
}

// 职业认证状态, 0-未认证 1-认证中 2-认证成功 3-认证失败
export enum CareerAuthStatus {
    // 未认证
    Default = 0,
    // 认证中
    Auditing = 1,
    // 认证成功
    Pass = 2,
    // 认证失败
    Fail = 3,
}

/**
 * @description 职业信息
 */
export interface CareerInfo {
    careerType: CareerType;
    careerAuthStatus: CareerAuthStatus;
    practicingInstitution: string;
}

// 账号信息
export interface AccountInfo {
    /** 账号类型 */
    accountType: AccountQualifyType;
    /** 个人账号信息 */
    personInfo?: PersonInfo;
    /** 企业或其他非个人主体账号信息 */
    customerInfo?: CustomerInfo;
    /** 职业信息 */
    career?: CareerInfo;
}

export enum AccountQualifyStatus {
    // 无实名
    Init = 'init',
    OrgPrimeAudit = 'OrgPrimeAudit',
    // 企业认证通过
    OrgPrimePass = 'OrgPrimePass',
    // 企业认证审核中
    OrgSeniorAudit = 'OrgSeniorAudit',
    // 企业真实性通过，政府直接等同于真实性通过
    OrgSeniorPass = 'OrgSeniorPass',
    // 个人认证通过
    PersonPrimePass = 'PersonPrimePass',
    // 个人真实性通过
    PersonSeniorPass = 'PersonSeniorPass',
}

export interface AccountInfoPage extends AccountInfo {
    qualifyStatus: AccountQualifyStatus;
}

// 0-未认证 1-新增 2-通过 4-拒绝 6-待审 9-考察期 8-取消
export enum OriginalCareerAuthStatus {
    UnAuth = 0,
    New = 1,
    Passed = 2,
    Refused = 4,
    Pending = 6,
    Examine = 9,
    Cancel = 8,
}

export interface PersonInfo {
    realName: string;
    idCardNumber: string;
    /** 实名认证状态 */
    realStatus: PersonQualifyStatus;
    /** 真实性认证状态 */
    authStatus: PersonQualifyStatus;
    careerType: CareerType;
    profession: string;
    careerAuthStatus: CareerAuthStatus;
    originalCareerAuthStatus: OriginalCareerAuthStatus;
}

export enum CustomerAuthStatus {
    NONE = 0,
    AUDIT = 1,
    PASS = 2,
    RETURN = 3,
    ACCESS = 4, // 豁免
}

export interface CustomerInfo {
    customerId: number;
    customerName: QualifyStatus;
    entityStatus: QualifyStatus;
    entityAuditMsg: string;
    // 真实性认证状态 0-未认证，1-认证中，2-认证通过，3-认证不通过 4-认证豁免
    authStatus: CustomerAuthStatus;
    // 真实性认证类型
    authType: AuthType;
    customerType: Partial<AccountQualifyType>;
}

export interface PassPersonInfo {
    realName: string;
    certCode: string;
}

/**
 * 开发者等级枚举
 * 现有 L0 - L6 7 个等级，L0 为初始等级，L6 为最高等级
 */
export enum DeveloperLevel {
    L0 = 0,
    L1 = 1,
    L2 = 2,
    L3 = 3,
    L4 = 4,
    L5 = 5,
    L6 = 6,
}

/**
 * 开发者相关信息
 */
export interface DeveloperInfo {
    /** 开发者等级 0: L0, 1: L1, ... */
    developerLevel?: DeveloperLevel;
}

export enum BjhQualifyStatus {
    AUDIT = 1,
    PASS = 2,
    UNPASS = 3,
    FREEZE = 8,
    CANCELED = 9,
    FROZEN = 10,
}

export enum BjhQualifyType {
    INDIVIDUAL = 'individual',
    ORGANIZATION = 'organization',
}

export enum BjhQualifySubType {
    INDIVIDUAL = 'individual',
    COMPANY = 'company',
    MEDIA = 'media',
    GOV = 'govt',
    OTHER = 'other',
}

export interface BjhQualifyInfo {
    name: string;
    type: BjhQualifyType;
    subType: BjhQualifySubType;
    orgName: string;
    status: BjhQualifyStatus;
}

export interface PublicInstitutionInfo {
    institutionName: string;
    institutionCode: string;
    institutionLicense: string;
    licenseStartTime: string;
    licenseEndTime: string;
}

export interface OrgSubmitParams {
    customerType: number;
    submitterName: string;
    submitterPhone: string;
    submitterIdCardNumber: string;
    enterpriseInfo?: EnterpriseInfo;
    governmentInfo?: GovernmentInfo;
    publicInstitutionInfo?: PublicInstitutionInfo;
    otherInfo?: EnterpriseInfo;
}

export interface OrgUpdateParams extends OrgSubmitParams {
    customerId: number;
}

export interface OrgSubmitInfo extends OrgSubmitParams {
    // 开发者信息是否可编辑
    canModifyToday?: boolean;
    customerType: number;
    customerId: number;
    entityStatus: QualifyStatus;
    entityAuditMsg?: string;
    enterpriseInfo?: EnterpriseInfo;
    governmentInfo?: GovernmentInfo;
    otherOrgInfo?: EnterpriseInfo;
}

export interface PublicInstitutionInfo {
    institutionName: string;
    institutionCode: string;
    institutionLicense: string;
    licenseStartTime: string;
    licenseEndTime: string;
    evidenceUrl: string[];
}

export interface EnterpriseInfo {
    enterpriseName: string;
    enterpriseCode: string;
    enterpriseLicense: string;
    orgType: string;
    // enterpriseType: EnterpriseType;
    licenseStartTime: string;
    licenseEndTime: string;
    evidenceUrl?: string[];
}

export interface GovernmentInfo {
    governmentName: string;
    applicationLetter: string;
}

export interface OrgLegalPersonQR {
    base64Qrcode: string;
    timeout: number;
}

export interface OrgAcctPayResponse {
    pcUrl: string;
    wiseUrl: string;
    token: string;
}

export interface OrgAvailableParams {
    companyName: string;
    companyCode: string;
}

export interface OrgAvailableResponse {
    pass: boolean;
    errMsg: string;
    errno: number;
}
