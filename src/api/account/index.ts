// 账号中心 & 实名认证接口
import {createInterface} from '@/api/request';
import {
    AccountInfo,
    DeveloperInfo,
    BjhQualifyInfo,
    OrgAcctPayResponse,
    OrgAvailableParams,
    OrgAvailableResponse,
    OrgLegalPersonQR,
    OrgSubmitInfo,
    OrgSubmitParams,
    OrgUpdateParams,
    PassPersonInfo,
} from './interface';

export default {
    /** 获取账号信息 */
    getAccountInfo: createInterface<void, AccountInfo>('POST', '/user/account/info'),
    /** 获取Pass个人实名信息 */
    getPassPersonInfo: createInterface<void, PassPersonInfo>('GET', '/user/account/realNameInfo'),
    /** 同步百家号认证状态 */
    acceptBjhInfo: createInterface<void, boolean>('POST', '/user/account/acceptBjhInfo'),
    /** 获取开发者相关信息 */
    getDeveloperInfo: createInterface<void, DeveloperInfo>('POST', '/user/account/developer/info'),
    /** 获取banner信息 */
    getBjhQualifyInfo: createInterface<void, BjhQualifyInfo>('GET', '/user/account/bjhInfo'),
    /** 机构类主体提交 */
    submitOrgInfo: createInterface<OrgSubmitParams, number>('POST', '/customer/submitV2'),
    /** 机构类主体更新 */
    updateOrgInfo: createInterface<OrgUpdateParams, number>('POST', '/customer/updateV2'),
    /** 机构类主体信息查询 */
    getOrgInfo: createInterface<void, OrgSubmitInfo>('GET', '/customer/detailV2'),
    /** 对公打款提交 */
    getAccPayLink: createInterface<void, OrgAcctPayResponse>('POST', '/customer/auth/acctPay'),
    /** 法人刷脸提交 */
    getLegalQRCode: createInterface<void, OrgLegalPersonQR>('POST', '/customer/auth/legalPerson'),
    /** 企业机审 */
    checkOrgAvailable: createInterface<OrgAvailableParams, OrgAvailableResponse>('POST', '/audit/commit/company'),
    /** 更新机构类开发者信息 */
    editOrgInfo: createInterface<OrgSubmitParams, number>('POST', '/customer/edit'),
};
