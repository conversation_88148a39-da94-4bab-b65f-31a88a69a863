import {RepeatTypeStatus, RepeatType} from '@/api/agentEdit/interface';
import {PrivacyInfoOption} from '../privcy/interface';

type AppId = string;

/** 查询agentId入参 */
export interface AppIdParams {
    appId: AppId;
}

/** 历史累计对话次数概览数据 */
export interface ConversationPVOverview {
    /** 历史累计对话次数 */
    pv: number;
    /** 历史累计对话次数周环比 */
    pvGrowth: string;
    /** 历史累计对话次数排名（新上线的智能体没有排名） */
    pvRank?: number;
}

/** 历史累计对话用户数概览数据 */
export interface ConversationUVOverview {
    /** 历史累计对话用户数 */
    uv: number;
    /** 历史累计对话用户数周环比 */
    uvGrowth: string;
    /** 历史累计对话用户数排名 */
    uvRank?: number;
}

/** 对话满意度概览数据 */
export interface UserSatisfactionOverview {
    /** 对话满意度  例如：93.34% */
    userSatisfactionRatio: string;
    /** 对话满意度增长or下降率 周环比 */
    userSatisfactionRatioGrowth: string;
    /** 对话满意度排名 */
    userSatisfactionRank?: number;
}

/** 对话满意度概览数据 */
export interface SearchDistributeOverview {
    /** 历史累计分发次数 */
    searchDistributeNum: number;
    /** 历史累计分发次数周环比 */
    searchDistributeNumGrowth: string;
    /** 历史累计分发次数排名 */
    searchDistributeRank?: number;
}

/** Agent数据分析概览数据 */
export interface StatisticsOverviewData
    extends ConversationPVOverview,
        ConversationUVOverview,
        UserSatisfactionOverview,
        SearchDistributeOverview {
    /** 参与排名的智能体总数，用于计算排名。用ta命名避免可能暴露敏感信息的风险 */
    ta: number;
}

/** 一级二级渠道来源数据 */
export interface StatisticsSource {
    /** 一级渠道值 */
    firstSource: string;
    /** 一级渠道值名称 */
    firstSourceName: string;
    /** 二级渠道列表 (没有二级列表输出[]) */
    subSources: Array<{
        /** 二级渠道值 */
        secondSource: string;
        /** 二级渠道值名称 */
        secondSourceName: string;
    }>;
}

/** 一级二级渠道来源列表数据 */
export type StatisticsSourceList = StatisticsSource[];

/** 数据分析 tab 类型枚举值，用于区分不同类型的分析数据 */
export enum AnalysisTabType {
    /**
     * 流量分析
     */
    Traffic = 'Traffic',
    /**
     * 用户分析
     */
    User = 'User',
    /**
     * 对话分析
     */
    Conversation = 'Conversation',
    /**
     * 行为分析
     */
    Action = 'Action',
    /**
     * 商品分析
     */
    Goods = 'Goods',
}

/** 转化为Tab中文的枚举值 */
export const AnalysisTabTypeCn = {
    [AnalysisTabType.Traffic]: '流量分析',
    [AnalysisTabType.User]: '用户分析',
    [AnalysisTabType.Conversation]: '对话分析',
    [AnalysisTabType.Action]: '行为分析',
    [AnalysisTabType.Goods]: '商品分析',
};

/**
 * AnalysisTabType 转化为请求路径的字符串
 */
export const StatisticsType = {
    [AnalysisTabType.Traffic]: 'traffic',
    [AnalysisTabType.User]: 'user',
    [AnalysisTabType.Conversation]: 'conversation',
    [AnalysisTabType.Action]: 'action',
    [AnalysisTabType.Goods]: 'goods',
};

/** 图表数据接口请求参数 */
export interface StatisticsChartParams {
    /** 智能体ID */
    appId: AppId;
    /** 起始时间范围（10位秒级时间戳）--- 含选定日期 */
    startTime: number;
    /** 结束时间范围（10位秒级时间戳）--- 含选定日期 */
    endTime: number;
    /** 一级渠道 不传查询全部渠道 */
    firstSource?: string;
    /** 二级渠道 不传查询全部渠道 */
    secondSource?: string;
}

/** 表格数据接口请求参数 */
export interface StatisticsDetailParams extends StatisticsChartParams {
    /** 分页页码，从1开始 */
    pageNo: number;
    /** 分页大小 */
    pageSize: number;
}

/** 表格数据下载接口请求参数 */
export type StatisticsDetailDownloadParams = StatisticsChartParams;

/** 接口返回数据指标字段key枚举 */
export enum IndicatorKeyEnum {
    // ---对话分析指标key枚举 start---
    /** 对话次数 */
    Rounds = 'rounds',
    /** 对话用户数 */
    RoundsUv = 'roundsUv',
    /** 人均对话数 = 对话次数 / 对话用户数 */
    AvgUserRounds = 'avgUserRounds',
    /** 次均对话数 = 对话次数 / 启动次数 */
    AvgRounds = 'avgRounds',
    /** 对话满意度 = 点赞对话数 /（点赞对话数 + 点踩对话数） */
    SatisfactionRate = 'satisfactionRate',
    // ---对话分析指标key枚举 end---

    // ---流量分析指标key枚举 start---
    /** 曝光次数 */
    DistributePv = 'distributePv',
    /** 曝光用户数 */
    DistributeUv = 'distributeUv',
    /** 点击次数 */
    ClickPv = 'clickPv',
    /** 点击用户数 */
    ClickUv = 'clickUv',
    /** 启动次数 */
    StartPv = 'startPv',
    /** 启动用户数 */
    StartUv = 'startUv',
    /** 人均使用时长（秒） */
    UseTimePerson = 'useTimePerson',
    /** 次均使用时长（秒） */
    UseTimeRound = 'useTimeRound',
    // ---流量分析指标key枚举 end---

    // ---用户分析指标key枚举 start---
    /** 新用户数 */
    NewUv = 'newUv',
    /** 新用户占比 = 新用户数 / 对话用户数 */
    NewUvRatio = 'newUvRatio',
    /** 次日留存率 */
    DayRetainedRatio = 'dayRetainedRatio',
    /** 7日留存率 */
    WeekRetainedRatio = 'weekRetainedRatio',
    /** 30日留存率 */
    MonthRetainedRatio = 'monthRetainedRatio',
    /** 回流率 */
    ReturningRatio = 'returningRatio',
    // ---用户分析指标key枚举 end---

    // ---行为分析指标key枚举 start---
    /** 点赞次数 */
    TopNum = 'topNum',
    /** 点赞用户数 */
    TopUserNum = 'topUserNum',
    /** 点踩次数 */
    TapNum = 'tapNum',
    /** 点踩用户数 */
    TapUserNum = 'tapUserNum',
    /** 点击语音播报次数 */
    ClickSpeechPv = 'clickSpeechPv',
    /** 点击语音播报用户数 */
    ClickSpeechUv = 'clickSpeechUv',
    /** AI通话次数 */
    AiCallPv = 'aiCallPv',
    /** AI通话用户数 */
    AiCallUv = 'aiCallUv',
    // ---行为分析指标key枚举 end---

    // ---商品分析指标key枚举 start---
    /** 订单数 */
    Sales = 'sales',
    /** 订单金额 */
    Amount = 'amount',
    // ---商品分析指标key枚举 end---
}

// /** Agent图表接口返回数据指标字段key枚举 */
export type ExcludeGoodsIndicatorKeyEnum = Exclude<IndicatorKeyEnum, IndicatorKeyEnum.Sales | IndicatorKeyEnum.Amount>;

/** 指标key枚举对应的中文名称 */
export const IndicatorCnNameMap: Record<IndicatorKeyEnum, string> = {
    [IndicatorKeyEnum.Rounds]: '对话次数',
    [IndicatorKeyEnum.RoundsUv]: '对话用户数',
    [IndicatorKeyEnum.AvgUserRounds]: '人均对话数',
    [IndicatorKeyEnum.AvgRounds]: '次均对话数',
    [IndicatorKeyEnum.SatisfactionRate]: '对话满意度',
    [IndicatorKeyEnum.DistributePv]: '曝光次数',
    [IndicatorKeyEnum.DistributeUv]: '曝光用户数',
    [IndicatorKeyEnum.ClickPv]: '点击次数',
    [IndicatorKeyEnum.ClickUv]: '点击用户数',
    [IndicatorKeyEnum.StartPv]: '启动次数',
    [IndicatorKeyEnum.StartUv]: '启动用户数',
    [IndicatorKeyEnum.UseTimePerson]: '人均使用时长(秒)',
    [IndicatorKeyEnum.UseTimeRound]: '次均使用时长(秒)',
    [IndicatorKeyEnum.NewUv]: '新用户数',
    [IndicatorKeyEnum.NewUvRatio]: '新用户占比',
    [IndicatorKeyEnum.DayRetainedRatio]: '次日留存率',
    [IndicatorKeyEnum.WeekRetainedRatio]: '7日留存率',
    [IndicatorKeyEnum.MonthRetainedRatio]: '30日留存率',
    [IndicatorKeyEnum.ReturningRatio]: '回流率',
    [IndicatorKeyEnum.TopNum]: '点赞次数',
    [IndicatorKeyEnum.TopUserNum]: '点赞用户数',
    [IndicatorKeyEnum.TapNum]: '点踩次数',
    [IndicatorKeyEnum.TapUserNum]: '点踩用户数',
    [IndicatorKeyEnum.ClickSpeechPv]: '语音播报次数',
    [IndicatorKeyEnum.ClickSpeechUv]: '语音播报用户数',
    [IndicatorKeyEnum.AiCallPv]: 'AI通话次数',
    [IndicatorKeyEnum.AiCallUv]: 'AI通话用户数',
    [IndicatorKeyEnum.Sales]: '订单数',
    [IndicatorKeyEnum.Amount]: '订单金额(单位: 元)',
};

/** 对话分析指标数据 */
export interface ConversationIndicators {
    /** 对话次数 */
    [IndicatorKeyEnum.Rounds]: number;
    /** 对话用户数 */
    [IndicatorKeyEnum.RoundsUv]: number;
    /** 人均对话数 = 对话次数 / 对话用户数 */
    [IndicatorKeyEnum.AvgUserRounds]: string;
    /** 次均对话数 = 对话次数 / 启动次数 */
    [IndicatorKeyEnum.AvgRounds]: string;
    /** 对话满意度 = 点赞对话数 /（点赞对话数 + 点踩对话数） */
    [IndicatorKeyEnum.SatisfactionRate]: string;
}

/** 流量分析指标数据 */
export interface FlowIndicators {
    /** 分发次数 */
    [IndicatorKeyEnum.DistributePv]: number;
    /** 分发用户数 */
    [IndicatorKeyEnum.DistributeUv]: number;
    /** 点击次数 */
    [IndicatorKeyEnum.ClickPv]: number;
    /** 点击用户数 */
    [IndicatorKeyEnum.ClickUv]: number;
    /** 启动次数 */
    [IndicatorKeyEnum.StartPv]: number;
    /** 启动用户数 */
    [IndicatorKeyEnum.StartUv]: number;
    /** 人均使用时长（秒） */
    [IndicatorKeyEnum.UseTimePerson]: number;
    /** 次均使用时长（秒） */
    [IndicatorKeyEnum.UseTimeRound]: number;
}

/** 用户分析指标数据 */
export interface UserIndicators {
    /** 新用户数 */
    [IndicatorKeyEnum.NewUv]: number;
    /** 新用户占比 = 新用户数 / 对话用户数 */
    [IndicatorKeyEnum.NewUvRatio]: string;
    /** 次日留存率 */
    [IndicatorKeyEnum.DayRetainedRatio]: string;
    /** 7日留存率 */
    [IndicatorKeyEnum.WeekRetainedRatio]: string;
    /** 30日留存率 */
    [IndicatorKeyEnum.MonthRetainedRatio]: string;
    /** 回流率 */
    [IndicatorKeyEnum.ReturningRatio]: string;
}

/** 行为分析指标数据 */
export interface BehaviorIndicators {
    /** 点赞次数 */
    [IndicatorKeyEnum.TopNum]: number;
    /** 点赞用户数 */
    [IndicatorKeyEnum.TopUserNum]: number;
    /** 点踩次数 */
    [IndicatorKeyEnum.TapNum]: number;
    /** 点踩用户数 */
    [IndicatorKeyEnum.TapUserNum]: number;
    /** 点击语音播报次数 */
    [IndicatorKeyEnum.ClickSpeechPv]: number;
    /** 点击语音播报用户数 */
    [IndicatorKeyEnum.ClickSpeechUv]: number;
    /** AI通话次数 */
    [IndicatorKeyEnum.AiCallPv]: number;
    /** AI通话用户数 */
    [IndicatorKeyEnum.AiCallUv]: number;
}

/** 商品分析指标数据 */
export interface GoodsIndicators {
    /** 订单数 */
    [IndicatorKeyEnum.Sales]: number;
    /** 订单金额(单位: 元) */
    [IndicatorKeyEnum.Amount]: number;
}

/** 图表/表格明细数据指标接口返回的所有数据 */
export type AllIndicatorsData = ConversationIndicators &
    FlowIndicators &
    UserIndicators &
    BehaviorIndicators & {
        date: string;
    };

export interface GoodsLineChartData extends GoodsIndicators {
    date: string;
}

/** 明细表格接口返回数据 */
export interface StatisticsDetailData {
    /** 页码 */
    pageNo: number;
    /** 分页大小 */
    pageSize: number;
    /** 总数据条数 */
    total: number;
    /** 数据列表 */
    dataList: AllIndicatorsData[];
}

/**
 * 数据分析接口请求参数
 */
export interface GetAgentAnalysisDataParams {
    /** 智能体ID */
    appId: string;
    startTime: number;
    endTime: number;
    pageNo?: number;
    pageSize?: number;
    source?: string;
}

/**
 * 数据分析渠道列表数据
 */
export interface AgentStatisticsSource {
    label: string;
    source: string;
    children?: AgentStatisticsSource[];
}

// 用于渲染析线图每个指标的数据类型，包含每个指标的数据数组和单位（可能出现 '%'）
export interface AgentLineChartRenderDataItem {
    data: number[];
    // 有的数据可能有单位，如：%
    unit: string;
}

// 用于渲染析线图的数据类型
export type AgentLineChartRenderData = {
    // 用于渲染 y 轴的数据
    [key in IndicatorKeyEnum]?: AgentLineChartRenderDataItem;
} & {
    // 用于渲染 x 轴的日期
    dateList: string[];
};

export interface GetConversationParams {
    appId: string;
    pageNo: number;
    pageSize: number;
}

export enum AgentStatus {
    /** 智能体从未上过线 */
    neverPublish = 0,
    /** 智能体发布过 */
    published = 1,
}

export interface QAItem {
    query: string;
    response: string;
    time: string;
}

export interface ConversationRecord {
    id: number;
    qaList: QAItem[];
}

export interface GetConversationResponse {
    status: AgentStatus;
    /** 是否在隐私设置里开启对话记录 */
    privacyOptions: PrivacyInfoOption[] | null;
    /** 生效日期 */
    effectiveDate: number | null;
    pageInfo: {
        pageNo: number;
        pageSize: number;
        total: number;
        dataList: ConversationRecord[];
    } | null;
}

export interface GetConversationCountParams {
    appId: string;
    startTime: number;
    endTime: number;
}

export interface GetConversationCountResponse {
    /** 时间范围内的总对话条数 */
    count: number;
    /** 上限，白名单用户或智能体是 50000 */
    limit: number;
    /** 导出等待时间，单位：秒， 总时间 = 1秒 * 有效天数 + 5秒 四舍五入取整 */
    during: number;
}

export type PostExportConversationParams = GetConversationCountParams;

export interface GetDuplicateAnalysisParams {
    appId: string;
}

export interface GetDuplicateAnalysisResponse {
    /** 复制分析数据 */
    duplicate: boolean;
}

export interface GetPlagiarizeAnalysisResponse {
    /** 重复类型 */
    repeatType: RepeatType;
}

export const repeatTypeTips: {[key in RepeatTypeStatus]?: string} = {
    [RepeatTypeStatus.logoName]: '头像/名称',
    [RepeatTypeStatus.logoDesc]: '头像/简介',
    [RepeatTypeStatus.logoPrompt]: '头像/人设',
    [RepeatTypeStatus.logoNameDesc]: '头像/名称/简介',
    [RepeatTypeStatus.logoNamePrompt]: '头像/名称/人设',
    [RepeatTypeStatus.logoNameDescPrompt]: '头像/名称/简介/人设',
    [RepeatTypeStatus.nameDesc]: '名称/简介',
    [RepeatTypeStatus.namePrompt]: '名称/人设',
    [RepeatTypeStatus.nameDescPrompt]: '名称/简介/人设',
    [RepeatTypeStatus.descPrompt]: '简介/人设',
};
