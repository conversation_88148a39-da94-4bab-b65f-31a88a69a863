/**
 * @file 智能体数据分析相关接口
 * <AUTHOR>
 * @update <EMAIL> 数据指标接口重构 2025-01
 */
import {createInterface, request} from '@/api/request';
import {
    GetAgentAnalysisDataParams,
    AgentStatisticsSource,
    GetConversationParams,
    GetConversationResponse,
    GetConversationCountParams,
    GetConversationCountResponse,
    PostExportConversationParams,
    GetDuplicateAnalysisParams,
    GetDuplicateAnalysisResponse,
    GetPlagiarizeAnalysisResponse,
    AppIdParams,
    StatisticsOverviewData,
    StatisticsSource,
    AnalysisTabType,
    StatisticsDetailDownloadParams,
    StatisticsType,
    StatisticsChartParams,
    StatisticsDetailParams,
    AllIndicatorsData,
    StatisticsDetailData,
    GoodsLineChartData,
} from './interface';

export default {
    /** 获取 agent 数据概览 */
    getStatisticsOverviewData: createInterface<AppIdParams, StatisticsOverviewData>(
        'GET',
        '/agent/statistics/overview'
    ),

    /** 获取 agent 一二级渠道列表 */
    getStatisticsSource: createInterface<AppIdParams, StatisticsSource[]>('GET', '/agent/statistics/source/subSource'),

    /** 获取图表数据(除了商品分析) */
    getStatisticsChartData: (params: StatisticsChartParams): Promise<AllIndicatorsData[]> =>
        request('GET', '/agent/statistics/all', params, {timeout: 10000}),

    /** 获取表格数据 */
    getStatisticsDetailData: (params: StatisticsDetailParams): Promise<StatisticsDetailData> =>
        request('GET', 'agent/statistics/all/detail', params, {timeout: 10000}),

    /** 获取不同统计类型表格 excel 文件下载地址 */
    downloadStatisticsDetailList: (type: AnalysisTabType, params: StatisticsDetailDownloadParams): Promise<string> =>
        request('GET', `/agent/statistics/detail/download/${StatisticsType[type]}`, params, {timeout: 10000}),

    /** 获取 agent 商品渠道值列表 */
    getAgentGoodsStatisticsSource: (): Promise<AgentStatisticsSource[]> =>
        request('GET', '/agent/statistics/source', {type: 'goods'}),

    /** 获取商品分析图表数据 */
    getGoodsChartData: (params: GetAgentAnalysisDataParams): Promise<GoodsLineChartData[]> =>
        request('GET', '/agent/statistics/simple/goods', params, {timeout: 10000}),

    /** 对话记录查询 */
    getConversation: createInterface<GetConversationParams, GetConversationResponse>(
        'GET',
        '/agent/tuning/conversation/query'
    ),

    /** 对话记录时间范围条数查询 */
    getConversationCount: createInterface<GetConversationCountParams, GetConversationCountResponse>(
        'GET',
        '/agent/tuning/conversation/count'
    ),
    /** 导出对话记录 */
    exportConversation: createInterface<PostExportConversationParams, {url: string}>(
        'POST',
        '/agent/tuning/conversation/export ',
        {
            timeout: 15 * 1000,
        }
    ),

    /** 获取复制分析 */
    getDuplicateAnalysis: createInterface<GetDuplicateAnalysisParams, GetDuplicateAnalysisResponse>(
        'GET',
        '/agent/duplicate/analysis'
    ),

    /** 获取抄袭分析 */
    getPlagiarizeAnalysis: createInterface<GetDuplicateAnalysisParams, GetPlagiarizeAnalysisResponse>(
        'GET',
        '/agent/duplicate/plagiarize/analysis'
    ),
};
