/**
 * @file 商家授权服务商相关接口
 * @doc 接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/HXKXXNhXbMP0wD#anchor-a24bac10-3f70-11ef-ad44-9fa1c52bc9b8
 * <AUTHOR>
 */

import {createInterface} from '../request';
import {
    ClosePopupParams,
    GetBindTpInfoResponse,
    GetTpInfoResponse,
    GetTpUnbindInfoRequest,
    GetUnbindTpInfoResponse,
    PostTpBindRequest,
    PostTpUnbindRequest,
} from './interface';

/** 获取待授权服务商信息 */
export const getToBindTpInfo = createInterface<{data: string}, GetTpInfoResponse>('GET', '/tp/getTpInfo', {
    forbiddenToast: true,
});

/** 用户授权 */
export const bindTp = createInterface<PostTpBindRequest, void>('POST', '/tp/bind', {
    forbiddenToast: true,
    onReject: () => {
        throw new Error();
    },
});

/** 用户取消授权 */
export const unbindTp = createInterface<PostTpUnbindRequest, void>('POST', '/tp/unbind');

/** 获取用户授权的服务商列表信息 */
export const getBindTpInfos = createInterface<void, GetBindTpInfoResponse>('GET', '/tp/getBindTpInfos');

/** 获取是否有被拒绝的解绑申请 */
export const getUnbindTpInfo = createInterface<GetTpUnbindInfoRequest, GetUnbindTpInfoResponse>(
    'POST',
    '/tp/unbindInfo'
);

/** 关闭弹窗 */
export const closeCustomerPopup = createInterface<ClosePopupParams, string>('POST', '/tp/closePopup');
