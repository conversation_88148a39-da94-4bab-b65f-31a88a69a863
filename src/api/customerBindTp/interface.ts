export interface GetAgentUserInfoResponse {
    /** 头像链接 */
    portrait: string;

    /** 账号名 */
    displayName: string;

    /** 主体名 */
    customerName: string;
}

export interface GetTpInfoResponse {
    tpId: string;
    /** 服务商名称 */
    facilitator: string;

    /** 企业名称 */
    customerName: string;

    /** 提交者名称 */
    submitterName: string;

    /** 提交者联系电话 */
    submitterPhone: string;

    /** 提交者联系邮箱 */
    submitterEmail: string;
}

/** 授权绑定类型 */
export enum AuthorizeBindType {
    /** 账号 */
    Account = 1,
    /** 智能体 */
    Agent = 2,
}

/** 授权权限类型 */
export enum AuthorizeScope {
    Develop = 'lingjing_develop',
    Deploy = 'lingjing_deploy',
    Tuning = 'lingjing_tuning',
    Operate = 'lingjing_operate',
    Data = 'lingjing_data',
}

export interface PostTpBindRequest {
    tpId: string;
    bindType: AuthorizeBindType;
    appId: string;
    scopeList: AuthorizeScope[];
}

// 商家侧 1申请解绑，2，同意解绑，3拒绝解绑
export enum UnbindType {
    Applying = 1,
    Agree = 2,
    Disagree = 3,
}

export interface PostTpUnbindRequest {
    tpId: string;
    bindType: AuthorizeBindType;
    appId: string;
    type: UnbindType; // 1申请解绑，2，同意解绑，3拒绝解绑
}

export interface GetTpUnbindInfoRequest {
    tpId: string;
    bindType: AuthorizeBindType;
    appId: string;
}

export type ClosePopupParams = GetTpUnbindInfoRequest;

/** 解绑状态 （商家视角） */
export enum CustomerUnbindStatus {
    /** 未申请解绑 */
    unbindNone = 0,
    /** 解绑申请中（发起解绑，申请解绑） */
    unbindApplying = 1,
    /** 待理处（服务商侧申请解绑） */
    pending = 2,
    /** 拒绝服务商解绑（拒绝服务商发起的解绑） */
    disagree = 3,
    /** 被拒绝（服务商侧拒绝解绑） */
    rejected = 4,
}

export interface BindTpInfoItem {
    /** 服务商和商家绑定关系ID */
    tpBindId: number;

    tpId: string;
    /** 授权绑定类型 */
    bindType: AuthorizeBindType;

    /** 解绑状态 */
    unbindStatus: CustomerUnbindStatus;

    /** 发起解绑申请时间 */
    unbindTime: number;

    /** 拒绝申请时间 */
    rejectTime: number;

    /** 服务商名称 */
    facilitator: string;

    /** 企业名称 */
    customerName: string;

    /** 提交者名称 */
    submitterName: string;

    /** 提交者联系电话 */
    submitterPhone: string;

    /** 提交者联系邮箱 */
    submitterEmail: string;
}

export type GetBindTpInfoResponse = BindTpInfoItem[];

export interface GetUnbindTpInfoResponse {
    /** 是否被服务商拒绝 */
    isReject: boolean;
    /** 拒绝申请时间 */
    rejectTime: number;
    /** 是否展示提示弹窗 */
    isShow: boolean;
}
