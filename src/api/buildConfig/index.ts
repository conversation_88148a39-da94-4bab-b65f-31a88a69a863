/**
 * @file AI 生成配置接口
 * <AUTHOR>
 * @date 2024/08/21
 */

import {AIBuildName} from '@/modules/agentPromptEditV2/interface';
import {AgentPluginFunctionIds, AgentPluginInfo} from '@/api/agentEdit';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {request} from '../request';

export interface AIBuildConfig {
    appId: string;
    systemAssistant: {
        instructions: string;
        thoughtInstructions: string;
        chatInstructions: string;
    };
    description: string;
    recommends: string[];
    buildType: AIBuildName;
    pluginList: AgentPluginInfo[];
}

export interface buildConfigWithAIParam {
    buildType: AIBuildName;
    signal?: AbortSignal;
    recommendCount?: number;
    name?: string;
    overview?: string;
    description?: string;
    recommends?: string[]; // 引导示例
    system: string;
    newPlugins: AgentPluginFunctionIds[]; // buildType为PLUGIN（4）时需要获取配置项传参，其他情况传[];
}

export const buildConfigWithAI = (params: buildConfigWithAIParam): Promise<AIBuildConfig> => {
    const {signal, buildType, recommendCount, name, overview, description, recommends, system, newPlugins} = params;
    return request(
        'POST',
        SECURE_URL_ENUM.AIAutoGen,
        {
            appId: new URLSearchParams(location.search).get('appId'),
            buildType,
            recommendCount,
            name,
            overview,
            description,
            recommends,
            system,
            newPlugins,
        },
        {
            timeout: 60000,
            signal,
        }
    );
};
