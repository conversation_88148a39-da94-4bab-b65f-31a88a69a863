// 查询任务页详情
export interface UserTaskPageInfo {
    /**
     * 任务页id
     */
    id: string;
    /**
     * 总记录数
     */
    totalRecord: number;
    /**
     * 顶部任务包
     */
    topPkgInfos: PkgInfo[];
    /**
     * 右侧任务包
     */
    rightPkgInfos: PkgInfo[];
    /**
     * 右侧图片信息
     */
    rightImgInfo: ImgInfo;
}

// 任务包信息
export interface PkgInfo {
    /**
     * 任务包id
     */
    id: string;
    /**
     * 任务包标题
     */
    title: string;
    /**
     * 任务包副标题
     */
    subTitle: string;
    /**
     * 任务包类型
     */
    taskTimeType: 1 | 2;
    /**
     * 任务包开始时间
     */
    taskStartTime: string;
    /**
     * 任务包结束时间
     */
    taskEndTime: string;
    /**
     * 任务包持续天数
     */
    taskKeepDays: string;
    /**
     * 子任务列表
     */
    subTaskInfos: SubTaskInfo[];
    /**
     * 是否展示任务完成度
     */
    showCompleteFlag: 1 | 0;
    /**
     * 是否展示奖励
     */
    showRewardFlag: 1 | 0;
    /**
     * 是否展示倒计时
     */
    showRemainTimeFlag: 1 | 0;
}

// 子任务包
export interface SubTaskInfo {
    /**
     * 子任务id
     */
    id: string;
    /**
     * 子任务标题
     */
    title: string;
    /**
     * 子任务副标题
     */
    subTitle: string;
    /**
     * 子任务事件码
     */
    eventCode: eventCodeValue;
    /**
     * 子任务奖励数量
     */
    rewardCount: number;
    /**
     * 子任务总数量
     */
    totalNum: number;
    /**
     * 子任务完成数量
     */
    finishNum: number;
    /**
     * 子任务状态
     */
    status: 0 | 1 | 2 | 3 | 4; // 0,未开始 1:进行中，2待抽奖，3.已抽奖，4.已下线
    /**
     * 子任务条件
     */
    condition: Condition;
    /**
     * 是否有奖励
     */
    rewardFlag: 1 | 0;
}

/**
 * 子任务编码
 */
export enum eventCodeValue {
    /**
     * 复制智能体且审核通过
     */
    COPY_AGENT_AND_AUDIT_SUCCESS = 'COPY_AGENT_AND_AUDIT_SUCCESS',
    /**
     * 新增创建且审核通过智能体
     */
    CREATE_AGENT_AND_AUDIT_SUCCESS = 'CREATE_AGENT_AND_AUDIT_SUCCESS',
    /**
     * 挂载插件并发布
     */
    MOUNT_PLUGIN_AND_PUBLISH = 'MOUNT_PLUGIN_AND_PUBLISH',
    /**
     * 挂载知识库并发布
     */
    MOUNT_DATASET_AND_PUBLISH = 'MOUNT_DATASET_AND_PUBLISH',
    /**
     * 调优智能体并发布
     */
    TUNING_AGENT_AND_PUBLISH = 'TUNING_AGENT_AND_PUBLISH',
    /**
     * 提交搜索问答并发布
     */
    TUNING_Q2C_AND_PUBLISH = 'TUNING_Q2C_AND_PUBLISH',
    /**
     * 使用数字人分身
     */
    USE_DYNAMIC_FIGURE_PUBLISH = 'USE_DYNAMIC_FIGURE_PUBLISH',
    /**
     * 挂载线索转化功能并发布
     */
    MOUNT_XIANSUO_AND_PUBLISH = 'MOUNT_XIANSUO_AND_PUBLISH',
    /**
     * 使用商品挂载功能并发布
     */
    MOUNT_PRODUCT_AND_PUBLISH = 'MOUNT_PRODUCT_AND_PUBLISH',
    /**
     * 使用链接挂载功能并发布
     */
    MOUNT_LINK_AND_PUBLISH = 'MOUNT_LINK_AND_PUBLISH',
    /**
     * 报名指定活动并成功提交
     */
    SUBMIT_ACTIVITY_SUCCESS = 'SUBMIT_ACTIVITY_SUCCESS',
    /**
     * 新增创建智能体数量
     */
    CREATE_AGENT_COUNT = 'CREATE_AGENT_COUNT',
    /**
     * 复制智能体且审核通过数量
     */
    COPY_AGENT_AND_AUDIT_SUCCESS_COUNT = 'COPY_AGENT_AND_AUDIT_SUCCESS_COUNT',
    /**
     * 新增创建且审核通过智能体数量
     */
    CREATE_AGENT_AND_AUDIT_SUCCESS_COUNT = 'CREATE_AGENT_AND_AUDIT_SUCCESS_COUNT',
}

export interface Condition {
    /**
     * id条件
     */
    appIdCondition: {
        value?: string;
    };
    /**
     * 链接条件
     */
    urlCondition: {
        value?: string;
    };
    /**
     * 数量条件
     */
    countCondition: {
        value?: number;
    };
}

// 图片信息
export interface ImgInfo {
    title: string;
    subTitle: string;
    imgInfos: ImgInfoItem[];
}

// 图片信息项
export interface ImgInfoItem {
    imgUrl: string;
    title: string;
    link: string;
}

// 任务/奖品id信息
export interface TaskIdInfo {
    /**
     * 任务页id
     */
    taskPageId: string;
    /**
     * 任务包id
     */
    taskPkgId: string;
    /**
     * 子任务id
     */
    subTaskId?: string;
    /**
     * 任务id
     */
    taskId?: string;
    /**
     * 记录id
     */
    recordId?: string;
}

export interface TaskList {
    /**
     * 总数量
     */
    total: number;
    /**
     * 页码
     */
    pageNo: number;
    /**
     * 每页数量
     */
    pageSize: number;
    /**
     * 任务列表
     */
    taskList: TaskListItem[];
}

export interface TaskListItem extends TaskIdInfo {
    /**
     * 任务id
     */
    id: string;
    /**
     * 任务名称
     */
    taskName: string;
    /**
     * 子任务状态
     */
    subStatus: SubTaskStatus;
    /**
     * 任务进度
     */
    status: TaskStatus;
    /**
     * 任务完成时间
     */
    finishTime?: string;
    /**
     * 是否展示奖励
     */
    showRewardFlag: 1 | 0;
    /**
     * 是否有奖励
     */
    rewardFlag: 1 | 0;
}

// 子任务状态
export enum SubTaskStatus {
    /*
     * 未开始
     */
    NOT_BEGIN = 0,
    /**
     * 进行中
     */
    IN_PROGRESS = 1,
    /**
     * 待抽奖
     */
    WAIT_DRAW = 2,
    /**
     * 已抽奖
     */
    DRAWN = 3,
    /**
     * 已终止
     */
    STOP = 4,
    /**
     * 未中奖
     */
    NO_WIN = 5,
}

// 任务进度
export enum TaskStatus {
    /**
     * 进行中
     */
    IN_PROGRESS = 1,
    /**
     * 已结束
     */
    FINISHED = 2,
}

export interface PrizesInfo {
    /**
     * 奖品列表
     */
    prizes: PrizesItem[];
}

export interface PrizesItem {
    /**
     * 奖品id
     */
    prizeid?: string;
    /**
     * 奖品过期时间
     */
    expires?: string;
    /**
     * 奖品描述
     */
    desc?: string;
    /**
     * 奖品使用url
     */
    useurl?: string;
    /**
     * 奖品订单号
     */
    orderNo?: string;
    /**
     * 奖品子分类
     */
    subCategory?: number;
    /**
     * 奖品分类
     */
    category?: number;
    /**
     * 奖品价格
     */
    price?: string;
    /**
     * 奖品小图标
     */
    smallicon?: string;
    /**
     * 奖品大图标
     */
    icon?: string;
    /**
     * 奖品名称
     */
    name?: string;
}

export interface DrawPrize {
    /**
     * 是否中奖
     */
    isWin: boolean;
    /**
     * 中奖信息
     */
    winInfo: {
        /**
         * 错误信息
         */
        errMsg: string;
        /**
         * 错误码
         */
        errNo: string;
    };
    /**
     * 奖品信息
     */
    prize: PrizeInfo;
}

export interface PrizeInfo {
    /**
     * 奖品资产号
     */
    assetNo: string;
    /**
     * 奖品值
     */
    prizeValue: string;
    /**
     * 奖品描述
     */
    desc: string;
    /**
     * 奖品价格
     */
    price: string;
    /**
     * 奖品小图标
     */
    smallicon: string;
    /**
     * 奖品大图标
     */
    icon: string;
    /**
     * 奖品id
     */
    prizeid: string;
    /**
     * 奖品子分类
     */
    subCategory: number;
    /**
     * 奖品分类
     */
    category: number;
    /**
     * 奖品名称
     */
    name: string;
    /**
     * 奖品展示名称
     */
    showName: string;
}

// 登记奖励信息
export interface TaskAward {
    /**
     * 记录id
     */
    recordId: string;
    /**
     * 姓名
     */
    name: string;
    /**
     * 手机号
     */
    phone: string;
    /**
     * 地址
     */
    address: string;
}

// 物流信息
export interface LogisticsInfo {
    /**
     * 审核状态
     */
    auditStatus: AuditStatus;
    /**
     * 快递单号/券码
     */
    prizeAccount: string;
    /**
     * 领取状态
     */
    receiveStatus: ReceiveStatus; // 0 待领取 1 已领取 2 已邮寄
    /**
     * 奖品资产号
     */
    assetNo: string;
    /**
     * 邮寄信息
     */
    postInfo: {
        /**
         * 姓名
         */
        name: string;
        /**
         * 手机号
         */
        phone: string;
        /**
         * 地址
         */
        address: string;
    };
    /**
     * 奖品信息
     */
    prize: {
        /**
         * 奖品id
         */
        id: string;
        /**
         * 奖品名称
         */
        name: string;
        /**
         * 奖品图标
         */
        icon: string;
        /**
         * 奖品大类, 为2时为实物奖品
         */
        type: PrizeType;
        /**
         * 奖品小类
         */
        sType: PrizeSType;
        /**
         * 跳转链接
         */
        url: string;
        /**
         * 虚拟奖品、券码信息
         */
        assetValue: string;
        /**
         * 奖品说明
         */
        prizeTxtIntro: string;
        /**
         * 获取时间
         */
        receiveTime: string;
    };
}

export enum ReceiveStatus {
    /**
     * 无需发放
     */
    NO_NEED = -1,
    /**
     * 待领取
     */
    WAIT_RECEIVE = 0,
    /**
     * 已领取
     */
    RECEIVED = 1,
    /**
     * 已邮寄
     */
    POSTED = 2,
}

export enum PrizeType {
    /**
     * 红包
     */
    HONGBAO = 1,
    /**
     * 实物奖品
     */
    PHYSICAL = 2,
    /**
     * 卡券（虚拟奖品）
     */
    VIRTUAL = 3,
    /**
     * 其他
     */
    OTHERS = 8,
}

export enum PrizeSType {
    /* 跳转至第三方券 */
    THIRD_PARTY_COUPON = 1,
    /* 通用兑换码 */
    COMMON_CODE = 2,
}

export enum AuditStatus {
    /**
     * 待审核
     */
    WAIT_AUDIT = 0,
    /**
     * 审核通过
     */
    AUDIT_PASS = 1,
    /**
     * 审核拒绝
     */
    AUDIT_REJECT = 2,
}
