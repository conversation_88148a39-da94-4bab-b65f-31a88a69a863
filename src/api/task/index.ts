import {createInterface} from '@/api/request';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {DrawPrize, LogisticsInfo, PrizesInfo, TaskAward, TaskIdInfo, TaskList, UserTaskPageInfo} from './interface';

export const GET_TASK_LIST_API = '/experhub/task/getUserTaskPageInfo';

const api = {
    // 获取用户的任务信息
    getUserTaskPageInfo: createInterface<void, UserTaskPageInfo>('GET', GET_TASK_LIST_API),

    // 开始任务
    taskStart: createInterface<TaskIdInfo, void>('POST', '/experhub/task/start'),

    // 获取任务列表
    getTaskList: createInterface<{pageSize: number; pageNo: number}, TaskList>('GET', '/experhub/task/userTaskList'),

    // 获取抽奖页面信息
    drawPageInfo: createInterface<TaskIdInfo, PrizesInfo>('GET', '/experhub/task/drawPageInfo'),

    // 抽奖
    drawPrize: createInterface<TaskIdInfo, DrawPrize>('POST', SECURE_URL_ENUM.ExperHubTaskDraw, {
        forbiddenToast: true,
    }),

    // 登记奖励信息
    sendTaskAward: createInterface<TaskAward, TaskAward>('POST', SECURE_URL_ENUM.ExperHubTaskReward),

    // 获取物流信息
    getLogisTics: createInterface<TaskIdInfo, LogisticsInfo>('GET', SECURE_URL_ENUM.ExperHubTaskRewardDetail),
};

export const getUserTaskPageInfo = () => api.getUserTaskPageInfo();

export default api;
