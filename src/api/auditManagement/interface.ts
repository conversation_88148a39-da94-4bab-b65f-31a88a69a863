import {AppId} from '@/api/appList/interface';
import {TuningId} from '@/api/q2c/interface';

// 主体类型
export enum CustomerType {
    /**
     * 个体
     */
    personal = 1,
    /**
     * 企业
     */
    enterprise = 2,
    /**
     * 政府
     */
    government = 3,
    /**
     * 事业单位
     */
    publicInstitution = 4,
}

export enum DevelopExperience {
    noUnderstanding = 1,
    basicUnderstanding = 2,
    intermediateUnderstanding = 3,
    advancedUnderstanding = 4,
}

export enum EnterpriseType {
    // 企业
    enterprise = 1,
    // 个体工商户
    individual = 2,
}

/** 一言权限审核状态 */
export enum ErnieStatus {
    waiting = 0,
    success = 1,
}

/** 主体资质审核状态 */
export enum EntityStatus {
    /**
     * 待审核
     */
    waiting = 0,
    /**
     * 审核中
     */
    checking = 1,
    /**
     * 审核通过
     */
    success = 2,
    /**
     * 审核不通过
     */
    fail = 3,
}

/** 个人中心主体资质展示未认证状态 */
export enum NoAuthEntityStatus {
    /**
     * 未认证
     */
    noAuth = -1,
}

export interface PersonalInfo {
    /** 运营者姓名 */
    operatorName: string;

    /** 运营者身份证号码 */
    idCardNumber: string;

    /** 身份证正面照片（人像页） */
    idCardFrontUrl: string;

    /** 身份证反面照片（国徽页） */
    idCardBackUrl: string;

    /** 身份证有效期起始时间 */
    idCardStartTime: string;

    /** 身份证有效期结束时间 */
    idCardEndTime: string;
}

export interface EnterpriseInfo {
    /** 企业名称 */
    enterpriseName: string;

    /** 企业类型 */
    enterpriseType: EnterpriseType;

    /** 企业营业执照（图片资源链接） */
    enterpriseLicense: string;

    /** 统一社会信用代码/营业执照注册号 */
    enterpriseCode: string;

    /** 营业执照起始时间 */
    licenseStartTime: string;

    /** 营业执照结束时间 */
    licenseEndTime: string;

    /** 入驻申请函（图片资源链接） */
    applicationLetter: string;
}

export interface GovernmentInfo {
    /** 政府名称 */
    governmentName: string;

    /** 入驻申请函（图片资源链接） */
    applicationLetter: string;
}

/** 事业单位详细信息 */
export interface PublicInstitutionInfo {
    /** 事业单位名称 */
    institutionName: string;
    /** 事业单位码 */
    institutionCode: string;
    /** 法人证书 */
    institutionLicense: string;
    /** 法人证书有效期-开始时间 */
    licenseStartTime: string;
    /** 法人证书有效期-结束时间 */
    licenseEndTime: string;
}

export enum DefaultImageType {
    applicationLetter = 'applicationLetter',
}

export type CustomerId = string;

export interface EntityFormParams {
    /** 申请人姓名 */
    submitterName: string;

    /** 申请人联系电话 */
    submitterPhone: string;

    /** 申请人联系邮箱 */
    submitterEmail: string;

    /** 主体类型 */
    customerType: CustomerType;

    /** 【个人主体】所需的信息 */
    personalInfo: PersonalInfo;

    /** 【企业主体】所需的信息 */
    enterpriseInfo: EnterpriseInfo;

    /** 【政府主体】所需的信息 */
    governmentInfo: GovernmentInfo;

    /** 【事业单位主体】所需的信息 */
    publicInstitutionInfo: PublicInstitutionInfo;
}

export interface ApplyEntityRes {
    customerId: CustomerId;
}

export interface UpdateEntityFormParams extends EntityFormParams {
    customerId: CustomerId;
    entityStatus?: EntityStatus;
}

export interface EntityInfoType {
    customerId: CustomerId;
    status: EntityStatus;
}

export interface EntityCheckResponse {
    status: EntityStatus;
    msg: string;
    ernieStatus: ErnieStatus;
}

// 通过 server 的行业列表接口获取的元素信息
export interface IndustryItem {
    // 行业的 id，保证前端-server-审核侧 的信息一致
    id: number;
    // 父行业的 id
    parentId: number;
    // 行业名称
    name: string;
    // level 为 1 则为一级行业
    level: number;
    // 行业类型
    type: string;
}

// 文件类型，server 根据此字段判断文件大小等策略
export enum FileType {
    idCard = 'idCard',
    enterpriseLicense = 'enterpriseLicense',
    applicationLetter = 'applicationLetter',
    institutionLicense = 'institutionLicense',
    // 实名认证补充材料
    evidence = 'evidence',
    // q2c需求新增类型
    qaImage = 'qaImage',
    qaVideo = 'qaVideo',
    // q2c百家号视频
    q2cVideo = 'q2c_video',
    // 名师招募活动 - 资质信息中压缩包资料
    operActivityZipInfo = 'operation_activity_zip_info',
}

export interface UploadFileParams {
    type: FileType;
    file: File;
    // q2c需求新增参数
    appId?: AppId;
    tuningId?: TuningId;
}

export interface UploadFileRes {
    url: string;
}

// 图像识别支持的功能：身份证正面 ｜ 身份证反面 ｜ 企业营业执照
export enum ImageType {
    idCardFront = 'idCardFront',
    idCardBack = 'idCardBack',
    enterpriseLicense = 'enterpriseLicense',
    institutionLicense = 'institutionLicense',
}

export interface ImageRecognizeParams {
    type: ImageType;
    url: string;
}

interface IdCardFrontInfo {
    name: string;
    id: string;
}

interface IdCardBackInfo {
    startDate: string;
    endDate: string;
}

interface LicenseInfo {
    enterpriseName: string;
    enterpriseCode: string;
    startDate: string;
    endDate: string;
    orgType: string;
}

type OCRInfo = IdCardFrontInfo & IdCardBackInfo & LicenseInfo & PublicInstitutionInfo;
export interface ImageInfo {
    type: ImageType;
    ocrInfo: OCRInfo;
}
