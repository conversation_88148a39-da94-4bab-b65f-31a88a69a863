import {createInterface, CustomOptions, request} from '@/api/request';
import {
    ApplyEntityRes,
    CustomerId,
    EntityCheckResponse,
    EntityFormParams,
    ImageInfo,
    ImageRecognizeParams,
    IndustryItem,
    UpdateEntityFormParams,
    UploadFileParams,
    UploadFileRes,
} from './interface';

export default {
    uploadFileToBos: (
        data: UploadFileParams,
        signal?: AbortSignal,
        options?: CustomOptions
    ): Promise<UploadFileRes> => {
        return request(
            'POST',
            '/tools/uploadFileToBos',
            {
                ...data,
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                timeout: 60 * 1000,
                signal,
                ...(options || {}),
            }
        );
    },
    applyEntityCertification: createInterface<EntityFormParams, ApplyEntityRes>('POST', '/customer/submit'),
    updateEntityForm: createInterface<UpdateEntityFormParams, void>('POST', '/customer/update'),
    getEntityFormDetail: createInterface<{customerId: CustomerId}, UpdateEntityFormParams>('GET', '/customer/detail'),
    getEntityCheckStatus: createInterface<{customerId: CustomerId}, EntityCheckResponse>('GET', '/customer/status'),
    getIndustryList: async (): Promise<IndustryItem[]> => {
        return request('GET', '/industry/getList', {}, {forbiddenToast: true}).then(data => {
            // 加一个兼容，否则该接口会导致页面白屏
            if (!data) return [];
            return data;
        });
    },
    recognizeImage: createInterface<ImageRecognizeParams, ImageInfo>('GET', '/tools/imageRecognition'),
};
