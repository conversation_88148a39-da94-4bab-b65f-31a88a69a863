/**
 * @file 知识库召回测试接口类型定义
 * <AUTHOR>
 */

import {DataSetId, FileId, FileType} from '../dataSet/interface';

/**
 * 知识库召回测试报告总结
 */
export interface DatasetRecallOverview {
    // 报告是否已生成
    reportReady: boolean;
    // 再查x次对话产出报告
    chatLeft: number;
    // 更新时间
    updateTime: number;
    // 知识库调用率
    datasetCallRatio?: number;
    // 知识库覆盖率
    datasetCoverage?: number;
    // 召回段落数
    textHits: number;
}

/**
 * 知识库召回测试报告详情
 */
export interface DatasetRecallReportInfo {
    retrievalAnalysis: RetrievalAnalysis;
    scoreAnalysis: ScoreAnalysis;
    queryAnalysis: QueryAnalysisItem[];
}

/**
 * 检索数据分析
 */
export interface RetrievalAnalysis {
    // 知识库调用率
    datasetCallRatio?: number;
    // 知识库覆盖率
    datasetCoverage?: number;
    // 召回段落数
    textHits: number;
}

/**
 * 相关度分析
 */
export interface ScoreAnalysis {
    // 文本相关度
    agentTextScore: number;
    // 图片相关度
    agentImageScore: number;
    // 视频相关度
    agentVideoScore: number;
    // 音频相关度
    agentAudioScore: number;
    // 是否挂载文本
    containText: boolean;
    // 是否挂载图片
    containImage: boolean;
    // 是否挂载视频
    containVideo: boolean;
    // 是否挂载音频
    containAudio: boolean;
}

/**
 * 高频query分析
 */
export interface QueryAnalysisItem {
    // 检索词
    query: string;
    // 检索次数
    pv: number;
    // 知识库是否覆盖
    cover: boolean;
    // 段落分析
    textAnalysis: TextAnalysisItem[];
}

/**
 * 段落分析
 */
export interface TextAnalysisItem {
    datasetId: DataSetId;
    datasetName: string;
    fileId: FileId;
    fileName: string;
    textId: string;
    // 文件类型
    fileType: FileType;
    // 段落内容
    text: string;
    // 链接（网页/图片/音频/视频）
    url?: string;
    // 段落序号
    number: number;
    // 相关度
    score: number;
    // 命中次数
    hitCount: number;
}

/**
 * 召回报告问题反馈请求参数
 */
export interface DatasetRecallFeedbackParams {
    // 数据来源：知识库召回报告 1
    source: number;
    // 智能体id
    agentId: string;
    // 问题描述
    description: string;
    // 反馈图片列表
    pictures?: string[];
}

/**
 * 数据明细下载请求参数
 */
export interface DatasetDetailDownloadParams {
    // 智能体ID
    appId: string;
}
