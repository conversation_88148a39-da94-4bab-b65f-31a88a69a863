/**
 * @file 知识库召回测试接口
 * <AUTHOR>
 */

import {request} from '../request';
import {
    DatasetRecallReportInfo,
    DatasetRecallOverview,
    DatasetRecallFeedbackParams,
    DatasetDetailDownloadParams,
} from './interface';

export default {
    /** 获取知识库召回测试报告总结 */
    getDatasetRecallOverview: ({appId}: {appId: string}): Promise<DatasetRecallOverview> => {
        return request('GET', '/agent/statistics/dataset/overview', {appId});
    },
    /** 获取知识库召回测试报告详情 */
    getDatasetRecallDetail: ({appId}: {appId: string}): Promise<DatasetRecallReportInfo> => {
        return request('GET', '/agent/statistics/dataset/detail', {appId});
    },
    /** 召回报告问题反馈 */
    datasetRecallFeedback: (params: DatasetRecallFeedbackParams): Promise<null> => {
        return request('POST', '/agent/statistics/dataset/feedback', params);
    },
    /** 数据明细下载 */
    downloadDatasetDetail: (params: DatasetDetailDownloadParams): Promise<string> => {
        return request('GET', '/agent/statistics/dataset/query/download', params);
    },
};
