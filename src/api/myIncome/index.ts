/**
 * @file 我的收益模块主要接口
 * <AUTHOR>
 */

import {request} from '@/api/request';
import {serverToClientLabel} from '../agentList/interface';
import {FeatureAccess, FeatureName} from '../agentEditV2/interface';
import {
    MyIncomeSummaryData,
    GetIncomeDetailParams,
    GetIncomeDetailRes,
    IncomeDetail,
    PayInfo,
    GetPayRecordListRes,
    GetPayRecordListParams,
    PayAccountFormInfo,
    GetBankListRes,
    DropdownMenuData,
    ProvinceCityData,
    ProvinceCityCascade,
    IncomeType,
    ApiIncomeType,
    SummaryProfitTrend,
    DaysPeriodQuery,
    IncomeCashOutSummaryData,
    CommonPayInfo,
} from './interface';

export default {
    /**
     * 获取我的收益概览数据
     * typeList 商业组件类型 1 联盟 2 打赏 3 RFQ 4 广告，可填多个，按逗号分隔
     */
    getMyIncomeSummaryData: (): Promise<MyIncomeSummaryData> =>
        request('GET', '/agent/profit/summary', {typeList: [1, 2, 3, 4].join(',')}),

    /** 收益提现概览数据（目前赞赏激励暂不支持提现）
     * typeList 商业组件类型 1 联盟 2 打赏 3 RFQ 4 广告，可填多个，按逗号分隔
     */
    getIncomeCashOutSummaryData: (isDistribution: boolean = false): Promise<IncomeCashOutSummaryData> =>
        request('GET', '/agent/profit/summary', {typeList: (isDistribution ? [1] : [3, 4]).join(',')}),
    /** 按分类获取收益详情 */
    getClassifyIncomeDetail: (incomeType: IncomeType, params: GetIncomeDetailParams): Promise<GetIncomeDetailRes> =>
        request('GET', `/agent/profit/detail/${ApiIncomeType[incomeType]}`, params).then(res => {
            return {
                ...res,
                dataList: res.dataList.map((item: Omit<IncomeDetail, 'displayStatus'>) => ({
                    ...item,
                    displayStatus: serverToClientLabel(item.status),
                })),
            };
        }),
    /** 获取日纬度累计收益趋势图数据 */
    getSummaryProfitTrendData: (
        daysPeriodQuery: DaysPeriodQuery,
        incomeType?: IncomeType
    ): Promise<SummaryProfitTrend> =>
        request(
            'GET',
            `/agent/profit/summary/trend${incomeType ? '/' + ApiIncomeType[incomeType] : ''}`,
            daysPeriodQuery
        ),
    /** 获取联盟分成用户提现账户信息 */
    getPayInfo: (): Promise<PayInfo> => request('GET', '/user/account/query'),
    /** 获取非联盟分成用户提现账户信息 */
    getCommonPayInfo: (): Promise<CommonPayInfo> => request('GET', '/user/commerce/account/query'),
    /** 获取用户提现记录（目前赞赏激励暂不支持提现）
     * typeList 商业组件类型 1 联盟 2 打赏 3 RFQ 4 广告，可填多个，按逗号分隔
     */
    getPayRecords: (params: GetPayRecordListParams, isDistribution: boolean = false): Promise<GetPayRecordListRes> =>
        request('GET', '/user/account/payments', {...params, typeList: (isDistribution ? [1] : [3, 4]).join(',')}),
    /** 获取银行列表 */
    getBankList: (): Promise<DropdownMenuData[]> =>
        request('GET', '/config/get', {key: 'bank_list'}).then((res: GetBankListRes) =>
            res.list.map(item => ({label: item, value: item}))
        ),
    /** 获取省市列表 */
    getProvinceCityList: (): Promise<ProvinceCityCascade[]> =>
        request('GET', '/config/get', {key: 'province_cities'}).then((res: ProvinceCityData[]) =>
            res.map(item => {
                return {
                    label: item.name,
                    value: item.name,
                    children: item.cities.map(city => ({label: city, value: city})),
                };
            })
        ),
    /** 发送验证码 */
    sendPhoneVerifyCode: (phoneNumber: string) => request('POST', '/common/code/send', {tel: phoneNumber}),
    /** 注册/编辑联盟分成提现账户信息 */
    updatePayInfo: (params: PayAccountFormInfo) => request('POST', '/user/account/save', params, {timeout: 10000}),
    /** 注册/编辑非联盟分成提现账户信息 */
    updateCommonPayInfo: (params: PayAccountFormInfo) =>
        request('POST', '/user/commerce/account/save', params, {timeout: 10000}),
    /** 获取下载提现记录地址 （目前赞赏激励暂不支持提现）
     * typeList 商业组件类型 1 联盟 2 打赏 3 RFQ 4 广告，可填多个，按逗号分隔 如：/download?typeList=3,4
     */
    getPayRecordsDownloadUrl: (isDistribution: boolean = false): Promise<string> => {
        const typeList = (isDistribution ? [1] : [3, 4]).join(',');
        return request('POST', '/user/account/payments/download', null, {params: {typeList}});
    },
    /** 是否展示广告分成收益 */
    getFCPromoProfitAccess: (): Promise<FeatureAccess> =>
        request('GET', '/feature/access/multi', {featureName: FeatureName.FCPromo}, {forbiddenToast: true}),
    /** 是否展示广告分成收益 */
    getLeadsProfitAccess: (): Promise<FeatureAccess> =>
        request('GET', '/feature/access/multi', {featureName: FeatureName.ClueSaleSetting}, {forbiddenToast: true}),
};
