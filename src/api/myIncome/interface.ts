/**
 * @file 我的收益模块接口数据状态
 * <AUTHOR>
 */

import {AgentStatusLabel, AllAuditStatus, ScopeStatus} from '../agentList/interface';

/**
 * 我的收益概览接口返回数据状态
 * 变化趋势无数据或异常数据，返回null
 * 金额或者百分比都是string类型，无数据或异常数据，返回'--'，TODO: 待确认
 */
export interface MyIncomeSummaryData {
    /** 累计收益 */
    totalProfit: string;
    /** 累计收益金额日环比变化趋势。1为增长，0为不变，-1为下降 */
    dayTotalTrend: GrowthTrend | null;
    /** 累计收益金额日环比 */
    dayTotalGrowth: string;
    /** 累计收益金额周环比变化趋势。1为增长，0为不变，-1为下降 */
    weekTotalTrend: GrowthTrend | null;
    /** 累计收益金额周环比 */
    weekTotalGrowth: string;
    /** 累计收益金额月环比变化趋势。1为增长，0为不变，-1为下降 */
    monthTotalTrend: GrowthTrend | null;
    /** 当前累计收益金额月环比 */
    monthTotalGrowth: string;
    /** 累计可提现金额 */
    freeBalance: string;
    /** 累计已提现金额 */
    withdrawnBalance: string;
    /** 累计提现中金额 */
    withdrawingBalance: string;
    /** 联盟分成总收益 */
    distributionProfitSummary: string;
    /** 赞赏激励总收益 */
    rewardProfitSummary: string;
    /** 线索RFQ售卖总收益 */
    rfqProfitSummary: string;
    /** 广告分成总收益 */
    fcPromoProfitSummary: string;
}

/**
 * 提现概览数据
 * 累计可提现金额、累计已提现金额、累计提现中金额、联盟分成总收益
 */
export type IncomeCashOutSummaryData = Required<
    Pick<MyIncomeSummaryData, 'freeBalance' | 'withdrawnBalance' | 'withdrawingBalance' | 'distributionProfitSummary'>
>;

/**
 * 环比趋势枚举
 * 1为增长，0为不变，-1为下降
 */
export enum GrowthTrend {
    /** 增长 */
    Up = 1,
    /** 不变 */
    Same = 0,
    /** 下降 */
    Down = -1,
}

/** 起止日期 */
export interface DaysPeriodQuery {
    /** 起始时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的0点 */
    startTime: number;
    /** 终止时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的24点 */
    endTime: number;
}

/** 每日收益 */
interface DayProfit {
    /** 日期 格式：2024/10/01 */
    date: string;
    /** 收益金额 格式："23.5" */
    profit: string;
}

/** 累计收益趋势 */
export interface SummaryProfitTrend {
    // 时间从小到大排序，当日没有的返回0
    /** 每日收益数据 */
    everyDayProfits: DayProfit[];
}

/** 收益类型，用于前端动态路由 */
export enum IncomeType {
    /** 联盟分成 */
    Distribution = 'distribution',
    /** 赞赏激励 */
    Reward = 'reward',
    /** 线索收集 */
    Leads = 'leads',
    /** 广告分成 */
    FCPromo = 'fcPromo',
}

/** 收益类型，用于接口路由type */
export const ApiIncomeType: Record<IncomeType, string> = {
    [IncomeType.Distribution]: 'distribution',
    [IncomeType.Reward]: 'reward',
    [IncomeType.Leads]: 'rfq',
    [IncomeType.FCPromo]: 'fcPromo',
};

/** 查询收益明细请求参数 */
export interface GetIncomeDetailParams {
    /** 起始时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的0点 */
    startTime: number;
    /** 终止时间范围（10位秒级时间戳）eg: 1701360000 后端处理为选定日期的24点 */
    endTime: number;
    /** 分页页码 */
    pageNo: number;
    /** 分页个数 */
    pageSize: number;
}

/** 查询收益明细返回数据 */
export interface GetIncomeDetailRes {
    pageNo: number;
    pageSize: number;
    total: number;
    /** 开发者名下是否有已上线的智能体 */
    hasOnlineAgent: boolean;
    /** 收益明细列表 */
    dataList: IncomeDetail[];
}

/** 查询收益明细返回数据状态 */
export interface IncomeDetail {
    /** 智能体名称 */
    appName: string;
    /** 智能体 ID */
    appId: string;
    /** 是否开启联盟分成 */
    distributionIsEnabled?: boolean;
    /** 是否开启赞赏 */
    rewardIsEnabled?: boolean;
    /** 是否配置了线索全部/部分售卖 TODO:待确认取值逻辑 */
    rfqIsEnabled?: boolean;
    /** 是否开启广告分成 */
    fcPromoEnabled?: boolean;
    /** 智能体访问权限，1-仅自己可访问 2-仅链接访问 3-公开访问 */
    permission: ScopeStatus;
    /** 智能体状态 */
    status: AllAuditStatus;
    /** 界面显示智能体状态 */
    displayStatus: AgentStatusLabel;
    /** 是否逻辑删除 未删除-0 删除-毫秒级时间戳 */
    isDelete: number;
    /** 智能体在时间范围内产生的收益 */
    profit: string;
}

/** 用户类型枚举
 * 0-个人，1-企业（企业/政府/事业单位）
 * 优先以CEP数据为准，cep无数据取平台的主体信息。企业用户本次需求不支持注册/更新财务数据，只支持查询CEP财务数据
 */
export enum UserType {
    /** 个人 */
    Person = 0,
    /** 企业 */
    Enterprise = 1,
}

/** 联盟分成-提现信息 */
export interface PayInfo {
    /** 提现账户类型 */
    userType: UserType;
    /** 是否开通百家号 */
    bjhOpen: boolean;
    /** 是否需要确认财务信息，未在平台设置过财务信息需要填写/确认财务信息 */
    needConfirmFinance: boolean;
    /** 财务信息 */
    userAccount: PayAccountInfo | null;
}

/** 联盟分成/非联盟分成-通用提现账户信息 */
export interface PayAccountInfo {
    /** 银行名称 */
    bankName: string;
    /** 银行卡号 只展示后4位 示例：******2790 */
    bankCardId: string;
    /** 银行卡开户所在省 */
    bankBranchProvince: string;
    /** 银行卡开户所在市 */
    bankBranchCity: string;
    /** 银行卡开户所在支行名称 */
    branchBranchName: string;
    /** 银行卡开户预留手机号 中间4位用* 示例：150****3223 */
    bankRegisteredTel: string;
    /** 是否开启自动付款，默认开启 */
    enableAutoPay: boolean;
    /** 持卡人真实姓名 企业-指企业名称，个人-指收款人真实姓名 */
    registerName: string;
    /** 持卡人身份证号 企业-指企业发票税号 个人-指收款人身份证号  */
    registerNumber: string;
}

/** 发票类型 */
export enum InvoiceType {
    /** 无 */
    None = -1,
    /** 营业税发票 */
    BusinessTax = 0,
    /** 增值税普通发票 */
    VATNormal = 1,
    /** 增值税专用发票 */
    VATSpecial = 2,
    /** 境外INVOICE */
    Overseas = 3,
}

/** 非联盟分成-提现财务信息 */
export interface CommonPayAccountInfo extends PayAccountInfo {
    // 税率, 企业需要填写个人不需要填写 1为1%
    taxRate?: number;
    /** 发票类型 企业需要填写个人不需要填写
     * 取值：-1:无, 0:营业税发票, 1:增值税普通发票, 2:增值税专用发票, 3:境外INVOICE
     */
    invoiceType?: InvoiceType;
}

/** 非联盟分成-提现账户信息 */
export interface CommonPayInfo {
    /** 提现账户类型 */
    userType: UserType;
    /** 是否开通百家号 */
    bjhOpen?: boolean;
    /** 是否需要确认财务信息，未在平台设置过财务信息需要填写/确认财务信息 */
    needConfirmFinance?: boolean;
    /** 财务信息 userAccount=null说明没有财务信息，需要用户注册财务信息 */
    userAccount: CommonPayAccountInfo | null;
}

/**
 * 银行列表接口返回数据
 */
export interface GetBankListRes {
    /** 银行总数 */
    count: number;
    /** 银行列表 */
    list: string[];
}

/** 下拉框展示数据接口 */
export interface DropdownMenuData {
    label: string;
    value: string;
}

/**
 * 银行列表下拉框数据列表
 * 银行列表接口返回数据 GetBankListRes 转成 下拉框展示数据
 */
export type BankList = DropdownMenuData[];

/** 省市列表接口返回数据 */
export interface ProvinceCityData {
    /** 省 */
    name: string;
    /** 省id */
    id: number;
    /** 省下市列表 */
    cities: string[];
}

// 转成联级下拉框数据结构
export interface ProvinceCityCascade {
    label: string;
    value: string;
    children?: ProvinceCityCascade[];
}

/** 联盟分成-提现账户提交信息 */
export interface PayAccountFormInfo extends PayAccountInfo {
    /** 银行卡开户所在省-市 */
    bankBranchProvinceCity: string[];
    /** 手机号验证码-提交时传 */
    verificationCode: string;
    /** 是否勾选服务协议 个人账户类型首次保存财务信息前端传给后端，编辑财务信息不传 */
    policySelected?: boolean;
}

/** 非联盟分成-提现账户提交信息 */
export interface CommonPayAccountFormInfo extends CommonPayAccountInfo {
    /** 银行卡开户所在省-市 */
    bankBranchProvinceCity: string[];
    /** 手机号验证码-提交时传 */
    verificationCode: string;
    /** 是否勾选服务协议 只有个人需要传 首次保存财务信息前端传给后端，编辑财务信息不传 */
    policySelected?: boolean;
}

/** 提现状态枚举 */
export enum PayStatus {
    /** 累计中 */
    Accumulate = 0,
    /** 提现中 */
    InProgress = 1,
    /** 提现成功 */
    Success = 2,
    /** 提现失败 */
    Failed = 3,
}

/** 发票状态枚举 */
export enum InvoiceStatus {
    /** 个人账户无需发票 */
    None = -1,
    /** 未贴税票 */
    NotStick = 0,
    /** 待审核 */
    Pending = 1,
    /** 审核通过 */
    Approved = 2,
    /** 审核拒绝 */
    Rejected = 3,
    /** 财务驳回 */
    FinanceRejected = 4,
    /** 暂停付款 */
    Suspended = 5,
    /** 提交失败 */
    SubmitFailed = 6,
    /** 审核中 */
    Auditing = 7,
    /** 已取消 */
    Canceled = 8,
}

/** 提现记录 */
export interface PayRecord {
    /** 交易流水号 */
    serialNumber: string;
    /** 提现账期 */
    accountPeriod: string;
    /** 税前预估收益（元） */
    estimateAmount: string;
    /** 税后实际收益（元） */
    estimateAmountAfterTax: string;
    /** 提现支付失败原因 */
    payFailedReason: string;
    /** 提现状态 */
    status: PayStatus;
    /** 发票状态 */
    invoiceState: InvoiceStatus;
}

/** 提现记录接口请求参数返回 */
export interface GetPayRecordListParams {
    pageNo: number;
    pageSize: number;
}

/** 提现记录接口返回 */
export interface GetPayRecordListRes {
    pageNo: number;
    pageSize: number;
    total: number;
    dataList: PayRecord[];
}
