/**
 * @file
 * <AUTHOR>
 */
export enum ActivityBadgeShow {
    /** 否 */
    No = 0,
    /** 是 */
    Yes = 1,
}

export enum ActivityType {
    // 训练营
    Training = 1,
    // 主题活动
    Theme = 2,
    // 灵感中心
    Inspiration = 3,
}

// 活动状态
export enum ActivityStatus {
    // 草稿
    Draft = 1,
    // 未开始
    NotStarted = 2,
    // 进行中
    InProgress = 3,
    // 已结束
    Ended = 4,
    // 已下线
    Offline = 5,
}

export interface ActivityListItem {
    // 活动id
    id: number;
    // 活动名称
    title: string;
    // 活动简介
    subTitle: string;
    // 活动发布时间
    pubTitle?: string;
    // 活动状态
    status: ActivityStatus;
    // 活动封面
    coverImg: string;
    // 是否置顶
    showTop: ActivityBadgeShow;
    // 是否 新
    showNew: ActivityBadgeShow;
    // 截止时间 单位 s
    endTime?: number;
    // 1.训练营、2.主题活动、3.灵感中心
    type: ActivityType;
}

export interface ActivityTabItem {
    // tab id
    id: number;
    // tab 名称
    name: string;
}

export interface ActivityListData {
    total: number;
    pageNo: number;
    pageSize: number;
    activities: ActivityListItem[];
    tabs: ActivityTabItem[];
}

export interface ActivityStatisticsData {
    // 参与数量
    participateInNum: number;
    // 进行中的数量
    inProgressNum: number;
}
