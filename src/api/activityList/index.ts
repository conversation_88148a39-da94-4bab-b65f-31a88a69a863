/**
 * @file
 * <AUTHOR>
 */
import {createInterface} from '@/api/request';
import {ActivityListData, ActivityStatisticsData} from '@/api/activityList/interface';

export default {
    /** 获取活动列表 */
    getActivityList: createInterface<{type?: number; pageNo: number; pageSize: number}, ActivityListData>(
        'GET',
        '/experhub/activity/list'
    ),
    /** 获取活动参与数据 */
    getActivityStatistics: createInterface<void, ActivityStatisticsData>('GET', '/experhub/activity/statistics'),
};
