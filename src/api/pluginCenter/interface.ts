/**
 * AI服务类型
 */
export enum AIServiceType {
    /**
     * 应用
     */
    App = 0,
    /**
     * 能力插件
     */
    Ability = 1,
    /**
     * 数据插件
     */
    Data = 2,
}

export const PLUGIN_TYPE_NAME = {
    [AIServiceType.Data]: '数据',
    [AIServiceType.Ability]: '能力',
    [AIServiceType.App]: '应用',
} as const;

export const PLUGIN_TYPE_EN_NAME = {
    [AIServiceType.Data]: 'DATA',
    [AIServiceType.Ability]: 'ABILITY',
    [AIServiceType.App]: 'APPLICATIONS',
} as const;

/**
 * AI服务类型英文小写字符串类型
 */
export type AIServiceEnType = 'data' | 'ability' | 'app';

/**
 * AI服务类型与英文小写字符串类型映射
 */
export const PLUGIN_TYPE_EN_LOWER_NAME = {
    [AIServiceType.Data]: 'data',
    [AIServiceType.Ability]: 'ability',
    [AIServiceType.App]: 'app',
} as const;

/** 插件Id类型 */
export type PluginId = string;
/** 插件FunctionId（即能力插件openapi.yaml文件中operationId）类型 */
export type PluginFunctionId = string;

/**
 * 插件发布范围类型
 * 0-私有 1-公开
 */
export enum PluginPublishType {
    /**
     * 私有
     */
    Private = 0,
    /**
     * @deprecated 后续只有私有和上架商店，公开类型废弃
     */
    Public = 1,
    /**
     * 上架商店
     */
    Store = 2,
}

/**
 * 插件发布范围名称
 */
export const PLUGIN_PUBLISH_TYPE_NAME = {
    [PluginPublishType.Private]: '私有',
    [PluginPublishType.Public]: '公开',
    [PluginPublishType.Store]: '上架商店',
} as const;

/**
 * 插件聚合状态枚举
 * 1-开发中 2-已发布 3-已下线
 */
export enum PluginStatus {
    /**
     * 开发中
     */
    Developing = 1,
    /**
     * 已发布
     */
    Online = 2,
    /**
     * 已下线
     */
    Offline = 3,
}
