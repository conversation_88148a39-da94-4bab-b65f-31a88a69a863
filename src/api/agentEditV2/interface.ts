import {AnswerStatus} from '@/api/q2c/interface';
import {AgentSource} from '@/api/createCityAgent/interface';

export enum TuningType {
    All = 0,
    Excellent = 1,
    Bad = 2,
}

export const TuningTypeText = {
    [TuningType.All]: '全部',
    [TuningType.Excellent]: '优质',
    [TuningType.Bad]: '较差',
};

export enum QaSource {
    Dataset = 1,
    Preview = 2,
}

export const QaSourceText = {
    [QaSource.Dataset]: '知识库',
    [QaSource.Preview]: '开发者调试',
};

export enum QaStatus {
    Pending,
    Feedbacked,
    Adopted,
    Featured,
    Effective,
    Expired,
    Fixed,
    /** 已提交调优数据 */
    Submitted,
    /** 已分发 */
    Distributed,
    /** 答案生成中 */
    Generating = 99,
}

export const QaStatusText = {
    [QaStatus.Pending]: '待处理',
    [QaStatus.Feedbacked]: '已反馈',
    [QaStatus.Adopted]: '已采纳',
    [QaStatus.Featured]: '已精选',
    [QaStatus.Effective]: '已生效',
    [QaStatus.Expired]: '已失效',
    [QaStatus.Fixed]: '已修正',
};

export interface AnnInfo {
    id: string; // 段落ID
    number: number; // 段落序号
    text: string; // 段落内容
    fileId: string; // 文件ID
    fileName: string; // 文件名
    datasetId: string; // 知识库ID
    datasetName: string; // 知识库名称
}

export interface QaDetail {
    tuningId: string;
    appId: string;
    tuningType: TuningType.Bad | TuningType.Excellent; // 1-优秀，2-较差
    status: QaStatus.Adopted | QaStatus.Featured | QaStatus.Feedbacked | QaStatus.Pending | QaStatus.Generating;
    keyword: string;
    question: string;
    /** 知识库自动生成的优质回答 / 开发者预览时的原始回答（不知道为什么要复用一个字段） */
    answer: string;
    source: QaSource;
    score: number;
    createTime: number;
    /** 关联数据集信息 */
    annText?: AnnInfo[];
    /** 反馈原因 */
    actionData?: {
        reasonList?: string[];
        idealAnswer?: string;
    };
    /** 调优数据 */
    tuningData?: {
        /** 思考路径调优 */
        answer?: string;
        /** 个性化调优 */
        chatAnswer?: string;
    };
    /** Q2C答案状态 */
    failStatus?: AnswerStatus;
}

interface PageParam {
    pageNo: number;
    pageSize: number;
}

export interface QaDetailListParam extends PageParam {
    appId: string;
    keyword?: string;
    source?: QaSource;
    tuningType?: TuningType;
    status?: QaStatus[];
}

export interface QaDetailListResponse {
    pageNo: number;
    pageSize: number;
    total: number;
    processingNum: number; // 待处理数量
    processedNum: number; // 已处理数量
    featuredNum: number; // 精选数量
    firstTuning: boolean; // 是否是第一个 Tuning
    processingDataSet: boolean; // 是否正在处理数据集
    dataList: QaDetail[];
}

export enum QaDetailAction {
    Adopt = 1,
    Feedback = 2,
    Featured = 3,
    CancelFeature = 4,
}

export const QaDetailActionText = {
    [QaDetailAction.Adopt]: '采纳',
    [QaDetailAction.Feedback]: '反馈',
    [QaDetailAction.Featured]: '精选',
    [QaDetailAction.CancelFeature]: '取消精选',
};

export interface QaDetailProcessParam {
    appId: string;
    tuningIds: string[];
    action: QaDetailAction;
    // 仅 action 为 反馈 时传该字段
    actionData?: {
        reasonList: string[];
        idealAnswer: string;
    };
}

export interface UpdateTuningDataParams {
    appId: string;
    tuningId: string;
    /** 思考路径调优 */
    answer: string;
    /** 个性化调优 */
    chatAnswer: string;
}

export interface DeleteTuningDataParams {
    tuningId: string;
}

/**
 * 是否存在下一页
 * 0-最后一页，1-存在下一页
 */
export enum HasMore {
    Yes = 1,
    No = 0,
}

/**
 * 触发总结状态
 * 0-触发总结成功，1-暂无可总结内容
 */
export enum SummaryTriggeredStatus {
    Success = 0,
    Fail = 1,
}

/**
 * 总结类型
 * 1-手动总结，2-自动总结
 */
export enum SummaryType {
    Manual = 1,
    Auto = 2,
}

/**
 * 总结状态
 */
export enum SummaryStatus {
    None = 'none',
    Doing = 'doing',
    Error = 'error',
    Finished = 'finished',
}

export interface LTMSummary {
    // 总结开始时间，毫秒级时间戳
    summaryStartTime: number;
    // 总结完成时间，毫秒级时间戳
    summaryEndTime: number;
    // 总结内容
    summaryText: string;
    summaryType: SummaryType;
    summaryId: string;
}

export interface LTMSummaryProgress {
    summaryStatus: SummaryStatus;
    summaryText?: string;
}

export interface LTMListParams {
    appId: string;
    pageSize?: number;
    pageNo?: number;
}

export interface LTMListResponse {
    summaryList: LTMSummary[];
    hasMore: HasMore;
}

export interface LTMSummaryResponse {
    status: SummaryTriggeredStatus;
    // toast 文案
    text: string;
}

// 白名单配置枚举
export enum FeatureName {
    /** 模型 */
    Model = 'model',
    /** 数字形象 */
    Figure = 'figure',
    /** 商品挂载 */
    GoodsMount = 'goods_mount',
    /** 调优分析报告 */
    TuningAnalyze = 'tuning_analyze',
    /** 模型框架 */
    RoleFramework = 'role_framework',
    /** 情感快捷指令 */
    ConsultShortcuts = 'consult_shortcuts',
    /** 快捷指令-配置端内链接 */
    InsideUrl = 'inside_url',
    /** 主动消息 */
    MessageReach = 'message_reach',
    /** Q2C */
    DeveloperQ2C = 'developerQ2C',
    /** 联盟分成收益 */
    DistributionProfit = 'distribution_profit',
    /** 内部用户不参与分成 */
    InnerNoProfit = 'innerNoProfit',
    /** 赞赏收益 */
    RewardProfit = 'reward_profit',
    /** 线索售卖 */
    ClueSaleSetting = 'clueSaleSetting',
    /** 对话头像 */
    DialogueAvatar = 'dialogueAvatar',
    /** 凤巢广告 */
    FCPromo = 'FCPromo',
    /** Q2C 上传视频能力 */
    Q2CVideo = 'q2c_video',
    /** 链接挂载 - 是否展示NA链接输入框 */
    LinkMountNaLinkInputVisible = 'linkmount_naLinkInputVisible',
}

// 白名单
export interface FeatureAccess extends Partial<Record<FeatureName, boolean>> {
    // 模型配置权限
    model?: boolean;
    // 数字人配置权限
    figure?: boolean;
    /** 商品挂载配置权限 */
    goods_mount?: boolean;
    /** 调优分析报告查看权限 */
    tuning_analyze?: boolean;
    /** 模型框架 */
    role_framework?: boolean;
    /** 情感快捷指令权限 */
    consult_shortcuts?: boolean;
    /** 百度APP内链接 */
    inside_url?: boolean;
    message_reach?: boolean;
    /** Q2C */
    developerQ2C?: boolean;
    /** 是否展示联盟分成收益 */
    [FeatureName.DistributionProfit]?: boolean;
    /** 内部用户不参与分成 */
    [FeatureName.InnerNoProfit]?: boolean;
    /** 是否展示赞赏收益 */
    [FeatureName.RewardProfit]?: boolean;
    /** 线索售卖 */
    [FeatureName.ClueSaleSetting]?: boolean;
    /** 对话头像 */
    [FeatureName.DialogueAvatar]?: boolean;
    /** 凤巢广告 */
    [FeatureName.FCPromo]?: boolean;
}

// 人物数字形象枚举，0-静态数字形象，1-动态数字形象
export enum FigureType {
    Static = 0,
    Dynamic = 1,
}

export enum CloudFigureId {
    Failed = -1,
}

// 动态人物数字形象生成状态枚举 0-待回调、1-生成成功、2-生成失败
export enum FigureTaskStatus {
    Generating = 0,
    Success = 1,
    Failed = 2,
}

/**
 * 动态数字人创建接口请求参数
 */
export interface FigureCreateParams {
    imgUrlList: string[];
    appId: string;
    // 动态 or 静态数字形象标识
    figureType: FigureType;
    // 模板id
    templateUuid?: string;
    // 来源
    source?: AgentSource;
}

/**
 * 动态数字人-抠图+质检 返回数据
 */
export interface FigureCheckAndSplitResponse {
    // 抠图结果
    foregroundImageUrl: string;
    // 质检结果状态码 0-成功 其他-失败
    errno: number;
    // 质检失败原因
    msg: string;
}

/**
 * 动态数字人-创建 接口返回数据
 */
export interface FigureCreateResponse {
    // 抠图结果 3:4
    foregroundImageUrl: string;
    // 数字人taskId
    taskUuid?: number;
    // 质检结果状态码 0-成功 其他-失败
    errno: number;
    // 质检失败原因
    msg: string;
}

/** 调优分步引导弹窗接口参数 */
export interface PopupParams {
    appId: string;
    name: string;
}

/** 调优分步引导弹窗接口响应 */
export interface PopupResponse {
    show: boolean;
    name: string;
    content: string;
}

// 主动发消息功能总结接口错误码
export enum SummaryErrno {
    NoConversation = 40013, // 没有对话历史
    Others = 40012, // 其他异常错误
}

/** 推荐对话接口 */
export interface RecommendResponse {
    msgId: string;
    query: string;
    answer: string;
}

/** 快速创建页示例智能体名称与人设 */
export interface Prompt {
    title: string;
    introduction: string;
}

export enum ShortcutsCredithStatus {
    Auth = 1,
    Unauth = 0,
}

export interface ShortcutsCreditResponse {
    authStatus: ShortcutsCredithStatus;
}

// 背景类型
export enum BgType {
    /* 系统内置 */
    OFFICIAL = -1,
    /* 用户自定义 */
    CUSTOM = 1,
}

export interface BackgroundConfigListItem {
    id: number;
    userInfoId: number;
    bgUrl: string;
    bgType: BgType;
    bgCityName: string;
    bgThumbnailUrl: string;
}
