/**
 * @file agent1.1 接口
 * <AUTHOR>
 * 2023/03/16
 */
import {
    MessageTriggerEvent,
    AgentConfigV2,
    PluginFunctionsRes,
    UserPluginFunctionsParams,
} from '@/api/agentEdit/interface';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {CustomOptions, request} from '@/api/request';
import {
    QaDetailListParam,
    QaDetailListResponse,
    QaDetailProcessParam,
    LTMListParams,
    LTMListResponse,
    LTMSummaryResponse,
    FigureCreateParams,
    FigureCreateResponse,
    FigureCheckAndSplitResponse,
    FeatureAccess,
    FeatureName,
    UpdateTuningDataParams,
    DeleteTuningDataParams,
    PopupParams,
    PopupResponse,
    RecommendResponse,
    Prompt,
    ShortcutsCreditResponse,
    BgType,
    BackgroundConfigListItem,
} from '@/api/agentEditV2/interface';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {TutorialListResponse} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';
import {getThreeInOnePrompt} from '@/utils/agent-compatible';
import {AgentMode} from '@/store/agent/initState';
import {getStringLength} from '@/utils/text';

export const generateAgentLogoV2 = (config: any, signal?: AbortSignal): Promise<string> =>
    request('POST', SECURE_URL_ENUM.AIAgentLogo, config, {
        timeout: 30000,
        signal,
    });

/**  获取精选插件 */
export const getOfficialPlugin = (params: UserPluginFunctionsParams): Promise<PluginFunctionsRes> =>
    request('GET', '/plugin/queryOfficialPlugin', params);

/**  获取用户能力插件Functions */
export const getUserPluginFunctions = (params: UserPluginFunctionsParams): Promise<PluginFunctionsRes> =>
    request('GET', '/plugin/queryUserPluginFunctionV2', params);

export interface GenerateAgentInfo {
    /** 名称 */
    name: string;
    /** 头像 */
    logoUrl: string;
    /** 指令 */
    system: string;
    /** 简介 */
    overview: string;
    /** 开场白 */
    description: string;
    /** 欢迎语 */
    recommends: string[];
    /** assistant 指令 */
    systemAssistant?: {
        shortcutsions: string;
        thoughtShortcutsions: string;
        chatShortcutsions: string;
    };
}

export enum SpeechType {
    UserSpeech = 1,
    OfficialSpeech = 2,
}

export enum SpeechStatus {
    Processing = 0,
    Success = 1,
    Failed = 2,
}

export enum SpeechTokenStatus {
    // 生效中
    Active = 0,
    // 已过期
    Expired = 1,
    // 已删除，移动端录音上传成功会删除
    Removed = 2,
}

export enum SpeechSource {
    TTS = 1,
    AIGC = 2,
}

// 待server端添加字段区分我的语音和官方语音
export interface AudioInfo {
    id: number;
    speechName: string;
    // 我的语音和官方语音
    speechType: SpeechType;
    // 语音来源 1-TTS  2-AIGC
    speechSource: SpeechSource;
    speechUrl: string;
    // 官方语音 id，个人语音时值相同
    ttsId: string;
    // 个人语音 id，个人语音生成中或生成失败、或者为官方语音时为空字符串
    mid: string;
    // 语音状态
    status: SpeechStatus;
    // 语音生成失败原因
    msg: string;
    /** 语速，取值1-15，默认为5 */
    spd: number | null;
    /** 语调，取值1-15，默认为5 */
    pit: number | null;
    /** 音量，取值1-15，默认为5 */
    vol: number | null;
}

// 语音试听接口传参
export interface PreviewSpeechParams {
    // 官方语音 id，个人语音时值相同
    ttsId: string;
    // 个人语音 id，个人语音生成中或生成失败、或者为官方语音时为空字符串
    mid: string;
    // 语音类型
    speechType: SpeechType;
    /** 语速，取值1-15，默认为5 */
    spd: number;
    /** 语调，取值1-15，默认为5 */
    pit: number;
    /** 音量，取值1-15，默认为5 */
    vol: number;
}

export interface SpeechConfigParams {
    // 声音ID
    speechId: number;
    // 语音名称
    speechName: string;
    /** 语速，取值1-15，默认为5 */
    spd: number;
    /** 语调，取值1-15，默认为5 */
    pit: number;
    /** 音量，取值1-15，默认为5 */
    vol: number;
}

export interface SpeechResponse {
    data: AudioInfo[];
    msg: string;
    errno: number;
}

export const generateAgentConfigV2 = async (
    payload: {
        introduction: string;
        mode?: AgentMode.Assistant;
        appId?: string | null;
    },
    signal?: AbortSignal
): Promise<GenerateAgentInfo> => {
    const res = await request('POST', SECURE_URL_ENUM.AgentConfGen, payload, {
        timeout: 90000,
        signal,
    });
    /** 防止接口生成的引导示例为空 */
    if (!res.recommends) {
        res.recommends = [];
    }

    // 舍弃长度大于 50 的 overview
    if (getStringLength(res.overview) > 100) {
        res.overview = '';
    }

    /** 如果生成的引导示例少于三个，则补充 */
    while (res.recommends.length < 3) {
        res.recommends.push('');
    }

    if (!res.system) {
        res.system = getThreeInOnePrompt(res.systemAssistant);
    }
    return res;
};

/**
 * 数字形象相关
 */
// 人物静态数字形象抠图接口
export const splitImage = (image: string): Promise<string> =>
    request(
        'GET',
        '/agent/splitImage',
        {image},
        {
            timeout: 45000,
        }
    );

// 其他类型数字形象拾取背景色接口
export const imageColorPick = (image: string): Promise<string> =>
    request(
        'GET',
        '/agent/imageColorPick',
        {image},
        {
            timeout: 30000,
        }
    );

// 动态数字形象抠图+质检接口
export const figureCheckAndSplit = (
    params: FigureCreateParams,
    signal?: AbortSignal
): Promise<FigureCheckAndSplitResponse> =>
    request('POST', SECURE_URL_ENUM.AIFigureCheckAndSplit, params, {
        timeout: 45000,
        signal,
    });

// 动态数字形象创建接口
export const figureCreate = (params: FigureCreateParams, signal?: AbortSignal): Promise<FigureCreateResponse> =>
    request('POST', SECURE_URL_ENUM.AIFigureCreate, params, {
        timeout: 45000,
        signal,
    });

// 根据背景类型查询背景配置
export const getFigureBackgroundConfig = (params: {bgType: BgType}): Promise<BackgroundConfigListItem[]> =>
    request('GET', `/background/config/list?bgType=${params.bgType}`);

// 停止数字人任务 6.29废弃接口
export const figureStop = (appId: string, uuid: number): Promise<void> =>
    request('POST', '/agent/figure/stop', {appId, uuid});

export const FETCH_SPEECH_DATA_API = '/speech/list';

// 获取语音包列表
export const fetchSpeechData = (): Promise<AudioInfo[]> => request('GET', FETCH_SPEECH_DATA_API, {});

// 语音试听
export const previewSpeech = (params: PreviewSpeechParams): Promise<{previewUrl: string}> =>
    request('GET', '/speech/preview', params);

// 更新语音配置（自定义语音）
export const updateSpeechConfig = (params: SpeechConfigParams): Promise<void> =>
    request('POST', '/speech/updateConfig', params);

// 获取用于移动端扫码录制的二维码 token
export const getSpeechToken = (): Promise<{token: string}> => request('GET', '/speech/getToken');

// 获取用于移动端扫码录制的二维码 token 的状态，用于提示用户是否在移动端录音完成
export const checkSpeechToken = (
    token: string
): Promise<{token: string; status: SpeechTokenStatus; speechInfo: AudioInfo}> =>
    request(
        'POST',
        '/speech/checkToken',
        {token},
        {
            headers: {
                'Content-Type': 'multipart/form-data',
                'User-Session': getMonitorStorage().get('sessionId'),
                'Lingjing-Operation': getMonitorStorage().get('operationModule'),
            },
            forbiddenToast: true,
        }
    );

// 编辑语音包名称
export const updateSpeechName = (speechId: number, speechName: string): Promise<void> =>
    request(
        'POST',
        '/speech/updateName',
        {speechId, speechName},
        {
            headers: {
                'Content-Type': 'multipart/form-data',
                'User-Session': getMonitorStorage().get('sessionId'),
                'Lingjing-Operation': getMonitorStorage().get('operationModule'),
            },
        }
    );

// 删除语音包
export const deleteSpeech = (speechId: number): Promise<void> =>
    request(
        'POST',
        '/speech/delete',
        {speechId},
        {
            headers: {
                'Content-Type': 'multipart/form-data',
                'User-Session': getMonitorStorage().get('sessionId'),
                'Lingjing-Operation': getMonitorStorage().get('operationModule'),
            },
        }
    );

// 获取指定语音关联的已发布 agent 列表
export const getAgentsBySpeechId = (speechId: number): Promise<Array<{appId: string; appName: string}>> =>
    request('GET', '/speech/getAgentsBySpeechId', {speechId});

// 当前登录用户上传录音文件数据
export const uploadSpeech = (file: FormData, signal?: AbortSignal): Promise<AudioInfo> =>
    request('POST', SECURE_URL_ENUM.SpeechUpload, file, {
        forbiddenToast: true,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 10000,
        signal,
    });

// 根据 token 上传录音文件数据
export const uploadSpeechWithToken = (file: FormData, signal?: AbortSignal): Promise<void> =>
    request('POST', '/speech/speechUploadWithToken', file, {
        forbiddenToast: true,
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 10000,
        signal,
    });

export const GET_TUNING_LIST_API = '/tuning/list';

export const getQaDetailList = (param: QaDetailListParam): Promise<QaDetailListResponse> =>
    request('POST', GET_TUNING_LIST_API, param, {timeout: 10000});

export const processQaDetail = (param: QaDetailProcessParam): Promise<void> =>
    request('POST', '/tuning/process', param);

export const updateTuningData = (param: UpdateTuningDataParams): Promise<void> =>
    request('POST', '/tuning/update', param);

export const deleteTuningData = (param: DeleteTuningDataParams): Promise<void> =>
    request('POST', '/tuning/delete', param);

// 查询是否需要展示调优引导提示
export const SHOW_TUNING_STEP_GUIDE_API = '/popup';

export const showTuningStepGuide = (param: PopupParams): Promise<PopupResponse> =>
    request('GET', SHOW_TUNING_STEP_GUIDE_API, param);

// 记录已展示调优引导提示
export const recordTuningStepGuide = (param: PopupParams): Promise<PopupResponse> =>
    request('GET', '/popup/view', param);

export const getIsFirstTuning = (appId: string) =>
    getQaDetailList({appId, pageNo: 1, pageSize: 1, status: [0]}).then(res => res.firstTuning);

export const startDatasetStrategy = (appId: string) => request('POST', '/tuning/strategy', {appId});

export const getLTMList = (params: LTMListParams): Promise<LTMListResponse> =>
    request('GET', '/agent/ltm/list', params);

export const summayLTM = (params: {appId: string}): Promise<LTMSummaryResponse> =>
    request('POST', '/agent/ltm/summary', {}, {params});

// 获取权限
export const getFeatureAccess = async (
    params: {featureName: FeatureName; appId?: string},
    options?: CustomOptions
): Promise<FeatureAccess> => request('GET', '/feature/access', params, options || {});

// 获取白名单权限V2
export const getFeatureAccessV2 = async (
    params: {featureName: FeatureName; appId?: string},
    options?: CustomOptions
): Promise<FeatureAccess> => request('GET', '/feature/access/v2', params, options || {});

/** 获取引导追问提示词 */
export const getDefaultSuggestionTemplate = (): Promise<{tpl: string}> =>
    request('GET', '/agent/conf/template?content=autoSuggestion');

// ai 生成角色形象
export const generateAgentFigure = (config: AgentConfigV2, signal?: AbortSignal): Promise<string> =>
    request('POST', SECURE_URL_ENUM.AIGenPic, config, {
        timeout: 30000,
        signal,
    });

// 获取多个权限
export const getFeatureAccessList = async (
    data: {featureNameList: FeatureName[]},
    options?: CustomOptions
): Promise<FeatureAccess> => request('POST', '/feature/accesslist', data, options || {});

// 快捷指令是否资质认证
export const getFeatureCredit = async (): Promise<ShortcutsCreditResponse> => request('GET', '/feature/credit');

/** 生成主动消息 */
export const triggerActiveMsg = (appId: string, summaryMode: MessageTriggerEvent): Promise<void> =>
    request(
        'POST',
        '/agent/message/summary',
        {appId, summaryMode},
        {
            timeout: 120000,
        }
    );

// 获取推荐对话列表
export const getRecommendDialogList = (params: {
    appId?: string;
    modeType?: AgentModeType;
}): Promise<RecommendResponse[]> => request('GET', '/recSession/list', params);

// 删除推荐对话
export const deleteRecommendDialog = (params: {msgId: string; appId: string}): Promise<void> =>
    request('POST', '/recSession/delete', params);

// 获取快速创建页示例智能体名称和人设
export const getIntroductionPrompts = (): Promise<Prompt[]> => request('GET', '/agent/introduction/prompt');

// 获取调优-调优教程列表数据
export const getTuningTutorialData = (params: {key: string}): Promise<TutorialListResponse> =>
    request('GET', '/config/get', params);
