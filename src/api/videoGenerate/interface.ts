export enum ConfirmationStatus {
    Waiting,
    Confirmed,
}

export enum GenerateStatus {
    All, // 全部
    Processing, // 处理中
    Done, // 已可用
    Fail, // 处理失败
}

export interface ConfirmationListParam {
    appId: string;
    status: ConfirmationStatus;
    generateStatus?: GenerateStatus;
    pageNo?: number;
    pageSize?: number;
    keyword?: string;
    reqType?: number; // 请求类型：1-一期请求，2-二期（托管）默认是1期(不托管)
}

export interface ConfirmationListResponse {
    pageNo: number;
    pageSize: number;
    total: number;
    processedNum: number;
    processingNum: number;
    failNum: number;
    dataList: ConfirmationInfo[];
}

export const enum ConfirmationInfoStatus {
    已确权 = 3,
    已提报 = 4,
    // 未提报 = 5,
    视频生成完成 = 8,
    视频生成失败 = 9,
    提报失败 = 10,
    视频生成中 = 11,
    // 确权驳回 = 14,
    慧播星视频生成中 = 15,
    慧播星视频生成完成 = 16,
    慧播星视频生成失败 = 17,
}

export const enum AuditStatus {
    审核中 = 0,
    通过 = 1,
    拒绝 = 2,
}

export interface ConfirmationInfo {
    itemId: number; // 教育调优ID
    appId: string; // 智能体ID
    query: string;
    status: ConfirmationInfoStatus; // 状态
    auditStatus: AuditStatus; // 百家号审核状态
    auditMsg: string;
    script: string; // 视频脚本
    verticalText: string; // 板书解析文本（动画板书、古诗词）
    videoUrl: string; // 视频链接
    coverUrl: string; // 封面链接
}

export interface ConfirmItemParam {
    appId: string; // 智能体ID
    items?: Array<{itemId: number}>; // 确权ID列表
    submitReject?: boolean; // 提交驳回，为true时，items为驳回列表
    submitAll?: boolean; // 一键确权为true，否则为false，为true时items可不传
}

export const enum HostingTabKey {
    /** 形象授权 */
    imageVideo = '1',
    /** 内容托管 */
    contentHosting = '2',
}

export enum ReqTypeStatus {
    /** 一期不托管（默认） */
    NotHosting = 1,
    /** 二期 */
    Hosting = 2,
}

export interface GetVideoHostingParams {
    appId: string;
    agreement: boolean;
    quantity: number;
}

export interface VideoHostingConfigResponse {
    quantity: number;
    complete: boolean; // true-上述额度生产完毕，false-尚未生产完毕
}

export interface VideoHostingSummaryRes {
    produced: number;
    quantity: number;
    total: number;
    availableNum: number;
    producing: number;
}

export interface DeleteConfirmItemParam {
    appId: string; // 智能体ID
    recordIds?: number[];
}

export interface VideoItem {
    itemId: string;
    title: string;
    videoUrl: string;
    updateTime: string;
    videoUrlWithFilename: string;
}

export interface BatchDownloadResponse {
    count: number;
    list: VideoItem[];
}

export interface VideoHostingDemoRes {
    videoUrl: string;
    confirmStatus: boolean;
    screen?: number; // 0为竖版，1为横版
}

/** 视频预览数据返回值 */
export interface VideoDemoResponse {
    status: number;
    showEdit: boolean;
    videoUrl?: string;
    figureImg?: string;
    backgroundImg?: string;
    width?: number;
    height?: number;
    screen?: number;
    position?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

/** 视频预览生成请求参数 */
export interface VideoPreviewGenerateParams {
    type: number;
    previewVideoGen: boolean;
    appId: string;
    backgroundImg?: string;
    previewImage: string;
    position: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    screen?: number; // 0为竖版，1为横版
}

/** 视频预览生成状态 */
export enum VideoPreviewStatus {
    /** 无 */
    NONE = 0,
    /** 预览视频生成中 */
    GENERATING = 1,
    /** 预览视频生成成功 */
    SUCCESS = 2,
    /** 错误状态 */
    ERROR = -1,
}

/** 视频预览状态查询返回值 */
export interface VideoPreviewStatusResponse {
    status: VideoPreviewStatus;
    videoUrl?: string;
}

/** 视频生成类型枚举 */
export enum VideoGenerateType {
    /** 预览视频生成 */
    PREVIEW = -1,
    /** 确认应用生成 */
    APPLY = 2,
}

export enum SCREEN_MODEL {
    Vertical = '竖版',
    Horizontal = '横版',
}
