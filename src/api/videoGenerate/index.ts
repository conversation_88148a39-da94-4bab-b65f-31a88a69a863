import {createInterface, request} from '@/api/request';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {
    ConfirmationListParam,
    ConfirmationListResponse,
    ConfirmItemParam,
    GetVideoHostingParams,
    VideoHostingConfigResponse,
    DeleteConfirmItemParam,
    VideoHostingSummaryRes,
    BatchDownloadResponse,
    VideoHostingDemoRes,
    VideoDemoResponse,
    VideoPreviewGenerateParams,
    VideoPreviewStatusResponse,
} from './interface';

/** 智能体垂类标签查询 */
export const getVideoGenerateTag = createInterface<{appId: string}, boolean>('POST', '/tuning/vertical/tag');

export const getConfirmationList = createInterface<ConfirmationListParam, ConfirmationListResponse>(
    'POST',
    '/tuning/vertical/list'
);

// 确权批量提交接口
export const confirmItem = createInterface<ConfirmItemParam, null>('POST', '/tuning/vertical/submit');

// 确权批量提交接口
export const deleteItem = createInterface<Pick<ConfirmItemParam, 'appId' | 'items'>, null>(
    'POST',
    '/tuning/vertical/delete'
);

/** 智能体视频托管配置查询接口 */
export const getVideoGenerateHosting = createInterface<{appId: string}, boolean>('GET', '/tuning/vertical/hosting');

/** 汇总数据  */
export const getVideoHostSummary = createInterface<{appId: string}, VideoHostingSummaryRes>(
    'GET',
    '/tuning/vertical/summary'
);

/** 教育垂类视频成产托管配置查询 接口 */
export const getVideoHostingConfig = createInterface<{appId: string}, VideoHostingConfigResponse>(
    'GET',
    '/tuning/video/hosting/query'
);

/**
 * 提交视频生产托管配置接口(只有当上述额度生产完毕 complete=true才允许提交)
 */
export const submitVideoHosting = createInterface<GetVideoHostingParams, null>('POST', '/tuning/video/hosting/setting');

/**
 * 确权批量删除接口（只能删除生成成功和生成失败的）
 */
export const verticalDeleteBatch = createInterface<DeleteConfirmItemParam, null>('POST', '/tuning/video/batchDelete');

/**
 * 确权批量/单个下载接口（只下载生成成功的）
 */
export const submitBatchDownload = createInterface<DeleteConfirmItemParam, BatchDownloadResponse>(
    'POST',
    '/tuning/video/batchDownload',
    {
        forbiddenToast: true,
        timeout: 60000,
    }
);

/** 取消托管配置授权(短信通知BD) */
export const cancelVideoHosting = createInterface<{appId: string}, null>('POST', '/tuning/video/hosting/cancel');

/**  tuning/video/demo'*/
export const getVideoHostingDemo = createInterface<{appId: string}, VideoHostingDemoRes>('GET', '/tuning/video/demo');

/** 获取视频预览数据 */
export const getVideoDemo = createInterface<{appId: string}, VideoDemoResponse>('GET', '/tuning/video/demo');

/** 生成预览视频 */
export const generatePreviewVideo = createInterface<VideoPreviewGenerateParams, null>(
    'POST',
    '/tuning/video/saveAndGen'
);

/** 获取预览视频状态 */
export const getPreviewVideoStatus = createInterface<{appId: string}, VideoPreviewStatusResponse>(
    'GET',
    '/tuning/video/demo'
);

interface BgImageItem {
    url: string;
}

/** 获取横版背景图片列表 */
export const getHorizonBgList = (): Promise<BgImageItem[]> => request('GET', '/config/get', {key: 'figure_horizon_bg'});

/** 获取竖版背景图片列表 */
export const getVerticalBgList = (): Promise<BgImageItem[]> =>
    request('GET', '/config/get', {key: 'figure_vertical_bg'});

/** 上传背景图片 */
export const uploadBgImage = (file: FormData, signal?: AbortSignal): Promise<string> =>
    request('POST', '/agent/uploadImage', file, {
        headers: {
            'Content-Type': 'multipart/form-data',
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        timeout: 60 * 1000,
        signal,
    });

/** demo视频确认无误  -- server打上视频生产标签。 如果是教育垂类，打上解题视频标签 & 视频生产标签 否则，只打上视频生产标签 */
export const videoDemoConfirm = createInterface<{appId: string}, null>('POST', '/tuning/video/demo/confirm');
