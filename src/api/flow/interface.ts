import {FlowJson} from '@/store/flow/flowJson';

export interface SaveFlowParams {
    appId: string;
    version?: string;
    operator?: string;
    flowJson: any;
    versionCodeOnSave: number;
}

export interface SaveFlowResponse {
    updateTime: number;
    versionCodeOnSave: number;
}

export interface GetFlowParams {
    appId: string;
}

export interface GetFlowResponse {
    appId: number;
    versionCodeOnSave: number;
    version: string;
    updateTime: number;
    flowJson: FlowJson;
}
