/**
 * @file 画布页的主要接口
 * <AUTHOR>
 */

import {FlowConfig} from '@/store/flow/flowConfigStore';
import {createInterface} from '../request';
import {GetFlowParams, GetFlowResponse, SaveFlowParams, SaveFlowResponse} from './interface';

export const saveFlow = createInterface<SaveFlowParams, SaveFlowResponse>('POST', '/appflow/save');
export const getFlow = createInterface<GetFlowParams, GetFlowResponse>('GET', '/appflow/detail');
export const getFlowConfig = createInterface<void, FlowConfig>('GET', '/component/all');
