/**
 * @file 全局指导
 * <AUTHOR>
 */

import {createInterface} from '@/api/request';
import {UserRecordParams, UserRecordData, UserRecordBody, PopupParams, PopupInfo} from './interface';

export default {
    // 获取用户上次插件类型
    getUserRecord: createInterface<UserRecordParams, UserRecordData>('GET', '/user/record'),
    // 记录用户选择插件类型
    postUserRecord: createInterface<UserRecordBody, void>('POST', '/user/record'),
    // 记录点击设置弹窗
    recordPopup: createInterface<PopupParams, PopupInfo>('GET', '/popup/view'),
    // 查询弹窗是否需要打开
    getPopup: createInterface<PopupParams, PopupInfo>('GET', '/popup'),
};
