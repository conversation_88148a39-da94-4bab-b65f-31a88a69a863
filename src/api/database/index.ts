/**
 * @file 数据库 请求接口
 * <AUTHOR>
 * 2024.7.8
 */

import {request} from '../request';
import {DatabaseConfig, DatabaseConfigList, DatabaseDataList, DatabaseDataContent} from './interface';

export default {
    /** 查询智能体下的数据表及表的schema信息 */
    getDatabaseList: (appId: string): Promise<DatabaseConfigList> => request('GET', '/database/list', {appId}),
    /** 新建数据表  */
    createDatabase: (params: DatabaseConfig): Promise<string> => request('POST', '/database/create', params),
    /** 编辑数据表  */
    updateDatabase: (params: DatabaseConfig): Promise<void> => request('POST', '/database/update', params),
    /** 删除数据表  */
    deleteDatabase: ({appId, databaseId}: {appId: string; databaseId: string}): Promise<void> =>
        request('DELETE', '/database/delete', {appId, databaseId}),
    /** 查询数据表数据内容  */
    searchDatabase: ({
        appId,
        databaseId,
        cursor,
        pageSize,
    }: {
        appId: string;
        databaseId: string;
        cursor?: number;
        pageSize?: number;
    }): Promise<DatabaseDataContent> => request('GET', '/database/data/search', {appId, databaseId, cursor, pageSize}),
    /** 清空数据表  */
    clearDatabase: ({appId, databaseId}: {appId: string; databaseId: string}): Promise<DatabaseDataList> =>
        request('DELETE', '/database/data/clear', {appId, databaseId}),
};
