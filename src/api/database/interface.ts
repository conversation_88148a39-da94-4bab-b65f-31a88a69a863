/**
 * 数据库配置相关
 */

// 数据表模式，1 单用户模式 2 多用户模式
export enum DatabaseMode {
    SingleUserMode = 1,
    MultiUserMode = 2,
}

// 字段类型枚举 1 String 2 Integer  3 Time 4 Number 5 Boolean
export enum DatabaseFieldType {
    String = 1,
    Integer = 2,
    Time = 3,
    Number = 4,
    Boolean = 5,
}

// 数据表字段信息
export interface DatabaseField {
    fieldId: string; // 字段id
    fieldName: string; // 字段名称
    fieldDescription: string; // 字段描述
    fieldType: DatabaseFieldType; // 字段类型
    required: boolean; // 是否必填
}

// 数据库配置信息
export interface DatabaseConfig {
    appId?: string;
    databaseId?: string;
    agentId?: string;
    databaseName: string; // 数据库名称
    databaseDescription: string; // 数据库描述
    databaseMode: DatabaseMode; // 工作模式
    databaseSchema: DatabaseField[]; // 数据库字段
}

export type DatabaseConfigList = DatabaseConfig[];

/**
 * 数据库信息
 */
export interface DatabaseInfo {
    [key: string]: DatabaseConfig[];
}

/**
 * 数据表内容
 */

export interface DatabaseDataContent {
    databaseName: string; // 数据库名称
    fieldNames: string[]; // 字段名称
    rows: string[][]; // 每行的数据
    hasMore: boolean; // 是否还有更多数据
    nextCursor: number; // 下一页游标
}

export type DatabaseDataList = DatabaseDataContent[];
