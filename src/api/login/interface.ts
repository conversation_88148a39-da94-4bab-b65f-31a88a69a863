/**
 * @description // 当前用户数据结构
 */
export enum UserType {
    Passport = 1,
    UC = 2,
}

export const USER_TYPE_NAME = {
    [UserType.Passport]: '百度账号',
    [UserType.UC]: '百度营销账号',
};

interface BaseUserInfoData {
    /**
     * @description // 用户唯一id
     */
    userId: string;
    /**
     * @description // 用户头像
     */
    portrait: string;
    /**
     * @description // 登录类型
     */
    userType: UserType;
    /**
     * @description 如果为uc用户则存在，uc用户ID
     */
    ucUserId?: string;
    /**
     * @description 是否拥有超级权限，拥有可以管理被代理人的智能体等超级权限
     */
    hasEnhancedAccess: boolean;
}

/**
 * @description // 接口返回用户信息
 */
export interface UserInfoData extends BaseUserInfoData {
    /**
     * @description // 用户名称
     */
    userName: string;
    /**
     * @description // passport中展示的
     */
    displayName: string;
}

/**
 * @description // store存储用户信息
 */
export interface UserInfoStore extends BaseUserInfoData {
    /**
     * @description // 平台展示名称
     */
    name: string;
}

/**
 * @description // 分散认证令牌信息
 */
export interface StokenInfo {
    /**
     * @description // stoken认证标识
     */
    stokenAuthFlag: boolean;
    /**
     * @description // stoken认证地址
     */
    stokenAuthUrl: string;
}

export enum EntityTypes {
    PERSON = 1,
    ENTERPRISE = 2,
    GOVERNMENT = 3,
}

export enum EntityQualifyStatus {
    Default = 0,
    Auditing = 1,
    Pass = 2,
    Fail = 3,
    InvalidEntityStatus = -1,
}

/**
 * @description  主体信息
 *
 */
export interface EntityInfo {
    /**
     * @description  客户主体ID
     *
     */
    customerId: string;
    /**
     * @description  主体审核状态
     * 0-待审核，1-审核中，2-审核通过，3-审核不通过
     */
    status: EntityQualifyStatus;
    /**
     * @description 主体类型
     * 1-个人 2-企业 3-政府
     */
    type?: EntityTypes;

    /** 开发者名称 */
    developerName?: string;
}

/**
 * @description 用户在不同平台创建的智能体信息，用于跳转判断
 */
export interface UserAgentInfo {
    /**
     * @description 是否存在文心智能体
     */
    hasWenXinAgent: boolean;
    /**
     * @description 是否存在巧舱智能体
     */
    hasQiaoCangAgent: boolean;
}

interface BaseUserData {
    /**
     * @description // 用户授权相关
     */
    stokenInfo: StokenInfo;
    /**
     * @description // 标记该用户是否有app true-有应用 false-无应用
     */
    hasAppFlag: boolean;

    customerInfo: EntityInfo;

    agentInfo: UserAgentInfo;
}

/**
 * @description // 接口用户数据结构
 */
export interface UserData extends BaseUserData {
    /**
     * @description // 用户相关
     */
    userInfo: UserInfoData;

    /** 是否被服务商代运营 */
    hasTpProxy?: boolean;
}

/**
 * @description // 接口用户数据结构
 */
export interface UserStore extends BaseUserData {
    /**
     * @description // 用户相关
     */
    userInfo: UserInfoStore;

    /** 当前用户是否被TP服务商代运营 */
    hasTpProxy?: boolean;
}

export interface UserInfoResponse {
    errno: number;
    msg: string;
    data: UserInfoData | null;
    timestamp: number | string;
}

/**
 * @description //退出登录返回值
 */
export type PassportUrl = string;

/**
 * 获取用户信息请求参数
 */
export interface UserInfoParams {
    /**
     * 用于获取stoken中定向的我们的地址
     */
    redir?: string;
}

export type Hao123params = string;
