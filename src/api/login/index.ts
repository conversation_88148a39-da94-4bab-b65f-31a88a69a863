import {createInterface, request} from '@/api/request';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {UserData, PassportUrl, UserInfoParams, Hao123params} from './interface';

export interface responseData<T> {
    errno: number;
    msg: number;
    data: T;
    timestamp: number;
}

// 对外暴露接口
export default {
    // 获取用户信息
    // getUserInfo: (async () => {
    //     const timer = getMonitorStorage().get('passportLoginSuccess');
    //     timer && clearTimeout(timer);
    //     return createInterface<UserInfoParams | void, UserData>('GET', '/user/info');
    // })(),

    // 2024.8.15 header中添加用户来源渠道值
    getUserInfo: (params: UserInfoParams | void, option: any): Promise<UserData> => {
        const timer = getMonitorStorage().get('passportLoginSuccess');
        timer && clearTimeout(timer);
        return request('GET', '/user/info', params, {
            ...option,
            headers: {
                Source: new URLSearchParams(location.search).get('source'),
            },
        });
    },

    // 退出登录
    userLogout: createInterface<void, PassportUrl>('GET', '/user/logout'),
    // 注入bduss的接口
    crossdomain: createInterface<Hao123params, void>('GET', '/crossdomain'),
};
