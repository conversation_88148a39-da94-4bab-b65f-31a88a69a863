/**
 * @file 数据插件-数据接口
 * @doc 贴接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/6HYOYHfwaY0Hz_
 * <AUTHOR>
 */

import dataSetApi from '@/api/dataSet';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {createInterface} from '../request';
import {PluginId} from '../pluginList/interface';
import {DataSetStatus} from '../dataSet/interface';
import {DataPluginFormInfo, DataPluginSubmitFormInfo, CheckNameDescInfo, AuthConfig, SubmitErrRes} from './interface';

// 对外暴露接口
export default {
    // 获取 sessionId
    getSession: createInterface<void, {sessionId: string}>('GET', '/plugin/session'),
    // 名称等校验
    dataPluginNameCheck: createInterface<CheckNameDescInfo, string | null>('GET', '/plugin/dataset/check'),
    // 根据id查询保存的插件内容
    getDataPluginById: createInterface<{pluginId: PluginId}, DataPluginFormInfo>('GET', '/plugin/dataset/edit'),
    // 测试预览
    dataPluginTest: createInterface<DataPluginFormInfo, AuthConfig>('POST', '/plugin/dataset/test'),
    // 保存=创建
    dataPluginSave: createInterface<DataPluginFormInfo, {pluginId: PluginId}>(
        'POST',
        SECURE_URL_ENUM.PluginDatasetSave
    ),
    // 提交审核
    dataPluginSubmit: createInterface<
        {
            pluginId?: PluginId;
        } & DataPluginSubmitFormInfo &
            DataPluginFormInfo,
        {pluginId?: PluginId} & SubmitErrRes
    >('POST', SECURE_URL_ENUM.PluginDatasetSubmit, {
        timeout: 8 * 1000,
    }),
    // 获取已完成的知识库
    getCompletedDataset: () => dataSetApi.getAllDataSet({status: DataSetStatus.done}),
    // 获取预览地址
    getPreviewUrl: createInterface<{sessionId: string; authConfig: AuthConfig}, {previewUrl: string}>(
        'POST',
        '/plugin/test/preview'
    ),
};
