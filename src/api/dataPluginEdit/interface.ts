/**
 * @file 数据插件-类型定义
 * @doc 贴接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/6HYOYHfwaY0Hz_
 * <AUTHOR>
 * @remark rd反馈配置相关的参数需要填充到插件的ymal文件中，所以尽量复用插件的
 */

import {DataSetDetail} from '../dataSet/interface';
import {PluginId} from '../pluginList/interface';

/**
 * 用户打开页面生成唯一标识
 */
type SessionId = string;

/**
 * 创建数据插件-基础信息
 */
export interface BaseInfo {
    /**
     * 插件id（平台生成的）
     */
    pluginId?: PluginId;
    /**
     * 插件名称（展示给用户的）
     */
    nameForHuman: string;
    /**
     * 插件头像（展示给用户的）
     */
    image: string;
    /**
     * 插件描述（展示给用户的）
     */
    desForHuman: string;
    /**
     * 知识库
     */
    datasetList: Array<{
        datasetId: DataSetDetail['datasetId'];
        datasetName: DataSetDetail['datasetName'];
    }>;
    /**
     * 联系邮箱
     */
    email: string;
}

/**
 * 创建数据插件-模型提示信息
 */
export interface ModalPromptInfo {
    /**
     * 模型提示名称
     */
    nameForModel: string;
    /**
     * 模型提示描述
     */
    desForModel: string;
}

/**
 * 创建数据插件-功能设置
 */
export interface FunctionSettingInfo {
    /**
     * 信息检索
     */
    searchApi?: boolean;
    /**
     * 对话内容生成
     */
    conversationApi?: boolean;
}

/**
 * 信息检索配置
 */
export interface SearchSetting {
    /**
     * 无匹配时由模型生成通用答案
     */
    searchMatch?: boolean;
    /**
     * 是否展示文件溯源
     */
    searchTrace?: boolean;
}
export enum AbilityValue {
    NOT_CALL = 0,
    INFO_RETRIEVE = 1,
}

/**
 * 创建数据插件-高级设置
 */
export interface AdvanceSetting {
    /**
     * 用户输入
     */
    userInput: string;
    /**
     * 预期功能
     */
    ability: AbilityValue;
    /**
     * 提示词
     */
    prompt: string;
    /**
     * 变量内容
     */
    content?: string;
}

/**
 * 创建数据插件-表单类型
 */
export interface DataPluginFormInfo extends BaseInfo, ModalPromptInfo, FunctionSettingInfo, SearchSetting {
    /**
     * 当前页面对话id
     */
    sessionId?: SessionId;
    /**
     * 上次编辑时间
     */
    lastSaveTime?: number;
    /**
     * 高级配置
     * 第一层是示例，第二层是对话轮次
     */
    advanceConfig?: AdvanceSetting[][];
}

/**
 * 数据插件-提交审核
 */
export interface DataPluginSubmitFormInfo {
    /**
     * 版本号
     */
    version: string;
    /**
     * 版本信息
     */
    versionInfo: string;
}

/**
 * 名称描述等机审校验
 */
export interface CheckNameDescInfo {
    /**
     * 插件名称（展示给用户的）
     */
    nameForHuman?: string;
    /**
     * 插件描述（展示给用户的）
     */
    desForHuman?: string;
    /**
     * 插件名称（展示给Model的）
     */
    nameForModel?: string;
    /**
     * 插件描述（展示给Model的）
     */
    desForModel?: string;
}

export interface AuthConfig {
    token: string;
    publicKey: string;
}

export interface SubmitErrRes {
    versionMsg?: string;
    contentMsg?: string;
}
