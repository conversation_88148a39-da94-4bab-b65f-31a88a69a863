import {createInterface, request} from '../request';

export enum BaijiahaoStatus {
    Success = 1,
    Fail = -1,
    InProcess = 0,
}

export interface BaijiahaoProcess {
    /** 导入任务ID */
    taskId: string;
    taskStatus: BaijiahaoStatus;
    /** 导入文件数 */
    taskFile: number;
    /** 导入文件总大小 */
    taskSize: number;
    /** 已导入文件大小，100%时为null */
    curFileSize: number | null;
    /** 导入耗时（毫秒） */
    cost: number;
    /** 剩余待导入文件数，为0时导入完成 */
    remainFile: number;
    /** 已导入的文件信息 */
    importFiles: Array<{
        fileId: string;
        fileName: string;
        type: BaijiahaoFileType;
        coverImage: string;
        createTime: number;
    }>;
    /** 绑定的知识库ID */
    datasetId?: string;
    /** 失败原因 */
    failMessage?: string;
}

export enum BaijiahaoPlatform {
    WeiXin = 'wx',
    TouTiao = 'toutiao',
    IQiYi = 'iqiyi',
    WeiBo = 'wb',
    <PERSON>Hong<PERSON>hu = 'xhs',
    DouYin = 'douyin',
    ZhiHu = 'zhihu',
    KuaiShou = 'ks',
    BiliBili = 'bilibili',
}

export enum BaijiahaoPlatformAuthStatus {
    NoAuth,
    Auth,
    AuthProcessing,
    AuthFail,
}

/** 任务类型，1: 同步 2: 异步 */
export enum BaijiahaoImportType {
    /** 同步导入 */
    Sync = 1,
    /** 异步导入 */
    Async = 2,
}

export interface BaijiahaoPlatformInfo {
    platform: BaijiahaoPlatform;
    authStatus: BaijiahaoPlatformAuthStatus;
}

export enum BaijiahaoFileType {
    ShortVideo = 1,
    DouYinVideo = 2,
    XiaoHongShuPdf = 3,
    WeiBoPdf = 4,
}

export interface BaijiahaoFileInfo {
    nid: string;
    type: BaijiahaoFileType;
    title: string;
    createTime: number;
    coverImage: string;
}

export interface ContentListParams {
    keyWord?: string;
    startTime?: number;
    endTime?: number;
    type?: string;
    currentPage: number;
    pageSize: number;
}

export interface ImportContentParams {
    autoImport?: boolean;
    contents: BaijiahaoFileInfo[];
}

export interface BaijiahaoWhiteStatus {
    /** 是否为内部垂类用户 */
    innerUser: boolean;
}

export default {
    // 查询用户是否授权过百家号
    getBaijiahaoAuthorized: createInterface<void, boolean>('GET', '/dataset/baijiahao/authorized'),
    // 百家号授权
    accessBaijiahao: createInterface<void, void>('POST', '/dataset/baijiahao/access'),
    // 百家号解除授权
    cancelBaijiahaoAccess: createInterface<void, void>('POST', '/dataset/baijiahao/access/cancel'),
    // 查询用户各平台授权详情
    getBaijiahaoAuthList: async () => {
        const authList: BaijiahaoPlatformInfo[] = await request('GET', '/dataset/baijiahao/auth/list');
        return authList.filter(item => (item.platform as string) !== 'yy');
    },
    // 查询用户自媒体内容列表
    getBaijiahaoContentList: createInterface<ContentListParams, BaijiahaoFileInfo[]>(
        'GET',
        '/dataset/baijiahao/content/list',
        {timeout: 30000}
    ),
    // 自媒体动态一键导入文心智能体
    importBaijiahaoContent: createInterface<
        ImportContentParams,
        {
            taskId: string;
            taskFile: number;
            taskSize: number;
            taskType: BaijiahaoImportType;
        }
    >('POST', '/dataset/baijiahao/import', {timeout: 60 * 1000}),
    // 自媒体内容自动导入开关
    changeBaijiahaoAutoImportConfig: createInterface<{auto: boolean; datasetId: string}, boolean>(
        'POST',
        '/dataset/baijiahao/import/auto'
    ),
    // 查询导入任务进度
    getBaijiahaoProcess: createInterface<void, BaijiahaoProcess>('GET', '/dataset/baijiahao/import/process'),
    // 清除现有任务
    cancelBaijiahaoImportTask: createInterface<{taskId: string}, boolean>('DELETE', '/dataset/baijiahao/import/cancel'),
    // 绑定知识库与网盘导入任务
    bindBaijiahaoImportTask: createInterface<{taskId: string; datasetId: string}, void>(
        'POST',
        '/dataset/baijiahao/task/bind'
    ),
    // 白名单用户查询
    getBaijiahaoWhiteStatus: createInterface<void, BaijiahaoWhiteStatus>('GET', '/dataset/api/whiteUser'),
};
