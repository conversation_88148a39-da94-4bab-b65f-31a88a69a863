import nth from 'lodash/nth';
import {WangpanAuthRedirectUri} from '@/modules/dataset/constant';
import {request} from '../request';

export enum WangpanVipType {
    NORMAL_USER = 0,
    ORDINARY_MEMBER = 1,
    SUPER_MEMBER = 2,
}

export interface WangpanUserInfo {
    avatarUrl: string;
    baiduName: string;
    netdiskName: string;
    vipType: WangpanVipType;
}

/** 查询用户网盘基本信息 */
export const getWangpanUser = async (): Promise<WangpanUserInfo | null> => {
    try {
        return await request('GET', '/dataset/wangpan/info', undefined, {forbiddenToast: true});
    } catch (error) {
        return null;
    }
};

/** 用户授权后透传授权码Code */
export const getWangpanAuthWithCode = (code: string): Promise<void> =>
    request('GET', `/dataset/wangpan/code?code=${code}&redirectUri=${encodeURIComponent(WangpanAuthRedirectUri)}`);

/** 1 视频、2 音频、3 图片、4 文档、5 应用、6 其他、7 种子 */
enum CategoryType {
    VIDEO = 1,
    AUDIO = 2,
    PICTURE = 3,
    DOCUMENT = 4,
    APPLICATION = 5,
    OTHER = 6,
    SEED = 7,
}
export interface WangpanFile {
    category: CategoryType;
    /** 文件在网盘唯一标识 */
    fsId: string;
    /** 0为否，1为是 */
    isDir: 0 | 1;
    /** 创建时间（时间戳） */
    createTime: number;
    /** 更新时间（时间戳） */
    updateTime: number;
    /** 文件路径 */
    path: string;
    /** 文件名 */
    fileName: string;
    /** 文件大小，单位B */
    size: number;
    /** 缩略图地址（只有请求参数web=1且该条目分类为图片时，该字段才存在） */
    thumbs: {
        /**  */
        url1: string;
    };
    /** 类型是否支持导入 */
    canImport: boolean;
    /** 是否为叶子节点 */
    isLeaf: boolean;
    /** 支持Tree组件名称 */
    title: string;
    /** 支持Tree组件key */
    key: string;
    /** 支持Tree组件禁用 */
    disabled: boolean;
}

export const resolveWangpanFile = (file: WangpanFile): WangpanFile => {
    const ext = (nth(file.fileName.split('.'), -1) || '').toLowerCase();
    if (file.isDir === 1) {
        file.disabled = false;
    } else if (file.canImport === false) {
        file.disabled = true;
    } else if (['txt', 'md', 'docx', 'pdf', 'xlsx', 'csv', 'm4a', 'mp3'].includes(ext)) {
        file.disabled = file.size > 50000000;
    } else if (['png', 'jpg', 'jpeg'].includes(ext)) {
        file.disabled = file.size > 20000000;
    } else if (['mp4', 'avi', 'mov'].includes(ext)) {
        file.disabled = file.size > 200000000;
    } else {
        file.disabled = true;
    }

    file.title = file.fileName;
    file.fsId = file.fsId + '';
    file.key = file.fsId;
    file.isLeaf = file.isDir === 0;
    return file;
};

/** 查询用户网盘文件列表 */
export const getWangpanList = async (payload: {
    path: string;
    start?: number;
    limit?: number;
    web?: 0 | 1;
}): Promise<WangpanFile[]> => {
    let start = payload.start || 0;
    const limit = payload.limit || 1000;
    const dataList: WangpanFile[] = [];

    /** 递归查询 */
    const getList = async (): Promise<void> => {
        const res = await request(
            'GET',
            `/dataset/wangpan/list?path=${encodeURIComponent(payload.path)}&start=${start}&limit=${limit}&web=${
                payload.web || 0
            }`,
            {forbiddenToast: true}
        );

        /** 兼容空文件夹 */
        (res.dataList || []).forEach((file: WangpanFile) => {
            dataList.push(resolveWangpanFile(file));
        });
        if (res.hasMore === 1) {
            start += limit;
            await getList();
        }
    };

    await getList();
    return dataList;
};

/** 网盘文件搜索接口 */
export const searchWangpanList = (payload: {path: string; key?: string; web?: 0 | 1}): Promise<WangpanFile[]> =>
    request('GET', `/dataset/wangpan/search?path=${payload.path}&key=${payload.key || ''}&web=${payload.web || 0}`);

/** 任务类型，1: 同步 2: 异步 */
export enum WangpanImportType {
    /** 同步导入 */
    SYNC = 1,
    /** 异步导入 */
    ASYNC = 2,
}

/** 网盘文件一键导入文心智能体 */
export const importWangpanFile = (
    fsIds: string[]
): Promise<{
    taskId: string;
    taskFile: number;
    taskSize: number;
    taskType: WangpanImportType;
}> => request('POST', '/dataset/wangpan/import', {fsIds}, {timeout: 60 * 1000});

/** 导入任务状态，-1：失败，0：导入中， 1：成功 */
export enum WangpanStatus {
    SUCCESS = 1,
    FAIL = -1,
    IN_PROGRESS = 0,
}

export enum WangpanFileStatus {
    DOWNLOAD = 1,
    SUCCESS = 2,
    FAIL = 3,
}

export interface WangpanProgress {
    /** 导入任务ID */
    taskId: string;
    status: WangpanStatus;
    /** 导入文件数 */
    taskFile: number;
    /** 导入文件总大小 */
    taskSize: number;
    /** 已导入文件大小，100%时为null */
    curFileSize: number | null;
    /** 导入耗时（毫秒） */
    cost: number;
    /** 剩余待导入文件数，为0时导入完成 */
    remainFile: number;
    /** 已导入的文件信息 */
    importFiles: Array<{
        fileId: string | null;
        fileName: string;
        fileStatus: WangpanFileStatus;
    }>;
    /** 绑定的知识库ID */
    datasetId?: string;
}

/** 查询用户下导入任务进度 */
export const getWangpanProgress = (): Promise<WangpanProgress | null> =>
    request('GET', '/dataset/wangpan/import/process');

/** 网盘解除授权 */
export const cancelWangpanAuth = (): Promise<void> => request('GET', '/dataset/wangpan/access/cancel');

/** 清除上传任务 */
export const cancelWangpanTask = (taskId: string): Promise<void> =>
    request('DELETE', '/dataset/wangpan/import/cancel', {taskId});

/** 绑定上传任务 */
export const bindWangpanTaskToDataset = (datasetId: string): Promise<void> =>
    request('POST', '/dataset/wangpan/task/bind', {datasetId});
