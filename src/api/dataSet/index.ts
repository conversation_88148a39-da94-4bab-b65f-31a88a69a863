import {AxiosProgressEvent} from 'axios';
import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {FileClassification, FileClassificationMap, fileTypeClassificationMap} from '@/modules/dataset/interface';
import {request, createInterface} from '../request';
import {ErrorResponseCode} from '../error';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_SPLIT_TEXT_MAX_SIZE,
    DataSetStatus,
    DataSetDetail,
    DataSetList,
    DataSetOutline,
    FileInfo,
    FileDetail,
    DynamicParams,
    CreateDataSetParams,
    GenerateParams,
    PreviewTextParams,
    UpdateDataSetParams,
    AddFilesParams,
    AllDatasetInfo,
    AllFileList,
    EntityNames,
    DatasetFileList,
    DatasetFileListParams,
    ParagraphListParam,
    ParagraphList,
    ParagraphEditParam,
    ParagraphDeleteParam,
    ParagraphPreviewEditParam,
    ParagraphPreviewDeleteParam,
    ParagraphConfirmInfo,
    GenerateAgentEmbeddingParams,
    PreviewMediaParams,
    MediaInfo,
    MediaInfoParam,
    FileTypeDetail,
    WebsiteCrawlInfo,
    CommitWebsiteTitleParams,
    FileType,
    ProcessTextDetail,
    ProcessTableDetail,
    PreviewTableParams,
    ProcessMediaDetail,
    TableHeaderParams,
    TableHeader,
    TableEditParam,
    OrderByUpdateTime,
    ParagraphInfo,
} from './interface';

const MAX_TIME_OUT = 1000 * 30;
const UPLOAD_FILE_TIME_OUT = 1000 * 360;
const NO_FILE = '文件不存在';
const NO_DATA_SET = '知识库不存在';
const EMPTY_DATASET_LIST: DataSetList = {
    total: 0,
    pageNo: 0,
    pageSize: DEFAULT_PAGE_SIZE,
    dataList: [],
};
export const GET_ALL_STATUS_DATASET_API = '/dataset/all-status';

export enum DatasetListStatus {
    ALL = '',
    WAITING = 1,
    PROCESSING = 2,
    FINISHED = 3,
    FAILED = 4,
}

export const getDatasetList = (params: {
    name: string;
    pageSize: number;
    pageNo: number;
    status: DatasetListStatus;
}): Promise<DataSetList> =>
    request(
        'GET',
        '/dataset/list',
        params,
        // 该接口在用户关联过多知识库时查询很慢，故增加超时时间
        {forbiddenToast: true, timeout: 1000 * 40}
    ).then(data => {
        // 兼容请求成功，但 server 返回 data 为 null，造成页面渲染错误的问题。兼容后知识库列表页预期展示「无数据」场景
        if (!data) {
            return EMPTY_DATASET_LIST;
        }

        data.list = data.dataList;
        return data;
    });

export const queryDatasetAkSk = (
    datasetId: string
): Promise<{
    ak: string;
    sk: string;
}> => request('GET', `/dataset/api/queryAkSK?datasetId=${datasetId}`);

// 查询知识库包含的文件类型
export const getDatasetFileTypes = (id: number | string): Promise<string[]> =>
    request('GET', `/dataset/file/type/${id}`);

// 知识库文件查询，支持模糊搜索和分页
export const getDatasetFile = createInterface<DatasetFileListParams, DatasetFileList>('POST', '/dataset/file/list');

// 获取知识库列表，分页
export const getDatasetParagraph = (params: ParagraphListParam): Promise<ParagraphList> =>
    request('POST', '/dataset/text/list', params);

export const getSingleParagraph = async (datasetId: string, textId: string, fileId: string): Promise<ParagraphInfo> => {
    const res = await request('POST', '/dataset/text/list', {
        datasetId,
        pageSize: 1,
        pageNo: 1,
        fileList: [fileId],
        textId,
    });
    return res.dataList[0];
};

export default {
    // 上传文件
    uploadFile: ({
        file,
        onUploadProgress,
    }: {
        file: File;
        onUploadProgress: (e: AxiosProgressEvent) => void;
    }): Promise<FileInfo> => {
        return request(
            'POST',
            SECURE_URL_ENUM.DatasetFileUpload,
            {
                file,
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                forbiddenToast: true,
                timeout: UPLOAD_FILE_TIME_OUT,
                onUploadProgress: onUploadProgress,
            }
        );
    },
    // 删除文件
    deleteFile: ({id, isAllowChange}: DynamicParams & {isAllowChange: boolean}): Promise<any> => {
        return request('DELETE', `/dataset/file/delete/${id}`, {isAllowChange});
    },
    // 套件内获取知识库信息接口（不分页)
    getAllDataSet: ({status = DataSetStatus.done}): Promise<DataSetOutline[]> => {
        return request(
            'GET',
            '/dataset/all',
            {
                status,
            },
            {forbiddenToast: true}
        );
    },

    // 查询所有可用知识库
    getAllDatasetInfo: createInterface<void, AllDatasetInfo[]>('GET', '/dataset/all/available'),

    // 查询所有知识库，包括可用和不可用（处理失败、处理中）
    getAllStatusDataset: (orderByUpdateTime?: OrderByUpdateTime): Promise<AllDatasetInfo[]> =>
        request('GET', GET_ALL_STATUS_DATASET_API, {orderByUpdateTime}),

    // 获取关联的实体名
    getRelatedEntity: ({id}: DynamicParams): Promise<EntityNames> => {
        return request('GET', `/dataset/related/${id}`, {}, {timeout: MAX_TIME_OUT});
    },
    // 获取知识库详情
    getDataSetDetail: ({id}: DynamicParams): Promise<DataSetDetail> => {
        return request('GET', `/dataset/detail/${id}`, {}, {forbiddenToast: true}).then(data => {
            // 兼容请求成功，但 server 返回 data 为 null，造成页面渲染错误的问题。知识库详情页预期展示「数据不存在」场景
            if (!data) {
                // eslint-disable-next-line prefer-promise-reject-errors
                return Promise.reject({
                    errno: ErrorResponseCode.NoData,
                    msg: NO_DATA_SET,
                });
            }
            return data;
        });
    },
    // 查询所有文件列表
    getAllFiles: createInterface<{datasetId: string}, AllFileList>('GET', '/dataset/file/all'),
    // 创建知识库
    createDataSet: ({datasetName, datasetDescription, fileList}: CreateDataSetParams): Promise<AllDatasetInfo> => {
        return request(
            'POST',
            SECURE_URL_ENUM.DatasetCreate,
            {
                datasetName,
                datasetDescription,
                fileList,
            },
            {timeout: MAX_TIME_OUT}
        );
    },
    // 增加知识库文件
    addDataSetFiles: ({id, fileList}: AddFilesParams): Promise<string> => {
        return request(
            'POST',
            `/dataset/addfiles/${id}`,
            {
                fileList,
            },
            {timeout: MAX_TIME_OUT}
        );
    },
    // 修改知识库的基础信息
    updateDataSetBasicInfo: ({id, datasetName, datasetDescription}: UpdateDataSetParams): Promise<string> => {
        return request('POST', `/dataset/update/${id}`, {
            datasetName,
            datasetDescription,
        });
    },
    // 删除知识库
    deleteDataSet: ({id}: DynamicParams): Promise<void> => {
        return request('DELETE', `/dataset/delete/${id}`);
    },
    // 获取表头行表头详情
    getTableHeader: createInterface<TableHeaderParams, TableHeader[]>('GET', '/dataset/text/preview/table/header'),
    // 获取文件的分段信息进行预览
    getPreviewText: ({
        datasetId,
        fileList,
        splitType,
        splitParams,
        maxSize = DEFAULT_SPLIT_TEXT_MAX_SIZE,
    }: PreviewTextParams): Promise<ProcessTextDetail[]> => {
        return request(
            'POST',
            '/dataset/text/preview',
            {
                datasetId,
                fileList,
                splitType,
                splitParams,
                maxSize,
            },
            {
                timeout: MAX_TIME_OUT,
            }
        );
    },
    // 获取多媒体文件的分段信息进行预览
    getPreviewMedia: ({
        maxSize = DEFAULT_SPLIT_TEXT_MAX_SIZE,
        ...props
    }: PreviewMediaParams): Promise<ProcessMediaDetail[]> => {
        return request(
            'POST',
            '/dataset/media/preview',
            {
                ...props,
                maxSize,
            },
            {
                timeout: MAX_TIME_OUT,
            }
        );
    },
    // 获取文件的分段信息进行预览
    getPreviewTable: ({
        datasetId,
        fileList,
        maxSize = DEFAULT_SPLIT_TEXT_MAX_SIZE,
    }: PreviewTableParams): Promise<ProcessTableDetail[]> => {
        return request(
            'POST',
            '/dataset/text/preview/table',
            {
                datasetId,
                fileList,
                maxSize,
            },
            {
                timeout: MAX_TIME_OUT,
            }
        );
    },
    // 预览时编辑图片
    editPreviewImage: createInterface<ParagraphPreviewEditParam, void>('POST', '/dataset/media/preview/edit'),
    // 预览时删除图片
    deletePreviewImage: createInterface<ParagraphPreviewDeleteParam, void>('DELETE', '/dataset/media/preview/delete'),
    // 生成向量
    generateEmbedding: createInterface<GenerateParams, Promise<void>>('POST', SECURE_URL_ENUM.DatasetSubmit, {
        timeout: MAX_TIME_OUT,
    }),
    // 获取文件详情，包含分段信息
    getFileDetail: ({id}: DynamicParams): Promise<FileDetail> => {
        return request('GET', `/dataset/file/detail/${id}`, {}, {forbiddenToast: true, timeout: MAX_TIME_OUT}).then(
            data => {
                // 兼容请求成功，但 server 返回 data 为 null，造成页面渲染错误的问题。修复后文件详情页预期展示「数据不存在」场景
                if (!data) {
                    // eslint-disable-next-line prefer-promise-reject-errors
                    return Promise.reject({
                        errno: ErrorResponseCode.NoData,
                        msg: NO_FILE,
                    });
                }
                return data;
            }
        );
    },
    // 编辑段落
    editParagraph: createInterface<ParagraphEditParam, void>('POST', SECURE_URL_ENUM.DatasetTextEdit),
    // 编辑段落
    editTable: createInterface<TableEditParam, void>('POST', '/dataset/text/edit/table'),
    // 删除段落
    deleteParagraph: createInterface<ParagraphDeleteParam, void>('DELETE', '/dataset/text/delete'),
    // 分段预览时获取会话ID
    getSession: createInterface<void, {sessionId: string}>('GET', '/dataset/session'),
    // 预览时编辑段落
    editPreviewParagraph: createInterface<ParagraphPreviewEditParam, void>('POST', '/dataset/text/preview/edit'),
    // 预览时删除段落
    deletePreviewParagraph: createInterface<ParagraphPreviewDeleteParam, void>(
        'DELETE',
        '/dataset/text/preview/delete'
    ),
    // 编辑前提醒接口（同时返回所有关联实体）
    getRemindInfo: ({id}: DynamicParams): Promise<ParagraphConfirmInfo> => {
        return request('GET', `/dataset/edit/remind/${id}`, {}, {forbiddenToast: true, timeout: MAX_TIME_OUT});
    },
    // 跳过编辑前提醒 (/dataset/edit/remind/{id} 中返回)
    skipRemind: createInterface<void, void>('POST', '/dataset/edit/remind/skip'),
    generateAgentEmbedding: createInterface<GenerateAgentEmbeddingParams, AllDatasetInfo>(
        'POST',
        '/dataset/file/agent/embedding',
        {
            timeout: MAX_TIME_OUT,
        }
    ),
    // 滚动获取图片列表
    getMediaScroll: createInterface<MediaInfoParam, MediaInfo[]>('POST', '/dataset/media/scroll'),
    // 编辑图片标注
    editImageLabel: createInterface<{datasetId: string; fileId: string; label: string; fileType: FileType}, void>(
        'POST',
        '/dataset/media/label'
    ),
    // 切换音频模型
    switchAudioModal: createInterface<{datasetId: string; fileId: string}, void>('POST', '/dataset/media/switch'),
    // 批量查询文件详情
    getFileListType: (params: {fileList: string[]}): Promise<FileClassificationMap> => {
        return request('POST', `/dataset/file/info/batch`, params).then(
            (data: FileTypeDetail[]) => {
                const res: Record<FileClassification, FileTypeDetail[]> = {
                    audio: [],
                    video: [],
                    picture: [],
                    text: [],
                    table: [],
                };
                data.forEach(item => {
                    res[fileTypeClassificationMap[item.type]].push(item);
                });
                return res;
            },
            () => ({audio: [], video: [], picture: [], text: [], table: []})
        );
    },
    // 抓取网页内容
    getWebsiteDetail: createInterface<{url: string}, WebsiteCrawlInfo>('POST', SECURE_URL_ENUM.DatasetUrlCrawl, {
        forbiddenToast: true,
        timeout: 1000 * 45, // 该接口在需要抓取的图片较多时，由于每张图片需要经过风控，耗时较长，设置超时时间 45s
    }),
    // 修改网页标题，并提交配置
    commitWebsiteTitle: createInterface<CommitWebsiteTitleParams, void>('POST', '/dataset/url/title'),
    // 获取知识库配容以及余额
    getQuota: createInterface<{datasetId: string}, {total: number; remain: number}>('GET', '/dataset/quota', {
        forbiddenToast: true,
    }),
    // 安全提示接口
    safeRemind: createInterface<void, {remind: boolean}>('GET', '/dataset/safe/remind'),
};
