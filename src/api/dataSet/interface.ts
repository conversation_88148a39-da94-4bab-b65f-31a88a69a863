import {AppId} from '@/api/appList/interface';
import {AuditStatus} from '@/modules/agentList/interface';
import {AgentData} from '@/modules/agentList/interface';

// 默认的页面大小
export const DEFAULT_PAGE_SIZE = 20;

// 预览分段时，默认获取的最大分段数目
export const DEFAULT_SPLIT_TEXT_MAX_SIZE = 1000;

export enum SplitType {
    default = 0, // 默认分段
    custom = 1, // 自定义分段
}

/** 所有状态的知识库数据按照更新时间排序：不传/0-顺序; 1-倒序 */
export enum OrderByUpdateTime {
    Ascending = 0,
    Descending = 1,
}

export enum DataSetStatus {
    waiting = 1,
    doing = 2,
    done = 3,
    failed = 4,
    auditing = 5,
    shielded = 6,
}

export const enum FileSource {
    local = 0,
    website = 1,
    cloudDisk = 2,
    baijiahao = 3,
    api = 4,
}

export const FILE_SOURCE_TEXT = {
    [FileSource.local]: '本地上传',
    [FileSource.website]: '网址提交',
    [FileSource.cloudDisk]: '网盘导入',
    [FileSource.baijiahao]: '自媒体平台',
    [FileSource.api]: 'API提交',
};

export const DATASET_STATUS_TEXTS = {
    [DataSetStatus.waiting]: '待处理',
    [DataSetStatus.doing]: '处理中',
    [DataSetStatus.done]: '已可用',
    [DataSetStatus.failed]: '处理失败',
    [DataSetStatus.auditing]: '审核中',
    [DataSetStatus.shielded]: '已屏蔽',
};

export enum FileStatus {
    waiting = 1,
    doing = 2,
    done = 3,
    failed = 4,
    auditing = 5,
    shielded = 6,
}

export const STATUS_DOT_COLOR = {
    [DataSetStatus.failed]: 'bg-error',
    [DataSetStatus.waiting]: 'bg-warning',
    [DataSetStatus.doing]: 'bg-info',
    [DataSetStatus.done]: 'bg-success',
    [DataSetStatus.auditing]: 'bg-warning',
    [DataSetStatus.shielded]: 'bg-gray-tertiary',
};

export const FILE_STATUS_TEXTS = {
    [FileStatus.waiting]: '待处理',
    [FileStatus.doing]: '处理中',
    [FileStatus.done]: '已可用',
    [FileStatus.failed]: '处理失败',
    [FileStatus.auditing]: '审核中',
    [FileStatus.shielded]: '已屏蔽',
};

export type DataSetId = string;

export type FileId = string;

export interface ProcessTextDetail {
    // 分段编号（对于图片只为1）
    number: number;
    // 知识库文件名称
    fileName: string;
    // 字符数目（对图片类型为标注字符数）
    characterNumber: number;
    // 分段内容
    content: string;
    // 文件ID
    fileId: string;
    // 段落在文件中序号（对于图片只为1）
    textFileNumber: string;
    // 段落唯一ID
    textUniqueId: string;
    // 网页链接（网页类型才有）
    webUrl?: string;
    // 段落 ID
    id?: string;
}

// 多媒体预览详情（视频、音频、图片）
export interface ProcessMediaDetail extends ProcessTextDetail {
    type: FileType.audio | FileType.video | FileType.picture;
    // 图片链接（图片类型才有）
    mediaUrl: string;
    // 视频缩略图
    icon: string;
    // 标注状态（需要智能标注的多媒体类型才有）
    status: LableStatus;
}

export interface RowType {
    number: number;
    cells: Array<{cellUniqueId: string; content: string}>;
}

// 表格预览详情
export interface ProcessTableDetail {
    // 文件ID
    fileId: string;
    // 知识库文件名称
    fileName: string;
    // 列数
    columnNum: number;
    // 表头
    headers: string[];
    // 表格内容列表
    rows: RowType[];
    // 表头列
    indexNumber?: number;
    // 序号 ID ，仅知识库预览编辑页存在，预览列表透传 textUniqueId
    id?: string;
}

export type ProcessDetail = ProcessTextDetail | ProcessMediaDetail | ProcessTableDetail;

export interface ParagraphDetail {
    // 分段编号 / 段落唯一ID
    id: string;
    // 知识库文件名称
    fileName: string;
    // 字符数目
    characterNumber: number;
    // 分段内容
    content: string;
    // 文件ID
    fileId: string;
    // 段落序号
    index: number;
    // 段落处理状态
    status?: ParagraphStatus;
    // 处理失败原因
    failMsg?: string;
    // 更新时间
    updateTime?: number;
    // 段落在文件中序号（对于图片只为1）
    textFileNumber?: string;
    // 图片链接（图片类型才有）
    image?: string;
    // 图片链接（图片类型才有）
    webUrl?: string;
    // 是否为预览状态
    isPreview: boolean;
    // 是否为图片
    isImage: boolean;
    // 标注状态
    labelStatus?: LableStatus;
}

/**
 * 自定义分段时的属性
 */
export interface SplitParams {
    /**
     * 分隔符号
     */
    separators?: string;
    /**
     * 两段字符串重叠的最大字符数量
     */
    chunkOverlap: number;
    /**
     * 文本分段的最大字符数
     */
    chunkSize: number;
}

export interface FileInfo {
    fileId: FileId;
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
    fileCharacter: number;
}

/**
 * 文件梗概信息
 */
export interface FileDetail {
    // 知识库 ID
    datasetId: DataSetId;

    // 知识库文件 id 标识
    fileId: FileId;

    // 文件名称
    fileName: string;

    // 文件大小
    fileSize: number;

    // 文件包含的分段计算方式
    splitType: number;

    // 文件包含的分段数量
    splitTextNumber: number;

    splitParams?: SplitParams;

    // 知识库状态
    status: number;

    failMsg?: string;

    textList?: ProcessDetail[];
}

/**
 * 文件类型
 */
export enum FileDisplayType {
    text = 1,
    picture = 2,
    table = 3,
    audio = 4,
    video = 5,
}

/**
 * 文件梗概信息
 */
export interface FileOutline {
    // 知识库 ID
    datasetId: DataSetId;
    // 知识库文件 id 标识
    fileId: FileId;
    // 文件名称
    fileName: string;
    // 文件大小
    totalSize: number;
    // 文件包含的分段计算方式
    splitType: string;
    // 文件包含的分段数量
    splitTextNumber: number;
    splitParams: SplitParams;
    createTime: number;
    updateTime: number;
    // 文件状态
    status: FileStatus;
    // 文件处理失败的原因
    failMsg?: string;
    // 文档总分段数（图片类型无）
    totalText?: number;
    // 文档总字符数
    totalCharacter: string;
    // 进度
    process?: number;
    // 图片链接（图片类型才有）
    image?: string;
    // 音频链接（音频类型才有）
    audio?: string;
    // 视频链接（视频类型才有）
    video?: string;
    // 网页链接（网页类型才有）
    webUrl?: string;
    // 文件来源
    source: FileSource;
    // 文件类型
    type?: FileType;
    // 文件后缀
    fileType?: string;
    // 文件分类
    displayType?: FileDisplayType;
}

/**
 * 知识库接口的请求参数
 */
export interface DataSetListParams {
    // 页码
    pageNo: number;
    pageSize: number;
}

// 【更新】创建智能体(同知识库列表)，返回的知识库，（返回数据新增sourceType=100代表是教师预制且不支持删除和查看）
export enum SourceType {
    TeacherRecruitAgent = 100,
}

/**
 * 知识库信息
 */
export interface DataSetOutline {
    // 知识库 id 标识
    datasetId: DataSetId;

    // 知识库名称
    datasetName: string;

    datasetTable?: string;

    // 知识库描述
    datasetDescription: string;

    // 知识库包含的字符数量
    totalCharacter: number;

    // 知识库包含的文件数量
    fileNumber: number;

    // 知识库关联的应用数目
    appNumber: number;

    /**
     * 知识库状态
     *
     */
    status: DataSetStatus;

    /**
     * 知识库是否支持修改（更新/删除）
     * 关联应用中包含上线/审核状态 APP 时, 即不可修改。
     */
    isAllowChange: number;

    // 是否为 api 创建的知识库
    isApi: boolean;
    // api 知识库是否只读
    isReadOnly: boolean;

    // 知识库总分段数
    totalText: string;

    // 进度
    process?: number;

    // 处理失败原因
    failMsg: string;

    // 文件大小
    totalSize: number;

    // 更新时间
    updateTime: number;

    // 是否存在 akSk
    hasAkSk?: boolean;

    // 知识库来源
    sourceType?: SourceType;
}

/**
 * 知识库列表返回
 */
export interface DataSetList {
    total: number;
    pageSize: number;
    pageNo: number;
    dataList: DataSetOutline[];
}

export interface AppBriefInfo {
    appId: AppId;
    appName: string;
    appStatus: AuditStatus;
}

export interface AgentInfo {
    agentId: string;
    agentName: string;
    agentStatus: AgentData['status'];
}

/**
 * 知识库详情信息
 */
export interface DataSetDetail {
    // 知识库 id 标识
    datasetId: DataSetId;

    // 知识库名称
    datasetName: string;

    // 知识库描述
    datasetDescription: string;

    // 知识库包含的字符数量
    totalCharacter: number;

    // 知识库关联的应用数目
    datasetAppInfoList: AppBriefInfo[];

    /**
     * 知识库状态
     *
     */
    status: number;

    /**
     * 知识库是否支持修改（更新/删除）
     * 关联应用中包含上线/审核状态 APP 时, 即不可修改。
     */
    isAllowChange: boolean;

    // 是否为 api 创建的知识库
    isApi: boolean;
    // api 知识库是否只读
    isReadOnly: boolean;

    fileList: FileOutline[];
    agentList: AgentInfo[];

    // 是否自动导入百家号
    autoImportBjh?: boolean;

    // 是否存在 akSk
    hasAkSk?: boolean;
}

/**
 * 所有知识库列表项
 */
export interface AllDatasetInfo {
    // 知识库 id 标识
    datasetId: DataSetId;

    // 知识库名称
    datasetName: string;

    // 知识库描述
    datasetDescription: string;

    // 知识库文档数
    fileNumber: number;

    /**
     * 知识库状态
     *
     */
    status: DataSetStatus;

    // 知识库包含的字符数量
    totalCharacter: number;

    // 知识库表名
    datasetTable: string;

    // 处理中知识库处理进度，0-100
    process: number;

    // 处理失败的知识库返回失败原因
    failMessage: string;

    // 是否自动导入百家号
    autoImportBjh?: boolean;

    // 来源类型
    sourceType: SourceType;
}

/**
 * 文件简略信息
 */
export interface FileSimpleInfo {
    // 知识库文件 id 标识
    fileId: FileId;
    // 文件名称
    fileName: string;
    // 文件状态
    status: number;
}

export type DatasetAllFileItem = Required<
    Pick<FileOutline, 'fileId' | 'fileName' | 'type' | 'status' | 'fileType' | 'displayType'>
>;

/**
 * 知识库中所有文件
 */
export interface AllFileList {
    // 文件总数
    total: number;
    // 文件列表
    fileList: DatasetAllFileItem[];
}

export interface Response<T> {
    errno: number;
    msg: string;
    data: T;
}

export interface DynamicParams {
    id: number | string;
}

export interface CreateDataSetParams {
    datasetName: string;
    datasetDescription?: string;
    fileList: string[];
}

export interface AddFilesParams {
    id: string;
    fileList: string[];
}

export interface UpdateDataSetParams {
    id: string;
    datasetName: string;
    datasetDescription: string;
}

interface PreviewParams {
    datasetId: DataSetId;
    maxSize: number;
}
export interface PreviewTextParams extends PreviewParams {
    fileList: string[];
    splitType: SplitType;
    splitParams?: SplitParams;
}

export interface TableHeaderParams {
    datasetId: DataSetId;
    fileId: string;
    headerRow: number;
}

export interface TableHeader {
    number: number;
    headerName: string;
}

export enum LabelModal {
    Chinese = 1,
    English = 2,
}

export interface AudioProcessFormItemValue {
    fileId: string[];
    labelType: LabelModal;
}

export interface FormattedAudioProcessFormItemValue {
    fileId: string;
    labelType: LabelModal;
}

export interface PreviewMediaParams extends PreviewParams {
    audioFileList: Array<{
        fileId: string;
        labelType: LabelModal;
    }>;
    videoFileList: string[];
    imageFileList: string[];
}

export interface TableProcessFormItemValue {
    fileId: string;
    headerRow: number;
    indexHeader: {
        value: number;
        label: string;
    };
}

export interface FormattedTableProcessFormItemValue {
    fileId: string;
    headerRow: number;
    indexHeaderNumber?: number;
    indexHeaderName?: string;
}

export interface PreviewTableParams extends PreviewParams {
    fileList: FormattedTableProcessFormItemValue[];
}

export interface GenerateParams {
    sessionId?: string;
    datasetId: DataSetId;
    fileList: string[];
    splitType: SplitType;
    splitParams?: SplitParams;
    tableHeaders?: FormattedTableProcessFormItemValue[];
    audioModels?: FormattedAudioProcessFormItemValue[];
}

/**
 * 分页查询知识库文件参数
 */
export interface DatasetFileListParams {
    datasetId: DataSetId;
    pageSize: number;
    pageNo: number;
    name?: string;
    typeList?: string[];
    status: number | '';
}

/**
 * 分页查询知识库文件返回值
 */
export interface DatasetFileList {
    total: number;
    pageSize: number;
    pageNo: number;
    dataList: FileOutline[];
}

/**
 * 实体状态
 */
export const enum EntityStatus {
    developing = 'developing',
    auditing = 'auditing',
    online = 'online',
}

export const EntityStatusArray: EntityStatus[] = [EntityStatus.auditing, EntityStatus.developing, EntityStatus.online];

export const ENTITY_STATUS_TEXTS: Record<EntityStatus, string> = {
    [EntityStatus.auditing]: '审核中',
    [EntityStatus.developing]: '开发中',
    [EntityStatus.online]: '线上',
};

/**
 * 实体类型
 */
export const enum EntityTypes {
    plugins = 'plugins',
    agents = 'agents',
    apps = 'apps',
}

export const EntityTypesArray: EntityTypes[] = [EntityTypes.agents, EntityTypes.apps, EntityTypes.plugins];

export const ENTITY_TYPES_TEXTS: Record<EntityTypes, string> = {
    [EntityTypes.agents]: '智能体',
    [EntityTypes.apps]: '应用',
    [EntityTypes.plugins]: '插件',
};

export interface EntityNamesInfo {
    [EntityStatus.auditing]?: string[];
    [EntityStatus.developing]?: string[];
    [EntityStatus.online]?: string[];
}

export interface EntityNames {
    [EntityTypes.agents]?: EntityNamesInfo;
    [EntityTypes.apps]?: EntityNamesInfo;
    [EntityTypes.plugins]?: EntityNamesInfo;
}

/**
 * 段落状态
 */
export const enum ParagraphStatus {
    processing = 1, // 处理中
    available = 2, // 已可用
    shield = 4, // 已屏蔽
}

export const ParagraphStatusArray = [ParagraphStatus.available, ParagraphStatus.processing, ParagraphStatus.shield];

export const ParagraphStatusText = {
    [ParagraphStatus.processing]: '处理中',
    [ParagraphStatus.available]: '已可用',
    [ParagraphStatus.shield]: '已屏蔽',
};

export const enum FilterRange {
    CurrentFile = '当前文件',
    AllFiles = '全部文件',
}

export type TableType = Array<Record<string, string>>;

interface BaseParagraphInfo {
    // 段落唯一ID
    id: string;
    // 段落序号
    number: number;
    // 段落内容
    text: string;
    // 段落状态
    status: ParagraphStatus;
    // 处理失败原因
    failMsg?: string;
    // 字符数（图片类型此项为null）
    character: number | null;
    // 更新时间
    updateTime: number;
    // 文件ID
    fileId: string;
    // 文件名
    fileName: string;
    // 图片链接（图片类型才有）
    image: null | string;
    // 网页链接（网址类型才有）
    webUrl: null | string;
    // 音频链接（音频类型才有）
    audio: null | string;
    // 音频模型（音频类型才有）
    audioModel: LabelModal | null;
    // 视频链接（视频类型才有）
    video: null | string;
    // 视频缩略图（视频类型才有）
    videoPreview: null | string;
    // 表格内容（表格文件才有，历史数据没有，取text字段）
    table: TableType | null;
    // 表格索引列序号（表格文件+列索引才有，历史数据没有）
    tableIndexNumber: number | null;
    // 段落命中次数
    hitCount: number | null;
}

export interface TextParagraphInfo extends BaseParagraphInfo {
    // 字符数
    character: number;
    // 图片链接（图片类型才有）
    image: null;
    // 网页链接（网址类型才有）
    webUrl: null;
    // 音频链接（音频类型才有）
    audio: null;
    // 音频模型（音频类型才有）
    audioModel: null;
    // 视频链接（视频类型才有）
    video: null;
    // 视频缩略图（视频类型才有）
    videoPreview: null;
    // 表格内容
    table: null;
    // 表格索引列序号
    tableIndexNumber: null;
}

export interface PictureParagraphInfo extends BaseParagraphInfo {
    // 图片链接（图片类型才有）
    image: string;
    // 字符数
    character: null;
    // 网页链接（网址类型才有）
    webUrl: null;
    // 音频链接（音频类型才有）
    audio: null;
    // 音频模型（音频类型才有）
    audioModel: null;
    // 视频链接（视频类型才有）
    video: null;
    // 视频缩略图（视频类型才有）
    videoPreview: null;
    // 表格内容
    table: null;
    // 表格索引列序号
    tableIndexNumber: null;
}

export interface WebTextParagraphInfo extends BaseParagraphInfo {
    // 字符数
    character: number;
    // 网页链接（网址类型才有）
    webUrl: string;
    // 图片链接（图片类型才有）
    image: null;
    // 音频链接（音频类型才有）
    audio: null;
    // 音频模型（音频类型才有）
    audioModel: LabelModal;
    // 视频链接（视频类型才有）
    video: null;
    // 视频缩略图（视频类型才有）
    videoPreview: null;
    // 表格内容
    table: null;
    // 表格索引列序号
    tableIndexNumber: null;
}

export interface AudioParagraphInfo extends BaseParagraphInfo {
    // 字符数
    character: number;
    // 音频链接（音频类型才有）
    audio: string;
    // 音频模型（音频类型才有）
    audioModel: null;
    // 图片链接（图片类型才有）
    image: null;
    // 网页链接（网址类型才有）
    webUrl: null;
    // 视频链接（视频类型才有）
    video: null;
    // 视频缩略图（视频类型才有）
    videoPreview: null;
    // 表格内容
    table: null;
    // 表格索引列序号
    tableIndexNumber: null;
}

export interface VideoParagraphInfo extends BaseParagraphInfo {
    // 字符数
    character: number;
    // 视频链接（视频类型才有）
    video: string;
    // 视频缩略图（视频类型才有）
    videoPreview: string;
    // 图片链接（图片类型才有）
    image: null;
    // 网页链接（网址类型才有）
    webUrl: null;
    // 音频链接（音频类型才有）
    audio: null;
    // 表格内容
    table: null;
    // 表格索引列序号
    tableIndexNumber: null;
}

export interface TableParagraphInfo extends BaseParagraphInfo {
    // 字符数
    character: number;
    // 视频链接（视频类型才有）
    video: null;
    // 视频缩略图（视频类型才有）
    videoPreview: null;
    // 图片链接（图片类型才有）
    image: null;
    // 网页链接（网址类型才有）
    webUrl: null;
    // 音频链接（音频类型才有）
    audio: null;
    // 表格内容
    table: Array<Record<string, string>>;
    // 表格索引列序号
    tableIndexNumber: number;
}

/**
 * 段落信息
 */
export type ParagraphInfo =
    | TextParagraphInfo
    | PictureParagraphInfo
    | WebTextParagraphInfo
    | AudioParagraphInfo
    | VideoParagraphInfo
    | TableParagraphInfo;

/**
 * 段落查询参数
 */
export interface ParagraphListParam {
    datasetId: string;
    pageSize: number;
    pageNo: number;
    statusList?: ParagraphStatus[];
    fileList?: string[];
    content?: string;
    textTypeList?: FileType[];
    textId?: string;
}

/**
 * 段落列表
 */
export interface ParagraphList {
    pageSize: number;
    pageNo: number;
    total: number;
    dataList: ParagraphInfo[];
}

/**
 * 段落编辑参数
 */
export interface ParagraphEditParam {
    datasetId: string;
    fileId: string;
    textId?: string;
    content: string;
}

export interface TableEditParam {
    datasetId: string;
    fileId: string;
    textId?: string;
    tableRow: TableType;
}

/**
 * 段落删除参数
 */
export interface ParagraphDeleteParam {
    datasetId: string;
    fileId: string;
    textId: string;
    isAllowChange: boolean;
}

/**
 * 段落编辑，预览时需携带的额外参数
 */
export interface ParagraphPreviewEditParam {
    sessionId: string;
    datasetId: string;
    fileId: string;
    textFileNumber: string;
    textUniqueId: string;
    textNumber: number;
    content: string;
}

/**
 * 段落删除，预览时需携带的额外参数
 */
export interface ParagraphPreviewDeleteParam {
    sessionId: string;
    datasetId: string;
    fileId: string;
    textFileNumber: string;
    textUniqueId: string;
    textNumber: number;
}

export interface EntityInfo {
    name: string;
    version?: string;
}

/**
 * 操作段落前确认信息接口
 */
export interface ParagraphConfirmInfo {
    remind: boolean;
    [EntityTypes.agents]: Partial<Record<EntityStatus, EntityInfo[]>>;
    [EntityTypes.plugins]: Partial<Record<EntityStatus, EntityInfo[]>>;
    [EntityTypes.apps]: Partial<Record<EntityStatus, EntityInfo[]>>;
}

export interface GenerateAgentEmbeddingParams {
    datasetId?: DataSetId;
    datasetName?: string;
    datasetDescription?: string;
    fileList: string[];
}

// 标注状态
export const enum LableStatus {
    // 标注中
    processing,
    // 标注成功
    success,
}

// 多媒体文件信息
export interface BaseMediaInfo {
    // 文件 ID
    fileId: string;
    // 标注状态
    status: LableStatus;
    // 标注内容
    label: string;
    // 文件名
    fileName: string;
    // 更新时间
    updateTime: number;
    // 文件处理状态
    fileStatus: FileStatus;
    // 视频资源链接
    video?: string | null;
    // 视频缩略图
    videoIcon?: string | null;
    // 图片链接
    image?: string | null;
    // 音频链接
    audio?: string | null;
}

interface PictureInfo extends BaseMediaInfo {
    // 图片链接
    image: string;
    // 视频资源链接
    video: null;
    // 视频缩略图
    videoIcon: null;
    // 音频链接
    audio: null;
}

interface VideoInfo extends BaseMediaInfo {
    // 图片链接
    image: null;
    // 视频资源链接
    video: string;
    // 视频缩略图
    videoIcon: string;
    // 音频链接
    audio: null;
}

interface AudioInfo extends BaseMediaInfo {
    // 图片链接
    image: null;
    // 视频资源链接
    video: null;
    // 视频缩略图
    videoIcon: null;
    // 音频链接
    audio: string;
}

export type MediaInfo = PictureInfo | VideoInfo | AudioInfo;

export const enum Direction {
    forward = 1,
    both = 0,
    backward = -1,
}

export interface MediaInfoParam {
    datasetId: string;
    fileId: string;
    scrollSize: number;
    scroll: Direction;
    fileType: FileType;
}

export const enum FileType {
    text = 1,
    picture = 2,
    websiteURL = 3,
    table = 4,
    audio = 5,
    video = 6,
    api = 7,
}

export interface FileTypeDetail {
    // 文件 ID
    fileId: string;
    // 文件名称
    fileName: string;
    // 文件类型
    fileType: string;
    // 文件状态
    status: string;
    // 文件大小
    totalSize: number;
    // 文件字符数
    totalCharacter: number;
    // 文件分段数
    totalText: number;
    // 更新时间（时间戳）
    updateTime: number;
    // 处理失败原因
    failMsg: number;
    // 文件类型
    type: FileType;
}

export interface WebsiteCrawlInfo {
    // 页面ID
    id: string;
    // 页面标题
    title: string;
    // 页面大小（字节）
    pageSize: number;
    // 页面字符数
    pageCharacter: number;
}

export interface CommitWebsiteTitleParams {
    urlList: Array<{id: string; url: string; title: string}>;
    prod: number;
}
