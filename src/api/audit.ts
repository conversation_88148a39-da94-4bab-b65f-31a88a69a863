import {Rule, RuleObject} from 'antd/es/form';
import {checkAgentNameRules} from '@/modules/agentPromptEditV2/utils';
import {request} from './request';

export const enum AuditTextType {
    Shortcuts = 'shortcuts',
    MultipleText = 'multipleText', // 后端根据此类型 3S词表检测、违规信息检测（违规联系信息（微信号、手机号、网站链接、QQ 号、邮箱））
    RiskText = 'riskText', // 风险文本检测
}

export const auditText = async (text: string, prefixText?: string, type?: AuditTextType) => {
    const auditRes = await request('POST', '/audit/text', {text, type});
    if (auditRes.errno === 0) {
        throw new Error((prefixText || '') + auditRes.errMsg);
    }
};

export const auditImage = async (imageUrl: string, prefixText?: string) => {
    const auditRes = await request('POST', '/audit/image', {imageUrl}, {timeout: 15000});
    if (auditRes.errno === 0) {
        throw new Error((prefixText || '') + auditRes.errMsg);
    }
};

export const getAuditTextRules = (prefixText: string, trigger?: string | string[], type?: AuditTextType): Rule => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, text: string) => {
            if (text && text.trim()) {
                try {
                    const res = await request('POST', '/audit/text', {
                        text,
                        appId: new URLSearchParams(location.search).get('appId') || '',
                        type,
                    });
                    if (res.errno === 0) {
                        return Promise.reject(new Error(prefixText + res.errMsg));
                    }
                } catch (error: any) {
                    return Promise.reject(new Error(error.msg));
                }
            }
        },
    };
};

export const getAgentNameRules = (prefixText: string, trigger?: string | string[]): Rule => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, value: string) => {
            try {
                return !value || checkAgentNameRules(value)
                    ? Promise.resolve()
                    : Promise.reject(new Error(prefixText + '仅支持中英文和数字'));
            } catch (error: any) {
                return Promise.reject(new Error(error.msg));
            }
        },
    };
};

export const getAuditImageRules = (prefixText: string, trigger?: string | string[]) => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, imageUrl: string) => {
            if (imageUrl) {
                try {
                    const res = await request(
                        'POST',
                        '/audit/image',
                        {
                            imageUrl,
                            appId: new URLSearchParams(location.search).get('appId') || '',
                        },
                        {timeout: 15000}
                    );
                    if (res.errno === 0) {
                        return Promise.reject(new Error(prefixText + res.errMsg));
                    }
                } catch (error: any) {
                    return Promise.reject(new Error(error.msg));
                }
            }
        },
    };
};

export const getAuditSpaceRules = (prefixText: string, trigger?: string | string[]) => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, text: string) =>
            !text || text.trim() ? Promise.resolve() : Promise.reject(new Error(prefixText + '为空')),
    };
};

/**
 * getAuditUrlRules 获取url表单项黄反机审校验规则
 * @param prefixText 提示文案前缀
 * @param suffixText 提示文案后缀
 * @param trigger 校验触发行为
 * @returns 表单项校验规则Rule
 */
export const getAuditUrlRules = (prefixText: string, suffixText: string, trigger?: string | string[]): Rule => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, urlText: string) => {
            if (urlText && urlText.trim()) {
                try {
                    const res = await request('POST', '/audit/porn/url', {
                        // 这里接口入参urls设计的是数组，当前场景只有单个url检测，所以这里需要包装一下
                        // 接口返回不是数组，接口返回数据结构设计不合理。后续如果有urls列表数组检测的场景，需要后端优化返回结构
                        urls: [urlText],
                        appId: new URLSearchParams(location.search).get('appId') || '',
                    });

                    // errno = 0 检测未通过，存在黄反url；errno = 1 检测通过
                    if (res.errno === 0) {
                        return Promise.reject(new Error(prefixText + res.errMsg + suffixText));
                    }
                } catch (error: any) {
                    return Promise.reject(new Error('检测失败，请稍后重试'));
                }
            }
        },
    };
};

// 检测输入内容中输否含有emoji表情
const containsEmoji = (str: string) => {
    return /([\u2700-\u27BF]|[\uE000-\uF8FF]|[\uD83C-\uDBFF\uDC00-\uDFFF]|[\uFE00-\uFE0F])/g.test(str);
};

/**
 *  对表单输入内容做emoji校验
 * @param prefixText 提示文案前缀
 * @param trigger 校验触发行为
 */
export const getInputEmoji = (prefixText: string, trigger?: string | string[]): Rule => {
    return {
        validateTrigger: trigger,
        validator: async (rule: RuleObject, value: string) => {
            try {
                return containsEmoji(value)
                    ? Promise.reject(new Error(prefixText + '不支持emoji符号'))
                    : Promise.resolve();
            } catch (error: any) {
                return Promise.reject(new Error(error.msg));
            }
        },
    };
};
