/**
 * @file 应用调试(测试、预览、发布)-数据接口
 * @doc 贴接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/J3rIeGHtkHGQUQ
 * <AUTHOR>
 */

import {createInterface} from '@/api/request';
import {
    BuildParams,
    BuildResult,
    BuildInfoQuery,
    BuildTaskDetail,
    PreviewParams,
    appPreviewUrl,
    PopupParams,
    PopupInfo,
} from '@/api/debug/interface';

// 对外暴露接口
export default {
    // 记录点击设置弹窗(表示用户看了)-应用维度
    recordPopup: createInterface<PopupParams, PopupInfo>('GET', '/popup/view'),

    // 查询弹窗是否需要打开-应用维度
    getPopup: createInterface<PopupParams, PopupInfo>('GET', '/popup'),

    // 构建应用-同步范围应用构建任务ID
    buildApp: createInterface<BuildParams, BuildResult>('POST', '/build/test'),

    // 根据构建任务获取构建信息
    getTaskInfo: createInterface<BuildInfoQuery, BuildTaskDetail>('GET', '/build/result'),

    // 需要先部署成功然后预览接口
    previewApp: createInterface<PreviewParams, {appPreviewUrl: appPreviewUrl}>('GET', '/build/preview'),
};
