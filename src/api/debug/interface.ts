/**
 * @file 应用调试(测试、预览、发布)-数据模型
 * <AUTHOR>
 * @doc flowJson 数据定义文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/3ecad61107464e
 */

import {FlowJson} from '@/store/flow/flowJson';
import {NodeStatus} from '@/store/flowContainer/flowBuildStatusStore'; // 采用画布的定义

type AppId = string; // todo改为从全局定义中获取

/**
 * 构建入参
 */
export interface BuildParams {
    /**
     * 应用ID
     */
    appId: AppId;
    /**
     * 构建环境
     */
    env: Env;
    /**
     * flowJson
     */
    flowJson: FlowJson; // 待flow侧补充
}

export enum Env {
    /**
     * 预览发布
     */
    preview = 1,
    /**
     * 线上分级发布
     */
    online,
}

type BuildTaskId = string;

/**
 * 构建结果
 */
export interface BuildResult {
    taskId: BuildTaskId;
    status: number;
    stage: number;
    buildInfo?: BuildInfo;
}

/**
 * 构建信息查询
 */
export interface BuildInfoQuery {
    /**
     * 应用ID
     */
    appId: AppId;
    /**
     * 构建任务ID
     */
    taskId: BuildTaskId;
}

/**
 * 构建信息详情
 */
export interface BuildTaskDetail {
    /**
     * 当前构建任务
     */
    taskId: BuildTaskId;
    /**
     * 构建异常日志
     */
    buildInfo: BuildErrInfo;
    /**
     * 构建状态
     */
    status: BuildStatus;
}

/**
 * 应用构建状态
 */
export enum BuildStatus {
    /**
     * 应用未构建
     */
    unBuild,
    /**
     * 应用构建中
     */
    building,
    /**
     * 构建成功
     */
    success,
    /**
     * 构建失败
     */
    fail,
}

/**
 * 全局中应用构建信息
 */
export interface BuildInfo {
    /**
     * 应用构建状态
     */
    buildStatus: BuildStatus;
    /**
     * 错误节点数量
     */
    errNodesNum: number;
}

/**
 * 编译错误信息(todo待与搜索勾兑)
 */
export interface BuildErrInfo {
    /**
     * 全局信息错误
     */
    errInfo: string;
    /**
     * 节点信息错误
     */
    nodes: NodeErrInfo[];
}

/**
 * 编译异常节点信息
 */
export interface NodeErrInfo {
    /**
     * 异常的节点id
     */
    id: string;
    /**
     * 异常的节点名
     */
    name: string;
    /**
     * 具化到异常节点内的具体字段错误-（本期暂不用）
     */
    position: string;
    /**
     * 整合的异常内容用于展示
     */
    log: string;
    /**
     * 节点状态
     */
    status: NodeStatus;
    /**
     * 运行时间
     */
    createTime: Date;
}

/**
 * 预览入参
 */
export interface PreviewParams {
    /**
     * 应用ID
     */
    appId: AppId;
    /**
     * 版本号(后端生成)
     */
    version?: string;
}

/**
 * 返回的预览链接
 */
export type appPreviewUrl = string;

/**
 * 弹窗名称
 */
export type PopupName = string;

/**
 * 查询/记录弹窗入参
 */
export interface PopupParams {
    appId?: AppId;
    name: PopupName;
}

/**
 * 返回的弹窗内容
 */
export interface PopupInfo {
    /**
     * 弹窗是否展示
     */
    show: boolean;
    /**
     * 弹窗名称
     */
    name: PopupName;
    /**
     * 弹窗内容
     */
    content: string;
}
