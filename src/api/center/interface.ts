import type {CardData, TabData} from '@/modules/center/interface';

export interface GetCardsParams {
    /** 关键字搜索插件名 */
    keyword?: string;

    /** 标签名 */
    tagId?: number;

    /** 每页显示的插件数量 */
    pageSize?: number;

    /** 当前页码 */
    pageNo?: number;

    /** 排序方式 1最热 2最新 不传默认最热 */
    order?: number;

    onlyMakeTheSame?: boolean;
}

export interface GetCardsResponse {
    /** 搜索记录总数 */
    total: number;

    /** 当前页码 */
    pageNo: number;

    /** 每页显示的插件数量 */
    pageSize?: number;

    /** 插件数据 */
    plugins: CardData[];

    /** 分类标签列表 */
    tabList?: TabList[];
}

export interface TabList {
    tagId: number;
    tagName: string;
}

export type GetTabsResponse = TabData[];

export interface AgentFavoriteStatus {
    /** 卡片的唯一id */
    appId: string;

    /** 卡片状态 */
    status: FavoriteStatus;
}

export enum FavoriteStatus {
    /** 已收藏 */
    Favorited = 1,
    /** 未收藏 */
    UnFavorited = 0,
}

// 首页轮播信息
export interface BannerInfo {
    banners: Banner[];

    tabs: BannerTab[];
}

export interface Banner {
    /** 轮播图id */
    id: number;

    /** 轮播图名称 */
    bannerName: string;

    /** 轮播图跳转链接 */
    jumpLink: string;

    /** 轮播图主体内容 */
    pcBannerImage: string;

    /** 轮播图文字主体 */
    bannerDescriptionImage: string;

    /** 轮播图背景图片 */
    bannerBackgroundImage: string;

    /** 移动端轮播图 */
    wiseBannerImage: string;

    /** 开始时间 */
    startTime: string;

    /** 结束时间 */
    endTime: string;

    /** 轮播图排序 */
    bannerOrder: number;
}

export interface BannerTab {
    tabId: number;

    /** tab名称 */
    tabName: string;

    /** tab描述 */
    tabDescription: string;

    /** tab图标 */
    icon: string;

    /** tab跳转链接 */
    jumpLink: string;

    /** tab排序 */
    tabOrder: number;

    /** 创建人 */
    operator: string;

    /** 创建时间 */
    createTime: string;

    /** 更新时间 */
    updateTime: string;
}

export interface DiscoveryResponse {
    /** tagid */
    tagId: number;

    /** 分类描述 */
    tagDesc: string;

    /** 包含的智能体 */
    agents?: CardData[];
}
