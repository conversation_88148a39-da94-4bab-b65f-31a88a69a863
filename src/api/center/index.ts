/**
 * @file 体验中心首页的主要接口
 *
 */

import {
    GetCardsParams,
    GetCardsResponse,
    GetTabsResponse,
    AgentFavoriteStatus,
    BannerInfo,
    DiscoveryResponse,
} from '@/api/center/interface';
import {createInterface} from '@/api/request';

export const GET_TABS_API = '/experhub/tab/tags';

export const GET_BANNER_API = '/experhub/operations/valid';

const api = {
    /** 获取标签列表 */
    getTabs: createInterface<void, GetTabsResponse>('GET', GET_TABS_API),

    /** 获取卡片列表 */
    getCards: createInterface<GetCardsParams, GetCardsResponse>('GET', '/experhub/search/list'),

    /** 获取收藏状态 */
    updateFavoriteStatus: createInterface<AgentFavoriteStatus, boolean>('POST', '/experhub/favorites/update'),

    /** 获取banner信息 */
    getBanner: createInterface<void, BannerInfo>('GET', GET_BANNER_API),

    /** 获取体验中心首页的发现智能体信息 */
    getDiscovery: createInterface<{onlyMakeTheSame?: boolean}, DiscoveryResponse[]>('GET', '/experhub/discovery'),

    /** 获取历史记录 */
    getHistory: createInterface<GetCardsParams, GetCardsResponse>('GET', '/experhub/recent/usage'),
};

export const getTabs = () => api.getTabs();

export const getBanner = () => api.getBanner();

export default api;
