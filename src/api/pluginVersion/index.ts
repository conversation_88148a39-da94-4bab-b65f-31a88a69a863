/**
 * @file 插件概览（详情+版本）-数据接口
 * @doc 贴接口文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/6HYOYHfwaY0Hz_
 * <AUTHOR>
 */

import {OnlineAuditDevelopVersion, VersionList, Page} from '@/api/appVersion/interface';
import {createInterface, request} from '../request';
import {
    PluginInfo,
    QueryPluginParams,
    GetPversionParams,
    OperatePVersionParams,
    PluginSettingParams,
    PluginPreview,
    PluginPreviewParams,
    UploadFileInfo,
    SaveEvaluateFileParams,
} from './interface';

const UPLOAD_FILE_TIME_OUT = 1000 * 360;

// 对外暴露接口
export default {
    // 获取单个插件信息
    getPluginInfoById: createInterface<QueryPluginParams, PluginInfo>('GET', '/plugin/getInfoPluginId'),
    // 更新插件设置
    updatePluginSetting: createInterface<PluginSettingParams, PluginInfo>('POST', '/plugin/setting/update'),
    // 获取应用审核和线上版本
    getOnlineAndAuditVersion: createInterface<QueryPluginParams, OnlineAuditDevelopVersion>(
        'GET',
        '/plugin/version/getonlineaudit'
    ),
    // 获取应用历史版本列表
    getHistoryVersions: createInterface<GetPversionParams, VersionList>('GET', '/plugin/version/gethistory'),
    // 对版本进行操作
    // 撤回申请
    withdrawAuditVersion: createInterface<OperatePVersionParams, void>('POST', '/plugin/version/withdraw'),
    // 审核通过发布
    publishVersion: createInterface<OperatePVersionParams, void>('POST', '/plugin/version/publish'),
    // 下线
    offlineVersion: createInterface<OperatePVersionParams, void>('POST', '/plugin/version/offline'),
    // 删除
    deleteVersion: createInterface<OperatePVersionParams, void>('POST', '/plugin/version/delete'),
    // TODO测试的
    getAppList: createInterface<Page, void>('GET', '/plugin/getMyPlugins'),
    // 获取预览Url
    getPreviewUrl: createInterface<PluginPreviewParams, PluginPreview>('GET', '/plugin/version/preview'),
    // 上传效果评估集文件
    uploadEvaluateFile: ({file}: {file: File}): Promise<UploadFileInfo> => {
        return request(
            'POST',
            '/plugin/file/upload?type=7',
            {
                file,
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                forbiddenToast: true,
                timeout: UPLOAD_FILE_TIME_OUT,
            }
        );
    },
    // 保存效果评估集文件
    saveEvaluateFile: createInterface<SaveEvaluateFileParams, void>('POST', '/plugin/file/modify'),
};

export const UPLOAD_IMAGE = '/lingjing/plugin/uploadImage';
