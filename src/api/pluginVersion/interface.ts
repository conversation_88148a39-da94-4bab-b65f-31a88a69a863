/**
 * @file 插件概览（详情+版本）-数据模型
 * <AUTHOR>
 */

import {Page, VersionStatus} from '@/api/appVersion/interface';
import {PluginBuildType, PluginStatus as PluginDisplayStatus} from '@/api/pluginList/interface';
import {PluginPublishType, PluginStatus} from '@/api/pluginCenter/interface';

export type PluginId = string;

export interface PluginPreviewParams {
    pluginId: PluginId;
    code: string;
}

export interface PluginPreview {
    previewUrl: string;
}

/**
 * 查询单个插件入参
 */
export interface QueryPluginParams {
    pluginId: PluginId;
}

/**
 * 查询插件版本入参
 */
export interface GetPversionParams extends Page {
    /**
     * ID
     */
    pluginId: PluginId;
}

/**
 * 操作插件版本入参
 */
export interface OperatePVersionParams {
    /**
     * ID
     */
    pluginId: PluginId;
    /**
     * 版本号
     */
    code: string;
}

/**
 * 使用案例icon（一期只有一个图片）
 */
export type UseCase = string;

/**
 * 插件通用设置入参
 */
export interface PluginSettingParams {
    /**
     * ID
     */
    pluginId: PluginId;
    /**
     * 发布类型
     */
    publishType: PublishType;
    /**
     * 使用案例icon（一期只有一个图片）
     */
    useCase?: {
        img: string[];
    };
}

/**
 * 插件发布类型
 */
export enum PublishType {
    /**
     * 手动发布
     */
    manual = 1,
    /**
     * 自动发布
     */
    auto = 2,
}

/**
 * 插件信息
 */
export interface PluginInfo {
    /**
     * 插件id
     */
    pluginId: PluginId;
    /**
     * 插件名称
     */
    pluginName: string;
    /**
     * 插件描述
     */
    pluginDesc: string;
    /**
     * 插件图片
     */
    logo: string;
    /**
     * 插件类型
     */
    pluginType: PluginType;
    /**
     * 插件状态
     */
    pluginStatus: PluginStatus;
    /**
     * 发布类型
     */
    publishType: PublishType;
    /**
     * 构建类型
     */
    buildType: PluginBuildType;
    /**
     * 当前最新版本号
     */
    curVersion: string;
    /**
     * 使用案例（一期只有一个图片）
     */
    useCase?: string;
    /**
     * 当前版本（当前线上最新版本）状态
     */
    versionStatus?: VersionStatus;
    /**
     * 应用密钥
     */
    secretKey?: string;
    /**
     * 是否为本地插件
     */
    localServer: boolean;
    /**
     * 插件状态
     */
    displayStatus: PluginDisplayStatus;
    /**
     * 引用次数
     */
    agentInfo: {total: number};
    /**
     * 审核（不通过）原因
     */
    auditMessage: string;
    /**
     * 私有/公有
     */
    pluginScope: PluginPublishType;
}

/**
 * 插件类型
 */
export enum PluginType {
    /**
     * 应用
     */
    Apply = 0,
    /**
     * 能力
     */
    Ability = 1,
    /**
     * 数据
     */
    Data = 2,
}

export const PLUGIN_TYPE_CN_NAME = {
    [PluginType.Apply]: '应用',
    [PluginType.Ability]: '能力插件',
    [PluginType.Data]: '数据插件',
} as const;

/**
 * 插件tab类型
 */
export enum PluginTabType {
    Overview = 'overview',
    Version = 'version',
    Output = 'output',
}

export const PluginTabMap = {
    [PluginTabType.Overview]: '概览',
    [PluginTabType.Version]: '版本管理',
    [PluginTabType.Output]: '对外部署',
};

/*
 * 上传效果评估集文件返回数据
 */
export interface UploadFileInfo {
    /**
     * 文件Id
     */
    fileId: string;
    /**
     * 文件Url
     */
    fileUrl: string;
}

/**
 * 保存插件评估效果集提交参数
 */
export interface SaveEvaluateFileParams {
    /**
     * 插件ID
     */
    pluginId: PluginId;
    /**
     * 插件评估效果集文件Id
     */
    fileId: string;
}
