/**
 * @file 付费咨询列表页的主要接口
 * <AUTHOR>
 */
import {createInterface} from '@/api/request';
import {CounselingAgentData, GoodsItem} from '@/modules/counseling/interface';
import {OnlineCounselingParams} from './interface';

/** 获取已认证的数字人智能体列表 */
export const getCounselingAgentList = createInterface<void, CounselingAgentData>('GET', '/counseling/agent/list');

/** 获取问一问商品服务列表数据 */
export const getCounselingGoodsList = createInterface<{appId: string}, GoodsItem[]>('GET', '/counseling/goods/list');

/** 上架服务 */
export const onlineCounseling = createInterface<OnlineCounselingParams, void>('POST', '/counseling/online');

/** 下架服务 */
export const offlineCounseling = createInterface<{appId: string}, void>('POST', '/counseling/offline', {
    headers: {
        'Content-Type': 'multipart/form-data',
    },
});

/** 获取 付费咨询 tab 是否展示 */
export const getCounselingAccess = createInterface<void, boolean>('GET', '/counseling/access');
