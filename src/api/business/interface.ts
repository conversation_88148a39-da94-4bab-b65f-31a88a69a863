import {AuditStatus} from '@/modules/agentList/interface';
import {DisplayInfo} from '../agentEdit/interface';
import {AppId} from '../appList/interface';
import {PopupName} from '../beginnerGuide/interface';

/** 商业组件类型，数值等于 /lingjing/business/list 接口中返回的 componentId */
export const enum BusinessType {
    /** 网页链接挂载 */
    Link = 1,
    /** 线索表单 */
    Lead = 2,
    /** 手动商品挂载 */
    Goods = 3,
    /** 赞赏激励 */
    Reward = 6,
    /** AI 自动挂载 */
    GoodsAutoMount = 7,
    /** 商品挂载 */
    GoodsCategory = 8,
    /** 凤巢广告 */
    FCPromo = 9,
}

/**
 * 商业组件配置信息
 */
export interface BusinessComponentConfig {
    /** 组件Id */
    componentId: number;
    /** 组件类型--前端未用到 */
    componentType: string;
    /** 组件名称 */
    componentName: string;
    /** 组件描述 */
    componentDesc: string;
    /** 组件示例图片地址--前端暂未用到 */
    examplePicUrl: string;
    /** 组件示例文档地址--前端暂未用到 */
    exampleDocUrl: string;
    /** 组件详细配置列表 */
    componentDetail: BusinessComponentUIInfo[];
    /** 嵌套结构 */
    childrenBusinessComponent: BusinessComponentConfig[];
}

/**
 * 所有商业组件配置信息
 */
export type BusinessComponentConfigList = BusinessComponentConfig[];

/**
 * 商业组件信息
 */
export interface BusinessInfo {
    [key: string]: BusinessComponentUIInfo[];
}

/**
 * 商业组件C端UI组件信息
 */
export interface BusinessComponentUIInfo {
    /** C端UI组件Id */
    componentDetailId: number | null;
    /**
     * C端UI组件UI信息
     * 接口uiData是json字符串，需要在/business/submit保存和/business/list详情api接口请求时进行转换
     */
    uiData: LinkUIData | LeadUIData | GoodsUIData | RewardUIData | AutoMountUIData | SwitchUIData;
}

/**
 * 商业组件-线索表单-C端UI组件信息
 */
export interface BusinessLeadUIInfo extends Omit<BusinessComponentUIInfo, 'uiData'> {
    uiData: LeadUIData;
}

/**
 * 商业组件-赞赏激励-C端UI组件信息
 */
export interface BusinessRewardUIInfo extends Omit<BusinessComponentUIInfo, 'uiData'> {
    uiData: RewardUIData;
}

/**
 * 商业化组件-通用开关UI信息
 */
export interface SwitchUIData {
    /** 开关状态 */
    isOpen: boolean;
}

/**
 * 商业组件-开关类组件uiData (如凤巢广告组件)
 */
export interface BusinessSwitchUIInfo extends Omit<BusinessComponentUIInfo, 'uiData'> {
    uiData: SwitchUIData;
}

/**
 * 附带（链接/线索）时机描述配置
 */
export interface AppendSceneListItem {
    // 附带场景
    scene: string;
    // 问题示例
    example: string;
}

/**
 * 配置的C端链接挂载UI组件信息
 */
export interface LinkUIData {
    /** 挂载链接名称 */
    linkName: string;
    /** 挂载链接描述 */
    linkDesc: string;
    /** 挂载链接logo */
    linkLogo: string;
    /** 挂载链接地址 http://xxx or https://xxx */
    linkUrl: string;
    /** na 挂载链接地址 baiduboxapp://xxx */
    naLinkUrl?: string;
    /** 挂载链接触发词，已废弃 */
    linkWords?: string[];
    /** 附带链接时机描述配置 */
    appendSceneList: AppendSceneListItem[];
}

/** 售卖方式类型枚举 */
export enum SaleMethodType {
    /** 全部售卖 */
    All = 1,
    /** 部分售卖 */
    Part = 2,
    /** 全部自用 */
    Self = 3,
}

/**
 * 省市数据结构
 */
export interface ProvinceCities {
    id: number;
    name: string;
    cities?: ProvinceCities[];
}

/** 漏接提醒类型 */
export enum MissCallNotifyType {
    /** 不提醒 */
    None = 0,
    /** 短信提醒 */
    Sms = 1,
}

/** 虚拟手机号信息 */
export interface VirtualPhoneInfo {
    /** 手机号Id */
    phoneId?: number;
    /** 号码区域 示例：省-市 本期只支持北京，前端无需表单选择省市，后端写死北京 */
    area?: string;
    /** 漏接提醒类型 0-不提醒 1-短信提醒 */
    notify: MissCallNotifyType;
}

/**
 * 配置C端线索表单UI组件信息
 */

export interface LeadUIData {
    /** 线索表单Id */
    solutionId: string;
    /** 表单类型 */
    formType: string;
    /** 表单名称 */
    solutionName: string;
    /** 表单描述，已废弃 */
    solutionDesc?: string;
    /** 引导语 */
    title: string;
    /** 表单线索属性 */
    properties: LeadSolutionProperties;
    /** 附带线索时机描述配置 */
    appendSceneList: AppendSceneListItem[];
    /** 电话线索属性 */
    phone?: {
        /** 引导语 该字段已废弃改用外层title字段 */
        caption?: string;
        /** 手机号 */
        secretKey?: string;
        /** 虚拟手机号信息 */
        virtualPhoneInfo?: VirtualPhoneInfo;
    };
    /** 是否有电话线索 */
    hasPhoneLeads?: boolean;
    /** 是否有表单线索 */
    hasFormLeads?: boolean;
    /** 垂直领域 */
    solutionCategory?: number;
    /** 是否在对话框上方展示预约咨询快捷指令入口 */
    formShortcut?: boolean;
    /** 是否在对话框上方展示电话线索快捷指令入口 */
    phoneShortcut?: boolean;

    /** --- 售卖配置 start --- */
    /**
     * 售卖方式
     * 1-全部售卖，2-部分售卖，3-全部自用
     */
    saleMethod?: SaleMethodType;
    /**
     * 是否开启售卖协议
     * 仅saleMethod=1/2时，必须同意协议，saleMethod=3（全部售卖）无需勾选协议
     */
    openRFQ?: boolean;
    /** 线索售卖自留城市id列表 */
    reservedLocations?: number[][];
    /** --- 售卖配置 end --- */
}

export interface LeadSolutionProperties {
    title?: string; // 为空
    subTitle?: string; // 为空
    // 线索表单项
    controls: LeadControl[];
    // 线索表单提交按钮UI
    // 后端写死label="提交"
    submitButton: {
        label: string;
    };
    // 验证方式：//sms-短信验证 picture-图片验证 smart-智能验证
    // 本期后端写死type="smart"
    captcha: {
        type: string;
    };
}

export enum LeadControlType {
    /** 电话输入框 */
    Phone = 'phone',
    /** 输入框 */
    Name = 'name',
    /** 单选框 */
    Radio = 'radio',
}

export interface LeadControl {
    /** 线索表单项类型 */
    type: LeadControlType;
    /** 表单项标题 */
    label: string;
    /** 是否必填 */
    required: boolean;
    /** 表单项属性 */
    props: {
        /** Input框placeholder */
        placeholder?: string;
        /** Radio框可选项值 */
        options?: string[];
    };
    /**
     * 用于B端平台配置线索表单的表单项是否选中，选中就会在C端展示
     * 提交接口的时候过滤掉未选中的表单项（checked=false的）
     */
    checked?: boolean;
}

/** 线索表单垂直领域 */
export enum LeadSolutionCategory {
    /** 医疗 */
    Medical = 1,
}

/** 保存商品组件配置提交参数数据结构 */
export interface SaveBusinessComponentParams {
    /** AgentId */
    appId: AppId;
    /** 组件Id */
    componentId: number;
    /** 组件详细配置列表 */
    componentDetail: BusinessComponentUIInfo[];
}

/** 保存商品组件配置提交返回数据结构 */
export interface SaveBusinessComponentRes extends SaveBusinessComponentParams {
    previewUrl: string;
    display: DisplayInfo;
    auditInfo: {
        status: AuditStatus;
        msg: string;
    };
}

/** 用户暗绑的UC信息 */
export interface BindUCInfo {
    ucId: number;
}

/** 配置C端商品UI组件信息 */
export interface GoodsUIData {
    /** 商品Id */
    goodsId: string;
    /** 商品原名称 */
    oriTitle: string;
    /** 商品原价格 */
    oriReservePrice: string;
    /** 销量 */
    sales: number;
    /** 商品封面 */
    oriCoverUrl: string;
    /** 商品落地页购买链接 */
    itemUrl: string;
    /** 商品是否下架 */
    isDelete: boolean;
}

export interface Platform {
    platform: 'jd' | 'pdd' | 'tb' | 'jx';
    platformName: string;
    logo: string;
    desc: string;
    tag: string[];
    choose: boolean;
}

export interface Category {
    categoryId: number;
    categoryName: string;
    choose: boolean;
}

export interface AutoMountUIData {
    goodsPlatform: Platform[];
    goodsCategory: Category[];
}

// 百家号账号状态码
export enum BJHStatusCode {
    /** 未注册 */
    Unregistered = 0,
    /** 已注册 */
    Registered,
    /** 封禁 */
    Banned,
    /** 注销 */
    Deactivated,
}

// 百家号动态挂载权益
export enum BJHMountRight {
    /** 未开通 */
    Unopened = 0,
    /** 开通中 */
    Opening,
    /** 已开通 */
    Opened,
}

export interface BJHAccountStatus {
    bidStatus: BJHStatusCode;
    bjhMountRight: BJHMountRight;
}

// 配置C端赞赏激励组件信息
export interface RewardUIData {
    /** 赞赏价格配置，B端本期不在前端配置，用不到该字段 */
    rewardData?: object;
}

// 平台信息
export interface PlatformInfo {
    status: number;
    goodsPlatform: string;
    authInfo: {
        pid: string;
    };
}

type NullablePlatformInfo = PlatformInfo | null;

export interface PIDStatus {
    [key: string]: NullablePlatformInfo | undefined; // 支持动态扩展平台
    /**
     * 京东
     */
    jd?: NullablePlatformInfo;
    /**
     * 拼多多
     */
    pdd?: NullablePlatformInfo;
    /**
     * 淘宝
     */
    tb?: NullablePlatformInfo;
    /**
     * 小度
     */
    xd?: NullablePlatformInfo;
}

export enum LeadShortcutText {
    /** 电话线索快捷指令 */
    Phone = '致电客服',
    /** 表单线索快捷指令 */
    Form = '预约咨询',
}

/** 初始化弹窗状态 */
export type PopupState = Record<PopupName, boolean>;

/** 获取线索组件策略配置请求参数数据结构 */
export interface GetLeadsStrategyValueParams {
    /** 智能体ID */
    appId: string;
    /** 智能体名称 */
    appName: string;
    /** 智能体简介 */
    overview: string;
    /** 人设与回复逻辑 */
    system: string;
    /** 开场文案 */
    description?: string;
    /** 开场白问题 */
    recommends?: string[];
}

/** 获取线索组件策略配置响应参数数据结构 */
export interface GetLeadsStrategyValueRes {
    /** 线索名称 */
    leadsName: string;
    /** 召回场景 */
    recall: string;
    /** 问题示例 */
    questionExample: string[];
}
