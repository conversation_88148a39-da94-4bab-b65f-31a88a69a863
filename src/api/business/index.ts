/**
 * @file 商业化组件 接口
 * <AUTHOR>
 */

import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {request} from '../request';
import {
    BindUCInfo,
    BJHAccountStatus,
    BusinessComponentConfig,
    BusinessComponentConfigList,
    BusinessComponentUIInfo,
    SaveBusinessComponentParams,
    SaveBusinessComponentRes,
    PIDStatus,
    ProvinceCities,
    GetLeadsStrategyValueParams,
    GetLeadsStrategyValueRes,
} from './interface';

type serverBusinessComponentUIInfo = Omit<BusinessComponentUIInfo, 'uiData'> & {
    uiData: string;
};

type ServerBusinessComponentConfig = Omit<BusinessComponentConfig, 'componentDetail' | 'childrenBusinessComponent'> & {
    componentDetail: serverBusinessComponentUIInfo[];
    childrenBusinessComponent: ServerBusinessComponentConfig[];
};

type ServerSaveBusinessComponentRes = Omit<SaveBusinessComponentRes, 'componentDetail'> & {
    componentDetail: serverBusinessComponentUIInfo[];
};

/** json stringify 格式化商业化组件uiData对象 */
export const jsonStringifyComponentDetail = (
    componentDetail: BusinessComponentUIInfo[]
): serverBusinessComponentUIInfo[] => {
    return componentDetail.map(detail => ({
        ...detail,
        uiData: JSON.stringify(detail.uiData),
    }));
};

/** json parse 格式化商业化组件uiData字符串 */
export const jsonParseComponentDetail = (
    componentDetail: serverBusinessComponentUIInfo[]
): BusinessComponentUIInfo[] => {
    return componentDetail.map(detail => ({
        ...detail,
        uiData: JSON.parse(detail.uiData || '{}'),
    }));
};

export default {
    /** 获取所有商业能力组件配置列表 */
    getBusinessComponentConfigList: ({
        appId,
        isOnlineVersion,
    }: {
        appId: string;
        /** 是否获取线上版本商业化数据-适用于公开配置页 */
        isOnlineVersion?: boolean;
    }): Promise<BusinessComponentConfigList> =>
        request('GET', isOnlineVersion ? '/business/online/structure/list' : '/business/structure/list', {appId}).then(
            res =>
                res.map((item: ServerBusinessComponentConfig) => ({
                    ...item,
                    // 商业组件详情
                    componentDetail: jsonParseComponentDetail(item.componentDetail),
                    // 商业组件嵌套子组件
                    childrenBusinessComponent: item.childrenBusinessComponent?.map(
                        (item: ServerBusinessComponentConfig) => ({
                            ...item,
                            componentDetail: jsonParseComponentDetail(item.componentDetail),
                        })
                    ),
                }))
        ),
    /** 提交保存商业化组件 */
    saveBusinessComponent: (params: SaveBusinessComponentParams): Promise<SaveBusinessComponentRes> => {
        return request(
            'POST',
            '/business/submit',
            {
                ...params,
                componentDetail: jsonStringifyComponentDetail(params.componentDetail),
            },
            // 线索表单有营销通机审，较耗时，前端限制10s超时
            // 商品挂载，商品列表有多个提交时，下游agent Server 部署耗时可能5s，整体可能超过11s，前端先限制15s超时，后端服务耗时有人力了整体排查看是否可以优化
            {forbiddenToast: true, timeout: 15000}
        ).then((res: ServerSaveBusinessComponentRes) => {
            return {
                ...res,
                componentDetail: jsonParseComponentDetail(res.componentDetail),
            };
        });
    },
    /** 获取用户暗绑的UC信息-主要是暗绑的ucId-以跳转营销通管理查看线索表单 */
    getBindUcInfo: (): Promise<BindUCInfo> => request('GET', '/user/getBindUcInfo', {}, {forbiddenToast: true}),

    /** 查询用户百家号账号状态 */
    getBJHAccountStatus: (): Promise<BJHAccountStatus> => {
        return request('POST', '/user/bid', {create: 0});
    },

    /**
     * @description 创建百家号账号
     */
    createBJHAccount: (): Promise<BJHAccountStatus> => {
        return request('POST', '/user/bid', {create: 1});
    },

    /**
     * @description 获取平台账号绑定状态
     */
    getPlatformBindStatus: (): Promise<PIDStatus> => {
        return request('GET', '/user/pid');
    },

    /** 获取省市列表 */

    getProvinceCityList: (): Promise<ProvinceCities[]> => request('GET', '/config/get', {key: 'province_cities_clue'}),

    /** 获取线索表单策略生成数据 */
    getLeadsStrategyValue: (params: GetLeadsStrategyValueParams): Promise<GetLeadsStrategyValueRes> => {
        return request('POST', SECURE_URL_ENUM.AgentLeadsInfoGen, {...params}, {forbiddenToast: true, timeout: 8000});
    },
};
