import {ServerAgentInfo} from '@/api/agentEdit/interface';

export enum AgentSource {
    /** 云游中国标签 */
    TravelAgentNewYear = 'travel_agent_newyear',
    /** 教师招募活动 */
    TeacherRecruitAgent = 'teacher_recruit_agent',
}

export interface AgentCreateParams {
    source?: AgentSource;
}

export interface AgentCreateRes {
    appId: string;
    name?: string;
    overview?: string;
    logoUrl?: string;
    versionCodeOnSave?: number;
}

export interface AgentLocation {
    /** 省份 */
    province?: {
        name: string;
        id: number;
    };

    /** 城市 */
    city?: {
        name: string;
        id: number;
    };
}

export interface PersonalityItem {
    id: string;
    name: string;
}

export interface CitySpecial {
    system: string;
}

export type AgentInfo = Pick<
    ServerAgentInfo,
    'appId' | 'name' | 'logoUrl' | 'permission' | 'speech' | 'digitalFigure'
> & {
    /** 位置 */
    location: AgentLocation;

    /** 城市特色 */
    citySpecial: CitySpecial;

    /** 角色个性 */
    personalities?: PersonalityItem[];
};

export interface AgentConfig {
    agentInfo: AgentInfo;
}

export interface TemplateImage {
    id: number;
    name: string;
    url: string;
    logoUrl: string;
    backgroundPicColor: string;
    templateUuid: string;
}

export interface CityList {
    id: string;
    name: string;
    cities?: Array<{
        id: string;
        name: string;
    }>;
}

export interface AgentTemplateReq {
    provinceId?: number;
    cityId?: number;
}

export interface AgentSetupFormData {
    agentInfo: Omit<AgentConfig['agentInfo'], 'appId'>;
}

export interface AgentPublishParams {
    agentInfo: AgentInfo;
}

export interface GenerateCityFeatureParams {
    location: AgentLocation;
}
