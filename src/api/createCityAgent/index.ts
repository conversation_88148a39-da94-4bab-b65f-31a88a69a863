/**
 * @file 智能体快速复制活动页接口
 *
 */

import {request} from '@/api/request';
import {
    AgentCreateParams,
    AgentPublishParams,
    GenerateCityFeatureParams,
    AgentCreateRes,
    AgentInfo,
    AgentTemplateReq,
    CityList,
    TemplateImage,
    CitySpecial,
} from '@/api/createCityAgent/interface';

export const CREATE_AGENT_API = '/agent/app/create';

export const createAgent = (params: AgentCreateParams): Promise<AgentCreateRes> =>
    request('POST', CREATE_AGENT_API, params);

// 获取静态图片库
export const getTemplateImages = (): Promise<TemplateImage[]> =>
    request('GET', '/config/get', {
        key: 'tour_china_template_images',
    });

export const getCityList = (): Promise<CityList[]> => request('GET', '/config/get', {key: 'province_cities2'});

export const getAgentTemplate = (params: AgentTemplateReq): Promise<AgentInfo> =>
    request('GET', '/agent/activity/conf', params, {timeout: 1000 * 60});

export const publishAgent = (params: AgentPublishParams): Promise<{appId: string}> =>
    request('POST', '/agent/saveAndPublish', params, {
        timeout: 1000 * 60,
        forbiddenToast: true,
    });

export const GENERATE_CITY_FEATURE_API = `/agent/cityspecial/change`;

export const generateCityFeature = (
    params: GenerateCityFeatureParams,
    signal?: AbortSignal
): Promise<{citySpecial: CitySpecial}> =>
    request('POST', GENERATE_CITY_FEATURE_API, params, {timeout: 1000 * 60, signal, forbiddenToast: true});
