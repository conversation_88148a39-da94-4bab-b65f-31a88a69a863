export enum SourceType {
    TeacherRecruitAgent = 'teacher_recruit_agent',
}

export interface ActivityInfoParams {
    source: string;
}

export enum IdentityType {
    EMPLOYED = 1, // 在职
    RETIRED = 2, // 退休
    KOL = 3, // 博主
}

// 认证状态
export enum AuthStatus {
    Pass = 1, // 授权通过
    Failed = 0, // 授权不通过/未授权
}

// 职业认证状态
export enum CareerAuthStatus {
    UnAuth = 0, // 未认证
    Auditing = 1, // 认证中
    Pass = 2, // 认证通过
}

// 认证信息
export interface QualificationInfo {
    // 实名认证状态
    realStatus: AuthStatus;
    realName: string;
    // 身份证号
    certCode: string;

    // 职业认证状态
    careerAuthStatus: CareerAuthStatus;
    profession: string;
}

export enum CareerInfoStatus {
    Waiting = 0, // 待审核
    Auditing = 1, // 审核中
    Pass = 2, // 审核通过
    Failed = 3, // 审核不通过
}

// 所属类型
export enum IUnitType {
    PublicSchool = 1, // 公立学校
    EducationalInstitution = 2, // 教育行政事业单位、教研机构
    PrivateInternationalSchool = 3, // 民办私立、国际学校
    TextbookPublisher = 4, // 教材教辅出版公司
    TrainingOrganization = 5, // 教培机构
    EducationMCN = 6, // 教育领域MCN/KOL
    Other = 7, // 其他（请填写）
    IndependentLecturer = 8, // 独立讲师
    IndependentTextbookAuthor = 9, // 独立教材作者
}

// 年级
export enum IGradeType {
    PRIMARY = 1, // 小学
    JUNIORHIGH = 2, // 初中
    SENIORHIGH = 3, // 高中
}

// 学科
export enum ISubjectType {
    CHINESE = 1, // 语文
    MATH = 2, // 数学
}

// 最高荣誉
export enum IHonor {
    None = -1, // 暂无以上荣誉
    NationalEducationMember = 1, // 国家级教育、教研相关机构成员
    NationalAward = 2, // 国家级官方、权威荣誉奖项
    NationalAcademicCertification = 3, // 国家级学术成果认证
    ProvincialEducationMember = 4, // 省级含直辖市教育、教研相关机构成员
    ProvincialAward = 5, // 省级官方、权威荣誉奖项
    ProvincialAcademicCertification = 6, // 省级学术成果认证
    TopTrainingInstructor = 7, // 全国头部教培机构s级名师
    DistrictEducationMember = 8, // 市区县教育、教研相关机构成员
    DistrictAward = 9, // 市区县官方、权威荣誉奖项
    Other = 10, // 其他
}

export enum IFansSection {
    NoOperation = 0, // 未运营账号
    Under10K = 1, // 1万粉以下
    TenTo50K = 2, // 1万~5万粉
    FiftyTo100K = 3, // 5万~10万粉
    HundredTo200K = 4, // 10万~20万粉
    Over200K = 5, // 20万粉以上
}

// 基础信息
export interface BaseInfo {
    teacherType: IdentityType | null;
    unitType: IUnitType | null;
    platform: string;
    userName: string;
    phone: string;
    garde: IGradeType | null;
    subject: ISubjectType | null;
    honor: number | null;
    honorDetail: string;
    teacherCertification: string;
    fansSection: IFansSection | null;
    indexLink: string; // 主页链接
    status: CareerInfoStatus | null;
    msg: string; // 审核结果信息
}

export enum AgentAuditStatus {
    onLine = 1,
    DRAFT = 3,
    AUDITING = 4,
    FAILURE = 5,
    SUCCESS = 6,
}

// 智能体信息
export interface AgentInfo {
    appId: string;
    status: AgentAuditStatus | null;
    versionCodeOnSave?: number;
    name?: string;
    overview?: string;
    logoUrl?: string;
}

// 总页面信息
export interface ActivityInfo {
    qualification: QualificationInfo;
    baseInfo: BaseInfo;
    agentInfo: AgentInfo;
}

export interface ActivityInfoRes {
    personInfo: QualificationInfo;
    careerBasicInfo: BaseInfo;
    agentInfo: AgentInfo;
}

export interface BasicInfoParams {
    source: string;
    careerBasicInfo: BaseInfo;
}
