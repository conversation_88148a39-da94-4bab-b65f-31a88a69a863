/**
 * @file 名师招募
 * <AUTHOR>
 */

import {createInterface} from '@/api/request';
import {ActivityInfoRes, BasicInfoParams, ActivityInfoParams} from './interface';

export default {
    /** 获取活动详情 */
    getActivityDetail: createInterface<ActivityInfoParams, ActivityInfoRes>('GET', '/operation/activity/info'),
    /** 提交资质信息 */
    submitAuthInfo: createInterface<BasicInfoParams, void>('POST', '/operation/activity/qualifications/submit'),
};
