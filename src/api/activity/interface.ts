// 主体类型枚举
export enum CustomerType {
    // 学生
    Student = 1,
    // 个人
    Personal = 2,
    // 企业
    Enterprise = 3,
}
export enum AgentType {
    // 健康
    Health = 1,
    // 情感
    Emotion = 2,
    // 法律
    Law = 3,
}
// 开发经历枚举
export enum ExperienceType {
    // 有0代码智能体开发经验
    AgentExperience = 1,
    // 有0代码、工作流、插件等开发经验
    WorkflowPluginExperience = 2,
    // 无开发经验
    NoExperience = 3,
}

// 联系方式枚举
export enum ContactType {
    Phone = 1,
    WeChat = 2,
    Other = 3,
}

// 问卷类型
export enum QuestionnaireType {
    // 报名表单
    DeveloperApply = 1,
    // 作品提交表单
    SubmitWork = 2,
    // 应征报名表单
    ExpertApply = 3,
    // 垂类专家智能体提交表单
    ExpertSubmitWork = 4,
    // 决赛提交作品表单
    FinalsSubmitWork = 5,
}

// 表单基础信息
// interface BaseInfo {
//     name: string;
//     contactType: ContactType;
//     contactNumber: string;
// }
// // 报名表单
// interface DeveloperApplyData extends BaseInfo {
//     // 主体类别
//     playerType: CustomerType;
//     // 开发经历
//     agentDevExperience: ExperienceType;
// }

// // 作品提交表单
// interface SubmitWorkData extends BaseInfo {
//     // 智能体名称
//     agentName: string;
//     // 智能体ID
//     agentId: string;
//     // 智能体简介
//     agentDesc: string;
// }

// // 专家垂类
// interface ExpertApplyData extends BaseInfo {
//     // 学历
//     highestEducation: string;
//     // 最高学历毕业院校
//     graduatedSchool: string;
//     // 技术领域
//     technicalField: string;
//     // 过往开发比赛获奖经历
//     competitionWinningExperience?: string;
//     // 合作专家智能体实现方案
//     implementation: string;
// }

// // 决赛作品提交
// interface FinalsSubmitWorkData {
//     name: string;
//     agentName: string;
//     agentId: string;
//     // 作品附件
//     workAttachment: string;
// }

// 表单提交接口入参
export interface SubmitParams {
    // 赛事ID
    competitionId: number;
    // 专家ID
    expertId?: number;
    // 问卷类型 5种
    questionnaireType: QuestionnaireType;
    // 问卷内容
    questionnaireDetail: string;
}

// 问卷提交状态
export enum SubmitStatus {
    // 待提交
    Pending = 0,
    // 已提交
    Submitted = 1,
}

// 专家组队状态
export enum TeamStatus {
    CanTeamUp = 0,
    CanNotTeamUp = 1,
}

// 专家信息
export interface ExpertInfo {
    id: number;
    expertName: string;
    expertPortrait: string;
    // 简介
    introduction: string;
    // 领域
    knowledgeArea: string;
    // 开发方向
    devDirection: string;
    // 预期效果
    expect: string;
    // 组队状态
    teamStatus: TeamStatus;
}

// 专家信息列表
export type ExpertInfoList = ExpertInfo[];

// 附件上传接口返回参数
export interface FileUploadResponse {
    fileName: string;
    fileType: string;
    fileSize: string;
    fileUrl: string;
}

// 报名信息来源
export enum InfoType {
    // 文心智能体官方平台
    Agent = 1,
    // CSDN
    CSDN = 2,
    // 51CTO
    CTO = 3,
    // 极星会
    Jixing = 4,
    // 人人都是产品经理
    Renren = 5,
    // 其他
    Other = 6,
}

// 是否还会参加其他商业化方向
export enum ParticipateOther {
    // 参与
    YES = 1,
    // 不参与
    NO = 0,
}

// 赛事类型
export enum ActivityType {
    // 开发者赚钱季
    DeveloperEanring = 2,
}

// 入围决赛名单
export interface FinalsListItem {
    agentName: string;
    agentId: string;
    // 初赛分数
    preliminaryScore: number;
    // 内容质量分数
    contentScore?: number;
    // 赛道专项分数
    subjectScore?: number;
}
