/**
 * @file 赛事平台相关接口
 * <AUTHOR>
 */
import {AxiosProgressEvent} from 'axios';
import {createInterface, request} from '@/api/request';
import {
    QuestionnaireType,
    SubmitParams,
    ExpertInfo,
    ExpertInfoList,
    SubmitStatus,
    FileUploadResponse,
} from '@/api/activity/interface';

export default {
    /** 获取 agent 数据概览 */
    questionnaireSubmit: createInterface<SubmitParams, void>('POST', '/experhub/questionnaire/submit'),
    /** 作品附件上传 */
    uploadFile: ({
        file,
        onUploadProgress,
    }: {
        file: File;
        onUploadProgress: (e: AxiosProgressEvent) => void;
    }): Promise<FileUploadResponse> => {
        return request(
            'POST',
            '/experhub/file/upload',
            {
                file,
            },
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                forbiddenToast: true,
                timeout: 1000 * 360,
                onUploadProgress: onUploadProgress,
            }
        );
    },
    /** 查询问卷状态 */
    getQuestionnaireStatus: createInterface<
        {competitionId: number; questionnaireType: QuestionnaireType},
        {submitStatus: SubmitStatus}
    >('GET', '/experhub/questionnaire/getStatus'),
    /** 赛题列表查询 */
    // competitionType不传参默认查询NV赛题,开发者赚钱季 :2
    getCompetitionList: createInterface<{competitionType: number} | void, Array<{id: number; competitionName: string}>>(
        'GET',
        '/experhub/competition/getList'
    ),
    /** 专家列表查询 */
    getExpertList: createInterface<{competitionId: number}, ExpertInfoList>('GET', '/experhub/group/getExpertList'),
    /** 通过专家姓名查询专家是否存在 */
    getExpertByName: createInterface<{expertName: string}, ExpertInfo>('GET', '/experhub/group/getExpertByName', {
        forbiddenToast: true,
    }),
};
