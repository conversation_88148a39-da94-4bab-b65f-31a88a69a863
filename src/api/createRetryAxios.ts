/**
 * @file 创建重试接口专用的 axios 实例
 * @description 支持自定义重试条件、次数、间隔时间、请求拦截器
 * <AUTHOR>
 *
 */

import axios from 'axios';
import axiosRetry from 'axios-retry';
import {AxiosResponse, AxiosError} from 'axios-interface';
import {defaultOptions} from '@/api/request';
import {toastHttpErrorMessage} from '@/api/error';

interface CustomConfig {
    /** 是否禁止请求失败异常弹窗 */
    forbiddenToast?: boolean;
}

// 扩展 AxiosRequestConfig 类型
declare module 'axios' {
    export interface AxiosRequestConfig {
        /** 自定义请求 Config 配置 */
        customConfig?: CustomConfig;
    }
}

interface CreateRetryAxiosOptions {
    /** 重试条件 */
    retryCondition: (error: AxiosError) => boolean;

    /** 重试间隔时间 */
    retryDelay?: (retryCount: number, error: AxiosError) => number;

    /** 重试延迟 */
    retries?: number;

    /** 响应拦截器 */
    responseInterceptor?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;

    /** 错误拦截器 */
    errorInterceptor?: (error: AxiosError) => Promise<AxiosError>;
}

// 默认的拦截器
// 参考：https://axios-http.com/docs/interceptors
const defaultResponseInterceptor = (response: AxiosResponse) => response;

const defaultErrorInterceptor = (error: AxiosError) => {
    // 请求失败时默认展示异常弹窗，可以通过 forbiddenToast 请求参数定制不展示
    // 定制方式：axiosInstance.get('/path', {params: {a: '请求参数'}, customConfig: {forbiddenToast: true}})
    if (!error.config?.customConfig?.forbiddenToast) {
        toastHttpErrorMessage(error.response?.data);
    }

    return Promise.reject(error);
};

// 默认的重试次数
const defaultRetries = 3;

// 默认无重试延迟
const defaultRetryDelay = () => 0;

/**
 * 创建重试接口专用的 axios 实例
 */
export default function createRetryAxios({
    retryCondition,
    retryDelay = defaultRetryDelay,
    retries = defaultRetries,
    responseInterceptor = defaultResponseInterceptor,
    errorInterceptor = defaultErrorInterceptor,
}: CreateRetryAxiosOptions) {
    // 使用 axios-retry 设置重试请求时需要传递 axios 实例，但 axios-interface 不支持直接获取 axios 实例
    // 因此直接使用 axios.create() 进行创建
    const axiosInstance = axios.create(defaultOptions);

    axiosInstance.interceptors.response.use(responseInterceptor, errorInterceptor);

    axiosRetry(axiosInstance, {
        retries,
        retryCondition,
        retryDelay,
    });

    return axiosInstance;
}
