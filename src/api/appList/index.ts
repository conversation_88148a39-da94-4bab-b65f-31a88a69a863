import {createInterface} from '../request';
import {
    AppListParams,
    AppListRes,
    AppTemplateInfo,
    CreateAppParams,
    AppId,
    CheckAppInfoParams,
    CheckAppInfoRes,
} from './interface';

const getAppList = createInterface<AppListParams, AppListRes>('GET', '/app/list');
const getAppTemplateList = createInterface<void, AppTemplateInfo[]>('GET', '/template/getTemplates');
const postCreateApp = createInterface<CreateAppParams, AppId>('POST', '/app/createApp');
const checkAppInfo = createInterface<CheckAppInfoParams, CheckAppInfoRes>('POST', '/audit/namedesc');

export default {
    // 获取应用列表
    getAppList: async ({pageNo = 1, pageSize}: AppListParams): Promise<AppListRes> =>
        await getAppList({pageNo, pageSize}),
    // 获取应用模版列表
    getAppTemplateList: async (): Promise<AppTemplateInfo[]> => await getAppTemplateList(),
    // 创建应用
    postCreateApp: async (createAppParams: CreateAppParams): Promise<AppId> => await postCreateApp(createAppParams),
    // 机审检查应用信息
    checkAppInfo: async (checkAppInfoParams: CheckAppInfoParams, forbiddenToast = false): Promise<CheckAppInfoRes> =>
        await checkAppInfo(checkAppInfoParams, {forbiddenToast}),
};
