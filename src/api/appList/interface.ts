import {AuditStatus, ServerAuditStatus} from '@/modules/agentList/interface';
/**
 * AI应用AppId
 */
export type AppId = string;
/**
 * 应用模版Id
 */
export type AppTemplateId = string;

export interface AppListParams {
    pageNo: number;
    pageSize: number;
}
/**
 * AI应用信息列表
 */
export interface AppListRes {
    total: number;
    pageNo: number;
    pageSize: number;
    appRespList: AppListItem[];
}

export interface AppListItem {
    appInfo: AppInfo;
    appStatistics: AppStatisticsOverview;
}

/**
 * AI应用信息
 */
export interface AppInfo {
    /**
     * 应用AppId
     */
    appId: AppId;
    /**
     * 应用名称
     */
    appName: string;
    /**
     * 应用简介
     */
    appDesc: string;
    /**
     * 应用头像
     */
    appAvatar: string; // 对应表字段photoAddr，建议接口对齐
    /**
     * 应用状态 同智能体状态
     * 历史原因，该字段的值在 src/api/request.ts 被拦截处理了
     * TODO：不应该request拦截层直接处理后端返回的status字段，而是应该依据后端返回的status字段值新增前端需要的字段，待技术优化
     */
    status: AuditStatus & ServerAuditStatus;
    /**
     * 应用状态 同智能体状态
     * 前端新增的字段，取值等同于接口返回的status字段
     * 暂时在 src/api/request.ts 拦截处理
     */
    originalStatus?: AuditStatus & ServerAuditStatus;
    /**
     * 应用创建类型
     * 1-可视化创建，2-自主开发
     */
    buildType: AppBuildType;
    /**
     * 应用类型
     * 1-通用式框架，2-对话式框架，3-内部自建应用
     */
    frameworkType: AppFrameworkType;
    /**
     * 应用模版Id
     * 为空时表示非模版创建
     */
    templateId: AppTemplateId;
    // /**
    //  * 应用概览数据
    //  */
    // appStatistics?: AppStatisticsOverview;
    /**
     * 应用审核状态
     */
    auditStatus: AppAuditStatus;
    /**
     * 应用审核消息
     */
    auditMessage?: string;
    /**
     * 应用密钥
     */
    secretKey?: string;
}

/**
 * 应用创建类型枚举
 * 1-可视化创建，2-自主开发, 3-Prompt编排
 */
export enum AppBuildType {
    /**
     * 可视化创建，低码
     */
    LowCode = 1,
    /**
     * 自主开发
     */
    SelfDevelop = 2,
    /**
     * Prompt编排
     */
    Prompt = 3,
}

export const APP_BUILD_TYPE_NAMES = {
    [AppBuildType.LowCode]: '可视化创建',
    [AppBuildType.SelfDevelop]: '自主开发',
    [AppBuildType.Prompt]: 'Prompt编排',
} as const;

/**
 * 应用类型枚举
 * 1-通用式框架，2-对话式框架，3-内部自建应用
 */
export enum AppFrameworkType {
    Generic = 1, // 通用式框架
    Chat = 2, // 对话式框架
    InnerBuild = 3, // 内部自建应用
}

/**
 * 应用统计概览数据
 */
export interface AppStatisticsOverview {
    /**
     * 历史累计活跃数
     */
    activeUv: number;
}

// 1-审核中，2-审核通过，3-审核失败
export enum AppAuditStatus {
    /**
     * 开发中
     */
    Developing = 2,
    /**
     * 审核中
     */
    Auditing = 3,
    /**
     * 审核通过
     */
    AuditPass = 4,
    /**
     * 审核失败
     */
    AuditFail = 5,
    /**
     * 已发布
     */
    Online = 6,
    /**
     * 已下线
     */
    Offline = 7,
}

export const APP_AUDIT_STATUS_MAP = {
    [AppAuditStatus.Developing]: '开发中',
    [AppAuditStatus.Auditing]: '审核中',
    [AppAuditStatus.AuditPass]: '审核通过',
    [AppAuditStatus.AuditFail]: '审核不通过',
    [AppAuditStatus.Online]: '已上线',
    [AppAuditStatus.Offline]: '已下线',
};

/**
 * 创建应用提交参数
 */
export interface CreateAppParams
    extends Required<Pick<AppInfo, 'appName' | 'appAvatar' | 'appDesc' | 'buildType' | 'frameworkType'>> {
    templateId?: AppTemplateId;
}

/**
 * AI应用模版信息
 */
export interface AppTemplateInfo {
    /**
     * 模版Id
     */
    templateId: AppTemplateId;
    /**
     * 模版名称
     */
    templateName: string;
    /**
     * 模版介绍
     */
    templateDesc: string;
    /**
     * 模版卡片背景图
     */
    templatePic: string;
    /**
     * 应用创建类型
     * 1-可视化创建，2-自主开发
     */
    buildType: AppBuildType;
    /**
     * 应用类型
     * 1-通用式框架，2-对话式框架
     */
    frameworkType: AppFrameworkType;
}

/**
 * 应用信息机审参数（小程序名字/简介）
 */
export type CheckAppInfoParams = Partial<Pick<AppInfo, 'appName' | 'appDesc' | 'appId'>>;

/**
 * 应用信息机审返回数据结构
 */
export interface CheckAppInfoRes {
    /**
     * 应用名称机审结果状态
     */
    nameRes?: CheckAppInfoStatus;
    /**
     * 应用名称机审结果信息
     */
    nameMessage: string;
    /**
     * 应用简介机审结果状态
     */
    descRes: number;
    /**
     * 应用简介机审结果信息
     */
    descMessage: string;
}

/**
 * 应用信息机审状态
 */
export enum CheckAppInfoStatus {
    CheckFail = 0,
    CheckSuccess = 1,
}
