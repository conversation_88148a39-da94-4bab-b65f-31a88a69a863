/* eslint-disable complexity */
import {AxiosError, AxiosResponse, createFactory, Method, Options} from 'axios-interface';
import dayjs from 'dayjs';
import {sendCustomApiError} from '@/utils/monitor/customError';
import {getMonitorStorage} from '@/utils/localStorageCache';
import {reportError, CustomError, ErrorType} from '@/utils/monitor/index';
import {useSecureStore} from '@/store/secure/useSecureStore';
import {HEADER_JT_KEY, NeedParisDetectUrlList} from '@/store/secure/constants';
import DICTS from '@/dicts/home';
import {
    ERROR_REQUEST,
    ErrorRequestCode,
    toastHttpErrorMessage,
    ErrorResponseCode,
    ExemptWarningUrl,
    ErrorRequestCodeName,
    ERROR_SECURE_MSG,
} from './error';

export interface CustomOptions extends Options {
    /** 是否禁止接口服务异常Toast */
    forbiddenToast?: boolean;
}

export interface RequestResponse<T = any> extends AxiosResponse {
    errno: number;
    msg: string;
    requestId: string;
    data: T;
    timestamp: number;
}

const controller = new AbortController(); // 用于取消请求

const ua = navigator.userAgent;
// 是否是移动端荣耀浏览器
const isHonor = /HONORVER/i.test(ua) && /Mobile/i.test(ua);
// 判断是否命中风控
const hitParis = (url?: string) => {
    // 移动端荣耀浏览器存在兼容性问题，暂时不走风控
    if (isHonor) return false;
    return !!url && NeedParisDetectUrlList.includes(url);
};

// h5页面接口请求转到xinxaing-proxy
const ENV = {
    [DICTS.XINXIANG_HOST]: `https://${DICTS.XINXIANG_PROXY_HOST}/edu`,
    [DICTS.AGENT_HOST]: `https://${DICTS.XINXIANG_PROXY_HOST}/edu`,
};

const BASE_URL = ENV[window.location?.host] || '/edu';

export const defaultOptions: Options = {
    baseURL: BASE_URL,
    timeout: 1000 * 6,
    onPending: async (_params: any, options: Options) => {
        if (hitParis(options.url)) {
            const jt = await useSecureStore.getState().getParisJtToken();

            if (!jt) {
                // 前端上报错误监控
                reportError(
                    new CustomError(
                        ErrorType.BusinessError,
                        (window.Banti ? '昊天镜JS SDK获取jt为空' : '昊天镜JS SDK尚未加载成功') + `url：${options.url}`
                    )
                );

                const error: AxiosError = {
                    code: ErrorRequestCodeName.NetWorkGetJTTokenError,
                    name: ErrorRequestCodeName.NetWorkGetJTTokenError,
                    message: '获取jt失败',
                    isAxiosError: false,
                    toJSON: () => ({}),
                };

                return Promise.reject(error);
            }

            return Promise.resolve({
                ...options,
                headers: {...options.headers, [HEADER_JT_KEY]: jt},
            });
        }

        return Promise.resolve(options);
    },
    // eslint-disable-next-line complexity
    onResolve: (response: AxiosResponse) => {
        // 处理通用拦截
        const {data} = response;

        // 心响App接入规范中 请求状态用status代替errno
        if (+data.status === 0 || +data.errno === 0) {
            return data.data;
        }

        // 处理异常
        return Promise.reject(data);
    },
    // eslint-disable-next-line complexity
    onReject: (
        error: AxiosError & {
            response?: AxiosResponse;
        }
    ) => {
        let errno = ErrorRequestCode.NetworkUnknownError;
        // 服务端有错误信息，优先使用服务端错误
        let serverMsg = '';
        if (error.response) {
            // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
            errno = ErrorRequestCode.NetworkHttpCodeError;
            const data = error.response?.data;
            // 目前服务端errno500归类为server error需排除
            if (data?.errno && data?.errno !== 500 && data?.msg) {
                serverMsg = data?.msg;
            }
        }

        // 获取jt token失败
        if (error.code === ErrorRequestCodeName.NetWorkGetJTTokenError) {
            errno = ErrorRequestCode.NetWorkGetJTTokenError;
        }

        // 请求未发出时的异常
        if (error.code === 'ERR_NETWORK' && !window.navigator.onLine) {
            // 无网络
            errno = ErrorRequestCode.NetworkOfflineError;
        } else if (error.code === 'ECONNABORTED') {
            // 超时
            errno = ErrorRequestCode.NetworkTimeoutError;
        } else if (error.code === 'ERR_CANCELED') {
            // 请求被中断
            errno = ErrorRequestCode.NetworkUnToastError;
        }

        // 服务端已经把业务中的很多错误改成500用于统计pvlost（这种前端支持主动上报）
        if (error?.response?.status === 200 && error?.response?.data.errno) {
            sendCustomApiError({
                url: error?.request?.resource?.url,
                method: error?.request?.resource?.method,
                status: error?.request?.resource?.status,
                response: error?.response,
                responseHeaders: error?.response?.headers,
            });
        }

        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject({
            errno,
            msg: serverMsg || ERROR_REQUEST[errno]?.msg,
            timestamp: dayjs().unix(),
        });
    },
    headers: {
        'Content-Type': 'application/json',
    },
    signal: controller?.signal,
    withCredentials: true,
};

const {request: axiosRequest} = createFactory(defaultOptions);

// request请求失败异常处理
function requestRejectErr(err: any, forbiddenToast: boolean) {
    // 未禁止异常弹窗
    if (!forbiddenToast) {
        // 需安全验证的接口，校验失败，弹窗提示映射为ERROR_SECURE_MSG
        if (err.errno === ErrorResponseCode.JtSecurityCheckFailed) {
            toastHttpErrorMessage({...err, msg: ERROR_SECURE_MSG}, true);
        } else {
            toastHttpErrorMessage(err, err.errno === ErrorResponseCode.UserNotLoggedIn);
        }

        return Promise.reject(err);
    }

    const formatErr = {
        ...err,
        msg: err.errno === ErrorResponseCode.JtSecurityCheckFailed ? ERROR_SECURE_MSG : err.msg,
    };

    return Promise.reject(formatErr);
}

/* 业务请求方法 */
function customRequest(method: Method, url: string, params?: any, customOptions?: CustomOptions | undefined) {
    const {forbiddenToast = false, ...requestOption} = customOptions || {};

    const key = `${method}${url}${JSON.stringify(params)}`;
    const forbidDuplicate = !ExemptWarningUrl.includes(url);
    if (forbidDuplicate) {
        const key = `${method}${url}${JSON.stringify(params)}`;
        if (getMonitorStorage().get(key)) {
            // 告警
            reportError(new CustomError(ErrorType.BusinessError, `${key}-repeat`));
        }

        getMonitorStorage().set(key, true, 1000);
    }

    return axiosRequest(method, url, params, {
        headers: {
            'User-Session': getMonitorStorage().get('sessionId'),
            'Lingjing-Operation': getMonitorStorage().get('operationModule'),
        },
        ...requestOption,
    })
        .catch(err => {
            return requestRejectErr(err, forbiddenToast);
        })
        .finally(() => {
            // 请求结束取消
            forbidDuplicate && getMonitorStorage().remove(key);
        });
}

export {customRequest as request};
