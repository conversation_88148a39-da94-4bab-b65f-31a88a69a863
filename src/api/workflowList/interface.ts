/**
 * 工作流Id
 */
export type WorkflowId = string;

/**
 * 工作流列表分页接口参数信息
 */
export interface WorkflowListParams {
    pageNo: number;
    pageSize: number;
}

/**
 * 工作流列表返回信息
 */
export interface WorkflowListRes {
    total: number;
    pageNo: number;
    pageSize: number;
    dataList: WorkflowInfo[];
}

/**
 * 工作流信息
 */
export interface WorkflowInfo {
    /**
     * 工作流Id
     */
    workflowId: WorkflowId;
    /**
     * 工作流名称
     */
    name: string;
    /**
     * 工作流简介
     */
    description: string;
    /**
     * 工作流头像icon
     */
    icon: string;
    /**
     * 工作流类型
     */
    status: WorkflowStatus;
    /**
     * flowJson 初始数据
     */
    flowJson: string;
}

/**
 * 创建工作流提交参数
 */
export type CreateWorkflowParams = Required<
    Pick<WorkflowInfo, 'name' | 'description' | 'icon' | 'flowJson'> & {templateWorkflowId?: string}
>;

/**
 * 工作流状态枚举
 */
export enum WorkflowStatus {
    /**
     * 开发中
     */
    Developing = 1,
    /**
     * 已发布
     */
    Online = 3,
}

/**
 * 工作流删除校验参数
 */
export interface DeleteValidateParams {
    workflowId: WorkflowId;
    pageNo: number;
    pageSize: number;
}

/**
 * 工作流删除解除绑定
 */
export interface UnbindWorkflowParams {
    workflowId: WorkflowId;
    appId: string;
}
/**
 * 工作流删除校验返回参数
 */
export interface AgentListParams {
    pageNo: number;
    pageSize: number;
    total: number;
    dataList: AgentData[];
}

export interface AgentData {
    appId: string;
    appName: string;
    appDesc: string;
    picAddr: string;
}
