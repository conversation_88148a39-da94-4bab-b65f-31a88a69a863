/**
 * @file 工作流相关接口
 * <AUTHOR>
 */

import {SECURE_URL_ENUM} from '@/store/secure/constants';
import {createInterface} from '../request';
import {
    CreateWorkflowParams,
    WorkflowId,
    WorkflowListParams,
    WorkflowListRes,
    DeleteValidateParams,
    UnbindWorkflowParams,
    AgentListParams,
} from './interface';

const getWorkflowList = createInterface<WorkflowListParams, WorkflowListRes>('GET', '/workflow/list');
const postCreateWorkflow = createInterface<CreateWorkflowParams, {workflowId: WorkflowId}>(
    'POST',
    SECURE_URL_ENUM.WorkflowCreate
);
const workflowNameIsExist = createInterface<{name: string}, boolean>('GET', '/workflow/nameIsExist');
const deleteWorkflow = createInterface<{workflowId: WorkflowId}, void>('POST', '/workflow/delete');
const duplicateWorkflow = createInterface<{workflowId: WorkflowId}, WorkflowId>('POST', '/workflow/copy');
const deleteValidate = createInterface<DeleteValidateParams, AgentListParams>('GET', '/workflow/delete/validate');
const unbindWorkflow = createInterface<UnbindWorkflowParams, void>('POST', '/workflow/unbind');

export default {
    // 获取工作流列表
    getWorkflowList: async ({pageNo = 1, pageSize}: WorkflowListParams): Promise<WorkflowListRes> =>
        await getWorkflowList({pageNo, pageSize}),
    // 创建工作流
    postCreateWorkflow: async (createWorkflowParams: CreateWorkflowParams): Promise<{workflowId: WorkflowId}> =>
        await postCreateWorkflow(createWorkflowParams),
    // 校验名称是否存在
    workflowNameIsExist: async (params: {name: string}): Promise<boolean> => await workflowNameIsExist(params),
    // 删除前校验
    deleteValidate: async ({workflowId, pageNo, pageSize}: DeleteValidateParams): Promise<AgentListParams> =>
        await deleteValidate({workflowId, pageNo, pageSize}),
    // 删除工作流
    deleteWorkflow: async (workflowId: WorkflowId): Promise<void> => await deleteWorkflow({workflowId}),
    // 删除工作流前解绑agent
    unbindWorkflow: async ({workflowId, appId}: UnbindWorkflowParams): Promise<void> =>
        await unbindWorkflow({workflowId, appId}),
    // 复制工作流
    duplicateWorkflow: async (workflowId: WorkflowId): Promise<WorkflowId> => await duplicateWorkflow({workflowId}),
};
