<!--
 * 配置HTML生成
 * 参考 https://reskript.dev/docs/advanced/multiple-entry#%E9%85%8D%E7%BD%AEhtml%E7%94%9F%E6%88%90
-->
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="baidu-site-verification" content="codeva-5w2rCCQ5Zb" />
    <meta name="keywords"
        content="文心智能体，文心智能体平台，智能体开发平台，文心一言官方智能体开发平台，一言插件平台,文心大模型插件,文心一言插件平台,文心大模型插件平台,一言官方智能体开发平台，插件平台，插件，AI插件，agent，plugin,llm plugin,LLM Plugin,ERNIE Plugin,agentbuilder,AgentBuilder,Agentbuilder">
    <meta name="description"
        content="文心智能体平台AgentBuilder，是百度推出的基于文心大模型的智能体平台，支持广大开发者根据自身行业领域、应用场景，选取不同类型的开发方式，打造大模型时代的产品能力。开发者可以通过prompt编排的方式低成本开发智能体（Agent），同时文心智能体平台还将为智能体（Agent）开发者提供相应的流量分发路径，完成商业闭环。">
    <title>
        <%= templateData.title %>
    </title>

    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?23da5e206ac2c6039108372b7a25c6e5";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>

<body>
</body>


</html>
