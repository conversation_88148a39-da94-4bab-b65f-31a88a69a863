import {ConfigProvider} from 'antd';
import {StyleProvider} from '@ant-design/cssinjs';
import {StrictMode} from 'react';
import {createRoot} from 'react-dom/client';

import {BrowserRouter} from 'react-router-dom';
import '@/styles';
import theme from '@/styles/lingjing-light-theme';
import {WeirwoodErrorBoundary} from '@/utils/monitor/errorBoundary';
import ComponentDemo from '@/components';

const root = document.createElement('div');
root.id = 'root';
document.body.appendChild(root);

const config = {
    autoCompleteConfig: {
        autoComplete: 'off',
    },
};

createRoot(root).render(
    <StrictMode>
        <WeirwoodErrorBoundary>
            <ConfigProvider theme={theme} autoInsertSpaceInButton={false} input={config.autoCompleteConfig}>
                <StyleProvider hashPriority="high">
                    <BrowserRouter>
                        <ComponentDemo />
                    </BrowserRouter>
                </StyleProvider>
            </ConfigProvider>
        </WeirwoodErrorBoundary>
    </StrictMode>
);
