import {ConfigProvider} from 'antd';
import {StyleProvider} from '@ant-design/cssinjs';
import {StrictMode} from 'react';
import {createRoot} from 'react-dom/client';

import {BrowserRouter} from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import '@/styles';
import theme from '@/styles/lingjing-light-theme';
import App from '@/modules';
import LoginModule from '@/components/Login';
import {WeirwoodErrorBoundary} from '@/utils/monitor/errorBoundary';
import PriorityManagerProvider from '@/components/Popover/PriorityManager';

dayjs.locale('zh-cn');

const root = document.createElement('div');
root.id = 'root';
document.body.appendChild(root);

const config = {
    autoCompleteConfig: {
        autoComplete: 'off',
    },
};

createRoot(root).render(
    <StrictMode>
        <WeirwoodErrorBoundary>
            <ConfigProvider
                locale={zhCN}
                theme={theme}
                autoInsertSpaceInButton={false}
                input={config.autoCompleteConfig}
            >
                <StyleProvider hashPriority="high">
                    <PriorityManagerProvider>
                        <BrowserRouter>
                            <App />
                            <LoginModule />
                        </BrowserRouter>
                    </PriorityManagerProvider>
                </StyleProvider>
            </ConfigProvider>
        </WeirwoodErrorBoundary>
    </StrictMode>
);
