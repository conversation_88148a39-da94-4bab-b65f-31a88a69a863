<!--
 * 配置HTML生成
 * 参考 https://reskript.dev/docs/advanced/multiple-entry#%E9%85%8D%E7%BD%AEhtml%E7%94%9F%E6%88%90
-->
<!doctype html>
<html>
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" />
        <meta name="baidu-site-verification" content="codeva-5w2rCCQ5Zb" />
        <meta
            name="keywords"
            content="文心智能体，文心智能体平台，智能体开发平台，文心一言官方智能体开发平台，一言插件平台,文心大模型插件,文心一言插件平台,文心大模型插件平台,一言官方智能体开发平台，插件平台，插件，AI插件，agent，plugin,llm plugin,LLM Plugin,ERNIE Plugin,agentbuilder,AgentBuilder,Agentbuilder"
        />
        <meta
            name="description"
            content="文心智能体平台AgentBuilder，是百度推出的基于文心大模型的智能体平台，支持广大开发者根据自身行业领域、应用场景，选取不同类型的开发方式，打造大模型时代的产品能力。开发者可以通过prompt编排的方式低成本开发智能体（Agent），同时文心智能体平台还将为智能体（Agent）开发者提供相应的流量分发路径，完成商业闭环。"
        />
        <title>文心智能体平台 请更新您的浏览器</title>
        <style>
            div,
            body,
            html,
            p,
            span,
            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin: 0;
                padding: 0;
            }

            .upgrade-container {
                max-width: 640px;
                height: 100vh;
                margin: auto;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .title {
                color: rgba(6, 7, 9, 0.8);
                font-size: 28px;
                font-weight: 500;
                text-align: center;
            }

            .description {
                margin-top: 8px;
                color: rgba(6, 7, 9, 0.5);
                font-size: 16px;
                text-align: center;
            }

            .browser-list-container {
                display: flex;
                margin-top: 20px;
                padding: 0 12px;
                width: 100%;
            }

            .browser-item {
                border-radius: 8px;
                background: rgba(6, 7, 9, 0.02);
                position: relative;
                margin: 12px 6px 12px;
                cursor: pointer;
                padding: 24px 0px;
                flex-grow: 1;
            }

            .browser-item:hover {
                background: rgba(6, 7, 9, 0.04);
            }

            .browser-icon {
                width: 56px;
                height: 56px;
                border-radius: 12px;
                background-color: white;
                position: relative;
                left: 50%;
                transform: translateX(-50%);
            }

            .browser-title {
                margin-top: 18px;
                color: rgba(6, 7, 9, 0.8);
                text-align: center;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 20px;
            }

            .browser-item-content {
                padding: 0 24px;
            }
        </style>
        <script>
            var ua = window.navigator.userAgent,
                isEdgeHTML = /Windows.*Edge/i.test(ua),
                isChrome = !isEdgeHTML && /Chrome/i.test(ua),
                isSafari = !isEdgeHTML && !isChrome && /Safari/i.test(ua),
                isFirefox = /Firefox/i.test(ua),
                isIE = /Trident/i.test(ua),
                isBaiduApp = /baiduboxapp\//i.test(ua),
                isIOS = /(iPhone|iPad)/i.test(ua);
            function isUnsupportedBrowser() {
                var i;
                if (isBaiduApp && isIOS) {
                    if ((i = ua.match(/(iPhone|iPad)\sOS\s([\d_]+)/)) && i[2]) {
                        return parseInt(i[2].replace(/_/g, '.'), 10) < 13;
                    }
                }
                if (isChrome) {
                    if ((i = ua.match(/Chrome\/(\d+)/i)) && i[1]) return parseInt(i[1], 10) < 88;
                } else if (isSafari) {
                    if ((i = ua.match(/Version\/(\d+)/i)) && i[1]) return parseInt(i[1], 10) < 14;
                } else {
                    if (!isFirefox) return !!isEdgeHTML || !!isIE;
                    if ((i = ua.match(/Firefox\/(\d+)/i)) && i[1]) return parseInt(i[1], 10) < 78;
                }
            }
            if (!isUnsupportedBrowser()) {
                window.location.replace('/center');
            }
        </script>
    </head>

    <body>
        <div class="upgrade-container">
            <p class="title">浏览器版本过低</p>
            <p class="description">
                您当前的浏览器版本可能无法支持【文心智能体平台
                AgentBuilder】的最佳体验。请更新您的浏览器或尝试以下推荐浏览器
            </p>
            <div class="browser-list-container">
                <div class="browser-item" onclick="window.open('https\://www.google.cn/chrome/')">
                    <div class="browser-item-content">
                        <img
                            class="browser-icon"
                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/supported-browser/chrome.png"
                        />
                        <p class="browser-title">Chrome</p>
                    </div>
                </div>
                <div class="browser-item" onclick="window.open('https\://www.microsoft.com/zh-cn/edge')">
                    <div class="browser-item-content">
                        <img
                            class="browser-icon"
                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/supported-browser/edge.png"
                        />
                        <p class="browser-title">Edge</p>
                    </div>
                </div>
                <div class="browser-item" onclick="window.open('https\://www.mozilla.org/zh-CN/firefox/new/')">
                    <div class="browser-item-content">
                        <img
                            class="browser-icon"
                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/supported-browser/firefox.png"
                        />
                        <p class="browser-title">Firefox</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
