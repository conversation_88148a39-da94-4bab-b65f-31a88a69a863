/**
 * @file Tabs，超出长度自动收起为下拉菜单
 * <AUTHOR>
 */

import styled from '@emotion/styled';
import {Dropdown, TabsProps} from 'antd';
import {ReactNode, useCallback, useEffect, useMemo, useRef, useState} from 'react';

const DeActiveStyle: React.CSSProperties = {
    borderRadius: 9,
    background: 'white',
    padding: '5px 20px',
    fontSize: 14,
    marginInlineEnd: 0,
    height: 30,
    lineHeight: 1,
    cursor: 'pointer',
    flexShrink: 0,
};
const ActiveStyle: React.CSSProperties = {
    color: '#5562F2',
    fontWeight: 600,
};

const StyledDropdown = styled(Dropdown)`
    .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-active {
        color: #5562f2;
        font-weight: 500;
        background-color: transparent;
    }
`;

export default function CollapseTabs(props: {
    items: TabsProps['items'];
    // 当前激活的key
    activeKey: string;
    onChange: (key: string) => void;
    // 更多节点样式
    more: ReactNode;
    // 标签样式
    labelStyle?: React.CSSProperties;
    // 标签激活样式
    activeStyle?: React.CSSProperties;
    // tabs间距
    marginInline?: number;
}) {
    const {items, more, labelStyle, activeStyle = {}, marginInline = 10, onChange} = props;
    const contentRef = useRef<HTMLDivElement>(null);
    const moreRef = useRef<HTMLDivElement>(null);
    const resizeObserver = useRef<ResizeObserver>();

    /** 显示的 tabsItem 数量 */
    const [splitIndex, setSplitIndex] = useState(0);
    const [isOverflow, setIsOverflow] = useState(false);
    /** 显示的 tabsItem  */
    const showItems = useMemo(() => {
        if (items) {
            return isOverflow ? items?.slice(0, splitIndex) : items;
        }
        return [];
    }, [isOverflow, items, splitIndex]);

    const determineDisplayItemIndex = useCallback(() => {
        if (!contentRef.current) return;
        /** 判断是否有超出长度的情况 */
        const isOverflow = contentRef.current.scrollWidth > contentRef.current.offsetWidth;
        setIsOverflow(isOverflow);
        if (!isOverflow) {
            return;
        }
        if (!moreRef.current?.children[0]) {
            return;
        }
        /** 获取 more 图标部分宽度  */
        const moreWidth = moreRef.current.children[0].clientWidth;
        /** 获取容器尺寸  */
        const contentWidth = contentRef.current.clientWidth;
        /** 容器内部元素  */
        const itemChildren = contentRef.current.children;
        /** 初始宽度为 more 图标部分宽度 */
        let sumWidth = moreWidth + marginInline;
        for (let i = 0; i < itemChildren.length; i++) {
            const itemWidth = itemChildren[i].clientWidth + marginInline;
            /** 总宽度超出时停止遍历 */
            if (sumWidth + itemWidth > contentWidth) {
                break;
            } else {
                /** 未超出宽度则继续增加显示的 tabsItem 索引 */
                sumWidth += itemWidth;
                setSplitIndex(i);
            }
        }
    }, [marginInline]);

    /** 监听传入的 items 变化，并重新计算显示的 tabsItem 数量 */
    useEffect(() => {
        determineDisplayItemIndex();
    }, [items, determineDisplayItemIndex]);

    /** 监听容器尺寸变化，并重新计算显示的 tabsItem 数量 */
    useEffect(() => {
        resizeObserver.current = new ResizeObserver(determineDisplayItemIndex);
        resizeObserver.current.observe(contentRef.current!);
        return () => {
            if (contentRef.current) {
                // eslint-disable-next-line react-hooks/exhaustive-deps
                resizeObserver.current?.unobserve(contentRef.current);
            }
        };
    }, [determineDisplayItemIndex]);

    const onClickItem = useCallback(
        (value: string) => {
            onChange(value);
        },
        [onChange]
    );

    const handleMenuSelect = useCallback(
        ({key}: {key: string}) => {
            onChange(key);
        },
        [onChange]
    );

    /** 获取 tabsItem 样式 */
    const getItemStyle = useCallback(
        (item: {key: string}) => {
            if (item.key === props.activeKey) {
                return {
                    ...DeActiveStyle,
                    marginInlineEnd: marginInline,
                    ...labelStyle,
                    ...ActiveStyle,
                    ...activeStyle,
                };
            }
            return {...DeActiveStyle, marginInlineEnd: marginInline, ...labelStyle};
        },
        [props.activeKey, marginInline, labelStyle, activeStyle]
    );

    /** 超出宽度时，下拉菜单显示的 tabsItem */
    const dropdownItems = useMemo(
        () =>
            items?.slice(splitIndex).map(item => ({
                key: item.key,
                label: item.label,
            })),
        [items, splitIndex]
    );

    /** 当前是否选中了下拉菜单项中的 tabsItem */
    const dropdownActive = useMemo(() => {
        return dropdownItems?.find(item => item.key === props.activeKey);
    }, [dropdownItems, props.activeKey]);

    return (
        <>
            <div className="flex w-full flex-shrink-0 items-center overflow-x-hidden">
                {showItems.map(item => (
                    <div style={getItemStyle(item)} onClick={() => onClickItem(item.key)} key={item.key}>
                        {item.label}
                    </div>
                ))}
                {isOverflow && (
                    <StyledDropdown
                        menu={{items: dropdownItems, activeKey: props.activeKey, onClick: handleMenuSelect}}
                        // eslint-disable-next-line react/jsx-no-bind
                        getPopupContainer={trigger => trigger}
                        placement="bottomLeft"
                        autoAdjustOverflow={false}
                    >
                        <div
                            className="hover:text-[#757679]"
                            style={{
                                display: 'inline-block',
                                flexShrink: 0,
                                cursor: 'pointer',
                                color: dropdownActive ? '#5562F2' : '',
                                fontWeight: dropdownActive ? 500 : '',
                            }}
                        >
                            {more}
                        </div>
                    </StyledDropdown>
                )}
            </div>
            {/** 计算 tabsItem 宽度区域 */}
            <div className="flex h-0 w-full overflow-x-auto overflow-y-hidden" ref={contentRef}>
                {items?.map(item => (
                    <div style={getItemStyle(item)} key={item.key}>
                        {item.label}
                    </div>
                ))}
            </div>
            {/** 计算 more 图标宽度区域 */}
            <div className="h-0 overflow-y-hidden" ref={moreRef}>
                <div className="inline-block">{more}</div>
            </div>
        </>
    );
}
