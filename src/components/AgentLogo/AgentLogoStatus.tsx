import React from 'react';
import {AuditStatus} from '@/modules/agentList/interface';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';

const AgentLogoStatus: React.FC<{
    packageStatus: AuditStatus.Online | AuditStatus.Offline;
    speechStatus?: SpeechStatus;
    figureTaskStatus?: FigureTaskStatus;
}> = ({packageStatus, speechStatus, figureTaskStatus}) => {
    if (packageStatus === AuditStatus.Offline) {
        return (
            <div
                className="absolute left-0 top-0 flex h-full w-full
                       items-center justify-center rounded-full bg-black
                       text-[14px] text-white opacity-50"
            >
                已下架
            </div>
        );
    }

    const showLogoMask =
        figureTaskStatus === FigureTaskStatus.Generating ||
        figureTaskStatus === FigureTaskStatus.Failed ||
        speechStatus === SpeechStatus.Processing ||
        speechStatus === SpeechStatus.Failed;

    if (showLogoMask) {
        return (
            <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center rounded-full bg-[#00000080] text-xs font-semibold leading-4 text-white">
                <span className="mb-[3px]">
                    {figureTaskStatus === FigureTaskStatus.Generating || figureTaskStatus === FigureTaskStatus.Failed
                        ? '形象'
                        : '声音'}
                </span>
                <span>
                    {figureTaskStatus === FigureTaskStatus.Generating || speechStatus === SpeechStatus.Processing
                        ? '生成中'
                        : '生成失败'}
                </span>
            </div>
        );
    }
    return null;
};

export default AgentLogoStatus;
