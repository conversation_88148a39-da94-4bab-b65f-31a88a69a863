/**
 * @file 兼容灵感中心的 logo 组件
 * <AUTHOR>
 */

import classNames from 'classnames';
import React, {useRef} from 'react';
import {AgentLogo, LabelValue} from '@/api/agentHistory/interface';
import {AuditStatus} from '@/modules/agentList/interface';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {convertToFillSize} from '@/utils/processImage';
import AgentLogoStatus from './AgentLogoStatus';

const ColorMap = {
    1: ['rgb(214, 242, 255)', 'rgb(0, 170, 255)'],
    2: ['rgb(255, 250, 171)', 'rgb(204, 170, 0)'],
    3: ['rgb(255, 224, 237)', 'rgb(244, 44, 124)'],
    4: ['rgb(232, 230, 255)', 'rgb(87, 71, 255)'],
    5: ['rgb(212, 255, 210)', 'rgb(9, 183, 0)'],
    6: ['rgb(255, 234, 217)', 'rgb(255, 115, 0)'],
};

const DefaultLogoPrefix = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/agent-logo/';

export default function AgentLogoComponent({
    logoText,
    logoUrl,
    isPress,
    width = '28px',
    height = '28px',
    fontSize = '15px',
    packageStatus = AuditStatus.Online,
    speechStatus,
    figureTaskStatus,
}: AgentLogo & {
    width?: string;
    height?: string;
    fontSize?: string;
    packageStatus?: AuditStatus.Online | AuditStatus.Offline;
    speechStatus?: SpeechStatus;
    figureTaskStatus?: FigureTaskStatus;
    isPress?: boolean;
}) {
    const imageRef = useRef<HTMLImageElement>(null);
    const sizeStyle = {
        width,
        height,
        fontSize,
    };

    if (logoUrl && logoUrl !== '') {
        return (
            <div className="relative">
                <div className="h-full w-full overflow-hidden rounded-full" style={sizeStyle}>
                    <img
                        className={classNames('rounded-full', 'bg-white', 'duration-300', 'object-cover', {
                            'group-hover:scale-[1.117]': !isPress,
                        })}
                        // 这里将width和height带px的单位转成数字，且裁剪尺寸为容器宽高的 2 倍，避免图片模糊
                        src={convertToFillSize(logoUrl, parseInt(width, 10) * 2, parseInt(height, 10) * 2)}
                        alt=""
                        style={sizeStyle}
                        ref={imageRef}
                        loading="lazy"
                    ></img>
                </div>

                <AgentLogoStatus
                    packageStatus={packageStatus}
                    speechStatus={speechStatus}
                    figureTaskStatus={figureTaskStatus}
                />
                <div
                    className="absolute left-[0] top-[0] box-border rounded-full border-[1px] border-solid border-black opacity-5"
                    style={{...sizeStyle}}
                ></div>
            </div>
        );
    }

    if (!logoText) {
        return null;
    }

    const {labelType, labelValue, color} = logoText;

    if (labelType === LabelValue.DefaultLogo) {
        return (
            <div className="relative">
                <div className="h-full w-full overflow-hidden rounded-full" style={sizeStyle}>
                    <img
                        className={'rounded-full, bg-white, duration-300 group-hover:scale-[1.117]'}
                        src={`${DefaultLogoPrefix}logo-${labelValue}.png`}
                        alt=""
                        style={sizeStyle}
                        ref={imageRef}
                    ></img>
                </div>
                <AgentLogoStatus
                    packageStatus={packageStatus}
                    speechStatus={speechStatus}
                    figureTaskStatus={figureTaskStatus}
                />
                <div
                    className="absolute left-[0] top-[0] box-border rounded-full border-[2px] border-solid border-[#000] opacity-10"
                    style={{...sizeStyle}}
                ></div>
            </div>
        );
    }

    const [bgColor, textColor] = ColorMap[color] ?? ['#fff', '#000'];

    return (
        <div className="relative">
            <div className="h-full w-full overflow-hidden rounded-full" style={sizeStyle}>
                <span
                    className={classNames(
                        'rounded-full',
                        'flex',
                        'justify-center',
                        'items-center',
                        'font-medium',
                        'leading-snug',
                        'font-pingfang',
                        'group-hover:scale-[1.117]',
                        'duration-300',
                        'bg-white'
                    )}
                    style={{...sizeStyle, backgroundColor: bgColor, color: textColor}}
                    ref={imageRef}
                >
                    {labelValue}
                </span>
            </div>
            <AgentLogoStatus
                packageStatus={packageStatus}
                speechStatus={speechStatus}
                figureTaskStatus={figureTaskStatus}
            />
            <div
                className="absolute left-[0] top-[0] box-border rounded-full border-[2px] border-solid border-[#000] opacity-10"
                style={{...sizeStyle}}
            ></div>
        </div>
    );
}
