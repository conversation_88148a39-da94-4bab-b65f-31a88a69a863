import React, {useEffect, useRef, useState, useCallback} from 'react';
import classNames from 'classnames';
import styled from '@emotion/styled';
import Icon from '@/components/Icon';
import StyledPopover from '@/components/Popover/StyledPopover';

const StyledTag = styled.div`
    cursor: pointer;
    white-space: nowrap;
    border-radius: 0.5rem;
    background-color: #fff;
    padding: 5px 12px;
    &.tab-tag-active {
        font-weight: 500;
        color: rgb(85, 98, 242);
    }
`;

export const Tag = ({
    className,
    active,
    children,
    ...props
}: React.HTMLAttributes<HTMLDivElement> & {active: boolean}) => {
    return (
        <StyledTag
            className={classNames(
                'tab-tag',
                {
                    'tab-tag-active': active,
                },
                className
            )}
            {...props}
        >
            <span className="tag-content">{children}</span>
        </StyledTag>
    );
};

function getTextWidth(text: string, fontSize = 14) {
    // 创建临时元素
    const span = document.createElement('span');
    // 放入文本
    span.innerText = text.repeat(10);
    // span元素转块级
    span.style.position = 'absolute';
    span.style.fontSize = fontSize.toString() + 'px';
    // span放入body中
    document.body.appendChild(span);
    // 获取span的宽度
    const width = span.offsetWidth;
    // 从body中删除该span
    document.body.removeChild(span);
    // 返回span宽度
    return width / 10;
}

type Cache = Record<number, Record<string, number>>;
const getTextWidthWithCache = (text: string, fontSize = 14, cache: Cache) => {
    const fontCache = cache[fontSize] || (cache[fontSize] = {});
    const textWidthCache = fontCache[text] || (fontCache[text] = getTextWidth(text, fontSize));
    return textWidthCache;
};

const Tabs = <K extends React.Key>({
    tabs,
    activeKey,
    onSelect,
    extra,
    ...props
}: {
    tabs: Array<{key: K; label: React.ReactNode; labelName: string}>;
    activeKey: K;
    onSelect: (key: K) => void;
    extra?: React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement>) => {
    const [splitIndex, setSplitIndex] = useState(tabs.length);
    const containerRef = useRef<HTMLDivElement>(null);
    const cacheRef = useRef<Cache>({});
    const resetSplitIndex = useCallback(() => {
        const container = containerRef.current!;
        // 初始宽度视为-8，因为第一个tab没有左边距
        let width = -8;
        let index = 0;
        const maxWidth = container.clientWidth;
        for (const item of tabs) {
            // 32 = 左内边距12 + 右内边距12 + 左侧外边距8
            const itemWidth = 32 + getTextWidthWithCache(item.labelName, 14, cacheRef.current);
            if (itemWidth + width > maxWidth) {
                // 如果剩余的宽度塞不下一个更多按钮，则需要减少一个在上方显示的数量，用于容纳更多按钮
                if (maxWidth - width < 38) {
                    index--;
                }

                break;
            }

            index++;
            width += itemWidth;
        }

        setSplitIndex(index);
    }, [tabs]);

    useEffect(() => {
        resetSplitIndex();
        window.addEventListener('resize', resetSplitIndex);
        return () => window.removeEventListener('resize', resetSplitIndex);
    }, [resetSplitIndex]);

    const handleSelect = useCallback(
        (key: K) => () => {
            onSelect(key);
        },
        [onSelect]
    );

    return (
        <div {...props}>
            <div className="tabs flex flex-grow basis-0 items-center gap-2 overflow-hidden" ref={containerRef}>
                {tabs.slice(0, splitIndex).map(item => (
                    <Tag key={item.key} active={activeKey === item.key} onClick={handleSelect(item.key)}>
                        {item.label}
                    </Tag>
                ))}
                {!!tabs.slice(splitIndex).length && (
                    <StyledPopover
                        rootClassName="[&_.ant-popover-inner]:p-1.5"
                        align={{offset: [12, -12]}}
                        placement="bottomLeft"
                        autoAdjustOverflow={false}
                        arrow={false}
                        content={tabs.slice(splitIndex).map(item => (
                            <div
                                key={item.key}
                                className={classNames(
                                    'min-w-[110px] cursor-pointer rounded-md px-3 py-2 hover:bg-colorBgFormList',
                                    {'font-medium text-primary': activeKey === item.key}
                                )}
                                onClick={handleSelect(item.key)}
                            >
                                {item.label}
                            </div>
                        ))}
                    >
                        <div className="flex h-[30px] w-[30px] flex-shrink-0 cursor-pointer items-center justify-center rounded-full bg-white hover:opacity-50">
                            <Icon name="menu" />
                        </div>
                    </StyledPopover>
                )}
            </div>
            {extra}
        </div>
    );
};

export default Tabs;
