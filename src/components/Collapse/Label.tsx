/**
 * 折叠面板 Label 区
 *
 */

import React, {useMemo, useCallback, ReactNode} from 'react';
import isString from 'lodash/isString';
import classNames from 'classnames';
import {Divider, Tag, Tooltip} from 'antd';

const LABEL_TAG_CONFIG = {
    noFunctionCall: {
        title: '模型不支持',
        tooltip: '当前模型不支持该功能，建议切换其他大模型使用。（若已配置将为您保留数据，但不生效。）',
    },
};

interface LabelProps {
    title: string | ReactNode;
    description: string | ReactNode;

    // 是否展示底部分割线
    bottomDivider?: boolean;
    itemCount?: string;
    requiredMark?: boolean;
    actionArea?: ReactNode;

    active?: boolean;
    collapsible?: boolean;
    labelClassName?: string;
    tagType?: keyof typeof LABEL_TAG_CONFIG;
}

export default function Label({
    title,
    description,
    active = false,
    collapsible = false,
    requiredMark = false,
    bottomDivider = false,
    itemCount,
    actionArea,
    labelClassName,
    tagType,
}: LabelProps) {
    const expandIcon = useMemo(() => {
        if (!collapsible) {
            return null;
        }

        return (
            <span
                className={classNames('iconfont text-base leading-4', {
                    'icon-Up': active,
                    'icon-Down': !active,
                })}
            />
        );
    }, [collapsible, active]);

    const handleActionAreaClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
        event.stopPropagation();
    }, []);

    return (
        <>
            <div
                className={classNames(
                    'flex w-full flex-col gap-[3px] py-3',
                    {
                        'cursor-pointer': collapsible,
                    },
                    labelClassName || ''
                )}
            >
                <div className="flex w-full items-center justify-between">
                    <div className="flex items-center gap-[6px]">
                        {/* 标题 */}
                        {isString(title) ? (
                            <span className="text-base font-medium leading-[22px] text-colorTextDefault">{title}</span>
                        ) : (
                            title
                        )}

                        {/* 统计数量 */}
                        {itemCount && <span className="text-sm text-gray-tertiary">{itemCount}</span>}

                        {/* 是否必填 */}
                        {requiredMark && (
                            <span className=" text-error" style={{fontFamily: 'SimSong'}}>
                                *
                            </span>
                        )}

                        {/* 展开图标 */}
                        {expandIcon}

                        {/* 标签 */}
                        {tagType && (
                            <Tooltip
                                title={LABEL_TAG_CONFIG[tagType]?.tooltip}
                                overlayClassName="text-[13px] max-w-[355px]"
                                placement="bottomLeft"
                            >
                                <Tag className="ml-[6px] text-gray-secondary" bordered={false}>
                                    {LABEL_TAG_CONFIG[tagType]?.title}
                                </Tag>
                            </Tooltip>
                        )}
                    </div>

                    {/* 操作区域 */}
                    <div className="cursor-pointer" onClick={handleActionAreaClick}>
                        {actionArea}
                    </div>
                </div>

                {isString(description) ? (
                    <div className="text-justify text-sm text-gray-tertiary">{description}</div>
                ) : (
                    description
                )}
            </div>

            {bottomDivider && <Divider className="m-0 border-t-[0.5px]" />}
        </>
    );
}
