/**
 * 折叠面板
 *
 */
import React, {ReactNode, useCallback} from 'react';
import classNames from 'classnames';
import {Divider} from 'antd';
import Label from '@/components/Collapse/Label';

interface CollapseProps {
    title: string | ReactNode;
    children: ReactNode;
    description?: string | ReactNode;

    // 是否可展开收起（设置后 active 和 onLabelClick 无效）
    collapsible?: boolean;
    active?: boolean;
    onLabelClick?: () => void;
    requiredMark?: boolean;
    actionArea?: ReactNode;
    itemCount?: string;
    labelClassName?: string;
    bottomDivider?: boolean;
    tagType?: 'noFunctionCall';
    containerRef?: React.RefObject<HTMLDivElement>;
}

export default function Collapse({
    title,
    description,
    children,
    active = false,
    onLabelClick = () => {},
    requiredMark = false,
    collapsible = true,
    itemCount,
    actionArea,
    labelClassName,
    bottomDivider = true,
    tagType,
    containerRef,
}: CollapseProps) {
    const handleLabelClick = useCallback(() => {
        if (!collapsible) {
            return;
        }

        onLabelClick?.();
    }, [onLabelClick, collapsible]);

    const showContent = collapsible && children && active;

    return (
        <div className="w-full" ref={containerRef}>
            <div className="w-full" onClick={handleLabelClick}>
                <Label
                    title={title}
                    description={description}
                    itemCount={itemCount}
                    requiredMark={requiredMark}
                    active={active}
                    collapsible={collapsible}
                    actionArea={actionArea}
                    labelClassName={labelClassName}
                    tagType={tagType}
                />
            </div>

            <div
                className={classNames(
                    'duration-400 transition-all ease-in-out',
                    {'max-h-0 overflow-hidden opacity-0': !showContent},
                    {'max-h-fit pb-3 opacity-100': showContent}
                )}
            >
                {children}
            </div>

            {bottomDivider && <Divider className="m-0 border-t-[0.5px]" />}
        </div>
    );
}
