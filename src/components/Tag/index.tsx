/**
 * 标签组件
 *
 */
import classNames from 'classnames';
import {Tag as AntTag, ConfigProvider, TagProps} from 'antd';
import {tagToken} from '@/styles/component-token';

export default function Tag({className, ...props}: TagProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Tag: tagToken,
                },
            }}
        >
            <AntTag className={classNames('px-[3px] py-[2.5px] font-medium', className)} bordered={false} {...props} />
        </ConfigProvider>
    );
}
