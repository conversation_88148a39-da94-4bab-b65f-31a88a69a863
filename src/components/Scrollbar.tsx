import styled from '@emotion/styled';
import debounce from 'lodash/debounce';
import omit from 'lodash/omit';
import {forwardRef, HtmlHTMLAttributes, ReactNode, useCallback, useRef, useState, UIEvent, LegacyRef} from 'react';

interface ScrollBarProps extends HtmlHTMLAttributes<HTMLDivElement> {
    children: ReactNode;
    delay?: number;
    /** 滚动到底部 */
    onReachBottom?: () => void;
    /** 偏移距离 */
    reachOffset?: number;
}

export const ScrollContainer = styled.div`
    ::-webkit-scrollbar {
        background-color: transparent;
        width: 4px;
    }
    ::-webkit-scrollbar-thumb {
        background-color: #d9d9d9;
        border-radius: 5px;
    }
    &.scrollbar-hidden {
        &::-webkit-scrollbar-thumb {
            background-color: transparent;
        }
    }
`;

const reachBottom = (container: HTMLDivElement, offset: number): boolean => {
    return Math.abs(container.scrollHeight - container.clientHeight - container.scrollTop) < offset;
};

const Scrollbar = forwardRef((props: ScrollBarProps, ref: LegacyRef<HTMLDivElement> | undefined) => {
    const [show, setShow] = useState(false);
    const timer = useRef<NodeJS.Timeout>();

    const delay = props.delay || 1000;

    /** 由于多次触发，进行防抖操作 */
    const debounceTrigger = debounce(() => {
        props.onReachBottom?.();
    }, 100);

    const handleScroll = useCallback(
        (event: UIEvent<HTMLDivElement>) => {
            setShow(true);
            clearTimeout(timer.current);
            timer.current = setTimeout(() => {
                setShow(false);
            }, delay);
            props.onScroll?.(event);
            /** 滚动到底部触发 */
            if (reachBottom(event.target as HTMLDivElement, props.reachOffset || 50)) {
                debounceTrigger();
            }
        },
        [debounceTrigger, delay, props]
    );

    return (
        <ScrollContainer
            {...omit(props, 'onReachBottom')}
            ref={ref}
            className={`${show ? '' : 'scrollbar-hidden'} ${props.className}`}
            onScroll={handleScroll}
        >
            {props.children}
        </ScrollContainer>
    );
});

export default Scrollbar;
