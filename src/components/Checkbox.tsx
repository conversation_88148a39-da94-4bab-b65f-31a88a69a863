import styled from '@emotion/styled';
import {Checkbox, CheckboxProps, ConfigProvider} from 'antd';

const StyledCheckbox = styled(Checkbox)`
    .ant-checkbox-inner:after {
        border-color: white !important;
    }
    .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #5562f2;
        border-width: 0;
    }
    .ant-checkbox-disabled .ant-checkbox-inner {
        opacity: 0.4;
    }
`;
export default function CustomCheckbox(props: CheckboxProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Checkbox: {
                        borderRadius: 9,
                        colorTextDisabled: '#000311',
                    },
                },
                token: {
                    lineWidthBold: 1,
                },
            }}
        >
            <StyledCheckbox {...props} />
        </ConfigProvider>
    );
}
