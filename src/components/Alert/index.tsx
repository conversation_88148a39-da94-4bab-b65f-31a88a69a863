/**
 * @file AlertComponet 单行/多行内联块样式Alert
 * <AUTHOR>
 */

import {InfoCircleOutlined} from '@ant-design/icons';
import {Alert, AlertProps, Space} from 'antd';
import styled from '@emotion/styled';
import classNames from 'classnames';

const StyledAlert = styled(Alert)`
    // alert 操作区 link式button
    .ant-alert-action {
        padding-left: 1.88rem;
        .ant-space {
            gap: 0 !important;
        }
        .ant-space-item:not(:last-child)::after {
            content: '|';
            margin: 0 1rem;
            color: rgba(var(--main-color-base), 0.1);
        }
        .ant-btn.ant-btn-link {
            height: auto;
            padding: 0;
        }
    }
    &.ant-alert {
        align-items: flex-start;
        .ant-alert-icon {
            margin-inline-end: 0.75rem;
            margin-top: 0.25rem;
        }
    }
`;

export default function StandardAlert({className, action, ...props}: AlertProps) {
    return (
        <StyledAlert
            showIcon
            icon={<InfoCircleOutlined />}
            {...props}
            className={classNames('flex w-fit px-4 py-2 text-black', className)}
            action={<Space direction="horizontal">{action}</Space>}
        />
    );
}
