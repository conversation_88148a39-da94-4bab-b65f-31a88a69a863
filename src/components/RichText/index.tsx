/**
 * @file 富文本渲染
 * <AUTHOR>
 */
/* bca-disable */
import DOMPurify from 'dompurify';
import styled from '@emotion/styled';
import {useEffect, useRef} from 'react';
import {convertToAutoFormat} from '@/utils/processImage';

const StyledDiv = styled.div`
    table,
    th,
    td {
        border: 1px solid #ddd;
        border-collapse: collapse;
    }

    /* 无序列表样式 */
    ul {
        list-style-type: disc;
        padding-left: 20px;
    }

    /* 有序列表样式 */
    ol {
        list-style-type: decimal;
        padding-left: 20px;
    }

    a {
        color: #5562f2;
    }
`;

/**
 * 调整图片样式
 *
 * @param img 需要调整样式的图片元素
 */
const adjustImageStyles = (img: HTMLImageElement) => {
    // 更新 img 标签的 src 属性
    const originalSrc = img.getAttribute('src');
    if (originalSrc) {
        img.setAttribute('src', convertToAutoFormat(originalSrc));
    }
};

export default function RichText({className, content}: {className?: string; content?: string}) {
    // 清理富文本内容以防止XSS攻击
    const sanitizedContent = DOMPurify.sanitize(content || '');

    const contentRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (contentRef.current) {
            // 获取所有 a 标签并添加 target 和 rel 属性
            const links = contentRef.current.querySelectorAll('a');
            links.forEach(link => {
                link.setAttribute('target', '_blank');
                link.setAttribute('rel', 'noopener noreferrer');
            });

            // 获取所有 img 标签并调整其src属性
            const images = contentRef.current.querySelectorAll('img');
            images.forEach(adjustImageStyles);
        }
    }, [sanitizedContent]);

    // eslint-disable-next-line react/no-danger
    return <StyledDiv ref={contentRef} className={className} dangerouslySetInnerHTML={{__html: sanitizedContent}} />;
}
