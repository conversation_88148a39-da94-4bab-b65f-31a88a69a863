/**
 * <AUTHOR>
 * @file 做同款按钮，从/CardTemplate/index.tsx中单独抽出来
 */

import React, {useCallback, useLayoutEffect} from 'react';
import {Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useCenterPopover} from '@/store/agent/centerPopover';
import {TagId} from '@/modules/center/utils';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import urls from '@/links';
import {useIsMobileStore} from '@/store/home/<USER>';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {SearchType} from '@/utils/loggerV2/interface';
import {useUniformLogin} from '../Login/utils/uniformLogin';

const StyledContainer = styled.div`
    white-space: nowrap;
    border-radius: 0.375rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-size: 0.75rem;
    line-height: 1;
    color: rgb(85, 98, 242);
    &:hover {
        color: rgb(57, 52, 219);
    }
`;

export default function Copy({
    isHot,
    isSearching,
    appId,
    agentName,
    activeTab,
    allowViewConfigHint = false,
}: {
    isHot?: boolean;
    isSearching?: SearchType;
    title?: string;
    appId?: string;
    agentName?: string;
    activeTab?: number | null;
    allowViewConfigHint?: boolean;
}) {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin();
    const isMobile = useIsMobileStore(store => store.isMobile);

    const {clickLog} = useUbcLogV2();
    const {setShowViewConfigHint} = useCenterPopover(store => ({
        setShowViewConfigHint: store.setShowViewConfigHint,
    }));

    const handleViewConfig = useCallback(
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            e.stopPropagation();

            clickLog(EVENT_VALUE_CONST.EXP_MAKE_TEMPLATE, EVENT_PAGE_CONST.EXPERIENCE, {
                [EVENT_EXT_KEY_CONST.IS_EXP_SEARCH]: isSearching,
                [EVENT_EXT_KEY_CONST.AGENT_ID]: appId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
            });
            // 未登录 先去登录
            if (!isLogin) {
                uniformLogin();
                return;
            }

            if (appId) {
                window.open(urls.agentPromptConfig.fill({id: appId}));
                window.localStorage.setItem('isCardShownViewConfigHint', '1');
                setShowViewConfigHint(false);
            }
        },
        [clickLog, isSearching, appId, agentName, isLogin, uniformLogin, setShowViewConfigHint]
    );

    useLayoutEffect(() => {
        if (activeTab !== TagId.configShare || !allowViewConfigHint) {
            return;
        }

        // 是否展示过“查看配置”的提示文案（点击过“查看配置”或者关闭过提示后，不再展示）
        const shownViewConfigHint = window.localStorage.getItem('isCardShownViewConfigHint') || '0';
        setShowViewConfigHint(!parseInt(shownViewConfigHint, 10));
    }, [activeTab, allowViewConfigHint, setShowViewConfigHint]);

    return (
        <>
            <StyledContainer className="make-same" onClick={handleViewConfig}>
                {isHot && (
                    <Tooltip placement="bottom" title="做同款">
                        <span className="iconfont icon-copy mr-1 text-xs leading-none" />
                    </Tooltip>
                )}

                {!isHot && '做同款'}
            </StyledContainer>

            {/* 移动端“查看配置”的点击热区比按钮大，覆盖卡片右下角 */}
            {isMobile && (
                <div
                    className="absolute bottom-0 right-0 h-[48px] w-[92px] bg-transparent "
                    onClick={handleViewConfig}
                ></div>
            )}
        </>
    );
}
