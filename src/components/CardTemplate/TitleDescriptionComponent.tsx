/**
 * @file 卡片标题和描述组件
 * <AUTHOR>
 *
 */

import React, {useCallback, useMemo} from 'react';
import {Tooltip} from 'antd';
import {css} from '@emotion/css';
import classNames from 'classnames';
import Toast from 'antd-mobile/es/components/toast';

import useTextOverflow from '@/modules/center/hooks/useTextOverflow';
import {AuditStatus} from '@/modules/agentList/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import {CardTag, MedalInfo} from '@/modules/center/interface';
import {convertToFillSize} from '@/utils/processImage';
import {AgentTitleTags} from './AgentTitleTags';

const customTipClassname = css`
    max-width: 268px !important;
`;

const tooltipStyle = css`
    max-width: 100% !important;
    .ant-tooltip-inner {
        border-radius: 12px !important;
    }
`;

const getPopupContainer = (triggerNode: HTMLElement) => triggerNode.parentElement as HTMLElement;

// eslint-disable-next-line complexity
export default function TitleDescriptionComponent({
    title,
    errTip,
    extra,
    description,
    onClick,
    hoverText,
    isMyAgent = false,
    isHot = false,
    isOffLine = AuditStatus.Online,
    tags,
    popupContainerRef,
    agentMedalInfo,
}: {
    title: string;
    errTip?: string;
    extra?: React.ReactNode;
    description: string;
    onClick?: () => void;
    hoverText?: string;
    isMyAgent?: boolean;
    isHot?: boolean;
    isOffLine?: AuditStatus.Online | AuditStatus.Offline;
    tags?: CardTag[];
    popupContainerRef?: React.RefObject<HTMLDivElement>;
    agentMedalInfo?: MedalInfo[] | null;
}) {
    const [titleRef, isTitleFlow] = useTextOverflow();
    const [descriptionRef, isDescriptionFlow] = useTextOverflow();
    const isMobile = useIsMobileStore(store => store.isMobile);

    const titleTooltip = useMemo(() => {
        if (isMobile) {
            return '';
        }

        if (hoverText) {
            return hoverText;
        }

        if (isTitleFlow) {
            return title;
        }
        return '';
    }, [title, hoverText, isMobile, isTitleFlow]);

    const clickErrTip = useCallback(
        (event: React.MouseEvent) => {
            if (isMobile && errTip) {
                Toast.show({content: errTip});
            }

            event.stopPropagation();
            event.preventDefault();
        },
        [errTip, isMobile]
    );

    return (
        <div
            className={classNames(
                {
                    'mb-2 mt-3': isHot,
                    'mb-3 ': isMobile,
                    'mb-4 ': !isMobile && !isHot,
                    'mt-4': !isHot,
                },
                'title-description-wrapper '
            )}
            onClick={onClick}
        >
            <div className="-mt-[7px] flex">
                <div
                    className={classNames(
                        {
                            'opacity-40': isOffLine === AuditStatus.Offline,
                            'max-w-[100%]': !errTip,
                            'max-w-[calc(100%-16px)]': errTip,
                            'justify-center': isHot,
                        },
                        'flex w-full items-center'
                    )}
                >
                    <Tooltip
                        placement="top"
                        title={titleTooltip}
                        arrow={false}
                        autoAdjustOverflow
                        rootClassName={customTipClassname}
                        getPopupContainer={getPopupContainer}
                    >
                        <div
                            className={classNames(
                                'truncate text-base font-semibold leading-[20px] text-black',
                                (agentMedalInfo && agentMedalInfo?.length > 0) || (tags && tags.length > 0)
                                    ? 'max-w-[calc(100%-18px)]'
                                    : 'max-w-[100%]',
                                {
                                    'text-sm font-medium': !isMyAgent && !isHot,
                                }
                            )}
                            ref={titleRef}
                        >
                            {title}
                        </div>
                    </Tooltip>

                    {agentMedalInfo && agentMedalInfo?.length > 0 ? (
                        <div
                            className={classNames('ml-[3px] flex h-[18px] w-[16px] items-center', {'mr-[3px]': errTip})}
                        >
                            <Tooltip
                                key={agentMedalInfo[0].medalId}
                                overlayClassName={tooltipStyle}
                                title={agentMedalInfo[0].name}
                                placement="top"
                            >
                                <img
                                    // 裁剪尺寸为容器宽高的 2 倍，避免图片模糊 （移动端12px和pc端14px差别不大，统一按照14 * 2处理）
                                    src={convertToFillSize(agentMedalInfo[0].level.img, 14 * 2, 14 * 2)}
                                    className={classNames(
                                        isMobile ? 'h-[12px] w-[12px]' : 'h-[14px] w-[14px]',
                                        'cursor-pointer bg-contain'
                                    )}
                                />
                            </Tooltip>
                        </div>
                    ) : (
                        tags &&
                        tags.length > 0 && (
                            <AgentTitleTags
                                tags={tags}
                                tooltipPlacement={isMobile ? 'bottom' : 'top'}
                                cardContainerRef={popupContainerRef}
                            />
                        )
                    )}
                </div>
                {errTip && (
                    <Tooltip
                        placement="top"
                        title={isMobile ? '' : errTip}
                        arrow={false}
                        autoAdjustOverflow
                        rootClassName={customTipClassname}
                        getPopupContainer={getPopupContainer}
                    >
                        <span className="iconfont icon-tip text-error" ref={titleRef} onClick={clickErrTip}></span>
                    </Tooltip>
                )}
            </div>
            <Tooltip
                placement="top"
                title={isDescriptionFlow && !isMobile ? description : ''}
                autoAdjustOverflow
                rootClassName={customTipClassname}
            >
                <div
                    className={classNames('mt-1 text-sm font-normal leading-[1.313rem] text-[#50525C]', {
                        truncate: isMyAgent || isHot,
                        'line-clamp-2 h-[42px] w-full whitespace-normal': !isMyAgent && !extra, // 当没有tag ，最多两行
                        'line-clamp-1  w-full whitespace-normal': !isMyAgent && extra, // 当有tag ，最多一行
                        'opacity-40': isOffLine === AuditStatus.Offline,
                        'line-clamp-1 w-full  whitespace-normal text-center': isHot,
                        'max-w-[268px]': isMyAgent,
                    })}
                    ref={descriptionRef}
                >
                    {description}
                </div>
            </Tooltip>
            <div className="mt-1">{extra}</div>
        </div>
    );
}
