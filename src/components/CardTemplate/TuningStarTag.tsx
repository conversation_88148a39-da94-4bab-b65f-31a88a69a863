/* eslint-disable complexity */
/**
 * @file 调优之星 tag
 * <AUTHOR>
 */

import {Flex, Tooltip} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import {matchPath} from 'react-router-dom';
import {css} from '@emotion/css';
import {TooltipPlacement} from 'antd/es/tooltip';
import urls from '@/links';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {TagsUsedBy} from '@/modules/center/interface';

export function TuningStarTag({
    latest = false,
    tooltipPlacement,
    cardContainerRef,
    scrollContainerRef,
    usedBy,
}: {
    expiredDays?: number;
    latest?: boolean;
    tooltipPlacement?: TooltipPlacement;
    cardContainerRef?: React.RefObject<HTMLDivElement>;
    scrollContainerRef?: React.RefObject<HTMLDivElement>;
    usedBy?: TagsUsedBy;
}) {
    const {showLog} = useUbcLogV3();

    const [showPopover, setShowPopover] = useState(false);
    // 是否需要首次自动弹窗，首次自动弹窗样式和hover弹窗样式不同
    const [isShowLatest, setIsShowLatest] = useState(false);

    // 是否是智能体商店页面调用
    const isFormAgentShop = !!matchPath(urls.center.raw(), location.pathname);

    const closePopover = useCallback(
        (manualClose?: boolean) => {
            // 首次自动弹窗需要手动关闭
            if (!manualClose && isShowLatest) {
                return;
            }

            setShowPopover(false);
            // 弹窗关闭有渐隐动效，延时设置首次弹窗的类型，否则会存在未关闭前弹窗箭头颜色已改变
            setTimeout(() => {
                setIsShowLatest(false);
            }, 300);
        },
        [isShowLatest]
    );

    useEffect(() => {
        if (!latest || isFormAgentShop) return;

        setIsShowLatest(true);

        // 避免前面的内容没渲染完，先渲染了此组件，导致popover位置出现弹跳问题，所以这里加了个延时。
        setTimeout(() => {
            setShowPopover(true);
        }, 1000);
    }, [latest, isFormAgentShop]);

    useEffect(() => {
        showLog(EVENT_VALUE_CONST.OPTIMISE_STAR_TAG);
    }, [showLog]);

    const placement =
        usedBy === TagsUsedBy.Header
            ? tooltipPlacement || 'bottomLeft'
            : usedBy === TagsUsedBy.AgentCard && !isShowLatest
            ? 'top'
            : tooltipPlacement || 'bottomLeft';

    const align = placement === 'top' ? {offset: [0, -12]} : {offset: [-12, 8]};

    const getPopupContainer = useCallback(
        () => (placement === 'top' ? scrollContainerRef?.current! : cardContainerRef?.current!),
        [cardContainerRef, placement, scrollContainerRef]
    );

    return (
        <Tooltip
            placement={placement}
            rootClassName={css`
                max-width: ${isShowLatest ? 303 : 404}px !important;
            `}
            overlayInnerStyle={{borderRadius: '12px', padding: '10px 12px'}}
            color={isShowLatest ? '#5562F2' : '#1e1f24f2'}
            open={showPopover}
            // 隐藏ToolTip回调
            onOpenChange={closePopover}
            getPopupContainer={getPopupContainer}
            autoAdjustOverflow={false}
            align={align}
            title={
                <div className="whitespace-break-spaces text-sm font-normal">
                    {/* 智能体商店页面调用, 展示的弹窗内容样式 || 非首次弹窗展示的内容样式 */}
                    {isFormAgentShop || !isShowLatest ? (
                        `调优之星：智能体调优后质量提升`
                    ) : (
                        // 首次展示的弹窗内容样式
                        <div>
                            <Flex justify="space-between">
                                <div>
                                    <p>
                                        祝贺！您的智能体经过调优质量提升，已获得「调优之星」称号，并在智能体首页最新发布频道获得优先推荐！{' '}
                                    </p>
                                    <p className="text-right">
                                        <a
                                            href="https://agents.baidu.com/community/article-detail.html?articleId=1998"
                                            target="_blank"
                                            className="font-medium text-white hover:opacity-80"
                                            rel="noreferrer"
                                        >
                                            查看详情
                                        </a>
                                    </p>
                                </div>
                                <div className="-mt-[2px] ml-3">
                                    <span
                                        className="iconfont icon-close cursor-pointer text-sm text-white/70 hover:opacity-80"
                                        onClick={e => {
                                            closePopover(true);
                                            e.stopPropagation();
                                        }}
                                    ></span>
                                </div>
                            </Flex>
                        </div>
                    )}
                </div>
            }
        >
            <img
                className={`${usedBy === TagsUsedBy.AgentCard ? 'mr-[3px] last:mr-0' : 'ml-[3px]'} h-[14px] w-[14px]`}
                src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/tuning_star.png"
                onMouseEnter={() => {
                    setShowPopover(true);
                    showLog(EVENT_VALUE_CONST.OPTIMISE_STAR_HOVER);
                }}
            />
        </Tooltip>
    );
}
