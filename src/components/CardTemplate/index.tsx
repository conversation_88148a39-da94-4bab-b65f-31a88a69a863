/**
 * @file 卡片模板组件（体验中心和我的智能体页面复用)
 * <AUTHOR>
 *
 */
import React, {useState} from 'react';
import {Card} from 'antd';
import styled from '@emotion/styled';
import classNames from 'classnames';
import AgentLogoComponent from '@/components/AgentLogo';
import {CardData} from '@/modules/center/interface';
import {AgentData} from '@/modules/agentList/interface';
import {Logo} from '@/modules/agentList/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {ExperienceTab} from '@/utils/loggerV2/interface';

const AgentLogoSize = {
    width: '68px',
    height: '68px',
    fontSize: '19px',
};
const CenterAgentLogoSize = {
    width: '56px',
    height: '56px',
};

const CardContainer = styled.div`
    .mt-8 {
        margin-top: 12px !important;
    }
`;

export interface MouseEventType {
    onmouseup: () => void;
    onmousedown: () => void;
    onmouseenter: () => void;
    onmouseleave: () => void;
}

interface Props {
    card?: CardData | AgentData;
    title: React.ReactNode;
    children?: React.ReactNode;
    logo?: Logo;
    speechStatus?: SpeechStatus;
    figureTaskStatus?: FigureTaskStatus;
    headCursorStyle?: string;
    isMyAgent?: boolean;
    isDiscovery?: boolean;
    /** Card Head 高度自适应 logo和title齐平 */
    cardHeadHeightAuto?: boolean;
    /** 页面tag打点value */
    tagLogName?: ExperienceTab;
}

// eslint-disable-next-line complexity
function CardTemplate({
    card,
    title,
    children,
    logo,
    speechStatus,
    figureTaskStatus,
    headCursorStyle = 'pointer',
    isMyAgent = false,
    cardHeadHeightAuto = false,
}: Props) {
    const [isHover, setIsHover] = useState(false);
    const [isPress, setIsPress] = useState(false);
    const isMobile = useIsMobileStore(store => store.isMobile);

    const handleMouseDown = () => {
        setIsHover(false);
        setIsPress(true);
    };

    const handleMouseUp = () => {
        setIsPress(false);
    };

    const handleMouseEnter = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
        setIsHover(true);
    };

    const handleMouseLeave = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
        setIsHover(false);
        setIsPress(false);
    };

    return (
        // 卡片整体高度固定，宽度跟随布局变化
        <div className="agent-card group relative ">
            {/* 顶部头像 */}
            <div
                className={classNames(
                    {
                        'top-[3px]': isMobile,
                        'agent-card-wise': isMobile,
                    },
                    // FIXME: z-[1]为了避免头像被蒙板遮挡，重构卡片布局，去掉z-index设置
                    'absolute z-[1] -translate-y-[-12px] translate-x-[12px] transform   rounded-full border-[#000000] transition duration-200 group-hover:shadow-card-selected'
                )}
            >
                <AgentLogoComponent
                    width={isMyAgent ? AgentLogoSize.width : CenterAgentLogoSize.width}
                    height={isMyAgent ? AgentLogoSize.height : CenterAgentLogoSize.width}
                    fontSize={AgentLogoSize.fontSize}
                    logoText={card?.logoText}
                    logoUrl={card?.logoUrl || logo?.logoUrl}
                    packageStatus={(card as CardData)?.packageStatus}
                    figureTaskStatus={figureTaskStatus}
                    speechStatus={speechStatus}
                    isPress={isPress}
                />
            </div>
            {/* 卡片主体 */}
            <CardContainer>
                <Card
                    className={classNames(
                        {
                            'bg-[rgba(255,255,255,0.8)] hover:shadow-card-selected ': isHover && !isMyAgent,
                            'h-[125px]': !isMyAgent,
                            'bg-[#ebecef]': isPress && !isMyAgent,
                        },
                        'rounded-xl font-pingfang shadow-none transition duration-200'
                    )}
                    // 卡片标题和描述
                    title={title}
                    headStyle={{
                        cursor: headCursorStyle,
                        justifyContent: 'start',
                        borderBottom: 0,
                        paddingLeft: isMyAgent ? 92 : 76,
                        paddingRight: 12,
                        paddingTop: isMyAgent && !cardHeadHeightAuto ? 0 : 4,
                        height: isMyAgent && !cardHeadHeightAuto ? 92 : '',
                    }}
                    bodyStyle={{
                        padding: 0,
                    }}
                    bordered={false}
                    // eslint-disable-next-line react/jsx-no-bind
                    onMouseDown={handleMouseDown}
                    // eslint-disable-next-line react/jsx-no-bind
                    onMouseUp={handleMouseUp}
                    // eslint-disable-next-line react/jsx-no-bind
                    onMouseEnter={handleMouseEnter}
                    // eslint-disable-next-line react/jsx-no-bind
                    onMouseLeave={handleMouseLeave}
                    // eslint-disable-next-line react/jsx-no-bind
                    onTouchStart={handleMouseDown}
                    // eslint-disable-next-line react/jsx-no-bind
                    onTouchEnd={handleMouseUp}
                >
                    {/* 卡片内容 / 操作区 */}
                    {children}
                </Card>
            </CardContainer>
        </div>
    );
}

const CardTemplateWrapper = (props: Props) => {
    return (
        <LogContextProvider ext={{expTabName: props.tagLogName}}>
            <CardTemplate {...props} />
        </LogContextProvider>
    );
};

export default CardTemplateWrapper;
