/**
 * @file 标题头衔标签
 * <AUTHOR>
 */
import {TooltipPlacement} from 'antd/es/tooltip';
import {TagsUsedBy, CardTag, CardTagName} from '@/modules/center/interface';
import {TuningStarTag} from './TuningStarTag';

export function AgentTitleTags({
    tags,
    tooltipPlacement,
    usedBy,
    cardContainerRef,
    scrollContainerRef,
}: {
    tags: CardTag[];
    tooltipPlacement?: TooltipPlacement;
    usedBy?: TagsUsedBy;
    cardContainerRef?: React.RefObject<HTMLDivElement>;
    scrollContainerRef?: React.RefObject<HTMLDivElement>;
}) {
    return (
        <>
            {tags.map(tag => (
                <div key={tag.name}>
                    {tag.name === CardTagName.tuningStar ? (
                        <TuningStarTag
                            expiredDays={tag.expiredDays}
                            latest={tag.newTag}
                            tooltipPlacement={tooltipPlacement}
                            cardContainerRef={cardContainerRef}
                            scrollContainerRef={scrollContainerRef}
                            usedBy={usedBy}
                        />
                    ) : (
                        <div className="ml-1 rounded-[3px] bg-[#FF82001A] p-[3px] text-xs font-medium leading-none text-[#E87400]">
                            {tag.name}
                        </div>
                    )}
                </div>
            ))}
        </>
    );
}
