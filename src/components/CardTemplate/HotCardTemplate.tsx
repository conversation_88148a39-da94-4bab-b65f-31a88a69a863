/**
 * @file 热榜卡片模板组件，逻辑与/CardTemplate/index.tsx相同，主要是dom结构和样式改变
 * <AUTHOR>
 *
 */
import React, {useCallback, useState} from 'react';
import {Card} from 'antd';
import styled from '@emotion/styled';
import classNames from 'classnames';
import AgentLogoComponent from '@/components/AgentLogo';
import {CardData} from '@/modules/center/interface';
import {AgentData} from '@/modules/agentList/interface';
import {Logo} from '@/modules/agentList/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';

const AgentLogoSize = {
    width: '65px',
    height: '65px',
    fontSize: '19px',
};

const CardContainer = styled.div`
    .mt-8 {
        margin-top: 12px !important;
    }
    .ant-card {
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: none !important;
    }
    .ant-card:hover {
        background-color: rgba(255, 255, 255, 1);
    }
`;

export interface MouseEventType {
    onmouseup: () => void;
    onmousedown: () => void;
    onmouseenter: () => void;
    onmouseleave: () => void;
}

// eslint-disable-next-line complexity
export default function CardTemplate({
    card,
    title,
    children,
    logo,
    speechStatus,
    figureTaskStatus,
    headCursorStyle = 'pointer',
    bgColor,
    cardHeadHeightAuto = false,
}: {
    card?: CardData | AgentData;
    title: React.ReactNode;
    children?: React.ReactNode;
    logo?: Logo;
    speechStatus?: SpeechStatus;
    figureTaskStatus?: FigureTaskStatus;
    headCursorStyle?: string;
    bgColor?: string;
    /** Card Head 高度自适应 logo和title齐平 */
    cardHeadHeightAuto?: boolean;
}) {
    const [isHover, setIsHover] = useState(false);
    const [isPress, setIsPress] = useState(false);
    const isMobile = useIsMobileStore(store => store.isMobile);

    const handleMouseDown = useCallback(() => {
        setIsHover(false);
        setIsPress(true);
    }, []);

    const handleMouseUp = useCallback(() => {
        setIsPress(false);
    }, []);

    const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
        setIsHover(true);
    }, []);

    const handleMouseLeave = useCallback((event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
        setIsHover(false);
        setIsPress(false);
    }, []);

    return (
        // 卡片整体高度固定，宽度跟随布局变化
        <div
            className={classNames(
                'agent-card-hot group flex h-[213px] w-full flex-col justify-center justify-items-center  hover:shadow-card-selected',
                {
                    'bg-opacity-80': isHover && !isMobile,
                    ' active:bg-[#e7eaed]': isPress && isMobile,
                    'agent-card-wise': isMobile,
                },
                bgColor,
                'rounded-xl font-pingfang shadow-none transition duration-200'
            )}
        >
            {/* 顶部头像 */}
            <div className={classNames('mx-auto mt-[6px]  rounded-full border-[#000000] transition duration-200')}>
                <AgentLogoComponent
                    width={AgentLogoSize.width}
                    height={AgentLogoSize.height}
                    fontSize={AgentLogoSize.fontSize}
                    logoText={card?.logoText}
                    logoUrl={card?.logoUrl || logo?.logoUrl}
                    packageStatus={(card as CardData)?.packageStatus}
                    figureTaskStatus={figureTaskStatus}
                    speechStatus={speechStatus}
                    isPress={isPress}
                />
            </div>

            {/* 卡片主体 */}
            <CardContainer>
                <Card
                    // 卡片标题和描述
                    className={classNames(
                        {
                            'h-[119px]': !isMobile,
                        },
                        'border-transparent bg-transparent'
                    )}
                    title={title}
                    headStyle={{
                        cursor: headCursorStyle,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderBottom: 0,
                        paddingTop: cardHeadHeightAuto ? 4 : 12,
                        height: cardHeadHeightAuto ? '' : 'auto',
                        paddingLeft: 12,
                        paddingRight: 12,
                    }}
                    bodyStyle={{
                        padding: 0,
                    }}
                    bordered={false}
                    onMouseDown={handleMouseDown}
                    onMouseUp={handleMouseUp}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    onTouchStart={handleMouseDown}
                    onTouchEnd={handleMouseUp}
                >
                    {/* 卡片内容 / 操作区 */}
                    {children}
                </Card>
            </CardContainer>
        </div>
    );
}
