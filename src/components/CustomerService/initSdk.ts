/**
 * @file 智能客服sdk实例化，对外暴露sdk实例
 * 智能客服sdk设计文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/5mAiFsK5ix/0BPp0mepCFWdbo
 * 2.20 新版智能客服文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/PuEmJr42JI/5gTFam7I2tm53A
 * <AUTHOR>
 */

import {init} from '@baidu/ai-docs-bot';
import logo from '@/assets/ai-customer-service-logo.png';
import {Z_INDEX_MAP} from '@/styles/z-index';

// interface CloseBotParams {
//     enableAnimation?: boolean; // 是否展示关闭动画
// }

// interface OpenBotParams {
//     noticeId: string; // 调用openBot的时候，如果有红点则传入noticeId，否则传入的noticeId为空值
// }

// interface AiBotSdk {
//     closeBot: (params?: CloseBotParams) => void; // 隐藏对话容器
//     destroy: () => void; // 销毁，清除 SDK 的 DOM
//     onBotCloseClick: (callback: () => void) => void; // bot 可视状态发生变化时触发
//     onBotStatusChange: (callback: (status: 'open' | 'close' | 'closing') => void) => void; // bot 头部关闭事件监听
//     openBot: (params: ChangeBotVisibleParams | undefined) => void; // 展示对话容器
//     sendMessage: () => void; // 在当前对话流中消息发送消息
//     setContentStyle: (style: string) => void; // 修改 bot 布局相关的样式
//     setTriggerStyle: (style: string) => void; // 修改触发按钮（页面浮标，唤起 bot 的入口）样式
//     onLogin: (callback: () => void) => void; // 登录触发监听
//     setGuideBubbleStatus: (status?: boolean) => void; // 更新是否允许 引导气泡 展示状态
//     setGuideBubbleStyle: (style: string) => void; // 不同页面更改气泡样式
// }

let aiSdkInstance: ReturnType<typeof init> | null = null;

export function getAiSdk() {
    if (!aiSdkInstance) {
        aiSdkInstance = init({
            productId: 'lingjing',
            name: '智能客服',
            logo: logo,
            guideBubbleStyle: 'top:67px;right:16px',
            contentStyle: `z-index: ${Z_INDEX_MAP.custService}`,
            source: 'lingjing',
            showTrigger: false,
            customerServiceUrl:
                'https://zhiqiu.baidu.com/imcswebchat/pc/index.html?id=49266&token=n8l70krf1uuk6jubd43f6gjelcfh4l07&domainID=lingjing&type=2',
            showContent: false,
            enableWatermark: false,
            enableNotice: false,
        });
    }

    return aiSdkInstance;
}
