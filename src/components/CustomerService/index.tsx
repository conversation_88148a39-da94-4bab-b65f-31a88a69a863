/**
 * @file 智能客服动态按钮
 * <AUTHOR>
 */
import React, {useCallback, useState} from 'react';
import 'tippy.js/dist/tippy.css';
import classNames from 'classnames';
import {useLocation} from 'react-router-dom';
import logoGif from '@/assets/ai-customer-service-animation.gif';
import logoStatic from '@/assets/ai-customer-service-logo.png';
import {getAiSdk} from '@/components/CustomerService/initSdk';
import {removeParameter} from '@/components/Login/hooks/useAccountNavigation';
import urls from '@/links';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import StyledPopover from '../Popover/StyledPopover';

// 是否是零代码对话创建header
interface Props {
    isBuilderContainer?: boolean;
    className?: string;
    onQRCodeHoverOpen?: () => void;
    onTipsShow?: () => void;
    onAiBotOpen?: () => void;
}

const CustomerService: React.FC<Props> = ({isBuilderContainer = false, className, onQRCodeHoverOpen, onAiBotOpen}) => {
    const [isHover, setIsHover] = useState(false);
    const {ubcClickLog} = useUbcLog();
    const location = useLocation();

    const onMouseEnter = useCallback(() => {
        setIsHover(true);
        onQRCodeHoverOpen?.();
    }, [onQRCodeHoverOpen]);

    const onMouseLeave = useCallback(() => {
        setIsHover(false);
    }, []);

    const AiSdk = getAiSdk();

    // 打开智能客服
    const openAiBot = useCallback(() => {
        AiSdk.openBot({
            // noticeId: '',
        });

        let logEvent = '';
        if (isBuilderContainer) {
            /* 零代码 */
            logEvent = EVENT_TRACKING_CONST.AgentPromptCustomerServiceBtn;
        } else if (location.pathname.includes(removeParameter(urls.agentFlowEdit.raw()))) {
            /* 低代码 */
            logEvent = EVENT_TRACKING_CONST.AgentFlowCustomerServiceBtn;
        } else if (location.pathname.includes(urls.pluginAbilityList.raw())) {
            /* 能力插件 */
            logEvent = EVENT_TRACKING_CONST.AbilitypluginCustomerServiceBtn;
        } else if (location.pathname.includes(urls.pluginDataList.raw())) {
            /* 数据插件 */
            logEvent = EVENT_TRACKING_CONST.DatapluginCustomerServiceBtn;
        } else if (location.pathname.includes(urls.dataset.raw())) {
            /* 知识库 */
            logEvent = EVENT_TRACKING_CONST.DatasetCustomerServiceBtn;
        }

        ubcClickLog(logEvent);

        onAiBotOpen?.();
    }, [isBuilderContainer, ubcClickLog, onAiBotOpen, AiSdk, location]);

    return (
        <StyledPopover
            zIndex={2000}
            content={
                <div className="flex flex-col items-center p-2">
                    <div className="w-[192px] text-justify text-sm text-colorTextDefault">
                        遇到问题了吗？点击智能客服进行24小时在线咨询吧！
                    </div>

                    {/* <div className="mt-3 flex justify-center">
                        <img className="h-[120px] w-[120px]" src={CommunityQRCode} />
                    </div>
                    <div className="mt-[9px] py-[1px] text-xs text-gray-tertiary">微信扫码进群，有专人解答问题哦～</div> */}
                </div>
            }
        >
            <div
                className={classNames(
                    'hover:cursor-pointer',
                    {
                        'w-[26px]': isBuilderContainer,
                        'w-[30px]': !isBuilderContainer,
                    },
                    className
                )}
                onClick={openAiBot}
            >
                <img
                    src={isHover ? logoGif : logoStatic}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    alt="智能客服"
                />
            </div>
        </StyledPopover>
    );
};

export default CustomerService;
