/**
 * @file 智能客服浮窗
 * <AUTHOR>
 */
import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {useLocation} from 'react-router-dom';
import logoGif from '@/assets/ai-customer-service-animation.gif';
import logoStatic from '@/assets/ai-customer-service-logo.png';
import {getAiSdk} from '@/components/CustomerService/initSdk';
import api from '@/api/customerService';
import urls from '@/links';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST, EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {MyAgentType, ServiceCloseType} from '@/utils/loggerV2/interface';
import {AgentTabType} from '@/modules/agentList/interface';
import {DatasetManageTabsExt} from '@/modules/dataset/utils';
import {LJExtData} from '@/utils/logger';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {removeParameter} from '@/components/Login/hooks/useAccountNavigation';
import StyledPopover from '../Popover/StyledPopover';

const CustomerServiceFloat: React.FC = () => {
    const [visible, setVisible] = useState(false); // 浮窗是否展示
    const [hover, setHover] = useState(false); // 图标hover
    const [tipOpen, setTipOpen] = useState(false); // 提示气泡
    const [menuOpen, setMenuOpen] = useState(false); // 隐藏弹窗
    const {showLog, clickLog} = useUbcLogV3();

    // 初始化时调接口，决定是否展示浮窗
    useEffect(() => {
        api.getCustomerServiceFloat()
            .then(res => {
                setVisible(!!(res && res.show));
            })
            .catch(() => setVisible(false));
    }, []);

    // 智能客服浮窗展现埋点
    useEffect(() => {
        if (visible) {
            showLog(EVENT_VALUE_CONST.FLOAT_SERVICE);
        }
    }, [showLog, visible]);

    // 点击x时弹出隐藏选项弹窗
    const handleCloseClick = useCallback(
        (e: React.MouseEvent) => {
            e.stopPropagation();
            setMenuOpen(true);
            setTipOpen(false);
            clickLog(EVENT_VALUE_CONST.FLOAT_SERVICE_CLOSE);
        },
        [clickLog]
    );

    // 点击“隐藏直到下次访问”
    const handleHideOnce = useCallback(() => {
        setVisible(false);
        setMenuOpen(false);
        clickLog(EVENT_VALUE_CONST.FLOAT_SERVICE_HIDE, {
            [EVENT_EXT_KEY_CONST.HIDE_TYPE]: ServiceCloseType.HIDE_ONCE,
        } as LJExtData);
    }, [clickLog]);

    // 点击“近30天内不再展示”
    const handleHide30Days = useCallback(async () => {
        setVisible(false);
        setMenuOpen(false);
        await api.hideCustomerServiceFloat();
        clickLog(EVENT_VALUE_CONST.FLOAT_SERVICE_HIDE, {
            [EVENT_EXT_KEY_CONST.HIDE_TYPE]: ServiceCloseType.HIDE_30_DAYS,
        } as LJExtData);
    }, [clickLog]);

    // 点击浮窗图标
    const handleIconClick = useCallback(() => {
        setMenuOpen(false);
        const aiSdk = getAiSdk();
        aiSdk.openBot({});
        clickLog(EVENT_VALUE_CONST.FLOAT_SERVICE);
    }, [clickLog]);

    if (!visible) {
        return null;
    }

    return (
        <div className="fixed bottom-[20px] right-[30px] z-[1000]">
            {/* 提示气泡 */}
            <StyledPopover
                open={tipOpen}
                placement="topRight"
                content={
                    <div className="flex items-center justify-between">
                        <span>Hi，我是智能客服。</span>
                        <span
                            onClick={handleCloseClick}
                            className="iconfont icon-close ml-2 cursor-pointer"
                            aria-label="关闭"
                        />
                    </div>
                }
                trigger="hover"
                onOpenChange={setTipOpen}
                zIndex={1001}
            >
                {/* 隐藏选项弹窗 */}
                <StyledPopover
                    open={menuOpen}
                    placement="topRight"
                    content={
                        <div className="flex flex-col items-stretch">
                            <button
                                className="px-4 py-2 text-left text-[14px] hover:text-primary"
                                onClick={handleHideOnce}
                            >
                                隐藏直到下次访问
                            </button>
                            <button
                                className="px-4 py-2 text-left text-[14px] hover:text-primary"
                                onClick={handleHide30Days}
                            >
                                近30天内不再展示
                            </button>
                            <div className="my-2 h-px bg-gray-200" />
                            <div className="pt-1 text-center text-xs text-gray-400">可以在左栏“服务空间”找到我喔</div>
                        </div>
                    }
                    trigger="click"
                    onOpenChange={setMenuOpen}
                    zIndex={1002}
                >
                    <div
                        onMouseEnter={() => setHover(true)}
                        onMouseLeave={() => setHover(false)}
                        onClick={handleIconClick}
                        className="h-[40px] w-[40px] cursor-pointer rounded-full"
                    >
                        <img src={hover ? logoGif : logoStatic} alt="智能客服" />
                    </div>
                </StyledPopover>
            </StyledPopover>
        </div>
    );
};

// 路由到埋点 page 常量的映射
// eslint-disable-next-line complexity
const getPageConstByPath = (pathname: string): EVENT_PAGE_CONST | undefined => {
    if (pathname === urls.center.raw()) return EVENT_PAGE_CONST.EXPERIENCE;
    if (pathname === urls.centerPlugin.raw()) return EVENT_PAGE_CONST.PLUGIN_LIST;
    if (pathname === urls.account.raw()) return EVENT_PAGE_CONST.ACCOUNT_CENTER;
    const agentListPrefix = removeParameter(urls.agentList.raw());
    if (pathname.startsWith(agentListPrefix)) {
        // /agent/list/codeless 或 /agent/list/workflow
        const tab = pathname.slice(agentListPrefix.length + 1);
        if (tab === AgentTabType.Codeless || tab === AgentTabType.Workflow) return EVENT_PAGE_CONST.MY_AGENT;
    }

    if (pathname === urls.datasetList.raw()) return EVENT_PAGE_CONST.MY_REPOSITORY;
    if (pathname.startsWith(removeParameter(urls.datasetFileDetail.raw()))) return EVENT_PAGE_CONST.MANAGE_REPONSITORY;
    if (pathname.startsWith(removeParameter(urls.datasetParagraphDetail.raw())))
        return EVENT_PAGE_CONST.MANAGE_REPONSITORY;
    if (pathname === urls.plugin.raw()) return EVENT_PAGE_CONST.MY_PLUGIN;
    if (pathname === urls.workflowList.raw()) return EVENT_PAGE_CONST.MY_WORKFLOW;
    if (pathname === urls.income.raw()) return EVENT_PAGE_CONST.MY_INCOME;
    return undefined;
};

const CustomerServiceFloatWithLogContext: React.FC = () => {
    const location = useLocation();
    // 计算埋点 page 和 ext，展现和点击复用
    const page = useMemo(() => getPageConstByPath(location.pathname), [location.pathname]);
    const logExt = useMemo(() => {
        let ext = {};
        // 埋点：我的智能体页面，区分零代码/工作流模式
        if (page === EVENT_PAGE_CONST.MY_AGENT) {
            const agentListPrefix = removeParameter(urls.agentList.raw());
            const tab = location.pathname.slice(agentListPrefix.length + 1);
            if (tab === AgentTabType.Codeless) {
                ext = {[EVENT_EXT_KEY_CONST.MY_AGENT_TYPE]: MyAgentType.CODELESS};
            } else if (tab === AgentTabType.Workflow) {
                ext = {[EVENT_EXT_KEY_CONST.MY_AGENT_TYPE]: MyAgentType.WORKFLOW};
            }
        }

        // 埋点：知识库文件管理页面，区分文件管理/分段管理
        if (page === EVENT_PAGE_CONST.MANAGE_REPONSITORY) {
            if (location.pathname.startsWith(removeParameter(urls.datasetFileDetail.raw()))) {
                ext = {[EVENT_EXT_KEY_CONST.P_REPOSITORY_MANAGE_TAB]: DatasetManageTabsExt.FILE};
            } else if (location.pathname.startsWith(removeParameter(urls.datasetParagraphDetail.raw()))) {
                ext = {[EVENT_EXT_KEY_CONST.P_REPOSITORY_MANAGE_TAB]: DatasetManageTabsExt.PARAGRAPH};
            }
        }
        return ext;
    }, [page, location.pathname]);

    return (
        <LogContextProvider page={page} ext={logExt}>
            <CustomerServiceFloat />
        </LogContextProvider>
    );
};

export default CustomerServiceFloatWithLogContext;
