import {useCallback, useState} from 'react';
import {Button} from 'antd';
import ImageSelect from '@/modules/agentPromptEditV2/pc/TuningTab/components/ImageSelect';
import CustomPopover from './Popover';
import PopoverContainer from './Popover/Container';

const content = '开发中的插件可以邀请其他用户试用啦啦快去尝试分享吧～';
const title = '标题文字一行就可以了';

const PopoverDemo = () => {
    const [open, setOpen] = useState(true);
    const handleClose = useCallback(() => {
        setOpen(false);
    }, []);
    const handleOpen = useCallback(() => {
        setOpen(true);
    }, []);
    return (
        <PopoverContainer className="flex h-screen w-screen flex-col items-center justify-center gap-y-48 overflow-y-auto p-10">
            <div className="flex gap-40 [&>div]:flex [&>div]:h-8 [&>div]:w-40 [&>div]:items-center [&>div]:justify-center [&>div]:p-10">
                <div>
                    <CustomPopover type="default" content={content} placement="top" open>
                        <span className="whitespace-nowrap">普通气泡 type=&quot;default&quot;</span>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover type="primary" content={content} placement="top" open>
                        <span className="whitespace-nowrap">普通气泡 type=&quot;primary&quot;</span>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover type="default" content={content} placement="top" open title={title}>
                        <span className="whitespace-nowrap">普通气泡 type=&quot;default&quot;</span>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover type="primary" content={content} placement="top" open title={title}>
                        <span className="whitespace-nowrap">普通气泡 type=&quot;primary&quot;</span>
                    </CustomPopover>
                </div>
            </div>
            <div className="flex gap-40 [&>div]:flex [&>div]:h-4 [&>div]:w-40 [&>div]:items-center [&>div]:justify-center [&>div]:p-10">
                <div>
                    <CustomPopover
                        type="default"
                        content={content}
                        placement="top"
                        open
                        footer={
                            <Button type="link" className="pr-0 font-medium">
                                文字按钮
                            </Button>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="primary"
                        content={content}
                        placement="top"
                        open
                        footer={
                            <Button type="link" className="pr-0 font-medium  text-white">
                                文字按钮
                            </Button>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="default"
                        content={content}
                        placement="top"
                        open
                        title={title}
                        footer={
                            <Button type="link" className="pr-0  font-medium">
                                文字按钮
                            </Button>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="primary"
                        content={content}
                        placement="top"
                        open
                        title={title}
                        footer={
                            <Button type="link" className="pr-0 font-medium text-white">
                                文字按钮
                            </Button>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
            </div>
            <div className="flex gap-40 [&>div]:flex [&>div]:h-4 [&>div]:w-40 [&>div]:items-center [&>div]:justify-center [&>div]:p-10">
                <div>
                    <CustomPopover type="default" content={content} placement="top" open={open} onClose={handleClose}>
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover type="primary" content={content} placement="top" open={open} onClose={handleClose}>
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="default"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        title={title}
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="primary"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        title={title}
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
            </div>
            <div className="flex gap-40 [&>div]:flex [&>div]:h-4 [&>div]:w-40 [&>div]:items-center [&>div]:justify-center [&>div]:p-10">
                <div>
                    <CustomPopover
                        type="default"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        footer={
                            <div>
                                <Button>次按钮</Button>
                                <Button type="primary">主按钮</Button>
                            </div>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="primary"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        footer={
                            <div>
                                <Button type="primary" className="border border-gray-border bg-transparent">
                                    次按钮
                                </Button>
                                <Button>主按钮</Button>
                            </div>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="default"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        title={title}
                        footer={
                            <div>
                                <Button>次按钮</Button>
                                <Button type="primary">主按钮</Button>
                            </div>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;default&quot;;</span>
                        </Button>
                    </CustomPopover>
                </div>
                <div>
                    <CustomPopover
                        type="primary"
                        content={content}
                        placement="top"
                        open={open}
                        onClose={handleClose}
                        title={title}
                        footer={
                            <div>
                                <Button type="primary" className="border border-gray-border bg-transparent">
                                    次按钮
                                </Button>
                                <Button>主按钮</Button>
                            </div>
                        }
                    >
                        <Button onClick={handleOpen}>
                            <span className="whitespace-nowrap">可关闭气泡 type=&quot;primary&quot;</span>
                        </Button>
                    </CustomPopover>
                </div>
            </div>
        </PopoverContainer>
    );
};

const CollapseContainer = ({title, children}: {title: React.ReactNode; children: React.ReactNode}) => {
    const [open, setOpen] = useState(false);
    return (
        <div className="w-full border-b bg-white">
            <div className="ml-4 cursor-pointer select-none py-2 text-3xl" onClick={() => setOpen(v => !v)}>
                {title}
            </div>
            <div hidden={!open}>{children}</div>
        </div>
    );
};

const list = 'http://dummyimage.com/160x600,'
    .repeat(13)
    .split(',')
    .map((link, index) => ({key: String(index), title: `图片${index}`, link}));

const ImageSelectDemo = () => {
    const [value, setValue] = useState('');
    return (
        <div>
            <ImageSelect className="mx-10" value={value} onChange={setValue} list={list} />
            <ImageSelect className="mx-10" value={value} onChange={setValue} list={list} pageSize={6} />
        </div>
    );
};

export default function ComponentDemo() {
    return (
        <div>
            <CollapseContainer title="气泡">
                <PopoverDemo />
            </CollapseContainer>
            <CollapseContainer title="图片选择器">
                <ImageSelectDemo />
            </CollapseContainer>
        </div>
    );
}
