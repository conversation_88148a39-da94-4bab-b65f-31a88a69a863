/* eslint-disable react-refresh/only-export-components */
import styled from '@emotion/styled';
import {Form} from 'antd';

export const StyledForm = styled(Form)`
    &.ant-form {
        position: relative;
    }
    .ant-form-item {
        margin-bottom: 14px;
        .ant-form-item-label {
            & > label::after {
                content: '' !important;
            }
        }
    }
` as unknown as typeof Form;

export const requiredMark = (label: React.ReactNode, info: {required: boolean}) =>
    typeof label === 'string' ? (
        <div className="flex w-full items-center justify-center leading-[22px] text-colorTextDefault">
            <div className="flex-grow">{label}</div>
            {info.required && (
                <span className="ml-1.5 text-sm text-error" style={{fontFamily: 'SimSong'}}>
                    *
                </span>
            )}
        </div>
    ) : (
        label
    );
