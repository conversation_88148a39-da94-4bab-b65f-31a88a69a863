import {useRef, useEffect, memo} from 'react';
import '@baidu/lingjing-fe-agent-adapter/dist/js/plugin.js';
import '@baidu/lingjing-fe-agent-adapter/dist/css/plugin.css';
import {PreviewData, PreviewStatus} from '@/components/InlinePluginPreview/interface';
import {serviceUrl, VersionType, isProd} from '@/components/PluginPreview/constant';
import {useChatViewLogin} from '@/components/PluginPreview/hooks/useChatViewLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';

interface ChatViewProps {
    pluginSessionId: string;
    previewData: PreviewData;
    setPreviewStatus: (setPreviewStatus: PreviewStatus) => void;
    onAlertRefresh: () => void;
}

enum ChatViewEventType {
    // 反馈调优气泡内的提交按钮监听
    ConversationAlertRefresh = 'conversationAlertRefresh',
}

const Chat = window.LingJingAgentSDK.PluginPreview;

const CHAT_VIEW_CONFIG = {
    narrow: true,
    // 隐藏导航栏
    nav: {
        type: 'top-menu',
        share: false,
        dropdown: false,
        menu: false,
    },
    // 隐藏脚注
    footer: false,
    // 隐藏背景水印
    watermark: false,
    history: false,
};

// 插件代理服务 WebSocket 线上地址，无法通过 FCNAP 转发
const PROXY_WEBSOCKET_SERVER_PATH = 'wss://chat-ws.baidu.com/lingjing/plugin/ws/conn';
const protocol = location.protocol === 'http:' ? 'ws' : 'wss';
const webSocketUrl = isProd() ? PROXY_WEBSOCKET_SERVER_PATH : `${protocol}://${location.host}/lingjing/plugin/ws/conn`;

// 本地、qa 环境调试的 WebSocket 地址
// const webSocketUrl = 'ws://localhost:8011';
// const webSocketUrl = 'ws://lingjing-qatest.now.baidu.com/lingjing/plugin/ws/conn';

export const ChatView: React.FC<ChatViewProps> = memo(function ChatView({
    pluginSessionId,
    previewData,
    setPreviewStatus,
    onAlertRefresh,
}) {
    const chatViewContainerRef = useRef<HTMLDivElement>(null);
    const chatRef = useRef<typeof Chat | null>(null);

    const [hasEnhancedAccess] = useUserInfoStore(store => [store.userInfoData?.userInfo.hasEnhancedAccess]);

    // 将平台实现的 login api 注入 ChatViewSDK
    const login = useChatViewLogin();

    useEffect(() => {
        if (pluginSessionId && !chatRef.current && Chat) {
            // 初始化 ChatViewSDK
            chatRef.current = new Chat({
                pluginInfo: {
                    pluginSessionId: pluginSessionId,
                    pluginMode: 'debug',
                },
                agentId: previewData?.appId,
                container: chatViewContainerRef.current!,
                serviceUrl,
                versionType: VersionType.Online,
                chatViewConfig: {
                    ...CHAT_VIEW_CONFIG,
                    inputBox: {
                        // 输入框、提问气泡里@插件名
                        atName: previewData?.pluginName,
                    },
                    userInfo: {
                        hasEnhancedAccess,
                    },
                },
                api: {
                    login,
                },
            });

            // 如果是本地插件，先连接 WebSocket 插件代理服务，再渲染对话容器
            if (previewData?.isLocal) {
                chatRef.current
                    ?.connectWebSocket({url: `${webSocketUrl}?sessionId=${pluginSessionId}`})
                    .then(() => {
                        chatRef.current?.render();
                    })
                    .catch(() => {
                        // 首次连接 WebSocket 失败，展示错误页
                        setPreviewStatus(PreviewStatus.PreviewError);
                    });
            } else {
                // 非本地插件，直接渲染对话容器
                chatRef.current?.render();
            }
        }

        chatRef.current?.on(ChatViewEventType.ConversationAlertRefresh, onAlertRefresh);

        return () => {
            previewData?.isLocal && chatRef.current?.closeWebSocket();
            chatRef.current?.destroy();
            chatRef.current?.off(ChatViewEventType.ConversationAlertRefresh, onAlertRefresh);
            chatRef.current = null;
        };
    }, [
        pluginSessionId,
        chatViewContainerRef,
        chatRef,
        previewData?.appId,
        previewData?.isLocal,
        previewData?.pluginName,
        setPreviewStatus,
        login,
        hasEnhancedAccess,
        onAlertRefresh,
    ]);

    return (
        <div className={'absolute flex h-full w-full'}>
            <div className="h-full w-full bg-white" ref={chatViewContainerRef} />
        </div>
    );
});
