import {Button} from 'antd';

interface PreviewErrorProps {
    tips?: string;
    // 重新预览按钮的点击回调
    onPreviewClick?: () => void;
}

export const PreviewError: React.FC<PreviewErrorProps> = ({tips = '', onPreviewClick}) => {
    return (
        <div className="mx-auto flex flex-col items-center justify-center ">
            <div className="mb-4 h-[3.3125rem] w-[3.875rem] rounded-lg border-[.375rem]">
                <div className="iconfont icon-visual pt-[0.0938rem] text-center text-4xl text-[#DBDCE0]" />
            </div>
            <div>
                <Button onClick={onPreviewClick} type="default">
                    重新预览
                </Button>
            </div>
            {!!tips && <p className="mt-[.375rem] text-sm text-flow-hover">{tips}</p>}
        </div>
    );
};
