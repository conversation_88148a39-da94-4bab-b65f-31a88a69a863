/**
 * @file 内嵌式插件预览组件（无侧导，开发版使用）
 *
 */
import {useCallback, useEffect, useRef, useState} from 'react';
import {PreviewPlaceholder} from '@/components/InlinePluginPreview/PreviewPlaceholder';
import {ChatView} from '@/components/InlinePluginPreview/ChatView';
import {PreviewData, PreviewStatus} from '@/components/InlinePluginPreview/interface';
import StaticTips from '@/components/StaticTips/index';
import {PreviewError} from './PreviewError';

interface PreviewContainerProps {
    // 插件临时 id
    pluginSessionId: string;
    // 预览按钮是否可点
    canPreview?: boolean;
    // 表单数据是否发生改变
    hasChange?: boolean;
    // 刷新按钮的点击回调；
    onRefresh?: () => Promise<PreviewData>;
    // 刷新按钮的展现回调；
    onRefreshBtnShow?: () => void;
    tips?: string;
    // 预览按钮的点击回调
    onPreview?: () => Promise<PreviewData>;
}

export const InlinePluginPreview: React.FC<PreviewContainerProps> = ({
    pluginSessionId,
    canPreview = true,
    tips = '',
    hasChange = false,
    onRefresh,
    onPreview,
    onRefreshBtnShow,
}) => {
    const [previewStatus, setPreviewStatus] = useState<PreviewStatus>(PreviewStatus.Placeholder);
    const [chatViewKey, setChatViewKey] = useState(0);
    const previewDataRef = useRef<PreviewData | null>(null);

    const showChatView = canPreview && previewStatus === PreviewStatus.ChatView && !!previewDataRef.current;
    const showPlaceholder = previewStatus === PreviewStatus.Placeholder;
    const showPreviewError = previewStatus === PreviewStatus.PreviewError;
    const showRefresh = showChatView && hasChange;

    useEffect(() => {
        if (showRefresh) {
            onRefreshBtnShow?.();
        }
    }, [showRefresh, onRefreshBtnShow]);

    const handlePreview = useCallback(() => {
        onPreview?.()
            .then(res => {
                previewDataRef.current = res;
                setPreviewStatus(PreviewStatus.ChatView);
            })
            .catch(() => {
                setPreviewStatus(PreviewStatus.PreviewError);
            });
    }, [onPreview, setPreviewStatus]);

    const handleRefresh = useCallback(() => {
        onRefresh?.()
            .then(res => {
                previewDataRef.current = res;
                // 使用递增的 key 刷新 ChatView
                setChatViewKey(prevKey => prevKey + 1);
            })
            .catch(() => {
                setPreviewStatus(PreviewStatus.PreviewError);
            });
    }, [onRefresh]);

    // 当用户在告警弹窗中点击刷新按钮的回调
    const handleAlertRefresh = useCallback(() => {
        // 使用递增的 key 刷新 ChatView
        setChatViewKey(prevKey => prevKey + 1);
    }, []);

    useEffect(() => {
        if (!canPreview) {
            setPreviewStatus(PreviewStatus.Placeholder);
        }
    }, [canPreview]);

    return (
        <div className="min-w-[42.25rem] flex-grow">
            <div className="relative flex h-full w-full bg-[#F6F7F9]">
                {showRefresh && (
                    <StaticTips
                        showButton
                        onClick={handleRefresh}
                        disable={!canPreview}
                        className="absolute left-[50%] top-6 z-10 w-[24.625rem] translate-x-[-50%] overflow-hidden "
                    >
                        检测到表单内容有调整，请刷新预览最新效果
                    </StaticTips>
                )}
                {showChatView && (
                    <ChatView
                        key={chatViewKey}
                        pluginSessionId={pluginSessionId}
                        previewData={previewDataRef.current!}
                        setPreviewStatus={setPreviewStatus}
                        onAlertRefresh={handleAlertRefresh}
                    />
                )}
                {showPlaceholder && (
                    <PreviewPlaceholder canPreview={canPreview} tips={tips} onPreviewClick={handlePreview} />
                )}
                {showPreviewError && <PreviewError tips="网络异常" onPreviewClick={handlePreview} />}
            </div>
        </div>
    );
};
