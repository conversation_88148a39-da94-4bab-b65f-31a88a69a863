# Cascader 级联组件

**组件说明： 基于antd组件二次封装的级联组件，支持PC和Wise。**

**PC**：Cascader 级联组件 基于 [Antd Cascader](https://ant-design.antgroup.com/components/cascader-cn) 组件 二次封装。

**Wise**: Cascader 级联组件 Popup公共弹层组件 和 [Antd-Mobile TreeSelect](https://mobile.ant.design/zh/components/tree-select/) 级联选择器 二次封装。只支持单选。仅弹出层组件，不包含Select下拉框。


# PC Cascader

## 接口说明

仅列举扩展的属性，更多属性请参考 [Antd Cascader](https://ant-design.antgroup.com/components/cascader-cn) 文档

| 属性名              | 说明                                     | 类型                       | 默认值     |
|--------------------|-----------------------------------------|----------------------------|----------|
| selectToken        | Select下拉框主题Token，默认平台灰底的Select样式  | Partial\<SelectToken\>   | {}       |
| cascaderToken      | Cascader主题Token，默认平台样式                | Partial\<CascaderToken\> | {}       |


## 代码示例
```jsx
import Cascader from '@/components/Cascader';

<Cascader
    value={value}
    options={options}
    placeholder="请选择开户省市"
    onChange={onChange}
    allowClear
    showSearch={{filter}}
    // 改写为白底的select框样式token
    selectToken={{
        selectorBg: '#fff',
        lineWidth: 1,
    }}
    />
```

# Wise Cascader

## 接口说明

| 属性名              | 说明                                     | 类型                       | 默认值     |
|--------------------|-----------------------------------------|----------------------------|----------|
| title              | Popup弹窗title                           | string                     | ''      |
| visible            | 是否弹出popup弹窗                         | boolean                    | false      |
| popupProps         | Popup公共组件props(比如修改弹层样式)        | PopupMProps                | {}      |
| options            | 级联数据数据源                            | TreeSelectOption[]         | []    |
| value              | 选中选项值                                | string[]                   | []      |
| setVisible         | 打开/关闭popup弹窗                        | (visible: boolean) => void | --      |
| onChange           | 选择值改变回调事件(选到叶子节点才回调)        | (value: string[], options: TreeSelectOption[]) => void | -- |

## 代码示例

```jsx
import Cascader from '@/components/Cascader/index-m';

<Cascader
    title="渠道筛选"
    visible={visible}
    setVisible={setVisible}
    value={value}
    options={options}
    onChange={onChange}
    />
```
