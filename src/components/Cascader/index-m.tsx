/**
 * 移动端弹窗式级联组件 只支持单选
 * 组件说明文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/W9QkurZ5SgGc6c
 */

import styled from '@emotion/styled';
import TreeSelect, {TreeSelectOption} from 'antd-mobile/es/components/tree-select';
import {useCallback} from 'react';
import Popup, {PopupMProps} from '../mobile/Popup';

const StyledTreeSelect = styled(TreeSelect)<{
    // 层级深度 不传默认2层
    depth?: number;
}>`
    &.adm-tree-select .adm-tree-select-column {
        width: ${props => `calc(100%/${props.depth || 2}) !important`};
        background-color: #f5f6fa;
        color: #000311;
        font-size: 1rem;
        .adm-tree-select-item {
            min-height: 2.875rem;
            padding: 0.375rem 1.125rem;
            &.adm-tree-select-item-active {
                position: relative;
                color: #5562f2;
                font-weight: normal;
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3px;
                    height: 0.875rem;
                    background-color: #5562f2;
                }
            }
        }

        &:last-child {
            background-color: #fff;
            .adm-tree-select-item.adm-tree-select-item-active::before {
                display: none;
            }
        }
    }
`;

interface Props {
    /** 弹窗title */
    title: string;
    /** 是否弹出popup弹窗 */
    visible: boolean;
    /** Popup组件Props */
    popupProps?: PopupMProps;
    /** 可选项数据源 */
    options: TreeSelectOption[];
    /** 选中选项值 */
    value?: string[];
    /** 打开/关闭popup弹窗事件  */
    setVisible: (visible: boolean) => void;
    /** 选择值改变回调事件 */
    onChange: (value: string[], options: TreeSelectOption[]) => void;
}

export default function CascaderM({title, visible, setVisible, popupProps, options, value, onChange}: Props) {
    const closePopup = useCallback(() => {
        setVisible(false);
    }, [setVisible]);

    const treeSelectChange = useCallback(
        (value: string[], extend: {options: TreeSelectOption[]}) => {
            // 选中节点的children属性
            const selectMenuChildren = extend.options[extend.options.length - 1].children;

            // 是不是选了叶子节点。是-执行change事件，否-不执行change事件还需继续选择或者取消
            if (!selectMenuChildren || selectMenuChildren.length === 0) {
                onChange(value, extend.options);
            }
        },
        [onChange]
    );

    return (
        <Popup
            visible={visible}
            bodyStyle={{height: '50%', paddingLeft: 0, paddingRight: 0}}
            title={title}
            suffix={
                <span
                    className="iconfont icon-close mr-[18px] text-[20px] text-gray-quaternary"
                    onClick={closePopup}
                ></span>
            }
            onClose={closePopup}
            onCancel={closePopup}
            {...popupProps}
        >
            {visible && <StyledTreeSelect defaultValue={value} options={options} onChange={treeSelectChange} />}
        </Popup>
    );
}
