/**
 * PC-级联下拉框选择组件
 * 组件说明文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/4UW1tQIcf-89dQ
 */
import {Cascader, ConfigProvider} from 'antd';
import {CascaderProps} from 'antd/lib/cascader';
import {css} from '@emotion/css';
import {SelectToken} from 'antd/es/select/style/token';
import {CascaderToken} from 'antd/es/cascader/style';
import {selectToken as DefSelectToken} from '@/styles/component-token';
import {useScrollStyle} from '../ScrollContainer/useScrollStyle';

export default function CascaderPC({
    className,
    cascaderToken,
    selectToken,
    ...props
}: CascaderProps & {
    cascaderToken?: Partial<CascaderToken>;
    selectToken?: Partial<SelectToken>;
}) {
    const ScrollStyle = useScrollStyle({scrollY: true, scrollbarWidth: 12, autoHideScrollbar: false});

    return (
        <ConfigProvider
            theme={{
                components: {
                    Select: {...DefSelectToken, ...selectToken},
                    Cascader: {
                        /** select选项框宽度 */
                        controlWidth: 160,
                        /** 下拉框高度 */
                        dropdownHeight: 172,
                        /** 下拉框单列宽度 */
                        controlItemWidth: 155,
                        /** 下拉框单列内间距 */
                        menuPadding: '10px 12px',
                        /** 选项内间距 */
                        optionPadding: '4px 12px',
                        /** 选中选项背景色 */
                        optionSelectedBg: 'transparent',
                        /** 选中选项文字fontWeight */
                        optionSelectedFontWeight: 400,
                        /** 选项hover时背景色 */
                        controlItemBgHover: 'transparent',
                        /** 文字大小 */
                        fontSize: 14,
                        /** 选项borderRadius */
                        borderRadiusSM: 4,
                        ...cascaderToken,
                    },
                },
            }}
        >
            <Cascader
                className={className}
                popupClassName={css`
                    .ant-cascader-menu {
                        ${ScrollStyle}
                        &::-webkit-scrollbar-thumb {
                            // 设置滚动条border宽度来实现滚动条不贴边
                            border: 4px solid #fff;
                        }
                        .ant-cascader-menu-item {
                            margin-bottom: 1px;
                            &:hover {
                                color: #5562f2;
                            }
                        }
                        .ant-cascader-menu-item-active {
                            color: #5562f2;
                            .ant-cascader-menu-item-expand-icon .iconfont {
                                color: #5562f2 !important;
                            }
                        }
                        // 子级未全选父级多选框样式
                        .ant-cascader-checkbox.ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner {
                            background-color: #5562f2;
                            border-color: #5562f2;
                            ::after {
                                background-color: #fff !important;
                                height: 2px !important;
                            }
                        }
                    }
                `}
                expandIcon={<span className="iconfont icon-right -mr-1 text-xs text-[#141414A6]"></span>}
                {...props}
            />
        </ConfigProvider>
    );
}
