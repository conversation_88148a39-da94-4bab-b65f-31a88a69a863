import {useEffect, useRef, useState} from 'react';
import {Upload} from 'antd';
import type {UploadFile, UploadProps} from 'antd/es/upload/interface';
import classNames from 'classnames';

interface Props extends UploadProps<any> {
    children: React.ReactNode;
}

interface FileCardProps {
    file: UploadFile;
    onRemove?: () => void;
}

interface FileIconProps {
    name: string;
    className?: string;
}

const formateFileSize = (sizeBit: number) => {
    let size = sizeBit;
    const units = ['B', 'KB', 'MB', 'GB'];
    for (const unit of units) {
        if (size < 1024) {
            return `${size.toFixed(1)}${unit}`;
        }
        size = size / 1024;
    }
    return `${size}GB`;
};

enum iconNameEnum {
    Docx = 'docx',
    Doc = 'doc',
    Xlsx = 'xlsx',
    Xls = 'xls',
    Csv = 'csv',
    Pdf = 'pdf',
    Txt = 'txt',
    Rar = 'rar',
    Zip = 'zip',
}

// 文件 icon
function FileIcon({name}: FileIconProps) {
    const type = (name.split('.').pop() || 'pdf').toLocaleLowerCase() as iconNameEnum;
    const iconNameMaps = {
        [iconNameEnum.Docx]: 'fileword',
        [iconNameEnum.Doc]: 'fileword',
        [iconNameEnum.Xlsx]: 'fileexcel',
        [iconNameEnum.Xls]: 'fileexcel',
        [iconNameEnum.Csv]: 'fileexcel',
        [iconNameEnum.Pdf]: 'filepdf',
        [iconNameEnum.Txt]: 'filetext',
        [iconNameEnum.Rar]: 'filerar',
        [iconNameEnum.Zip]: 'filezip',
    };

    return <span className={`iconfont icon-${iconNameMaps[type] || 'filetext'} text-[18px]`}></span>;
}

// 展示已上传文件的文件信息
function UploadFileCard({file, onRemove}: FileCardProps) {
    const [percent, setPercent] = useState<number>(1);
    const interval = useRef<any>();
    const hasError = file.status === 'error';
    const status = file.status;
    // upload 组件传递的 percent 只有 0 和 100，需要补充一个假的进度条状态
    useEffect(() => {
        if (file.status !== 'uploading') {
            if (interval.current) {
                clearInterval(interval.current);
            }
            return;
        }

        if (interval.current) {
            if (percent > 90) {
                clearInterval(interval.current);
            }
            return;
        }

        const intervalId = setInterval(() => {
            setPercent(value => value + 18);
        }, 100);
        interval.current = intervalId;
    }, [percent, file.status]);

    return (
        <>
            <div
                className={classNames(
                    'relative z-10 mt-[.625rem] overflow-hidden rounded-[.5625rem] border px-5 py-[1.0938rem] text-sm',
                    {
                        'border-[#FF4D4F] border-opacity-40 bg-red-50': hasError,
                        'border-[#eceef3]': !hasError,
                    }
                )}
            >
                <div className="flex h-[1.375rem] content-center items-center overflow-hidden">
                    <FileIcon name={file.name} />
                    <div className="ml-[.375rem] flex flex-grow items-center">
                        <span className="inline-block max-w-[17.5rem] overflow-hidden overflow-ellipsis whitespace-nowrap">
                            {file.name}
                        </span>
                        <span className="text-gray-tertiary">({formateFileSize(file.size || 0)})</span>
                    </div>
                    <div className="flex items-center">
                        {status === 'uploading' && `${percent}%`}
                        <div
                            onClick={onRemove}
                            className={classNames(
                                'iconfont icon-delete ml-4 cursor-pointer text-lg text-[#585E75] hover:text-primaryActive',
                                {
                                    'text-gray-300 hover:text-gray-300': status === 'uploading',
                                }
                            )}
                        />
                    </div>
                </div>
                {hasError && <p className="ml-6 text-error">{file.error}</p>}
            </div>
            {status === 'uploading' && (
                <div className="absolute h-[58px] w-full -translate-y-[58px] overflow-hidden rounded">
                    <div
                        className="h-full bg-gray-border-secondary opacity-40"
                        style={{width: `${percent}%`, transition: 'width 0.1s ease'}}
                    ></div>
                </div>
            )}
        </>
    );
}

const handleRenderFileItem = (
    originalNode: React.ReactNode,
    file: UploadFile,
    fileList: UploadFile[],
    {remove}: {remove: () => void}
) => {
    return <UploadFileCard file={file} onRemove={remove} />;
};

const UploadFiles: React.FC<Props> = ({children, ...props}) => {
    return (
        <Upload.Dragger {...props} itemRender={props.itemRender || handleRenderFileItem}>
            {children}
        </Upload.Dragger>
    );
};

export default UploadFiles;
