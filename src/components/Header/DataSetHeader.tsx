/**
 * 顶部导航
 */
import {useCallback} from 'react';
import CustomerService from '@/components/CustomerService';
import {STEP} from '@/modules/dataset/constant';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {CommunityPopoverType, CreateRepositoryStep} from '@/utils/loggerV2/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {LJExtData} from '@/utils/loggerV2/utils';

interface HeaderProps {
    onBack: () => void;
    onSubmit: () => void;
    title: JSX.Element;
    step: STEP;
    children?: React.ReactNode;
    button?: React.ReactNode;
}

export function Header({onBack, title, button, children, step}: HeaderProps) {
    const {showLog} = useUbcLogV3();

    const handleQRCodeHoverOpen = useCallback(() => {
        showLog(EVENT_VALUE_CONST.COMMUNITY_POPOVER, {
            [EVENT_EXT_KEY_CONST.CREATE_REPOSITORY_STEP]:
                step === STEP.first ? CreateRepositoryStep.FIRST : CreateRepositoryStep.SECOND,
            [EVENT_EXT_KEY_CONST.COMMUNITY_POPOVER_TYPE]: CommunityPopoverType.ACTIVE,
        } as LJExtData);
    }, [showLog, step]);

    const handleTipsShow = useCallback(() => {
        showLog(EVENT_VALUE_CONST.CUSTOMER_SERVICE_BUBBLE);
    }, [showLog]);

    const handleAiBotOpen = useCallback(() => {
        showLog(EVENT_VALUE_CONST.CUSTOMER_SERVICE_DRAWER);
    }, [showLog]);

    return (
        <header className="relative flex h-[56px] w-full items-center justify-between border-b border-gray-border-secondary bg-white p-4">
            <div className="flex items-center text-black">
                <span className="cursor-pointer pr-4 hover:text-primary" onClick={onBack}>
                    <span className="iconfont icon-left text-xl"></span>
                </span>
                <span>{title}</span>
            </div>
            <div className="absolute left-1/2 -translate-x-1/2">{children}</div>
            <div className="flex items-center gap-3 text-black">
                {button}
                {/* 智能客服 */}
                <div className="h-[22px] border-r-[1px] border-[#DEE0E7]"></div>
                <CustomerService
                    className="mr-2"
                    onQRCodeHoverOpen={handleQRCodeHoverOpen}
                    onTipsShow={handleTipsShow}
                    onAiBotOpen={handleAiBotOpen}
                />
            </div>
        </header>
    );
}
