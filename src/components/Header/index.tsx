import React, {lazy, useCallback} from 'react';
import {<PERSON>} from 'react-router-dom';
import urls from '@/links';
import {LingJingIcon} from '@/dicts/index';
import homeConstant from '@/dicts/home';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';

// 懒加载头像组件
const UserAvatar = lazy(() => import('@/components/UserAvatar'));

interface HeaderProps {
    showDoc?: boolean;
    docUrl?: string;
}

const Header = (props: HeaderProps) => {
    const {showDoc, docUrl = homeConstant.DOCS_URL} = props;
    const {ubcClickLog} = useUbcLog();

    const handleLogoClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.HeaderLingjingLogo);
    }, [ubc<PERSON>lickLog]);

    const handleDocClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.HeaderLingjingDoc);
    }, [ubcClickLog]);

    return (
        <header
            className="
            mx-auto flex h-[70px]
            items-center justify-between
            border-[0.5px]
            border-gray-border bg-white py-[16px] pl-5 pr-6
            leading-[68px]
            "
        >
            <div className="flex w-[156px] items-center text-sm font-normal leading-normal text-neutral-800">
                <Link to={urls.root.raw()} onClick={handleLogoClick}>
                    <img src={LingJingIcon} className={'inline-block h-[36px] w-full'} alt="logo" />
                </Link>
            </div>
            <div className="flex items-center">
                {showDoc && (
                    <Link
                        to={docUrl}
                        onClick={handleDocClick}
                        target="_blank"
                        className="mr-[2rem] min-w-fit opacity-60 hover:cursor-pointer hover:opacity-100"
                    >
                        文档
                    </Link>
                )}
                <UserAvatar />
            </div>
        </header>
    );
};

export default Header;
