/**
 * @file 提审模态框表单
 * <AUTHOR>
 */

import {Form, Input, Modal, Radio} from 'antd';
import {InfoCircleOutlined} from '@ant-design/icons';
import type {RadioChangeEvent} from 'antd';
import {useForm} from 'antd/es/form/Form';
import styled from '@emotion/styled';
import {useCallback, useEffect, useState} from 'react';
import {PluginPublishType, PLUGIN_PUBLISH_TYPE_NAME} from '@/api/pluginCenter/interface';
import {VersionStatus} from '@/api/appVersion/interface';
import {AIServiceType} from '@/api/pluginCenter/interface';
import ThemeConfig from '@/styles/lingjing-light-theme';

export const StyledFormItem = styled(Form.Item)`
    &.ant-form-item {
        margin-bottom: 1.5rem;
        .ant-input-data-count {
            transform: translate(-10px, -27px);
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            background: white;
            width: 660px;
            text-align: right;
        }
        .ant-radio-inner {
            border: 1px solid ${ThemeConfig.token.colorPrimary};
        }
    }

    .ant-form-item-label {
        width: 5.3rem;
        height: 1.5rem;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        text-align: left;
        margin-right: 0.55rem;
    }
    .ant-form-item-label .ant-form-item-required::before {
        display: none !important;
    }
    .ant-form-item-label .ant-form-item-required::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSong;
        line-height: 22px;
        font-weight: 400;
        content: '*' !important;
        // 设置label独占一行后content的visibility会变成hidden
        visibility: visible !important;
    }
`;

interface SubmitModalProps {
    isSubmitting: boolean;
    open: boolean;
    lastVersion?: string;
    versionStatus?: VersionStatus;
    onSubmit: (versionData: {version: string; versionInfo: string; pluginScope: PluginPublishType}) => void;
    onClose: () => void;
    pluginScope: PluginPublishType;
    pluginType: AIServiceType;
}

const versionInfoMaxLength = 200;

const reg = new RegExp(/^[1-9][0-9]?[.][1-9]?[0-9][.][1-9]?[0-9]$/);

const version2number = (version: string) => {
    return Number(
        version
            .split('.')
            .map(str => (str.length > 1 ? str : '0' + str))
            .join('')
    );
};

export function SubmitModal(props: SubmitModalProps) {
    const {open, isSubmitting, lastVersion, versionStatus, onSubmit, onClose, pluginScope, pluginType} = props;
    const [form] = useForm<{version: string; versionInfo: string; pluginScope: PluginPublishType}>();
    const [publishType, setPublishType] = useState<PluginPublishType>(pluginScope);

    const validateVersion = useCallback(
        (_: any, value: string) => {
            if (!value) return Promise.reject(new Error(`请输入版本号`));
            if (!reg.test(value)) return Promise.reject(new Error(`版本号格式为：X.Y.Z（1-99.0-99.0-99）`));

            const lastVersionNumber = version2number(lastVersion || '0.99.99');
            const inputNumber = version2number(value);
            if (
                lastVersionNumber > inputNumber ||
                (versionStatus !== VersionStatus.AuditFail && lastVersionNumber === inputNumber)
            ) {
                return Promise.reject(new Error(`输入的版本号数字应该大于历史版本号`));
            }
            return Promise.resolve();
        },
        [lastVersion, versionStatus]
    );

    useEffect(() => {
        // 关闭提交弹窗时清空表单信息
        if (!open) {
            form.resetFields();
        }
        form.setFieldValue('pluginScope', publishType);
    }, [form, open, publishType]);

    useEffect(() => {
        // 监听非首次打开弹窗时pluginScope的变化（getlastVersion接口返回上一次公开范围）
        setPublishType(pluginScope);
    }, [pluginScope]);

    const handleOk = useCallback(() => {
        // 表单数据(版本信息+公开范围)
        const data = form.getFieldsValue();
        onSubmit(data);
    }, [form, onSubmit]);

    const onChangePluginScope = useCallback(
        (e: RadioChangeEvent) => {
            form.setFieldValue('pluginScope', e.target.value);
            setPublishType(e.target.value);
        },
        [form]
    );

    return (
        <Modal
            centered
            open={open}
            title="提交信息"
            okText="确认"
            okButtonProps={{loading: isSubmitting}}
            cancelText="取消"
            onOk={handleOk}
            onCancel={onClose}
            width={720}
        >
            <Form form={form} labelCol={{span: 4}} className="mt-4" autoComplete="off">
                {pluginType === AIServiceType.Ability && (
                    <>
                        <StyledFormItem label="公开范围" required className="ml-[-3rem]" name="pluginScope">
                            <Radio.Group onChange={onChangePluginScope}>
                                <Radio value={PluginPublishType.Public}>
                                    {PLUGIN_PUBLISH_TYPE_NAME[PluginPublishType.Public]}
                                </Radio>
                                <Radio value={PluginPublishType.Private} className="ml-[1rem]">
                                    {PLUGIN_PUBLISH_TYPE_NAME[PluginPublishType.Private]}
                                </Radio>
                            </Radio.Group>
                        </StyledFormItem>
                        {publishType === PluginPublishType.Private && (
                            <div className="mb-6 mt-[-1.3875rem] text-sm/[1.375rem] font-normal text-gray-tertiary">
                                <InfoCircleOutlined className="mr-2" />
                                <span>当前插件仅个人可见，将无法在公开发布的智能体中使用</span>
                            </div>
                        )}
                    </>
                )}
                <StyledFormItem
                    colon={false}
                    labelAlign="left"
                    label="版本号"
                    name="version"
                    required
                    rules={[{validator: validateVersion}]}
                    labelCol={{span: 24}}
                >
                    <Input
                        className="mt-[0.6875rem] rounded-md border	border-[#DEE0E7]"
                        prefix={<span className="text-black-base pr-1">V</span>}
                        placeholder={`仅支持3位小数，数字间用"."分隔，${
                            lastVersion ? `当前线上版本 ${lastVersion}` : '例如 1.0.1'
                        }`}
                    />
                </StyledFormItem>
                <StyledFormItem
                    colon={false}
                    labelAlign="left"
                    label="版本信息"
                    name="versionInfo"
                    rules={[
                        {
                            required: true,
                            message: '请输入版本信息',
                        },
                        {
                            max: versionInfoMaxLength - 1,
                            message: `版本信息长度不可超过${versionInfoMaxLength}`,
                        },
                    ]}
                    labelCol={{span: 24}}
                >
                    <Input.TextArea
                        placeholder="请输入版本信息与用户使用引导内容"
                        allowClear
                        style={{height: 100, resize: 'none'}}
                        showCount={{formatter: ({value}) => `${value.length} / ${versionInfoMaxLength}`}}
                        className="mt-[0.6875rem] rounded-md border	border-[#e7dee2] pb-7"
                        maxLength={200}
                    />
                </StyledFormItem>
            </Form>
        </Modal>
    );
}
