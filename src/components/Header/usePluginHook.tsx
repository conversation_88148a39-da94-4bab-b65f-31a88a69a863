import {useMemo} from 'react';
import {useLocation, useParams} from 'react-router-dom';
import {AIServiceType, PLUGIN_TYPE_EN_LOWER_NAME} from '@/api/pluginCenter/interface';
import urls from '@/links';

/**
 * 通过url分割关键字获取当前页面所在插件类型
 * 智能体路由格式：/agent/*，agent是关键字
 * 应用插件路由格式： /plugin/app，plugin是关键字
 * 数据/能力插件路由格式： /plugin/:type/*，:type是插件类型
 * @returns AIServiceType | undefined 返回插件类型
 */
export const useAIServiceType = (): AIServiceType | undefined => {
    const location = useLocation();
    const ROUTE_MODULE_INDEX = 1;
    const pathArr = location.pathname.split('/');
    const currentModule = pathArr[ROUTE_MODULE_INDEX];

    const {type = ''} = useParams();

    return useMemo(() => {
        if (currentModule === 'agent' || (currentModule === 'plugin' && pathArr[ROUTE_MODULE_INDEX + 1] === 'app')) {
            return AIServiceType.App;
        }

        const pluginTypeStr =
            (currentModule === 'plugin' &&
                Object.entries(PLUGIN_TYPE_EN_LOWER_NAME).find(item => item[1] === type)?.[0]) ||
            '';
        return pluginTypeStr ? +pluginTypeStr : undefined;
    }, [currentModule, type, pathArr]);
};

/**
 * 通过url分割关键字获取当前页面对应的插件列表route
 * 智能体编排路由格式：/agent/flow/*
 * 应用插件路由格式： /plugin/app，plugin是关键字
 * 数据/能力插件路由格式： /plugin/:type/*，:type是插件类型
 * @returns 返回插件类型对应列表route url，不存在插件类型，返回插件中心
 */
export const usePluginListRoute = (): string => {
    const type = useAIServiceType();

    return useMemo(() => {
        if (type === undefined) {
            return urls.plugin.raw();
        }

        const pluginListRouteMaps = {
            [AIServiceType.App]: urls.pluginAppList.raw(),
            [AIServiceType.Ability]: urls.pluginAbilityList.raw(),
            [AIServiceType.Data]: urls.pluginDataList.raw(),
        };

        return pluginListRouteMaps[type] || urls.plugin.raw();
    }, [type]);
};
