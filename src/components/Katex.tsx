/* eslint-disable react/no-danger */
import React from 'react';
import katex from 'katex';
import 'katex/dist/katex.min.css'; // 引入 KaTeX 的样式
import DOMPurify from 'dompurify';

// const demo = `题目:
// 最小的两位数是（$\\qquad$），它与最大的一位数的和是（$\\qquad$），积是（$\\qquad$）。

// 分析:
// 考查点：两位数与一位数的认识及运算
// 关键步骤：确定最小两位数→确定最大一位数→求和与积

// 解析:
// 最小的两位数是$10$，因为两位数的范围是$10$到$99$。
// 最大的一位数是$9$，因为一位数的范围是$1$到$9$。
// 和：$10 + 9 = 19$，积：$10 \\times 9 = 90$。
// 答：最小的两位数是$10$，和是$19$，积是$90$。

// 总结:
// 本题需要明确最小两位数和最大一位数的定义，并通过加减乘运算得出结果。关键点在于正确理解数位概念并准确计算`;

// const demo = `这是一个行内公式：$E=mc^2$，这是一个块级公式：
// $$\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}$$
// 这是换行的文本。
// 这是另一行。
// `;

// 新增高亮处理函数
const highlightKeywords = (text: string, keyword?: string) => {
    if (!keyword) return text;
    const regex = new RegExp(keyword, 'gi');
    return text.replace(regex, match => `<span class="font-bold text-[#4E6EF2]">${match}</span>`);
};

const renderStringWithKatex = (text: string, keyword?: string) => {
    // 处理 \hspace 为实际空格
    // const withSpaces = text.replace(/\\hspace\{1em\}/g, ' ');

    // 正则表达式匹配行内公式 ($...$) 和块级公式 ($$...$$)
    const regex = /(\$\$.*?\$\$|\$.*?\$)/g;

    // 将字符串按公式和非公式部分拆分
    const parts = text.split(regex);

    return parts.map(part => {
        // 如果是公式部分
        if (part.startsWith('$') && part.endsWith('$')) {
            const isBlock = part.startsWith('$$'); // 判断是否是块级公式
            const formula = part.slice(isBlock ? 2 : 1, isBlock ? -2 : -1); // 提取公式内容

            try {
                // 使用 KaTeX 渲染公式
                const cleanHTML = DOMPurify.sanitize(
                    katex.renderToString(formula, {
                        displayMode: isBlock, // 块级公式使用 displayMode
                        throwOnError: false, // 忽略错误
                    })
                );

                // bca-disable-line
                return <span key={formula} dangerouslySetInnerHTML={{__html: cleanHTML}} />;
            } catch (error) {
                console.error('KaTeX rendering error:', error);
                return <span key={formula}>{part}</span>; // 如果渲染失败，回退到原始文本
            }
        }

        // 如果是普通文本，处理换行符和高亮关键词
        const highlightedText = highlightKeywords(part, keyword);
        const lines = highlightedText.split('\n');
        return lines.map((line, lineIndex) => (
            <>
                {/* bca-disable-line */}
                <span dangerouslySetInnerHTML={{__html: line}} />
                {lineIndex < lines.length - 1 && <br />} {/* 添加换行 */}
            </>
        ));
    });
};

const Katex = ({
    children,
    keyword,
    ...props
}: React.HTMLAttributes<HTMLDivElement> & {children?: string; keyword?: string}) => {
    return <div {...props}>{children && renderStringWithKatex(children, keyword)}</div>;
};

export default Katex;
