/**
 * + 添加按钮
 *
 */
import {Button, ConfigProvider, ButtonProps} from 'antd';

export default function AddButton({className, ...props}: ButtonProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        contentFontSize: 14,
                        contentLineHeight: 1,
                        lineWidth: 0,
                        paddingBlock: 0,
                        paddingInline: 0,
                        controlHeight: 14,
                        colorLink: '#848691',
                    },
                },
            }}
        >
            <Button
                type="link"
                icon={<span className="iconfont icon-plus text-sm font-normal leading-none" />}
                {...props}
            />
        </ConfigProvider>
    );
}
