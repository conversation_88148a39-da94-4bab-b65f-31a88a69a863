import {Tooltip, TooltipProps} from 'antd';
import {useMemo} from 'react';

export type CustomTooltipProps = TooltipProps & {offset?: number};

/**
 * @description 封装 antd 原生的 Tooltip 增加关闭按钮和自定义偏移量 offset
 */
const CustomTooltip: React.FC<CustomTooltipProps> = ({children, placement = 'top', offset = 16, ...props}) => {
    const align = useMemo(() => {
        const isX = placement.includes('left') || placement.includes('right');
        const isPositive = placement.includes('bottom') || placement.includes('right');
        return {
            offset: [isX ? (isPositive ? offset : -offset) : 0, isX ? 0 : isPositive ? offset : -offset],
        };
    }, [offset, placement]);

    return (
        <Tooltip align={align} placement={placement} {...props}>
            {children}
        </Tooltip>
    );
};

export default CustomTooltip;
