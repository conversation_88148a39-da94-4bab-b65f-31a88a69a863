/**
 * @file 数据分析时间选择器
 * <AUTHOR>
 * @update <EMAIL>
 */

import React, {useCallback, useMemo} from 'react';
import {ConfigProvider, DatePicker} from 'antd';
import type {RangePickerProps} from 'antd/es/date-picker';
import locale from 'antd/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import {AliasToken} from 'antd/es/theme/interface';
import {DefaultOptionType} from 'antd/es/select';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import Select from '@/components/Select';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);

const {RangePicker} = DatePicker;
const defaultOptions = [
    {value: '1', label: '昨天'},
    {value: '7', label: '近7天'},
    {value: '14', label: '近14天'},
    {value: '30', label: '近30天'},
    {value: '60', label: '近60天'},
    {value: '', label: '自定义'},
];
/** 时间选择器触发组件类型 */
export type DateRangePickerTriggerType = 'dropdown' | 'rangePicker';

export default function AnalysisDatePicker({
    searchToday,
    dateRangeValue,
    recentDays,
    themeToken,
    gapX,
    setDateRangeValue,
    setRecentDays,
    getAnalysisData,
    getTriggerType,
}: {
    /** 是否查询今日，默认false，查询昨日 */
    searchToday?: boolean;
    /** 快捷下拉框选择的最近多少日的value值 */
    recentDays: string;
    /** 时间范围选择器的value值 */
    dateRangeValue: RangePickerProps['value'];
    /** 容器token */
    themeToken?: Partial<AliasToken>;
    /** 子容器横向间距 */
    gapX?: string;
    setDateRangeValue: (value: RangePickerProps['value']) => void;
    setRecentDays: (value: string) => void;
    getAnalysisData?: (value?: RangePickerProps['value']) => void;
    /** 事件-告知时间选择器触发组件类型 */
    getTriggerType?: (type: DateRangePickerTriggerType, recentDays: string) => void;
}) {
    const dateOptions = useMemo<DefaultOptionType[]>(() => {
        const options = [...defaultOptions];
        if (searchToday) {
            options[0] = {
                value: '0',
                label: '今日',
            };
        }

        return options;
    }, [searchToday]);

    const {ubcClickLog} = useUbcLog();
    // 是否打开时间选择器
    // const [openRangePicker, setOpenRangePicker] = useState(false);

    // 下拉选择器change事件
    const handleChange = useCallback(
        (value: string) => {
            setRecentDays(value);
            if (value) {
                // 设置时间选择器时间
                const dateRange: RangePickerProps['value'] = [
                    dayjs().subtract(Math.max(Number(value) - (searchToday ? 1 : 0), 0), 'day'),
                    dayjs().subtract(searchToday ? 0 : 1, 'day'),
                ];
                setDateRangeValue(dateRange);

                // 触发查询数据
                getAnalysisData && getAnalysisData(dateRange);

                getTriggerType && getTriggerType('dropdown', value);
            } else {
                // 自定义-打开时间选择器面板
                // setOpenRangePicker(true);
            }

            // 埋点
            ubcClickLog(EVENT_TRACKING_CONST.DataAnalysisPeriod, {
                [EVENT_EXT_KEY_CONST.PERIOD_TIME]: defaultOptions.find(item => item.value === value)?.label,
            });
        },
        [setRecentDays, ubcClickLog, searchToday, setDateRangeValue, getAnalysisData, getTriggerType]
    );

    // 时间选择器change事件
    const onRangePickerChange = useCallback(
        (values: any) => {
            if (!values) {
                return;
            }

            const [startDate] = values;

            setDateRangeValue(values);

            // diff符合最近**天
            const diff = Math.abs(startDate.diff(dayjs(), 'day'));
            // 默认下拉选择器为'', 即自定义
            let days = '';

            if (dateOptions.map(item => item.value).includes(`${diff}`)) {
                // 设置下拉选择器
                days = diff.toString();
            }

            setRecentDays(days);
            ubcClickLog(EVENT_TRACKING_CONST.DataAnalysisDate);
            // 触发查询数据
            getAnalysisData && getAnalysisData(values);
            getTriggerType && getTriggerType('rangePicker', days);
        },
        [setDateRangeValue, dateOptions, ubcClickLog, getAnalysisData, getTriggerType, setRecentDays]
    );

    // 只有当前一年内可查
    // todo: 临时使用 any
    const disabledDate = useCallback(
        (date: any) => {
            const diffToday = searchToday ? 0 : 1;

            return (
                date.isBefore(dayjs().subtract(1, 'year').subtract(diffToday, 'day')) ||
                date.isAfter(dayjs().subtract(diffToday, 'day'))
            );
        },
        [searchToday]
    );

    return (
        <div className="z-0 flex items-center">
            <ConfigProvider
                theme={{
                    token: themeToken || {
                        // 容器高度
                        controlHeight: 36,
                        // 输入框容器背景色
                        colorBgContainer: '#f5f6fa',
                        // 输入框容器边框颜色色
                        colorBorder: '#f5f6fa',
                    },
                }}
            >
                <RangePicker
                    className="w-[20.75rem]"
                    style={{marginRight: gapX || '0.5rem'}}
                    locale={locale}
                    value={dateRangeValue}
                    disabledDate={disabledDate}
                    onChange={onRangePickerChange}
                />
                <Select
                    defaultValue="7"
                    value={recentDays}
                    style={{width: 120}}
                    onChange={handleChange}
                    options={dateOptions}
                />
            </ConfigProvider>
        </div>
    );
}
