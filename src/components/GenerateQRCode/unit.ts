/**
 * @file 前端生成二维码function
 * <AUTHOR>
 * 关于二维码的规范可以参考微信支付二维码规范：https://pay.weixin.qq.com/docs/partner/development/access-specification/qrcode-specification.html
 */

import {message} from 'antd';
import QRCode from 'qrcode';

/**
 * 生成二维码Canvas
 * @param url 生成二维码的链接
 * @param qrOptions 生成二维码的options
 * @returns Promise<HTMLCanvasElement>
 */
const generateQRCodeCanvas = (url: string, qrOptions?: object): Promise<HTMLCanvasElement> => {
    return new Promise((resolve, reject) => {
        // options包含二维码纠错等级、二维码大小、边距、颜色等样式设置属性
        const options = {
            // 纠错等级H, 容错率最高，适用于有遮挡场景
            errorCorrectionLevel: 'H',
            // 二维码大小
            width: 300,
            // 二维码白边
            // 微信公众号会给上传的二维码右下角打上水印，所以设置一个白边，避免水印被遮挡
            margin: 4,
            ...qrOptions,
        };

        QRCode.toCanvas(url, options, (err: any, qrCanvas: HTMLCanvasElement) => {
            if (err) {
                reject(err);
            } else {
                resolve(qrCanvas);
            }
        });
    });
};

/**
 * 将 logo 添加到二维码图片上
 * @param qrCanvas 二维码Canvas
 * @param logoSrc 二维码中间logo 图片地址
 * @returns Promise<HTMLCanvasElement>
 */
const addLogoToQRCode = (qrCanvas: HTMLCanvasElement, logoSrc: string): Promise<HTMLCanvasElement> => {
    return new Promise(resolve => {
        const ctx: CanvasRenderingContext2D | null = qrCanvas.getContext('2d');

        if (!ctx || !logoSrc) return qrCanvas;

        const logoImg = new Image();
        // 某些浏览器安全策略下，会报canvas.toDataURL错误，导致下载失败
        // Uncaught SecurityError: Failed to execute 'toDataURL' on 'HTMLCanvasElement': Tainted canvases may not be exported
        // 原因：canvas.drawImage 获取绘制了img，那么某些浏览器版本下会认为img跨域，canvas被污染，从而canvas.toDataURL报错
        // 解决方案：
        // 1、img服务资源请求头设置access-control-allow-origin: *，允许跨域访问。
        // 2、下载img前设置crossOrigin属性为anonymous，告诉浏览器在下载图像数据时请求CORS访问
        // 参考1: https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image
        // 参考2: https://stackoverflow.com/questions/22710627/tainted-canvases-may-not-be-exported
        logoImg.crossOrigin = 'anonymous';
        logoImg.src = logoSrc;

        logoImg.onload = () => {
            const qrSize = qrCanvas.width;
            // 一般容错能级为M的二维码覆盖面积不超过10%，容错能级为H的二维码被覆盖面积不超过25%
            // 设置 logo 大小为二维码大小的25%（margin=4时为黑白二维码尺寸的30%左右），面积即为9%
            const logoSize = qrSize / 4;
            const logoX = (qrSize - logoSize) / 2;
            const logoY = (qrSize - logoSize) / 2;

            ctx.drawImage(logoImg, logoX, logoY, logoSize, logoSize);
            resolve(qrCanvas);
        };

        logoImg.onerror = () => {
            // logoImg图片下载失败，直接返回不带logo的二维码
            resolve(qrCanvas);
        };
    });
};

/**
 * 生成带有 logo 的二维码Canvas
 * @param url 生成二维码的链接
 * @param logoSrc 二维码中间logo 图片地址
 * @param qrOptions 生成二维码的options
 * @returns qrCanvasWithLogo
 */
export const generateQRCodeWithLogo = async ({
    url,
    logoSrc,
    qrOptions,
}: {
    /** url 生成二维码的链接 */
    url: string;
    /** 二维码中间logo 图片地址 */
    logoSrc?: string;
    /** 生成二维码的options */
    qrOptions?: object;
}) => {
    if (!url) {
        throw new Error('缺少生成二维码的文本');
    }

    const qrCanvas = await generateQRCodeCanvas(url, qrOptions);
    if (!logoSrc) {
        return qrCanvas;
    }

    const qrCanvasWithLogo = await addLogoToQRCode(qrCanvas, logoSrc);

    return qrCanvasWithLogo;
};

/**
 * 生成带有 logo 的二维码Canvas
 * @param qrCanvas 二维码Canvas
 * @param fileName 下载二维码文件名
 * @returns
 */
export const downloadQRCode = async ({
    qrCanvas,
    fileName,
}: {
    /** 二维码Canvas */
    qrCanvas: HTMLCanvasElement;
    /** 下载二维码文件名 */
    fileName?: string;
}) => {
    try {
        // 输出的图像分辨率为96 dpi, 一般屏幕扫描二维码图像分辨率需不小于72dpi
        const qrCodeUrl = qrCanvas.toDataURL();

        const a = document.createElement('a');

        a.download = fileName || 'qrCode.png';
        a.href = qrCodeUrl;

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    } catch (e) {
        message.error('下载失败，请重试');
        throw e;
    }
};
