/**
 * @file 生成下载智能体二维码
 * <AUTHOR>
 */

import {Button, message} from 'antd';
import {useCallback, useState} from 'react';
import {BaseButtonProps} from 'antd/es/button/button';
import classNames from 'classnames';
import LingJingLogo from '@/assets/lingjing-logo.png';
import {downloadQRCode, generateQRCodeWithLogo} from './unit';

interface Props {
    /** 生成二维码的链接/文本 */
    url: string;
    /** 是否要logo, 默认需要灵境logo */
    hasLogo?: boolean;
    /** qrOptions 生成二维码的options */
    qrOptions?: object;
    /** 点击下载文字 */
    downloadText?: string;
    /** fileName 下载二维码文件名 */
    fileName?: string;

    btnType?: BaseButtonProps['type'];
    className?: string;
}

export default function DownloadQR({
    url,
    hasLogo = true,
    qrOptions,
    downloadText = '点击下载二维码',
    fileName = 'QRCode.png',
    btnType = 'link',
    className,
}: Props) {
    const [loading, setLoading] = useState(false);

    // 生成并下载二维码
    const downloadQR = useCallback(async () => {
        try {
            setLoading(true);

            const qrCanvas = await generateQRCodeWithLogo({
                url,
                logoSrc: hasLogo ? LingJingLogo : '',
                qrOptions,
            });

            downloadQRCode({
                qrCanvas,
                fileName,
            });
            setLoading(false);
        } catch (err: any) {
            message.error('下载失败，请重试');
            setLoading(false);
        }
    }, [url, hasLogo, qrOptions, fileName]);

    return (
        <Button
            disabled={!url || loading}
            type={btnType}
            className={classNames(btnType === 'link' ? 'h-auto border-0 p-0' : '', className)}
            onClick={downloadQR}
        >
            {downloadText}
        </Button>
    );
}
