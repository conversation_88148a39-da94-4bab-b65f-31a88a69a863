/**
 * @file 图片上传
 * <AUTHOR>
 * 2023/12/22重构，传参类型继承UploadProps，可由非受控表单控制，其他参数可直接覆盖
 * 示例：
 * <FormItem name={['agentInfo', 'logoUrl']} valuePropName="imgUrl"
 *      trigger="setImgUrl" validateTrigger={['setImgUrl']}>
        <UploadImage />
    </FormItem>
 */

import React, {MouseEvent, useCallback, useState, useEffect} from 'react';
import {Upload, Image} from 'antd';
import type {UploadChangeParam} from 'antd/es/upload';
import type {RcFile, UploadFile, UploadProps} from 'antd/es/upload/interface';
import classNames from 'classnames';
import {getBase64, getImgWH} from '@/utils/image';
const {Dragger} = Upload;

interface UploadImageProps extends UploadProps {
    imgUrl?: string;
    setImgUrl?: (imgUrl: string) => void;
    tips?: string;
    errMsg: string;
    setErrMsg: (errMsg: string) => void;
    circleLogo?: boolean;
    isCutSquare?: boolean;
}

// eslint-disable-next-line complexity
const ImageUpload = (props: UploadImageProps) => {
    const {imgUrl, setImgUrl, tips, errMsg, setErrMsg, circleLogo = false, isCutSquare = false} = props;
    const [loading, setLoading] = useState(false);
    // 内部load状态
    const [loadPercent, setLoadPercent] = useState<number | undefined>(0);
    const [base64ImgUrl, setBase64ImageUrl] = useState<string>('');
    const [isLongPic, setIsLongPic] = useState<boolean>(false);
    const [previewOpen, setPreviewOpen] = useState(false);

    // 上传时状态处理
    const onChange = useCallback(
        // eslint-disable-next-line complexity
        (info: UploadChangeParam<UploadFile>) => {
            if (info.file.status === 'uploading') {
                setLoading(true);

                const percent = Math.min(Math.floor(info.file.percent || 0), 99);
                setLoadPercent(percent);

                // 前端文件流生成base64图片
                if (!base64ImgUrl) {
                    getBase64(info.file.originFileObj as RcFile).then(url => setBase64ImageUrl(url));
                }
            } else if (info.file.status === 'done') {
                setLoading(false);

                const {errno, msg, data: url} = info.file.response;
                if (errno === 0 && url) {
                    setImgUrl && setImgUrl(url);
                } else {
                    setErrMsg(msg || '图片上传失败，请重新上传!');
                    setImgUrl && setImgUrl('');
                    setBase64ImageUrl('');
                }
            } else if (info.file.status === 'error') {
                setLoading(false);
                setErrMsg('图片上传失败，请重新上传!');
            }
        },
        [base64ImgUrl, setImgUrl, setErrMsg]
    );

    /**
     * 点击上传区域，部分场景禁止冒泡调起Dragger组件的上传事件
     * 上传中、已上传完成，阻止冒泡调起上传文件选择框
     */
    const draggerAreaClick = useCallback(
        (e: MouseEvent) => {
            if (loading || imgUrl) {
                e.stopPropagation();
            }
        },
        [imgUrl, loading]
    );

    /**
     * 删除图片
     */
    const removeImg = useCallback(
        (e?: MouseEvent) => {
            setImgUrl && setImgUrl('');
            setBase64ImageUrl('');

            // 删除图片，阻止冒泡调起上传文件选择框
            e && e.stopPropagation();
        },
        [setImgUrl]
    );
    /**
     * 预览图片
     */
    const previewImg = useCallback((e: MouseEvent) => {
        e.stopPropagation();
        setPreviewOpen(true);
    }, []);

    /**
     * 关闭预览
     */
    const handleCancel = useCallback(() => {
        setPreviewOpen(false);
    }, []);

    useEffect(() => {
        if (base64ImgUrl) {
            getImgWH(base64ImgUrl).then((res: any) => {
                setIsLongPic(res.height > res.width);
            });
            return;
        }

        if (imgUrl) {
            getImgWH(imgUrl).then((res: any) => {
                setIsLongPic(res.height > res.width);
            });
        }
    }, [base64ImgUrl, imgUrl]);

    return (
        <Dragger
            {...{
                name: 'image',
                action: '/lingjing/app/uploadPhoto',
                showUploadList: false,
                onChange,
                ...props,
            }}
            style={{border: '1px #eceef3 solid'}}
        >
            <div
                className={`relative h-[12.1875rem] hover:${!loading && !imgUrl ? 'cursor-pointer' : 'cursor-default'}`}
                onClick={draggerAreaClick}
            >
                <div className="flex items-center justify-center">
                    {/* 图片不存在且未上传中时展示默认图标、上传按钮和提示 */}
                    {!imgUrl && !loading && (
                        <div className="flex flex-col items-center">
                            <div
                                className={`${errMsg ? 'mt-5' : 'mt-8'} ${
                                    circleLogo ? 'rounded-full' : 'rounded-[9px]'
                                } flex h-[5.0625rem] w-[5.0625rem] items-center justify-center bg-[#f1f1ff]`}
                            >
                                <span className="iconfont icon-picture text-2xl text-primary"></span>
                            </div>
                            <div>
                                <p className="text-black-base mb-[0.3125rem] mt-[0.94rem] text-sm">
                                    将图片拖到此处，或者
                                    <a className="underline">点击上传</a>
                                </p>
                                {tips && (
                                    <p className="flex h-[1.35rem] scale-[0.8333] items-center text-xs text-flow-hover">
                                        {tips}
                                    </p>
                                )}
                                {errMsg && <p className="text-sm leading-[1.375rem] text-error">{errMsg}</p>}
                            </div>
                        </div>
                    )}
                    {/* 图片上传时展示图片+百分比 */}
                    {loading && (
                        <div
                            className={`relative mt-6 flex h-36 w-36 items-center justify-center overflow-hidden bg-[#f1f1ff] ${
                                circleLogo ? 'rounded-full' : 'rounded-[9px]'
                            }`}
                        >
                            <img
                                className={classNames('object-cover', {
                                    'w-full': isCutSquare ? isLongPic : !isLongPic,
                                    'h-full': isCutSquare ? !isLongPic : isLongPic,
                                })}
                                src={base64ImgUrl || imgUrl}
                                alt="avatar"
                            />
                            <div className="absolute left-0 right-0 flex h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] text-white">
                                <span className="text-xl font-bold">{loadPercent}%</span>
                            </div>
                        </div>
                    )}
                    {/* 图片上传成功展示图片 */}
                    {imgUrl && (
                        <div
                            className={`group relative mt-6 flex h-36 w-36 items-center justify-center overflow-hidden ${
                                circleLogo ? 'rounded-full' : 'rounded-[9px]'
                            }`}
                        >
                            <img
                                className={classNames('object-cover', {
                                    'w-full': isCutSquare ? isLongPic : !isLongPic,
                                    'h-full': isCutSquare ? !isLongPic : isLongPic,
                                })}
                                src={base64ImgUrl || imgUrl}
                                alt="avatar"
                            />
                            <div className="absolute left-0 right-0 hidden h-full w-full items-center justify-around rounded-[9px] bg-black/[.4] group-hover:flex">
                                <span
                                    className={'iconfont icon-visual text-4xl text-white hover:cursor-pointer'}
                                    onClick={previewImg}
                                ></span>
                                <span
                                    className={'iconfont icon-delete text-4xl text-white hover:cursor-pointer'}
                                    onClick={removeImg}
                                ></span>
                            </div>
                            <Image
                                style={{display: 'none'}}
                                preview={{
                                    visible: previewOpen,
                                    src: imgUrl,
                                    onVisibleChange: handleCancel,
                                    toolbarRender: originalNode => (isCutSquare ? <></> : originalNode),
                                }}
                            />
                        </div>
                    )}
                </div>
            </div>
        </Dragger>
    );
};

export default ImageUpload;
