/**
 * @file 自定义 login api，注入 ChatView SDK
 *
 */
import {useCallback} from 'react';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import useInitLogin from '@/components/Login/utils/useInitLogin';
import api from '@/api/login';
import {UserData} from '@/api/login/interface';

export const useChatViewLogin = () => {
    const uniformLogin = useUniformLogin();
    const initLogin = useInitLogin();

    const login = useCallback(async () => {
        // 请求 user/info 接口获取实时的登录态
        // 不使用 userInfoStore 里的 login 状态的原因：
        // 用户在平台已登录的情况下，切换其他 tab 页退登，再返回平台获取到的 isLogin 为 true，需要从请求获取此时的登录态
        try {
            const response: UserData = await api.getUserInfo(
                {
                    redir: window.location.href,
                },
                {
                    // 禁止异常弹窗，未登录10010 无权限 10020 无需弹窗
                    forbiddenToast: true,
                }
            );

            return Promise.resolve({
                userInfo: {
                    // 渲染只用到了 displayName 和 avatarUrl
                    nickName: response?.userInfo?.displayName,
                    avatarUrl: response?.userInfo?.portrait,
                    displayName: response?.userInfo?.displayName,
                },
            });
        } catch (err) {
            initLogin();
            uniformLogin();
            return Promise.reject(err);
        }
    }, [uniformLogin, initLogin]);

    return login;
};
