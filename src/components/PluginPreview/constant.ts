export enum VersionType {
    Online = 'online',
    Develop = 'develop',
    Audit = 'audit',
}

const AGENT_PROXY_WS_SERVER_PREFIX = 'https://agent-proxy-ws.baidu.com';
const AGENT_PROXY_SERVER_PREFIX = window.location.origin;

export const isProd = () => {
    return ['agents.baidu.com', 'plugin.baidu.com', 'plugin-prod.now.baidu.com', 'plugin.now.baidu.com'].includes(
        window.location.hostname
    );
};

export const serviceUrl = {
    getAppInfo: `${AGENT_PROXY_SERVER_PREFIX}/get_app_info`,
    init: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/init`,
    conversation: `${isProd() ? AGENT_PROXY_WS_SERVER_PREFIX : AGENT_PROXY_SERVER_PREFIX}/agent/call/conversation`,
    history: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/history`,
    cleanContext: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/clean_context`,
    dangerCheck: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/danger_check`,
    feedBackLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/like/receive`,
    feedBackDisLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/dislike/receive`,
    getLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/like/get`,
    cancel: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/cancel`,
};
