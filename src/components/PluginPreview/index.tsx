/**
 * @file 插件预览组件（有侧导，审核、线上、分享版使用）
 *
 */
import '@baidu/lingjing-fe-agent-adapter/dist/js/plugin.js';
import '@baidu/lingjing-fe-agent-adapter/dist/css/plugin.css';
import {useRef, useEffect} from 'react';
import {useParams} from 'react-router-dom';
import {useResource} from 'react-suspense-boundary';
import api from '@/api/pluginEdit';
import {serviceUrl, VersionType} from '@/components/PluginPreview/constant';
import {useChatViewLogin} from '@/components/PluginPreview/hooks/useChatViewLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';

interface ChatViewContainerProps {
    chatViewConfig: {
        [key: string]: any;
    };
    pluginMode?: 'debug';
}

const Chat = window.LingJingAgentSDK.PluginPreview;

const formatExpireTimeStamp = (milliseconds: number) => {
    const date = new Date(milliseconds);
    return date.toLocaleString('zh-GB', {timeZone: 'Asia/Shanghai'});
};

export function PluginPreview({chatViewConfig, pluginMode}: ChatViewContainerProps) {
    const {pluginSessionId} = useParams();
    const decodedPluginSessionId = decodeURIComponent(pluginSessionId || '');
    const [previewData] = useResource(api.getPreviewData, {pluginSessionId: decodedPluginSessionId});

    const chatViewContainerRef = useRef<HTMLDivElement>(null);
    const chatRef = useRef<typeof Chat | null>(null);

    const [hasEnhancedAccess] = useUserInfoStore(store => [store.userInfoData?.userInfo.hasEnhancedAccess]);

    // 将平台实现的 login api 注入 ChatViewSDK
    const login = useChatViewLogin();

    useEffect(() => {
        if (pluginSessionId && !chatRef.current && Chat) {
            // 初始化 ChatViewSDK
            chatRef.current = new Chat({
                pluginInfo: {
                    pluginSessionId: pluginSessionId,
                    pluginMode,
                },
                // 临时加兜底 agentId
                agentId: previewData?.appId || '8poqz2OZGhqhQC4FfSGzTS24ho3t22m7',
                container: chatViewContainerRef.current!,
                serviceUrl,
                versionType: VersionType.Online,
                chatViewConfig: {
                    ...chatViewConfig,
                    nav: {
                        ...chatViewConfig.nav,
                        devInfo: {
                            summary: '欢迎来到插件预览助手，您可以在此预览文心智能体平台的插件使用效果。',
                            tip: pluginMode
                                ? `效果预览, 有效期至 ${formatExpireTimeStamp(previewData?.expireTimeStamp)}`
                                : '仅作效果预览, 实际情况以线上为准',
                        },
                    },
                    inputBox: {
                        atName: previewData?.pluginName,
                    },
                    userInfo: {
                        hasEnhancedAccess,
                    },
                },
                api: {
                    login,
                },
            });

            chatRef.current?.render();
        }
    }, [
        pluginSessionId,
        chatViewContainerRef,
        chatRef,
        previewData?.appId,
        previewData?.pluginName,
        previewData?.expireTimeStamp,
        pluginMode,
        chatViewConfig,
        login,
        hasEnhancedAccess,
    ]);

    return (
        <div className={'absolute flex h-full w-full'}>
            <div className="h-full w-full bg-white" ref={chatViewContainerRef} />
        </div>
    );
}
