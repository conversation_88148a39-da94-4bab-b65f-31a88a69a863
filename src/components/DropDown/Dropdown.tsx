/**
 * @file 单行下拉框
 * <AUTHOR>
 *
 */

import classNames from 'classnames';
import {useCallback, useState} from 'react';
import {Button} from 'antd';

interface Props {
    msgId: string; // 下拉框id
    title: string; // 标题
    content: string; // 内容
    dropClick?: (isDropdown: boolean, msgId: string) => void; // 点击下拉按钮
    actionText?: string; // 动作按钮文案
    clickHandle?: (msgId: string) => Promise<void>; // 动作按钮点击事件
}
export const Dropdown = ({msgId, title, content, dropClick, actionText, clickHandle}: Props) => {
    // 判断是否下拉
    const [isDropdown, setIsDropdown] = useState(false);
    // 删除loading
    const [loading, setLoading] = useState(false);

    // 点击下拉按钮
    const handleDropClick = useCallback(() => {
        setIsDropdown(!isDropdown);
        dropClick && dropClick(!isDropdown, msgId);
    }, [dropClick, isDropdown, msgId]);

    // 动作按钮点击事件
    const actionClickHandle = useCallback(async () => {
        setLoading(true);

        clickHandle &&
            (await clickHandle(msgId).finally(() => {
                setLoading(false);
            }));
    }, [clickHandle, msgId]);

    return (
        <div
            key={msgId}
            className={classNames('mt-[9px] flex  w-full justify-between rounded-[9px]  px-[15px] py-3 ', {
                'bg-colorBgInput': isDropdown,
                'max-h-[70px] border': !isDropdown,
            })}
        >
            {/* 下拉按钮 */}
            <div className="mr-[18px]">
                <div
                    onClick={handleDropClick}
                    className={classNames('iconfont icon-Down cursor-pointer', {
                        'rotate-180': isDropdown,
                    })}
                />
            </div>
            {/* 内容区域 */}
            <div className="flex w-full flex-col ">
                {/* 标题与删除按钮 */}
                <div className="flex items-center justify-between">
                    <div className="flex w-[452px] font-medium">
                        <div
                            className={classNames('w-full', {
                                truncate: !isDropdown,
                            })}
                        >
                            {title}
                        </div>
                    </div>
                    {actionText && (
                        <Button
                            className="h-0 cursor-pointer border-none p-0 text-sm font-normal text-primary"
                            onClick={actionClickHandle}
                            type="text"
                            loading={loading}
                            icon={<>{actionText}</>}
                        />
                    )}
                </div>

                {/* 内容区域 */}
                <div
                    className={classNames('w-[452px] font-normal text-[#848691]', {
                        truncate: !isDropdown,
                    })}
                >
                    {content}
                </div>
            </div>
        </div>
    );
};
