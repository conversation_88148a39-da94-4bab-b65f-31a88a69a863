import {Form, FormInstance, Input} from 'antd';
import {NamePath} from 'antd/es/form/interface';
import {useCallback, useEffect, useState, useRef} from 'react';
import {PHONE_REG, validatePhone} from '@/utils/formValidator';
import VerifyCode from './VerifyCode';
const {Item} = Form;

interface OperatorFormProps {
    phoneNamePath: NamePath;
    codeNamePath: NamePath;
    checkPhoneVerify: (phone: string) => () => Promise<boolean>;
    sendAuthCode: (phone: string) => () => Promise<any>;
    hideVerifyCodeBtn?: boolean;
    // 验证码请求未发送前不展示验证码输入框
    showCodeInputDefault?: boolean;
    // 提交按钮 调用的接口 ，得到的校验状态，同步到验证码表单处，如有变更，控制显示隐藏
    isVerify: boolean | undefined;
    form?: FormInstance;
    onCodeSendCB?: () => void;
    disabled?: boolean;
    phoneValidator?: (rule: any, value: string) => Promise<void>;
}

const PhoneCodeFormGroup = (props: OperatorFormProps) => {
    const [disabled, setDisabled] = useState(true);
    const [lastPhone, setLastPhone] = useState<string>('');
    const [showVerifyBtn, setShowVerifyBtn] = useState<boolean>(true);
    const [showVerifyCode, setShowVerifyCode] = useState<boolean>();
    // 记录初始手机号值，用于判断是否被修改过
    const initialPhoneRef = useRef<string>('');
    const {
        phoneNamePath,
        codeNamePath,
        showCodeInputDefault = true,
        checkPhoneVerify,
        sendAuthCode,
        hideVerifyCodeBtn,
        isVerify,
        form,
        onCodeSendCB,
        disabled: propsDisabled,
        phoneValidator,
    } = props;
    const phoneValue = Form.useWatch(phoneNamePath, form);

    useEffect(() => {
        setDisabled(!(phoneValue && PHONE_REG.exec(phoneValue)));
    }, [phoneValue]);

    // 记录初始手机号值
    useEffect(() => {
        if (phoneValue && !initialPhoneRef.current) {
            initialPhoneRef.current = phoneValue;
        }
    }, [phoneValue]);

    useEffect(() => {
        // 考虑到 验证码请求未发送前不展示验证码输入框，所以isVerify 默认 undefined，showCodeInputDefault，，如果isVerify是boolean，那使用isVerify控制验证码表单显隐
        setShowVerifyCode((typeof isVerify === 'boolean' ? isVerify : showCodeInputDefault) && !hideVerifyCodeBtn);
        // 默认 验证码按钮显示
        setShowVerifyBtn(typeof isVerify === 'boolean' ? isVerify : true);
    }, [showCodeInputDefault, isVerify, hideVerifyCodeBtn]);

    const onCodeSended = useCallback(() => {
        setShowVerifyCode(true);
        onCodeSendCB?.();
    }, [onCodeSendCB]);

    const checkPhone = useCallback(() => {
        setLastPhone(phoneValue);
        return checkPhoneVerify(phoneValue)();
    }, [checkPhoneVerify, phoneValue]);

    const changePhone = () => {
        // 当手机号发生改变时，展示验证码按钮
        if (lastPhone !== phoneValue) {
            setShowVerifyBtn(true);
        }

        // 如果手机号被修改过（与初始值不同），自动显示验证码输入框
        if (initialPhoneRef.current && phoneValue !== initialPhoneRef.current) {
            setShowVerifyCode(true);
        }
    };

    /**
     * 校验完接口后 对 验证码按钮、验证码表单校验以及显示隐藏状态设置
     */
    const sendVerify = useCallback(
        (show: boolean) => {
            setShowVerifyBtn(show);
            setShowVerifyCode(show);
            onCodeSendCB?.();
        },
        [onCodeSendCB]
    );

    return (
        <>
            <Item
                label="手机号"
                name={phoneNamePath}
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                    {required: true, message: '请输入手机号'},
                    {
                        validateTrigger: ['onBlur'],
                        validator: phoneValidator || validatePhone('手机号格式有误，请重新输入'),
                    },
                ]}
            >
                <Input
                    className="w-[320px]"
                    placeholder="请输入手机号"
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={changePhone}
                    disabled={propsDisabled}
                    suffix={
                        hideVerifyCodeBtn ? (
                            '已验证'
                        ) : showVerifyBtn ? (
                            <VerifyCode
                                size="small"
                                disabled={disabled || hideVerifyCodeBtn}
                                className="h-[22px]"
                                type="link"
                                countdownLimit={60}
                                checkPhone={checkPhone}
                                sendAuthCode={sendAuthCode(phoneValue)}
                                onCodeSended={onCodeSended}
                                sendVerify={sendVerify}
                            />
                        ) : (
                            <span style={{color: '#39B362'}}>已验证通过</span>
                        )
                    }
                />
            </Item>
            {showVerifyCode && (
                <Item
                    label="验证码"
                    name={codeNamePath}
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                        {required: true, message: '请输入验证码'},
                        {
                            validateTrigger: ['onBlur'],
                            pattern: /^\d{6}$/,
                            message: '验证码格式有误，请重新输入6位数字验证码',
                        },
                    ]}
                >
                    <Input className="w-[320px]" placeholder="请输入6位数字验证码" />
                </Item>
            )}
        </>
    );
};

export default PhoneCodeFormGroup;
