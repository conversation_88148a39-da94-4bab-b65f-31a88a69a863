import {Button} from 'antd';
import {ButtonType, ButtonProps} from 'antd/lib/button';
import omit from 'lodash/omit';
import {FC, forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';

export interface VerifyCodeProps extends ButtonProps {
    checkPhone: () => Promise<boolean>;
    sendAuthCode: () => Promise<void>;
    countdownLimit: number;
    type: ButtonType;
    onCodeSended?: () => void;
    sendVerify?: (isVerify: boolean) => void;
}

const VerifyCode: FC<VerifyCodeProps> = forwardRef((props, ref) => {
    const [time, setTime] = useState(60);
    const [loading, setLoading] = useState<boolean>(false);
    const timerRef = useRef<NodeJS.Timer>();
    const {checkPhone, sendAuthCode, countdownLimit, onCodeSended, sendVerify} = props;

    useEffect(() => {
        setTime(countdownLimit || 60);
    }, [countdownLimit]);

    useEffect(() => {
        timerRef.current && clearInterval(timerRef.current);
        return () => {
            timerRef.current && clearInterval(timerRef.current);
        };
    }, []);

    useEffect(() => {
        if (time === 0) {
            clearInterval(timerRef.current);
            setTime(countdownLimit || 60);
        }
    }, [time, countdownLimit]);

    const getCode = useCallback(async () => {
        setLoading(true);
        try {
            const isVerify = await checkPhone(); // 判断手机号是否需要校验
            sendVerify?.(isVerify);
            if (isVerify && sendAuthCode) {
                await sendAuthCode(); // 发送验证码
                // eslint-disable-next-line no-param-reassign
                timerRef.current = setInterval(() => setTime(time => --time), 1000); // 启动倒计时
                onCodeSended?.();
            }

            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    }, [checkPhone, sendAuthCode, onCodeSended, sendVerify]);

    useImperativeHandle(ref, () => ({
        getCode,
    }));

    const btnProps = omit(props, ['sendAuthCode', 'countdownLimit', 'onCodeSended', 'checkPhone', 'sendVerify']);

    return (
        <Button
            {...btnProps}
            loading={loading}
            onClick={getCode}
            disabled={btnProps.disabled || (time !== countdownLimit && !!time)}
        >
            {time === countdownLimit || !time ? '验证码' : <span className="text-primary">{`${time}s`}</span>}
        </Button>
    );
});

export default VerifyCode;
