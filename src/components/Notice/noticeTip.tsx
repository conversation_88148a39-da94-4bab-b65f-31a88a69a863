/**
 * @file 消息红点
 * <AUTHOR>
 */
import React from 'react';
import {useCallback, useEffect} from 'react';
import {Badge} from 'antd';
import api from '@/api/notice';
import {NoticeName} from '@/api/notice/interface';
import {useNoticeTipStore} from '@/store/notice/noticeTipStore';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUserInfoStore} from '@/store/login/userInfoStore';

interface NoticeTipProps {
    left?: number;
    top?: number;
    size?: number;
}

/**
 * @description 智能客服公告红点
 */
const NoticeTip: React.FC<NoticeTipProps> = ({left = 0, top = 0, size = 4}) => {
    const style = {
        left: left,
        top: top,
        width: size,
        height: size,
    };

    const [isShowTip, setTipShowState, setNoticeId] = useNoticeTipStore(store => [
        store.isShowTip,
        store.setTipShowState,
        store.setNoticeId,
    ]);
    const isLogin = useUserInfoStore(store => store.isLogin);

    const {ubcShowLog} = useUbcLog();

    /**
     * 请求红点展示状态
     */
    const getTipStatus = useCallback(async () => {
        const res = await api.noticePopup({name: NoticeName.TipPop}).catch(() => {});
        if (res && res.notice && res.isShow) {
            setNoticeId(res.notice.noticeId);
        }

        res && setTipShowState(res.isShow);
    }, [setTipShowState, setNoticeId]);

    useEffect(() => {
        isLogin && getTipStatus();
    }, [isLogin, getTipStatus]);

    useEffect(() => {
        ubcShowLog(EVENT_TRACKING_CONST.HomeNoticeTipShow);
    }, [ubcShowLog]);

    return (
        <>
            {isShowTip && (
                <div className="absolute" style={style}>
                    <Badge status="error" className="-ml-[6px] -mt-4" />
                </div>
            )}
        </>
    );
};

export default NoticeTip;
