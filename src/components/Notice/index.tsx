import {useCallback, useState, useEffect, useImperativeHandle, forwardRef} from 'react';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import {Image, Button, ConfigProvider, PopoverProps} from 'antd';
import {Link} from 'react-router-dom';
import fallbackUrl from '@/assets/image-empty.png';
import {useIsMobileStore} from '@/store/home/<USER>';
import {useAiBotStore} from '@/store/agent/aiBot';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import urls from '@/links';
import api from '@/api/notice';
import {getAiSdk} from '@/components/CustomerService/initSdk';
import {NoticeName} from '@/api/notice/interface';
import {useNoticeTipStore} from '@/store/notice/noticeTipStore';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import StyledPopover from '@/components/Popover/StyledPopover';

const popoverClassName = (isPlusClass: boolean) => {
    const commonClassName = `
        .ant-popover-content {
            .ant-popover-inner {
                padding: 14px !important;
                border-radius: 8px !important;
                width: 280px;
                box-shadow: ${
                    isPlusClass ? '0px 30px 200px 0px rgba(29, 34, 82, 0.2)' : '0px 8px 40px 0px rgba(29, 34, 82, 0.06)'
                };
            }
        }
    `;
    return css(commonClassName);
};

const SummaryStyled = styled.div`
    position: relative;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
`;

type NoticePopoverProps = Omit<PopoverProps, 'content'> & {buttonClassName: string};

/**
 * @description 平台公告组件 封装 antd Popover 组件
 */
const NoticePopover: React.FC<NoticePopoverProps & {ref: any}> = forwardRef(({...props}, ref) => {
    const [open, setOpen] = useState(false);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const isMobile = useIsMobileStore(store => store.isMobile);
    const navigate = useNavigate();
    const {setAiBotState} = useAiBotStore(state => ({
        setAiBotState: state.setAiBotState,
    }));

    const [previewOpen, setPreviewOpen] = useState(false);
    const [noticeContent, setNoticeId, setNoticeContent, deleteNoticeTip] = useNoticeTipStore(store => [
        store.noticeContent,
        store.setNoticeId,
        store.setNoticeContent,
        store.deleteNoticeTip,
    ]);
    const {ubcClickLog, ubcShowLog} = useUbcLog();

    const AiSdk = getAiSdk();

    /**
     * 关闭公告
     */
    const handleClose = useCallback(
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            e.stopPropagation();
            setOpen(false);
            ubcClickLog(EVENT_TRACKING_CONST.HomeNoticeCloseBtn);
        },
        [ubcClickLog]
    );

    /**
     * 对外暴露关闭公告事件
     */
    useImperativeHandle(ref, () => {
        return {
            closeNoticePopover() {
                setOpen(false);
            },
        };
    });

    /**
     * 「去提问」按钮点击回调事件
     */
    const handleGoAsk = useCallback(async () => {
        await deleteNoticeTip();
        setOpen(false);

        if (isMobile) {
            setAiBotState(true);
            navigate(urls.agentAISDK.raw());
        } else {
            AiSdk.openBot({
                // noticeId: isShowTip ? noticeContent.noticeId : '',
            });
        }

        ubcClickLog(EVENT_TRACKING_CONST.HomeNoticeGoAskBtn);
    }, [deleteNoticeTip, ubcClickLog, navigate, setAiBotState, isMobile, AiSdk]);

    /**
     * 预览图片
     */
    const previewImg = useCallback(() => {
        setPreviewOpen(true);
    }, []);

    /**
     * 关闭预览
     */
    const handleCancel = useCallback(() => {
        setPreviewOpen(false);
    }, []);

    /**
     * 请求公告状态、内容
     */
    const getNoticeContent = useCallback(async () => {
        const res = await api.noticePopup({name: NoticeName.NoticePop}).catch(() => {});
        if (res && res.notice) {
            setNoticeContent(res.notice);
            setNoticeId(res.notice?.noticeId);
        }

        res && setOpen(res.isShow);
    }, [setNoticeContent, setNoticeId]);

    /**
     * 公告内按钮点击
     */
    const handleButtonClick = useCallback(
        (linkUrl: string) => () => {
            window.location.replace(linkUrl);
            deleteNoticeTip();
            ubcClickLog(EVENT_TRACKING_CONST.HomeNoticeLinkBtn);
        },
        [deleteNoticeTip, ubcClickLog]
    );

    /**
     * 查看全部按钮点击
     */
    const handleViewAllClick = useCallback(() => {
        deleteNoticeTip();
        setOpen(false);
        ubcClickLog(EVENT_TRACKING_CONST.HomeNoticeViewAllBtn);
    }, [deleteNoticeTip, ubcClickLog]);

    useEffect(() => {
        isLogin && getNoticeContent();
    }, [isLogin, getNoticeContent]);

    useEffect(() => {
        ubcShowLog(EVENT_TRACKING_CONST.HomePageNotice);
    }, [ubcShowLog]);

    return (
        <ConfigProvider
            theme={{
                token: {
                    sizePopupArrow: 20,
                },
            }}
        >
            <StyledPopover
                open={open}
                autoAdjustOverflow
                {...props}
                content={
                    <div className="flex flex-col gap-[10px]">
                        {/* 标题 */}
                        <div className="flex justify-between">
                            <span className="text-[16px] font-semibold leading-[22px]">{noticeContent.title}</span>
                            <span
                                className="iconfont icon-close cursor-pointer text-[14px] leading-[22px] text-gray-secondary"
                                onClick={handleClose}
                            ></span>
                        </div>

                        {/* 图片 */}
                        {noticeContent.images?.length > 0 && (
                            <>
                                {noticeContent.images?.map(url => (
                                    <div key={url} className="group relative w-[252px] cursor-pointer">
                                        <div className="h-[168px] w-[252px] overflow-hidden rounded-[4px]">
                                            <img className="h-full w-full object-cover" src={url || fallbackUrl} />
                                        </div>
                                        <div
                                            className="absolute left-0 top-0 hidden h-full w-full items-center justify-around rounded-[4px] bg-black/[.2] group-hover:flex group-hover:justify-center group-hover:gap-2"
                                            onClick={previewImg}
                                        >
                                            <span
                                                className={
                                                    'iconfont icon-visual text-[36px] text-white hover:cursor-pointer'
                                                }
                                            ></span>
                                        </div>
                                        {previewOpen && (
                                            <div style={{display: 'none'}}>
                                                <Image
                                                    src={url}
                                                    preview={{
                                                        visible: previewOpen,
                                                        onVisibleChange: handleCancel,
                                                        toolbarRender: () => null,
                                                    }}
                                                />
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </>
                        )}

                        {/* 摘要 */}
                        <div className="relative w-full leading-[22px] text-gray-secondary">
                            <SummaryStyled>
                                {noticeContent.summary}
                                {/* 文字截断 & 查看全部按钮 */}
                                {noticeContent.detailUrl && (
                                    <>
                                        <span
                                            className="absolute bottom-0 right-[62px] h-[22px] w-[84px]"
                                            style={{
                                                background:
                                                    'linear-gradient(269deg, #fff 9.72%, #fff 36.23%, rgba(255, 255, 255, .1) 92.71%)',
                                            }}
                                        ></span>
                                        <span className="absolute bottom-0 right-0">
                                            <Link
                                                to={noticeContent.detailUrl}
                                                className="bg-white text-[14px] text-gray-tertiary hover:text-primary active:text-[#8FA1F8]"
                                                target="_blank"
                                                onClick={handleViewAllClick}
                                            >
                                                查看全部
                                                <span className="iconfont icon-right text-[14px]"></span>
                                            </Link>
                                        </span>
                                    </>
                                )}
                            </SummaryStyled>
                        </div>

                        {/* 按钮 */}
                        <div
                            className={classNames('mt-[5px] flex flex-wrap justify-end gap-[8px]', {
                                'mt-[10px]': noticeContent.images?.length === 0,
                                'mt-[5px]': noticeContent.images?.length > 0,
                            })}
                        >
                            {/* 固定文案按钮 */}
                            <Button
                                className={classNames(
                                    'h-[30px] text-center leading-[12px] ' + (props.buttonClassName || ''),
                                    {
                                        'font-semibold': noticeContent.buttons?.length === 0,
                                        'text-default': noticeContent.buttons?.length > 0,
                                    }
                                )}
                                type={noticeContent.buttons?.length === 0 ? 'primary' : 'default'}
                                onClick={handleGoAsk}
                            >
                                去提问
                            </Button>

                            {/* 配置的按钮 */}
                            {noticeContent.buttons?.length > 0 &&
                                noticeContent.buttons?.map(button => (
                                    <Button
                                        key={button.text}
                                        className={
                                            'h-[30px] text-center font-semibold leading-[12px] ' +
                                            (props.buttonClassName || '')
                                        }
                                        type="primary"
                                        onClick={handleButtonClick(button.linkUrl)}
                                    >
                                        {button.text}
                                    </Button>
                                ))}
                        </div>
                    </div>
                }
                overlayClassName={popoverClassName(noticeContent.images?.length > 0)}
            />
        </ConfigProvider>
    );
});

export default NoticePopover;
