/* eslint-disable complexity */
/**
 * @file 路由拦截器
 * <AUTHOR>
 */

import {lazy} from 'react';
import {Navigate, Route, Routes} from 'react-router-dom';
import urls from '@/links';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {BaseLayoutM, BaseLayoutPC} from '@/modules';
import CreateAudio from '@/modules/agentPromptEditV2/mobile/components/CreateAudioWithToken/CreateAudio';
import Loading from '@/components/Loading/LingJingLoading';
import CenterPlugin from '@/modules/centerPlugin';
import CenterPluginDetail from '@/modules/centerPluginDetail';
import ActivityListPc from '@/modules/activityList/index-pc';

const isMobile = isMobileDevice();
const Center = lazy(() => {
    return isMobile ? import('@/modules/center/index-m') : import('@/modules/center/index-pc');
});

const BindTpAuthorization = lazy(() => {
    return isMobile ? import('@/modules/tpAuthorization/index-m') : import('@/modules/tpAuthorization/index-pc');
});

const Rank = lazy(() => import('@/modules/rank'));
const Agreement = lazy(() => import('@/modules/docs/Agreement'));
const Error = lazy(() => import('@/modules/error'));

const AbilityPluginSharePreview = lazy(() => import('@/modules/abilityPluginPreview/Share'));
const AbilityPluginAuditPreview = lazy(() => import('@/modules/abilityPluginPreview/Audit'));
const AbilityPluginReleasePreview = lazy(() => import('@/modules/abilityPluginPreview/Release'));

const DataPluginAuditPreview = lazy(() => import('@/modules/dataPluginPreview/Audit'));
const DataPluginReleasePreview = lazy(() => import('@/modules/dataPluginPreview/Release'));
const AgentPreview = lazy(() => {
    return isMobile ? import('@/modules/agentPreview/index-m') : import('@/modules/agentPreview/index-pc');
});
const ActivityCenter = lazy(() => import('@/modules/activity'));
const NvidiaActivityDetail = lazy(() => import('@/modules/activity/ActivityDetail'));
const ActivityTeam = lazy(() => import('@/modules/activity/ActivityTeam'));
// 开发者赚钱季大赛
const ActivityDpCenter = lazy(() => import('@/modules/activity/developerEarning'));
const ActivityDpDetail = lazy(() => import('@/modules/activity/developerEarning/ActivityDetail'));

const ActivityDetail = lazy(() => {
    return isMobile ? import('@/modules/activityDetail/index-m') : import('@/modules/activityDetail/index-pc');
});
const ActivityForm = lazy(() => {
    return isMobile ? import('@/modules/activityForm/index-m') : import('@/modules/activityForm/index-pc');
});

// 教育名师招募
const ActivityMasterRecruitment = lazy(() => import('@/modules/activity/masterRecruitment'));

export const RequireAuthRoute: React.FC<{children: React.ReactNode}> = ({children}) => {
    const [isLogin, isLoginExpired, isUserInfoLoaded] = useUserInfoStore(store => [
        store.isLogin,
        store.isLoginExpired,
        store.isUserInfoLoaded,
    ]);
    const isBlackAccount = !!useUserInfoStore(store => store.blackAccountInfo);

    if (!isUserInfoLoaded) {
        return <Loading />;
    }

    /* 确保 user/info 请求完成后，进行如下逻辑判断 */

    // 登录失效时需要留在当前页面不需要重定向到体验中心
    if (isLogin || isLoginExpired) {
        // 返回所有路由
        return children;
    }

    if (isBlackAccount) {
        return (
            <Routes>
                <Route path={urls.error.raw()} element={<Error />} />
                <Route path="*" element={<Navigate replace to={urls.error.raw()} />} />
            </Routes>
        );
    }

    // 移动端未登录，移动端进留资页后再登录
    if (isMobile) {
        return (
            <Routes>
                <Route path={urls.root.raw()} element={<Navigate replace to={urls.center.raw()} />} />
                {/* 体验中心 */}
                <Route path={urls.center.raw()} element={<BaseLayoutM />}>
                    <Route path={urls.center.raw()} element={<Center key={location.pathname} />} />
                    <Route path={urls.centerSearch.raw()} element={<Center key={location.pathname} />} />
                    <Route path={urls.centerSearchWithKey.raw()} element={<Center key={location.pathname} />} />
                    <Route path={urls.centerAgentPreview.raw()} element={<AgentPreview />} />
                </Route>
                {/* 移动端扫 PC 端二维码后打开的录制页，不需要登录 */}
                <Route path={urls.agentCreateAudio.raw()} element={<CreateAudio />} />

                {/* 下线 questionnaire 链接 */}
                {/* <Route path={urls.questionnaire.raw()} element={<Questionnaire />} /> */}
                {/* 实名认证升级后，原有资质认证URL重定向到新账号中心 */}
                <Route path={urls.entity.raw()}>
                    <Route path={urls.entity.raw()} element={<Navigate replace to={urls.account.raw()} />} />
                    <Route path={urls.entityUpdate.raw()} element={<Navigate replace to={urls.account.raw()} />} />
                </Route>
                <Route path={urls.docs.splat()} />
                <Route path={urls.agreement.raw()} element={<Agreement />} />
                <Route path="*" element={<Navigate replace to={urls.center.raw()} />} />

                {/* 授权给服务商 */}
                <Route path={urls.bindTpAuthorization.raw()} element={<BindTpAuthorization />} />

                {/* 赛事中心 */}
                <Route path={urls.activity.raw()}>
                    <Route path={urls.activityCenter.raw()} element={<ActivityCenter />} />
                    <Route path={urls.nvidiaActivityDetail.raw()} element={<NvidiaActivityDetail />} />
                    <Route path={urls.activityTeam.raw()} element={<ActivityTeam />} />
                    {/* 根路径/activity 匹配任意路径时，跳转至赛事中心 */}

                    {/* 开发者赚钱季大赛 */}
                    <Route path={urls.activityDpCenter.raw()} element={<ActivityDpCenter />} />
                    <Route path={urls.activityDpDetail.raw()} element={<ActivityDpDetail />} />
                    {/* 开发者赚钱季大赛路径/activity/developerEarning 匹配任意路径时，跳转至开发者赚钱季大赛 */}
                    <Route
                        path={urls.activityDp.splat()}
                        element={<Navigate replace to={urls.activityDpCenter.raw()} />}
                    />

                    {/* 教育名师招募 */}
                    <Route path={urls.activityMasterRecruitment.raw()} element={<ActivityMasterRecruitment />} />

                    <Route
                        path={urls.activity.raw() || urls.activity.splat()}
                        element={<Navigate replace to={urls.activityCenter.raw()} />}
                    />
                </Route>
                {/* 榜单页 */}
                <Route path={urls.rank.raw()} element={<Rank />} />
                <Route path={urls.activityDetail.raw()} element={<ActivityDetail />} />
                {/* 表单详情 */}
                <Route path={urls.activityFormDetail.raw()} element={<ActivityForm />} />

                {/* 春节城市智能体 */}
                <Route path={urls.agentCreateCityAgent.raw()} element={<Navigate replace to={urls.center.raw()} />} />
                <Route path={urls.cityAgentList.raw()} element={<Navigate replace to={urls.center.raw()} />} />
            </Routes>
        );
    }
    // pc端未登录
    return (
        <Routes>
            <Route path={urls.root.raw()} element={<Navigate replace to={urls.center.raw()} />} />
            {/* 体验中心 */}
            <Route path={urls.center.raw()} element={<BaseLayoutPC />}>
                <Route path={urls.center.raw()} element={<Center />} />
            </Route>
            <Route path={urls.centerAgentPreview.raw()} element={<AgentPreview />} />

            {/* 插件商店 */}
            <Route path={urls.centerPlugin.raw()} element={<BaseLayoutPC />}>
                <Route path={urls.centerPlugin.raw()} element={<CenterPlugin />} />
                <Route path={urls.centerPluginDetail.raw()} element={<CenterPluginDetail />} />
            </Route>

            {/* 移动端扫 PC 端二维码后打开的录制页，不需要登录 */}
            <Route path={urls.agentCreateAudio.raw()} element={<CreateAudio />} />

            <Route path={urls.docs.splat()} />
            <Route path={urls.agreement.raw()} element={<Agreement />} />

            {/* 能力插件预览（线上、审核、分享） */}
            <Route path={urls.pluginAbilityPreviewRelease.raw()} element={<AbilityPluginReleasePreview />} />
            <Route path={urls.pluginAbilityPreviewAudit.raw()} element={<AbilityPluginAuditPreview />} />
            <Route path={urls.pluginAbilityPreviewShare.raw()} element={<AbilityPluginSharePreview />} />

            {/* 数据插件预览（线上、审核） */}
            <Route path={urls.pluginDataPreviewRelease.raw()} element={<DataPluginReleasePreview />} />
            <Route path={urls.pluginDataPreviewAudit.raw()} element={<DataPluginAuditPreview />} />
            <Route path="*" element={<Navigate replace to={urls.center.raw()} />} />

            {/* 授权给服务商 */}
            <Route path={urls.bindTpAuthorization.raw()} element={<BindTpAuthorization />} />

            {/* 赛事中心 */}
            <Route path={urls.activity.raw()}>
                <Route path={urls.activityCenter.raw()} element={<ActivityCenter />} />
                <Route path={urls.nvidiaActivityDetail.raw()} element={<NvidiaActivityDetail />} />
                <Route path={urls.activityTeam.raw()} element={<ActivityTeam />} />

                {/* 开发者赚钱季大赛 */}
                <Route path={urls.activityDpCenter.raw()} element={<ActivityDpCenter />} />
                <Route path={urls.activityDpDetail.raw()} element={<ActivityDpDetail />} />
                {/* 开发者赚钱季大赛路径/activity/developerEarning 匹配任意路径时，跳转至开发者赚钱季大赛 */}
                <Route path={urls.activityDp.splat()} element={<Navigate replace to={urls.activityDpCenter.raw()} />} />

                {/* 教育名师招募 */}
                <Route path={urls.activityMasterRecruitment.raw()} element={<ActivityMasterRecruitment />} />

                {/* 根路径/activity 匹配任意路径时，跳转至赛事中心 */}
                <Route
                    path={urls.activity.raw() || urls.activity.splat()}
                    element={<Navigate replace to={urls.activityList.raw()} />}
                />
            </Route>
            {/* 活动中心 */}
            <Route path={urls.activity.raw()} element={<BaseLayoutPC />}>
                <Route path={urls.activityList.raw()} element={<ActivityListPc />} />
                <Route path={urls.activityDetail.raw()} element={<ActivityDetail />} />
            </Route>
            {/* 表单详情 */}
            <Route path={urls.activityFormDetail.raw()} element={<ActivityForm />} />
            {/* 榜单页 */}
            <Route path={urls.rank.raw()} element={<Rank />} />
        </Routes>
    );
};
