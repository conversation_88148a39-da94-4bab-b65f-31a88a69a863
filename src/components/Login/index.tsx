/**
 * @file 登录弹窗
 * <AUTHOR>
 */

import React, {useState, useCallback, useEffect, useRef} from 'react';
import classNames from 'classnames';
import {Modal, Tabs} from 'antd';
import type {TabsProps} from 'antd';
import styled from '@emotion/styled';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {AccountType} from '@/utils/loggerV2/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {UserType, USER_TYPE_NAME} from '@/api/login/interface';
// import PassportLogin from './utils/passportLogin';
import useLoginOptions from '@/components/Login/utils/useLoginOptions';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {reportError, CustomError, ErrorType} from '@/utils/monitor';
import {Z_INDEX_MAP} from '@/styles/z-index';
import {initUcOption} from './utils/ucLogin';

const tabItems: Array<{
    label: string;
    key: string;
}> = [
    {
        label: USER_TYPE_NAME[UserType.Passport],
        key: UserType.Passport.toString(),
    },
    {
        label: USER_TYPE_NAME[UserType.UC],
        key: UserType.UC.toString(),
    },
];

const StyledTabs = styled(Tabs)<TabsProps>`
    .ant-tabs-nav-wrap {
        padding: 24px 0 !important;
    }
    .ant-tabs-nav-list {
        transform: translate(14px, 0px) !important;
    }
    .ant-tabs-nav {
        &::before {
            border-bottom: none !important;
        }
        margin-bottom: 0 !important;
    }
    .ant-tabs-tab {
        color: #858585;
        font-size: 24px !important;
        font-style: normal;
        font-weight: 400;
        line-height: 24px !important;
        padding: 9px 0 !important;
    }
    .ant-tabs-tab-active {
        color: #222;
        font-weight: 500;
        .ant-tabs-tab-btn {
            color: #222 !important;
        }
    }
`;

const StyledModal = styled(Modal)`
    .ant-modal-content {
        padding: 0 32px 0px 32px !important;
        height: 500px !important;
        width: 780px !important;
    }
    .ant-modal-header {
        margin-bottom: 0 !important;
    }
    .ant-modal-close {
        top: 16px !important;
        right: 16px !important;
        width: 14px !important;
        height: 14px !important;
        &:hover,
        &:focus,
        &:active {
            background-color: #ffffff !important;
        }
    }
`;

const LoginModule = () => {
    const [showLoginModal, isLoginExpired, setShowLoginModal, setLoginExpired, loginStateSwitch] = useUserInfoStore(
        store => [
            store.showLoginModal,
            store.isLoginExpired,
            store.setShowLoginModal,
            store.setLoginExpired,
            store.loginStateSwitch,
        ]
    );
    const [userType, setUserType] = useState<string>(UserType.Passport.toString());
    const timer = useRef<any>(0);
    // 打点
    const {showLog, clickLog} = useUbcLogV2();

    // 登录弹层展现打点
    const handleOk = useCallback(() => {
        setShowLoginModal(true);
        showLog(EVENT_VALUE_CONST.LOGIN_BOX);
    }, [setShowLoginModal, showLog]);
    const handleCancel = useCallback(() => {
        setShowLoginModal(false);
        // 登录失效情况下，点击关闭登录弹窗，重定向到体验中心页
        if (isLoginExpired) {
            setLoginExpired(false);
            loginStateSwitch(false);
            window.open('/center', '_self');
        }

        // 标记退出登陆功能
        generateSessionId();
        generateOperationModule(OperationModuleType.Other);
        // 关闭计时器
        const timeout = timer.current;
        timeout && clearTimeout(timeout);
    }, [isLoginExpired, loginStateSwitch, setLoginExpired, setShowLoginModal, timer]);

    const tabChange = useCallback(
        (activeKey: string) => {
            setUserType(activeKey);
            // 切换登录方式打点
            clickLog(EVENT_VALUE_CONST.ACCOUNT_TAB, '', {
                [EVENT_EXT_KEY_CONST.ACCOUNT_TYPE]:
                    activeKey === UserType.Passport.toString() ? AccountType.PASSPORT : AccountType.UC,
            });
        },
        [clickLog]
    );

    const {action, config, callback} = useLoginOptions();

    // 初次渲染时，加载uc登录组件
    const afterOpenChange = useCallback(
        (open: boolean) => {
            const passComponent = document.getElementById('componseLeft');
            const ucComponent = document.getElementById('uc-login');
            if (open && !passComponent) {
                window.passport?.use(action, config, callback);
            }

            if (open && !ucComponent) {
                initUcOption(clickLog);
            }

            document.body.style.overflow = open ? 'hidden' : 'auto';
            // 2s后还不展现就报错
            timer.current = setTimeout(() => {
                !document.getElementById('componseLeft') &&
                    reportError(new CustomError(ErrorType.BusinessError, 'passport登陆表单未展现'));
                !document.getElementById('uc-login') &&
                    reportError(new CustomError(ErrorType.BusinessError, 'uc登陆表单未展现'));
            }, 2000);
        },
        [action, config, callback, clickLog]
    );

    useEffect(() => {
        const timeout = timer.current;
        return () => {
            timeout && clearTimeout(timeout);
        };
    }, []);

    return (
        <StyledModal
            title={
                <StyledTabs
                    defaultActiveKey={userType}
                    items={tabItems}
                    onChange={tabChange}
                    tabBarStyle={{borderBottom: 'none'}}
                    centered
                    tabBarGutter={48}
                />
            }
            open={showLoginModal}
            centered
            onOk={handleOk}
            onCancel={handleCancel}
            afterOpenChange={afterOpenChange}
            footer={null}
            width={780}
            zIndex={Z_INDEX_MAP.login}
            maskClosable={false}
            forceRender
        >
            {/* PassportLogin */}
            <div
                className={classNames({
                    hidden: userType !== UserType.Passport.toString(),
                })}
            >
                <div id="passport-login"></div>
            </div>
            {/* UcLogin */}
            <div
                className={classNames({
                    hidden: userType !== UserType.UC.toString(),
                })}
                id="ucContainer"
            ></div>
        </StyledModal>
    );
};

export default LoginModule;
