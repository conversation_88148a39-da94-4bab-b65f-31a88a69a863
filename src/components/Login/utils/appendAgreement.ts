/**
 * @file 添加AI开放平台协议入口
 * <AUTHOR>
 */

import {getOrigin} from './getOrigin';

const appendAgreement = () => {
    // 创建一个新的span元素
    const newSpan = document.createElement('span');
    newSpan.style.display = 'block';
    newSpan.style.color = '#525252';
    newSpan.style.position = 'relative';
    newSpan.style.left = '32px';
    newSpan.style.bottom = '-3px';

    const createLink = (text: string, url: string) => {
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        link.textContent = text;
        link.style.zIndex = '70000';
        link.style.fontSize = '14px';
        link.style.color = '#4e6ef2';
        return link;
    };

    // 创建三个新的a元素
    const a1 = createLink('百度用户协议', 'http://passport.baidu.com/static/passpc-account/html/protocal.html');
    const a2 = createLink('隐私政策', 'http://privacy.baidu.com/policy');
    const a3 = createLink('平台服务协议', `${getOrigin()}/agreement`);

    // 创建三个a元素前边的文字
    const newText1 = document.createTextNode('阅读并接受 ');
    const newText2 = document.createTextNode('、');
    const newText3 = document.createTextNode(' 和 ');

    // 这里不能在原来协议位置的span后添加一个span，这样切换登录方式后，另一种登录方式不会被添加
    // 获取表单右半边的div，也就是原来协议位置span的父元素的父元素，直接往父元素的父元素里添加
    const componseRight = document.getElementById('componseRight');
    const form = document.getElementById('TANGRAM__PSP_11__form');

    // span里添加a元素
    const aElements = [a1, a2, a3];
    // componseRight的末尾子元素form后面没有元素(form.nextSibling===null)再添加，防止每次渲染都添加一个span
    if (!form?.nextSibling) {
        aElements.forEach(a => {
            newSpan.appendChild(a);
        });
        componseRight?.appendChild(newSpan);
    }

    // 在a标签前面添加对应文本内容
    const newTexts = [newText1, newText2, newText3];
    aElements.forEach((a, index) => {
        if (a.parentNode) {
            a.parentNode.insertBefore(newTexts[index], a);
        }
    });

    // 隐藏原来的span
    const agreementSpans = document.getElementsByClassName('tang-pass-sms-agreement');
    Array.prototype.forEach.call(agreementSpans, span => {
        if (span) {
            span.style.display = 'none';
        }
    });
};

export default appendAgreement;
