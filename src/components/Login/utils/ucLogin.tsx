/**
 * @file ucLogin容器组件
 * <AUTHOR>
 * @doc uc登录配置文件 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/DDWA7rKjDG/ctFiYDJ-YO/30Hmnzo8YDHIJt
 */

// import {message} from 'antd';
import {getOrigin} from '@/components/Login/utils/getOrigin';
import ucQrcodeDemo from '@/assets/login/uc-qrcode-demo.png';
import ucCodeSuccessIcon from '@/assets/login/qrcode-success.png';
import ucCodeErrorIcon from '@/assets/login/qrcode-error.png';
import {AccountType, LoginType} from '@/utils/loggerV2/interface';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {LoginStatus} from '@/utils/loggerV2/interface';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {reportError, CustomError, ErrorType} from '@/utils/monitor';

/**
 * 添加一些自定义dom元素
 */
// eslint-disable-next-line max-statements
const appendCustomDom = (clickLog: any): void => {
    const ucContainer = document.getElementById('ucContainer');
    const loginAction = ucContainer?.querySelector('.login-action');
    const qrcodeBtn = loginAction?.querySelector('.uc-code-btn-change') as HTMLElement;
    const forgetPasswordBtn = loginAction?.querySelectorAll('a')?.[1] as HTMLElement;
    const qrCodeContainer = ucContainer?.querySelector('.uc-code-item-scan');
    const qrCodeImg = qrCodeContainer?.querySelector('img');
    const newDiv = document.createElement('div');
    newDiv.className = 'custom-tips';
    // bca-disable-line
    newDiv.innerHTML = '<span>温馨提示：与百度营销、百度网盟、百度联盟、百度统计、百度司南等产品账号通用</span>';
    if (loginAction) {
        loginAction.appendChild(newDiv);
    }
    // 设置二维码图片宽度
    if (qrCodeImg) {
        qrCodeImg.width = 140;
        qrCodeImg.height = 140;
    }
    // 添加二维码描边
    const qrCodeWrap = document.createElement('div');
    qrCodeWrap.className = 'custom-uc-qrcode-wrap';
    qrCodeImg && qrCodeWrap?.appendChild(qrCodeImg);
    qrCodeContainer?.insertBefore(qrCodeWrap, qrCodeContainer.firstChild);

    // 添加自定义二维码扫描示例图片
    const ucImg = document.createElement('img');
    ucImg.src = ucQrcodeDemo;
    ucImg.className = 'custom-uc-qrcode-img';
    qrCodeContainer?.insertBefore(ucImg, qrCodeWrap?.nextSibling);

    // 添加成功icon
    const ucCodeSuccess = ucContainer?.querySelector('.uc-code-item-success');
    const successIcon = document.createElement('img');
    successIcon.src = ucCodeSuccessIcon;
    successIcon.className = 'custom-uc-qrcode-icon';
    ucCodeSuccess?.insertBefore(successIcon, ucCodeSuccess?.firstChild);

    // 添加错误icon
    const ucCodeFail = ucContainer?.querySelector('.uc-code-item-fail');
    const errorIcon = document.createElement('img');
    errorIcon.src = ucCodeErrorIcon;
    errorIcon.className = 'custom-uc-qrcode-icon';
    ucCodeFail?.insertBefore(errorIcon, ucCodeFail?.firstChild);

    // 忘记密码添加打点
    forgetPasswordBtn?.addEventListener('click', () => {
        clickLog(EVENT_VALUE_CONST.FORGET_PASSWORD);
    });

    // 触发二维码的展示
    setTimeout(() => {
        qrcodeBtn?.click();
    }, 0);
};

/**
 * 初始化配置&挂载
 */
export const initUcOption = (clickLog: any): void => {
    window?.ucCommonLogin?.init({
        container: 'ucContainer',
        appid: 771, // 改成申请的appid
        isLayer: 0,
        defaultCss: 0,
        defaultCssVersion: 3,
        // multiLogin: 1,
        isCanCodeLogin: true,
        ucTitle: '账号登录',
        staticPage: `${getOrigin()}/v3Jump.html`,
        // fromu
        success: () => {
            // 登录成功
            clickLog(EVENT_VALUE_CONST.LOGIN, '', {
                [EVENT_EXT_KEY_CONST.LOGIN_STATUS]: LoginStatus.LOGIN,
                [EVENT_EXT_KEY_CONST.ACCOUNT_TYPE]: AccountType.UC,
                [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: LoginType.ACCOUNT,
            });
            // 标记退出登陆功能
            generateSessionId();
            generateOperationModule(OperationModuleType.Other);
        },
        fail: (e: any) => {
            // 登录失败
            clickLog(EVENT_VALUE_CONST.LOGIN, '', {
                [EVENT_EXT_KEY_CONST.LOGIN_STATUS]: LoginStatus.NOT_LOGIN,
                [EVENT_EXT_KEY_CONST.ACCOUNT_TYPE]: AccountType.UC,
                [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: LoginType.ACCOUNT,
            });
            // 标记退出登陆功能
            generateSessionId();
            generateOperationModule(OperationModuleType.Other);
            // 上报登陆失败
            reportError(new CustomError(ErrorType.BusinessError, e.toString()));
        },
        rendered: () => {
            // 登录渲染完成，动态添加一些样式
            appendCustomDom(clickLog);
        },
    });
};

/**
 * 动态加载UC登录脚本&样式
 */
export const loadUcScript = () => {
    // 已经加载过，直接返回
    if (document.getElementById('script-uc')) {
        return;
    }
    const script = document.createElement('script');
    const scriptUrl = process.env.UC_LOGIN_JS;
    script.src = scriptUrl!;
    script.id = 'script-uc';

    // 外部JS文件加载完毕
    script.onload = (): void => {
        // 执行弹层初始化，传入配置项，初始化结果给到全局
        // window.initPopUp = window.passport?.pop?.init(options);
        // initUcOption();
    };

    script.onerror = (): void => {
        // 只有开发环境会存在需要安全认证情况
        if (process.env.NODE_ENV === 'development') {
            // 加载失败
            // !isMobile && message.warning('登录表单初始化失败，请进行认证');
        }
    };

    document.body.appendChild(script);
};
