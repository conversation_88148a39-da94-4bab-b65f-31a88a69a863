/**
 * @file 引入jssdk，初始化登录弹层
 * <AUTHOR>
 */
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {loadUcScript} from './ucLogin';

const scriptUrl = `${process.env.PASS_HOST}/passApi/js/wrapper.js?cdnversion=${new Date().getTime()}`;

/**
 * 登录弹层初始化函数
 * @returns 返回引入jssdk函数
 */
const useInitLogin = () => {
    const setScriptLoaded = useUserInfoStore(store => store.setScriptLoaded);
    // jssdk引入
    const initLogin = () => {
        if (!document.getElementById('componseLeft')) {
            const script = document.createElement('script');
            script.src = scriptUrl;
            script.id = 'script-passport';

            // 外部JS文件加载完毕
            script.onload = (): void => {
                setScriptLoaded(true);
            };

            script.onerror = (): void => {
                // 只有开发环境会存在需要安全认证情况  8.20-取消开发环境的提示
                // if (process.env.NODE_ENV === 'development') {
                //     // 加载失败
                //     !isMobile && message.warning('登录表单初始化失败，请进行认证');
                // } else {
                //     // 兜底报错
                //     message.warning('登录表单初始化失败');
                // }
                setScriptLoaded(false);
            };

            document.body.appendChild(script);
        }
        // 加载ucjssdk
        loadUcScript();
    };

    return initLogin;
};
export default useInitLogin;
