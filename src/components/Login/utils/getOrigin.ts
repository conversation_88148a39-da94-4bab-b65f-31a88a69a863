/**
 * @file 获取源
 * <AUTHOR>
 */
export function getOrigin(): string {
    const {protocol, hostname, port} = window.location;
    let origin = window.location.origin;

    if (!window.location.origin) {
        origin = protocol + '//' + hostname + (port ? ':' + port : '');
    }

    if (!window.origin) {
        window.origin = window.location.origin;
        origin = window.location.origin;
    }

    return origin;
}
