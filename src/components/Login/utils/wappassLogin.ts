import boxx from '@baidu/boxx';
import {isBaiduApp} from '@/utils/baiduApp';
/**
 * @file 移动端登录函数
 * <AUTHOR>
 */
export const wappassLogin = () => {
    /**
     * 调起手百 NA 登录的端能力
     * https://console.cloud.baidu-int.com/devops/icode/repos/baidu/search-fe/boxx/blob/master/doc/api/account.md
     */
    if (isBaiduApp() && boxx.canIUse('account.login')) {
        boxx.account.login({
            loginType: 'sms',
            showThirdLogin: '0',
            loginSource: 'lingjing',
            normalizeAccount: '0',
            success() {
                console.info('端能力登陆成功');
                location.reload();
            },
            fail(error: {message: string}) {
                console.error(error.message);
                /** @todo 端能力登陆失败，且非用户主动取消时，需要上报错误 */
            },
        });
    } else {
        // query配置tpl= & u= 和 #/xxx 默认登录方式
        window.location.href = `https://wappass.baidu.com/passport/?login&tpl=wise&u=${encodeURIComponent(
            window.location.href
        )}#/sms_login_new`;
    }
};
