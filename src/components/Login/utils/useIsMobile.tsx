/**
 * @file 用于检测访问设备是否为移动端, 根据视口尺寸实时改遍状态
 * <AUTHOR>
 */

import {useEffect} from 'react';
import {useIsMobileStore} from '@/store/home/<USER>';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';

const isMobile = isMobileDevice();

export const useIsMobile = () => {
    const isMobileStateSwitch = useIsMobileStore(store => store.isMobileStateSwitch);

    useEffect(() => {
        // 初始化更新isMobile状态
        isMobileStateSwitch(isMobile);
    }, [isMobileStateSwitch]);
};
