/**
 * @file 登录弹层样式修改&追加点击事件
 * <AUTHOR>
 * 1.26 uc登录-文件废弃
 */
import {useCallback, useRef} from 'react';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST, UserTypeLog} from '@/utils/logger/constants/extkey';
import appendAgreement from './appendAgreement';

/**
 * 弹层样式修复&添加点击事件
 * @returns 返回弹层完成渲染回调
 */
const useLoginModalShow = () => {
    // 登录框打点
    const {ubcClickLog} = useUbcLog();

    // 忘记密码点击
    const handleForgotPasswordClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.LoginmodalForgotpasswordLink, {
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: UserTypeLog.PASSPORT,
        });
    }, [ubcClickLog]);

    // 立即注册按钮点击
    const handleRegisterClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.LoginmodalRegisterLink, {
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: UserTypeLog.PASSPORT,
        });
    }, [ubcClickLog]);

    // 发送验证码按钮点击
    const handleVerifyCodeClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.LoginmodalVerificationcodeBtn, {
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: UserTypeLog.PASSPORT,
        });
    }, [ubcClickLog]);

    // 给dom元素追加样式属性方法
    const applyStyles = useCallback((element: HTMLElement | null, styles: Record<string, string>) => {
        if (element) {
            Object.assign(element.style, styles);
        }
    }, []);

    const loginPopRef = useRef<HTMLElement | null>(null);

    /* eslint-disable */ //暂时关闭eslint函数长度校验
    const loginStyles = useCallback(() => {
        // 获取表单最外层元素
        const loginPop: HTMLElement | null = document.getElementById('passport-login-pop') || null;
        loginPopRef.current = loginPop;
        // 获取弹窗元素
        const elements = {
            // input框下的错误提示
            errorWrapper: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__errorWrapper'),
            generalError: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__error'),
            // input框清除按钮
            userNamClearbtn: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__userName_clearbtn'),
            // 短信验证码登录右下角错误提示
            smsErrorWrapper: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__smsErrorWrapper'),
            // 短信验证码登录左下角错误提示
            smsError: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__smsError'),
            // 忘记密码
            passFgtpwd: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__fgtpwdLink'),
            // 验证码系列
            verifyCodeChange: loginPopRef.current?.ownerDocument.getElementById(
                'TANGRAM__PSP_11__confirmVerifyCodeChange'
            ),
            // 验证码按钮
            verifyCodeBtn: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__smsTimer'),
            // 立即注册
            passReglink: loginPopRef.current?.ownerDocument.getElementById('TANGRAM__PSP_11__regLink'),
        };

        // 账号登录的错误提示样式
        if (elements.generalError) {
            applyStyles(elements.generalError, {
                height: 'auto',
                lineHeight: '18px',
                marginBottom: '12px',
                float: 'left',
            });
        }

        if (elements.errorWrapper) {
            applyStyles(elements.errorWrapper, {
                position: 'relative',
                zIndex: '20',
            });
        }

        // 短信登录的错误提示样式
        if (elements.smsError) {
            applyStyles(elements.smsError, {
                height: 'auto',
                maxWidth: '220px',
                float: 'left',
            });
        }

        // 收不到验证码
        if (elements.smsErrorWrapper) {
            applyStyles(elements.smsErrorWrapper, {
                height: '30px',
            });
        }

        // 忘记密码
        if (elements.passFgtpwd) {
            applyStyles(elements.passFgtpwd, {
                color: '#525252',
                marginTop: '-5px',
                position: 'relative',
                bottom: '10px',
                zIndex: '22',
            });
            elements.passFgtpwd.addEventListener('click', handleForgotPasswordClick);
        }

        // 验证码按钮
        if (elements.verifyCodeBtn) {
            elements.verifyCodeBtn.addEventListener('click', handleVerifyCodeClick);
        }

        // 立即注册按钮
        if (elements.passReglink) {
            elements.passReglink.addEventListener('click', handleRegisterClick);
        }

        // 输入框的清除符号
        if (elements.userNamClearbtn) {
            applyStyles(elements.userNamClearbtn, {
                background: `url(${process.env.PASS_HOST}/passApi/img/pass_login_icons.png) no-repeat 0 0')`,
                right: '10px',
            });
        }

        // 输入框的清除符号
        if (elements.verifyCodeChange) {
            applyStyles(elements.verifyCodeChange, {
                top: '-10px',
                marginLeft: '0px',
            });
        }

        // 遍历表单里的所有img元素
        const loginPopImgs = loginPop?.querySelectorAll('img');
        if (loginPopImgs && Array.prototype.forEach) {
            Array.prototype.forEach?.call(loginPopImgs, img => {
                img.setAttribute('style', 'display:inline-block;');
            });
        }

        // 添加协议入口
        appendAgreement();

        // 移除事件监听器
        return () => {
            if (elements.passFgtpwd) {
                elements.passFgtpwd.removeEventListener('click', handleForgotPasswordClick);
            }

            if (elements.verifyCodeBtn) {
                elements.verifyCodeBtn.removeEventListener('click', handleVerifyCodeClick);
            }

            if (elements.passReglink) {
                elements.passReglink.removeEventListener('click', handleRegisterClick);
            }
        };
    }, []);

    return loginStyles;
};

export default useLoginModalShow;
