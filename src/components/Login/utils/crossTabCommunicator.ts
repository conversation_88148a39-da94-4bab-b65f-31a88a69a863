/**
 * @file 跨标签页共享 构造类
 */
export class CrossTabCommunicator {
    private readonly channel: BroadcastChannel | null;
    private readonly storageKey: string;
    private messageCallback?: (data: any) => void;
    private storageCallback?: (event: StorageEvent) => void;

    /**
     * @description 构造函数，创建一个新的广播通道实例。
     * 如果支持 BroadcastChannel，则使用该 API 创建一个新的通道；否则返回 null。
     * @param {string} channelName - 通道名称，字符串类型。
     * @returns {void} 无返回值。
     */
    constructor(channelName: string) {
        if ('BroadcastChannel' in window) {
            this.channel = new BroadcastChannel(channelName);
        } else {
            this.channel = null;
        }
        this.storageKey = channelName;
    }

    /**
     * @description
     * 向消息通道或本地存储发送消息。
     * 如果已经建立了消息通道，则将消息发送到该通道；否则，将消息保存在本地存储中并清除。
     * @param data {any} 需要发送的数据，可以是任何类型的值。
     */
    postMessage(data: any) {
        if (this.channel) {
            this.channel.postMessage(data);
        } else {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        }
    }

    /**
     * @description
     * 监听消息或存储事件，当有数据到达时调用回调函数。
     * 如果使用了 channel，则监听 channel 的 message 事件；否则监听 storage 的 storage 事件。
     * @param {Function} callback 回调函数，接收一个参数 data，表示到达的数据。
     */
    listen(callback: (data: any) => void) {
        this.messageCallback = (event: MessageEvent) => callback(event.data);
        this.storageCallback = (event: StorageEvent) => {
            if (event.key === this.storageKey && event.newValue) {
                callback(JSON.parse(event.newValue));
            }
        };

        if (this.channel) {
            this.channel.addEventListener('message', this.messageCallback);
        } else {
            window.addEventListener('storage', this.storageCallback);
        }
    }

    /**
     * 移除监听器，释放资源。
     * @returns void
     */
    removeListener() {
        if (this.channel) {
            this.channel.removeEventListener('message', this.messageCallback!);
        } else {
            window.removeEventListener('storage', this.storageCallback!);
        }
    }
}
