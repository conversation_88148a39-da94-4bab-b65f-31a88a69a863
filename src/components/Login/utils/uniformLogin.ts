/**
 * @file 统一登录入口
 * <AUTHOR>
 */
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import useInitLogin from '@/components/Login/utils/useInitLogin';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {isMobileDevice} from './isMobileDevice';
import {wappassLogin} from './wappassLogin';

const isMobile = isMobileDevice();

export const useUniformLogin = (loginSource?: number) => {
    const [setShowLoginModal] = useUserInfoStore(store => [store.setShowLoginModal]);
    const scriptLoaded = useUserInfoStore(store => store.scriptLoaded);
    const initLogin = useInitLogin();
    // 打点
    const {showLog} = useUbcLogV2();

    return () => {
        // 标记进入登陆功能
        generateSessionId();
        generateOperationModule(OperationModuleType.Login);

        if (isMobile) {
            // 移动设备进入pass登录页(暂定，后续可能做移动端适配)
            wappassLogin();
        } else if (scriptLoaded) {
            // 打开登录弹窗
            setShowLoginModal(true);
            // 登陆弹层展示打点
            if (loginSource) {
                showLog(EVENT_VALUE_CONST.LOGIN_BOX, '', {
                    [EVENT_EXT_KEY_CONST.LOGIN_SOURCE]: loginSource,
                });
            } else {
                showLog(EVENT_VALUE_CONST.LOGIN_BOX);
            }
        } else {
            // 重新执行script加载，加载失败弹提示
            initLogin();
        }
    };
};
