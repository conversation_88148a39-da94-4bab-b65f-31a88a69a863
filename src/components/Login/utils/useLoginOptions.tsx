/**
 * @file passport v4登录弹层配置
 * <AUTHOR>
 */
import {useCallback} from 'react';
import {getOrigin} from '@/components/Login/utils/getOrigin';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {AccountType, LoginType} from '@/utils/loggerV2/interface';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {LoginStatus} from '@/utils/loggerV2/interface';
import usePassportDomOperate from '@/components/Login/utils/usePassportDomOperate';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {reportError, CustomError, ErrorType, recordDelayedReportTime, UserAction} from '@/utils/monitor';

/**
 * 参考文档: http://dev.passport.baidu.com/docs/agg/viewv2?path=tech.text&doc=pc/passport/loginv4.text
 * 接入新版文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/unME93M6Lc/V7CjWhBz-KSNqH
 * 新版嵌入demo：https://passport.baidu.com/c/html/loginMergeCompose.html（https://passport.baidu.com/passApi/js/wrapper.js）
 * 新版弹窗demo：https://passport.baidu.com/passApi/html/diaNewStyle.html
 */

/**
 * 设置登录相关配置
 * @returns 返回登录配置项对象。
 */

type PassportCallback = (apiMagic: PassportSDK.ApiMagic) => void;

const useLoginOptions = () => {
    // 打点
    const {clickLog} = useUbcLogV2();

    // 登陆成功回调
    const loginSuccess = useCallback(() => {
        // 登录成功打点
        clickLog(EVENT_VALUE_CONST.LOGIN, undefined, {
            [EVENT_EXT_KEY_CONST.LOGIN_STATUS]: LoginStatus.LOGIN,
            [EVENT_EXT_KEY_CONST.ACCOUNT_TYPE]: AccountType.PASSPORT,
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: Number(window.localStorage.getItem('loginType') || LoginType.ACCOUNT),
        });
        // 标记退出登陆功能
        generateSessionId();
        generateOperationModule(OperationModuleType.Other);
        // 延迟上报错误
        recordDelayedReportTime({
            action: UserAction.PassportLoginSuccess,
            errMsg: 'passport登陆成功但页面1s后未成功跳转',
        });
    }, [clickLog]);

    // 登录失败回调
    const loginFailed = useCallback(
        (e: any) => {
            clickLog(EVENT_VALUE_CONST.LOGIN, undefined, {
                [EVENT_EXT_KEY_CONST.LOGIN_STATUS]: LoginStatus.NOT_LOGIN,
                [EVENT_EXT_KEY_CONST.ACCOUNT_TYPE]: AccountType.PASSPORT,
                [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: Number(window.localStorage.getItem('loginType') || LoginType.ACCOUNT),
            });
            // 标记退出登陆功能
            generateSessionId();
            generateOperationModule(OperationModuleType.Other);
            // 上报登陆失败
            reportError(new CustomError(ErrorType.BusinessError, e.toString()));
        },
        [clickLog]
    );

    // 操作dom追加事件
    const domOperate = usePassportDomOperate();

    const passportCallback: PassportCallback = apiMagic => {
        // 实例化登录api
        /* eslint-disable-next-line */
        const loginInstance = new apiMagic.passport.login({
            product: 'wise', // 产品线名称
            staticPage: `${getOrigin()}/v3Jump.html`,
            u: `${window.location.href}`, // 登录成功后跳转原来页面
            memberPass: true, // 是否记住登录状态
            sms: 5, // 短信登录
            userPwdLogin: 2, // 默认帐号密码登录，配置1表示指定帐号密码登录
            qrcodeLogin: 3, // 二维码、账号密码左右分布
            // passport: 2,
            defaultCss: true, // 是否自动加载默认CSS，仅配置loginMerge后有效
            loginMerge: true,
            autosuggest: true, // 是否有邮箱/历史登录记录提示，仅配置loginMerge后有效
            qrcode: true, // 扫码登录
            hasPlaceholder: true,
            hasRegUrl: true, // 是否有注册链接
            uniqueId: true, // 是否进行id唯一处理
            hideLogo: 1, // 是否隐藏百度logo（默认为0展示logo，1 隐藏百度logo）
            isSyncBaiduInt: 1, // 低版本不支持，用接口处理cookie是否同步bai-int
            composeTemplate: 1,
        });

        loginInstance.on('render', domOperate); // 渲染执行，必须写在render之前
        loginInstance.render('passport-login');
        loginInstance.on('loginSuccess', loginSuccess); // 登录成功事件
        loginInstance.on('loginError', loginFailed); // 登录失败事件
    };

    // 返回passport登录相关配置
    return {
        action: 'login',
        config: {
            tangram: true,
            loginVersion: 'v5' as `v${number}`,
        },
        callback: passportCallback,
    };
};
export default useLoginOptions;
