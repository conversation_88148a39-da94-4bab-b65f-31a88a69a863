/**
 * @file 登录函数
 * <AUTHOR>
 * 1.26 uc登录-文件废弃
 */
import {wappassLogin} from './wappassLogin';
import {isMobileDevice} from './isMobileDevice';

const isMobile = isMobileDevice();

// 登录点击事件回调
export const passportLogin = () => {
    if (isMobile) {
        // 移动设备进入pass登录页
        wappassLogin();
    } else if (window.initPopUp) {
        // 非移动设备展示登录弹层
        window.initPopUp.show();
    }
};

// 注册点击事件回调
export const passportRegister = () => {
    window.open(`${process.env.PASS_HOST}/v2/?reg&tpl=&u=`);
};
