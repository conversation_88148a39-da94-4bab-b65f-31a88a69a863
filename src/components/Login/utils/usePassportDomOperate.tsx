/**
 * @file 登录弹层Passport部分追加点击事件
 * <AUTHOR>
 * */
import {useCallback, useRef} from 'react';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {LoginType} from '@/utils/loggerV2/interface';
import appendAgreement from './appendAgreement';

/**
 * 弹层样式修复&添加点击事件
 * @returns 返回dom操作执行函数
 */
const usePassportDomOperate = () => {
    // 登录框打点
    const {clickLog} = useUbcLogV2();

    // 【忘记密码】点击事件
    const handleForgotPasswordClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.FORGET_PASSWORD);
    }, [clickLog]);

    // 【立即注册】按钮点击事件
    const handleRegisterClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.REGISTER);
    }, [clickLog]);

    // 【发送验证码】按钮点击事件
    const handleVerifyCodeClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.SEND_CODE);
    }, [clickLog]);

    // 【下载百度APP】按钮点击事件
    const handleDownloadClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.DOWNLOAD_APP);
    }, [clickLog]);

    // 【账号登录】按钮点击事件
    const handleAccountLoginClick = useCallback(() => {
        window.localStorage.setItem('loginType', LoginType.ACCOUNT.toString());
        clickLog(EVENT_VALUE_CONST.LOGIN_TAB, '', {
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: LoginType.ACCOUNT,
        });
    }, [clickLog]);

    // 【手机登录】按钮点击事件
    const handlePhoneLoginClick = useCallback(() => {
        window.localStorage.setItem('loginType', LoginType.PHONE.toString());
        clickLog(EVENT_VALUE_CONST.LOGIN_TAB, '', {
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: LoginType.PHONE,
        });
    }, [clickLog]);

    const passportLoginRef = useRef<HTMLElement | null>(null);

    const domOperate = useCallback(() => {
        // 获取表单最外层元素
        const passportLogin: HTMLElement | null = document.getElementById('passport-login') || null;
        passportLoginRef.current = passportLogin;

        // 获取弹窗元素
        const elements = {
            // 忘记密码
            passFgtpwd: passportLoginRef.current?.ownerDocument.getElementsByClassName('pass-fgtpwd')[0],
            // 验证码按钮
            verifyCodeBtn: passportLoginRef.current?.ownerDocument.getElementsByClassName('pass-item-timer')[0],
            // 立即注册
            passReglink: passportLoginRef.current?.ownerDocument.getElementsByClassName('pass-reglink')[0],
            // 下载百度APP
            downloadApp: passportLoginRef.current?.ownerDocument.querySelector('.pass-qrcode-download .pass-link'),
            // 账号登录
            accountLogin: passportLoginRef.current?.ownerDocument.querySelector('.login-type-tab span:nth-child(2)'),
            // 手机登录
            phoneLogin: passportLoginRef.current?.ownerDocument.querySelector('.login-type-tab span:nth-child(3)'),
        };

        // 忘记密码
        if (elements.passFgtpwd) {
            elements.passFgtpwd.addEventListener('click', handleForgotPasswordClick);
        }

        // 验证码按钮
        if (elements.verifyCodeBtn) {
            elements.verifyCodeBtn.addEventListener('click', handleVerifyCodeClick);
        }

        // 立即注册按钮
        if (elements.passReglink) {
            elements.passReglink.addEventListener('click', handleRegisterClick);
        }

        // 下载百度APP按钮
        if (elements.downloadApp) {
            elements.downloadApp.addEventListener('click', handleDownloadClick);
        }

        // 账号登录
        if (elements.accountLogin) {
            elements.accountLogin.addEventListener('click', handleAccountLoginClick);
        }

        // 手机登录
        if (elements.phoneLogin) {
            elements.phoneLogin.addEventListener('click', handlePhoneLoginClick);
        }

        // 添加协议入口
        appendAgreement();

        // 移除事件监听器
        return () => {
            if (elements.passFgtpwd) {
                elements.passFgtpwd.removeEventListener('click', handleForgotPasswordClick);
            }

            if (elements.verifyCodeBtn) {
                elements.verifyCodeBtn.removeEventListener('click', handleVerifyCodeClick);
            }

            if (elements.passReglink) {
                elements.passReglink.removeEventListener('click', handleRegisterClick);
            }

            if (elements.downloadApp) {
                elements.downloadApp.removeEventListener('click', handleDownloadClick);
            }

            if (elements.accountLogin) {
                elements.accountLogin.removeEventListener('click', handleAccountLoginClick);
            }

            if (elements.phoneLogin) {
                elements.phoneLogin.removeEventListener('click', handlePhoneLoginClick);
            }
        };
    }, [
        handleForgotPasswordClick,
        handleVerifyCodeClick,
        handleRegisterClick,
        handleDownloadClick,
        handleAccountLoginClick,
        handlePhoneLoginClick,
    ]);

    return domOperate;
};

export default usePassportDomOperate;
