/**
 * @file 用于检测访问设备是否为移动端
 */

// 返回值为 true（是移动端） 或 false（非移动端）
export const isMobileDevice = () => {
    const ua = window.navigator.userAgent;

    const isAndroid = ua.includes('Android');
    // 是否为移动设备
    const isMobile = ua.includes('Mobile') || isAndroid;
    const isFireFox = /(?:Firefox)/.test(ua);
    // 是否为平板
    const isTablet =
        /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua));

    // 屏幕尺寸
    const width = window.screen.width;
    // 方便拖动浏览器视口模拟移动端调试
    const innerWidth = window.innerWidth;

    // tailwind中的md是>=768px，所以这里不包含768
    return width < 768 || innerWidth < 768 || (isMobile && !isTablet);
};
