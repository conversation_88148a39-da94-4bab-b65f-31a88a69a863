/**
 * @file 路由拦截器（服务商平台）
 * <AUTHOR>
 */

import {lazy} from 'react';
import {Navigate, Route, Routes} from 'react-router-dom';
import urls from '@/links/index-tp';

import {useUserInfoStore} from '@/store/login/userInfoStore';
import {BaseLayoutPC} from '@/modules/index-tp';
import {useAccountNavigation} from './hooks/useAccountNavigation-tp';

const Error = lazy(() => import('@/modules/error'));

const CustomerList = lazy(() => import('@/modules/tpCustomerList'));
export const RequireAuthRoute: React.FC<{children: React.ReactNode}> = ({children}) => {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const isBlackAccount = !!useUserInfoStore(store => store.blackAccountInfo);

    // 重定向
    useAccountNavigation();

    if (isLogin) {
        // 已登录 返回所有路由
        return children;
    }
    if (isBlackAccount) {
        return (
            <Routes>
                <Route path={urls.error.raw()} element={<Error />} />
            </Routes>
        );
    }

    // pc端未登录
    return (
        <Routes>
            <Route path={urls.root.raw()} element={<Navigate replace to={urls.customerList.raw()} />} />
            <Route path={urls.customerList.raw()} element={<BaseLayoutPC />}>
                <Route path={urls.customerList.raw()} element={<CustomerList />} />
            </Route>
        </Routes>
    );
};
