/**
 * @file 退出登录逻辑
 * <AUTHOR>
 */

import api from '@/api/login';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {PassportUrl} from '@/api/login/interface';

// 退出登录逻辑
export const useLogout = () => {
    const [loginShare, loginStateSwitch, setUserInfoData] = useUserInfoStore(store => [
        store.loginShare,
        store.loginStateSwitch,
        store.setUserInfoData,
    ]);
    const logout = async () => {
        try {
            // 请求接口 在这里发送请求
            const passportUrl: PassportUrl = await api.userLogout();
            // 清空用户信息
            setUserInfoData(null);
            // 不管退出没退出成功，前端都给它按照退出成功处理
            loginStateSwitch(false);
            // 跨页签同步-广播退登消息
            loginShare.postMessage({loginEvent: false});
            if (passportUrl) {
                window.location.href = passportUrl;
            }
        } catch (err: any) {
            // 后端没有退出失败的情况，失败toast在request中抛出
            loginStateSwitch(false);
        }
    };
    return logout;
};
