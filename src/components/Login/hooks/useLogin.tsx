/**
 * @file 登录相关hook
 * <AUTHOR>
 */

import {useCallback} from 'react';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {LoginSource} from '@/utils/loggerV2/interface';

export default function useLogin(loginSource?: LoginSource) {
    const {ubcClickLog} = useUbcLog();
    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin(loginSource);

    const loginCheck = useCallback(() => {
        if (isLogin) {
            return;
        }
        ubcClickLog(EVENT_TRACKING_CONST.LoginBtn);
        uniformLogin();
    }, [ubcClickLog, uniformLogin, isLogin]);

    return {
        loginCheck,
    };
}
