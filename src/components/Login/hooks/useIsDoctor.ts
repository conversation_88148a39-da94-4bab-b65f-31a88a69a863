/**
 * 判断用户是否是医生的 Hook
 * <AUTHOR>
 */

import {CareerType} from '@/api/account/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';

/**
 * 判断用户是否是医生的 Hook
 * @returns {boolean} 是否是医生
 */
export const useIsDoctor = () => {
    const [careerInfo] = useUserInfoStore(store => [store.userAccountInfo?.career]);
    // 执业机构
    const hasPracticingInstitution = !!careerInfo?.practicingInstitution;
    // 是否是医生，下游接口存在部分个人职业也是医生的场景，所以需要特殊处理
    return (
        careerInfo?.careerType === CareerType.Doctor ||
        (careerInfo?.careerType === CareerType.Profession && hasPracticingInstitution)
    );
};
