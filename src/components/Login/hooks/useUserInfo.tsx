/**
 * @file 登录逻辑
 * <AUTHOR>
 * @update 获取完用户信息，已登录用户，加载初始化昊天镜JSSDK <EMAIL> 2024.09
 */

import {useEffect} from 'react';
import {useLocation} from 'react-router-dom';
import {message} from 'antd';
import urls from '@/links';
import api from '@/api/login';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {EntityInfo, UserData} from '@/api/login/interface';
import {ErrorResponseCode, toastHttpErrorMessage} from '@/api/error';
import {wappassLogin} from '@/components/Login/utils/wappassLogin';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {useSecureStore} from '@/store/secure/useSecureStore';

/**
 * 登录逻辑 获取用户信息
 * @returns {void}
 */
export const useUserInfo = () => {
    const [
        loginShare,
        loginStateSwitch,
        authorityStateSwitch,
        setUserInfoData,
        setEntityInfo,
        setBlackAccountInfo,
        setLoginExpired,
        setUserInfoLoaded,
    ] = useUserInfoStore(store => [
        store.loginShare,
        store.loginStateSwitch,
        store.authorityStateSwitch,
        store.setUserInfoData,
        store.setEntityInfo,
        store.setBlackAccountInfo,
        store.setLoginExpired,
        store.setUserInfoLoaded,
    ]);
    const isMobile = isMobileDevice();
    const location = useLocation();

    const [initParisInstance] = useSecureStore(store => [store.initParisInstance]);

    useEffect(() => {
        // eslint-disable-next-line complexity
        (async () => {
            try {
                // 请求接口 在这里发送请求
                const response: UserData = await api.getUserInfo(
                    {
                        redir: window.location.href,
                    },
                    {
                        // 禁止异常弹窗，未登录10010 无权限10020 无需弹窗
                        forbiddenToast: true,
                    }
                );

                // 授权逻辑
                if (response?.stokenInfo?.stokenAuthFlag && response?.stokenInfo?.stokenAuthUrl) {
                    window.open(response?.stokenInfo?.stokenAuthUrl, '_self');
                    return;
                }

                // 修改状态管理仓库里的数据
                setUserInfoData(response);
                // Weirwood上报数据增加用户信息
                window.WeirwoodClient &&
                    window.WeirwoodClient?.error?.setContext({
                        userId: response?.userInfo?.userId || '',
                        user: response?.userInfo?.userName || '',
                    });
                loginStateSwitch(true); // 登录状态
                authorityStateSwitch(true); // 权限状态
                setEntityInfo(
                    response.customerInfo && response.customerInfo.customerId
                        ? response.customerInfo
                        : ({customerId: '', status: -1} as EntityInfo)
                );
                // 重置当前登录失效记录为false
                setLoginExpired(false);
                // 跨页签同步- 广播 登录成功
                loginShare.postMessage({loginEvent: true});
                setUserInfoLoaded(true);
                // 已登录，加载初始化昊天镜JSSDK
                initParisInstance();
            } catch (res: any) {
                switch (res.errno) {
                    case ErrorResponseCode.UserNotLoggedIn:
                        // 未登录
                        if (
                            isMobile &&
                            (location.pathname.includes(urls.entity.raw()) ||
                                location.pathname.includes(urls.account.raw()))
                        ) {
                            message.info('请登录');
                            // 移动端未登录 且进入的是留资页 直接进入pass登录页
                            wappassLogin();
                        } else {
                            loginStateSwitch(false);
                        }

                        setEntityInfo({customerId: '', status: -1} as EntityInfo);
                        break;
                    case ErrorResponseCode.UserNoPermission:
                        // 没有权限  没权限也有用户数据
                        setUserInfoData(res.data);
                        loginStateSwitch(true);
                        authorityStateSwitch(false);
                        setEntityInfo(
                            res.data.customerInfo && res.data.customerInfo.customerId
                                ? res.data.customerInfo
                                : ({customerId: '', status: -1} as EntityInfo)
                        );
                        break;
                    case ErrorResponseCode.UserAccountException:
                        // 黑产拦截
                        loginStateSwitch(false);
                        toastHttpErrorMessage(res);
                        setBlackAccountInfo(res?.msg || '当前账号存在异常情况，已被限制访问');
                        break;
                    default:
                        // 兜底报错toast
                        toastHttpErrorMessage(res);
                        loginStateSwitch(false);
                        setEntityInfo({customerId: '', status: -1} as EntityInfo);
                        break;
                }

                setUserInfoLoaded(true);
            }
        })();
        // 路径发生变化时不重新执行，location.pathname不添加进依赖数组，否则会无法退出登录
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [loginStateSwitch, setUserInfoData, authorityStateSwitch, setEntityInfo, isMobile, loginShare]);
};
