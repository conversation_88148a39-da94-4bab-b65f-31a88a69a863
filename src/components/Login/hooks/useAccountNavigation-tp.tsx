/**
 * @file 路由重定向（服务商平台）
 * <AUTHOR>
 */

import {useLocation, useNavigate} from 'react-router-dom';
import {useEffect} from 'react';
import urls from '@/links/index-tp';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {allowPath} from './useAccountNavigation';

// 目标路径是否为 pc【未登录】放行的路径
const noLoginAllowPath = [urls.root.raw(), urls.customerList.raw()];

// 重定向逻辑
export const useAccountNavigation = () => {
    const [isLogin, blackAccountInfo] = useUserInfoStore(store => [store.isLogin, store.blackAccountInfo]);

    const navigate = useNavigate();
    const location = useLocation();
    const isBlackAccount = !!blackAccountInfo;

    useEffect(() => {
        if (isBlackAccount) {
            navigate(urls.error.raw());
            return;
        }

        if (!isLogin && !allowPath(noLoginAllowPath)) {
            navigate(urls.customerList.raw());
        }
    }, [isBlackAccount, isLogin, location, navigate]);
};
