/**
 * @description 获取用户账户信息
 * <AUTHOR>
 */

import {
    AccountInfo,
    AccountQualifyType,
    CareerAuthStatus,
    CustomerAuthStatus,
    PersonQualifyStatus,
    QualifyStatus,
} from '@/api/account/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';

/** 从store中获取账户信息、是否企业、真实性认证是否通过等 */
export const useUserAccountInfo = () => {
    const [accountInfo] = useUserInfoStore(store => [store.userAccountInfo]);

    if (!accountInfo) {
        return {
            /** 账户信息 */
            accountInfo: {} as AccountInfo,
            /** 是否是个人 */
            isPersonal: false,
            /** 是否组织机构是企业类型 */
            isEnterprise: false,
            /** 是否组织机构 */
            isOrg: false,
            /** 是否是除了企业类型的其他组织机构（政府、事业单位、其他组织） */
            isOrgExceptEnterprise: false,
            /** 是否组织机构是【其他组织】类型（非政府、非事业单位） */
            isOtherOrg: false,
            /** 是否组织机构资质认证通过 */
            isOrgQualifyPass: false,
            /** 是否组织机构真实性认证通过 */
            isOrgAuthPass: false,
            /** 是否历史组织机构真实性认证豁免 */
            isOrgAuthExempt: false,
            /** 是否个人真实性认证通过 */
            isPersonAuthPass: false,
            /** 是否职业认证通过 */
            isCareerAuthPass: false,
        };
    }

    const isPersonal = accountInfo.accountType === AccountQualifyType.Person;
    const isEnterprise = accountInfo.accountType === AccountQualifyType.Enterprise;
    const isOtherOrg = accountInfo.accountType === AccountQualifyType.Other;

    return {
        /** 账户信息 */
        accountInfo,
        /** 是否是个人 */
        isPersonal,
        /** 是否组织机构是企业类型 */
        isEnterprise,
        /** 是否组织机构 */
        isOrg: !isPersonal,
        /** 是否是除了企业类型的其他组织机构（政府、事业单位、其他组织） */
        isOrgExceptEnterprise: !isPersonal && !isEnterprise,
        /** 是否组织机构是【其他组织】类型（非政府、非事业单位） */
        isOtherOrg,
        /** 是否组织机构资质认证通过 */
        isOrgQualifyPass: accountInfo.customerInfo?.entityStatus === QualifyStatus.PASS,
        /** 是否组织机构真实性认证通过 */
        isOrgAuthPass: accountInfo.customerInfo?.authStatus === CustomerAuthStatus.PASS,
        /** 是否历史组织机构真实性认证豁免 */
        isOrgAuthExempt: accountInfo.customerInfo?.authStatus === CustomerAuthStatus.ACCESS,
        /** 是否个人真实性认证通过 */
        isPersonAuthPass: accountInfo.personInfo?.authStatus === PersonQualifyStatus.PASS,
        /** 是否职业认证通过 */
        isCareerAuthPass: accountInfo.career?.careerAuthStatus === CareerAuthStatus.Pass,
    };
};
