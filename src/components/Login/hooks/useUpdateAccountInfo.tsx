/**
 * @description 更新用户认证信息，调用接口后同步更新useUserInfoStore中的userAccountInfo
 * <AUTHOR>
 */

import {useCallback, useEffect} from 'react';
import {AccountInfo} from '@/api/account/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import accountApi from '@/api/account';

export const useUpdateAccountInfo = () => {
    const {setUserAccountInfo, isLogin} = useUserInfoStore(store => ({
        setUserAccountInfo: store.setUserAccountInfo,
        isLogin: store.isLogin,
    }));
    const updateUserAccountInfo = useCallback(async () => {
        try {
            const response: AccountInfo = await accountApi.getAccountInfo();
            setUserAccountInfo(response);
        } catch (error) {
            console.error('获取账户信息失败', error);
        }
    }, [setUserAccountInfo]);

    useEffect(() => {
        if (isLogin) {
            updateUserAccountInfo();
        }
    }, [updateUserAccountInfo, isLogin]);
};
