/**
 * @file 退出登录
 * <AUTHOR>
 */

import {useCallback, useState} from 'react';
import {useLogout} from '@/components/Login/hooks/useLogout';

export default function useLogoutModal() {
    const [modalOpen, setModalOpen] = useState(false);
    const logout = useLogout();
    const logoutClickHandler = useCallback(() => {
        setModalOpen(true);
    }, [setModalOpen]);

    const logoutHandler = useCallback(() => {
        logout().then(() => {
            setModalOpen(false);
        });
    }, [logout]);

    const cancelLogoutHandler = useCallback(() => {
        setModalOpen(false);
    }, [setModalOpen]);

    return {
        logout<PERSON>lick<PERSON>and<PERSON>,
        logoutHandler,
        cancelLogoutHandler,
        modalOpen,
        setModalOpen,
    };
}
