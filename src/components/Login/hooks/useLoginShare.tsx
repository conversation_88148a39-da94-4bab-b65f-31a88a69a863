/**
 * @file 登录态 跨标签页共享 & 登录失效监听
 * <AUTHOR>
 */
import {useCallback, useEffect, useRef} from 'react';
import {useUserInfoStore} from '@/store/login/userInfoStore';

export const useLoginShare = () => {
    const [isLogin, loginShare, loginStateSwitch, setUserInfoData, setShowLoginModal, setLoginExpired] =
        useUserInfoStore(store => [
            store.isLogin,
            store.loginShare,
            store.loginStateSwitch,
            store.setUserInfoData,
            store.setShowLoginModal,
            store.setLoginExpired,
        ]);

    const currentIsLogin = useRef(isLogin);

    useEffect(() => {
        currentIsLogin.current = isLogin;
    }, [isLogin]);

    const handleLoginChange = useCallback(
        ({loginEvent}: {loginEvent: boolean}) => {
            setShowLoginModal(false);
            if (loginEvent && !currentIsLogin.current) {
                window.location.reload();
            }
            if (!loginEvent && currentIsLogin.current) {
                loginStateSwitch(false);
                setUserInfoData(null);
            }
        },
        [loginStateSwitch, setShowLoginModal, setUserInfoData]
    );

    useEffect(() => {
        // 初始化登录失效的记录状态
        setLoginExpired(false);

        /** 绑定登入登出态跨页签共享监听。注意：这里初次渲染绑定一次 handleLoginChange函数
         * handleLoginChange 中 如果 直接使用isLoginExpired 和 isLogin ,会受到闭包的影响，获取不到最新值，
         * 所以需要 useEffect监听变化，最终使用 currentIsLogin 和 currentIsLoginExpired 获取到最新的值
         */
        loginShare.listen(handleLoginChange);

        return () => {
            loginShare.removeListener();
        };
        // eslint-disable-next-line
    }, []);
};
