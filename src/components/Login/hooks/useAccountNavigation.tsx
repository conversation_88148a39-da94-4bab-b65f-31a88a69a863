/**
 * @file 路由重定向
 * <AUTHOR>
 */

import urls from '@/links';

// 去除动态参数 只保留path
export const removeParameter = (path: string): string => {
    const pathSegments = path.split('/');

    if (pathSegments.length > 1) {
        pathSegments.pop();
        return pathSegments.join('/');
    }
    return path;
};

export const allowPath = (pathArr: string[]) => {
    // 无权限或无登录情况下，文档页都放行
    if (location.pathname.startsWith(urls.docs.raw())) {
        return true;
    }

    // 对于动态的 path，使用前缀。
    return pathArr.some(item => {
        if (location.pathname === item) {
            return true;
        } else if (location.pathname.includes(item)) {
            // 去除动态id后匹配
            return removeParameter(location.pathname) === item;
        } else return false;
    });
};
