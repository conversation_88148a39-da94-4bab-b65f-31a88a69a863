/**
 * @description 更新开发者信息，调用接口后同步更新 useUserInfoStore 中的 developerInfo
 * <AUTHOR>
 */

import {useCallback, useEffect} from 'react';
import {DeveloperInfo} from '@/api/account/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import api from '@/api/account';

export const useUpdateDeveloperInfo = () => {
    const {setDeveloperInfo, isLogin} = useUserInfoStore(store => ({
        setDeveloperInfo: store.setDeveloperInfo,
        isLogin: store.isLogin,
    }));
    const updateDeveloperInfo = useCallback(async () => {
        try {
            const response: DeveloperInfo = await api.getDeveloperInfo();
            setDeveloperInfo(response);
        } catch (error) {
            console.error('获取开发者信息失败', error);
        }
    }, [setDeveloperInfo]);

    useEffect(() => {
        if (isLogin) {
            updateDeveloperInfo();
        }
    }, [updateDeveloperInfo, isLogin]);
};
