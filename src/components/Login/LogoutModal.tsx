/**
 * @file 退出登录提示弹窗组件
 * <AUTHOR>
 */

import React, {forwardRef, useImperativeHandle} from 'react';
import {Space, Modal, Button} from 'antd';
import useLogoutModal from '@/components/Login/hooks/useLogoutModal';
import {ConfirmModalStyleConfig} from '@/styles/components/ModalStyle';

export interface LogoutWarningModalRef {
    logoutClickHandler: () => void;
}

const LogoutWarningModal = forwardRef<LogoutWarningModalRef, any>((props, ref) => {
    const {modalOpen, logoutClickHandler, logoutHandler, cancelLogoutHandler} = useLogoutModal();

    useImperativeHandle(ref, () => {
        return {
            logoutClickHandler,
        };
    });

    return (
        <Modal
            centered
            title="你确定要退出吗？"
            open={modalOpen}
            styles={ConfirmModalStyleConfig}
            onCancel={cancelLogoutHandler}
            width={400}
            closable={false}
            footer={
                <Space size={[6, 0]}>
                    <Button
                        key="lingjing-logout"
                        shape="round"
                        onClick={logoutHandler}
                        type="default"
                        className="font-medium"
                    >
                        退出
                    </Button>
                    <Button
                        key="lingjing-stay-login"
                        shape="round"
                        onClick={cancelLogoutHandler}
                        type="primary"
                        className="font-medium"
                    >
                        保持登录
                    </Button>
                </Space>
            }
        >
            <p>退出后，将无法使用完整功能</p>
        </Modal>
    );
});

export default LogoutWarningModal;
