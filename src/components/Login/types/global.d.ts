declare interface Window {
    $message: {
        error: (message: string) => void;
        success: (message: string) => void;
        warn: (message: string) => void;
    };
    BMap?: BMapSDK;
    onBMapCallback: () => void;
    passport: PassportSDK.Passport;
    initPopUp: any;
}

declare const $features: Record<string, any>;
declare const $build: {version: string; time: string; target: string};
