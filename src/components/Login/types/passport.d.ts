/**
 * @file PassportSDK 的 简易类型定义
 * @description 参考文档：http://dev.passport.baidu.com/docs/agg/viewv2?path=tech.text&doc=pc/passport/loginv4.text
 * <AUTHOR>
 */

declare namespace PassportSDK {
    interface Event<TRes extends Record<string, any> = Record<string, any>> {
        returnValue: boolean;
        rsp?: {
            data?: TRes;
        };
    }

    interface LoginParam {
        product: string;
        staticPage: string;
        u: string;
        /**
         * 是否记住登录状态
         */
        memberPass?: boolean;
        /**
         * 短信登录
         */
        sms?: number;
        passport?: number;
        /**
         * 默认帐号密码登录，配置1表示指定帐号密码登录
         */
        userPwdLogin?: number;
        /**
         * 默认二维码登录，配置1表示指定二维码登录
         */
        qrcodeLogin?: number;
        /**
         * 是否自动加载默认CSS，仅配置loginMerge后有效
         */
        defaultCss?: boolean;
        loginMerge?: boolean;
        /**
         * 是否有邮箱/历史登录记录提示，仅配置loginMerge后有效
         */
        autosuggest?: boolean;
        /**
         * 扫码登录
         */
        qrcode?: boolean;
        hasPlaceholder?: boolean;
        /**
         * 是否有注册链接
         */
        hasRegUrl?: boolean;
        /**
         * 是否进行id唯一处理
         */
        uniqueId?: boolean;
        /**
         * 低版本不支持，用接口处理cookie是否同步bai-int
         */
        isSyncBaiduInt?: number | boolean;
        /**
         * 是否隐藏百度logo（默认为0展示logo，1 隐藏百度logo）
         */
        hideLogo?: 1 | 0;
        /**
         * 左侧展示扫码登录，右侧展示其他登录
         */
        composeTemplate: 1 | 0;
    }

    class Login<TRes extends Record<string, unknown> = Record<string, unknown>> {
        constructor(param: LoginParam);
        render(id: string): void;
        on(name: 'loginSuccess' | 'loginError' | 'render', callback: (e: Event<TRes>) => void);
    }

    type LoginFun<TRes extends Record<string, unknown> = Record<string, unknown>> = new (
        option?: LoginParam
    ) => Login<TRes>;

    interface ApiMagic<TRes extends Record<string, unknown> = Record<string, unknown>> {
        passport: {
            login: LoginFun<TRes>;
        };
    }

    interface Passport {
        pop: Pop;
        use<TRes extends Record<string, any> = Record<string, any>>(
            action: string,
            option: {tangram?: boolean; loginVersion?: `v${number}`},
            callback: (apiMagic: ApiMagic<TRes>) => void
        ): void;
    }

    interface Pop {
        init(options?: any): any;
        insertScript(src: string, callback?: () => void);
    }
}
