/**
 * @file 带字数文本域
 * <AUTHOR>
 * 2023/03/16
 */

import styled from '@emotion/styled';
import {Input} from 'antd';

export default styled(Input.TextArea)`
    .ant-input-suffix .ant-input-data-count {
        bottom: 5px;
        right: 12px;
        padding-left: 5px;
        color: #b7b9c1;
    }
    &.ant-input-textarea-affix-wrapper.ant-input-affix-wrapper {
        padding-bottom: 30px;
        padding-right: 3px;
        textarea {
            padding-top: 0px;
            margin-top: 4px;
        }
        textarea::-webkit-scrollbar {
            background: transparent;
            width: 4px;
        }
        textarea::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 5px;
        }
        textarea {
            color: inherit !important;
        }
    }
    &.ant-input {
        &::-webkit-scrollbar {
            background: transparent;
            width: 4px;
        }
        &::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 5px;
            cursor: pointer;
        }
    }
`;
