/**
 * @file 右侧面板，一般用来展示详情
 * <AUTHOR>
 */

import classNames from 'classnames';
import {CloseOutlined} from '@ant-design/icons';
import {useLayoutStore} from '@/store/home/<USER>';
import {useRightPanel} from './useRightPanelHook';

export default function RightPanel({className, children}: {className?: string; children?: React.ReactNode}) {
    const {showRightPanel, rightPanelWidth, setShowRightPanel} = useLayoutStore(store => ({
        showRightPanel: store.showRightPanel,
        rightPanelWidth: store.rightPanelWidth,
        setShowRightPanel: store.setShowRightPanel,
    }));

    useRightPanel();

    return (
        <div
            className={classNames('relative overflow-hidden bg-white transition-[width] duration-200', className)}
            style={{
                width: showRightPanel ? rightPanelWidth : '0px',
                height: showRightPanel ? '100vh' : '0px',
            }}
        >
            <span
                className="absolute right-4 top-4 z-[1] flex h-10 w-10 cursor-pointer items-center justify-center text-gray-tertiary"
                onClick={() => {
                    setShowRightPanel(false);
                }}
            >
                <CloseOutlined />
            </span>
            <div className="h-screen overflow-y-auto px-6 pt-8">{children}</div>
        </div>
    );
}
