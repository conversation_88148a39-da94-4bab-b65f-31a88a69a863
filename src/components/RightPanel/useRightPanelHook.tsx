/**
 * @file 右侧面板RightPanel
 * <AUTHOR>
 */

import {useEffect} from 'react';
import {useLayoutStore} from '@/store/home/<USER>';

const getRightPanelWidth = (): string => {
    // 可视屏幕尺寸<=1680，右侧面板占父容器页面的50%，>1680, 右侧面板宽度为700px
    return window.innerWidth <= 1680 ? '50%' : '700px';
};

export const useRightPanel = () => {
    const {setRightPanelWidth, clearRightPanel} = useLayoutStore(store => ({
        setRightPanelWidth: store.setRightPanelWidth,
        clearRightPanel: store.clearRightPanel,
    }));

    useEffect(() => {
        // 初始化右侧面板宽度，让整个页面自适应
        setRightPanelWidth(getRightPanelWidth());

        // 视口尺寸改变实时更新右侧面板宽度
        const handleResize = () => {
            setRightPanelWidth(getRightPanelWidth());
        };

        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [setRightPanelWidth]);

    useEffect(() => {
        // 组件卸载时，设置RightPanel为初始态
        return () => {
            clearRightPanel();
        };
    }, [clearRightPanel]);
};
