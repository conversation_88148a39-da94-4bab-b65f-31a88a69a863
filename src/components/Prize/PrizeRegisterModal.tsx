// 奖品登记弹窗

import {Button, Form, message, Modal} from 'antd';
import styled from '@emotion/styled';
import {memo, useCallback, useEffect, useState} from 'react';
import {validatePhone} from '@/utils/formValidator';
import Input from '@/components/Input';
import {LogisticsInfo, TaskIdInfo} from '@/api/task/interface';
import api from '@/api/task';

interface PrizeRegisterModalProps {
    visible: boolean;
    onClose: () => void;
    taskIdInfo: TaskIdInfo | null;
    logisticsInfo: LogisticsInfo | null;
}

const StyledModal = styled(Modal)``;

const StyledForm = styled(Form)`
    .ant-form-item-required {
        font-weight: 400;
        font-size: 14px;
    }
    .ant-form-item-required::before {
        display: none !important;
    }
    .ant-form-item-required::after {
        display: inline-block;
        margin-inline-start: 6px !important;
        color: #ff3333;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 32px;
        content: '*' !important;
    }

    .ant-form-item-control-input {
        justify-content: flex-end;
        .ant-form-item-control-input-content {
            flex: none;
        }
    }

    .ant-form-item-explain-error {
        width: 445px !important;
        margin-left: auto;
    }
`;

// 弹窗组件
export const PrizeRegisterModal = memo(({visible, onClose, taskIdInfo, logisticsInfo}: PrizeRegisterModalProps) => {
    const [form] = Form.useForm();
    const [disabled, setDisabled] = useState(true);
    const [loading, setLoading] = useState(false);
    const values = Form.useWatch([], form);

    const handleClose = useCallback(() => {
        form.resetFields();
        setDisabled(false);
        setLoading(false);
        onClose();
    }, [form, onClose]);

    const handleSubmit = useCallback(async () => {
        setDisabled(true);
        setLoading(true);
        try {
            if (!taskIdInfo?.recordId) {
                message.error('任务信息获取失败');
                return;
            }

            await api.sendTaskAward({
                recordId: taskIdInfo?.recordId,
                name: values.name,
                phone: values.phone,
                address: values.address,
            });
            message.success('登记成功');
            handleClose();
        } catch (e) {
            throw e;
        } finally {
            setDisabled(false);
            setLoading(false);
        }
    }, [handleClose, taskIdInfo?.recordId, values]);

    useEffect(() => {
        if (!values || Object.keys(values).length === 0) {
            return;
        }

        form.validateFields({validateOnly: true})
            .then(() => setDisabled(false))
            .catch(() => setDisabled(true));
    }, [form, values]);

    return (
        <StyledModal
            open={visible}
            onCancel={handleClose}
            footer={null}
            destroyOnClose
            width={600}
            centered
            title={'领取奖品'}
            maskStyle={{background: 'rgba(0, 0, 0, 0)'}}
        >
            <div className="flex flex-col gap-2">
                <div className="mb-[14px] flex w-full flex-col items-center justify-center">
                    <img
                        className="my-auto h-[120px] w-[120px] rounded-[12px] object-cover"
                        src={logisticsInfo?.prize.icon}
                    />
                    <span className="mt-[6px]">{logisticsInfo?.prize.name}</span>
                </div>
                <StyledForm form={form}>
                    <Form.Item
                        name="name"
                        label="收件人姓名"
                        rules={[{required: true, message: '姓名不能为空，请输入'}]}
                    >
                        <Input autoComplete="name" placeholder="请输入" className="w-[445px]" />
                    </Form.Item>
                    <Form.Item
                        name="phone"
                        label="联系电话"
                        rules={[
                            {required: true, message: '联系电话不能为空，请输入'},
                            {validator: validatePhone('联系电话格式有误，请重新输入')},
                        ]}
                    >
                        <Input autoComplete="tel" placeholder="请输入" className="w-[445px]" />
                    </Form.Item>
                    <Form.Item
                        name="address"
                        label="收件地址"
                        rules={[{required: true, message: '地址不能为空，请输入'}]}
                        className="mb-0"
                    >
                        <Input autoComplete="address" placeholder="请输入" className="w-[445px]" />
                    </Form.Item>
                    <div className="mt-[18px] flex">
                        <span className="iconfont icon-warn text-gray-tertiary"></span>
                        <span className="ml-[6px] mt-[2px] w-full text-sm text-gray-tertiary">
                            个人信息仅用作奖品发放，平台将严格保护您的隐私信息。系统审核通过后将尽快寄出，可在任务记录里查看详情。
                        </span>
                    </div>
                    <div className="mt-[18px] flex w-full justify-end gap-2 ">
                        <Button shape="round" className="text-sm font-medium" onClick={onClose}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            shape="round"
                            className="text-sm font-medium"
                            onClick={handleSubmit}
                            loading={loading}
                            disabled={disabled}
                        >
                            确定
                        </Button>
                    </div>
                </StyledForm>
            </div>
        </StyledModal>
    );
});
