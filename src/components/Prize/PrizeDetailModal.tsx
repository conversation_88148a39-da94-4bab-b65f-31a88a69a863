/* eslint-disable complexity */
// 奖品详情弹窗

import {Button, message, Modal, Spin} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useEffect, useMemo, useState} from 'react';
import api from '@/api/task';
import {LogisticsInfo, ReceiveStatus, TaskIdInfo, PrizeType, AuditStatus, PrizeSType} from '@/api/task/interface';
import {useLottery} from './useLottery';

interface PrizeDetailModalProps {
    visible: boolean;
    onClose: () => void;
    taskIdInfo: TaskIdInfo | null;
}

const StyledModal = styled(Modal)``;
// 弹窗组件
interface CopyMessageProps {
    name: string;
    value: string;
}
const CopyMessage = ({name, value}: CopyMessageProps) => {
    const copy = useCallback(() => {
        navigator.clipboard.writeText(value);
        message.success(`复制${name}成功`);
    }, [name, value]);
    return (
        <div className="mt-[18px] flex w-full flex-row justify-between text-sm font-normal">
            <div className="mr-6 leading-9">{name}</div>
            <div className="relative h-9 w-[486px] rounded-lg bg-colorBgFormList ">
                <div
                    className="h-full max-w-[420px] overflow-scroll whitespace-nowrap pl-3 leading-9 text-gray-tertiary"
                    style={{
                        scrollbarWidth: 'none',
                    }}
                >
                    {value}
                </div>
                <Button type="link" className="absolute right-0 top-1/2 -translate-y-1/2 text-sm " onClick={copy}>
                    <span className="icon-copy iconfont mr-1 text-xs"></span>
                    复制
                </Button>
            </div>
        </div>
    );
};

// 格式化时间戳为年月日时分
const formatDate = (timestamp: string | number) => {
    const date = new Date(Number(timestamp) * 1000); // 将秒转换为毫秒
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}/${month}/${day} ${hours}:${minutes}`;
};

export function PrizeDetailModal({visible, onClose, taskIdInfo}: PrizeDetailModalProps) {
    const {openPrizeRegister} = useLottery();
    const [logisticsInfo, setLogisticsInfo] = useState<LogisticsInfo | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (taskIdInfo && visible) {
            setIsLoading(true);
            api.getLogisTics({
                subTaskId: taskIdInfo.subTaskId,
                taskPageId: taskIdInfo.taskPageId,
                taskPkgId: taskIdInfo.taskPkgId,
            })
                .then(res => {
                    setLogisticsInfo(res);
                })
                .finally(() => {
                    setIsLoading(false);
                });
        }
        return () => {
            setLogisticsInfo(null);
            setIsLoading(true);
        };
    }, [taskIdInfo, visible]);

    const getReceiveStatus = useMemo(() => {
        // 待领取
        if (logisticsInfo?.receiveStatus === ReceiveStatus.WAIT_RECEIVE) {
            return '待领取';
        }

        // O端待审核 或 未填写快递单号时，展示待发货页面
        if (logisticsInfo?.auditStatus === AuditStatus.WAIT_AUDIT || !logisticsInfo?.prizeAccount) {
            return '待发货，系统审核通过后更新快递单号';
        }

        // O端审核通过且填写快递单号时，展示已发货界面
        if (logisticsInfo?.auditStatus === AuditStatus.AUDIT_PASS && logisticsInfo?.prizeAccount) {
            return '已发货';
        }
    }, [logisticsInfo]);

    // 去领取
    const handleReceive = useCallback(() => {
        openPrizeRegister(taskIdInfo, logisticsInfo);
    }, [openPrizeRegister, taskIdInfo, logisticsInfo]);

    return (
        <StyledModal
            open={visible}
            onCancel={onClose}
            footer={null}
            destroyOnClose
            width={600}
            centered
            title={'奖品详情'}
        >
            {isLoading && !logisticsInfo ? (
                <div className="flex h-[220px] w-full items-center justify-center">
                    <Spin />
                </div>
            ) : (
                <>
                    {logisticsInfo?.prize.type === PrizeType.PHYSICAL ||
                    logisticsInfo?.prize.sType === PrizeSType.THIRD_PARTY_COUPON ? (
                        <div className="mt-4 flex items-start">
                            {/* 实物 or 第三方奖品详情 */}
                            <div className="flex">
                                <img
                                    className="h-[120px] w-[120px] rounded-[12px] object-cover"
                                    src={logisticsInfo?.prize.icon}
                                />
                            </div>
                            <div className="ml-6 inline-block w-[408px]">
                                <div className="mb-2">奖品名称：{logisticsInfo?.prize.name}</div>
                                <div className="mb-2">
                                    获取时间：
                                    {logisticsInfo?.prize.receiveTime
                                        ? formatDate(logisticsInfo.prize.receiveTime)
                                        : '-'}
                                </div>
                                {logisticsInfo?.prize.sType === PrizeSType.THIRD_PARTY_COUPON ? (
                                    <>
                                        <div className="mb-2">奖品已于第三方平台领取，感谢参与！</div>
                                    </>
                                ) : (
                                    <>
                                        <div className="mb-2">发货详情：{getReceiveStatus}</div>
                                        {logisticsInfo?.receiveStatus !== ReceiveStatus.WAIT_RECEIVE && (
                                            <>
                                                {!!logisticsInfo?.prizeAccount && (
                                                    <div className="mb-[12px]">
                                                        快递单号：{logisticsInfo.prizeAccount}
                                                    </div>
                                                )}
                                                <div className="flex flex-col gap-1 rounded-[9px] bg-gray-bg-base p-3 text-xs font-normal text-gray-tertiary">
                                                    <div>收件人：{logisticsInfo?.postInfo.name}</div>
                                                    <div>联系方式：{logisticsInfo?.postInfo.phone}</div>
                                                    <div>收获地址：{logisticsInfo?.postInfo.address}</div>
                                                </div>
                                            </>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="flex flex-col gap-2">
                            {/* 卡券奖品详情 */}
                            <div className="flex w-full flex-col items-center justify-center">
                                <img
                                    className="my-auto h-[120px] w-[120px] rounded-[12px] object-cover"
                                    src={logisticsInfo?.prize.icon}
                                />
                                <span className="mt-[6px]">{logisticsInfo?.prize.name}</span>
                                <CopyMessage
                                    name="兑换码"
                                    value={logisticsInfo?.prizeAccount || logisticsInfo?.prize.assetValue || ''}
                                />
                                {logisticsInfo?.prize.prizeTxtIntro && (
                                    <div className="mt-[18px] flex w-full items-start">
                                        <span className="iconfont icon-warn text-[15px] leading-4 text-gray-tertiary"></span>
                                        <span className="ml-[6px] w-full text-left align-top text-sm text-gray-tertiary">
                                            {logisticsInfo.prize.prizeTxtIntro}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="mt-11 flex w-full justify-end gap-2 ">
                        {logisticsInfo?.receiveStatus === ReceiveStatus.WAIT_RECEIVE &&
                        logisticsInfo?.prize.type === PrizeType.PHYSICAL ? (
                            <>
                                <Button shape="round" className="text-sm font-medium" onClick={onClose}>
                                    取消
                                </Button>
                                <Button
                                    type="primary"
                                    shape="round"
                                    className="text-sm font-medium"
                                    onClick={handleReceive}
                                >
                                    去领取
                                </Button>
                            </>
                        ) : (
                            <Button
                                type="primary"
                                shape="round"
                                className="h-[30px] text-sm font-medium"
                                onClick={onClose}
                            >
                                知道了
                            </Button>
                        )}
                    </div>
                </>
            )}
        </StyledModal>
    );
}
