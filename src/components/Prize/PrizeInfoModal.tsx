import {useCallback, useMemo} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {DrawPrize, TaskIdInfo} from '@/api/task/interface';
import winTitle from '@/assets/prize/winTitle.png';
import loseTitle from '@/assets/prize/loseTitle.png';
import winContainer from '@/assets/prize/winContainer.png';
import loseContainer from '@/assets/prize/loseContainer.png';
import buttonTask from '@/assets/prize/Button_TASK.png';
import buttonPrize from '@/assets/prize/Button_Prize.png';

import prizeIcon from '@/assets/prize/prize.png';
import urls from '@/links';
import {StyledModal} from './LotteryModal';
import {useLottery} from './useLottery';

interface PrizeInfoModalProps {
    visible: boolean;
    onClose: () => void;
    taskIdInfo: TaskIdInfo | null;
    prizeInfo?: DrawPrize;
}
export default function PrizeInfoModal({visible, onClose, taskIdInfo, prizeInfo}: PrizeInfoModalProps) {
    const {openPrizeDetail, refreshTaskRecord} = useLottery();
    const {pathname} = useLocation();
    const navigate = useNavigate();
    const ImageMap = useMemo(() => {
        if (prizeInfo?.isWin) {
            return {
                title: winTitle,
                container: winContainer,
                button: buttonPrize,
                icon: prizeInfo?.prize.icon,
                iconPosition: '130px',
                discription: prizeInfo?.prize.name,
                discFontSize: '25px',
                titleWidth: '295px',
                style: {
                    width: '100px',
                    height: '100px',
                },
            };
        }
        return {
            title: loseTitle,
            container: loseContainer,
            button: buttonTask,
            icon: prizeIcon,
            iconPosition: '90px',
            discription: '继续做任务获更多抽奖机会',
            discFontSize: '17px',
            titleWidth: '346px',
            style: {
                width: '143px',
                height: '162px',
            },
        };
    }, [prizeInfo]);

    const buttonClick = useCallback(() => {
        // 中奖，且在活动列表时，转跳到任务记录
        if (prizeInfo?.isWin && pathname === urls.activityList.raw()) {
            navigate(urls.activityTaskRecord.raw());
            // 中奖，且在任务记录页面时，打开领取奖品页面
        } else if (prizeInfo?.isWin) {
            openPrizeDetail(taskIdInfo);
            refreshTaskRecord?.();
            // 未中奖，转跳回任务记录
        } else {
            navigate(urls.activityList.raw());
        }

        onClose();
    }, [prizeInfo?.isWin, pathname, navigate, openPrizeDetail, taskIdInfo, onClose, refreshTaskRecord]);

    const handleClose = useCallback(() => {
        onClose();
        refreshTaskRecord?.();
    }, [onClose, refreshTaskRecord]);

    return (
        <StyledModal
            open={visible}
            onCancel={handleClose}
            footer={null}
            destroyOnClose
            width={400}
            maskClosable={false}
            centered
        >
            <div className="h-[533px] py-3">
                <img src={ImageMap.title} className="mx-auto mb-[19px]" style={{width: ImageMap.titleWidth}} />
                <div className="relative">
                    <img src={ImageMap.container} className="-ml-7 h-[463px] w-[480px] max-w-none" />
                    <div
                        className="absolute left-1/2  ml-[5px] -translate-x-1/2"
                        style={{
                            top: ImageMap.iconPosition,
                            width: ImageMap.style.width,
                            height: ImageMap.style.height,
                        }}
                    >
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div className="flex  items-center justify-center">
                                <img src={ImageMap.icon} className="h-full w-full object-contain" alt="prize" />
                            </div>
                        </div>
                    </div>
                    <div className="absolute left-1/2 top-[280px] w-[220px] -translate-x-1/2">
                        <div
                            className="break-words text-center font-bold text-[#215EF6]"
                            style={{fontSize: ImageMap.discFontSize}}
                        >
                            {ImageMap.discription}
                        </div>
                    </div>
                    <img
                        src={ImageMap.button}
                        className="absolute bottom-14 left-1/2 w-[220px] -translate-x-1/2 cursor-pointer"
                        onClick={buttonClick}
                    />
                </div>
            </div>
        </StyledModal>
    );
}
