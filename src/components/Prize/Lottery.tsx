/* eslint-disable complexity */
// 转盘组件
import styled from '@emotion/styled';
import {message, Spin} from 'antd';
import React, {useRef, useState, useEffect, useCallback} from 'react';
import LotteryUrl from '@/assets/prize/Lottery.png';
import ButtonUPUrl from '@/assets/prize/Button_UP.png';
import ButtonDOWNUrl from '@/assets/prize/Button_DOWN.png';
import PointerUrl from '@/assets/prize/Pointer.png';
import prizeTitleUrl from '@/assets/prize/prizeTitle.png';
import api from '@/api/task';
import {DrawPrize, TaskIdInfo} from '@/api/task/interface';
import {Sector} from './LotteryModal';

const Container = styled.div`
    width: 400px;
    height: 533px;
    display: flex;
    flex-direction: column;
    background-image: url(${LotteryUrl});
    background-size: 110%;
    background-repeat: no-repeat;
    background-position: center 80px;
`;

const WheelContainer = styled.div`
    margin-top: 70px;
    position: relative;
    width: 280px;
    height: 280px;
    border-radius: 50%;
    overflow: visible;
    margin-left: 58px;
`;

const Wheel = styled.svg`
    position: absolute;
    width: 100%;
    height: 100%;
    transition: transform 3s ease-out;
`;

const CenterButton = styled.div`
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 84px;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    background-image: url(${PointerUrl});
    background-size: 100% 100%;
`;

interface StyledButtonProps {
    isSpinning: boolean;
}

const StyledButton = styled.div<StyledButtonProps>`
    margin-left: 97px;
    margin-top: 8px;
    height: 60px;
    width: 197px;
    border: none;
    background: transparent;
    cursor: pointer;
    background-image: url(${props => (props.isSpinning ? ButtonDOWNUrl : ButtonUPUrl)});
    background-size: 100% 100%;
    transition: transform 0.5s ease-in-out;
    pointer-events: ${props => (props.isSpinning ? 'none' : 'auto')};
`;

// 常量配置
const WHEEL_CONFIG = {
    // 最大速度，单位：度/秒
    MAX_SPEED: 12,
    // 加速度，单位：度/秒^2
    ACCELERATION: 1,
    // 帧时间，单位：毫秒
    FRAME_TIME: 16,
    // 旋转时间，单位：毫秒
    SPIN_DURATION: 4000,
    // 额外旋转次数
    EXTRA_ROTATIONS: 3,
} as const;

// 自定义 Hook 处理动画逻辑
const useWheelAnimation = ({sectors, angle}: {sectors: Sector[]; angle: number}) => {
    // 旋转角度
    const [rotation, setRotation] = useState(0);
    // 是否正在旋转
    const [isSpinning, setIsSpinning] = useState(false);
    // 当前转速
    const [currentSpeed, setCurrentSpeed] = useState(0);
    // 动画引用
    const animationRef = useRef<number | null>(null);

    // 清理动画
    const cleanupAnimations = useCallback(() => {
        if (animationRef.current !== null) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    }, []);

    // 组件卸载时清理动画
    useEffect(() => {
        return () => cleanupAnimations();
    }, [cleanupAnimations]);

    // 计算目标旋转角度
    const calculateTargetRotation = useCallback(
        (targetSectorId: string) => {
            // 找到目标扇区
            const targetSector = sectors.find(s => s.prizeid === targetSectorId);
            if (!targetSector || !targetSector.angle) return 0;

            // 计算目标角度
            const targetAngle = -targetSector.angle - angle / 2 - 90;
            // 当前旋转角度
            const currentRotation = rotation % 360;
            // 目标角度与当前角度差
            let deltaAngle = targetAngle - currentRotation;
            // 如果目标角度小于当前角度，则加上360度
            if (deltaAngle < 0) {
                deltaAngle += 360;
            }

            // 返回目标角度加上额外旋转次数的360度倍数,目的是为了减速的时候能看起来缓慢些
            return rotation + deltaAngle + 360 * WHEEL_CONFIG.EXTRA_ROTATIONS;
        },
        [angle, sectors, rotation]
    );

    return {
        rotation,
        isSpinning,
        currentSpeed,
        setRotation,
        setIsSpinning,
        setCurrentSpeed,
        cleanupAnimations,
        calculateTargetRotation,
        animationRef,
    };
};

const LotteryWheel = ({
    loading,
    sectors,
    angle,
    unPrizeId,
    taskIdInfo,
    onClose,
    setPrizeInfo,
    setPrizeInfoModalVisible,
}: {
    loading: boolean;
    sectors: Sector[];
    angle: number;
    unPrizeId: string[];
    taskIdInfo: TaskIdInfo | null;
    onClose: () => void;
    setPrizeInfo: (prizeInfo?: DrawPrize) => void;
    setPrizeInfoModalVisible: (visible: boolean) => void;
}) => {
    const wheelRef = useRef<SVGSVGElement>(null);
    const {
        rotation,
        isSpinning,
        currentSpeed,
        setRotation,
        setIsSpinning,
        setCurrentSpeed,
        cleanupAnimations,
        calculateTargetRotation,
        animationRef,
    } = useWheelAnimation({sectors, angle});

    // 模拟抽奖请求
    const lotteryRequest = useCallback(async () => {
        if (!taskIdInfo) return;
        const res = await api.drawPrize({
            taskPageId: taskIdInfo.taskPageId,
            taskPkgId: taskIdInfo.taskPkgId,
            subTaskId: taskIdInfo.subTaskId,
        });
        if (!res) {
            throw new Error('抽奖结果为空');
        }
        return res;
    }, [taskIdInfo]);

    // 开始旋转
    const spinWheel = useCallback(async () => {
        if (isSpinning) return;

        try {
            cleanupAnimations();
            setIsSpinning(true);
            setCurrentSpeed(0);
            setRotation(0);

            let lastTime = performance.now();
            let speed = 0;

            // 加速动画
            const accelerate = (currentTime: number) => {
                const deltaTime = currentTime - lastTime;

                // 如果时间间隔大于等于帧时间
                if (deltaTime >= WHEEL_CONFIG.FRAME_TIME) {
                    // 加速
                    speed = Math.min(speed + WHEEL_CONFIG.ACCELERATION, WHEEL_CONFIG.MAX_SPEED);
                    // 更新旋转角度
                    setRotation(prev => prev + speed);
                    // 更新当前转速
                    setCurrentSpeed(speed);
                    // 更新时间
                    lastTime = currentTime;
                }

                animationRef.current = requestAnimationFrame(accelerate);
            };

            animationRef.current = requestAnimationFrame(accelerate);

            // 获取抽奖结果
            const result = await lotteryRequest();
            cleanupAnimations();

            // 如果中奖，则使用中奖的奖品id，否则使用未中奖的奖品id
            const prizeId = result?.isWin ? result.prize.prizeid : unPrizeId[0];
            const targetRotation = calculateTargetRotation(prizeId);

            // 开始的旋转角度
            const startRotation = rotation + 360;
            // 开始时间
            const startTime = performance.now();
            // 开始速度
            const startSpeed = currentSpeed;

            // 减速动画
            const decelerate = (currentTime: number) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / WHEEL_CONFIG.SPIN_DURATION, 1);
                const easeOut = (t: number) => 1 - Math.pow(1 - t, 3);

                const currentRotation = startRotation + (targetRotation - startRotation) * easeOut(progress);
                const newSpeed = startSpeed * (1 - easeOut(progress));

                setCurrentSpeed(newSpeed);
                setRotation(currentRotation);

                if (progress < 1) {
                    animationRef.current = requestAnimationFrame(decelerate);
                } else {
                    cleanupAnimations();
                    setIsSpinning(false);
                    setCurrentSpeed(0);
                    setRotation(0);
                    setPrizeInfo(result);
                    onClose();
                    setPrizeInfoModalVisible(true);
                }
            };

            animationRef.current = requestAnimationFrame(decelerate);
        } catch (error) {
            if (error && typeof error === 'object' && 'errno' in error && error.errno === 60003) {
                message.info('完成当前任务仅可抽奖1次，抽奖次数已达上限，快去做其他任务吧');
            } else {
                message.error('抽奖系统繁忙，请稍后再试');
                console.error('抽奖出错:', error);
                setIsSpinning(false);
            }

            cleanupAnimations();

            setCurrentSpeed(0);
            setRotation(0);
        }
    }, [
        isSpinning,
        setIsSpinning,
        setCurrentSpeed,
        cleanupAnimations,
        animationRef,
        lotteryRequest,
        unPrizeId,
        calculateTargetRotation,
        rotation,
        currentSpeed,
        setRotation,
        setPrizeInfo,
        onClose,
        setPrizeInfoModalVisible,
    ]);

    return (
        <Container>
            <div
                className="mx-auto mt-4 h-[53px] w-[340px]"
                style={{
                    backgroundImage: `url(${prizeTitleUrl})`,
                    backgroundSize: '100%',
                    backgroundRepeat: 'no-repeat',
                }}
            >
                <div
                    className="mx-auto text-center text-sm text-white"
                    style={{
                        marginTop: '4.7rem',
                    }}
                >
                    点击抽奖，好礼优享
                </div>
            </div>
            {loading ? (
                <WheelContainer>
                    <div className="absolute left-0 top-0 z-10 flex h-full w-full flex-col items-center justify-center">
                        <Spin />
                        <span className="mt-3 text-sm text-white">奖品加载中...</span>
                    </div>
                </WheelContainer>
            ) : (
                <WheelContainer>
                    <Wheel
                        ref={wheelRef}
                        viewBox="0 0 100 100"
                        style={{
                            transform: `rotate(${rotation}deg)`,
                            transition: isSpinning ? 'none' : 'transform 3s ease-out',
                        }}
                    >
                        {sectors.map(item => {
                            const startAngle = item.angle ?? 0;
                            const endAngle = startAngle + angle;
                            const x1 = 50 + 50 * Math.cos((Math.PI * startAngle) / 180);
                            const y1 = 50 + 50 * Math.sin((Math.PI * startAngle) / 180);
                            const x2 = 50 + 50 * Math.cos((Math.PI * endAngle) / 180);
                            const y2 = 50 + 50 * Math.sin((Math.PI * endAngle) / 180);

                            // 计算文本位置（在扇形区域中心）
                            const textAngle = startAngle + angle / 2; // 扇形中心角度
                            const textRadius = 40; // 文本距离中心的距离
                            const textX = 50 + textRadius * Math.cos((Math.PI * textAngle) / 180);
                            const textY = 50 + textRadius * Math.sin((Math.PI * textAngle) / 180);
                            const rotation = textAngle + 90; // 加90度使文字垂直于半径

                            return (
                                <g key={item.prizeid}>
                                    <path
                                        d={`M50,50 L${x1},${y1} A50,50 0 0,1 ${x2},${y2} Z`}
                                        fill={item.color}
                                        stroke="#39468c"
                                        strokeWidth="0.5"
                                    />
                                    <g mask={`url(#sector-mask-${item.prizeid})`}>
                                        <image
                                            x={textX - (sectors.length === 4 ? 12 : sectors.length === 6 ? 10 : 7.5)}
                                            y={textY + 1}
                                            height={sectors.length === 4 ? '23' : sectors.length === 6 ? '17' : '15'}
                                            width={sectors.length === 4 ? '23' : sectors.length === 6 ? '20' : '15'}
                                            href={item.icon}
                                            preserveAspectRatio="xMidYMid meet"
                                            transform={`rotate(${rotation}, ${textX}, ${textY})`}
                                        />
                                        <text
                                            x={textX}
                                            y={textY + (sectors.length === 4 ? -2 : sectors.length === 6 ? -2 : -2)}
                                            fill="#215EF6"
                                            fontSize={sectors.length === 4 ? '5' : sectors.length === 6 ? '4.5' : '4'}
                                            fontWeight="bold"
                                            textAnchor="middle"
                                            dominantBaseline="middle"
                                            transform={`rotate(${rotation}, ${textX}, ${textY})`}
                                        >
                                            {item.name}
                                        </text>
                                    </g>
                                    {/* 定义扇形遮罩 目的是实现svg的overflow:hidden */}
                                    <mask id={`sector-mask-${item.prizeid}`}>
                                        <path d={`M50,50 L${x1},${y1} A50,50 0 0,1 ${x2},${y2} Z`} fill={item.color} />
                                    </mask>
                                </g>
                            );
                        })}
                    </Wheel>
                    <CenterButton />
                </WheelContainer>
            )}
            <StyledButton onClick={spinWheel} isSpinning={isSpinning || loading} />
        </Container>
    );
};

export default LotteryWheel;
