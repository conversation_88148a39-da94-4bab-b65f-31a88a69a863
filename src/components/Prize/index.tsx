/* eslint-disable complexity */
/* eslint-disable max-depth */
// 抽奖/详情弹窗ContextProvider
import React, {createContext, useState, useCallback, useRef} from 'react';
import {TaskIdInfo, LogisticsInfo, AuditStatus, PrizeType, PrizeSType, ReceiveStatus} from '@/api/task/interface';
import taskApi from '@/api/task';
import {useUserAccountInfo} from '@/components/Login/hooks/useUserAccountInfo';
import {useUpdateAccountInfo} from '@/components/Login/hooks/useUpdateAccountInfo';
import {LotteryModal} from './LotteryModal';
import {PrizeDetailModal} from './PrizeDetailModal';
import {PrizeRegisterModal} from './PrizeRegisterModal';
import {AuditModal} from './AuditModal';
import {AuthVerificationModal} from './AuthVerificationModal';

// Context 类型
export interface LotteryContextType {
    // 打开抽奖弹窗
    openLottery: (taskIdInfo: TaskIdInfo | null) => void;
    // 关闭抽奖弹窗
    closeLottery: () => void;
    // 打开审核信息弹窗
    openAuditModal: (LogisticsInfo: LogisticsInfo | null) => void;
    // 关闭审核信息弹窗
    closeAuditModal: () => void;
    // 打开奖品详情弹窗
    openPrizeDetail: (taskIdInfo: TaskIdInfo | null) => void;
    // 关闭奖品详情弹窗
    closePrizeDetail: () => void;
    // 打开奖品登记弹窗
    openPrizeRegister: (taskIdInfo: TaskIdInfo | null, LogisticsInfo: LogisticsInfo | null) => void;
    // 关闭奖品登记弹窗
    closePrizeRegister: () => void;
    // 刷新任务记录
    refreshTaskRecord?: () => void;
    // 设置刷新任务中心
    setRefreshTaskCenter?: (fn: () => void) => void;
    // 刷新任务中心
    refreshTaskCenter?: () => void;
}

export const LotteryContext = createContext<LotteryContextType>({
    openLottery: () => {},
    closeLottery: () => {},
    openAuditModal: () => {},
    closeAuditModal: () => {},
    openPrizeDetail: () => {},
    closePrizeDetail: () => {},
    openPrizeRegister: () => {},
    closePrizeRegister: () => {},
    refreshTaskRecord: () => {},
    setRefreshTaskCenter: () => {},
    refreshTaskCenter: () => {},
});

// Provider 组件
export function LotteryProvider({children}: {children: React.ReactNode}) {
    const [lotteryModalState, setLotteryModalState] = useState<boolean>(false);

    const [prizeDetailModalState, setPrizeDetailModalState] = useState<boolean>(false);

    const [prizeRegisterModalState, setPrizeRegisterModalState] = useState<boolean>(false);

    // 审核状态提示弹窗
    const [auditModalOpen, setAuditModalOpen] = useState<boolean>(false);
    // 真实性认证引导弹窗
    const [authVerificationModalOpen, setAuthVerificationModalOpen] = useState<boolean>(false);

    const [currentPrizeIDDetail, setCurrentPrizeIDDetail] = useState<TaskIdInfo | null>(null);
    const [currentPrizeDetail, setCurrentPrizeDetail] = useState<LogisticsInfo | null>(null);

    useUpdateAccountInfo();
    const {
        accountInfo,
        isPersonal,
        isOrgExceptEnterprise,
        isPersonAuthPass,
        isOrgAuthPass,
        isOrgAuthExempt,
        isOrgQualifyPass,
    } = useUserAccountInfo();

    const refreshTaskCenter = useRef<() => void>(() => {});

    // 抽奖弹窗
    const openLottery = useCallback((taskIdInfo: TaskIdInfo | null) => {
        setCurrentPrizeIDDetail(taskIdInfo);
        setLotteryModalState(true);
    }, []);

    const closeLottery = useCallback(() => {
        setLotteryModalState(false);
        refreshTaskCenter.current();
    }, [refreshTaskCenter]);

    // 审核状态弹窗
    const openAuditModal = useCallback((logisticsInfo: LogisticsInfo | null) => {
        setCurrentPrizeDetail(logisticsInfo);
        setAuditModalOpen(true);
    }, []);

    const closeAuditModal = useCallback(() => {
        setAuditModalOpen(false);
    }, []);

    const closeAuthVerification = useCallback(() => {
        setAuthVerificationModalOpen(false);
    }, []);

    // 奖品详情弹窗
    const openPrizeDetail = useCallback(
        async (taskIdInfo: TaskIdInfo | null) => {
            try {
                const logisticsInfo = await taskApi.getLogisTics({
                    subTaskId: taskIdInfo?.subTaskId,
                    taskPageId: taskIdInfo?.taskPageId || '',
                    taskPkgId: taskIdInfo?.taskPkgId || '',
                });

                /**
                 * 判断当前用户是否进行真实性认证/主体认证
                 * @returns 如果认证通过，则返回 true；否则返回 false
                 */
                const hasAuth = () => {
                    if (isPersonal) {
                        return isPersonAuthPass;
                    } else if (isOrgExceptEnterprise) {
                        return isOrgQualifyPass;
                    } else {
                        return isOrgAuthPass && (isOrgAuthExempt || isOrgQualifyPass);
                    }
                };

                // 需要真实性/实名认证，打开认证弹窗
                if (!hasAuth()) {
                    setAuthVerificationModalOpen(true);
                    return;
                }

                // 卡券类
                if (logisticsInfo?.prize.type === PrizeType.VIRTUAL) {
                    // 第三方卡券
                    if (logisticsInfo?.prize.sType === PrizeSType.THIRD_PARTY_COUPON) {
                        if (
                            logisticsInfo?.receiveStatus === ReceiveStatus.WAIT_RECEIVE ||
                            logisticsInfo?.receiveStatus === ReceiveStatus.NO_NEED
                        ) {
                            // 跳转第三方领取
                            window.open(logisticsInfo?.prize.url);
                        } else {
                            // 已领取，展示奖品详情
                            setCurrentPrizeIDDetail(taskIdInfo);
                            setPrizeDetailModalState(true);
                        }
                    } else if (logisticsInfo?.auditStatus === AuditStatus.AUDIT_PASS && logisticsInfo?.prizeAccount) {
                        // 审核通过，打开奖品详情
                        setCurrentPrizeIDDetail(taskIdInfo);
                        setPrizeDetailModalState(true);
                    } else {
                        // 审核未通过 or 审核拒绝，打开审核提示弹窗
                        setAuditModalOpen(true);
                        setCurrentPrizeDetail(logisticsInfo);
                    }
                }

                // 实物类
                if (logisticsInfo?.prize.type === PrizeType.PHYSICAL) {
                    if (
                        logisticsInfo?.auditStatus === AuditStatus.AUDIT_PASS ||
                        logisticsInfo?.auditStatus === AuditStatus.WAIT_AUDIT
                    ) {
                        // 审核通过，打开奖品详情
                        setCurrentPrizeIDDetail(taskIdInfo);
                        setPrizeDetailModalState(true);
                    } else {
                        // 审核拒绝，打开审核提示弹窗
                        setAuditModalOpen(true);
                        setCurrentPrizeDetail(logisticsInfo);
                    }
                }
            } catch (e) {
                throw e;
            }
        },
        [isPersonal, isPersonAuthPass, isOrgAuthPass, isOrgAuthExempt, isOrgQualifyPass, isOrgExceptEnterprise]
    );

    const closePrizeDetail = useCallback(() => {
        setPrizeDetailModalState(false);
        refreshTaskCenter.current();
    }, [refreshTaskCenter]);

    // 奖品登记弹窗
    const openPrizeRegister = useCallback((taskIdInfo: TaskIdInfo | null, logisticsInfo: LogisticsInfo | null) => {
        setCurrentPrizeIDDetail(taskIdInfo);
        setCurrentPrizeDetail(logisticsInfo);
        setPrizeRegisterModalState(true);
    }, []);

    const closePrizeRegister = useCallback(() => {
        setPrizeRegisterModalState(false);
        setPrizeDetailModalState(false);
        setCurrentPrizeDetail(null);
        refreshTaskCenter.current();
    }, [refreshTaskCenter]);

    const setRefreshTaskCenter = useCallback((fn: () => void) => {
        refreshTaskCenter.current = fn;
    }, []);

    return (
        <LotteryContext.Provider
            value={{
                openLottery,
                closeLottery,
                openAuditModal,
                closeAuditModal,
                openPrizeDetail,
                closePrizeDetail,
                openPrizeRegister,
                closePrizeRegister,
                setRefreshTaskCenter,
            }}
        >
            {children}
            <LotteryModal visible={lotteryModalState} onClose={closeLottery} taskIdInfo={currentPrizeIDDetail} />
            {/* 奖品审核信息弹窗 */}
            <AuditModal open={auditModalOpen} onClose={closeAuditModal} logisticsInfo={currentPrizeDetail} />
            {/* 真实性认证引导弹窗 */}
            <AuthVerificationModal
                open={authVerificationModalOpen}
                onClose={closeAuthVerification}
                accountInfo={accountInfo}
            />
            {/* 奖品详情弹窗 */}
            <PrizeDetailModal
                visible={prizeDetailModalState}
                onClose={closePrizeDetail}
                taskIdInfo={currentPrizeIDDetail}
            />
            {/* 奖品登记弹窗 */}
            <PrizeRegisterModal
                visible={prizeRegisterModalState}
                onClose={closePrizeRegister}
                taskIdInfo={currentPrizeIDDetail}
                logisticsInfo={currentPrizeDetail}
            />
        </LotteryContext.Provider>
    );
}
