// 抽奖弹窗

import {Modal} from 'antd';
import styled from '@emotion/styled';
import {v4 as uuidv4} from 'uuid';
import {useCallback, useEffect, useState} from 'react';
import {DrawPrize, PrizesItem, TaskIdInfo} from '@/api/task/interface';
import api from '@/api/task';
import LotteryWheel from './Lottery';
import PrizeInfoModal from './PrizeInfoModal';
interface LotteryModalProps {
    visible: boolean;
    onClose: () => void;
    taskIdInfo: TaskIdInfo | null;
}

export const StyledModal = styled(Modal)`
    .ant-modal-content {
        background-color: transparent !important;
        box-shadow: none !important;
        padding: 0 !important;
    }
    .ant-modal-close {
        top: 4px !important;
        right: 4px !important;
        color: #fff !important;
        border-radius: 50% !important;
        border: 1px solid #fff !important;
    }
`;

export interface Sector extends PrizesItem {
    color?: string;
    angle?: number;
}

// 弹窗组件
export function LotteryModal({visible, onClose, taskIdInfo}: LotteryModalProps) {
    const [unPrizeId, setUnPrizeId] = useState<string[]>(['']);
    const [sectors, setSectors] = useState<Sector[]>([]);
    const [angle, setAngle] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    // 奖品信息弹窗
    const [prizeInfoModalVisible, setPrizeInfoModalVisible] = useState(false);
    const [prizeInfo, setPrizeInfo] = useState<DrawPrize | undefined>(undefined);

    const closePrizeInfoModal = useCallback(() => {
        setPrizeInfoModalVisible(false);
        onClose();
        setPrizeInfo(undefined);
    }, [onClose]);

    useEffect(() => {
        if (visible && taskIdInfo) {
            setLoading(true);
            api.drawPageInfo(taskIdInfo).then(res => {
                const prize = res.prizes;
                // 奇数个奖品
                const isOdd = prize.length % 2 === 1;
                // 奇数需要添加一个未中奖，偶数添加两个未中奖
                let value: Sector[] = [];
                if (isOdd) {
                    const unPrizeId = uuidv4();
                    const unPrize: PrizesItem = {name: '谢谢参与', prizeid: unPrizeId};
                    setUnPrizeId([unPrizeId]);
                    value = [...prize, unPrize];
                } else {
                    const [unPrizeId1, unPrizeId2] = [uuidv4(), uuidv4()];
                    const unPrize1: PrizesItem = {name: '谢谢参与', prizeid: unPrizeId1};
                    const unPrize2: PrizesItem = {name: '谢谢参与', prizeid: unPrizeId2};
                    setUnPrizeId([unPrizeId1, unPrizeId2]);
                    // 在奖品的对称位置添加两个未中奖，确保不相邻
                    const halfLength = Math.floor(prize.length / 2);
                    const newPrize = [unPrize1, ...prize.slice(0, halfLength), unPrize2, ...prize.slice(halfLength)];

                    value = newPrize;
                }

                setAngle(360 / value.length);

                for (let i = 0; i < value.length; i++) {
                    if (i % 2 === 0) {
                        value[i].color = '#d1edfd';
                    } else {
                        value[i].color = '#f3fafe';
                    }

                    value[i].angle = i * angle + 90;
                }

                setSectors(value);
                setLoading(false);
            });
        }
    }, [angle, taskIdInfo, visible]);

    return (
        <>
            <StyledModal
                open={visible}
                onCancel={onClose}
                footer={null}
                destroyOnClose
                width={400}
                centered
                maskClosable={false}
            >
                <LotteryWheel
                    loading={loading}
                    sectors={sectors}
                    unPrizeId={unPrizeId}
                    angle={angle}
                    taskIdInfo={taskIdInfo}
                    onClose={onClose}
                    setPrizeInfo={setPrizeInfo}
                    setPrizeInfoModalVisible={setPrizeInfoModalVisible}
                />
            </StyledModal>
            <PrizeInfoModal
                prizeInfo={prizeInfo}
                visible={prizeInfoModalVisible}
                onClose={closePrizeInfoModal}
                taskIdInfo={taskIdInfo}
            />
        </>
    );
}
