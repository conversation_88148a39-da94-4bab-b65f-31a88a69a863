/**
 * @file 奖品审核信息弹窗
 * @description 通过奖品审核状态判断，未审核&审核不通过时，在展示奖品详情弹窗前，展示该弹窗
 * <AUTHOR>
 * @date 2025-06-4
 */

import {Button, message, Modal} from 'antd';
import {ModalProps} from 'antd/lib';
import {useCallback, useMemo} from 'react';
import {LogisticsInfo, AuditStatus} from '@/api/task/interface';

interface AuditModalProps extends ModalProps {
    onClose: () => void;
    logisticsInfo: LogisticsInfo | null;
}

export function AuditModal(props: AuditModalProps) {
    const {onClose, logisticsInfo, ...Modalrops} = props;

    const copyEmail = useCallback(() => {
        navigator.clipboard
            .writeText('<EMAIL>')
            .then(() => {
                message.success('邮箱已复制至剪切板');
            })
            .catch(() => {
                message.error('复制失败');
            });
    }, []);
    const auditReject = useMemo(() => {
        return logisticsInfo?.auditStatus === AuditStatus.AUDIT_REJECT;
    }, [logisticsInfo]);

    const waitAudit = useMemo(() => {
        return !auditReject && (logisticsInfo?.auditStatus === AuditStatus.WAIT_AUDIT || !logisticsInfo?.prizeAccount);
    }, [logisticsInfo, auditReject]);

    return (
        <Modal
            {...Modalrops}
            width={442}
            onCancel={onClose}
            centered
            title={logisticsInfo?.auditStatus === AuditStatus.WAIT_AUDIT ? '领取奖品' : '温馨提示'}
            footer={
                <div className="flex justify-end" onClick={onClose}>
                    <Button className="rounded-full" type="primary">
                        知道了
                    </Button>
                </div>
            }
        >
            {waitAudit && (
                <span>
                    为保证顺利兑奖，系统审核通过后将以短信形式向您发放卡券兑换码，可在任务记录中查看。审核结果预计1-2个工作日内。
                </span>
            )}
            {auditReject && (
                <>
                    <span>
                        很抱歉您的中奖信息没有通过系统审核，无法兑换 如有疑问请联系官方邮箱*********************
                    </span>
                    <span className="ml-2 cursor-pointer text-primary" onClick={copyEmail}>
                        <span className="iconfont icon-copy"></span>复制
                    </span>
                </>
            )}
        </Modal>
    );
}
