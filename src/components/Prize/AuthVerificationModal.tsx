/**
 * @file 真实性认证引导弹窗
 * @description 领取奖品前，进行真实性认证，真实性认证未通过展示该弹窗
 * <AUTHOR>
 * @date 2025-06-4
 */

import {Button, Modal} from 'antd';
import {ModalProps} from 'antd/lib';
import {useCallback} from 'react';
import {AccountQualifyType, AccountInfo} from '@/api/account/interface';
import urls from '@/links';
import {PASS_AUTH_HOST} from '@/modules/account/pc/Org/components/config';

interface AuditModalProps extends ModalProps {
    onClose: () => void;
    accountInfo: AccountInfo | null;
}

export function AuthVerificationModal(props: AuditModalProps) {
    const {onClose, accountInfo, ...Modalrops} = props;

    const handleVerification = useCallback(() => {
        // 主体为个人，判断是否完成真实性认证
        if (accountInfo?.accountType === AccountQualifyType.Person) {
            window.open(PASS_AUTH_HOST);
        } else {
            window.open(urls.accountOrgAuth.raw());
        }

        onClose();
    }, [accountInfo, onClose]);

    return (
        <Modal
            {...Modalrops}
            width={442}
            onCancel={onClose}
            centered
            title="真实性认证"
            footer={
                <div className="flex justify-end gap-2">
                    <Button
                        className="h-[30px] rounded-full border-[#EDEEF0] font-medium"
                        type="default"
                        onClick={onClose}
                    >
                        取消
                    </Button>
                    <Button className="h-[30px] rounded-full font-medium" type="primary" onClick={handleVerification}>
                        去认证
                    </Button>
                </div>
            }
        >
            为保障您的权益，请在兑奖前核对您的真实性身份，仅需30s，快去认证吧！
        </Modal>
    );
}
