/**
 * @file tab页面切换按钮组
 * <AUTHOR>
 * @update <EMAIL> 抽取为公共组件
 */

import styled from '@emotion/styled';
import {Radio} from 'antd';

const TabRadioGroup = styled(Radio.Group)`
    background: #eceef3;
    color: #50525c;
    border-radius: 30px !important;
    padding: 2px !important;
    .ant-radio-button-wrapper {
        border-radius: 54px !important;
        border: none !important;
        background: #eceef3;
        // radio-button默认四字的宽度96px
        width: 96px;
        text-align: center;
        line-height: 32px;
    }
    .ant-radio-button-wrapper-checked {
        border: none !important;
        border-radius: 54px !important;
        font-weight: 500;
        background-color: #ffffff !important;
    }
    ant-radio-button-wrapper-checked::before {
        background-color: #ffffff !important;
    }
    .ant-radio-button-wrapper:not(:first-of-type)::before {
        width: 0px !important;
    }
`;

export default TabRadioGroup;
