/**
 * @file 头像组件
 * <AUTHOR>
 */

import React, {useCallback, useRef, useState} from 'react';
import {Avatar, Dropdown, Button, DropdownProps} from 'antd';
import {Link} from 'react-router-dom';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import defaultAvatar from '@/assets/default-usert-avatar.png';
import urls from '@/links';
import {LoginSource} from '@/utils/loggerV2/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/index';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import LogoutWarningModal, {LogoutWarningModalRef} from '../Login/LogoutModal';

const UserAvatar = () => {
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);

    const [dropdownOpen, setDropdownOpen] = useState(false);
    const {clickLog} = useUbcLogV3();

    const logoutRef = useRef<LogoutWarningModalRef>(null);

    const logoutConfirm = useCallback(() => {
        logoutRef?.current?.logoutClickHandler();
        setDropdownOpen(false);
    }, []);

    // 弹窗开启时关闭下拉框
    const handleOpenChange: DropdownProps['onOpenChange'] = useCallback((dropdownOpen: boolean) => {
        setDropdownOpen(dropdownOpen);
    }, []);

    const uniformLogin = useUniformLogin(LoginSource.LOGIN_ENTRANCE);
    const uniformButtonLogin = useUniformLogin(LoginSource.LOGIN_ENTRANCE);
    const handleClick = useCallback(() => {
        uniformLogin();
        // 侧边栏登陆按钮打点
        clickLog(EVENT_VALUE_CONST.SIDEBAR_LOGIN);
    }, [uniformLogin, clickLog]);

    const handleAuthorizeManagement = useCallback(() => {
        window.location.href = `${location.origin}/authorize/management`;
        setDropdownOpen(false);
    }, []);

    const dropdown = useCallback(() => {
        return (
            <div className="text-[16px] font-medium">
                <div className="z-10 flex w-fit min-w-[87px] flex-col justify-center rounded-[9px] bg-white px-[4px] py-[4px] shadow-[0_0px_40px_0px_rgba(29,34,82,0.2)]">
                    {/* 超级权限下不展示资质认证入口 */}
                    {!userInfoData?.userInfo.hasEnhancedAccess && (
                        <Link
                            to={urls.account.raw()}
                            className="flex h-[38px] items-center rounded-[6px] px-[10px] leading-[38px] text-black hover:bg-colorAgentItemHoverBg"
                        >
                            <span>账号信息</span>
                        </Link>
                    )}
                    {userInfoData?.hasTpProxy && (
                        <div
                            onClick={handleAuthorizeManagement}
                            className="flex h-[38px] cursor-pointer items-center rounded-[6px] px-[10px] leading-[38px] text-black hover:bg-colorAgentItemHoverBg"
                        >
                            <span className="text-black-base">授权管理</span>
                        </div>
                    )}
                    <div
                        className="h-[38px] cursor-pointer rounded-[6px] px-[10px] leading-[38px] hover:bg-colorAgentItemHoverBg"
                        onClick={logoutConfirm}
                    >
                        <span className="text-black-base">退出登录</span>
                    </div>
                </div>
            </div>
        );
    }, [handleAuthorizeManagement, logoutConfirm, userInfoData?.hasTpProxy, userInfoData?.userInfo.hasEnhancedAccess]);
    return (
        <>
            <Dropdown
                dropdownRender={dropdown}
                onOpenChange={handleOpenChange}
                open={dropdownOpen}
                disabled={!isLogin}
                placement="topRight"
            >
                <div
                    className={`z-20 flex h-12 w-full cursor-pointer items-center ${
                        !isLogin && 'justify-between'
                    } gap-2 rounded-[9px] px-1 text-sm font-medium hover:bg-gray-border-secondary/40`}
                    onClick={isLogin ? undefined : handleClick}
                >
                    <div className="flex items-center">
                        <Avatar
                            src={userInfoData?.userInfo.portrait || defaultAvatar}
                            size={36}
                            className="mr-2 flex-shrink-0 border-none"
                        />
                        {isLogin ? (
                            <span className="float-right inline-block overflow-hidden overflow-ellipsis whitespace-nowrap text-[16px] font-medium">
                                {userInfoData?.userInfo.name || ''}
                            </span>
                        ) : (
                            <span className="text-gray-secondary" style={{whiteSpace: 'nowrap'}}>
                                立即体验
                            </span>
                        )}
                    </div>
                    {!isLogin && (
                        <Button
                            type="primary"
                            className="login-btn h-[30px] w-[52px] rounded-full p-0 text-center font-bold"
                            onClick={uniformButtonLogin}
                        >
                            登录
                        </Button>
                    )}
                    {userInfoData?.userInfo.hasEnhancedAccess && (
                        <div className="ml-auto h-[18px] whitespace-nowrap rounded bg-gray-border-secondary px-1 text-xs font-medium leading-[18px] text-[#787B94]">
                            代理
                        </div>
                    )}
                </div>
            </Dropdown>
            <LogoutWarningModal ref={logoutRef} />
        </>
    );
};

export default UserAvatar;
