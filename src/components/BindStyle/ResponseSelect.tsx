/**
 * @file 变量选择器
 * <AUTHOR>
 */
import {Tag, Tooltip, TreeSelect} from 'antd';
import React, {Key, useCallback, useEffect, useState} from 'react';
import type {BaseOptionType} from 'antd/lib/select';
import {css} from '@emotion/css';
import classNames from 'classnames';
import {FormItemComponentProps, TreeSelectNodeData, VariableType} from '@/components/BindStyle/interface';
import {isBasicDataType, isMatch} from '@/components/BindStyle/utils';
import {getPopupContainer} from '@/utils/getPopupContainer';

const customTreeSelectStyle = css`
    .ant-select-selection-item {
        .custom-tree-node-title .tag {
            display: none;
        }
    }
`;

const customTreeNodeStyle = css`
    padding: 4px 0 !important;
    &:hover {
        background: #eceef366;
    }
    &.ant-select-tree-treenode-selected {
        background: #1a46ff14 !important;
    }
    .ant-select-tree-node-content-wrapper {
        color: rgba(0, 0, 0, 0.85) !important;
        padding: 0 !important;
    }
    &.ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
        opacity: 0.4;
    }
    .ant-select-tree-node-content-wrapper:hover {
        background: transparent !important;
    }
    .ant-select-tree-node-selected {
        background: transparent !important;
        border-radius: 0 !important;
    }
    .ant-select-tree-switcher {
        width: 12px !important;
        margin-right: 8px !important;
        margin-left: 12px !important;
        transition: transform 0.1s;

        &.ant-select-tree-switcher_close {
            transform: rotate(-90deg);
        }
    }
`;

interface ResponseSelectProps extends FormItemComponentProps {
    treeData?: TreeSelectNodeData[];
}

function TreeNodeTitle({title, tips, type}: {title: string; tips: string; type: string}) {
    return (
        <div className="custom-tree-node-title flex items-center">
            <Tooltip overlayInnerStyle={{pointerEvents: 'none'}} title={tips}>
                <span>{title}</span>
            </Tooltip>
            {!isBasicDataType(type) && (
                <Tag
                    className="tag ml-2 bg-gray-border-secondary px-[8px] py-[1px] text-sm text-[#787B94]"
                    bordered={false}
                >
                    {type}
                </Tag>
            )}
        </div>
    );
}

const getTreeNode = (data?: TreeSelectNodeData[], selectType?: VariableType) => {
    if (data?.length) {
        return data.map((item: TreeSelectNodeData) => {
            // 增加类型判断，类型不一致时不可选
            const isDisabled = item.disabled || !isMatch(item.type, selectType);

            return (
                <TreeSelect.TreeNode
                    className={customTreeNodeStyle}
                    key={item.value}
                    title={<TreeNodeTitle title={item.title} tips={item.tips} type={item.type} />}
                    value={item.value}
                    disabled={isDisabled}
                >
                    {getTreeNode(item.children, selectType)}
                </TreeSelect.TreeNode>
            );
        });
    }
    return [];
};

export default function ResponseSelect({options, treeData, placeholder, disabled, onChange}: ResponseSelectProps) {
    const {name, type, value} = options;

    // 展开的节点
    const [treeExpandedKeys, setTreeExpandedKeys] = useState<Key[]>(['']);

    const onTreeExpand = useCallback((expandedKeys: Key[]) => {
        setTreeExpandedKeys(expandedKeys);
    }, []);

    // 用户选中时，更新数据
    const onSelectChange = useCallback(
        (newValue: string) => {
            onChange?.(newValue, name);
        },
        [name, onChange]
    );

    const filterTreeNode = useCallback((input: string, option?: BaseOptionType) => {
        if (!option) {
            return false;
        }
        return (option?.title?.props?.title ?? '').toLowerCase().includes(input.toLowerCase());
    }, []);

    useEffect(() => {
        // 初始化时默认展开到二级节点
        setTreeExpandedKeys(treeData?.map((item: TreeSelectNodeData) => item.value) || []);
    }, [treeData]);

    return (
        <TreeSelect
            value={value}
            showSearch
            allowClear
            disabled={disabled}
            treeExpandedKeys={treeExpandedKeys}
            // 将选择框后缀图标由搜索改为下拉
            switcherIcon={<span className="iconfont icon-expand1 pointer-events-none text-black" />}
            suffixIcon={<span className="iconfont icon-Down pointer-events-none text-black" />}
            className={classNames('nodrag w-full border-gray-200', customTreeSelectStyle)}
            popupClassName="nodrag nowheel py-0 px-0 rounded-md"
            placeholder={placeholder || '请选择'}
            filterTreeNode={filterTreeNode}
            onChange={onSelectChange}
            onTreeExpand={onTreeExpand}
            getPopupContainer={getPopupContainer}
        >
            {getTreeNode(treeData, type)}
        </TreeSelect>
    );
}
