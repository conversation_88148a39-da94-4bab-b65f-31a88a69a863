/**
 * @file 输入框组件
 * <AUTHOR>
 */
import {Input as InputComponent} from 'antd';
import {useCallback} from 'react';
import {FormItemComponentProps} from '@/components/BindStyle/interface';
import {getCountConfig} from '@/utils/text';

export interface InputProps extends FormItemComponentProps {
    options: FormItemComponentProps['options'] & {minLength?: number; maxLength?: number};
}

export default function Input({options, placeholder = '请输入', disabled, onChange}: InputProps) {
    const {name, value, maxLength} = options;

    const onInputChange = useCallback(
        (e: any) => {
            onChange?.(e.target.value, name);
        },
        [onChange, name]
    );

    return (
        <InputComponent
            value={value}
            placeholder={placeholder}
            disabled={disabled}
            onChange={onInputChange}
            count={maxLength ? getCountConfig(maxLength, true) : undefined}
        />
    );
}
