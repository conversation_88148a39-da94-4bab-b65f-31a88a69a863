/**
 * @file 跳转链接表单
 * <AUTHOR>
 */
import {useEffect, useMemo, useState} from 'react';
import {FormItemProps} from 'antd';
import ItemsContainer from '@/components/BindStyle/ItemsContainer';
import {LinkEventsForm, LinkLabel} from '@/components/BindStyle/const';
import FormItem from '@/components/BindStyle/FormItem';
import {FormType, StyleFormData, TreeSelectNodeData, VariableType} from '@/components/BindStyle/interface';
import {FeatureName} from '@/api/agentEditV2/interface';
import {getFeatureAccess} from '@/api/agentEditV2';

export default function LinkForm({
    treeData,
    disabled,
    formData,
    dependencies,
}: {
    treeData?: TreeSelectNodeData[];
    disabled?: boolean;
    formData: StyleFormData;
    dependencies?: FormItemProps['dependencies'];
}) {
    const [hasAuth, setHasAuth] = useState(false);

    const enableSwanFormItemOptions = useMemo(
        () => ({
            name: 'component.events.click.enableSwan',
            formType: FormType.Switch,
            type: VariableType.Boolean,
        }),
        []
    );

    useEffect(() => {
        (async () => {
            try {
                const {inside_url: accessRes} = await getFeatureAccess({featureName: FeatureName.InsideUrl});
                setHasAuth(!!accessRes);
            } catch (err) {
                throw err;
            }
        })();
    }, []);

    return (
        <div>
            <ItemsContainer className="mt-6" title="设置卡片跳转链接" required>
                {/* TODO: 暂时没有定义 interface，先使用 any */}
                {(hasAuth
                    ? LinkEventsForm[0].items
                    : LinkEventsForm[0].items.filter(item => item.label !== LinkLabel.SCHEME_LINK)
                ).map((item: any) => {
                    item.treeData = treeData;

                    return (
                        <FormItem
                            className="mb-3"
                            key={item.name}
                            options={item}
                            formData={formData}
                            disabled={disabled}
                            dependencies={dependencies}
                        />
                    );
                })}
            </ItemsContainer>
            <ItemsContainer
                className="mt-6"
                title={
                    <div className="flex items-center">
                        <span className="mr-2">优先跳转小程序</span>
                        <FormItem
                            noStyle
                            options={enableSwanFormItemOptions}
                            formData={formData}
                            disabled={disabled}
                            dependencies={dependencies}
                        />
                    </div>
                }
                desc="配置后可在支持打开百度小程序的场景，优先跳转小程序"
            >
                {/* TODO: 暂时没有定义 interface，先使用 any */}
                {!!formData?.component?.events?.click?.enableSwan &&
                    LinkEventsForm[1].items.map((item: any) => {
                        item.treeData = treeData;

                        return (
                            <FormItem
                                className="mb-3"
                                key={item.name}
                                options={item}
                                formData={formData}
                                disabled={disabled}
                                dependencies={dependencies}
                            />
                        );
                    })}
            </ItemsContainer>
        </div>
    );
}
