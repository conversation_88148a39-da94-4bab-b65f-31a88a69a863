/**
 * @file 绑定样式弹窗
 * <AUTHOR>
 */
import {Form, Modal, Image, ConfigProvider} from 'antd';
import React, {useRef, useCallback, useEffect, useMemo, useState} from 'react';
import isEqual from 'lodash/isEqual';
import merge from 'lodash/merge';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {ResponseSchema, StyleBindInfo} from '@/components/BindStyle/interface';
import api from '@/api/pluginEdit/index';
import {PluginStyle, StyleUpdateTag} from '@/api/pluginEdit/interface';
import OfficialStyleForm from '@/components/BindStyle/OfficialStyleForm';
import Empty from '@/components/Empty';
import {formDataToUIMeta, getDefaultFormData} from '@/components/BindStyle/utils';
import {CustomStyleId, DefaultStyleId, MarkdownStyleId} from '@/components/BindStyle/const';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import NoBindStyleSrc from '@/assets/no-bind-style.png';
import {inputToken, selectToken} from '@/styles/component-token';
import Tag from '@/components/Tag';
import Loading from '@/components/Loading';
import CustomPopover from '@/components/Popover';

const StyledModal = styled(Modal)`
    &.ant-modal {
        min-width: 1200px !important;
        max-width: 1680px !important;
        width: calc(100vw - 240px) !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }
    .ant-modal-close {
        top: 21px !important;
        right: 21px !important;
    }
    .ant-modal-content {
        padding-left: 0px !important;
    }
    .ant-modal-footer {
        margin-top: 0 !important;
        padding-top: 24px !important;
    }
`;

const pluginConfirmModalStyle = css`
    .ant-modal-confirm-paragraph {
        max-width: 100% !important;
    }
`;

export interface BindStyleModalProps {
    // 开/关弹窗
    open: boolean;
    // 弹窗标题后显示的文本（工作流的节点名称或者插件的 operationId）
    titleSuffix?: string;
    // 要绑定样式的工具的响应数据结构
    response: ResponseSchema | null;

    /**
     * 仅插件绑定样式时需要传入该字段
     * 渲染 ui.json 文件时，传入 sdk 的数据的字段
     *
     * 如：response 结构为 {data: {locations: [{title: '标题'}]}}，xReturnRaw 为 data
     * 则渲染 ui.json 时传入 sdk 的数据为 {locations: [{title: '标题'}]}
     */
    xReturnRaw?: string;
    /**
     * 仅插件绑定样式时需要传入该字段
     * 如果 openapi.yaml 文件中没有配置该字段，则第一次选择某一个变量时将通过 setXReturnRaw 设置该字段
     */
    setXReturnRaw?: (raw: string) => void;
    // 要绑定样式的工具的当前配置的样式数据
    bindInfo?: StyleBindInfo;
    // 是否在使用自定义样式(自定义 ui.json 文件)
    hasCustomStyle?: boolean;
    hasMarkdownStyle?: boolean;
    // 弹窗关闭事件
    onCancel: () => void;
    // 弹窗确认事件，将绑定的配置数据传回父组件
    onConfirm: (bindInfo: StyleBindInfo) => void;
    pluginId?: string;
    onSwitchStyle?: (styleId: number) => void;
    isPluginStyleUpdate?: boolean;
    isLoading?: boolean;
}

function OfficialStyleCard({
    couldShowUpdate,
    shouldShowPopover,
    onCardClick,
    style,
    selected,
}: {
    /** 可以展示更新 Tag，具体是否展示取决于 updateTag 数据 */
    couldShowUpdate: boolean;
    /** 应该展示更新 Popover，已经由外部计算出来需要展示 */
    shouldShowPopover: boolean;
    onCardClick: (style: PluginStyle) => void;
    style: PluginStyle;
    selected: boolean;
}) {
    return (
        <div className="cursor-pointer" onClick={() => onCardClick(style)}>
            <CustomPopover
                type="primary"
                align={{offset: [8, 0]}}
                placement="right"
                open={couldShowUpdate && shouldShowPopover}
                content={
                    style.updateTag === StyleUpdateTag.Update ? '模板已更新至最新版' : '模板已升级至最新版，请更新配置~'
                }
            >
                <div
                    className={classNames(
                        'mb-[6px] w-full rounded-[9px] border-[1px] p-[2px] transition-transform duration-300 hover:scale-[1.1]',
                        {
                            'border-primary': selected,
                            'border-transparent': !selected,
                        }
                    )}
                >
                    <img className="h-[111px]" src={style.styleTemplateImage} />
                </div>
            </CustomPopover>
            <div className="px-[2px] text-sm text-gray-tertiary">
                <div className="flex items-center font-medium leading-[20px] text-black">
                    <span>{style.styleName}</span>
                    {style.updateTag !== StyleUpdateTag.NoUpdate && couldShowUpdate && (
                        <Tag color="success" className="ml-[6px]">
                            NEW
                        </Tag>
                    )}
                </div>
                <div className="mt-[3px] leading-[22px]">{style.styleDesc}</div>
            </div>
        </div>
    );
}

const EmptyStyleImage = () => (
    <Empty
        img={<span className="iconfont icon-visual-disable text-[90px] leading-none text-[#DDDDF1]" />}
        desc={<span className="pt-[2px] leading-[24px] text-gray-tertiary">暂无可配置和预览的内容</span>}
    />
);

const CustomStylePreview = () => (
    <>
        <div className="mt-4 pl-[24px] pr-[64px]">
            <div className="flex rounded-[9px] bg-[#ECEFFE] px-[15px] py-[9px]">
                <div>
                    <span className="iconfont icon-tip-fill text-[#4F6FF9]" />
                </div>

                <span className="ml-2 text-black">
                    <>
                        通过ui.json配置样式暂无预览内容；若选择官方模板，则原样式失效。ui.json已升级为markdown，欢迎查看
                        <a
                            href={
                                'https://agents.baidu.com/docs/develop/plugin/ability-plugin/advanced/markdownConfig/'
                            }
                            target="_blank"
                            rel="noreferrer"
                        >
                            官方文档
                        </a>
                        自定义样式，或者选择官方模板配置样式~
                    </>
                </span>
            </div>
        </div>
        <EmptyStyleImage />
    </>
);

const MarkdownStylePreview = () => (
    <>
        <div className="mt-4 flex justify-center">
            <div className="flex rounded-[9px] bg-[#ECEFFE] px-[15px] py-[9px]">
                <div>
                    <span className="iconfont icon-tip-fill text-[#4F6FF9]" />
                </div>

                <span className="ml-2 text-black">
                    通过markdown配置样式暂无预览内容；若选择官方模板，则原样式失效。
                </span>
            </div>
        </div>
        <EmptyStyleImage />
    </>
);

const PluginStyleUpdateToast = ({onCloseToast}: {onCloseToast: () => void}) => {
    const closeToast = useCallback(() => {
        onCloseToast();
    }, [onCloseToast]);

    return (
        <div className="ml-[62px] flex rounded-[9px] bg-[#ECEFFE] px-3 py-[9px] text-sm font-normal">
            <span className="iconfont icon-tip-fill text-[#4F6FF9]" />

            <span className="ml-2 text-black">已为您同步插件最新样式配置，若需修改，则以最后配置生效</span>

            <span className="iconfont icon-close ml-3 cursor-pointer text-sm" onClick={closeToast} />
        </div>
    );
};

const getInitStyleId = ({
    bindInfo,
    hasCustomStyle,
    hasMarkdownStyle,
}: Pick<BindStyleModalProps, 'bindInfo' | 'hasCustomStyle' | 'hasMarkdownStyle'>): number => {
    // bindInfo 无效，这种情况下，可能是用户没有绑定 style
    // 或者用户仅仅是本地上传了 ui.json 或者是 markdown style
    const invalidBindInfo = bindInfo?.styleId === undefined;
    if ((invalidBindInfo || bindInfo?.styleId === CustomStyleId) && hasCustomStyle) {
        return CustomStyleId;
    }

    if ((invalidBindInfo || bindInfo?.styleId === MarkdownStyleId) && hasMarkdownStyle) {
        return MarkdownStyleId;
    }

    if (typeof bindInfo?.styleId === 'number') {
        return bindInfo.styleId;
    }
    return DefaultStyleId;
};

// eslint-disable-next-line complexity, max-statements
export default function BindStyleModal({
    open,
    titleSuffix,
    response,
    xReturnRaw,
    setXReturnRaw,
    bindInfo,
    hasCustomStyle = false,
    hasMarkdownStyle = false,
    pluginId,
    onCancel,
    onConfirm,
    onSwitchStyle,
    isPluginStyleUpdate = false,
    isLoading = false,
}: BindStyleModalProps) {
    const [showPluginStyleUpdate, setShowPluginStyleUpdate] = useState<boolean>(isPluginStyleUpdate);
    const initStyleId = getInitStyleId({bindInfo, hasCustomStyle, hasMarkdownStyle});
    const [currentSelectedStyleId, setCurrentSelectedStyleId] = useState<PluginStyle['styleId']>(initStyleId);

    const [lastLoading, setLastLoading] = useState(isLoading);
    if (lastLoading !== isLoading) {
        setLastLoading(isLoading);
        if (!isLoading) {
            setCurrentSelectedStyleId(initStyleId);
            setShowPluginStyleUpdate(isPluginStyleUpdate);
        }
    }

    const [styleList, setStyleList] = useState<PluginStyle[]>([]);
    const [showUpdatePopover, setShowUpdatePopover] = useState<boolean>(false);

    useEffect(() => {
        let id: number | null = null;
        if (isLoading) {
            return;
        }

        api.getOfficialPluginStyle().then(styleList => {
            setStyleList(styleList);
            setShowUpdatePopover(true);
            id = setTimeout(() => {
                setShowUpdatePopover(false);
            }, 5000) as unknown as number;
            return () => {
                id && clearTimeout(id);
            };
        });
    }, [isLoading]);

    const currentSelectedStyle = useMemo(() => {
        return styleList?.find(item => item.styleId === currentSelectedStyleId);
    }, [currentSelectedStyleId, styleList]);

    const [form] = Form.useForm();
    const formData = Form.useWatch([], form);

    const {ubcShowLog, ubcClickLog} = useUbcLog();

    const formDataMapRef = useRef<Map<number, any>>(new Map());

    const handleStyleChange = useCallback(
        (nextStyleId: number) => {
            onSwitchStyle?.(nextStyleId);
            setShowUpdatePopover(false);
            const lastStyleId = currentSelectedStyleId;
            if (lastStyleId !== DefaultStyleId) {
                formDataMapRef.current.set(lastStyleId, formData);
            }

            if (nextStyleId !== DefaultStyleId) {
                if (formDataMapRef.current.has(nextStyleId)) {
                    const formData = formDataMapRef.current.get(nextStyleId);
                    form.setFieldsValue(formData);
                } else {
                    form.resetFields();
                }
            }

            setCurrentSelectedStyleId(nextStyleId);
        },
        [currentSelectedStyleId, form, formData, onSwitchStyle]
    );

    const [couldShowUpdateMap, setCouldShowUpdateMap] = useState<Record<number, boolean>>({});

    const onCardCLick = useCallback(
        (style: PluginStyle) => {
            ubcClickLog(EVENT_TRACKING_CONST.AbilityPluginOperationBindStyleModalStyleCard, {
                [EVENT_EXT_KEY_CONST.TEMPLATE_NAME]: style.styleJsonObject?.uiMeta?.component?.type,
            });
            // 新进入 style 需要关闭，上一次的也要关闭（主要用于一开始进来的那个 style 已经是新的
            setCouldShowUpdateMap(prev => ({...prev, [currentSelectedStyleId]: false, [style.styleId]: false}));
            handleStyleChange(style.styleId);
        },
        [handleStyleChange, ubcClickLog, setCouldShowUpdateMap, currentSelectedStyleId]
    );

    const selectDefault = useCallback(() => {
        handleStyleChange(DefaultStyleId);
    }, [handleStyleChange]);

    const [modal, modalContextHolder] = Modal.useModal();

    // eslint-disable-next-line complexity
    const onOk = useCallback(async () => {
        ubcClickLog(EVENT_TRACKING_CONST.AbilityPluginOperationBindStyleModalConfirm);

        if (pluginId) {
            const {isBindAgent} = await api.pluginBindAgent({pluginId});
            if (isBindAgent) {
                const isConfirm = await modal.confirm({
                    icon: null,
                    className: pluginConfirmModalStyle,
                    title: '确认要更新样式吗？',
                    content: '当前插件已被智能体引用，提交&审核通过后，将为您同步更新至绑定该插件的智能体',
                    cancelText: '取消',
                    okText: '确认',
                    centered: true,
                });
                if (!isConfirm) {
                    return;
                }
            }
        }

        const formData = await form.validateFields();
        if (initStyleId === CustomStyleId) {
            const isConfirm = await modal.confirm({
                icon: <ExclamationCircleOutlined className="mr-2 mt-1 text-sm" />,
                content: '确定要使用该模板吗？使用后，原ui.json的配置将失效。',
                cancelText: '取消',
                okText: '确认',
                centered: true,
            });
            if (!isConfirm) {
                return;
            }
        }

        if (initStyleId === MarkdownStyleId) {
            const isConfirm = await modal.confirm({
                icon: <ExclamationCircleOutlined className="mr-2 mt-1 text-sm" />,
                content: '确定要使用该模板吗？使用后，原markdown的配置将失效。',
                cancelText: '取消',
                okText: '确认',
                centered: true,
            });
            if (!isConfirm) {
                return;
            }
        }

        if (currentSelectedStyleId === DefaultStyleId) {
            if (initStyleId === DefaultStyleId) {
                onCancel();
                return;
            }

            onConfirm({
                styleId: DefaultStyleId,
                styleVersion: '',
                styleJson: '',
            });
            return;
        }

        const styleJsonObject = {
            formData,
            uiMeta: formDataToUIMeta(formData, currentSelectedStyle!, xReturnRaw),
        };

        if (currentSelectedStyleId === initStyleId && currentSelectedStyleId > CustomStyleId) {
            // 直接来自 form 的 formData 包含了为 undefined 的值，但这部分数据发送到服务端就不再存在
            // 因此必须过滤一遍才能比较
            const toServerStyleJsonObject = JSON.parse(JSON.stringify(styleJsonObject));
            if (isEqual(bindInfo?.styleJsonObject, toServerStyleJsonObject)) {
                onCancel();
                return;
            }
        }

        onConfirm({
            styleId: currentSelectedStyle!.styleId,
            styleVersion: currentSelectedStyle!.styleVersion,
            styleJsonObject,
            styleJson: JSON.stringify(styleJsonObject),
        });
    }, [
        ubcClickLog,
        pluginId,
        form,
        initStyleId,
        currentSelectedStyleId,
        currentSelectedStyle,
        xReturnRaw,
        onConfirm,
        modal,
        onCancel,
        bindInfo?.styleJsonObject,
    ]);

    // 关闭弹窗
    const onClose = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.AbilityPluginOperationBindStyleModalClose);
        onCancel();
    }, [onCancel, ubcClickLog]);

    // 关闭插件样式更新提示
    const handleClosePluginStyleUpdate = useCallback(() => {
        setShowPluginStyleUpdate(false);
    }, [setShowPluginStyleUpdate]);

    useEffect(() => {
        if (initStyleId === CustomStyleId) {
            ubcShowLog(EVENT_TRACKING_CONST.AbilityPluginOperationBindStyleModalUIJsonExistNotice);
        }
    }, [initStyleId, ubcShowLog]);

    useEffect(() => {
        if (isLoading) {
            return;
        }

        setCurrentSelectedStyleId(initStyleId);

        form.setFieldsValue(merge(getDefaultFormData(), bindInfo?.styleJsonObject?.formData || {}));

        const formDataMap = formDataMapRef.current;

        // 配置过样式后，表单初始化时，对表单项进行校验
        if (bindInfo?.styleId && bindInfo?.styleId > 0) {
            setTimeout(() => {
                form.validateFields();
            }, 500);
        }
        return () => {
            setCurrentSelectedStyleId(DefaultStyleId);
            form.resetFields();
            formDataMap.clear();
        };
    }, [
        isLoading,
        form,
        bindInfo?.styleJsonObject?.formData,
        hasCustomStyle,
        ubcShowLog,
        bindInfo,
        hasMarkdownStyle,
        initStyleId,
    ]);

    const {showLog} = useUbcLogV2();

    useEffect(() => {
        if (open && !isLoading) {
            // 埋点 Z-1 插件回复卡片页面展现
            showLog(EVENT_VALUE_CONST.REPLY_BUBBLE_CONFIG, EVENT_PAGE_CONST.CREATE_ABLE_PLUGIN);
        }
    }, [open, showLog, isLoading]);

    // eslint-disable-next-line @typescript-eslint/init-declarations
    let shouldShowPopoverStyle: PluginStyle | undefined;
    // 如果当前绑定的样式不是自定义样式，则显示其他样式更新弹窗
    if (bindInfo?.styleId && bindInfo?.styleId > CustomStyleId) {
        const bindStyle = styleList.find(style => style.styleId === bindInfo.styleId);
        shouldShowPopoverStyle = bindStyle?.updateTag === StyleUpdateTag.NoUpdate ? undefined : bindStyle;
    } else {
        shouldShowPopoverStyle = styleList.find(style => style.updateTag !== StyleUpdateTag.NoUpdate);
    }

    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: inputToken,
                    Select: selectToken,
                },
            }}
        >
            <StyledModal
                open={open}
                title={
                    <div className="ml-6 flex h-10 items-center">
                        <span>配置回复卡片样式</span>

                        {!!titleSuffix && (
                            <Tag
                                bordered={false}
                                className="ml-[9px] rounded-[3px] px-[3px] text-base font-medium leading-[20px] text-[#272933]"
                                color="#F5F6FA"
                            >
                                {titleSuffix}
                            </Tag>
                        )}

                        {showPluginStyleUpdate && (
                            <PluginStyleUpdateToast onCloseToast={handleClosePluginStyleUpdate} />
                        )}
                    </div>
                }
                onCancel={onClose}
                style={{top: 70}}
                maskClosable={false}
                onOk={onOk}
                okText="确定"
                cancelText="取消"
                okButtonProps={{
                    disabled:
                        !(+currentSelectedStyleId > CustomStyleId || currentSelectedStyleId === DefaultStyleId) ||
                        isLoading,
                }}
                destroyOnClose
            >
                {modalContextHolder}

                <div className="mt-6 flex h-[calc(100vh-296px)]">
                    <div className="absolute left-0 h-[1px] w-full bg-[#E6E9EF]" />
                    {isLoading || styleList.length === 0 ? (
                        <div className="flex grow items-center justify-center">
                            <Loading />
                        </div>
                    ) : (
                        <>
                            <div className="flex h-full w-[298px] flex-col border-r-[0.5px] border-[#E6E9EF]">
                                <ScrollContainer
                                    className="flex h-full flex-col gap-[26px] p-[26px]"
                                    scrollbarColor="#D9D9D9"
                                    scrollbarWidth={4}
                                    scrollY
                                >
                                    <div className="cursor-pointer" onClick={selectDefault}>
                                        <div
                                            className={classNames(
                                                'w-full rounded-[9px] border-[1px] transition-transform duration-300 hover:scale-[1.1]',
                                                {
                                                    'border-primary': currentSelectedStyleId === DefaultStyleId,
                                                    'border-transparent': currentSelectedStyleId !== DefaultStyleId,
                                                }
                                            )}
                                            style={{
                                                background: 'linear-gradient(180deg, #F7F8FA 51.72%, #F5F8FF 100%)',
                                            }}
                                        >
                                            <div className="flex flex-col gap-[7px] px-[14px] py-[22px]">
                                                <span className="text-base font-medium leading-5">不使用卡片样式</span>
                                                <span className="text-sm text-gray-tertiary">
                                                    大模型根据调用结果生成文本回复
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="text-xl font-medium text-black">选择模板</div>

                                    {styleList?.map(style => (
                                        <OfficialStyleCard
                                            key={style.styleId}
                                            style={style}
                                            selected={currentSelectedStyle?.styleId === style.styleId}
                                            shouldShowPopover={shouldShowPopoverStyle === style && showUpdatePopover}
                                            couldShowUpdate={couldShowUpdateMap[style.styleId] !== false}
                                            onCardClick={onCardCLick}
                                        />
                                    ))}
                                    <div>
                                        <img
                                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/official-plugin-style/more.png"
                                            className="h-[111px] w-full"
                                        />
                                    </div>
                                </ScrollContainer>
                            </div>
                            {currentSelectedStyle?.styleId ? (
                                <>
                                    <div className="flex h-full flex-1 flex-col pl-6 pt-6">
                                        <div className="mb-3 text-xl font-medium text-black">配置</div>
                                        <ScrollContainer
                                            className="mr-1 h-full pr-4"
                                            scrollbarColor="#D9D9D9"
                                            scrollbarWidth={4}
                                            scrollY
                                        >
                                            <OfficialStyleForm
                                                form={form}
                                                style={currentSelectedStyle}
                                                formData={formData}
                                                response={response}
                                                xReturnRaw={xReturnRaw}
                                                setXReturnRaw={setXReturnRaw}
                                            />
                                        </ScrollContainer>
                                    </div>
                                    <div className="flex h-full w-[435px] flex-col pl-3 pt-6 text-xs">
                                        <div className="mb-6 text-xl font-medium text-black">预览</div>
                                        <ScrollContainer
                                            className="h-full rounded-[15px] bg-[#F7F8FA]"
                                            scrollbarColor="#D9D9D9"
                                            scrollbarWidth={4}
                                            scrollY
                                        >
                                            <img src={currentSelectedStyle?.stylePreviewImage} className="w-full" />
                                        </ScrollContainer>
                                    </div>
                                </>
                            ) : (
                                <div className="relative flex-1">
                                    {currentSelectedStyleId === CustomStyleId && <CustomStylePreview />}
                                    {currentSelectedStyleId === MarkdownStyleId && <MarkdownStylePreview />}
                                    {currentSelectedStyleId === DefaultStyleId && (
                                        <div className="flex max-h-[100%] justify-center overflow-hidden">
                                            <Image
                                                preview={false}
                                                className="mt-[26px] w-[430px]"
                                                src={NoBindStyleSrc}
                                            />
                                        </div>
                                    )}
                                </div>
                            )}
                        </>
                    )}
                </div>
            </StyledModal>
        </ConfigProvider>
    );
}
