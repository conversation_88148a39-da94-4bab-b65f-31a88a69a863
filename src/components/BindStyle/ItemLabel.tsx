/**
 * @file 选项 label
 * <AUTHOR>
 */
import {Tooltip} from 'antd';

export default function ItemLabel({title, desc, required}: {title?: string; desc?: string; required?: boolean}) {
    return (
        <div className="flex items-center">
            <span className="whitespace-nowrap">{title}</span>
            {required ? (
                <span className="ml-[3px] text-error" style={{fontFamily: 'SimSong'}}>
                    *
                </span>
            ) : null}
            {desc?.length ? (
                <Tooltip title={<span>{desc}</span>} placement="bottomLeft" align={{offset: [-13, 9]}}>
                    <span className="iconfont icon-questionCircle ml-[3px] text-sm text-gray-tertiary" />
                </Tooltip>
            ) : null}
        </div>
    );
}
