/**
 * @file BindStyleModal 组件需要的 state 统一在 useBindStyleModalHook 中定义，避免其他模块每次定义多个 state
 * <AUTHOR>
 */
import {useState} from 'react';
import {ResponseSchema, StyleBindInfo} from '@/components/BindStyle/interface';

export default function useBindStyleModalHook<T extends StyleBindInfo>() {
    /**
     * 仅插件绑定样式时需要传入该字段
     * 配置样式时选择的工具的响应数据配置的 'x-return-raw' 数据
     */
    const [xReturnRaw, setXReturnRaw] = useState<string>('');
    // 配置样式时选择的插件工具、工作流节点的返回的数据结构
    const [selectedResponse, setSelectedResponse] = useState<ResponseSchema | null>(null);
    // 开/关弹窗
    const [openBindStyleModal, setOpenBindStyleModal] = useState(false);
    // 当前选择并编辑的绑定数据
    const [selectedStyleBindInfo, setSelectedStyleBindInfo] = useState<T>();

    return {
        xReturnRaw,
        setXReturnRaw,
        selectedResponse,
        setSelectedResponse,
        openBindStyleModal,
        setOpenBindStyleModal,
        selectedStyleBindInfo,
        setSelectedStyleBindInfo,
    };
}
