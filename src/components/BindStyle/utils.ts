/**
 * @file 绑定样式模块的工具方法
 * <AUTHOR>
 */
import cloneDeep from 'lodash/cloneDeep';
import isObject from 'lodash/isObject';
import isPlainObject from 'lodash/isPlainObject';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';
import set from 'lodash/set';
import {
    InterpOptions,
    ResponseSchema,
    StyleBindType,
    StyleFormData,
    TreeSelectNodeData,
    UIMeta,
} from '@/components/BindStyle/interface';
import {PluginStyle} from '@/api/pluginEdit/interface';
import {DataType, OutputItem} from '@/modules/workflow/flow/interface';

/**
 * 判断是否为插值模板 '{{expr}}'
 *
 * @param value 要判断的值
 * @return
 */
export function isInterp(value: any): boolean {
    return typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}');
}

/**
 * 获取插值变量值
 *
 * @param interp 插值表达式，可选参数，默认为空字符串
 * @returns 返回去掉插值标记后的字符串，若参数为空则返回空字符串
 */
export function getInterpVariable(interp?: string) {
    return (interp || '').replace('{{', '').replace('}}', '').trim();
}

/**
 * 判断给定的字符串类型是否为数组类型
 *
 * @param type 待判断的类型字符串
 * @returns 如果类型字符串为 'array'、以 'Array<' 开头或以 '[]' 结尾，则返回 true；否则返回 false
 */
export function isArray(type?: string): boolean {
    if (!type) {
        return false;
    }

    return type === 'array' || type.startsWith('Array<') || type.endsWith('[]');
}

/**
 * 判断给定类型字符串是否为整数类型
 *
 * @param type 类型字符串
 * @returns 返回布尔值，表示给定类型字符串是否为整数类型
 */
export function isInteger(type?: string): boolean {
    if (!type) {
        return false;
    }

    return (
        type.toLowerCase() === 'number' ||
        type.toLowerCase() === 'integer' ||
        type.toLowerCase() === 'int' ||
        type.toLowerCase() === 'uint' ||
        type.toLowerCase() === 'long'
    );
}

/**
 * 判断字符串是否为浮点数类型
 *
 * @param type 待判断的字符串
 * @returns 是浮点数类型返回true，否则返回false
 */
export function isFloat(type?: string): boolean {
    if (!type) {
        return false;
    }

    return type.toLowerCase() === 'float' || type.toLowerCase() === 'double';
}

/**
 * 判断传入的字符串类型是否为数字类型
 *
 * @param type 待判断的字符串类型
 * @returns 返回布尔值，表示是否为数字类型
 */
export function isNumber(type?: string): boolean {
    return isInteger(type) || isFloat(type);
}

/**
 * 判断传入的字符串是否为布尔类型
 *
 * @param type 待判断的字符串
 * @returns 如果字符串为 'boolean' 或 'bool'（不区分大小写），则返回 true；否则返回 false
 */
export function isBoolean(type?: string): boolean {
    if (!type) {
        return false;
    }

    return type.toLowerCase() === 'boolean' || type.toLowerCase() === 'bool';
}

/**
 * 判断给定的类型是否为基本数据类型
 *
 * @param type 待判断的类型，字符串形式
 * @returns 如果是基本数据类型（string、number、boolean）则返回true，否则返回false
 */
export function isBasicDataType(type: string): boolean {
    if (!type) {
        return false;
    }

    return type.toLowerCase() === 'string' || isNumber(type) || isBoolean(type);
}

/**
 * 判断当前变量跟当前响应的数据中的字段类型是否匹配
 *
 * @param type 待匹配的类型
 * @param targetType 目标类型
 * @returns 返回布尔值，表示两个类型是否匹配
 */
export function isMatch(type?: string, targetType?: string): boolean {
    if (!type || !targetType) {
        return false;
    }

    // 类型相同时可选择
    return (
        type.toLowerCase() === targetType.toLowerCase() ||
        (isBoolean(type) && isBoolean(targetType)) ||
        (isNumber(type) && isNumber(targetType)) ||
        (isArray(targetType) && isArray(type))
    );
}

/**
 * 获取 TreeSelect 数据
 *
 * @param responseSchema 工具响应的数据
 * @param keyPath 路径字符串（递归拼接）
 * @param options
 * @param options.variable 当前选择的变量名称，一般是父级绑定的变量，如：上级绑定数组字段后绑定子变量时变量选择器的数据为数组内的子元素的数据结构
 * @param options.xReturnRaw 根节点字符串，responseSchema 的一级节点只能使用一个，如：'responseSchema.data' 和 'responseSchema.data1' 只能使用 data 和 data1 中其中一个，xReturnRaw 参数用来指定使用哪个
 * @param options.canSelectFirstGrade 是否可选一级属性
 * @returns 任意类型的数组，TreeSelect 节点数据
 */
export function getTreeData(
    responseSchema: ResponseSchema | null,
    keyPath: string,
    options?: {variable?: string; xReturnRaw?: string; canSelectFirstGrade?: boolean}
): TreeSelectNodeData[] {
    const childKeyPathPrefix = keyPath ? `${keyPath}.` : '';
    const fullKeyPathPrefix = options?.variable ? `${options?.variable}.` : '';
    // 是否为完整 treeData 中的一级属性
    const isFullDataFirstGrade = !keyPath && !options?.variable;
    // 是否为当前 treeData 中的一级属性
    const isCurrentFirstGrade = !keyPath;

    if (responseSchema?.type === 'array') {
        // 拼接路径
        const childKeyPath = `${childKeyPathPrefix}item`;
        // 拼接完整路径
        const fullKeyPath = `${fullKeyPathPrefix}${childKeyPath}`;
        // 只标记当前节点的可选状态，不标记子节点的可选状态
        const isDisabled = responseSchema.disableAllChildren || responseSchema.disabled || !isCurrentFirstGrade;
        responseSchema.items.disabled = isDisabled;
        const tips = isDisabled ? '不能直接绑定数组中的元素 item 以及 item 下的字段' : '';
        responseSchema.items.tips = tips;
        // 向下级同步子节点禁用状态
        responseSchema.items.disableAllChildren = isDisabled;

        return [
            {
                title: 'item',
                value: childKeyPath,
                fullKeyPath,
                children: getTreeData(responseSchema.items, childKeyPath, options),
                type: responseSchema.items.type,
                disabled: isDisabled,
                tips,
            } as TreeSelectNodeData,
        ];
    }

    if (responseSchema?.type === 'object') {
        // eslint-disable-next-line complexity
        return Object.entries(responseSchema.properties || {}).map(([key, value]) => {
            // 拼接路径
            const childKeyPath = `${childKeyPathPrefix}${key}`;
            // 拼接完整路径
            const fullKeyPath = `${fullKeyPathPrefix}${childKeyPath}`;
            // 是否禁用一级属性
            const isDisabledFirstGrade = !options?.canSelectFirstGrade && isFullDataFirstGrade;
            // 判断是否为完整 treeData 中的一级数组属性，一级数组属性不能选择
            const isFirstGradeArray = isDisabledFirstGrade && isArray(value.type);
            // 判断是否为渲染 ui.json 时传入到 data 的字段，如果不是则不能选择
            const isNotXReturnRaw = isDisabledFirstGrade && options?.xReturnRaw && options?.xReturnRaw !== key;
            // 父级已标记所有子节点不可选、当前节点非指定返回到渲染 ui.json 时传入的字段、当前节点为一级数组节点，则当前节点以及所有子节点不可选
            value.disableAllChildren = responseSchema.disableAllChildren || isNotXReturnRaw || isFirstGradeArray;
            // 只用于标记当前节点的可选状态，不标记子节点的可选状态
            value.disabled = responseSchema.disableAllChildren || isNotXReturnRaw || isDisabledFirstGrade;
            // 设置提示信息
            if (isNotXReturnRaw) {
                value.tips = `已选择一级节点 ${options?.xReturnRaw}，不能选择其他一级节点下的字段`;
            } else if (isDisabledFirstGrade) {
                value.tips = '不能选择一级属性';
            } else if (value.disabled) {
                value.tips = responseSchema.tips;
            } else {
                value.tips = '';
            }

            return {
                title: key,
                value: `{{${childKeyPath}}}`,
                fullKeyPath,
                children: getTreeData(value, childKeyPath, options),
                type: value.type,
                disabled: value.disabled,
                tips: value.tips,
            } as TreeSelectNodeData;
        });
    }

    return [];
}

/**
 * 从响应数据中获取子树数据
 *
 * @param response 响应数据对象，需要符合 ResponseSchema 类型
 * @param options
 * @param options.variable 指定从哪个变量中获取子树数据，格式为 "a.b.c"
 * @param options.xReturnRaw 根节点字符串，responseSchema 的一级节点只能使用一个，如：'responseSchema.data' 和 'responseSchema.data1' 只能使用 data 和 data1 中其中一个，xReturnRaw 参数用来指定使用哪个
 * @param options.canSelectFirstGrade 是否可选一级属性
 * @returns 返回一个数组，包含从响应数据中获取的子树数据
 */
export function getChildTreeData(
    response: ResponseSchema | null,
    options?: {variable?: string; xReturnRaw?: string; canSelectFirstGrade?: boolean}
) {
    if (!response) {
        return [];
    }

    let childResponse: ResponseSchema | undefined = response;
    if (options?.variable) {
        const keys = getInterpVariable(options?.variable).split('.');
        keys.forEach(key => {
            childResponse = childResponse?.properties?.[key];
        });
    }

    return getTreeData(childResponse, '', options);
}

/**
 * 替换插值语句中指定的前缀并返回处理后的插值语句
 *
 * @param interpValue 待处理的字符串
 * @param xReturnRaw 前缀字符串
 * @returns 处理后的值
 */
function replaceXReturnRawPrefix(interpValue?: string, xReturnRaw?: string) {
    // 输入为空时原样返回
    if (!interpValue) {
        return interpValue;
    }

    // xReturnRaw 为空时前后拼接差值语句的前后缀再返回
    if (!xReturnRaw) {
        return `{{${interpValue}}}`;
    }

    return `{{${(interpValue || '').replace(new RegExp(`^${xReturnRaw}.`), '')}}}`;
}

/**
 * 将表单数据转换为 UIMeta 数据
 *
 * @param formData 表单数据对象，包含组件信息和样式信息
 * @param style 插件样式对象，包含样式 id 和样式 JSON 对象
 * @param xReturnRaw 根节点字符串，responseSchema 的一级节点只能使用一个，如：'responseSchema.data' 和 'responseSchema.data1' 只能使用 data 和 data1 中其中一个，xReturnRaw 参数用来指定使用哪个
 * @returns 转换后的 UIMeta 数据对象
 */
export function formDataToUIMeta(formData: StyleFormData, style: PluginStyle, xReturnRaw?: string): UIMeta {
    if (!formData.component || !style?.styleId) {
        return {} as UIMeta;
    }

    const formDataCopy = cloneDeep(formData);
    // 将配置中的所有插值语句变量替换 xReturnRaw 前缀，因为渲染 sdk 时传入的数据为 xReturnRaw 字段，而不是完整的数据
    const customizer = (objValue: any, srcValue: any): any => {
        if (isInterp(srcValue)) {
            // 满足 if 条件时，srcValue 为 string 类型
            const srcValueVariable = getInterpVariable(srcValue);

            return replaceXReturnRawPrefix(srcValueVariable, xReturnRaw);
        } else if (isPlainObject(srcValue)) {
            return mergeWith(objValue, srcValue, customizer);
        }

        return srcValue;
    };

    formDataCopy.component = mergeWith({}, formDataCopy.component, customizer);
    const uiMeta = cloneDeep(style.styleJsonObject?.uiMeta);
    uiMeta.component.props = uiMeta.component.props || {};
    if (style.styleJsonObject?.form?.bindType === StyleBindType.Single) {
        const events = formDataCopy.component.events;
        const option = {};
        // 单条数据的情况整个卡片的点击事件需要通过 events 实现，因此需要将 events 配置中的 options 增加前缀 '$event.ctx.model.'
        const customizer = (objValue: any, srcValue: any): any => {
            if (isInterp(srcValue)) {
                // 满足 if 条件时，srcValue 为 string 类型
                const srcValueVariable = getInterpVariable(srcValue);

                return `{{$data.${srcValueVariable}}}`;
            } else if (isPlainObject(srcValue)) {
                return mergeWith(objValue, srcValue, customizer);
            }

            return srcValue;
        };

        mergeWith(option, events.click.option, customizer);
        uiMeta.component.events = {
            click: [
                {
                    action: 'link',
                    option,
                },
            ],
        };
        // 兼容 ui sdk 即将上线的非兼容改造，临时采用通过组件名判断的方式，后续考虑将 goodData 层级配置到组件配种中（后续可能不会有组件的 props 整体多出一个层级的情况）
        if (uiMeta.component.type === 'good-card' || uiMeta.component.type === 'goods-card') {
            set(uiMeta, 'component.props.goodData.link', events.click.option);
        }
    } else {
        const forVariable = getInterpVariable(formDataCopy.component.for);
        if (forVariable) {
            uiMeta.component.for = `{{item, index in ${forVariable}}}`;
        }

        const events = formDataCopy.component.events;
        uiMeta.component.props.link = events.click.option;
    }

    uiMeta.component.props = merge(uiMeta.component.props || {}, formDataCopy.component.props);

    return uiMeta;
}

/**
 * 获取默认的表单数据
 *
 * @returns 返回 StyleFormData 类型的默认表单数据对象
 */
export function getDefaultFormData(): StyleFormData {
    return {
        component: {
            type: '',
            props: {},
            events: {},
        },
        dataExtends: {},
    } as StyleFormData;
}

/**
 * 获取变更的字段的值，如：{link: {url: '{{data.url}}'}}，则获取 '{{data.url}}'
 *
 * @param options 插值表达式配置
 * @returns 返回变化的值，类型为字符串
 */
export function getChangedValue(options: InterpOptions): string {
    let value = options;
    while (isObject(value)) {
        value = Array.isArray(value) ? value[0] : Object.values(value)[0];
    }

    return value;
}

/**
 * 判断一个值是否包含绑定值，如：{link: {url: '{{data.url}}'}} 或者 {button: {text: '购买'}}
 * 多层嵌套的情况需要递归判断
 *
 * @param value 待判断的值
 * @returns 如果包含绑定值，则返回 true；否则返回 false
 */
function isBindValue(value: any): boolean {
    if (isPlainObject(value)) {
        return Object.values(value).some((v: any) => isBindValue(v));
    }

    if (Array.isArray(value)) {
        return value.some((v: any) => isBindValue(v));
    }

    return value !== undefined && value !== null && value !== '';
}

/**
 * 判断给定的选项对象是否包含绑定值，如：{link: {url: '{{data.url}}'}} 或者 {button: {text: '购买'}}
 *
 * @param options 选项对象
 * @returns 如果包含绑定值则返回 true，否则返回 false
 */
export function hasBindValue(options: InterpOptions): boolean {
    if (!isPlainObject(options)) {
        return false;
    }

    return Object.values(options).some((v: any) => isBindValue(v));
}

/**
 * 将 OutputItem 对象转换为 ResponseSchema 对象（递归处理）
 *
 * @param outputItem 要转换的 OutputItem 对象
 * @returns 转换后的 ResponseSchema 对象
 */
function outputItemToResponseSchemaRecursive(outputItem: OutputItem): ResponseSchema {
    if (!outputItem?.type) {
        return {} as ResponseSchema;
    }

    if (outputItem.type === DataType.Array) {
        return {
            type: 'array',
            items: outputItemToResponseSchemaRecursive(outputItem.schema[0]),
        } as ResponseSchema;
    }

    if (outputItem.type === DataType.Object) {
        return {
            type: 'object',
            properties: outputItem.schema.reduce(
                (acc, item) => ({...acc, [item.name]: outputItemToResponseSchemaRecursive(item)}),
                {}
            ),
        } as ResponseSchema;
    }

    return {
        type: outputItem.type === DataType.Number ? 'number' : outputItem.type,
    } as ResponseSchema;
}

/**
 * 将 OutputItem 数组转换为 ResponseSchema 对象
 *
 * @param outputItems 要转换的 OutputItem 数组
 * @returns 转换后的 ResponseSchema 对象
 */
export function outputItemsToResponseSchema(outputItems: OutputItem[]): ResponseSchema {
    return {
        type: 'object',
        properties: outputItems?.reduce(
            (acc, item) => ({...acc, [item.name]: outputItemToResponseSchemaRecursive(item)}),
            {}
        ),
    } as ResponseSchema;
}

/**
 * 获取树形选择器的节点数据
 *
 * @param treeData 树形数据
 * @param value 节点值
 * @returns 返回树形选择器的节点数据，若未找到则返回 undefined
 */
export function getTreeNodeData(treeData?: TreeSelectNodeData[], value?: string): TreeSelectNodeData | undefined {
    const interpVariable = getInterpVariable(value);
    if (!interpVariable || !treeData?.length) {
        return;
    }

    const valueKeys = interpVariable.split('.');
    let findNode: TreeSelectNodeData | undefined = {children: treeData} as TreeSelectNodeData;

    for (const key of valueKeys) {
        if (!findNode?.children?.length) {
            return;
        }

        findNode = findNode.children.find(node => node.title === key);
    }

    return findNode;
}
