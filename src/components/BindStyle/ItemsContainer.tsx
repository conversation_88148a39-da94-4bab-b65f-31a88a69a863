/**
 * @file 每个选项列表模块的容器
 * <AUTHOR>
 */
import React from 'react';

export default function ItemsContainer({
    className,
    title,
    desc,
    required,
    children,
}: {
    className?: string;
    title: string | React.ReactNode;
    desc?: string;
    required?: boolean;
    children?: React.ReactNode;
}) {
    return (
        <div className={className}>
            <div className="mb-[6px]">
                <div className="mb-[3px] text-base font-medium">
                    <span>{title}</span>
                    {required ? (
                        <span className="ml-[3px] text-error" style={{fontFamily: 'SimSong'}}>
                            *
                        </span>
                    ) : null}
                </div>
                {desc?.length ? <div className="text-sm leading-[22px] text-gray-tertiary">{desc}</div> : null}
            </div>
            {children}
        </div>
    );
}
