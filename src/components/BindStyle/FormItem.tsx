/**
 * @file 表单项组件 Form.Item
 * <AUTHOR>
 */
import get from 'lodash/get';
import {Form, FormItemProps, Switch} from 'antd';
import {useMemo} from 'react';
import {RuleObject} from 'antd/es/form';
import ItemLabel from '@/components/BindStyle/ItemLabel';
import ResponseSelect from '@/components/BindStyle/ResponseSelect';
import {FormType, StyleFormData, TreeSelectNodeData, VariableType} from '@/components/BindStyle/interface';
import Input, {InputProps} from '@/components/BindStyle/Input';
import {getStringLength} from '@/utils/text';
import {getTreeNodeData, isMatch} from '@/components/BindStyle/utils';
import {LinkLabel} from './const';

interface CustomFormItemProps extends FormItemProps {
    hideLabel?: boolean;
    options: {
        name: string;
        label?: string;
        desc?: string;
        required?: boolean;
        formType?: string;
        type: VariableType;
        treeData?: TreeSelectNodeData[];
    };
    disabled?: boolean;
    formData: StyleFormData;
}

export default function FormItem({
    className,
    hideLabel,
    noStyle,
    options,
    formData,
    disabled,
    dependencies,
}: CustomFormItemProps) {
    const itemOptions = useMemo(
        () => ({
            ...options,
            value: get(formData, options.name),
        }),
        [options, formData]
    );
    const rules = useMemo(() => {
        const rules: RuleObject[] = [
            {
                required: itemOptions.required,
                message: itemOptions.formType === 'Input' ? `请输入${itemOptions.label}` : `请选择${itemOptions.label}`,
            },
        ];
        if (itemOptions.formType === FormType.Input) {
            const {minLength, maxLength} = itemOptions as InputProps['options'];
            rules.push({
                validator: (rule: RuleObject, value: string) => {
                    const length = +(getStringLength(value) / 2).toFixed(0);
                    if (length && !value.match(/^[a-zA-Z\u4e00-\u9fa5]+$/g)) {
                        return Promise.reject(new Error('存在不支持的特殊字符，请重新输入'));
                    }

                    if (minLength && length && length < minLength) {
                        return Promise.reject(new Error(`${itemOptions.label}不能少于${minLength}个字符`));
                    }

                    if (maxLength && length > maxLength) {
                        return Promise.reject(new Error(`${itemOptions.label}不能多于${maxLength}个字符`));
                    }

                    return Promise.resolve();
                },
            });
        }
        // formType 为空表示 FormType.ResponseSelect
        else if (!itemOptions.formType || itemOptions.formType === FormType.ResponseSelect) {
            rules.push({
                // 校验绑定的变量是否存在，节点的 outputs 变更后绑定的变量可能会不存在或者类型变更后类型错误
                validator: (rule: RuleObject, value: string) => {
                    // 未绑定变量时走第一个 required 校验，不需要执行该 validator 校验，否则同时会出现两个错误提示
                    if (!value) {
                        return Promise.resolve();
                    }

                    const nodeData = getTreeNodeData(options.treeData, value);
                    if (!nodeData?.value) {
                        return Promise.reject(new Error('绑定的变量不存在'));
                    } else if (!isMatch(nodeData?.type, itemOptions.type)) {
                        return Promise.reject(new Error('绑定的变量类型错误'));
                    }

                    return Promise.resolve();
                },
            });
        }

        return rules;
    }, [itemOptions, options.treeData]);

    return (
        <Form.Item
            className={className}
            noStyle={noStyle}
            key={options.name}
            name={options.name.split('.')}
            label={
                hideLabel ? null : <ItemLabel required={options.required} title={options.label} desc={options.desc} />
            }
            rules={rules}
            dependencies={dependencies}
        >
            {options.formType === FormType.Input ? (
                <Input options={itemOptions} disabled={disabled} />
            ) : options.formType === FormType.Switch ? (
                <Switch size="small" value={itemOptions.value} disabled={disabled} />
            ) : (
                <ResponseSelect
                    options={itemOptions}
                    treeData={options.treeData}
                    disabled={disabled}
                    placeholder={
                        options.label === LinkLabel.SCHEME_LINK ? '请选择符合NA协议的链接，否则配置无效' : '请选择'
                    }
                />
            )}
        </Form.Item>
    );
}
