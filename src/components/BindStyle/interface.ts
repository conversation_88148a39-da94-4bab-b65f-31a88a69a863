/**
 * @file 接口文件
 * <AUTHOR>
 */
export enum FormType {
    Input = 'Input',
    ResponseSelect = 'ResponseSelect',
    Switch = 'Switch',
}

// 官方样式类型
export enum StyleBindType {
    // 单条数据
    Single = 'single',
    // 垂直列表
    VerticalList = 'verticalList',
    // 横向列表
    HorizontalList = 'horizontalList',
}

// 变量类型
export enum VariableType {
    Boolean = 'boolean',
    Number = 'number',
    String = 'string',
    Array = 'array',
    Object = 'object',
}

/**
 * 插值表达式配置，如：
 * {
        title: '{{data.title}}',
        link: {
            url: '{{data.link.url}}'
        }
    }
 */
export interface InterpOptions {
    [key: string]: string | InterpOptions;
}

// 事件配置
export interface EventData {
    enableSwan?: boolean;
    action: string;
    option: InterpOptions;
}

// 渲染 ui.json 时的 scheme 数据
export interface UIMeta {
    component: {
        for?: string;
        type: string;
        props: InterpOptions;
        events: {
            click: EventData;
        };
    };
    dataExtends: InterpOptions;
}

// 插件工具绑定样式时的表单数据，结构与 UIMeta 一致，配置的值有区别，在提交到后端时将 StyleFormData 转换为 UIMeta 存入数据库
export declare type StyleFormData = UIMeta;

// 插件工具响应的数据结构
export interface ResponseSchema {
    // 字段类型
    type: string;
    // 子字段列表：key 为字段名,type 为 object 时才存在
    properties: {[key: string]: ResponseSchema};
    // 数组元素类型，type 为 array 时才存在
    items: ResponseSchema;

    /**
     * 以下为平台上扩展的字段，实际开发者配置的 openapi.yaml 文件里不会存在
     */
    // 当前节点是否禁用
    disabled?: boolean;
    // 当前节点是否禁用所有子节点
    disableAllChildren?: boolean;
    // hover 展示的文本内容
    tips?: string;
}

// 表单项内的组件 props
export interface FormItemComponentProps {
    options: {
        // 当前配置项变量 name，如：title、coverImage
        name: string;
        // 变量类型，如：string、array
        type: VariableType;
        // 当前配置项变量值，如：data.title、data.img
        value: string;
    };
    // 选择器 placeholder
    placeholder?: string;
    // 是否禁用
    disabled?: boolean;
    // 用户选择后，更新 ui json 数据
    onChange?(value: string, name: string): void;
}

export interface TreeSelectNodeData {
    // 当前节点的标题，字段名：title
    title: string;
    // 当前节点的 keyPath 插值语法：'{{data.title}}'
    value: string;
    // 当前节点的完整 keyPath，父级绑定了数组的情况，子元素绑定变量时 value 内的值从数组的 item 开始（'{{item.title}}'），因此额外存储完整的 keyPath
    fullKeyPath: string;
    // 当前节点的子节点
    children: TreeSelectNodeData[];
    // 当前节点的变量数据类型
    type: VariableType;
    // 当前节点是否禁用
    disabled: boolean;
    // 当前节点是否禁用所有子节点
    disableAllChildren: boolean;
    // hover 展示的文本内容
    tips: string;
}

export interface StyleJsonObject {
    // 工具绑定的样式 scheme 的表单数据，仅在平台上使用，用于表单回填
    formData: StyleFormData;
    // 工具绑定的样式 scheme 的 uiMeta 数据，用于合并到 ui.json 文件中
    uiMeta: UIMeta;
}

export interface StyleBindInfo {
    // 工具绑定的样式 id
    styleId: number;
    // 工具绑定的样式版本
    styleVersion: string;
    // 工具绑定的样式 scheme 的 JSON 字符串
    styleJson: string;

    // JSON.parse(styleJson) 后的对象
    styleJsonObject?: StyleJsonObject;
    // 节点是否更改
    isChange?: boolean;
}
