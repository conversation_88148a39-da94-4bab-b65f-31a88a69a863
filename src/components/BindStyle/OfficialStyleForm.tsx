/**
 * @file 官方样式配置表单组件
 * <AUTHOR>
 */
import {useCallback, useMemo} from 'react';
import {Form, FormInstance, Table} from 'antd';
import {PluginStyle} from '@/api/pluginEdit/interface';
import {
    InterpOptions,
    ResponseSchema,
    StyleBindType,
    StyleFormData,
    VariableType,
} from '@/components/BindStyle/interface';
import LinkForm from '@/components/BindStyle/LinkForm';
import ItemsContainer from '@/components/BindStyle/ItemsContainer';
import ItemLabel from '@/components/BindStyle/ItemLabel';
import FormItem from '@/components/BindStyle/FormItem';
import {
    getChangedValue,
    getChildTreeData,
    getInterpVariable,
    getTreeData,
    isInterp,
} from '@/components/BindStyle/utils';
import {TableColumns} from '@/components/BindStyle/const';

interface OfficialStyleFormProps {
    form: FormInstance;
    style: PluginStyle;
    formData: StyleFormData;
    response: ResponseSchema | null;
    xReturnRaw?: string;
    setXReturnRaw?: (xReturnRaw: string) => void;
}

export default function OfficialStyleForm({
    form,
    style,
    response,
    xReturnRaw,
    setXReturnRaw,
    formData,
}: OfficialStyleFormProps) {
    const isSingle = style?.styleJsonObject?.form?.bindType === StyleBindType.Single;
    const treeData = useMemo(() => {
        // 传入的 setXReturnRaw 和 xReturnRaw 均为空表示不需要配置 x-return-raw，无 x-return-raw 时可选择一级属性
        return getTreeData(response, '', {xReturnRaw, canSelectFirstGrade: !setXReturnRaw && !xReturnRaw});
    }, [response, xReturnRaw, setXReturnRaw]);
    // 列表类型的样式绑定的数组变量
    const listVariable = useMemo(() => {
        return isSingle ? '' : formData?.component?.for;
    }, [isSingle, formData]);

    const listVariableOptions = useMemo(
        () => ({
            name: 'component.for',
            label: '关联卡片数组来源',
            type: VariableType.Array,
            required: true,
            treeData,
        }),
        [treeData]
    );

    // "关联卡片数组来源" 配置更新时下方的表单触发校验逻辑
    const formItemDependencies = useMemo(() => {
        return isSingle ? undefined : [['component', 'for']];
    }, [isSingle]);

    // 列表类型的样式绑定数组变量后才能继续绑定列表项内的变量
    const childFormItemDisabled = !isSingle && !listVariable;

    // 数组内的元素的变量选择器，绑定数组变量后，列表项的变量选择器只能选择数组内的元素的变量
    const childTreeData = useMemo(() => {
        return getChildTreeData(response, {
            variable: listVariable,
            xReturnRaw,
            // 传入的 setXReturnRaw 和 xReturnRaw 均为空表示不需要配置 x-return-raw，无 x-return-raw 时可选择一级属性
            canSelectFirstGrade: !setXReturnRaw && !xReturnRaw,
        });
    }, [listVariable, response, xReturnRaw, setXReturnRaw]);

    // 自定义渲染 "变量" 列
    TableColumns[0].render = useCallback((checked: boolean, record: any) => {
        return <ItemLabel title={record.label} desc={record.desc} required={record.required} />;
    }, []);

    // 自定义渲染 "数据" 列
    TableColumns[2].render = useCallback(
        (checked: boolean, record: any) => {
            const name = `component.props.${record.name}`;
            const options = {
                ...record,
                name,
                treeData: childTreeData,
            };

            return (
                <FormItem
                    className="mb-0"
                    hideLabel
                    options={options}
                    formData={formData}
                    disabled={childFormItemDisabled}
                    dependencies={formItemDependencies}
                />
            );
        },
        [formData, childTreeData, childFormItemDisabled, formItemDependencies]
    );

    // form 变更时，获取插值表达式中的变量的一级字段，作为 xReturnRaw
    const onValueChange = useCallback(
        (changedValues: InterpOptions) => {
            if (!setXReturnRaw) {
                return;
            }
            const value = getChangedValue(changedValues);
            // 当前不存在 xReturnRaw 且本次变更的字段的值为插值表达式，则获取插值表达式中的变量的一级字段当作 xReturnRaw
            if (!xReturnRaw && isInterp(value)) {
                const valueVariable = getInterpVariable(value);
                setXReturnRaw(valueVariable.split('.')[0]);
            }
        },
        [xReturnRaw, setXReturnRaw]
    );

    return (
        <Form layout="vertical" form={form} requiredMark={false} onValuesChange={onValueChange}>
            {!isSingle && (
                <ItemsContainer className="mt-3" title="关联卡片数组来源" required>
                    <FormItem hideLabel options={listVariableOptions} formData={formData} />
                </ItemsContainer>
            )}
            <ItemsContainer
                className="mt-3"
                title={style?.styleJsonObject.form.data.title}
                desc={style?.styleJsonObject.form.data.desc}
                required
            >
                <Table
                    rowKey="name"
                    columns={TableColumns}
                    dataSource={style?.styleJsonObject.form.data.props}
                    pagination={false}
                />
            </ItemsContainer>
            <LinkForm
                treeData={childTreeData}
                formData={formData}
                disabled={childFormItemDisabled}
                dependencies={formItemDependencies}
            />
        </Form>
    );
}
