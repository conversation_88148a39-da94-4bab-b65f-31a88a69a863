/**
 * @file 常量
 * <AUTHOR>
 */
import {ColumnsType} from 'antd/es/table';

// 用户自定义的样式 id
export const CustomStyleId = 0;

// 默认的样式 id（没有配置样式时的 styleId）
export const DefaultStyleId = -1;

// markdown 样式 id
export const MarkdownStyleId = -2;

// 绑定变量的 Table 的 column
export const TableColumns: ColumnsType<any> = [
    {
        title: '变量',
        dataIndex: 'label',
        className: 'p-3 text-black',
        width: '25%',
    },
    {
        title: '类型',
        dataIndex: 'type',
        className: 'p-3 text-black',
        width: '25%',
    },
    {
        title: '数据',
        dataIndex: 'action',
        className: 'p-3 text-black',
        width: '50%',
    },
] as ColumnsType;

export enum LinkLabel {
    COMMON_LINK = '通用链接',
    PC_LINK = 'PC端链接',
    SCHEME_LINK = '百度APP内链接',
}

// 链接相关的配置表单
export const LinkEventsForm = [
    {
        title: '设置卡片跳转链接',
        required: true,
        items: [
            {
                name: 'component.events.click.option.url',
                type: 'string',
                label: LinkLabel.COMMON_LINK,
                required: true,
                desc: '点击后跳转指定页面',
            },
            {
                name: 'component.events.click.option.pcUrl',
                type: 'string',
                label: LinkLabel.PC_LINK,
                desc: '配置后可指定在PC端跳转的页面',
            },
            {
                name: 'component.events.click.option.scheme',
                type: 'string',
                label: LinkLabel.SCHEME_LINK,
                desc: '配置百度App内的NA协议链接，点击打开百度APP内内容。若配置链接不符合NA协议，则此项配置无效。',
            },
        ],
    },
    {
        title: '优先跳转小程序',
        desc: '配置后可在支持打开百度小程序的场景，优先跳转小程序',
        items: [
            {
                name: 'component.events.click.option.swan.appKey',
                type: 'string',
                label: 'appKey',
                required: true,
                desc: '小程序唯一标识，可通过【小程序开发者平台-开发管理-开发设置】查询',
            },
            {
                name: 'component.events.click.option.swan.path',
                type: 'string',
                label: 'path',
                desc: '小程序打开的页面路径。页面路径可以在小程序代码 app.json 文件的 pages 属性中取到，常见形式为 "pages/index/index"',
            },
            {
                name: 'component.events.click.option.swan.query',
                type: 'string',
                label: 'query',
                desc: '小程序页面路径参数',
            },
        ],
    },
];
