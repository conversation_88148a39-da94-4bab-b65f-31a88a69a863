import {useCallback, useEffect, useState} from 'react';
import debounce from 'lodash/debounce';
import {useWatch} from 'antd/es/form/Form';
import classNames from 'classnames';
import {HtmlTag, validateHasHTML, getLink} from '@/utils/text';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {request} from '@/api/request';

interface Props {
    path: string[];
    name?: string;
    hidden?: boolean;
}
const urlList = [
    'agents.baidu.com',
    'agent-proxy.baidu.com',
    'agent-proxy-ws.baidu.com',
    'wen.baidu.com',
    'aiplugin.baidu.com',
    'ossapi.baidu.com',
];

export const HtmlValidationMessage = ({path, name, hidden}: Props) => {
    const {setSystemHasInvalidTag, setSystemLinkHasInvalidTag, setSystemLinkChecking} = usePromptEditStoreV2(store => ({
        setSystemHasInvalidTag: store.setSystemHasInvalidTag,
        setSystemLinkHasInvalidTag: store.setSystemLinkHasInvalidTag,
        setSystemLinkChecking: store.setSystemLinkChecking,
    }));
    const [warningText, setWarningText] = useState('');
    const value = useWatch(path);

    const judgeWarningText = useCallback((htmlTag: HtmlTag[]) => {
        // 判断标签中是否有 on*事件，是否包含javascript标签：script、iframe、object、embed
        if (htmlTag.some(tag => /script|iframe|object|embed/.test(tag.tagName))) {
            return {
                hasInvalidTag: true,
                reason: '请删除html标签中非法html标签，若保留将无法正常发布。',
            };
        }

        if (htmlTag.some(tag => /on\w+/.test(tag.tag))) {
            return {
                hasInvalidTag: true,
                reason: '请删除html标签中非法on*事件，若保留将无法正常发布。',
            };
        }

        return {
            hasInvalidTag: false,
            reason: '',
        };
    }, []);

    // 判断系统指令链接是否包含a、img标签
    const judgeLink = useCallback((htmlTag: HtmlTag[]) => {
        return htmlTag.filter(tag => /a|img/.test(tag.tagName));
    }, []);

    // 判断链接是否非法
    const auditPornUrl = async (urls: string[]) => {
        if (!urls.length) {
            return true;
        }

        return await request('POST', '/audit/porn/url', {urls});
    };

    // 判断图片是否非法
    const auditImage = async (imageUrl: string) => {
        // 判断图片的链接是否正确
        if (!imageUrl) {
            return true;
        }

        return await request('POST', '/audit/image', {imageUrl}, {timeout: 15000});
    };

    const checkIsLinkSafe = useCallback(async (htmlTag: HtmlTag[]) => {
        // 提取所有a标签的href属性
        const ahrefs = htmlTag.map(tag => /href="([^"]+)"/.exec(tag.tag)?.[1] ?? '').filter(Boolean);
        // 获取所有img的src属性
        const imgsrcs = htmlTag.map(tag => /src="([^"]+)"/.exec(tag.tag)?.[1] ?? '').filter(Boolean);

        // 检查链接是否包含 urlList 中的前缀
        const hasInvalidPrefix = [...ahrefs, ...imgsrcs].some(url => {
            // 防止相对/绝对路径请求接口
            const regex = /^(\/|\.\/|\.\.\/)/;
            return urlList.some(invalidUrl => url.toLowerCase().includes(invalidUrl.toLowerCase())) || regex.test(url);
        });

        if (hasInvalidPrefix) {
            return false;
        }

        const urlArr = [];
        if (ahrefs.length) {
            urlArr.push(auditPornUrl(ahrefs));
        }

        if (imgsrcs.length) {
            urlArr.push(...imgsrcs.map(auditImage));
        }

        const res = await Promise.all(urlArr);

        return res.every(item => item.pass === true);
    }, []);

    // 判断文本中的链接是否合法
    const checkLink = useCallback(
        async (value: string) => {
            // 获取文本中的链接
            const links = getLink(value);
            if (!links.length || hidden) {
                return false;
            }

            setSystemLinkChecking(true);
            // 判断链接是否合法
            const res = await auditPornUrl(links).finally(() => {
                setSystemLinkChecking(false);
            });
            if (res.pass === true) {
                return false;
            }

            setWarningText('请删除非法链接，若保留将无法正常发布。');
            setSystemHasInvalidTag(false);
            setSystemLinkHasInvalidTag(true);
            return true;
        },
        [hidden, setSystemHasInvalidTag, setSystemLinkChecking, setSystemLinkHasInvalidTag]
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedValidate = useCallback(
        debounce(async (val: string) => {
            const htmlTag = validateHasHTML(val);
            const isLinkValid = await checkLink(val);
            // 有非法链接直接返回
            if (isLinkValid) {
                return;
            }

            // 重置状态
            const resetState = () => {
                setWarningText('');
                setSystemHasInvalidTag(false);
                setSystemLinkHasInvalidTag(false);
                setSystemLinkChecking(false);
            };

            // 如果没有 HTML 标签，重置所有状态并返回
            if (!htmlTag.length || hidden) {
                resetState();
                return;
            }

            // 设置基础警告
            setWarningText(`不支持html标签渲染，标签将在智能体内转义为文本输出`);

            // 检查非法标签
            const isInvalidTag = judgeWarningText(htmlTag);
            if (isInvalidTag.hasInvalidTag) {
                setWarningText(isInvalidTag.reason);
                setSystemHasInvalidTag(true);
                return;
            }

            // 检查链接标签
            const linkTag = judgeLink(htmlTag);
            if (linkTag.length) {
                try {
                    setSystemLinkChecking(true);
                    const isSafe = await checkIsLinkSafe(linkTag);

                    if (isSafe) {
                        setSystemLinkHasInvalidTag(false);
                        setSystemHasInvalidTag(false);
                    } else {
                        setWarningText('请删除非法链接，若保留将无法正常发布。');
                        setSystemLinkHasInvalidTag(true);
                    }
                } catch {
                    setSystemLinkHasInvalidTag(true);
                } finally {
                    setSystemLinkChecking(false);
                }
                return;
            }

            // 如果没有问题，重置标记
            setSystemHasInvalidTag(false);
            setSystemLinkHasInvalidTag(false);
        }, 500),
        []
    );

    useEffect(() => {
        debouncedValidate(value);
        // 清除防抖函数的延时器
        return () => {
            debouncedValidate.cancel();
        };
    }, [value, debouncedValidate, checkLink]);

    return warningText && !hidden ? (
        <div
            className={classNames(' text-sm text-warning', {
                'mt-3': name,
            })}
        >
            {warningText}
        </div>
    ) : null;
};
