/**
 * 多行输入框组件
 *
 */
import {forwardRef, useCallback, useMemo, useState} from 'react';
import {Input, ConfigProvider, PopoverProps} from 'antd';
import {TextAreaProps} from 'antd/es/input';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {inputToken} from '@/styles/component-token';
import StyledPopover from '../Popover/StyledPopover';

const StyledTextarea = styled(Input.TextArea)`
    .ant-input-suffix .ant-input-data-count {
        bottom: 9px;
        right: 12px;
        padding-left: 5px;
        color: #848691;
    }

    &.ant-input-textarea-affix-wrapper.ant-input-affix-wrapper {
        padding-bottom: 30px;
        padding-right: 3px;

        textarea {
            padding-top: 0px;
            margin-top: 9px;
            color: inherit !important;
            cursor: inherit;
        }

        textarea::-webkit-scrollbar {
            background: transparent;
            width: 4px;
        }

        textarea::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 5px;
        }
    }

    &.ant-input {
        &::-webkit-scrollbar {
            background: transparent;
            width: 4px;
        }
        &::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 5px;
        }
    }
`;

interface Props extends TextAreaProps {
    // 聚焦时右侧显示气泡，默认气泡内容为 placeholder
    showFocusPopover?: boolean;
    // 聚焦时右侧显示的气泡 props
    popoverProps?: PopoverProps;
}

// 在其他组件中使用 styled 修改样式时，需要使用 forwardRef，否则控制台报错
const Textarea = forwardRef(
    ({className, placeholder, onFocus, onBlur, showFocusPopover, popoverProps, ...props}: Props) => {
        // 存储聚焦状态，用于控制右侧气泡的显示隐藏和 placeholder 的显示隐藏
        const [isFocus, setIsFocus] = useState(false);

        // 处理 focus 事件，设置 isFocus 状态
        const handleOnFocus = useCallback(
            (e: React.FocusEvent<HTMLTextAreaElement>) => {
                setIsFocus(true);
                onFocus?.(e);
            },
            [onFocus]
        );

        // 处理 blur 事件，设置 isFocus 状态
        const handleOnBlur = useCallback(
            (e: React.FocusEvent<HTMLTextAreaElement>) => {
                setIsFocus(false);
                onBlur?.(e);
            },
            [onBlur]
        );

        // 缓存 Textarea 组件实例
        const textArea = useMemo(
            () => (
                <StyledTextarea
                    className={classNames('caret-[#4e6ef2]', className)}
                    placeholder={showFocusPopover && isFocus ? '' : placeholder}
                    onFocus={handleOnFocus}
                    onBlur={handleOnBlur}
                    {...props}
                />
            ),
            [className, showFocusPopover, isFocus, placeholder, handleOnFocus, handleOnBlur, props]
        );

        return (
            <ConfigProvider
                theme={{
                    components: {
                        Input: inputToken,
                    },
                }}
            >
                {showFocusPopover ? (
                    <StyledPopover
                        placement="rightTop"
                        content={<div className="w-[276px] text-justify text-sm">{placeholder}</div>}
                        trigger="focus"
                        {...popoverProps}
                    >
                        {textArea}
                    </StyledPopover>
                ) : (
                    textArea
                )}
            </ConfigProvider>
        );
    }
);

export default Textarea;
