/**
 * 搜索框
 *
 */
import classNames from 'classnames';
import {Button, ConfigProvider, Input} from 'antd';
import {SearchProps} from 'antd/es/input';
import styled from '@emotion/styled';
import {inputSearchToken} from '@/styles/component-token';

const StyledInputSearch = styled(Input.Search)`
    .ant-input:hover + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {
        border-inline-start-color: transparent !important;
    }

    .ant-input:focus + .ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {
        border-inline-start-color: transparent !important;
    }

    &.ant-input-search .ant-input:hover,
    &.ant-input-search .ant-input:focus {
        border-width: 0 !important;
    }

    &.ant-input-search
        > .ant-input-group
        > .ant-input-group-addon:last-child
        .ant-input-search-button:not(.ant-btn-primary) {
        color: #1e1f24 !important;

        &:hover {
            color: #5562f2 !important;
        }
    }
`;

export default function Search({className, ...props}: SearchProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: inputSearchToken,
                },
            }}
        >
            <StyledInputSearch
                className={classNames('rounded-[9px] bg-colorBgInput', className)}
                enterButton={
                    <Button type="link" icon={<span className="iconfont icon-search text-sm font-normal" />} />
                }
                {...props}
            />
        </ConfigProvider>
    );
}
