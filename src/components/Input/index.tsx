/**
 * 单行输入框组件
 *
 */
import {forwardRef} from 'react';
import classNames from 'classnames';
import {Input as AntInput, ConfigProvider, InputProps} from 'antd';
import styled from '@emotion/styled';
import {inputToken} from '@/styles/component-token';

const StyledInput = styled(AntInput)`
    .ant-input-suffix .ant-input-show-count-suffix {
        color: #848691 !important;
    }
`;

// 在其他组件中使用 styled 修改样式时，需要使用 forwardRef，否则控制台报错
// 4.22 forwardRef不加ref 控制台会报错
const Input = forwardRef(({className, ...props}: InputProps, ref: any) => {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: inputToken,
                },
            }}
        >
            <StyledInput ref={ref} className={classNames('caret-[#4e6ef2]', className)} {...props} />
        </ConfigProvider>
    );
});

export default Input;
