/**
 * @file 带底部内容和边框的输入框
 * <AUTHOR>
 * @date 2024/08/21
 */

import {TextAreaProps} from 'antd/es/input';
import omit from 'lodash/omit';
import {ReactNode} from 'react';
import {ConfigProvider} from 'antd';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import Textarea from '@/components/Input/Textarea';

interface TextAreaWithFooterProps extends TextAreaProps {
    children: ReactNode;
    outerClassName?: string;
}

export default function TextareaWithFooter(props: TextAreaWithFooterProps) {
    const {readonly} = usePromptEditContext();

    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: {
                        paddingBlock: 0,
                    },
                },
            }}
        >
            <div
                style={{
                    borderRadius: 9,
                    paddingTop: 4,
                    backgroundColor: readonly ? '#F5F6F9' : '#F5F6FA',
                }}
                className={props.outerClassName}
            >
                <Textarea {...omit(props, 'children')} />
                {props.children}
            </div>
        </ConfigProvider>
    );
}
