import {css} from '@emotion/css';
import {useMemo} from 'react';

export interface ScrollStyleProps {
    // 滑块宽度
    scrollbarWidth?: number;
    // 滑块颜色
    scrollbarColor?: string;
    // 滑块轨道颜色，默认透明
    trackColor?: string;
    // X 轴方向是否滚动
    scrollX?: boolean;
    // Y 轴方向是否滚动
    scrollY?: boolean;
    // 是否自动隐藏滑动条（悬浮可见）
    autoHideScrollbar?: boolean;
    // 是否隐藏滑块轨道（防止悬浮显示滚动条时盒子内容区域被缩短，若不自动隐藏滑动条则此属性无效）
    hideTrack?: boolean;
}

export const useScrollStyle = ({
    scrollbarWidth = 8,
    scrollX = false,
    scrollY = false,
    trackColor = 'rgba(0, 0, 0, 0)',
    scrollbarColor = '#d3d9e6',
    hideTrack = true,
    autoHideScrollbar = false,
}: ScrollStyleProps) => {
    const style = useMemo(
        // eslint-disable-next-line complexity
        () => css`
            --scrollbar-width: ${scrollbarWidth}px;
            overflow-x: ${(!autoHideScrollbar || hideTrack) && scrollX ? 'auto' : 'hidden'};
            overflow-y: ${(!autoHideScrollbar || hideTrack) && scrollY ? 'auto' : 'hidden'};
            &::-webkit-scrollbar {
                width: ${!autoHideScrollbar || hideTrack ? `var(--scrollbar-width)` : '0px'};
                height: ${!autoHideScrollbar || hideTrack ? `var(--scrollbar-width)` : '0px'};
            }
            &::-webkit-scrollbar-thumb {
                background: ${autoHideScrollbar ? 'rgba(0, 0, 0, 0)' : scrollbarColor};
                border-radius: calc(var(--scrollbar-width) / 2);
            }
            &::-webkit-scrollbar-corner {
                background-color: ${trackColor};
            }
            &::-webkit-scrollbar-track {
                background-color: ${trackColor};
            }
            &:hover {
                overflow-x: ${scrollX ? 'auto' : 'hidden'};
                overflow-y: ${scrollY ? 'auto' : 'hidden'};
                &::-webkit-scrollbar-thumb {
                    background: ${scrollbarColor};
                }
                &::-webkit-scrollbar {
                    width: var(--scrollbar-width);
                    height: var(--scrollbar-width);
                }
            }
        `,
        [autoHideScrollbar, hideTrack, scrollX, scrollY, scrollbarColor, scrollbarWidth, trackColor]
    );
    return style;
};
