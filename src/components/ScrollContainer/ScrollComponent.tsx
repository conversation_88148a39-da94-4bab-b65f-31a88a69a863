import classNames from 'classnames';
import {ScrollStyleProps, useScrollStyle} from './useScrollStyle';

type Props = ScrollStyleProps & React.HTMLAttributes<HTMLDivElement>;

export const ScrollContainer: React.FC<Props> = ({
    scrollbarWidth = 8,
    scrollX = false,
    scrollY = false,
    trackColor = 'rgba(0, 0, 0, 0)',
    scrollbarColor = '#d3d9e6',
    hideTrack = true,
    autoHideScrollbar = false,
    className,
    ...props
}) => {
    const ScrollStyle = useScrollStyle({
        scrollbarWidth,
        scrollX,
        scrollY,
        trackColor,
        scrollbarColor,
        hideTrack,
        autoHideScrollbar,
    });
    return <div className={classNames(ScrollStyle, className)} {...props} />;
};
