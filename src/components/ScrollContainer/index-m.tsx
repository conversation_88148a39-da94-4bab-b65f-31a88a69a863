/**
 * @file 修改滚动容器内的滚动条样式
 * <AUTHOR>
 *
 */

import styled from '@emotion/styled';

export const ScrollContainerM = styled.div`
    /* 隐藏滚动条*/
    &::-webkit-scrollbar {
        display: none;
    }
    overscroll-behavior: none;
    button,
    div {
        // 移动端不能使用 cursor: pointer，会导致点击态有浅蓝色背景
        cursor: default !important;
    }
    -webkit-tap-highlight-color: transparent !important;
`;
