import {Gutter} from 'antd/es/grid/row';

// 反馈选项按钮的内容
export interface FeedbackOption<T> {
    value: T;
    label?: React.ReactNode;
}

// 反馈选项按钮的配置
export interface FeedbackOptionConfig<T> {
    // 选项的栅格, 决定列数
    span: number;
    gutter: Gutter;
    // 对选项按钮的样式定制
    optionClassName?: string;
    options: Array<FeedbackOption<T>>;
}

// 反馈表单值
export interface FormValue<T> {
    reasonList: T[];
    detail: string;
    pictures?: string[];
}

// 反馈表单组件参数
export interface FeedbackFormProps<T> {
    className?: string;
    onFinish?: (values: FormValue<T>) => Promise<boolean>;
    onCancel?: () => void;
    // 反馈选项props
    optionsProps?: {
        title: string;
        // 是否必填(标题显示星号)
        required: boolean;
        // 选择的选项变化时的回调
        onSelectedChange?: (selectedOptions: T[]) => void;
    } & FeedbackOptionConfig<T>;

    // 详细描述 部分的 props
    detailProps?: {
        title: string;
        // 文本框placeholder
        placeHolder: string;
        // 是否必填(标题显示星号)
        required: boolean;
        maxLength?: number;
        // 文本框的样式定制
        textareaClassName?: string;
    };

    // 图片上传 部分的 props
    imageUploadProps?: {
        // 是否显示图片上传区域
        enabled: boolean;
        // 标题
        title?: string;
        // 最大上传数量
        maxCount?: number;
        // 上传接口
        uploadAction?: string;
        // 文件变化回调
        onChange?: (urlList: string[]) => void;
    };
}
