/**
 * @file 反馈popover样式
 * <AUTHOR>
 */
import {Popover, PopoverProps} from 'antd';
import {css} from '@emotion/css';
import classNames from 'classnames';

const FeedbackPopoverStyle = css`
    .ant-popover-content {
        border-radius: 0.5rem;
        background: #fff;
        max-width: 60rem;
        .ant-popover-inner {
            box-shadow: 0px 30px 200px 0px rgba(29, 34, 82, 0.2);
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #fff;
            color: #000;
        }
    }
`;

export default function FeedBackPopover({rootClassName, ...props}: PopoverProps) {
    return <Popover rootClassName={classNames(FeedbackPopoverStyle, rootClassName)} {...props} />;
}
