/**
 * 下拉选择
 *
 */
import {Select as AntSelect, ConfigProvider, SelectProps} from 'antd';
import {SelectToken} from 'antd/es/select/style/token';
import {selectToken} from '@/styles/component-token';

export default function Select({themeToken, ...props}: SelectProps & {themeToken?: Partial<SelectToken>}) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Select: {...selectToken, ...themeToken},
                },
            }}
        >
            <AntSelect {...props} />
        </ConfigProvider>
    );
}
