/**
 * @file 选择器（底部弹出选择器） - 移动端
 * <AUTHOR>
 */
import {CheckList} from 'antd-mobile';
import type {CheckListValue} from 'antd-mobile/es/components/check-list/check-list';
import {ReactNode, useCallback, useState} from 'react';
import {Empty, Tag} from 'antd';
import {css} from '@emotion/css';
import classNames from 'classnames';
import PopupM, {PopupMProps} from '@/components/mobile/Popup';
import {selectToken} from '@/styles/component-token';

interface Option {
    label: string;
    value: any;
    disabled?: boolean;
}

export interface Props {
    // 选择器容器 class
    className?: string;
    // 选择器值
    value?: CheckListValue | CheckListValue[];
    // 模式，multiple 多选，其他为单选
    mode?: 'multiple';
    // 占位符
    placeholder?: string;
    // 选择器后缀图标 默认 Down
    suffixIcon?: string;
    // 选项
    options?: Option[];
    // 选项列表中的选中图标
    menuItemSelectedIcon?: ReactNode;
    // 选项为空时显示的内容
    notFoundContent?: ReactNode;
    // 弹出的 popup 组件的属性
    popupProps?: PopupMProps;
    // 自定义渲染选项列表
    optionRender?: (item: Option, {index}: {index: number}) => React.ReactNode;
    // 自定义渲染选中的 tag
    tagRender?: (item: Option) => React.ReactNode;
    // 是否禁用
    disabled?: boolean;
    // 选项值改变事件
    onChange?: (val: any[] | any) => void;
}

const MobileSelect = css`
    &.mobile-select-container {
        border-radius: ${selectToken.borderRadius}px;
        background: ${selectToken.selectorBg};
        border-width: ${selectToken.lineWidth}px;
        min-height: ${selectToken.controlHeight}px;
        line-height: 22px;
        padding: 4px 4px 4px 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &.disabled {
            background: ${selectToken.colorBgContainerDisabled};
        }

        .mobile-select-placeholder {
            color: ${selectToken.colorTextQuaternary};
        }
        .mobile-select-value {
            margin-left: 8px;
        }
    }
`;

const CheckListStyle = css`
    &.adm-list {
        .adm-list-body {
            border-width: 0;
            max-height: 50vh;
            overflow-y: scroll;
        }
        .adm-list-item {
            padding-left: 18px;
            font-size: 16px;

            .adm-list-item-content {
                padding-right: 18px;
                border-top-width: 0;
            }
        }
    }
`;

export default function SelectM({
    className,
    value,
    mode,
    placeholder,
    suffixIcon,
    options,
    menuItemSelectedIcon,
    notFoundContent,
    popupProps,
    tagRender,
    optionRender,
    onChange,
    disabled,
}: Props) {
    // 是否多选
    const isMultiple = mode === 'multiple';
    // 选中值，CheckList 组件的 value 为数组，因此针对单选和多选做兼容处理
    const checkListValue = value ? (Array.isArray(value) ? value : [value]) : [];
    // 弹窗开/关状态
    const [open, setOpen] = useState(false);

    // 打开弹窗
    const openPopup = useCallback(() => {
        !disabled && setOpen(true);
    }, [disabled]);

    // 关闭弹窗
    const closePopup = useCallback(() => {
        setOpen(false);
    }, []);

    // 选项值改变事件
    const onCheckListChange = useCallback(
        (val: CheckListValue[]) => {
            if (isMultiple) {
                onChange?.(val);
            } else {
                onChange?.(val?.[0]);
                closePopup();
            }
        },
        [isMultiple, onChange, closePopup]
    );

    return (
        <div className={classNames(MobileSelect, 'mobile-select-container', {disabled}, className)}>
            <div
                key="select"
                className="mobile-select-selector flex w-full items-center justify-between"
                onClick={openPopup}
            >
                {checkListValue?.length ? (
                    <span>
                        {checkListValue.map(item => {
                            const option = options?.find(({value}) => value === item);
                            if (!isMultiple) {
                                return (
                                    <span key="single-select-value" className="mobile-select-value">
                                        {option?.label || item}
                                    </span>
                                );
                            }

                            if (tagRender) {
                                return tagRender(option || {label: item as string, value: item});
                            }

                            return (
                                <Tag bordered={false} key={item}>
                                    {option?.label || item}
                                </Tag>
                            );
                        })}
                    </span>
                ) : (
                    <span key="placeholder" className="mobile-select-placeholder text-gray-tertiary">
                        {placeholder}
                    </span>
                )}
                <span
                    key="icon"
                    className={classNames('iconfont', `icon-${suffixIcon || 'Down'}`, 'mr-2 text-black')}
                />
            </div>
            <PopupM
                key="popup"
                visible={open}
                onClose={closePopup}
                closeOnMaskClick
                title={popupProps?.title || '选项'}
                headerClassName="leading-[18px] -mb-[6px]"
                showCloseButton
                bodyStyle={{
                    paddingLeft: 0,
                    paddingRight: 0,
                }}
                {...popupProps}
            >
                {options?.length ? (
                    <CheckList
                        className={classNames(CheckListStyle)}
                        multiple={isMultiple}
                        value={checkListValue}
                        activeIcon={menuItemSelectedIcon || <span className="iconfont icon-check text-primary" />}
                        onChange={onCheckListChange}
                    >
                        {options?.map((item, index) => (
                            <CheckList.Item key={item.value} value={item.value} disabled={item.disabled}>
                                {optionRender ? optionRender(item, {index}) : item.label}
                            </CheckList.Item>
                        ))}
                    </CheckList>
                ) : (
                    <div className="mt-[25%] translate-y-[-50%]">
                        {notFoundContent || (
                            <Empty
                                className="p-3"
                                imageStyle={{height: 44}}
                                description={<span className="p-3 text-gray-tertiary">暂无数据</span>}
                            />
                        )}
                    </div>
                )}
            </PopupM>
        </div>
    );
}
