import {ReactNode, useCallback, useEffect, useRef, useState, FC, CSSProperties} from 'react';

interface CountdownProps {
    countdownLabel: (time: number) => ReactNode;
    label: ReactNode;
    countdownLimit: number;
    // 一开始就倒计时，还是点击后再倒计时
    invoke?: boolean;
    // 倒计时结束后的回调
    onFinish?: () => void;
    style?: CSSProperties;
}

const CountdownText: FC<CountdownProps> = props => {
    const [remainTime, setRemain] = useState(props.countdownLimit);
    const timerRef = useRef<NodeJS.Timer>();
    const {countdownLabel, label, invoke, onFinish, style} = props;

    useEffect(() => {
        timerRef.current && clearInterval(timerRef.current);
        return () => {
            timerRef.current && clearInterval(timerRef.current);
        };
    }, []);

    useEffect(() => {
        if (remainTime === 0) {
            clearInterval(timerRef.current as unknown as number);
            onFinish?.();
        }
    }, [remainTime, onFinish]);

    const startCountdown = useCallback(() => {
        timerRef.current = setInterval(
            () =>
                setRemain(time => {
                    const update = time - 1;
                    return update;
                }),
            1000
        );
    }, []);

    useEffect(() => {
        if (invoke) {
            startCountdown();
        }
    }, [invoke, startCountdown]);

    return <span style={style}>{remainTime === 0 ? label : countdownLabel(remainTime)}</span>;
};

export default CountdownText;
