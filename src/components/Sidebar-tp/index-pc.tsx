import {Link} from 'react-router-dom';
import {LingJingIcon} from '@/dicts/index';
import urls from '@/links/index-tp';
import UserAvatar from '@/components/UserAvatar';

export default function Left() {
    return (
        <div className="flex h-full flex-col justify-between bg-white px-4 py-4">
            <Link className="mb-[17px]" to={urls.root.raw()}>
                <img src={LingJingIcon} className="inline-block h-9" alt="logo" />
            </Link>
            <div className="mb-auto flex flex-col justify-center gap-6">
                <div className="flex h-10 cursor-pointer items-center rounded-[9px] bg-[#E5EAFF] p-[10px] text-sm font-medium text-[#1A46FF]">
                    <span className="iconfont icon-a-discoverycenter"></span>
                    <span className="ml-[10px] font-medium">代运营中心</span>
                </div>
            </div>

            <div className="w-full pb-1">
                <UserAvatar />
            </div>
        </div>
    );
}
