import classNames from 'classnames';

type TipsType = 'success' | 'primary' | 'error';

interface Props {
    actionDisable?: boolean;
    children?: React.ReactNode;
    className?: string;
    type?: TipsType;
    action?: React.ReactNode;
    actionName?: React.ReactNode;
    onAction?: () => void;
}

const tipConfig: Record<TipsType, any> = {
    success: {
        color: '#39B362',
        icon: 'check-circle-fill',
    },
    primary: {
        color: '#5562F2',
        icon: 'tip',
    },
    error: {
        color: '#F73131',
        icon: 'tip',
    },
};

const StaticTips: React.FC<Props> = ({
    actionDisable,
    children,
    className,
    type = 'primary',
    actionName = '刷新',
    onAction,
    action,
}) => {
    const config = tipConfig[type];
    return (
        <div className="flex">
            <div
                className={classNames(
                    'flex gap-2 overflow-hidden rounded-lg  px-4 py-2 text-sm',
                    // 旧样式
                    {
                        'bg-[#E6F7F2]': type === 'success',
                        'bg-[#ECEFFE]': type === 'primary',
                        'bg-[#FFF1F1]': type === 'error',
                    },
                    // 新样式，暂不采用
                    // 'bg-opacity-[0.04]',
                    // {
                    //     'bg-[#39B362]': type === 'success',
                    //     'bg-[#5562F2]': type === 'primary',
                    //     'bg-[#F73131]': type === 'error',
                    // },
                    className
                )}
                style={{backgroundColor: config.color + '34'}}
            >
                <span
                    className={classNames('iconfont', {
                        'icon-check-circle-fill text-[#39B362]': type === 'success',
                        'icon-tip-fill text-[#5562F2]': type === 'primary',
                        'icon-wenziqingchu text-[#F73131]': type === 'error',
                    })}
                    style={{color: config.color}}
                />
                {children}
                {action || (
                    <div
                        className={classNames(
                            'cursor-pointer pl-4',
                            // 旧样式
                            'text-[#4F6FF9]',
                            {
                                'opacity-40': actionDisable,
                            }
                            // 新样式，暂不采用
                            // {
                            //     'text-[#39B362]': type === 'success',
                            //     'text-[#5562F2]': type === 'primary',
                            //     'text-[#F73131]': type === 'error',
                            // }
                        )}
                        onClick={onAction}
                        style={{color: config.color}}
                    >
                        {actionName}
                    </div>
                )}
            </div>
        </div>
    );
};

export default StaticTips;
