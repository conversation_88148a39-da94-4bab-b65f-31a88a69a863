import classNames from 'classnames';

interface Props {
    disable?: boolean;
    onClick?: () => void;
    children?: React.ReactNode;
    showButton?: boolean;
    className?: string;
    hideIcon?: boolean;
    bgColor?: string;
    iconfont?: string;
}

const StaticTips: React.FC<Props> = ({
    disable,
    onClick,
    children,
    showButton = false,
    className,
    iconfont = 'icon-tip',
    hideIcon = false,
    bgColor = 'bg-[#E8EDFF]',
}) => {
    return (
        <div className={classNames('flex overflow-hidden rounded-lg px-4 py-2 text-sm', className, bgColor)}>
            {!hideIcon && <span className={classNames('iconfont mr-[.625rem] text-[#4F6FF9]', iconfont)} />}
            {children}
            {showButton && (
                <div
                    className={classNames('w-14 cursor-pointer pl-6 text-primary', {
                        'opacity-40': disable,
                    })}
                    onClick={onClick}
                >
                    刷新
                </div>
            )}
        </div>
    );
};

export default StaticTips;
