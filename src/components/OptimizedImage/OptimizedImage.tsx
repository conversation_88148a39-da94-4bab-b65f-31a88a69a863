/**
 * <AUTHOR>
 * @file
 */

import {convertToAutoFormat} from '@/utils/processImage';

// 新增优化图片组件
export const OptimizedImage = ({
    src,
    className,
    isPriorityImage,
    ...props
}: {
    src: string;
    className?: string;
    isPriorityImage?: boolean;
} & React.ImgHTMLAttributes<HTMLImageElement>) => (
    <img
        className={className}
        src={convertToAutoFormat(src)}
        loading={isPriorityImage ? 'eager' : 'lazy'}
        fetchPriority={isPriorityImage ? 'high' : 'low'}
        {...props}
    />
);
