import {useEffect} from 'react';
import isNumber from 'lodash/isNumber';
import styled from '@emotion/styled';
import {ConfigProvider, ThemeConfig} from 'antd';
import classNames from 'classnames';
import Icon from '../Icon';
import Popover, {type StyledPopoverProps} from './StyledPopover';

export interface CustomPopoverProps extends StyledPopoverProps {
    // 关闭按钮点击的回调
    onClose?: (event?: React.MouseEvent) => void;
    // content 的根元素参数，需要特殊自定义功能时可以使用
    contentRootProps?: React.HTMLAttributes<HTMLDivElement>;
    // 底部功能区插槽
    footer?: (() => React.ReactNode) | React.ReactNode;
    // 默认传入 onClose 就会显示关闭图标，通过该参数可以禁止这一行为
    hiddenCloseIcon?: boolean;
    // 是否开启定时退场时机，若填数字，则表示定时时间（单位-秒），默认60秒
    exitTime?: boolean | number;
}

// 为保证组件的拓展性，此处不使用 tailwind 样式，方便进行覆盖
const PopoverWrapper = styled.div`
    display: flex;
    font-family: PingFang SC;
    max-width: 276px;
    // 存在标题时，关闭按钮放在标题容器里，最外层的关闭按钮需要隐藏掉
    &:has(.popover-title-container) {
        & > .popover-close-container {
            display: none;
        }
    }
    .popover-container {
        .popover-title-container {
            display: flex;
            .popover-title {
                margin-right: auto;
                font-weight: 600;
            }
        }
    }
    .popover-close-container {
        .popover-close {
            cursor: pointer;
        }
    }
    .popover-footer-container {
        text-align: right;
        margin-top: 6px;
    }
}`;

const defaultThemeConfig: ThemeConfig = {
    components: {
        Button: {
            borderRadius: 999,
        },
    },
};

const PopoverWrapperWithTheme: React.FC<React.HTMLAttributes<HTMLDivElement>> = props => {
    return (
        <ConfigProvider theme={defaultThemeConfig}>
            <PopoverWrapper {...props} />
        </ConfigProvider>
    );
};

/**
 * @description 可以关闭的弹窗
 */
const CustomPopover: React.FC<CustomPopoverProps> = ({
    content,
    onClose,
    contentRootProps,
    title,
    footer,
    type,
    hiddenCloseIcon = false,
    exitTime,
    open,
    ...props
}) => {
    const showCloseIcon = !!onClose && !hiddenCloseIcon;

    // 定时关闭气泡
    useEffect(() => {
        const shouldStartTimeOut = !!exitTime;
        if (!shouldStartTimeOut || !open) return;

        const timer = setTimeout(
            () => {
                onClose && onClose();
            },
            (isNumber(exitTime) ? exitTime : 60) * 1000
        );

        return () => clearTimeout(timer);
    }, [exitTime, onClose, open]);

    return (
        <Popover
            content={
                <PopoverWrapperWithTheme className="popover-wrapper" {...contentRootProps}>
                    <div className="popover-container">
                        {title && (
                            <div className="popover-title-container">
                                <div className="popover-title">{typeof title === 'function' ? title() : title}</div>
                                {showCloseIcon && (
                                    <div className="popover-close-container">
                                        <Icon
                                            name="close"
                                            hoverStyle={false}
                                            className="popover-close"
                                            onClick={onClose}
                                        />
                                    </div>
                                )}
                            </div>
                        )}
                        <div className="popover-content">{typeof content === 'function' ? content() : content}</div>
                        {footer && (
                            <div className="popover-footer-container">
                                {typeof footer === 'function' ? footer() : footer}
                            </div>
                        )}
                    </div>
                    {showCloseIcon && !title && (
                        <div className="popover-close-container">
                            <Icon name="close" hoverStyle={false} className="popover-close" onClick={onClose} />
                        </div>
                    )}
                </PopoverWrapperWithTheme>
            }
            overlayClassName={classNames({
                '[&_.ant-popover-content_.ant-popover-inner]:rounded-xl': type === 'primary',
            })}
            type={type}
            open={open}
            {...props}
        />
    );
};

export default CustomPopover;
