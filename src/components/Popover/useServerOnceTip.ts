import {useEffect, useState, useCallback} from 'react';
import api from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';

/**
 * POPUP 接口信息
 *
 * 有几种控制发起请求时机的方式：
 * 1. autoFetch: 设置为 true，则组件挂载后自动发起请求
 * 2. autoFetch: 设置为 false，则组件挂载后不发起请求，需手动调用 fetchPopup 方法
 * 3. autoFetch: 值为一个可变的 boolean 类型 ref | state 变量，当 autoFetch 值为 false 时，则不发起请求，当 autoFetch 值变为 true 时，则主动发起请求
 *
 * @param {PopupName} param.name 后端的提示枚举值
 * @param {string} param.appId 智能体 id，有值时为智能体维度，没有此字段或值为 '' || null 时，是以开发者维度
 * @param {boolean} param.autoView 是否自动打开气泡 默认 false，true 时 后端在 /popup 请求后就会把此 pop 的值置为 false，false 时，需前端调用 /popup/view 后才会置为 false;
 * @param {boolean} param.autoFetch 是否自动请求后端 默认 true - 【新增】
 *
 * @returns {open, setOpen, handleClose, fetchPopup}
 * open: 气泡打开状态;
 * setOpen: 设置气泡状态;
 * handleClose: 关闭气泡，并同时后端;
 * fetchPopup: 请求 popup 状态;
 */
const useServerOnceTip = ({
    name,
    appId,
    autoView,
    autoFetch = true,
}: {
    name: PopupName;
    appId?: string;
    autoView?: boolean;
    autoFetch?: boolean;
}) => {
    const [open, setOpen] = useState(false);
    const isLogin = useUserInfoStore(store => store.isLogin);
    // 请求后端
    const fetchPopup = useCallback(() => {
        if (!isLogin) return;
        api.getPopup({name, appId, autoView})
            .then(({show}) => {
                setOpen(show);
            })
            .catch();
    }, [appId, autoView, isLogin, name]);

    useEffect(() => {
        // 当 autoFetch 为 true 时，才会去请求后端接口
        if (!autoFetch) return;
        fetchPopup();
    }, [autoFetch, fetchPopup]);

    const handleClose = useCallback(() => {
        if (!isLogin) return;
        setOpen(false);
        api.recordPopup({name, appId});
    }, [isLogin, name, appId]);

    return {
        open,
        setOpen,
        handleClose,
        fetchPopup,
    } as const;
};

export default useServerOnceTip;
