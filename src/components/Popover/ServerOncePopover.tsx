import {useCallback, useEffect} from 'react';
import isBoolean from 'lodash/isBoolean';
import {PopupName} from '@/api/beginnerGuide/interface';
import CustomPopover, {CustomPopoverProps} from '.';
import useServerOnceTip from './useServerOnceTip';

interface OncePopoverProps extends CustomPopoverProps {
    name: PopupName;
    appId?: string;
    autoView?: boolean;
    onShow?: (name: PopupName) => void;
}

/**
 * @description 通过 server 实现只展示一次的弹窗
 */
const ServerOncePopover: React.FC<OncePopoverProps> = ({
    name,
    appId,
    autoView,
    onClose,
    onShow,
    open: outOpen,
    ...props
}) => {
    const {open: innerOpen, handleClose: handleInnerClose} = useServerOnceTip({
        name,
        appId,
        autoView,
    });

    const handleClose = useCallback(() => {
        onClose && onClose();
        handleInnerClose();
    }, [handleInnerClose, onClose]);

    const open = isBoolean(outOpen) ? outOpen : true && innerOpen;

    useEffect(() => {
        if (!open) return;
        onShow && onShow(name);
    }, [name, onShow, open]);

    return <CustomPopover {...props} open={open} onClose={handleClose} />;
};

export default ServerOncePopover;
