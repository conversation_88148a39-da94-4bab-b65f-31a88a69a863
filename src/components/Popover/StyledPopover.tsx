import {useEffect} from 'react';
import {css} from '@emotion/css';
import classNames from 'classnames';
import {Popover, PopoverProps} from 'antd';
import isNumber from 'lodash/isNumber';
import {colorPrimary} from '@/styles/lingjing-light-theme';
import {usePopoverContainer} from './Container';
import {usePriorityManagerContext} from './PriorityManager';

export interface StyledPopoverProps extends PopoverProps {
    type?: 'primary' | 'default';
    // 气泡优先级，数值越高优先级越高，约定 新功能引导应为 0，操作指引应为 100，新手引导应为 200；其他数值可以灵活运用，作为类型气泡之间的优先级或者展现顺序；不传则不使用优先级系统
    priorityLevel?: number;
}

const popoverPrimaryClassName = css`
    color: white;
    .ant-popover-arrow::before {
        background-color: ${colorPrimary} !important;
        background: ${colorPrimary} !important;
    }
    .ant-popover-content {
        .ant-popover-inner {
            border-radius: 8px;
            .ant-popover-inner-content {
                color: white !important;
            }
        }
    }
`;

/**
 * @description 封装 antd 原生的 Popover，封装样式（目前有蓝色底和白色底两种样式）以及容器获取逻辑；
 */
const StyledPopover: React.FC<StyledPopoverProps> = ({
    type = 'default',
    color,
    overlayClassName,
    priorityLevel,
    open,
    ...props
}) => {
    const {currentPriorityLevel, registerHighPriority, removeHighPriority} = usePriorityManagerContext();

    // 气泡打开后注册当前气泡优先级，关闭后注销当前优先级
    useEffect(() => {
        if (!open || !isNumber(priorityLevel)) return;
        registerHighPriority(priorityLevel);
        return () => removeHighPriority(priorityLevel);
    }, [open, priorityLevel, registerHighPriority, removeHighPriority]);

    const getPopoverContainer = usePopoverContainer();
    return (
        <Popover
            getPopupContainer={getPopoverContainer}
            overlayClassName={classNames(overlayClassName, {
                [popoverPrimaryClassName]: type === 'primary',
                '[&_.ant-popover-content_.ant-popover-inner]:rounded-xl': type === 'primary',
            })}
            color={color || type === 'primary' ? colorPrimary : undefined}
            open={(!isNumber(priorityLevel) || currentPriorityLevel <= priorityLevel) && open}
            {...props}
        />
    );
};

export default StyledPopover;
