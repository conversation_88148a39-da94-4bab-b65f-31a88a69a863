import {createContext, forwardRef, useCallback, useContext, useImperativeHandle, useRef} from 'react';

const popoverContainerContext = createContext<((triggerNode: HTMLElement) => HTMLDivElement) | undefined>(undefined);
const PopoverContainerProvider = popoverContainerContext.Provider;

// eslint-disable-next-line react-refresh/only-export-components
export const usePopoverContainer = () => useContext(popoverContainerContext);

const PopoverContainer = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => {
    const innerRef = useRef<HTMLDivElement>(null);
    const getPopoverContainer = useCallback(() => {
        return innerRef.current!;
    }, []);

    // 将外部的 ref 绑定到内部的 divRef 上
    useImperativeHandle(ref, () => innerRef.current!, []);

    return (
        <PopoverContainerProvider value={getPopoverContainer}>
            <div {...props} ref={innerRef} />
        </PopoverContainerProvider>
    );
});

export default PopoverContainer;
