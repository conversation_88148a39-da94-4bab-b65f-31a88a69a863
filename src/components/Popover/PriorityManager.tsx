import {createContext, PropsWithChildren, useCallback, useContext, useMemo, useState} from 'react';

interface Context {
    // 注册一个高优气泡
    registerHighPriority: (priorityLevel: number) => void;
    // 注销一个高优气泡
    removeHighPriority: (priorityLevel: number) => void;
    // 当前最高优等级
    currentPriorityLevel: number;
}

const PriorityManagerContext = createContext<Context>({
    registerHighPriority: () => {},
    removeHighPriority: () => {},
    currentPriorityLevel: 0,
});

// eslint-disable-next-line react-refresh/only-export-components
export const usePriorityManagerContext = () => useContext(PriorityManagerContext);

type HighPriorityMap = Record<number, number>;

const PriorityManagerProvider: React.FC<PropsWithChildren> = ({children}) => {
    const [highPriorityMap, setHighPriorityMap] = useState<HighPriorityMap>({});

    // 注册一个高优气泡
    const registerHighPriority = useCallback((priorityLevel: number) => {
        setHighPriorityMap(oldHighPriorityMap => {
            return {...oldHighPriorityMap, [priorityLevel]: oldHighPriorityMap[priorityLevel] || 1};
        });
    }, []);

    // 注销一个高优气泡
    const removeHighPriority = useCallback((priorityLevel: number) => {
        setHighPriorityMap(oldHighPriorityMap => {
            return {...oldHighPriorityMap, [priorityLevel]: (oldHighPriorityMap[priorityLevel] || 1) - 1};
        });
    }, []);

    // 当前最高优等级
    const currentPriorityLevel = Math.max(
        ...Object.entries(highPriorityMap).map(([key, value]) => (value ? Number(key) : 0))
    );

    const value = useMemo(
        () => ({registerHighPriority, removeHighPriority, currentPriorityLevel}),
        [currentPriorityLevel, registerHighPriority, removeHighPriority]
    );

    return <PriorityManagerContext.Provider value={value}>{children}</PriorityManagerContext.Provider>;
};

export default PriorityManagerProvider;
