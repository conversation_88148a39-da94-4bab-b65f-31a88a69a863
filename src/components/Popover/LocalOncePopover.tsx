import {useCallback, useState} from 'react';
import CustomPopover, {CustomPopoverProps} from '.';

interface OncePopoverProps extends CustomPopoverProps {
    storageKey: string;
}

/**
 * @description 通过 localStorage 实现只展示一次的弹窗
 */
const LocalOncePopover: React.FC<OncePopoverProps> = ({storageKey, onClose, ...props}) => {
    const [open, setOpen] = useState(() => !localStorage.getItem(storageKey));
    const handleClose = useCallback(() => {
        localStorage.setItem(storageKey, '1');
        setOpen(() => !localStorage.getItem(storageKey));
        onClose && onClose();
    }, [onClose, storageKey]);

    return <CustomPopover {...props} open={open} onClose={handleClose} />;
};

export default LocalOncePopover;
