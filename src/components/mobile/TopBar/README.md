# TopBar 顶部导航

参数：

| 属性名      | 说明                        | 类型        | 默认值   |
|----------|---------------------------|-----------|-------|
| leftNode | 左侧功能                   | ReactNode | ''    |
| title    | 页面标题，可缺省                   | string    | ''    |
| rightNode| 左侧功能                   | ReactNode | ''    |



# 代码示例
```jsx
<TopBar
    leftNode={
        <div className="flex">
            <Left />
        </div>
    }
    title="我的智能体"
    rightNode={
        <div className="flex">
            <div
                className="rounded-[9px] px-3 py-2 font-medium leading-none text-primary "
                style={{
                    background: 'linear-gradient(#DFEEFF, #E6E7FF)',
                }}
            >
                <span className="iconfont icon-plus"></span>
                创建
            </div>
        </div>
    }
/>
```
