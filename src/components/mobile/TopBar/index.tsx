/**
 * @file 顶 bar
 * <AUTHOR>
 */

import classNames from 'classnames';

export default function TopBarM({
    leftNode,
    rightNode,
    title,
    height = 40,
    rootClassName,
    titleClassName,
}: {
    leftNode?: React.ReactNode;
    rightNode?: React.ReactNode;
    title?: string;
    height?: number;
    rootClassName?: string;
    titleClassName?: string;
}) {
    return (
        <div className={classNames('sticky top-0 z-20 bg-gray-bg-base px-3 pb-1.5 pt-[5px]', rootClassName)}>
            <div
                className="flex items-center justify-between"
                style={{
                    height: `${height}px`,
                }}
            >
                <div className={classNames({'flex-1': title})}>{leftNode}</div>
                {title && (
                    <div className="flex flex-1 items-center justify-center">
                        <span className={classNames('text-black-base text-[18px] font-medium', titleClassName)}>
                            {title}
                        </span>
                    </div>
                )}
                <div className={classNames('flex justify-end', {'flex-1': title})}>{rightNode}</div>
            </div>
        </div>
    );
}
