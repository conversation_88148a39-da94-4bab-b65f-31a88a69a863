/**
 * @file 顶 bar 创建按钮
 * <AUTHOR>
 */

import {useCallback} from 'react';
import {Space} from 'antd';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import styled from '@emotion/styled';
import urls from '@/links';
import useLogin from '@/components/Login/hooks/useLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useAgentNumModal} from '@/components/Sidebar/hooks/useAgentNum';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';

const StyledDiv = styled.div`
    border-radius: 99px;
    background-color: #5562f2;
    padding: 8px 12px;
    font-weight: 500;
    ine-height: 1;
    color: #ffffff;
    &:active {
        opacity: 0.2;
    }
    &.disabled {
        opacity: 0.4 !important;
    }
`;

export default function CreateBtn() {
    const navigate = useNavigate();
    const {loginCheck} = useLogin();
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const {setOpenAgentWarningModal, appNum, appLimit, AgentNumModal, Modalprops} = useAgentNumModal();

    const allowCreate = !userInfoData?.hasTpProxy;

    const clickHandler = useCallback(() => {
        if (!allowCreate) {
            showDisallowOperateToast();
            return;
        }

        if (appNum! >= appLimit!) {
            setOpenAgentWarningModal(true);
            return;
        }

        if (isLogin) {
            navigate(urls.agentPromptQuickStart.raw());
            return;
        }

        loginCheck();
    }, [allowCreate, appNum, appLimit, isLogin, loginCheck, setOpenAgentWarningModal, navigate]);

    return (
        <StyledDiv className={classNames('create-agent-btn-wise', allowCreate ? '' : 'disabled')}>
            <div onClick={clickHandler} className="text-sm leading-none">
                <Space size={[3, 0]}>
                    <span className="iconfont icon-creat h-[12px] w-[12px]"></span>
                    <span>创建</span>
                </Space>
            </div>
            <AgentNumModal {...Modalprops} />
        </StyledDiv>
    );
}
