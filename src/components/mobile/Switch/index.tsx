export default function SwitchM(props: {
    value?: number | string;
    options: Array<{label: string; value: string | number; onClick?: () => void}>;
    onChange?: (payload: string | number) => void;
    disabled?: boolean;
}) {
    return (
        <div className="inline-flex justify-end gap-1 rounded-[20px] bg-gray-bg-base p-[1px]">
            {props.options.map(option => (
                <div
                    className={`rounded-[20px] px-3 py-[6px] ${
                        props.value === option.value ? 'bg-white font-semibold text-primary' : 'text-gray-secondary'
                    } ${props.disabled ? 'opacity-40' : ''}`}
                    key={option.value}
                    onClick={() => {
                        if (props.disabled) return;
                        props.onChange && props.onChange(option.value);
                        option.onClick && option.onClick();
                    }}
                >
                    {option.label}
                </div>
            ))}
        </div>
    );
}
