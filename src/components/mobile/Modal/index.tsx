import {css} from '@emotion/css';
import {Config<PERSON>rovider, Modal, ModalProps} from 'antd';
import {JSX} from 'react';

const customStyle = css`
    .ant-modal-content {
        padding: 0 !important;
        .ant-btn:not(.ant-btn-icon-only) > .ant-btn-icon:not(:last-child) {
            margin-inline-end: 8px;
        }
    }
    .ant-btn-text {
        font-weight: 400;
        border-radius: 0;
        border-right: 0.5px solid #e8e8e8;
    }
`;

export default function ModalM(props: JSX.IntrinsicAttributes & ModalProps) {
    return (
        <ConfigProvider
            theme={{
                token: {
                    controlHeight: 51,
                    marginXS: 0,
                },
                components: {
                    Button: {
                        contentFontSize: 18,
                        fontWeight: 500,
                    },
                },
            }}
        >
            <Modal
                keyboard={false}
                closable={false}
                width={332}
                styles={{
                    body: {
                        flex: 1,
                        minHeight: '67px',
                        fontSize: '16px',
                        paddingTop: '27px',
                        paddingBottom: '12px',
                    },
                    footer: {borderTop: '0.5px solid #e8e8e8', height: '51px', display: 'flex'},
                    mask: {backgroundColor: 'rgba(0,0,0,0.6)'},
                }}
                maskClosable={false}
                centered
                zIndex={1200}
                className={customStyle}
                okButtonProps={{
                    type: 'link',
                    block: true,
                }}
                cancelButtonProps={{
                    type: 'text',
                    block: true,
                }}
                {...props}
            >
                {props.children}
            </Modal>
        </ConfigProvider>
    );
}
