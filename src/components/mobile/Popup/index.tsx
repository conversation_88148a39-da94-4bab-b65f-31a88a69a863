/**
 * @file 弹出组件
 * <AUTHOR>
 */

import {ReactNode} from 'react';
import {Button, ButtonProps} from 'antd';
import Popup, {PopupProps} from 'antd-mobile/es/components/popup';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import classNames from 'classnames';
import {Variant} from './constants';

interface ComponentProps {
    variant?: Variant;
    title?: string | ReactNode;
    prefix?: ReactNode;
    suffix?: ReactNode;
    confirmText?: string | ReactNode;
    cancelText?: string | ReactNode;
    onConfirm?: () => void;
    onCancel?: () => void;
    disabled?: boolean;
    background?: string;
    confirmButtonProps?: ButtonProps;
    cancelButtonProps?: ButtonProps;
    headerClassName?: string;
}

export const StyledButton = styled(Button)`
    &.ant-btn.ant-btn-loading {
        background-color: #bbc0fa;
        opacity: 1;
    }

    &.ant-btn.ant-btn-default {
        background-color: #ebecfd !important;
        border-color: #ebecfd;
        color: #5562f2;
    }

    &.ant-btn.ant-btn-primary:disabled {
        background-color: rgba(187, 192, 250, 1) !important;
    }
`;

const StyledPopupClass = css`
    .adm-popup-close-icon {
        right: 18px !important;
        top: 13px !important;
        font-size: 20px;
        color: #b7b9c1;
    }
`;

const Header = (props: Pick<ComponentProps, 'title' | 'prefix' | 'suffix' | 'headerClassName'>) => {
    const {title, prefix, suffix, headerClassName} = props;
    return (
        <header>
            {prefix && <div className="absolute top-[16px]">{prefix}</div>}

            {title && (
                <div
                    className={classNames(
                        'h-40px w-full flex-shrink-0 pb-[24px] pt-[18px] text-center text-[18px] font-semibold',
                        headerClassName
                    )}
                >
                    {title}
                </div>
            )}

            {suffix && <div className="absolute right-[0] top-[13px]">{suffix}</div>}
        </header>
    );
};

export type PopupMProps = ComponentProps & PopupProps;

export default function PopupM(props: PopupMProps) {
    const {
        visible,
        variant,
        title,
        prefix,
        suffix,
        headerClassName,
        confirmText,
        cancelText,
        onConfirm,
        onCancel,
        disabled,
        confirmButtonProps,
        cancelButtonProps,
        background = 'white',
    } = props;

    const FullScreenStyle = {
        height: '100%',
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        paddingLeft: 0,
        paddingRight: 0,
    };

    return (
        <Popup
            {...props}
            visible={visible}
            bodyStyle={{
                height: 'auto',
                overflow: 'hidden',
                borderTopLeftRadius: '21px',
                borderTopRightRadius: '21px',
                minHeight: 'auto',
                paddingLeft: variant === Variant.Fullscreen ? 0 : '12px',
                paddingRight: variant === Variant.Fullscreen ? 0 : '12px',
                background,
                ...(variant === Variant.Fullscreen ? FullScreenStyle : {}),
                ...props.bodyStyle,
            }}
            className={StyledPopupClass}
        >
            <div className="flex h-full w-full flex-col overflow-auto">
                <Header prefix={prefix} title={title} suffix={suffix} headerClassName={headerClassName} />

                <div className={'min-h-0 flex-1'}>{props.children}</div>

                {onConfirm && (
                    <footer className="flex w-full px-[14px]">
                        {onCancel && (
                            <StyledButton
                                disabled={disabled}
                                {...cancelButtonProps}
                                onClick={onCancel}
                                type="default"
                                key="cancel"
                                className="mb-6 mr-2 h-[50px] flex-1 rounded-xl text-base font-medium"
                            >
                                {cancelText || '取消'}
                            </StyledButton>
                        )}

                        <StyledButton
                            key="confirm"
                            disabled={disabled}
                            {...confirmButtonProps}
                            onClick={onConfirm}
                            type="primary"
                            className="mb-6 h-[50px] flex-1 rounded-xl text-base font-medium"
                        >
                            {confirmText || '确认'}
                        </StyledButton>
                    </footer>
                )}
            </div>
        </Popup>
    );
}
