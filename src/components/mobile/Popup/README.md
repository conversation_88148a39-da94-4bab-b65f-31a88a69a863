# Popup 弹层

从底部弹出的浮层，支持全屏、半屏、根据内容适配。
基于 antd-mobile 组件的二次封装 https://mobile.ant.design/zh/components/popup

tips: 展示关闭按钮时，不支持左滑退出、不支持点击蒙层退出

# 接口说明

仅列举扩展的属性，更多属性请参考 antd-mobile 文档

| 属性名                | 说明                                           | 类型             | 默认值     |
|--------------------|----------------------------------------------|----------------|---------|
| variant            | 弹层类型，<br>fullscreen 全屏弹窗，高度 100%<br> 否则内容自适应 | 'fullscreen'   | ''      |
| title              | 标题                                           | string         | ''      |
| prefix             | 自定义前缀，通常用作返回按钮等                              | Node           | --      |
| suffix             | 自定义后缀，通常用作关闭按钮/操作等                           | Node           | --      |
| confirmText        | 确认按钮文字，支持传递文字或组件                             | string \| Node |         |
| cancelText         | 取消按钮文字，支持传递文字或组件                             | string \| Node |         |
| background         | 背景色,十六进制                                     | string         | 'white' |
| confirmButtonProps | 自定义确认按钮的样式                                   | ButtonProps    | --      |
| onClose            | 右上角，通常功能是关闭弹层                                | function       |         |
| onCancel           | 左下角，通常功能是取消/或其他                              | function       |         |
| onConfirm          | 右下角，通常功能是确认                                  | function       |         |

# 代码示例
```jsx
<Popup
    visible={visible}
    bodyStyle={{height: '90%'}}
    variant={Variant.Fullscreen}
    background="#F5F6F9"
    title="👏 快速创建智能体"
    prefix={
        <Button type="text" className="text-base font-medium" onClick={back}>
            返回
        </Button>
    }
    suffix={
        <Button type="text" className="text-base font-medium" onClick={handleSkip}>
            跳过
        </Button>
    }
    confirmText="一键生成配置"
    disabled={!canCreate}
    onClose={handleSkip}
    onConfirm={handleCreate}
    onCancel={handleCreate}
>
</Popup>
```
