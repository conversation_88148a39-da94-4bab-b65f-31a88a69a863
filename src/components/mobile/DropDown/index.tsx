/**
 * @file 移动端下拉框样式封装，props参考 Antd Popovert组件
 * <AUTHOR>
 * todo: 添加 Readme
 */

import {css} from '@emotion/css';
import {Popover, Space} from 'antd';
import {PopoverProps} from 'antd/lib';
import classNames from 'classnames';
import {useCallback, useState} from 'react';

const PopoverOverlayCSS = css`
    z-index: 100 !important;
    .ant-popover-content .ant-popover-inner {
        border-radius: 9px !important;
        padding: 0px;
        box-shadow: 0px 2px 30px 0px #1d22520f;
    }
`;

type OptionValue = string | number;
interface Option {
    label: string;
    value: OptionValue;
    iconName?: string;
}

interface PopoverContentInfo {
    value?: OptionValue;
    options: Option[];
    popoverWith?: number;
    popoverHeight?: number;
    onChange: (value: OptionValue) => void;
}

/** 更多按钮Popover的操作按钮集合 */
function PopoverContent({value, options, popoverWith = 120, popoverHeight = 258, onChange}: PopoverContentInfo) {
    return (
        <ul className="overflow-y-auto text-base font-medium" style={{width: popoverWith, height: popoverHeight}}>
            {options.map(item => (
                <li
                    className="mx-[21px] flex h-[57px] cursor-pointer items-center justify-between border-t border-[#edeef0] first:border-0"
                    key={item.value}
                    onClick={() => onChange(item.value)}
                >
                    <Space size={[4, 0]}>
                        {item.iconName && <span className={`iconfont text-gray-tertiary ${item.iconName}`}></span>}
                        <span className="text-sm text-black">{item.label}</span>
                    </Space>
                    {item.value === value && (
                        <span className="iconfont icon-check text-lg font-bold text-primary"></span>
                    )}
                </li>
            ))}
        </ul>
    );
}

export default function DropDown(
    props: PopoverProps &
        PopoverContentInfo & {
            dropDownText: string;
        }
) {
    const [open, setOpen] = useState(false);

    const handleOpenChange = useCallback(
        (visible: boolean) => {
            setOpen(visible);
        },
        [setOpen]
    );

    const onSelectChange = useCallback(
        (value: OptionValue) => {
            props.onChange(value);
            setOpen(false);
        },
        [props, setOpen]
    );

    return (
        <Popover
            trigger="click"
            placement="bottomRight"
            open={open}
            onOpenChange={handleOpenChange}
            content={
                <PopoverContent
                    value={props.value}
                    options={props.options}
                    popoverWith={props.popoverWith}
                    popoverHeight={props.popoverHeight}
                    onChange={onSelectChange}
                />
            }
            {...props}
            overlayClassName={classNames(PopoverOverlayCSS, props.overlayClassName)}
        >
            <div className="flex items-center truncate">
                <span className="truncate text-sm">{props.dropDownText}</span>
                <span className="iconfont icon-expand1 ml-1 text-[8px] text-gray-quaternary"></span>
            </div>
        </Popover>
    );
}
