/**
 * @file 移动端社群二维码弹窗
 *
 */
import React from 'react';
import {CenterPopup} from 'antd-mobile';

const CommunityQRCode = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/community-qrcode.jpg';

interface CommunityPopupProps {
    visible: boolean;
    onCloseBtnClick: () => void;
    onMaskClick: () => void;
    showBottomTip?: boolean;
}

export default function CommunityModal({
    visible,
    onCloseBtnClick,
    onMaskClick,
    showBottomTip = false,
}: CommunityPopupProps) {
    return (
        <CenterPopup
            visible={visible}
            onMaskClick={onMaskClick}
            bodyStyle={{paddingTop: '12px', paddingBottom: '24px', width: '208px'}}
        >
            <>
                <div className="flex items-center justify-end gap-[13px] pr-[11px] text-sm">
                    <span className="font-medium">微信扫码加入官方社群</span>
                    <span className="iconfont icon-close text-sm text-gray-secondary" onClick={onCloseBtnClick} />
                </div>
                <div className="flex justify-center pt-3">
                    <img className="w-[118px]" src={CommunityQRCode} />
                </div>
                {showBottomTip && <div className="flex justify-center pt-[9px] text-sm">长按扫码加入吧～</div>}
            </>
        </CenterPopup>
    );
}
