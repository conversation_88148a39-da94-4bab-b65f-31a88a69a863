import {ConfigProvider, Form, InputProps} from 'antd';
import {ReactNode} from 'react';
import {TextAreaProps} from 'antd/es/input';
import styled from '@emotion/styled';
import {Input} from 'antd';
import classNames from 'classnames';
import {InputTextAlign} from '@/components/mobile/AgentName/interface';

export const StyledInputWise = styled(Input)`
    &.ant-input {
        border: none !important;
    }
    &.ant-input-outlined {
        border: none !important;
    }
`;

export const StyledTextareaWise = styled(Input.TextArea)`
    &.ant-input {
        border: none !important;
        border-radius: 12px !important;
    }
    & {
        padding-top: 12px;
    }
    &::-webkit-scrollbar {
        background: transparent;
        width: 4px;
    }
    &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 5px;
    }
`;

interface InputWithLabelProps extends InputProps {
    label: string;
    require?: boolean;
    inputTextAlign?: InputTextAlign;
    errorMsgNode?: ReactNode;
}

export default function InputWithLabel(props: InputWithLabelProps) {
    const {status, errors} = Form.Item.useStatus();
    const {
        onChange,
        onBlur,
        value,
        placeholder,
        disabled,
        onClick,
        count,
        type,
        inputTextAlign = InputTextAlign.START,
        errorMsgNode,
    } = props;

    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: {
                        colorTextPlaceholder: '#848691',
                        fontSize: 16,
                        paddingBlock: 0,
                        paddingInline: 0,
                        lineHeight: 1,
                    },
                },
            }}
        >
            <section
                className={`flex items-center rounded-[12px] bg-white px-[13px] py-[14px] leading-none ${
                    props.className || ''
                }`}
            >
                <div className="flex-shrink-0 text-base font-semibold" key="label">
                    {props.label}
                </div>
                {props.required ? (
                    <span
                        className="mt-1 w-6 flex-shrink-0 pl-1 font-semibold text-error"
                        style={{fontFamily: 'SimSong'}}
                        key="require"
                    >
                        *
                    </span>
                ) : (
                    <div className="w-6 flex-shrink-0" />
                )}
                <StyledInputWise
                    className={classNames({
                        'text-end': inputTextAlign === InputTextAlign.END,
                    })}
                    {...{onChange, value, onBlur, placeholder, disabled, key: 'input', onClick, count, type}}
                />
            </section>
            {errorMsgNode || (
                <div
                    className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                    style={{
                        transition: 'all 0.2s linear',
                    }}
                >
                    {errors}
                </div>
            )}
        </ConfigProvider>
    );
}

interface TextAreaWithLabelProps extends TextAreaProps {
    label: string;
    require?: boolean;
    suffix?: ReactNode;
    template?: ReactNode;
    showTemplate?: boolean;
}

InputWithLabel.Textarea = function TextareaWithLabel(props: TextAreaWithLabelProps) {
    const {status, errors} = Form.Item.useStatus();
    const {onChange, onBlur, value, placeholder, count, autoSize, onClick, disabled} = props;
    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: {
                        colorTextPlaceholder: '#848691',
                        fontSize: 16,
                        paddingInline: 0,
                        paddingBlock: 0,
                    },
                },
            }}
        >
            <section
                className={classNames('rounded-[12px] bg-white px-[13px] py-[14px] leading-none', props.className)}
            >
                <header className="flex items-center pb-[9px]">
                    <div className="flex-shrink-0 text-base font-semibold" key="label">
                        {props.label}
                    </div>
                    {props.required && (
                        <span
                            className="ml-1 mr-2 mt-1 font-semibold text-error"
                            style={{fontFamily: 'SimSong'}}
                            key="require"
                        >
                            *
                        </span>
                    )}
                    {props.suffix}
                </header>
                {props.showTemplate ? (
                    props.template
                ) : (
                    <StyledTextareaWise
                        {...props}
                        {...{
                            onChange,
                            value,
                            onBlur,
                            placeholder,
                            count,
                            autoSize,
                            onClick,
                            disabled,
                            key: 'input',
                        }}
                    />
                )}
            </section>
            <div
                className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                style={{
                    transition: 'all 0.2s linear',
                }}
            >
                {errors}
            </div>
        </ConfigProvider>
    );
};
