import classNames from 'classnames';
import React, {ReactNode} from 'react';

export default function CardM(props: {
    title: ReactNode;
    className?: string;
    children?: ReactNode;
    subTitle?: string;
    suffix?: ReactNode;
    required?: boolean;
    id?: string;
    height?: string;
}) {
    return (
        <section
            className={classNames(
                'mt-[15px] rounded-[12px] bg-white px-[13px] pb-[15px] pt-3 leading-none',
                props.className
            )}
            id={props.id}
        >
            <header className={classNames('flex items-center', {'pb-[9px]': props.children})}>
                <span className="shrink-0 text-base font-semibold">{props.title}</span>
                {props.required && (
                    <span className="ml-1 mr-2 mt-1 font-semibold text-error" style={{fontFamily: 'SimSong'}}>
                        *
                    </span>
                )}
                {props.subTitle && (
                    <span className="ml-3 inline-block text-[14px] text-gray-quaternary">{props.subTitle}</span>
                )}
                <div className="flex-grow truncate">{props.suffix}</div>
            </header>
            {props.children}
        </section>
    );
}
