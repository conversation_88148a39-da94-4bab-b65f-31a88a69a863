# Card 卡片

卡片组件，符合视觉规范，

参数：

| 属性名      | 说明                        | 类型        | 默认值   |
|----------|---------------------------|-----------|-------|
| title    | 标题                        | ReactNode | ''    |
| subTitle | 副标题                       | string    | ''    |
| suffix   | 与标题同一行的内容，可放文字、按钮、输入框，或任何 | ReactNode | ''    |
| required | 给标题加星号                    | boolean   | false |

示例1: 适用于输入框
```jsx
 <Card
    title="名称"
    suffix={
        <>
            <Input
                placeholder="起一个响亮的名字吧"
                value={name}
                onChange={handleNameChange}
                className="border-none bg-white placeholder:text-gray-tertiary"
                name="name"
                onBlur={auditInputText}
            />
            <p className="absolute left-4 text-sm text-error">{nameAuditErrorText}</p>
        </>
    }
/>

```

示例 2：
```jsx
<Card title="设定" required>
    <div className="relative overflow-hidden rounded-xl">
        <StyledTextarea
            placeholder="你是一个美食推荐专家，你热爱生活，热爱吃喝，很喜欢和大家分享美食制作的小技巧和心得体会。在回复的过程中，你需要保持亲切友好、活泼、热情的语气，让用户有沉浸感～"
            className="h-40 resize-none border-none bg-white placeholder:text-gray-tertiary"
            value={introduction}
            onChange={handleIntroChange}
            onBlur={auditInputText}
            name="introduction"
        />
    </div>
    <p className="absolute left-4 text-sm text-error">{introAuditErrorText}</p>
</Card>

```
