/**
 * @file 移动端用的，黑色背景的全屏组件，自带导航栏
 * <AUTHOR>
 *
 * 没有使用 popup 的原因是：https://github.com/ant-design/ant-design-mobile/issues/6288
 */

import {createPortal} from 'react-dom';
import styled from '@emotion/styled';
import {useToVh} from '@/modules/agentPromptEditV2/mobile/components/DigitalFigure/convertDVHtoVH';
interface MobileFullScreenProps {
    show: boolean;
    showBack?: boolean;
    onBack?: () => void;
    title: string;
    children: React.ReactNode;
}

const FullScreenContainer = styled.div`
    height: calc(100 * var(--dvh));
`;

export default function MobileFullScreen({title, children, show, onBack, showBack = true}: MobileFullScreenProps) {
    useToVh();

    if (!show) {
        return null;
    }

    return createPortal(
        <div className="fixed left-0 top-0 z-[1000] h-full w-full touch-none">
            <FullScreenContainer className="absolute z-50 w-screen bg-black text-white">
                <header className="relative flex h-11 w-full items-center justify-center py-3 text-lg font-medium">
                    {showBack && (
                        <span
                            className="iconfont icon-left absolute left-4 top-2 cursor-pointer text-xl"
                            onClick={onBack}
                        ></span>
                    )}
                    <span>{title}</span>
                </header>
                <div className="h-[calc(100%-44px)]">{children}</div>
            </FullScreenContainer>
        </div>,
        document.body
    );
}
