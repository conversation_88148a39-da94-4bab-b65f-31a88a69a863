/**
 * 智能体名称配置项
 *
 */
import classNames from 'classnames';
import {ReactNode, useMemo} from 'react';
import {Form} from 'antd';
import {NamePath} from 'antd/es/form/interface';
import {useWatch} from 'antd/es/form/Form';
import {getAuditSpaceRules, getAuditTextRules, getAgentNameRules} from '@/api/audit';
import {getCountConfig} from '@/utils/text';
import InputWithLabel from '@/components/mobile/InputWithLabel/index';
import {InputTextAlign} from '@/components/mobile/AgentName/interface';
import {RepeatType} from '@/api/agentEdit/interface';
import {excludedNameRepeatTypes} from '@/modules/agentPromptEditV2/utils';

export default function AgentName({
    name,
    placeholder = '起个响亮的名字吧～',
    disabled = false,
    onAgentNameClick,
    requiredMark = true,
    rootClassName,
    inputTextAlign = InputTextAlign.START,
    errorMsgNode,
    label = '名称',
    nameSimilarity = 0,
    isTourOpen = false,
    repeatType,
}: {
    name: NamePath;
    placeholder?: string;
    disabled?: boolean;
    onAgentNameClick?: () => void;
    requiredMark?: boolean;
    rootClassName?: string;
    inputTextAlign?: InputTextAlign;
    errorMsgNode?: ReactNode;
    label?: string;
    nameSimilarity?: number;
    isTourOpen?: boolean;
    repeatType?: RepeatType;
}) {
    const value = useWatch(name);

    const showRepeatTips = useMemo(() => {
        /** 检查 repeatType 是否存在且其值不在 excludedNameRepeatTypes 列表中 */
        if (!!repeatType && !excludedNameRepeatTypes.includes(repeatType)) {
            return (
                <div className="mt-[6px] text-sm text-[#FF8200]">
                    当前名称与其他智能体相同，可能影响流量效果及使用，建议修改
                </div>
            );
        }

        /** 名称相似度超过 0.75(75%)且不是首次复制时，显示提示  */
        if (nameSimilarity >= 0.75 && !isTourOpen && value) {
            return (
                <div className="mt-[6px] text-sm text-[#FF8200]">
                    请修改名称，与原智能体过度相似将影响流量效果及使用
                </div>
            );
        }

        return null;
    }, [repeatType, nameSimilarity, isTourOpen, value]);

    return (
        <div className={classNames(rootClassName)}>
            <Form.Item
                name={name}
                rules={[
                    {
                        required: true,
                        message: '请输入智能体名称',
                    },
                    getAuditSpaceRules('智能体名称', 'onBlur'),
                    getAuditTextRules('智能体名称', 'onBlur'),
                    getAgentNameRules('智能体名称', ['onBlur', 'onChange']),
                ]}
                validateTrigger={['onBlur', 'onChange']}
                noStyle
            >
                <InputWithLabel
                    disabled={disabled}
                    label={label}
                    count={getCountConfig(20, false)}
                    onClick={onAgentNameClick}
                    required={requiredMark}
                    placeholder={placeholder}
                    inputTextAlign={inputTextAlign}
                    errorMsgNode={errorMsgNode}
                />
            </Form.Item>
            {showRepeatTips}
        </div>
    );
}
