/**
 * @file 录制声音组件
 *
 */
import {Spin} from 'antd';
import Lottie from 'lottie-react';
import {css} from '@emotion/css';
import {CreateAudioStatus, MaxRecordDuration} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';
import RecordAudioJSON from '@/modules/home/<USER>/record-audio';
import spinIndicatorLarge from '@/modules/agentPromptEditV2/pc/static/audio-spin-indicator-large.png';
import {RecordText} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';

interface RecordAudioProps {
    /** 录音状态 */
    recordStatus: CreateAudioStatus;
    /** 录音步骤引导 */
    guideText: string;
    /** 是否显示倒计时 */
    showCountdown: boolean;
    /** 录音时长 */
    recordDuration: number;
    /** 开始录音点击事件 */
    onStartRecordClick: () => void;
    /** 停止录音点击事件 */
    onStopRecordClick: () => void;
}

const ClickableDivStyle = css`
    -webkit-tap-highlight-color: transparent;
    &:active {
        opacity: 0.2;
    }
`;

export default function RecordAudio({
    recordStatus,
    guideText,
    showCountdown,
    recordDuration,
    onStartRecordClick,
    onStopRecordClick,
}: RecordAudioProps) {
    return (
        <div className="flex h-full flex-col items-center justify-center">
            {/* 录音步骤引导 */}
            <div className="my-6 text-base leading-none text-gray-tertiary">
                <span>{guideText}</span>
                {showCountdown && (
                    <span className="ml-2 font-semibold text-black">{MaxRecordDuration - recordDuration}s</span>
                )}
            </div>

            <div className="mb-[18px] flex flex-1 flex-col items-center justify-center rounded-xl bg-white p-8 text-2xl font-medium leading-[38px] text-[#000]">
                {/* 录音文本 */}
                <div className="text-justify">{RecordText}</div>
                {/* 录音动画 */}
                <div className="h-[50px]">
                    {recordStatus === CreateAudioStatus.RECORDING && (
                        <Lottie
                            className="h-full w-full rounded-xl bg-gray-bg-base"
                            animationData={RecordAudioJSON}
                            loop
                        />
                    )}
                </div>
            </div>

            <div className="mb-4">
                {(recordStatus === CreateAudioStatus.NONE || recordStatus === CreateAudioStatus.FAIL) && (
                    <div className={ClickableDivStyle} onClick={onStartRecordClick}>
                        <div className="mb-3 flex h-[72px] w-[72px] items-center justify-center rounded-full border-[3px] border-[#DBDCE0] bg-white">
                            <div className="h-[60px] w-[60px] rounded-full bg-[#FE5858]" />
                        </div>
                        <div className="text-center text-sm leading-none text-gray-tertiary">
                            {recordStatus === CreateAudioStatus.NONE ? '点击录制' : '重新录制'}
                        </div>
                    </div>
                )}
                {(recordStatus === CreateAudioStatus.RECORDING || recordStatus === CreateAudioStatus.UPLOADING) && (
                    <div className={ClickableDivStyle} onClick={onStopRecordClick}>
                        <div className="mb-3 flex h-[72px] w-[72px] items-center justify-center rounded-full border-[3px] border-[#DBDCE0] bg-white">
                            {recordStatus === CreateAudioStatus.UPLOADING ? (
                                <Spin
                                    className="w-8"
                                    indicator={
                                        <img
                                            className="ant-spin-dot ant-spin-dot-spin mr-1 h-8 w-8"
                                            src={spinIndicatorLarge}
                                        />
                                    }
                                />
                            ) : (
                                <div className="h-[28px] w-[28px] rounded-lg bg-[#FE5858]" />
                            )}
                        </div>
                        <div className="text-center text-sm leading-none text-gray-tertiary">停止录制</div>
                    </div>
                )}
            </div>
        </div>
    );
}
