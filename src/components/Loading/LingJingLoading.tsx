/**
 * @file 文心智能体平台 Loading 组件
 * <AUTHOR>
 */
import {useRef, useLayoutEffect, useEffect} from 'react';
import gsap from 'gsap';
import {shallow} from 'zustand/shallow';
import {LoadingSize} from '@/components/Loading/interface';
import {Z_INDEX_MAP} from '@/styles/z-index';
import {useLoadingIconStore} from '@/store/loading/loadingIconStore';

export default function LingJingLoading({
    size = LoadingSize.default,
    zIndex = Z_INDEX_MAP.loadingBase,
}: {
    size?: LoadingSize;
    zIndex?: number;
}) {
    const loadingRef = useRef<HTMLDivElement>(null);
    const loadingCtx = useRef<gsap.Context | null>(null);

    useLayoutEffect(() => {
        loadingCtx.current = gsap.context(() => {
            // 透明度从 0.4->0.6 的循环动画
            gsap.fromTo('.loading-icon', {opacity: 0.4}, {opacity: 0.6, duration: 3, repeat: -1, ease: 'power1.inOut'});
        }, loadingRef);

        return () => loadingCtx.current?.revert();
    }, []);

    // 使用store获取缓存的图片URL
    const {cachedIconUrl, loadIcon} = useLoadingIconStore(
        state => ({
            cachedIconUrl: state.cachedIconUrl,
            loadIcon: state.loadIcon,
        }),
        shallow
    );

    useEffect(() => {
        if (!cachedIconUrl) {
            loadIcon();
        }
    }, [cachedIconUrl, loadIcon]);

    return (
        <div
            className="loading-container absolute left-0 h-[100vh] w-[100vw]"
            ref={loadingRef}
            style={{
                background: 'linear-gradient(#D1DCFC, #ECF1FE, #F7F9FE)',
                zIndex,
            }}
        >
            <div className="loading-icon absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
                <img className="object-contain" src={cachedIconUrl || ''} style={{height: size}} alt={'loading'} />
            </div>
        </div>
    );
}
