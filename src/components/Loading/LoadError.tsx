import {ReactNode, useCallback} from 'react';
import {Button} from 'antd';
import classNames from 'classnames';
import {useNavigate} from 'react-router-dom';
import urls from '@/links';
import {isMobileDevice} from '../Login/utils/isMobileDevice';

// size 大小，small 用于小模块级别error UI
type Size = 'small';

export default function LoadError({
    size,
    tips,
    hideBtn,
    btnText,
    extra,
    className,
    onBtnClick,
    btnAction = 'refresh',
    iconClassName,
    tipsClassName,
    btnClassName,
    extraClassName,
}: {
    size?: Size;
    tips?: string;
    hideBtn?: boolean;
    btnText?: string;
    extra?: ReactNode;
    className?: string;
    onBtnClick?: () => void;
    btnAction?: 'refresh' | 'center';
    iconClassName?: string;
    tipsClassName?: string;
    btnClassName?: string;
    extraClassName?: string;
}) {
    const navigate = useNavigate();

    const handleClick = useCallback(() => {
        if (btnAction === 'center') {
            navigate(urls.center.raw(), {replace: true});
        }

        window.location.reload();
    }, [btnAction, navigate]);

    const gap = isMobileDevice() ? 'mt-3' : 'mt-[18px]';
    const titleWidth = isMobileDevice() ? 'max-w-[260px]' : 'max-w-[480px]';

    return (
        <div
            className={classNames(
                'flex min-h-fit w-full items-center justify-center py-8',
                {
                    'h-[calc(100vh-9.5rem)]': size !== 'small',
                    'h-full': size === 'small',
                },
                className
            )}
        >
            <div className={classNames('flex flex-col items-center')}>
                <span
                    className={classNames(
                        'iconfont icon-exclamationcircle p-3 text-7xl text-primary opacity-20',
                        iconClassName
                    )}
                />
                <div
                    className={classNames(
                        'mt-1.5 text-center text-base font-medium leading-tight text-gray-tertiary',
                        titleWidth,
                        tipsClassName
                    )}
                >
                    {tips || '加载失败，请稍后重试'}
                </div>
                {!!extra && (
                    <div
                        className={classNames(
                            'text-sm leading-none text-gray-quaternary',
                            isMobileDevice() ? 'mt-1.5' : 'mt-[12px]',
                            extraClassName
                        )}
                    >
                        {extra}
                    </div>
                )}
                {!hideBtn && (
                    <Button
                        type="primary"
                        shape="round"
                        className={classNames('h-auto px-[14px] py-[4px] text-sm font-medium', gap, btnClassName)}
                        onClick={onBtnClick || handleClick}
                    >
                        {btnText || '重新加载'}
                    </Button>
                )}
            </div>
        </div>
    );
}
