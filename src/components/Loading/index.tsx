/**
 * @file 居中 Loading 组件
 * <AUTHOR>
 */

import {useEffect} from 'react';
import {shallow} from 'zustand/shallow';
import {LoadingSize} from '@/components/Loading/interface';
import {useLoadingIconStore} from '@/store/loading/loadingIconStore';

export default function Loading({size = LoadingSize.default}: {size?: LoadingSize}) {
    // 使用store获取缓存的图片URL
    const {cachedIconUrl, loadIcon} = useLoadingIconStore(
        state => ({
            cachedIconUrl: state.cachedIconUrl,
            loadIcon: state.loadIcon,
        }),
        shallow
    );

    useEffect(() => {
        if (!cachedIconUrl) {
            loadIcon();
        }
    }, [loadIcon, cachedIconUrl]);

    return (
        <div className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
            <img
                className="animate-[lingjing-loading_3s_infinite] object-contain"
                src={cachedIconUrl || ''}
                style={{color: 'radial-gradient(50% 50% at 50% 50%, #FFF 0%, #E1E5ED 100%)', height: size}}
                alt="loading"
            />
        </div>
    );
}
