/**
 * @file 空态组件
 * <AUTHOR>
 */

import React from 'react';
import classNames from 'classnames';
import empty from '@/assets/version_empty_3.png';

export default function Empty({
    img,
    desc,
    children,
}: {
    img?: React.ReactNode; // 空态图片
    desc?: string | React.ReactNode; // 空态描述
    children?: React.ReactNode; // 空态操作，比如引导创建操作
}) {
    return (
        <div className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
            <div className="flex flex-col items-center">
                {img || <img src={empty} className="h-[66px] w-[71px]" />}
                <div
                    className={classNames('mt-2.5 text-sm text-gray-secondary', {
                        'mt-[1.125rem]': !!children,
                    })}
                >
                    {desc || '暂无数据'}
                </div>
                {children}
            </div>
        </div>
    );
}
