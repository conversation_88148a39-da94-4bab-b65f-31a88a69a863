/**
 * @file 分享智能体的二维码组件
 * <AUTHOR>
 * 文档：https://github.com/kozakdenys/qr-code-styling?tab=readme-ov-file
 * 配置项：https://qr-code-styling.com/
 * 视觉稿：https://www.figma.com/design/MBNYEsGRc7Ri06vo02JPpw/%E8%A7%86%E9%A2%91%E6%A0%87%E5%87%86%E5%8C%96%E7%94%9F%E4%BA%A7%E6%B5%81%E7%A8%8B?node-id=302-5386&p=f&t=XJGzLQPPnSNIrkmQ-0
 * @date 2025-03-11
 */

import React, {useEffect, useRef, useMemo} from 'react';
import QRCodeStyling from 'qr-code-styling';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import ImageWithBase64 from '@/modules/agentPromptEditV2/pc/components/ShareAgent/ImageWithBase64';
import platformLogo from './assets/logo.png';

const qrCodeOptions = new QRCodeStyling({
    type: 'canvas',
    shape: 'square',
    margin: 15,
    qrOptions: {
        typeNumber: 8,
        mode: 'Byte',
        /* 纠错等级：L 7%、M 15%、Q 25%、H 30% */
        errorCorrectionLevel: 'H',
    },
    imageOptions: {
        imageSize: 0.6,
        crossOrigin: 'anonymous',
        margin: 20,
    },
    dotsOptions: {
        type: 'dots',
        color: '#6a1a4c',
        roundSize: true,
        gradient: {
            type: 'linear',
            rotation: 0.7853981633974483,
            colorStops: [
                {
                    offset: 0,
                    color: '#5377f3',
                },
                {
                    offset: 1,
                    color: '#7dbafa',
                },
            ],
        },
    },
    cornersSquareOptions: {
        type: 'dot',
        color: '#000000',
        gradient: {
            type: 'linear',
            rotation: 0.7853981633974483,
            colorStops: [
                {
                    offset: 0,
                    color: '#5377f3',
                },
                {
                    offset: 1,
                    color: '#7da8fa',
                },
            ],
        },
    },
    cornersDotOptions: {
        type: 'dot',
        color: '#5377f3',
        gradient: {
            type: 'linear',
            rotation: 0.7853981633974483,
            colorStops: [
                {
                    offset: 0,
                    color: '#5377f3',
                },
                {
                    offset: 1,
                    color: '#7da8fa',
                },
            ],
        },
    },
});

const QRCodeLogo = ({agentLogo, platformLogo}: {agentLogo: string; platformLogo: string}) => {
    return (
        <div
            className="relative w-fit rounded-full bg-white p-[6px]"
            style={{
                transform: 'scale(0.3)',
                transformOrigin: 'left top',
            }}
        >
            <ImageWithBase64 className="h-[70px] w-[70px] rounded-full" url={agentLogo} />
            <img src={platformLogo} className="absolute bottom-[5px] left-[50px] h-[28px] w-[28px]" />
        </div>
    );
};

export default function ShareQRCode({url, agentLogo, size = 72}: {url: string; agentLogo: string; size: number}) {
    const qrCodeRef = useRef<HTMLDivElement>(null);
    const width = 900;

    // 尺寸小于80二维码不显示，并且会有一个消除不了的外边距
    // 所以这里采用二维码尺寸900px，再通过传入的size来计算缩放比例将二维码缩小到目标尺寸。
    const scale = useMemo(() => {
        return size / width;
    }, [size]);

    useEffect(() => {
        qrCodeOptions.update({
            data: url,
            width: width,
            height: width,
        });
    }, [url, size]);

    useEffect(() => {
        if (qrCodeRef?.current) {
            qrCodeOptions.append(qrCodeRef.current);
        }
    }, []);

    return (
        <CommonErrorBoundary>
            <div className="absolute left-[30px] top-[27px] z-[30]">
                <QRCodeLogo agentLogo={agentLogo} platformLogo={platformLogo} />
            </div>

            <div className="qrCodeWrapper">
                <div
                    ref={qrCodeRef}
                    style={{
                        transform: `scale(${scale})`,
                        transformOrigin: 'left top',
                    }}
                />
            </div>
        </CommonErrorBoundary>
    );
}
