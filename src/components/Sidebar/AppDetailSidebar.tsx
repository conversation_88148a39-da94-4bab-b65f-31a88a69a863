import React, {useCallback, useEffect, useState} from 'react';
import {Link, useLocation, useParams} from 'react-router-dom';
import {MenuItemType} from 'antd/es/menu/hooks/useItems';
import urls from '@/links';
import api from '@/api/appList';
import {AppListItem} from '@/api/appList/interface';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {DetailSidebar} from './DetailSidebar';

const ROUTE_KEY_MAP = {
    detail: 'agentFlowDetail',
    version: 'agentFlowVersion',
} as const;

export default function AppDetailSidebar() {
    const {id} = useParams();
    const {ubcClickLog} = useUbcLog();
    const handleAppDetailClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginDetail);
    }, [ubcClickLog]);
    const handleAppVersionClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginVersion);
    }, [ubcClickLog]);
    const handlePluginSwitchClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginSwitch);
    }, [ubcClickLog]);

    // menu items
    const appDetailItems = [
        {
            label: (
                <Link
                    to={urls.agentFlowDetail.fill({id: id!, tab: PluginTabType.Overview})}
                    onClick={handleAppDetailClick}
                >
                    应用概览
                </Link>
            ),
            key: 'detail',
            icon: <span className={'iconfont icon-overview text-xl'}></span>,
        },
        {
            label: (
                <Link
                    to={urls.agentFlowDetail.fill({id: id!, tab: PluginTabType.Version})}
                    onClick={handleAppVersionClick}
                >
                    版本管理
                </Link>
            ),
            key: 'version',
            icon: <span className={'iconfont icon-versions text-xl'}></span>,
        },
    ];

    // fetch async data
    const [appList, setAppList] = useState<AppListItem[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        (async () => {
            try {
                setLoading(true);
                const res = await api.getAppList({pageNo: 1, pageSize: 50});
                setAppList(res.appRespList);
                setLoading(false);
            } catch (error) {
                setLoading(false);
                console.error(error);
            }
        })();
    }, []);

    // drop items
    const location = useLocation();
    const ROUTE_MENU_INDEX = 3;
    const currentRoute = location.pathname.split('/')[ROUTE_MENU_INDEX] as keyof typeof ROUTE_KEY_MAP;

    const [current, setCurrent] = useState<{name?: string; logo?: string; path?: string}>({});
    const [dropItems, setDropItems] = useState<MenuItemType[]>([]);

    useEffect(() => {
        const dropItems: MenuItemType[] = appList?.map(item => {
            const logo = item.appInfo.appAvatar;
            const name = item.appInfo.appName;
            const path = urls.agentFlowDetail.fill({id: item.appInfo.appId, tab: PluginTabType.Overview});

            if (item.appInfo.appId === id) {
                setCurrent({
                    name,
                    logo,
                    path,
                });
            }

            return {
                label: (
                    <Link
                        to={path}
                        onClick={handlePluginSwitchClick}
                        className={'block w-[8.75rem] overflow-hidden truncate text-base'}
                    >
                        {name}
                    </Link>
                ),
                key: item.appInfo.appId,
                icon: <img className={'h-6 w-6 rounded-full'} src={logo} alt="icon" />,
            };
        });

        setDropItems(dropItems);
    }, [currentRoute, id, appList, handlePluginSwitchClick]);

    return (
        <DetailSidebar
            loading={loading}
            appDetailItems={appDetailItems}
            current={current}
            dropItems={dropItems}
            currentRoute={currentRoute}
        />
    );
}
