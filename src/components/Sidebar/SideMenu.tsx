import {Menu} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {useEffect, useMemo, useState} from 'react';
import {MenuItemType} from 'antd/es/menu/hooks/useItems';

// 菜单的提示框样式需要定制
const TooltipsSpan = styled.span`
    .ant-tooltip:has(&) {
        left: 92px !important;
        box-shadow: none !important;
        .ant-tooltip-arrow {
            display: none;
        }
        .ant-tooltip-content {
            .ant-tooltip-inner {
                background-color: #fff;
                height: 46px;
                padding: 12px;
                box-shadow: 0px 8px 40px 0px rgba(29, 34, 82, 0.06) !important;
                span a {
                    color: #1e1f24;
                    line-height: 22px;
                }
            }
        }
    }
`;

const getStyledTooltip = (children: React.ReactNode) => {
    return <TooltipsSpan>{children}</TooltipsSpan>;
};

const StyledMenu = styled(Menu)`
    .ant-menu-item:not(.ant-menu-item-selected):active {
        opacity: 50%;
    }
`;

const useCollapsed = () => {
    const [collapsed, setCollapsed] = useState(() => window.visualViewport!.width < 1440);
    useEffect(() => {
        const handleSizeChange = () => setCollapsed(window.visualViewport!.width < 1440);
        window.addEventListener('resize', handleSizeChange);
        return () => window.removeEventListener('resize', handleSizeChange);
    }, []);
    return collapsed;
};

/**
 *
 * @param items 路由项
 * @param inline 是否是内联模式（样式存在差异）
 * @param currentKey 当前选中的路由项
 * @constructor
 */
export const SideMenu: React.FC<{
    items?: MenuItemType[];
    inline?: boolean;
    currentKey?: string;
    withCollapsed?: boolean;
    styleTooltips?: boolean;
}> = ({items, currentKey, withCollapsed = true, styleTooltips}) => {
    const collapsed = useCollapsed();
    const currentKeys = useMemo(() => {
        if (!currentKey) {
            return undefined;
        }
        return [currentKey];
    }, [currentKey]);

    const styleItems = useMemo(() => {
        if (!styleTooltips) {
            return undefined;
        }
        return items?.map(item => ({
            ...item,
            label: getStyledTooltip(item.label),
        }));
    }, [items, styleTooltips]);

    return (
        <StyledMenu
            className={classNames('h-full border-0 bg-white', {
                'w-[4.75rem]': withCollapsed && collapsed,
                'w-52': !withCollapsed || !collapsed,
            })}
            mode="inline"
            inlineCollapsed={withCollapsed && collapsed}
            selectedKeys={currentKeys}
            items={styleItems || items}
        />
    );
};
