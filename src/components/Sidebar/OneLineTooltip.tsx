import Tooltip, {TooltipPlacement} from 'antd/es/tooltip';
import {ReactNode} from 'react';

export default function OneLineTooltip({
    children,
    flag,
    content,
    placement = 'bottomLeft',
}: {
    children: ReactNode;
    flag: boolean;
    content: string;
    placement?: TooltipPlacement;
}): ReactNode {
    return (
        <Tooltip
            title={flag ? undefined : content}
            placement={placement}
            overlayInnerStyle={{width: 'max-content', whiteSpace: 'nowrap'}}
        >
            {children}
        </Tooltip>
    );
}
