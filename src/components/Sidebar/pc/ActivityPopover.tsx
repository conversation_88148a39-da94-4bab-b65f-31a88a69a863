// 任务/活动中心气泡

import {Popover} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import api from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';

export default function ActivityPopover({children}: {children: React.ReactNode}) {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const timer = useRef<NodeJS.Timeout>();
    const [isOpen, setIsOpen] = useState<boolean>(false);

    const close = useCallback(async () => {
        setIsOpen(false);
        await api.recordPopup({name: PopupName.TaskCenter});
    }, []);

    useEffect(() => {
        if (!isLogin) return;
        (async () => {
            const {show} = await api.getPopup({name: PopupName.TaskCenter});
            setIsOpen(show);
            if (show) {
                timer.current = setTimeout(() => {
                    close();
                }, 60000);
            }
        })();

        // 组件销毁时 清除定时器
        return () => {
            isLogin && timer.current && clearTimeout(timer.current);
            isLogin && close();
        };
    }, [close, isLogin]);

    return (
        <Popover
            color="#5562F2"
            placement="right"
            content={
                <div className="flex w-[252px] justify-between text-justify text-white">
                    <div className="w-[226px] text-sm">
                        开启AI成长之旅，升级打怪抽好礼
                        <div
                            className="cursor-pointer text-right text-sm font-bold"
                            onClick={e => {
                                e.stopPropagation();
                                close();
                            }}
                        >
                            知道了
                        </div>
                    </div>
                    <span
                        className="iconfont icon-close cursor-pointer text-sm"
                        onClick={e => {
                            e.stopPropagation();
                            close();
                        }}
                    ></span>
                </div>
            }
            autoAdjustOverflow
            open={isOpen}
        >
            {children}
        </Popover>
    );
}
