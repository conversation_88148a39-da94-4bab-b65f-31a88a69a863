/**
 * @file 官方社群菜单项组件
 * <AUTHOR>
 */
import React, {useCallback} from 'react';
import StyledPopover from '@/components/Popover/StyledPopover';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {CommunityPopoverType, ElementPositionType} from '@/utils/loggerV2/interface';

const CommunityQRCode = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/community-qrcode.jpg';

export default function CommunityMenuItemPopover({children}: {children: React.ReactNode}) {
    const {showLog} = useUbcLogV3();

    const onOpenChange = useCallback(
        (open: boolean) => {
            if (open) {
                showLog('wecom_code_pop', {
                    cElementPos: ElementPositionType.SIDE_BAR,
                    cWecomPopType: CommunityPopoverType.ACTIVE,
                });
            }
        },
        [showLog]
    );

    return (
        <StyledPopover
            placement="right"
            trigger="hover"
            overlayInnerStyle={{padding: '6px 17px 10px'}}
            align={{offset: [-100, 0]}}
            priorityLevel={100}
            content={
                <div>
                    <div className="mb-1 text-center text-xs font-medium leading-[18px]">微信扫码加入官方社群</div>
                    <img className="h-[120px] w-[120px]" src={CommunityQRCode} />
                </div>
            }
            onOpenChange={onOpenChange}
        >
            {children}
        </StyledPopover>
    );
}
