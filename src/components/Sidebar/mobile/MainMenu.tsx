/**
 * @file 侧边栏主要功能菜单
 * <AUTHOR>
 */
import {useCallback} from 'react';
import {useLocation} from 'react-router-dom';
import urls from '@/links';
import centerIcon from '@/assets/center-icon.png';
import createIcon from '@/assets/create-icon.png';
import myAgentIcon from '@/assets/my-agent-icon.png';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/index';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import useLogin from '../../Login/hooks/useLogin';
import {useAgentNumModal} from '../hooks/useAgentNum';

const menuClickEventMap: Record<string, EVENT_VALUE_CONST> = {
    [urls.center.raw()]: EVENT_VALUE_CONST.AGENT_STORE,
    [urls.agentList.raw()]: EVENT_VALUE_CONST.MY_AGENT,
    [urls.agentPromptEdit.raw()]: EVENT_VALUE_CONST.CREATE_AGENT,
};

export default function MainMenu({closeDrawer}: {closeDrawer: () => void}) {
    const location = useLocation();

    const {loginCheck} = useLogin();
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const {setOpenAgentWarningModal, appNum, appLimit, AgentNumModal, Modalprops} = useAgentNumModal();
    const {clickLog} = useUbcLogV3();

    const allowCreate = !userInfoData?.hasTpProxy;

    const itemClick = useCallback(
        (checkLogin: boolean, checkAllowCreate: boolean, jumpUrl: string) => {
            return () => {
                const valueName = menuClickEventMap[jumpUrl];
                valueName && clickLog(valueName);

                if (checkAllowCreate && !allowCreate) {
                    showDisallowOperateToast();
                    return;
                }

                if (jumpUrl === urls.agentPromptEdit.raw() && appNum! >= appLimit!) {
                    setOpenAgentWarningModal(true);
                    return;
                }

                if (!isLogin && checkLogin) {
                    loginCheck();
                    return;
                }

                if (location.pathname.startsWith(jumpUrl)) {
                    closeDrawer();
                    return;
                }

                window.location.href = jumpUrl;
            };
        },
        [
            allowCreate,
            appLimit,
            appNum,
            closeDrawer,
            isLogin,
            location.pathname,
            loginCheck,
            setOpenAgentWarningModal,
            clickLog,
        ]
    );

    const item = (text: string, iconSrc: string, clickHandler: () => void, disabled?: boolean) => {
        return (
            <div
                onClick={clickHandler}
                className={`flex grow cursor-default flex-col items-center justify-center rounded-lg ${
                    disabled ? 'opacity-30' : ''
                }`}
            >
                <img src={iconSrc} className="mb-[9px] h-[44px] w-[44px] active:opacity-20" />
                <span className="leading-[20px]">{text}</span>
            </div>
        );
    };

    return (
        <div className="flex gap-[10px]">
            {item('创建智能体', createIcon, itemClick(true, true, urls.agentPromptEdit.raw()), !allowCreate)}
            {item('体验中心', centerIcon, itemClick(false, false, urls.center.raw()))}
            {item('我的智能体', myAgentIcon, itemClick(true, false, urls.agentList.raw()))}
            <AgentNumModal {...Modalprops} />
        </div>
    );
}
