/**
 * @file 侧边栏常用工具组件
 * <AUTHOR>
 */

import {useNavigate} from 'react-router-dom';
import React, {useEffect, useCallback, useState} from 'react';
import {Badge} from 'antd';
import homeConstant from '@/dicts/home';
import urls from '@/links';
import useLogin from '@/components/Login/hooks/useLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useActivityConfig} from '@/utils/useActivityConfig';
import CommunityModal from '@/components/mobile/CommunityModal';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {ElementPositionType, LJExtData} from '@/utils/loggerV2/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import CommunityIcon from '@/assets/community-icon.png';
import {useNoticeTipStore} from '@/store/notice/noticeTipStore';
import useAiBotSdk from '@/components/Sidebar/hooks/useAiBotSdk';

export const Tools = ({closeDrawer}: {closeDrawer: () => void}) => {
    const [showCommunityModal, setShowCommunityModal] = useState(false);
    const navigate = useNavigate();
    const {openAiBot} = useAiBotSdk();

    const {loginCheck} = useLogin();
    const isLogin = useUserInfoStore(store => store.isLogin);
    // 消息中心红点展示
    const [isShowNoticeTip, refreshTipShowState] = useNoticeTipStore(store => [
        store.isShowTip,
        store.refreshTipShowState,
    ]);
    const {clickLog, showLog} = useUbcLogV3();

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            // 消息中心红点展示
            refreshTipShowState();
        })();
    }, [isLogin, refreshTipShowState]);

    const clickCustomerService = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.SIDE_SERVICE);

        if (!isLogin) {
            closeDrawer();
            setTimeout(() => {
                loginCheck();
            }, 300);
            return;
        }

        openAiBot();
    }, [closeDrawer, isLogin, loginCheck, clickLog, openAiBot]);

    const handleDocsClick = useCallback(() => {
        closeDrawer();
        clickLog(EVENT_VALUE_CONST.SIDE_DOCUMENT);

        setTimeout(() => {
            window.open(homeConstant.DOCS_URL, '_blank');
        }, 300);
    }, [clickLog, closeDrawer]);

    const closeCommunityModal = useCallback(() => {
        setShowCommunityModal(false);
    }, []);

    const handleCommunityClick = useCallback(() => {
        setShowCommunityModal(true);
        clickLog(EVENT_VALUE_CONST.SIDE_COMMUNITY);

        // 官方社群弹窗展现打点
        showLog(EVENT_VALUE_CONST.MOBILE_COMMUNITY_MODAL, {
            [EVENT_EXT_KEY_CONST.ELEMENT_POSITION]: ElementPositionType.SIDE_BAR,
        } as LJExtData);
    }, [showLog, clickLog]);

    // 消息中心点击
    const handleNoticeClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.MSG_CENTER);
        closeDrawer();
        navigate(urls.noticeList.raw());
    }, [closeDrawer, navigate, clickLog]);

    const toolItem = (clickHandler: () => void, title: string, iconNode: React.ReactNode) => {
        return (
            <div className="flex h-[50px] items-center" onClick={clickHandler}>
                {iconNode}
                <span className="ml-3 font-medium">{title}</span>
            </div>
        );
    };

    // 运营活动按钮配置
    const activityBtnConfig = useActivityConfig().home;
    const handleActivityClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.UNDERSTAND_AGENT);
        window.open(activityBtnConfig?.jumpUrl);
    }, [activityBtnConfig, clickLog]);

    return (
        <div className="mb-[-11px] mt-[-9px]">
            {toolItem(
                handleDocsClick,
                '文档平台',
                <div className="flex h-[28px] w-[28px] items-center justify-center rounded-[100px] bg-gray-bg-base">
                    <span className="iconfont icon-a-discoverycenter text-sm text-colorTextDefault"></span>
                </div>
            )}
            {activityBtnConfig?.show &&
                toolItem(
                    handleActivityClick,
                    '秒懂智能体',
                    <div className="flex h-[28px] w-[28px] items-center justify-center rounded-[100px] bg-gray-bg-base">
                        <span className="iconfont icon-guide text-sm text-colorTextDefault"></span>
                    </div>
                )}
            {toolItem(
                clickCustomerService,
                '官方客服',
                <div className="relative flex h-[28px] w-[28px] items-center justify-center rounded-[100px] bg-gray-bg-base">
                    <span className="iconfont icon-services relative text-sm text-colorTextDefault"></span>
                </div>
            )}
            {toolItem(
                handleCommunityClick,
                '官方社群',
                <div className="flex h-[28px] w-[28px] items-center justify-center rounded-[100px] bg-gray-bg-base">
                    <img src={CommunityIcon} className="h-[14px] w-[14px]" alt="community" />
                </div>
            )}
            {toolItem(
                handleNoticeClick,
                '消息中心',
                <div className="relative flex h-[28px] w-[28px] items-center justify-center rounded-[100px] bg-gray-bg-base">
                    <span className="iconfont icon-bell absolute text-sm text-colorTextDefault"></span>
                    {/* 消息红点 */}
                    {isShowNoticeTip && <Badge status="error" className="absolute -mt-[22px] ml-4" />}
                </div>
            )}
            <CommunityModal
                visible={showCommunityModal}
                onCloseBtnClick={closeCommunityModal}
                onMaskClick={closeCommunityModal}
            />
        </div>
    );
};
