/**
 * @file 侧边栏用户信息组件
 * <AUTHOR>
 */

import {Avatar} from 'antd';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import ModalM from '@/components/mobile/Modal';
import AvatarPopover from '@/modules/home/<USER>/AvatarPopover';
import {DEFAULT_PORTRAIT, truncateMiddle} from '@/modules/home/<USER>/utils';
import useLogoutModal from '../../Login/hooks/useLogoutModal';
import useLogin from '../../Login/hooks/useLogin';

const UserInfo = ({size = '28px'}: {size?: string}) => {
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {loginCheck} = useLogin();
    const {logoutClickHandler, modalOpen, logoutHandler, cancelLogoutHandler} = useLogoutModal();
    const LogoutModal = (
        <ModalM open={modalOpen} okText="退出" cancelText="取消" onOk={logoutHandler} onCancel={cancelLogoutHandler}>
            <p className="px-[38px] text-center text-black opacity-90">退出登录将无法继续使用智能体，确认退出吗？</p>
        </ModalM>
    );

    const truncatedUserName = truncateMiddle(userInfoData?.userInfo.name || '', 12);

    return (
        <div onClick={loginCheck}>
            {isLogin ? (
                <AvatarPopover
                    height="104"
                    placement="topLeft"
                    overlayStyle={{
                        bottom: '55px',
                        left: '25px',
                        width: '172px',
                        height: '96px',
                    }}
                    overlayInnerStyle={{
                        padding: '0',
                        boxShadow: '0px 2px 30px 0px #1D22520F',
                        borderRadius: '9px',
                    }}
                    logoutBtn={
                        <div
                            className="mb-[9px] flex h-9 w-[132px] items-center justify-center rounded-[9px] bg-[#EBECFD] text-sm font-medium text-primary"
                            style={{margin: '0 auto'}}
                            onClick={logoutClickHandler}
                        >
                            退出登录
                        </div>
                    }
                >
                    <div
                        className="absolute bottom-0 w-full bg-white px-[25px] py-[15px]"
                        style={{boxShadow: '0px 1px 0px 0px #F5F6F9 inset'}}
                    >
                        <Avatar
                            src={userInfoData?.userInfo.portrait}
                            style={{width: `${size}`, height: `${size}`, minWidth: '20px', minHeight: '20px'}}
                        />
                        <span className="text-black-base ml-3 font-medium ">{truncatedUserName}</span>
                    </div>
                </AvatarPopover>
            ) : (
                <div
                    className="absolute bottom-0 w-full bg-white px-[25px] py-[15px]"
                    style={{boxShadow: '0px 1px 0px 0px #F5F6F9 inset'}}
                >
                    <Avatar
                        src={DEFAULT_PORTRAIT}
                        style={{width: `${size}`, height: `${size}`, minWidth: '20px', minHeight: '20px'}}
                    />
                    <span className="text-black-base ml-3 font-medium ">登录体验完整功能</span>
                </div>
            )}
            {LogoutModal}
        </div>
    );
};

export default UserInfo;
