import {Link, useLocation} from 'react-router-dom';
import {useCallback} from 'react';
import urls from '@/links';
import {SideMenu} from '@/components/Sidebar/SideMenu';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';

/**
 * 左导航点击打点的EventName和路由的映射
 */
const menuClickLogEventNameMaps = {
    [urls.plugin.raw()]: EVENT_TRACKING_CONST.LeftMenuPluginCenter,
    [urls.datasetList.raw()]: EVENT_TRACKING_CONST.LeftMenuDataset,
};

/**
 * 包裹打点事件的Link组件
 * @param {ReactNode} children Link子组件
 * @param {string} to 路由Url
 * @returns {ReactNode} React组件
 */
function MenuLink({children, to}: {children: React.ReactNode; to: string}) {
    const {ubcClickLog} = useUbcLog();

    const handleMenuClickLog = useCallback(() => {
        ubcClickLog(menuClickLogEventNameMaps[to]);
    }, [to, ubcClickLog]);

    return (
        <Link to={to} onClick={handleMenuClickLog}>
            {children}
        </Link>
    );
}

const mainItems = [
    {
        label: <MenuLink to={urls.plugin.raw()}>插件中心</MenuLink>,
        key: 'plugin',
        icon: <span className="iconfont icon-plug-in text-xl"></span>,
    },
    {
        label: <MenuLink to={urls.datasetList.raw()}>知识库</MenuLink>,
        key: 'dataset',
        icon: <span className="iconfont icon-dataset text-xl"></span>,
    },
];

export default function MainSidebar() {
    const ROUTE_MENU_INDEX = 2;
    const location = useLocation();
    const currentRoute = location.pathname.split('/')[ROUTE_MENU_INDEX];

    return (
        <div className="h-full border border-gray-border">
            <SideMenu items={mainItems} currentKey={currentRoute} styleTooltips />
        </div>
    );
}
