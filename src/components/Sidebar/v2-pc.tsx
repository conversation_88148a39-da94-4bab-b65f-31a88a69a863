import {Link, useNavigate} from 'react-router-dom';
import {Bad<PERSON>, Button, ConfigProvider} from 'antd';
import {useLocation} from 'react-router-dom';
import {useEffect, useCallback, useRef, useMemo, ReactNode} from 'react';
import classNames from 'classnames';
import {gsap} from 'gsap';
import styled from '@emotion/styled';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {LingJingIcon} from '@/dicts/index';
import urls from '@/links';
import UserAvatar from '@/components/UserAvatar';
import {useAgentHistoryStore} from '@/store/agent/history';
import homeConstant from '@/dicts/home';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/index';
import {ElementPositionType, LJExtData, LoginSource} from '@/utils/loggerV2/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {useNavigateQiaoCang} from '@/utils/home/<USER>';
import useLogin from '@/components/Login/hooks/useLogin';
import {recordDelayedReportTime, UserAction} from '@/utils/monitor/index';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import CommunityIcon from '@/assets/community-icon.png';
import CommunityMenuItemPopover from '@/components/Sidebar/pc/CommunityMenuItemPopover';
import {STEP, TOTAL_STEP, useBeginnerTourStore} from '@/store/agent/useBeginnerTour';
import FlashPoint from '@/modules/center/components/pc/FlashPoint';
import {TimeLineContext} from '@/modules/center/index-pc';
import {FlashPointColors} from '@/modules/center/components/pc/FlashPoint/constant';
import api from '@/api/debug';
import NoticePopover from '@/modules/noticeCenter/components/pc/NoticePopover';
import {useNoticeTipStore} from '@/store/notice/noticeTipStore';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {useLayoutStore} from '@/store/home/<USER>';
import LazySkeleton from '@/components/Sidebar/pc/LazySkeleton';
import Icon from '../Icon';
import useAiBotSdk from './hooks/useAiBotSdk';
import {useAgentNumModal} from './hooks/useAgentNum';
import {useGetUserMenus} from './hooks/useGetUserMenus';
import OneLineTooltip from './OneLineTooltip';
import ActivityPopover from './pc/ActivityPopover';

const menuClickEventMap: Record<string, EVENT_VALUE_CONST> = {
    [urls.center.raw()]: EVENT_VALUE_CONST.AGENT_STORE,
    [urls.agentPromptEdit.raw()]: EVENT_VALUE_CONST.CREATE_AGENT,
    [urls.agentList.raw()]: EVENT_VALUE_CONST.MY_AGENT,
    [urls.plugin.raw()]: EVENT_VALUE_CONST.MY_PLUGIN,
    [urls.datasetList.raw()]: EVENT_VALUE_CONST.MY_REPOSITORY,
    [urls.income.raw()]: EVENT_VALUE_CONST.MY_INCOME,
    [urls.centerPlugin.raw()]: EVENT_VALUE_CONST.PLUGIN_STORE,
};

interface MenuDetail {
    label?: React.ReactNode;
    children?: React.ReactNode;
    key: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}

interface LeftProps {
    showQiaocang?: boolean;
    showUser?: boolean;
    showServeMenuCard?: boolean;
}

const StyledMenuItem = styled.div`
    height: 2.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    &:hover {
        background-color: rgb(247, 248, 250);
    }
    &.menu-item-active {
        background-color: rgba(236, 238, 243, 0.4);
        color: rgb(30, 31, 36);
    }
`;

// 智能助手
const AiBotMenuItem = (): ReactNode => {
    const {clickLog, showLog} = useUbcLogV3();
    const {openAiBot} = useAiBotSdk();

    const handleAiBotClick = useCallback(() => {
        // 智能体客服点击
        clickLog(EVENT_VALUE_CONST.SIDE_SERVICE);

        openAiBot();

        // 智能客服抽屉展现
        showLog(EVENT_VALUE_CONST.CUSTOMER_SERVICE_DRAWER, {
            [EVENT_EXT_KEY_CONST.ELEMENT_POSITION]: ElementPositionType.SIDE_BAR,
        } as LJExtData);
    }, [clickLog, showLog, openAiBot]);

    return (
        <MenuItem className="rounded-lg px-1 py-2" key="smart-service" url="smart-service" onClick={handleAiBotClick}>
            <span className="relative flex items-center gap-2 px-2">
                <span className="iconfont icon-services text-base"></span>
                <span>智能客服</span>
            </span>
        </MenuItem>
    );
};

const MenuItem = ({className, url, ...props}: {url: string} & React.HTMLAttributes<HTMLDivElement>) => {
    const pathname = useLocation().pathname;
    const active = url.startsWith(urls.center.raw()) ? pathname === url : pathname.startsWith(url);
    // 添加新手引导文档中心高亮区域
    const documentCenterRef = useRef<HTMLDivElement>(null);
    const setStep = useBeginnerTourStore(action => action.setStep);
    useEffect(() => {
        !!documentCenterRef.current && setStep(documentCenterRef, STEP.FirstStep);
    }, [documentCenterRef, setStep]);
    return (
        <StyledMenuItem
            ref={url === homeConstant.DOCS_URL ? documentCenterRef : undefined}
            className={classNames(className, 'menu-item transition-colors', {
                'menu-item-active': active,
            })}
            {...props}
        />
    );
};

const CommunityMenuItemContent = (): ReactNode => {
    return (
        <span className="relative flex items-center gap-2 px-2">
            {/* 视觉上传的 icon 线条粗细不一致，暂时使用图片 */}
            <img src={CommunityIcon} className="my-1 h-4 w-4" alt="community" />
            <span>官方社群</span>
        </span>
    );
};

const MenuCard = ({
    title,
    menus,
    className,
}: {title: string; menus: MenuDetail[]} & React.HTMLAttributes<HTMLDivElement>) => {
    return (
        <div className={classNames(className, 'mx-auto w-full border-t-[1px] border-t-gray-border-secondary')}>
            <div className="px-3 pb-3 pt-6 text-sm font-medium text-gray-tertiary">{title}</div>
            <div className="flex flex-col gap-1">
                {menus.map(
                    menu =>
                        menu.children || (
                            <MenuItem
                                className="rounded-lg px-1 py-2"
                                key={menu.key}
                                url={menu.key}
                                onClick={menu.onClick}
                            >
                                {menu.label}
                            </MenuItem>
                        )
                )}
            </div>
        </div>
    );
};

// eslint-disable-next-line max-statements
function Left({showQiaocang = true, showUser = true, showServeMenuCard = true}: LeftProps) {
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const allowCreate = !userInfoData?.hasTpProxy;

    const [shouldScrollToTop, resetShouldScrollToTop, agentHistory] = useAgentHistoryStore(store => [
        store.shouldScrollToTop,
        store.resetShouldScrollToTop,
        store.agentHistory,
    ]);
    const {setOpenAgentWarningModal, appNum, appLimit, AgentNumModal, Modalprops} = useAgentNumModal();
    const historyListRef = useRef<HTMLDivElement>(null);
    const {ubcClickLog} = useUbcLog();
    const {clickLog} = useUbcLogV3();

    const noticePopoverRef = useRef<any>();

    // 消息中心红点展示
    const [isShowNoticeTip, refreshTipShowState] = useNoticeTipStore(store => [
        store.isShowTip,
        store.refreshTipShowState,
    ]);

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            // 消息中心红点展示
            refreshTipShowState();
        })();
    }, [isLogin, refreshTipShowState]);

    useEffect(() => {
        if (historyListRef.current && shouldScrollToTop) {
            const scrollBar = historyListRef.current;
            scrollBar.scrollTop = 0;
            resetShouldScrollToTop();
        }
    }, [shouldScrollToTop, agentHistory, resetShouldScrollToTop]);

    const navigate = useNavigate();
    const handleLinkClick: (linkUrl: string) => () => void = useCallback(
        linkUrl => {
            return () => {
                const valueName = menuClickEventMap[linkUrl];
                valueName && clickLog(valueName);
                navigate(linkUrl);
            };
        },
        [navigate, clickLog]
    );
    const {loginCheck} = useLogin(LoginSource.CREATE_AGENT);
    const [beginnerGuideOpen, setCurrent, setOpen] = useBeginnerTourStore(store => [
        store.open,
        store.setCurrent,
        store.setOpen,
    ]);
    const handleCreate = useCallback(() => {
        loginCheck();
        if (beginnerGuideOpen) {
            setCurrent(TOTAL_STEP);
            setOpen(false);
        }

        if (appNum && appLimit && appLimit <= appNum) {
            setOpenAgentWarningModal(true);
            return;
        }

        if (isLogin) {
            // 记录进入创建
            generateSessionId();
            generateOperationModule(OperationModuleType.CreateAgent);

            handleLinkClick(urls.agentPromptQuickStart.raw())();

            // 记录打开智能体
            recordDelayedReportTime({
                action: UserAction.ClickQuickCreateAgent,
                errMsg: '快速创建智能体页面2s内未成功展示',
                interval: 2000,
            });
        }
    }, [
        loginCheck,
        appNum,
        appLimit,
        isLogin,
        setOpenAgentWarningModal,
        handleLinkClick,
        setCurrent,
        setOpen,
        beginnerGuideOpen,
    ]);

    const handleLogoClick: React.MouseEventHandler<HTMLAnchorElement> = useCallback(
        () => ubcClickLog(EVENT_TRACKING_CONST.SideBarLogoBtn),
        [ubcClickLog]
    );

    const checkAllowCreate = useCallback(
        (task: any): any => {
            if (!allowCreate) {
                return () => {};
            }

            task();
        },
        [allowCreate]
    );

    const handleActivityClick: React.MouseEventHandler<HTMLDivElement> = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.GROWTH_CENTER, {}, EVENT_PAGE_CONST.EXPERIENCE);
        handleLinkClick(urls.activityList.raw())();
    }, [handleLinkClick, clickLog]);

    const handleNoticeClick: React.MouseEventHandler<HTMLDivElement> = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.MSG_CENTER, {}, EVENT_PAGE_CONST.EXPERIENCE);
        handleLinkClick(urls.noticeList.raw())();
        if (noticePopoverRef.current) {
            // 关闭公告气泡
            noticePopoverRef.current.closeNoticePopover();
        }
    }, [handleLinkClick, clickLog]);

    const active = useLocation().pathname;
    const uniformLoginNoticeCenter = useUniformLogin(LoginSource.NOTICE_CENTER);
    const {userMenus} = useGetUserMenus();

    useAiBotSdk();

    const handleDocsClick = useCallback(() => {
        // 文档中心点击
        clickLog(EVENT_VALUE_CONST.SIDE_DOCUMENT);
        window.open(homeConstant.DOCS_URL);
    }, [clickLog]);

    const handleCommunityClick = useCallback(() => {
        // 社区中心点击
        clickLog(EVENT_VALUE_CONST.SIDE_COMMUNITY);
        window.open(homeConstant.COMMUNITY_URL);
    }, [clickLog]);

    const serveMenuItem: MenuDetail[] = useMemo(
        () => [
            {
                key: urls.activity.raw(),
                label: (
                    <ActivityPopover>
                        <span className="flex items-center gap-2 px-2">
                            <span className="iconfont icon-gift text-base"></span>
                            <span>成长中心</span>
                        </span>
                    </ActivityPopover>
                ),
                onClick: handleActivityClick,
            },
            {
                key: homeConstant.DOCS_URL,
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span className="iconfont icon-a-platformdoc text-base"></span>
                        <span>文档中心</span>
                    </span>
                ),
                onClick: handleDocsClick,
            },
            {
                key: urls.noticeCenter.raw(),
                label: (
                    <>
                        <span className="flex items-center gap-2 px-2">
                            <span className="iconfont icon-bell text-base" />
                            <LogContextProvider page={EVENT_PAGE_CONST.EXPERIENCE}>
                                <NoticePopover ref={noticePopoverRef} />
                            </LogContextProvider>

                            {isShowNoticeTip && <Badge status="error" className="-ml-[8px] -mt-4" />}
                        </span>
                    </>
                ),
                onClick: isLogin ? handleNoticeClick : uniformLoginNoticeCenter,
            },
            {
                key: homeConstant.COMMUNITY_URL,
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span className="iconfont icon-a-dialogueballoons text-base"></span>
                        <span>社区中心</span>
                    </span>
                ),
                onClick: handleCommunityClick,
            },
            {
                key: homeConstant.COMMUNITY_QR_CODE_KEY,
                children: (
                    <CommunityMenuItemPopover>
                        <MenuItem className="rounded-lg px-1 py-2" key="community" url="community">
                            <CommunityMenuItemContent />
                        </MenuItem>
                    </CommunityMenuItemPopover>
                ),
            },
            {
                key: homeConstant.SMART_SERVICE_KEY,
                children: <AiBotMenuItem />,
            },
        ],
        [
            handleCommunityClick,
            handleDocsClick,
            handleNoticeClick,
            isLogin,
            uniformLoginNoticeCenter,
            isShowNoticeTip,
            handleActivityClick,
        ]
    );
    // 添加新手引导创建智能体高亮区域
    const createAgentRef = useRef<HTMLDivElement>(null);
    const [current, setStep] = useBeginnerTourStore(store => [store.current, store.setStep]);
    const showFlashPoint = useMemo(() => current === STEP.LastStep, [current]);
    const globalTimeLine = gsap.timeline();
    useEffect(() => {
        !!createAgentRef.current && setStep(createAgentRef, STEP.LastStep);
    }, [createAgentRef, setStep]);
    const navigateQiaoCang = useNavigateQiaoCang();
    const isUcUser = useUserInfoStore(store => !!store.userInfoData?.userInfo.ucUserId);

    const handlePluginStoreClick = useCallback(() => {
        navigate(urls.centerPlugin.raw());
        if (isLogin) {
            api.recordPopup({name: 'pluginCenter'});
        }

        clickLog(EVENT_VALUE_CONST.PLUGIN_STORE);
    }, [clickLog, isLogin, navigate]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Menu: {
                        itemSelectedBg: 'rgba(229, 234, 255, 1)',
                        itemHoverBg: 'rgba(229, 234, 255, 1)',
                        itemSelectedColor: ThemeConfig.token.colorPrimary,
                        itemHeight: 50,
                        marginXXS: 10,
                        iconMarginInlineEnd: 15,
                    },
                    Badge: {
                        statusSize: 7,
                    },
                },
            }}
        >
            <div className="side-bar flex h-full flex-col justify-between bg-white px-4 py-4">
                <Link className="mb-8" to={urls.center.raw()} onClick={handleLogoClick}>
                    <img src={LingJingIcon} className="inline-block h-9" alt="logo" />
                </Link>

                <div className="flex flex-col justify-center">
                    <TimeLineContext.Provider value={{timeLine: globalTimeLine}}>
                        <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                            <div
                                ref={createAgentRef}
                                className="create-agent-btn-container mb-4 h-10 w-full"
                                onClick={handleCreate}
                            >
                                <Button
                                    className="create-agent-btn h-full w-full rounded-lg"
                                    type="primary"
                                    disabled={!allowCreate}
                                >
                                    <div className="flex items-center justify-center gap-1 font-medium">
                                        <span className="iconfont icon-creat"></span>
                                        <span>创建智能体</span>
                                        {showFlashPoint && (
                                            <div className="absolute right-0 top-[8px] cursor-pointer">
                                                <FlashPoint color={FlashPointColors.White} />
                                            </div>
                                        )}
                                    </div>
                                </Button>
                                <div className="create-agent-img-btn h-full w-full transition-colors"></div>
                            </div>
                        </OneLineTooltip>
                    </TimeLineContext.Provider>
                    <MenuItem
                        url={urls.center.raw()}
                        className="flex items-center gap-2 rounded-lg px-3 py-2"
                        onClick={handleLinkClick(urls.center.raw())}
                    >
                        <span
                            className={classNames('iconfont', {
                                'icon-a-discoverycenter': active !== urls.center.raw(),
                                'icon-a-discoverycenter1': active === urls.center.raw(),
                            })}
                        ></span>
                        <span>智能体商店</span>
                    </MenuItem>

                    <MenuItem
                        url={urls.centerPlugin.raw()}
                        className="relative mt-1 flex items-center gap-2 rounded-lg px-3 py-2"
                        onClick={handlePluginStoreClick}
                    >
                        <Icon name={active === urls.centerPlugin.raw() ? 'plugin' : 'tiyanzhongxin'} />

                        <span>插件商店</span>
                    </MenuItem>
                </div>
                {/* 此部分超出高度可滚动 */}
                <div className="no-scrollbar mb-auto overflow-y-auto">
                    <MenuCard title="个人空间" menus={userMenus} className="mb-6 mt-3" />
                    {showServeMenuCard && <MenuCard title="服务空间" menus={serveMenuItem} />}
                    <div className="h-8 w-full"></div>
                </div>
                <div className="relative w-full pb-1">
                    {/* 滚动区域底部遮罩 */}
                    <div
                        className="l-0 pointer-events-none absolute top-[-32px] h-8 w-full"
                        style={{
                            background:
                                'linear-gradient(to top, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%)',
                        }}
                    ></div>
                    {isUcUser && showQiaocang && (
                        <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT} placement="topLeft">
                            <div
                                className={`mb-3 flex w-full cursor-pointer justify-between rounded-lg bg-[#F8F9FC] px-[12px] py-3 transition-colors duration-75 ${
                                    allowCreate ? 'hover:bg-[#e5eaff]' : 'text-[#3333334D]'
                                }`}
                                onClick={() => checkAllowCreate(navigateQiaoCang)}
                            >
                                前往巧舱创建
                                <span className="iconfont icon-enter rotate-45" />
                            </div>
                        </OneLineTooltip>
                    )}
                    {showUser && <UserAvatar />}
                </div>
                <AgentNumModal {...Modalprops} />
            </div>
        </ConfigProvider>
    );
}

export default function SideBarContainer(props: LeftProps) {
    const showLeftSideBar = useLayoutStore(store => store.showLeftSideBar);

    return (
        <section className="h-full">
            {showLeftSideBar ? (
                <Left {...props} />
            ) : (
                <LazySkeleton showUser={props.showUser} showServeMenuCard={props.showServeMenuCard} />
            )}
        </section>
    );
}
