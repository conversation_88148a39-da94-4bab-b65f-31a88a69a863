import React, {useCallback, useState, Fragment} from 'react';
import styled from '@emotion/styled';
import {MenuItemType} from 'antd/es/menu/hooks/useItems';
import {Dropdown} from 'antd';
import {Link} from 'react-router-dom';
import classNames from 'classnames';
import {SideMenu} from '@/components/Sidebar/SideMenu';
import Loading from '@/components/Loading';
import ThemeConfig from '@/styles/lingjing-light-theme';

/**
 * 动态计算下拉框的最大高度，避免小屏幕时下拉高度过高
 */
function dropMaxHeight() {
    const calculatedHeight = window.innerHeight - 140;
    return calculatedHeight <= 708 ? `${calculatedHeight}px` : '708px';
}

const StyledDropDownContainer = styled.div`
    .ant-dropdown-menu {
        border-radius: 9px !important;
        border: 1px solid #eceef3 !important;
        background: #fff !important;
        max-height: ${dropMaxHeight()} !important;
        overflow-y: scroll !important;
        margin-top: 10px !important;
        box-shadow: 0 15px 80px 0 rgba(29, 34, 82, 0.08) !important;
        padding: 0 !important;

        &::-webkit-scrollbar {
            width: 0.1875rem;
            height: 0.1875rem;
            transform: translateX(-10px);
        }

        &::-webkit-scrollbar-thumb {
            background: #d3d9e6;
            border-radius: 0.0938rem;
        }

        .ant-dropdown-menu-item {
            padding: 1rem !important;

            .ant-dropdown-menu-item-icon {
                flex-shrink: 0;
            }
        }

        .ant-dropdown-menu-item:hover {
            background: none !important;
            color: ${ThemeConfig.token.colorPrimary} !important;
        }

        .ant-dropdown-menu-item:active span {
            opacity: 0.5;
        }
    }
`;

export const DetailSidebar = ({
    dropItems,
    current,
    appDetailItems,
    currentRoute,
    loading,
}: {
    appDetailItems: MenuItemType[];
    current: {
        name?: string;
        logo?: string;
        path?: string;
    };
    loading: boolean;
    dropItems?: MenuItemType[];
    currentRoute: string;
}) => {
    const [dropDownOpen, setDropDownOpen] = useState<boolean>(false);
    const onOpen = useCallback((open: boolean) => {
        setDropDownOpen(open);
    }, []);

    const onClick = useCallback(() => {
        // 点击后默认收起
        setDropDownOpen(false);
    }, []);

    const dropdownRender = useCallback(
        (nodes: React.ReactNode) => <StyledDropDownContainer onClick={onClick}>{nodes}</StyledDropDownContainer>,
        [onClick]
    );

    return (
        <div className="flex h-full w-[13.125rem] flex-col items-center border border-gray-100 bg-white pt-4">
            <Fragment>
                {loading ? (
                    <div className="relative h-8 w-52">
                        <Loading />
                    </div>
                ) : (
                    <>
                        {dropItems?.length! > 0 && (
                            <Dropdown
                                menu={{items: dropItems}}
                                overlayStyle={{
                                    width: '13.125rem',
                                }}
                                dropdownRender={dropdownRender}
                                placement={'bottom'}
                                onOpenChange={onOpen}
                            >
                                <Link
                                    to={current.path!}
                                    className="mb-2 flex w-full items-center px-4 hover:text-primary"
                                >
                                    <img className={'h-6 w-6 rounded-full'} src={current.logo} alt="" />
                                    <span
                                        className={classNames(
                                            'ml-2 mr-1.5 block w-[8rem] overflow-hidden truncate text-base font-medium',
                                            {
                                                'text-primary': dropDownOpen,
                                            }
                                        )}
                                    >
                                        {current.name}
                                    </span>
                                    {dropDownOpen ? (
                                        <span className="iconfont icon-Up text-lg"></span>
                                    ) : (
                                        <span className="iconfont icon-Down text-lg"></span>
                                    )}
                                </Link>
                            </Dropdown>
                        )}
                    </>
                )}
            </Fragment>
            <SideMenu items={appDetailItems} currentKey={currentRoute} withCollapsed={false} />
        </div>
    );
};
