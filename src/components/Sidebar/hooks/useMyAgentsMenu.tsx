import {useCallback, useEffect, useMemo, useState} from 'react';
import {Badge} from 'antd';
import {RedMarkScene, useUserInfoStore} from '@/store/login/userInfoStore';
import {getRedDot} from '@/api/redDot';
import {RedDotType} from '@/api/redDot/interface';
import CustomPopover from '@/components/Popover';

export const useMyAgentsMenu = () => {
    const [
        isLogin,
        myAgentListAnalysisRedMarkShow,
        myAgentListRedMarkShow,
        myAgentListDatasetRecallTestRedMarkShow,
        setRedMark,
    ] = useUserInfoStore(store => [
        store.isLogin,
        store.redMarks[RedMarkScene.MyAgentListAnalysis]?.redShow,
        store.redMarks[RedMarkScene.MyAgentList]?.redShow,
        store.redMarks[RedMarkScene.MyAgentListDatasetRecallTest]?.redShow,
        store.setRedMark,
    ]);

    const [myAgentsMenuPopOpen, setMyAgentsMenuPopOpen] = useState(false);
    const popTitle = '';
    const closeMyAgentsMenuPopOpen = useCallback(() => {
        setMyAgentsMenuPopOpen(false);
    }, []);

    /** 我的智能体是否展示小红点 */
    useEffect(() => {
        (async () => {
            if (!isLogin) return;

            try {
                // 我的智能体红点场景
                let res = await getRedDot({
                    type: RedDotType.MyAgents,
                });

                setRedMark(RedMarkScene.MyAgentList, res);

                // 我的智能体红点场景-诊断报告
                if (!res?.redShow) {
                    res = await getRedDot({
                        type: RedDotType.AgentDiagnosis,
                    });

                    setRedMark(RedMarkScene.MyAgentListAnalysis, res);
                }

                // 我的智能体红点场景-知识库召回测试
                if (!res?.redShow) {
                    res = await getRedDot({
                        type: RedDotType.AgentDiagnosis,
                    });

                    setRedMark(RedMarkScene.MyAgentListDatasetRecallTest, res);
                }
            } catch (error) {
                console.error(error);
            }
        })();
    }, [isLogin, setRedMark]);

    const closePop = useCallback(
        (e?: React.MouseEvent) => {
            closeMyAgentsMenuPopOpen();
            e?.stopPropagation();
        },
        [closeMyAgentsMenuPopOpen]
    );

    const myAgentsMenu = useMemo(() => {
        return (
            <>
                <CustomPopover
                    type="primary"
                    placement="rightTop"
                    open={myAgentsMenuPopOpen}
                    title={<span className="font-normal">{popTitle}</span>}
                    onClose={closePop}
                    exitTime
                    arrow={{pointAtCenter: true}}
                    align={{
                        targetOffset: [0, -5],
                    }}
                    rootClassName="max-w-[276px]"
                >
                    <span>我的智能体</span>
                </CustomPopover>
                {(myAgentListAnalysisRedMarkShow ||
                    myAgentListRedMarkShow ||
                    myAgentListDatasetRecallTestRedMarkShow) && <Badge status="error" className="-ml-2 -mt-4" />}
            </>
        );
    }, [
        closePop,
        myAgentListAnalysisRedMarkShow,
        myAgentListRedMarkShow,
        myAgentsMenuPopOpen,
        myAgentListDatasetRecallTestRedMarkShow,
    ]);

    return {myAgentsMenu, myAgentsMenuPopOpen, closeMyAgentsMenuPopOpen};
};
