import {useCallback, useEffect, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import urls from '@/links';
import {useAgentCountStore} from '@/store/agent/agentNum';
import {useIsMobileStore} from '@/store/home/<USER>';
import agentListA<PERSON> from '@/api/agentList';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {AgentNumModal} from '../AgentNumModal';

export const useAgentNum = () => {
    // agentNum
    const {appNum, appLimit, setAgentNumber} = useAgentCountStore(store => ({
        setAgentNumber: store.setAgentNumber,
        appNum: store.appNum,
        appLimit: store.appLimit,
    }));

    // 获取agent数量
    const updateAgentListNum = useCallback(async () => {
        const {appNum, appLimit} = await agentListApi.getAgentListNum();
        setAgentNumber({appNum, appLimit});
    }, [setAgentNumber]);

    return {
        appNum,
        appLimit,
        updateAgentListNum,
    };
};

export const useAgentNumModal = () => {
    const [openAgentWarningModal, setOpenAgentWarningModal] = useState(false);
    // 控制弹窗
    const {appNum, appLimit, updateAgentListNum} = useAgentNum();

    const isMobile = useIsMobileStore(store => store.isMobile);

    const navigate = useNavigate();

    const isLogin = useUserInfoStore(store => store.isLogin);

    const handleModalClick = useCallback(() => {
        setOpenAgentWarningModal(false);
    }, [setOpenAgentWarningModal]);

    const handleModalClickOk = useCallback(() => {
        setOpenAgentWarningModal(false);
        navigate(urls.agentList.raw());
    }, [navigate]);

    const updateAgentListNumRef = useRef(updateAgentListNum);

    useEffect(() => {
        if (isLogin) {
            updateAgentListNumRef.current();
        }
    }, [isLogin]);

    const Modalprops = {
        isMobile,
        openAgentWarningModal,
        appNum,
        appLimit,
        handleModalClick,
        handleModalClickOk,
    };

    return {
        openAgentWarningModal,
        setOpenAgentWarningModal,
        appNum,
        appLimit,
        updateAgentListNum,
        AgentNumModal,
        Modalprops,
    };
};
