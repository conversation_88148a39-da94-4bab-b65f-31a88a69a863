/**
 * @file 智能客服调起和关闭
 * <AUTHOR>
 */

import {useCallback, useRef} from 'react';
import {getAiSdk} from '@/components/CustomerService/initSdk';
import {useAiBotStore} from '@/store/agent/aiBot';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {LoginSource} from '@/utils/loggerV2/interface';

const useAiBotSdk = () => {
    const {setAiBotState} = useAiBotStore(state => ({
        setAiBotState: state.setAiBotState,
        showAiBot: state.showAiBot,
    }));
    const uniformLogin = useUniformLogin(LoginSource.INTELLIGENT_SERVICE);

    const hasRegisteredCallbacks = useRef(false);

    const openAiBot = useCallback(() => {
        const AiSdk = getAiSdk();

        AiSdk.openBot({
            enableAnimation: true,
        });

        setAiBotState(true);

        if (!hasRegisteredCallbacks.current) {
            AiSdk.onLogin(() => {
                uniformLogin();
            });

            AiSdk.onBotCloseClick(() => {
                setAiBotState(false);
            });

            hasRegisteredCallbacks.current = true;
        }
    }, [setAiBotState, uniformLogin]);

    return {
        openAiBot,
    };
};

export default useAiBotSdk;
