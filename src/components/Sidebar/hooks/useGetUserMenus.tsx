import {useCallback, useEffect, useMemo, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import {Badge} from 'antd';
import urls from '@/links';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {LoginSource} from '@/utils/loggerV2/interface';
import {RedMarkScene, useUserInfoStore} from '@/store/login/userInfoStore';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import Icon from '@/components/Icon';
import {getRedDot} from '@/api/redDot';
import {CounselingRedDotSubType, RedDotType} from '@/api/redDot/interface';
import {getCounselingAccess} from '@/api/counseling';
import OneLineTooltip from '../OneLineTooltip';
import {useMyAgentsMenu} from './useMyAgentsMenu';

interface MenuDetail {
    label?: React.ReactNode;
    children?: React.ReactNode;
    key: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}

const menuClickEventMap: Record<string, EVENT_VALUE_CONST> = {
    [urls.agentPromptEdit.raw()]: EVENT_VALUE_CONST.CREATE_AGENT,
    [urls.agentList.raw()]: EVENT_VALUE_CONST.MY_AGENT,
    [urls.plugin.raw()]: EVENT_VALUE_CONST.MY_PLUGIN,
    [urls.datasetList.raw()]: EVENT_VALUE_CONST.MY_REPOSITORY,
    [urls.income.raw()]: EVENT_VALUE_CONST.MY_INCOME,
};

export const useGetUserMenus = () => {
    const active = useLocation().pathname;
    const [userInfoData, isLogin, counselingRedMarkShow, setRedMark] = useUserInfoStore(store => [
        store.userInfoData,
        store.isLogin,
        store.redMarks?.[RedMarkScene.Counseling]?.redShow,
        store.setRedMark,
    ]);
    const [showCounseling, setShowCounseling] = useState(false);

    const allowCreate = !userInfoData?.hasTpProxy;

    const uniformLoginMyAgent = useUniformLogin(LoginSource.MY_AGENT);
    const uniformLoginCounseling = useUniformLogin(LoginSource.MY_COUNSELING);
    const uniformLoginMyPlugin = useUniformLogin(LoginSource.MY_PLUGIN);
    const uniformLoginMyKnowledge = useUniformLogin(LoginSource.MY_KNOWLEDGE);
    const uniformLoginMyWorkflow = useUniformLogin(LoginSource.MY_WORKFLOW);
    const uniformLoginMyIncome = useUniformLogin(LoginSource.MY_INCOME);

    const {clickLog} = useUbcLogV2();

    const {myAgentsMenu, myAgentsMenuPopOpen, closeMyAgentsMenuPopOpen} = useMyAgentsMenu();

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            const res = await getRedDot({
                type: RedDotType.Counseling,
                subType: CounselingRedDotSubType.Sidebar,
            });

            setRedMark(RedMarkScene.Counseling, res);
        })();
    }, [isLogin, setRedMark]);

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            try {
                // 获取是否开放付费咨询入口
                const isShow = await getCounselingAccess();
                setShowCounseling(isShow);
            } catch (error) {
                setShowCounseling(false);
            }
        })();
    }, [isLogin]);

    const checkAllowCreate = useCallback(
        (task: any): any => {
            if (!allowCreate) {
                return () => {};
            }

            task();
        },
        [allowCreate]
    );

    const navigate = useNavigate();
    const handleLinkClick: (linkUrl: string) => () => void = useCallback(
        linkUrl => {
            return () => {
                const valueName = menuClickEventMap[linkUrl];
                valueName && clickLog(valueName);
                navigate(linkUrl);

                // 点击我的智能体导航菜单，关闭引导气泡
                if (linkUrl.includes(urls.agentList.raw()) && myAgentsMenuPopOpen) {
                    closeMyAgentsMenuPopOpen();
                }
            };
        },
        [clickLog, closeMyAgentsMenuPopOpen, navigate, myAgentsMenuPopOpen]
    );

    const userMenus: MenuDetail[] = useMemo(() => {
        const menuList = [
            {
                key: urls.agent.raw(),
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span
                            className={classNames('iconfont text-base', {
                                'icon-myagent': !active.startsWith(urls.agent.raw()),
                                'icon-myagent1': active.startsWith(urls.agent.raw()),
                            })}
                        />
                        {myAgentsMenu}
                    </span>
                ),
                onClick: isLogin ? handleLinkClick(urls.agentList.raw()) : uniformLoginMyAgent,
            },
            {
                key: urls.plugin.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2 ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-a-myplugin': !active.startsWith(urls.plugin.raw()),
                                    'icon-myplugin': active.startsWith(urls.plugin.raw()),
                                })}
                            ></span>
                            <span>我的插件</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () => checkAllowCreate(isLogin ? handleLinkClick(urls.plugin.raw()) : uniformLoginMyPlugin),
            },
            {
                key: urls.dataset.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-DataSet': !active.startsWith(urls.dataset.raw()),
                                    'icon-dataset1': active.startsWith(urls.dataset.raw()),
                                })}
                            ></span>
                            <span>我的知识库</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.datasetList.raw()) : uniformLoginMyKnowledge),
            },
            {
                key: urls.workflowList.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-workflow': !active.startsWith(urls.workflowList.raw()),
                                    'icon-workflows': active.startsWith(urls.workflowList.raw()),
                                })}
                            ></span>
                            <span>我的工作流</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.workflowList.raw()) : uniformLoginMyWorkflow),
            },
            {
                key: urls.income.raw(),
                label: (
                    <span className={`text-black-base flex items-center gap-2 px-2`}>
                        <Icon
                            name={active.startsWith(urls.income.raw()) ? 'reward' : 'a-reward-unselected'}
                            hoverStyle={false}
                            className="text-base leading-none"
                        />
                        <span>我的收益</span>
                    </span>
                ),
                onClick: isLogin ? handleLinkClick(urls.income.raw()) : uniformLoginMyIncome,
            },
        ];

        const counseling = {
            key: urls.counseling.raw(),
            label: (
                <span className="flex items-center gap-2 px-2">
                    <span
                        className={classNames('iconfont text-base', {
                            'icon-pay': !active.startsWith(urls.counseling.raw()),
                            'icon-pay-select': active.startsWith(urls.counseling.raw()),
                        })}
                    />
                    <span>付费咨询</span>
                    {counselingRedMarkShow && <Badge status="error" className="-ml-2 -mt-4" />}
                </span>
            ),
            onClick: isLogin ? handleLinkClick(urls.counseling.raw()) : uniformLoginCounseling,
        };

        /** 是否展示付费咨询入口 */
        return showCounseling
            ? [
                  menuList[0],
                  counseling, // 插入付费咨询
                  ...menuList.slice(1), // 插入剩余菜单项
              ]
            : menuList;
    }, [
        active,
        myAgentsMenu,
        isLogin,
        handleLinkClick,
        uniformLoginMyAgent,
        allowCreate,
        uniformLoginMyIncome,
        counselingRedMarkShow,
        uniformLoginCounseling,
        showCounseling,
        checkAllowCreate,
        uniformLoginMyPlugin,
        uniformLoginMyKnowledge,
        uniformLoginMyWorkflow,
    ]);

    return {
        userMenus,
    };
};
