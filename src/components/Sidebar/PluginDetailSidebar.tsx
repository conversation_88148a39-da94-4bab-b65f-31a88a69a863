import {useCallback, useEffect, useState} from 'react';
import {Link, useLocation, useParams} from 'react-router-dom';
import {MenuItemType} from 'antd/es/menu/hooks/useItems';
import urls from '@/links';
import {AIServiceType, PLUGIN_TYPE_EN_LOWER_NAME} from '@/api/pluginCenter/interface';
import api from '@/api/pluginList';
import {PluginInfo} from '@/api/pluginList/interface';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {DetailSidebar} from './DetailSidebar';

const ROUTE_KEY_MAP = {
    detail: 'pluginDetail',
    version: 'pluginVersion',
} as const;

export default function PluginDetailSidebar() {
    const {id, type = PLUGIN_TYPE_EN_LOWER_NAME[AIServiceType.Ability]} = useParams();

    const {ubcClickLog} = useUbcLog();
    const handlePluginDetailClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginDetail);
    }, [ubcClickLog]);
    const handlePluginVersionClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginVersion);
    }, [ubcClickLog]);
    const handlePluginSwitchClick = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.SideBarPluginSwitch);
    }, [ubcClickLog]);

    // menu items
    const appDetailItems = [
        {
            label: (
                <Link
                    to={urls.pluginDetail.fill({id: id!, type: 'data', tab: PluginTabType.Overview})}
                    onClick={handlePluginDetailClick}
                >
                    插件概览
                </Link>
            ),
            key: 'detail',
            icon: <span className={'iconfont icon-overview text-xl'}></span>,
        },
        {
            label: (
                <Link
                    to={urls.pluginDetail.fill({id: id!, type: 'data', tab: PluginTabType.Version})}
                    onClick={handlePluginVersionClick}
                >
                    版本管理
                </Link>
            ),
            key: 'version',
            icon: <span className={'iconfont icon-versions text-xl'}></span>,
        },
    ];

    // fetch async data
    const [appList, setAppList] = useState<PluginInfo[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        (async () => {
            try {
                setLoading(true);

                // 区分能力和数据插件列表
                const res =
                    type === PLUGIN_TYPE_EN_LOWER_NAME[AIServiceType.Data]
                        ? await api.getDataPluginList({pageNo: 1, pageSize: 50})
                        : await api.getAbilityPluginList({pageNo: 1, pageSize: 50});

                setAppList(res.pluginList);
                setLoading(false);
            } catch (error) {
                setLoading(false);
                console.error(error);
            }
        })();
    }, [type]);

    // drop items
    const location = useLocation();
    /**
     * path menu route 关键字在第5位
     * http://localhost:8100/console/plugin/:type/detail/:id
     * http://localhost:8100/console/plugin/:type/version/:id
     */
    const ROUTE_MENU_INDEX = 4;
    const currentRoute = location.pathname.split('/')[ROUTE_MENU_INDEX] as keyof typeof ROUTE_KEY_MAP;

    const [current, setCurrent] = useState<{name?: string; logo?: string; path?: string}>({});
    const [dropItems, setDropItems] = useState<MenuItemType[]>([]);

    useEffect(() => {
        const dropItems: MenuItemType[] = appList?.map(item => {
            const logo = item.logo;
            const name = item.pluginName;
            const path = urls.pluginDetail.fill({id: item.pluginId, tab: 'data', type: PluginTabType.Overview});

            if (item?.pluginId === id) {
                setCurrent({
                    name,
                    logo,
                    path,
                });
            }

            return {
                label: (
                    <Link
                        to={path}
                        onClick={handlePluginSwitchClick}
                        className={'block w-[8.75rem] overflow-hidden truncate text-base'}
                    >
                        {name}
                    </Link>
                ),
                key: item?.pluginId,
                icon: <img className={'h-6 w-6 rounded-full'} src={logo} alt="icon" />,
            };
        });

        setDropItems(dropItems);
    }, [currentRoute, id, appList, type, handlePluginSwitchClick]);

    return (
        <DetailSidebar
            loading={loading}
            appDetailItems={appDetailItems}
            current={current}
            dropItems={dropItems}
            currentRoute={currentRoute}
        />
    );
}
