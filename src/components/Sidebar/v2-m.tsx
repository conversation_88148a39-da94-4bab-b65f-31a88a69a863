/**
 * @file 移动端侧边栏组件
 * <AUTHOR>
 */

import {ConfigProvider, Drawer} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useState} from 'react';
import sideBarIcon from '@/assets/sidebar-mobile.png';
import Card from '@/components/mobile/Card';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/index';
import MainMenu from './mobile/MainMenu';
import {Tools} from './mobile/Tools';
import UserInfo from './mobile/UserInfo';

const StyledDrawer = styled(Drawer)`
    .ant-drawer-body {
        padding: 0 !important;
    }
`;

export default function Left({onDrawerOpen}: {onDrawerOpen?: () => void}) {
    const [open, setOpen] = useState(false);
    const {showLog} = useUbcLogV3();

    const openDrawer = useCallback(() => {
        setOpen(true);
        // ios 上避免打开蒙层后底层列表仍可滚动问题
        document.body.style.overflow = 'hidden';

        showLog(EVENT_VALUE_CONST.SIDEBAR);
        onDrawerOpen?.();
    }, [setOpen, onDrawerOpen, showLog]);

    const closeDrawerAndNotice = useCallback(() => {
        document.body.style.overflow = 'auto';
        setOpen(false);
    }, [setOpen]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Drawer: {
                        padding: 0,
                        marginXS: 0,
                    },
                },
            }}
        >
            <div>
                <img src={sideBarIcon} className="mr-[15px] mt-1 h-[18px] w-[19px]" onClick={openDrawer} />
                <StyledDrawer
                    placement="left"
                    closable={false}
                    open={open}
                    onClose={closeDrawerAndNotice}
                    key="left"
                    width="85%"
                >
                    <div className="flex h-[calc(100%-58px)] flex-col overflow-y-auto bg-gray-bg-base">
                        <div className=" flex flex-col px-3">
                            <Card title="">
                                <MainMenu closeDrawer={closeDrawerAndNotice} />
                            </Card>
                            <Card title="常用工具" className="mt-[8px] px-[13px]">
                                <Tools closeDrawer={closeDrawerAndNotice} />
                            </Card>
                        </div>
                        <UserInfo />
                    </div>
                </StyledDrawer>
            </div>
        </ConfigProvider>
    );
}
