import {Modal} from 'antd';
import ModalM from '../mobile/Modal';

interface ModalProps {
    isMobile: boolean;
    openAgentWarningModal: boolean;
    appNum?: number;
    appLimit?: number;
    handleModalClick: () => void;
    handleModalClickOk: () => void;
}

export const AgentNumModal = ({
    isMobile,
    openAgentWarningModal,
    appNum,
    appLimit,
    handleModalClick,
    handleModalClickOk,
}: ModalProps) => {
    const ModalComponent = isMobile ? ModalM : Modal;
    const modalTitle = isMobile ? '' : '创建失败';
    const modalContentClassName = isMobile ? 'px-4' : '';
    return (
        <ModalComponent
            open={openAgentWarningModal}
            onOk={handleModalClickOk}
            onCancel={handleModalClick}
            title={modalTitle}
            okText={'管理我的智能体'}
            cancelText={'知道了'}
        >
            <p className={modalContentClassName}>
                已达到智能体创建上限{appNum}/{appLimit}，如需新增请前往【我的智能体】清理后创建！
            </p>
        </ModalComponent>
    );
};
