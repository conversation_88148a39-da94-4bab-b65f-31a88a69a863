import {Tooltip, TooltipProps} from 'antd';
import classNames from 'classnames';

type Props = TooltipProps & {
    title: string;
};

const TooltipIcon = ({title, rootClassName, ...props}: Props) => {
    return (
        <Tooltip
            title={title}
            arrow={false}
            placement="bottom"
            rootClassName={classNames('max-w-[24.375rem]', rootClassName)}
            {...props}
        >
            <span className="iconfont icon-questionCircle ml-1.5 text-sm text-gray-400"></span>
        </Tooltip>
    );
};

export default TooltipIcon;
