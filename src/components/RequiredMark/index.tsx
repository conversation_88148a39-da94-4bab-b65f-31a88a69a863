export const RequiredMark: (
    labelNode: React.ReactNode,
    info: {
        required: boolean;
    }
) => React.ReactNode = (labelNode, {required}) => {
    return required ? (
        <span className="flex items-center">
            <span>{labelNode}</span>
            <span className="ml-[6px] text-error" style={{fontFamily: 'SimSong'}}>
                *
            </span>
        </span>
    ) : (
        labelNode
    );
};
