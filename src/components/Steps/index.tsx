/**
 * 企业认证步骤条
 * huangxueping、v_jinxiaolan
 */
import {ReactNode, useMemo} from 'react';
import {ConfigProvider, Steps, StepsProps} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import omit from 'lodash/omit';

// 定义步骤条配置Props接口
interface OrgAuthStepProps extends StepsProps {
    className?: string;
}

const StyledSteps = styled(Steps)`
    .ant-steps-item {
        .ant-steps-item-container {
            display: flex;
            align-items: center;

            .ant-steps-item-icon {
                margin-right: 10px;
            }

            .ant-steps-item-title {
                padding-inline-end: 10px;
            }
        }

        &.ant-steps-item-wait .ant-steps-item-title:after {
            background-color: #dcdde0 !important;
        }

        &.ant-steps-item-finish .ant-steps-item-title:after {
            background-color: #39b362 !important;
        }
        &.ant-steps-item-process .ant-steps-item-title:after {
            background-color: #5562f2 !important;
        }
    }
`;

const StepTitle = ({title, active, finished = false}: {title: ReactNode; active: boolean; finished?: boolean}) => {
    return (
        <span
            className={classNames('font-pingfang text-[21px] font-medium leading-[21px]', {
                'text-primary': active,
                'text-gray-tertiary': !active && !finished,
                'text-[#000331]': finished,
            })}
        >
            {title}
        </span>
    );
};

const StepIcon = ({
    step,
    active,
    finished = false,
}: {
    step: number;
    active: boolean;
    finished?: boolean;
    status: string;
}) => {
    return (
        <div
            className={classNames(
                'flex h-6 w-6 items-center justify-center rounded-full font-pingfang text-sm font-medium text-white',
                {
                    'border-[2px] border-primary bg-primary': active,
                    'bg-[#8D9FBB]': !active,
                    'border-[2px] border-success bg-success': finished,
                }
            )}
        >
            {finished ? <span className="iconfont icon-a-Group2036083764 text-2xl" /> : step + 1}
        </div>
    );
};

export default function LingJingSteps(props: OrgAuthStepProps) {
    const {items, className, current = 1} = props;
    const stepItems = useMemo(() => {
        return (items || []).map(({title}, stepProcess) => ({
            title: <StepTitle title={title} active={current === stepProcess} finished={current > stepProcess} />,
            icon: (
                <StepIcon
                    step={stepProcess}
                    active={current === stepProcess}
                    finished={current > stepProcess}
                    status={current > stepProcess ? 'finished' : current === stepProcess ? 'process' : 'wait'}
                />
            ),
        }));
    }, [items, current]);

    const stepsProps = omit(props, ['items', 'className']);

    return (
        <div className={classNames('h-6', className)}>
            <ConfigProvider
                theme={{
                    components: {
                        Steps: {
                            titleLineHeight: 21,
                            customIconFontSize: 21,
                            iconSize: 24,
                        },
                    },
                }}
            >
                <StyledSteps {...stepsProps} current={current - 1} items={stepItems} responsive={false} />
            </ConfigProvider>
        </div>
    );
}
