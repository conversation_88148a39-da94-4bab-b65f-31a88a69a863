/**
 * @file 错误态样式（体验中心和我的智能体空态时复用）
 * <AUTHOR>
 *
 */
import React from 'react';
import classNames from 'classnames';
import Exclamation from '@/assets/exclamation-circle-icon.png';
import WhiteExclamation from '@/assets/exclamation-circle-white-icon.png';
import ExclamationSmall from '@/assets/icon-empty-small.png';
import {ExclamationColor} from '@/components/ErrorState/interface';

interface ErrorStateProps {
    /**
     * 错误信息
     */
    msg: string;

    /**
     * 错误信息 className
     */
    msgClassName?: string;

    /**
     * 空态高度
     */
    height?: string;

    /**
     * 小size，适用于移动端空态
     */
    smallSize?: boolean;

    /**
     * 标题下方内容，比如提示操作语
     */
    children?: React.ReactNode;

    /**
     * 感叹号图标颜色
     */
    exclamationColor?: ExclamationColor;

    /**
     * 感叹号图标 className
     */
    exclamationClassName?: string;
}

function getExclamationSrc(exclamationColor: ExclamationColor, smallSize: boolean | undefined) {
    if (smallSize) {
        return ExclamationSmall;
    }

    return exclamationColor === ExclamationColor.WHITE ? WhiteExclamation : Exclamation;
}

export const ErrorState: React.FC<ErrorStateProps> = React.memo(
    ({
        msg,
        msgClassName,
        height = '37.5rem',
        smallSize,
        exclamationColor = ExclamationColor.Default,
        exclamationClassName = '',
        children,
    }) => {
        return (
            <div className="flex w-full items-center justify-center" style={{height: height}}>
                <div className="flex flex-col items-center justify-center">
                    <img
                        src={getExclamationSrc(exclamationColor, smallSize)}
                        className={classNames(
                            'w-[96px]',
                            {
                                'mb-[12px] w-[66px]': smallSize,
                            },
                            {
                                'mb-[6px]': !smallSize,
                            },
                            exclamationClassName
                        )}
                    />
                    <span
                        className={classNames(
                            'text-center text-base font-medium leading-tight text-gray-tertiary',
                            msgClassName
                        )}
                    >
                        {msg}
                    </span>
                    <div className="mt-1 text-sm font-normal not-italic text-gray-tertiary">{children}</div>
                </div>
            </div>
        );
    }
);
