/**
 * @description Icon 组件，封装了一些 Icon 的基础的样式，包括悬浮、禁用、加载状态
 * <AUTHOR>
 */

import classNames from 'classnames';
import {css} from '@emotion/css';
import spinPng from '@/assets/spin.png';
import {colorPrimary} from '@/styles/lingjing-light-theme';

interface BaseIconProps {
    // iconfont 图标标识
    name?: string;
    // 图标禁用状态
    disabled?: boolean;
    // 是否应用悬浮样式，一般图标按钮需要有悬浮样式，对于纯图标不需要悬浮样式
    hoverStyle?: boolean;
    // 加载状态
    loading?: boolean;
}

interface ButtonIconProps extends BaseIconProps {
    name: string;
}

interface LoadingIconProps extends BaseIconProps {
    loading: true;
}

type IconProps = LoadingIconProps | ButtonIconProps;

const iconDefaultStyle = css`
    line-height: 1;
    border-radius: 0.25rem;
`;

const iconDisabledStyle = css`
    cursor: not-allowed;
    opacity: 0.3;
`;

const iconHoverStyle = css`
    cursor: pointer;
    &:hover {
        opacity: 0.3;
        color: ${colorPrimary};
    }
`;

const iconLoadingStyle = css`
    height: 1rem;
    width: 1rem;
    animation: spin 1s linear infinite;
`;

const Icon = ({
    disabled = false,
    name,
    onClick,
    className,
    loading = false,
    hoverStyle = true,
    ...props
}: IconProps & React.HTMLAttributes<HTMLSpanElement>) => {
    return loading ? (
        <img src={spinPng} className={classNames(iconLoadingStyle, className)} />
    ) : (
        <span
            className={classNames(
                `iconfont icon-${name}`,
                iconDefaultStyle,
                {
                    [iconDisabledStyle]: disabled,
                    [iconHoverStyle]: hoverStyle && !disabled,
                },
                className
            )}
            onClick={onClick}
            {...props}
        />
    );
};

export default Icon;
