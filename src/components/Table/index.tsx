/**
 * @file 表格组件
 * <AUTHOR>
 */
import React, {useCallback} from 'react';
import {ConfigProvider, Table} from 'antd';
import Empty from '@/components/Empty';

/**
 * 封装一层 Table，通过 ConfigProvider 给 Table 设置空状态组件
 *
 * @param props 与 antd 的 Table 组件的 props 一致
 */
export default function CustomTable(props: any) {
    const renderEmpty = useCallback(
        () => (
            <div className="m-[32px] h-[70px]">
                <Empty />
            </div>
        ),
        []
    );

    return (
        <ConfigProvider renderEmpty={renderEmpty}>
            <Table {...props} />
        </ConfigProvider>
    );
}
