/**
 * @file 可高亮部分文本的输入框
 * @doc 方案文档 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/659DHIQpcQ/Ed4ThGolkdONjS
 * <AUTHOR>
 */
import styled from '@emotion/styled';
import DOMPurify from 'dompurify';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import Scrollbar from '../Scrollbar';

const DEFAULT_PLACEHOLDER = '请输入内容';

interface ColoredTextareaProps {
    className?: string;
    highlightColor?: string;
    placeholder?: string | undefined;
    showClear?: boolean;
    value: string | number | bigint | readonly string[] | undefined;
    setValue: React.Dispatch<React.SetStateAction<string>>;
    onBlur?: (newValue: string, name: string) => void;
    onClear?: () => void;
}

const StyledTextarea = styled.div`
    /* 修改垂直滚动条 */
    ::-webkit-scrollbar {
        background: transparent;
        width: 4px;
    }
    ::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 5px;
    }
    // 使用伪元素实现 placeholder
    ::before {
        content: attr(placeholder);
        color: #858691;
        position: absolute;
        top: 16px;
        left: 16px;
        pointer-events: none; /* 防止伪元素影响鼠标事件 */
    }
    :not(:empty):before {
        display: none;
    }
`;
/**
 * 使用 div 实现可高亮部分文本的输入文本框
 *
 * 1. 可修改部分文字颜色
 * 2. 在光标处进行粘贴，并对粘贴的内容进行过滤
 * 3. 修改样式，匹配现有组件：灰色背景、padding、滚动条样式
 * 4. 内容为空时，展示placeholder；内容不为空时，右下角展示【清空】按钮
 */
const ColoredTextarea = forwardRef(
    (
        {
            className,
            highlightColor,
            placeholder,
            showClear = true,
            value,
            setValue,
            onBlur,
            onClear,
        }: ColoredTextareaProps,
        ref: React.Ref<any>
    ) => {
        const textareaRef = useRef<HTMLDivElement>(null);
        const clearVisible = useMemo(() => showClear && !!value, [showClear, value]);
        const [content, setContent] = useState({__html: ''});
        // 约定好的高亮文本标记与替换后的span元素的样式
        const spanPrefix = `<span style="color: ${highlightColor || '#5562F2'}">`;
        const spanSuffix = '</span>';
        const highlightMarkStart = '[';
        const highlightColorEnd = ']';

        // 初次渲染绑定监听事件，取消挂载时注销事件
        useEffect(() => {
            const element = textareaRef.current;
            if (!element) {
                return;
            }

            // 输入内容时，触发自定义的contentchange事件
            const inputEventCallback = () => {
                element.dispatchEvent(new CustomEvent('contentchange', {bubbles: true}));
            };

            element.addEventListener('input', inputEventCallback);

            // 添加监听事件，当文本内容变化时，修改设定的值，同时根据是否为空，显示或隐藏 placeholder
            const contentchangeEventCallback = () => {
                if (element.innerText.trim() === '') {
                    setContent({__html: ''});
                    setValue('');
                } else {
                    setValue(element.innerText.trim());
                }
            };

            element.addEventListener('contentchange', contentchangeEventCallback);

            // 添加粘贴事件，在光标处进行粘贴，并过滤粘贴内容
            const pasteEventCallback = (event: ClipboardEvent) => {
                event.preventDefault();
                const clipboardData = event.clipboardData;
                if (!clipboardData) {
                    return;
                }

                const text = clipboardData.getData('text/plain');

                // 获取当前光标选中区域
                const selection = window.getSelection()!;
                const range = selection.getRangeAt(0);

                // 在光标位置插入文本
                range.deleteContents();
                const textNode = document.createTextNode(text);
                range.insertNode(textNode);

                // 重置选区
                selection.removeAllRanges();
                selection.addRange(range);

                textareaRef.current?.dispatchEvent(new CustomEvent('contentchange', {bubbles: true}));
            };

            element.addEventListener('paste', pasteEventCallback);

            // 添加失去焦点事件，触发自定义的onblur事件
            const onBlurEventCallback = () => {
                onBlur && !!value && onBlur(textareaRef.current?.innerText.trim() ?? '', 'introduction');
            };

            element.addEventListener('blur', onBlurEventCallback);
            return () => {
                element.removeEventListener('input', inputEventCallback);
                element.removeEventListener('contentchange', contentchangeEventCallback);
                element.removeEventListener('paste', pasteEventCallback);
                element.removeEventListener('blur', onBlurEventCallback);
            };
        }, [onBlur, placeholder, setValue, value]);

        // 清空文本输入框内容
        const handleClear = useCallback(() => {
            setContent({__html: ''});
            setValue('');
            setTimeout(() => {
                if (textareaRef.current) {
                    // 上面修改html无法清空内容，需要选中输入框内容后删除
                    const range = document.createRange();
                    range.selectNodeContents(textareaRef.current);
                    range.deleteContents();
                    // 重新渲染后，触发 contentchange 事件
                    textareaRef.current?.dispatchEvent(new CustomEvent('contentchange', {bubbles: true}));
                }
            }, 0);
            onClear && onClear();
        }, [textareaRef, setValue, onClear]);

        // 对外暴露修改部分文字颜色的方法
        useImperativeHandle(ref, () => {
            return {
                highlightPartialText: (text: string) => {
                    // 文本由服务器接口返回，安全起见，此处过滤特殊字符，防止XSS攻击
                    const safeHtml = DOMPurify.sanitize(text);
                    const highlightHtml = safeHtml
                        .replaceAll(highlightMarkStart, spanPrefix)
                        .replaceAll(highlightColorEnd, spanSuffix);
                    setContent({__html: highlightHtml});

                    // 重新渲染后，触发 contentchange 事件，更新内容
                    setTimeout(() => {
                        textareaRef.current?.dispatchEvent(new CustomEvent('contentchange', {bubbles: true}));
                    }, 0);
                },
            };
        });

        /* bca-disable */
        return (
            <Scrollbar className={`overflow-y-auto bg-gray-bg-base ${className}`}>
                {/* FIXME:外部包裹div避免key报错，测试后发现StyledTextarea去掉ref或者单独添key均能去掉key报错，根因不详 */}
                <div>
                    <StyledTextarea
                        placeholder={placeholder ?? DEFAULT_PLACEHOLDER}
                        dangerouslySetInnerHTML={content}
                        ref={textareaRef}
                        contentEditable
                        className="h-full overflow-y-scroll p-4 focus:outline-none"
                    />
                </div>
                {clearVisible && (
                    <div
                        className="absolute bottom-[6px] right-[10px] cursor-pointer text-gray-tertiary"
                        onClick={handleClear}
                    >
                        清空
                    </div>
                )}
            </Scrollbar>
        );
    }
);

export default ColoredTextarea;
