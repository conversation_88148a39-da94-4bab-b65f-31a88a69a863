/**
 * @file 插件自定义工具-functionInfo
 * <AUTHOR>
 */

import {Tag} from 'antd';
import {css} from '@emotion/css';
import classNames from 'classnames';
import {AgentPluginFunction} from '@/api/agentEdit';
import StyledPopover from '../Popover/StyledPopover';

const PopoverOverlayCSS = css`
    .ant-popover-content .ant-popover-inner {
        border-radius: 9px !important;
        padding: 1rem;
        box-shadow: 0px 30px 200px 0px rgba(29, 34, 82, 0.2);
        .content-list {
            &::-webkit-scrollbar {
                width: 0.25rem;
                height: 0.25rem;
            }
            &::-webkit-scrollbar-thumb {
                background: #d9d9d9;
                border-radius: 3px;
            }
        }
    }
`;

// 函数functionName + 气泡展示参数列表
export default function FunctionInfo({
    functionInfo,
    showFunctionDesc,
}: {
    functionInfo: AgentPluginFunction;
    /** 是否显示function说明 */
    showFunctionDesc?: boolean;
}) {
    return (
        <div>
            {functionInfo.paramList?.length ? (
                <>
                    <div className="flex items-center gap-[6px]">
                        <span className="truncate">{functionInfo.operationId}</span>

                        <StyledPopover
                            trigger="hover"
                            placement="right"
                            content={
                                <ul className="content-list -mx-2 -my-1 max-h-[23.75rem] w-[23.8125rem] overflow-y-auto px-2 py-1">
                                    {functionInfo.paramList.map((paramInfo, index) => (
                                        <li
                                            key={paramInfo.paramName}
                                            className={classNames('py-[0.62rem]', {
                                                'border-t border-gray-border-secondary/70': index > 0,
                                                'pt-0': index === 0,
                                                'pb-0': index === (functionInfo.paramList?.length || 0) - 1,
                                            })}
                                        >
                                            <p className="flex items-center">
                                                <span className="font-medium">{paramInfo.paramName}</span>
                                                <span className="ml-2">
                                                    {paramInfo.paramType}
                                                    {paramInfo.paramItems?.paramType && (
                                                        <span>{'<' + paramInfo.paramItems.paramType + '>'}</span>
                                                    )}
                                                </span>
                                                <Tag
                                                    bordered={false}
                                                    className="ml-2 rounded-[3px] pe-[3px] ps-[3px] text-xs"
                                                >
                                                    {paramInfo.required ? '必填' : '选填'}
                                                </Tag>
                                            </p>
                                            <p className="text-gray-tertiary">{paramInfo.paramDesc}</p>
                                        </li>
                                    ))}
                                </ul>
                            }
                            arrow
                            overlayClassName={PopoverOverlayCSS}
                        >
                            <span className="iconfont icon-tip cursor-pointer text-sm text-gray-tertiary"></span>
                        </StyledPopover>
                    </div>
                    {showFunctionDesc && <p className="text-gray-tertiary">{functionInfo.functionDesc}</p>}
                </>
            ) : (
                <div className="flex items-center gap-[6px]">
                    <span className="truncate">{functionInfo.operationId}</span>
                    {showFunctionDesc && <p className="text-gray-tertiary">{functionInfo.functionDesc}</p>}
                </div>
            )}
        </div>
    );
}
