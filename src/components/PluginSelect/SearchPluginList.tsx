/**
 * @file 插件检索列表
 * <AUTHOR>
 */

import {Button, Checkbox, Collapse, ConfigProvider, Divider} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useEffect, useState} from 'react';
import cloneDeep from 'lodash/cloneDeep';
import {AgentPluginFunction, AgentPluginFunctionIds, AgentPluginInfo} from '@/api/agentEdit';
import Empty from '@/components/Empty';
import Loading from '@/components/Loading';
import urls from '@/links';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {PluginStatus} from '@/api/pluginCenter/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {MyPluginListType} from '@/api/plugin';
import Scrollbar from '../Scrollbar';
import FunctionInfo from './FunctionInfo';
import CollapseLabel from './CollapseLabel';

const StyledCollapse = styled(Collapse)`
    &.ant-collapse {
        .ant-collapse-item {
            .ant-collapse-header {
                align-items: center !important;
                .ant-collapse-expand-icon {
                    padding-right: 15px;
                }
                .ant-collapse-arrow {
                    font-size: 1rem;
                    svg {
                        display: none;
                    }
                    // 对应icon-Down字体图标
                    &::after {
                        font-family: 'iconfont' !important;
                        content: '\\e652';
                    }
                }
            }
            &.ant-collapse-item-active {
                .ant-collapse-arrow {
                    transform: rotate(180deg);
                }
            }
        }
        .ant-collapse-content {
            /* margin: 0 16px; */
            background-color: transparent;
            .ant-collapse-content-box {
                padding-top: '0px !important';
                .ant-checkbox {
                    & + span {
                        display: block;
                        padding-inline: 0;
                        flex: 1;
                    }
                }
            }
        }
    }
`;

const defaultCollapseItemStyle = {
    marginBottom: 9,
    background: '#FFFFFF',
    borderRadius: 9,
    border: '1px solid #EDEEF0',
};

const activeCollapseItemStyle = {
    marginBottom: 9,
    borderRadius: 9,
    background: '#F5F6FA',
    border: '1px solid #F5F6FA',
};

interface Props {
    /** 搜索loading */
    loading: boolean;
    /** 搜索的插件列表  */
    pluginList: AgentPluginInfo[];
    /** 最多可选Function个数 */
    maxFunctionsNum: number;
    selectedFunctions: AgentPluginFunctionIds[];
    setSelectedFunctions: React.Dispatch<React.SetStateAction<AgentPluginFunctionIds[]>>;
    onReachBottom: () => void;
    listType: MyPluginListType;
    onFavourite: (plugin: AgentPluginInfo) => void;
}

export default function SearchPluginList({
    loading,
    pluginList,
    onReachBottom,
    maxFunctionsNum,
    selectedFunctions,
    setSelectedFunctions,
    listType,
    onFavourite,
}: Props) {
    const [activeItems, setActiveItems] = useState<string | string[]>([]);
    const handleCollapseChange = useCallback((key: string | string[]) => {
        setActiveItems(key);
    }, []);
    useEffect(() => {
        if (pluginList.length === 0) {
            setActiveItems([]);
        }
    }, [pluginList.length]);

    const getOperationIsChecked = useCallback(
        (pluginInfo: AgentPluginInfo, operationId: string) => {
            return selectedFunctions
                .find(item => pluginInfo.pluginId === item.pluginId)
                ?.operationIds?.includes(operationId);
        },
        [selectedFunctions]
    );

    // 选中的插件 function 未达到最大限制个数且插件状态是"已发布"时为可点，否则未选中的 Checkbox 置灰
    const getOperationIsDisabled = (pluginInfo: AgentPluginInfo, operationId: string) => {
        const selectedOperationCount = selectedFunctions.reduce((pre, cur) => pre + cur.operationIds?.length || 0, 0);
        if (selectedOperationCount < maxFunctionsNum && pluginInfo.pluginStatus === PluginStatus.Online) {
            return false;
        } else {
            return !selectedFunctions
                .find(item => pluginInfo.pluginId === item.pluginId)
                ?.operationIds?.includes(operationId);
        }
    };

    const {clickLog} = useUbcLogV3();
    const handleClickOperation = useCallback(
        (pluginInfo: AgentPluginInfo, operationId: string) => () => {
            const checked = !getOperationIsChecked(pluginInfo, operationId);
            if (checked) {
                clickLog(EVENT_VALUE_CONST.TOOL_TICK, {
                    toolName: pluginInfo.pluginName,
                    toolId: pluginInfo.pluginId,
                });
            }

            setSelectedFunctions(prev => {
                const prevFunctions = cloneDeep(prev);
                const func = prevFunctions.find(func => func.pluginId === pluginInfo.pluginId);
                if (func) {
                    if (checked) {
                        func.operationIds?.push(operationId);
                    } else {
                        func.operationIds = func.operationIds?.filter(id => id !== operationId);
                    }
                } else if (checked) {
                    prevFunctions.push({
                        ...pluginInfo,
                        operationIds: [operationId],
                    });
                } else {
                    prevFunctions.splice(
                        prevFunctions.findIndex(func => func.pluginId === pluginInfo.pluginId),
                        1
                    );
                }
                return [...prevFunctions];
            });
        },
        [clickLog, getOperationIsChecked, setSelectedFunctions]
    );

    const handleCreatePlugin = useCallback(() => {
        window.open(urls.pluginAbilityCreate.raw());
    }, []);

    const handleToPluginStore = useCallback(() => {
        window.open(urls.centerPlugin.raw());
    }, []);

    return (
        <Scrollbar
            onReachBottom={onReachBottom}
            className="relative -mr-1 min-h-0 flex-grow overflow-y-auto"
            reachOffset={100}
        >
            {pluginList.length > 0 ? (
                <div className="flex">
                    <ConfigProvider
                        theme={{
                            components: {
                                Collapse: {
                                    colorFillAlter: '#fff',
                                    headerPadding: '12px 15px',
                                    contentPadding: '0px 15px',
                                },
                            },
                        }}
                    >
                        <StyledCollapse
                            bordered={false}
                            className="w-full"
                            expandIconPosition="start"
                            items={pluginList.map(pluginInfo => ({
                                forceRender: true,
                                key: pluginInfo.pluginId,
                                label: (
                                    <CollapseLabel
                                        pluginInfo={pluginInfo}
                                        onFavourite={onFavourite}
                                        listType={listType}
                                    />
                                ),
                                children: (
                                    <div>
                                        <Divider className="m-0 border-t-[0.5px] border-t-gray-border-secondary" />
                                        {pluginInfo.functionList?.length ? (
                                            <div className="flex flex-col gap-3 py-3">
                                                {pluginInfo.functionList.map((functionInfo: AgentPluginFunction) => (
                                                    <Checkbox
                                                        key={functionInfo.operationId}
                                                        className="flex w-full justify-between gap-4"
                                                        checked={getOperationIsChecked(
                                                            pluginInfo,
                                                            functionInfo.operationId
                                                        )}
                                                        disabled={getOperationIsDisabled(
                                                            pluginInfo,
                                                            functionInfo.operationId
                                                        )}
                                                        onChange={handleClickOperation(
                                                            pluginInfo,
                                                            functionInfo.operationId
                                                        )}
                                                    >
                                                        <FunctionInfo functionInfo={functionInfo} showFunctionDesc />
                                                    </Checkbox>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-center text-gray-tertiary">暂无可选插件</p>
                                        )}
                                    </div>
                                ),
                                style: activeItems.includes(pluginInfo.pluginId)
                                    ? activeCollapseItemStyle
                                    : defaultCollapseItemStyle,
                            }))}
                            onChange={handleCollapseChange}
                        />
                    </ConfigProvider>
                </div>
            ) : loading ? (
                <Loading />
            ) : (
                <Empty
                    desc={
                        <div>
                            {listType === MyPluginListType.MY ? (
                                <>
                                    暂无插件，去
                                    <Button className="px-0 font-normal" type="link" onClick={handleCreatePlugin}>
                                        新建插件
                                    </Button>
                                    或
                                </>
                            ) : listType === MyPluginListType.FAVOURITE ? (
                                <>暂无收藏插件，</>
                            ) : (
                                <></>
                            )}
                            去
                            <Button className="px-0 font-normal" type="link" onClick={handleToPluginStore}>
                                插件商店
                            </Button>
                            选择
                        </div>
                    }
                />
            )}
            {/* 滚动到底部时展现加载态 */}
            {loading && pluginList.length > 0 && (
                <div className="flex items-center justify-center pb-6 pt-[12px]">
                    <span className="text-xs leading-3 text-flow-hover">加载中...</span>
                </div>
            )}
        </Scrollbar>
    );
}
