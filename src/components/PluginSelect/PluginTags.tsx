/**
 * @file 插件和工作流相关的 tag 组件
 * <AUTHOR>
 */
import {PluginStatus} from '@/api/pluginCenter/interface';
import Tag from '@/components/Tag';

/** 官方Tag */
export const PluginOfficialTag = () => {
    return (
        <Tag className="m-0 text-[#FF8200]" color="#FF82001A">
            官方
        </Tag>
    );
};

export const PluginStatusTag = ({status, showOnlineStatus}: {status: PluginStatus; showOnlineStatus?: boolean}) => {
    if (status === PluginStatus.Online && !showOnlineStatus) {
        return null;
    }

    return status === PluginStatus.Online ? (
        <Tag className="m-0" color="success">
            已发布
        </Tag>
    ) : (
        <Tag className="m-0 text-[#272933]" color="#F5F6FA">
            未发布
        </Tag>
    );
};
