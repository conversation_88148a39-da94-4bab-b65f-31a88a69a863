/**
 * @file 插件列表折叠标签
 * <AUTHOR>
 */

import {Avatar, Button} from 'antd';
import {MouseEvent, useCallback} from 'react';
import dayjs from 'dayjs';
import {AgentPluginInfo, PluginTag} from '@/api/agentEdit';
import {AIServiceType, PLUGIN_TYPE_EN_LOWER_NAME} from '@/api/pluginCenter/interface';
import urls from '@/links';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {dealWithPV} from '@/modules/center/utils';
import {MyPluginListType} from '@/api/plugin';
import {PluginOfficialTag, PluginStatusTag} from './PluginTags';
import {isIndependent} from './util';

export default function CollapseLabel({
    pluginInfo,
    onFavourite,
    listType,
}: {
    pluginInfo: AgentPluginInfo;
    onFavourite: (pluginInfo: AgentPluginInfo) => void;
    listType: MyPluginListType;
}) {
    // 打开插件详情页
    const toDetail = useCallback(
        (e: MouseEvent<HTMLDivElement>) => {
            e.stopPropagation();
            // safari 浏览器直接跳转会失效
            setTimeout(() => {
                window.open(
                    urls.pluginDetail.fill({
                        id: pluginInfo.pluginId,
                        type: PLUGIN_TYPE_EN_LOWER_NAME[AIServiceType.Ability],
                        tab: PluginTabType.Overview,
                    })
                );
            }, 0);
        },
        [pluginInfo.pluginId]
    );

    const handleClickFavourite = useCallback(
        (e: MouseEvent<HTMLDivElement>) => {
            e.stopPropagation();
            onFavourite(pluginInfo);
        },
        [onFavourite, pluginInfo]
    );

    const formattedPublishTime = dayjs(+pluginInfo.latestPublishTime).format('YYYY/MM/DD  HH:mm');

    return (
        <div className="flex items-center">
            <Avatar size="small" src={pluginInfo.logo} className="h-10 w-10 border-[3px] border-solid border-white" />
            <div className="ml-3 w-0 flex-1 flex-col">
                <p className="flex items-center gap-[6px]">
                    <span className="text-sm font-medium">{pluginInfo.pluginName}</span>
                    {pluginInfo.pluginTag === PluginTag.Official && <PluginOfficialTag />}
                    <PluginStatusTag status={pluginInfo.pluginStatus} />
                </p>
                <p className="mt-[3px] w-full truncate text-sm">{pluginInfo.pluginDesc}</p>
                {listType !== MyPluginListType.MY && pluginInfo.developerName && (
                    <p className="mt-[6px] flex h-[18px] items-center text-xs text-[#818283]">
                        <span className="ml-1">
                            {pluginInfo.pluginTag === PluginTag.Official
                                ? '@文心智能体平台官方'
                                : `@${pluginInfo.developerName}`}
                        </span>
                        <span className="iconfont icon-fire ml-3 text-sm leading-none"></span>
                        <span className="ml-1">{dealWithPV(pluginInfo.agentNum || 0)}</span>
                        {pluginInfo.isFavorited ? (
                            <span
                                onClick={handleClickFavourite}
                                className="iconfont icon-star-fill ml-[9px] text-sm leading-none text-[#ffc741]"
                            />
                        ) : (
                            <span
                                onClick={handleClickFavourite}
                                className="iconfont icon-star ml-[9px] text-sm leading-none hover:text-colorTextDefault"
                            />
                        )}
                        <span className="ml-1">{dealWithPV(pluginInfo.favorNum || 0)}</span>
                        <div className="flex-grow"></div>
                        <span className="whitespace-pre">发布于{formattedPublishTime}</span>
                    </p>
                )}
            </div>
            {listType === MyPluginListType.MY && !isIndependent(pluginInfo) && (
                <Button type="link" className="-mr-4 font-normal" onClick={toDetail}>
                    查看
                </Button>
            )}
        </div>
    );
}
