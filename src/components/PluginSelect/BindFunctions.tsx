/**
 * @file 关联插件自定义工具集
 * <AUTHOR>
 */

import yaml from 'js-yaml';
import {message, Switch, Tooltip, Modal} from 'antd';
import {ReactNode, useCallback, useEffect, useMemo, useState} from 'react';
import cloneDeep from 'lodash/cloneDeep';
import difference from 'lodash/difference';
import classnames from 'classnames';
import styled from '@emotion/styled';
import {
    AgentPluginFunction,
    AgentPluginFunctionIds,
    PluginTag,
    PluginStyleStatus,
    queryPluginOperationDetail,
    bindPluginStyle,
    AgentPluginInfo,
} from '@/api/agentEdit';
import {
    PLUGIN_TYPE_EN_LOWER_NAME,
    PluginId,
    AIServiceType,
    PluginStatus,
    PluginPublishType,
} from '@/api/pluginCenter/interface';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import previewContainerEmitter from '@/modules/agentPromptEditV2/components/PreviewContainer/emitter';
import urls from '@/links';
import {PopupName} from '@/api/beginnerGuide/interface';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {LJExtData} from '@/utils/logger';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {getOperationResponse} from '@/modules/pluginEdit/components/operationList/utils';
import {OpenApiYaml} from '@/modules/pluginEdit/components/operationList/interface';
import {useAIBuilding} from '@/modules/agentPromptEditV2/hooks/useAbortableRequest';
import {
    checkPluginNormal,
    getSelectPluginValue,
    isIndependent,
    parseSelectPluginValue,
} from '@/components/PluginSelect/util';
import {usePluginDataStore} from '@/store/plugin/usePluginDataStore';
import {DefaultStyleId} from '@/components/BindStyle/const';
import BindStyleModal, {BindStyleModalProps} from '@/components/BindStyle/BindStyleModal';
import {StyleBindInfo} from '@/components/BindStyle/interface';
import CustomPopover from '@/components/Popover';
import api from '@/api/beginnerGuide';
import {queryPluginStyleStatus} from '@/api/agentEdit/index';
import PolishPreviewImage from '@/modules/agentPromptEditV2/pc/components/pluginFunctions/PolishPreviewImage';
import {useNoFunctionCallModelContext} from '@/modules/agentPromptEditV2/pc/context/NoFunctionCallModelContext';
import {PluginOfficialTag} from './PluginTags';
import FunctionInfo from './FunctionInfo';

const StyledModal = styled(Modal)`
    .ant-modal-content {
        padding: 24px !important;

        .ant-modal-header {
            margin-bottom: 3px;

            .ant-modal-title {
                font-size: 18px;
                line-height: 24px;
            }
        }
        .ant-modal-close {
            top: 25px;
            right: 21px;
        }
    }
`;
const MAX_PLUGIN_NAME_LENGTH = 10;

const truncatePluginName = (name: string): string => {
    return name.length > MAX_PLUGIN_NAME_LENGTH ? `${name.slice(0, MAX_PLUGIN_NAME_LENGTH)}...` : name;
};

const getPluginStyleStatusId = (pluginId: string, operationId: string): string => {
    return `${pluginId}###${operationId}`;
};

const getPluginStyleTooltip = (pluginInfo: AgentPluginInfo, pluginUiStatus: PluginStyleStatus | undefined): string => {
    if (pluginInfo.pluginStatus === PluginStatus.Offline) {
        return '';
    }

    if (pluginInfo.pluginScope === PluginPublishType.Store) {
        return '暂不支持配置卡片样式';
    }

    if (pluginInfo.pluginTag === PluginTag.Official || pluginInfo.pluginTag === PluginTag.Independent) {
        return '暂不支持配置卡片样式';
    }

    if (pluginUiStatus?.hasStyle) {
        return '已配置卡片样式，不影响插件在其他智能体中的样式';
    }

    return '配置卡片样式，不影响插件在其他智能体中的样式';
};

/** 插件名称 + 标签展示 */
const PluginInfo = ({pluginInfo}: {pluginInfo: AgentPluginInfo}) => {
    const {readonly} = usePromptEditContext();
    const {isNoFunctionCallModel} = useNoFunctionCallModelContext();
    if (!pluginInfo.pluginName) {
        return null;
    }

    const isTruncated = pluginInfo.pluginName?.length > MAX_PLUGIN_NAME_LENGTH;

    const pluginNameElement = (
        <span
            className={classnames('whitespace-nowrap', {
                'text-error': pluginInfo.pluginStatus !== PluginStatus.Online,
                'text-gray-tertiary': readonly || isNoFunctionCallModel,
            })}
        >
            {truncatePluginName(pluginInfo.pluginName)}
        </span>
    );

    return (
        <>
            {isTruncated ? (
                <Tooltip title={pluginInfo.pluginName} placement="top" arrow={false}>
                    {pluginNameElement}
                </Tooltip>
            ) : (
                pluginNameElement
            )}
            {pluginInfo.pluginTag === PluginTag.Official && <PluginOfficialTag />}
        </>
    );
};

// 打开插件详情页
const openPluginDetail = (pluginId: PluginId) => {
    window.open(
        urls.pluginDetail.fill({
            id: pluginId,
            type: PLUGIN_TYPE_EN_LOWER_NAME[AIServiceType.Ability],
            tab: PluginTabType.Overview,
        })
    );
};

function ToFixPlugin(props: {pluginInfo: AgentPluginInfo; children: ReactNode}) {
    return (
        <div className="pb-[9px] text-xs text-colorTextDefault">
            <div className="flex items-center justify-between rounded-[9px] bg-white px-[9px] py-[7px]">
                <span>{props.children}</span>
                {/* 我的插件显示去修复 */}
                {props.pluginInfo?.self && !isIndependent(props.pluginInfo) && (
                    <a className="text-gray-tertiary" onClick={() => openPluginDetail(props.pluginInfo.pluginId)}>
                        去修复
                    </a>
                )}
            </div>
        </div>
    );
}

interface CheckIsAiRecommend {
    pluginId: string;
    operationId: string;
}

type BindStyleModalData = Pick<
    BindStyleModalProps,
    'bindInfo' | 'response' | 'hasCustomStyle' | 'hasMarkdownStyle' | 'titleSuffix' | 'isPluginStyleUpdate'
> & {
    pluginId: string;
    operationId: string;
    loading: boolean;
};

const getAllFunctions = (plugins: AgentPluginInfo[]): string[] => {
    return plugins.map(plugin => plugin.functionList?.map(func => `${plugin.pluginId}###${func.operationId}`)).flat();
};

interface Props {
    /**
     * 更新Agent表单保存提交数据，插件Ids和FunctionIds
     * @param pluginFunctionIdsList AgentPluginFunctionIds[]
     * @returns
     */
    onChange?: (pluginFunctionIdsList: AgentPluginFunctionIds[]) => void;
}

const NO_STYLE_DISABLED_TIP = '为确保可读性，插件无样式时需经过润色，不允许关闭';
const OFFICIAL_DISABLED_TIP = '官方插件暂不支持自定义';

function getPolishDisabledTip(pluginTag: PluginTag, pluginStyleStatus?: PluginStyleStatus): string {
    // 官方插件不支持改动开关
    if (pluginTag === PluginTag.Official) {
        return OFFICIAL_DISABLED_TIP;
    }

    // 非官方插件且没有绑定样式的情况下
    if (!pluginStyleStatus?.hasStyle) {
        return NO_STYLE_DISABLED_TIP;
    }

    return '';
}

/**
 * 是否叠加大模型润色开关
 */
function PluginPolishEntry({
    openPolishModal,
    onPolishModalClose,
    openTip,
    onRemoveTip,
    onPolishModalOpen,
    isPolish,
    onChangePolish,
    disabledTip,
    disabledBtn = false,
}: {
    openPolishModal: boolean;
    onPolishModalOpen: () => void;
    onPolishModalClose: () => void;
    openTip: boolean;
    onRemoveTip: () => void;
    isPolish: boolean;
    onChangePolish: (value: boolean) => void;
    disabledTip: string;
    disabledBtn: boolean;
}) {
    const [polishEnabled, setPolishEnabled] = useState(isPolish);

    // 点击开关，切换润色状态
    const handleSwitch = useCallback((value: boolean) => {
        setPolishEnabled(value);
    }, []);

    // 更新润色状态
    useEffect(() => {
        setPolishEnabled(isPolish);
    }, [isPolish]);

    // 点击弹窗确定
    const handleConfirm = useCallback(() => {
        // 关闭弹窗
        onPolishModalClose();

        // 只在状态发生变化时才触发更新
        if (polishEnabled !== isPolish) {
            onChangePolish(polishEnabled);
        }
    }, [isPolish, onChangePolish, onPolishModalClose, polishEnabled]);

    // 点击弹窗取消
    const handleCancel = useCallback(() => {
        // 关闭弹窗
        onPolishModalClose();
        // 恢复初始润色状态
        setPolishEnabled(isPolish);
    }, [isPolish, onPolishModalClose]);

    return (
        <>
            <CustomPopover
                placement="top"
                align={{offset: [0, -16]}}
                autoAdjustOverflow={false}
                open={openTip}
                content="插件支持选择是否叠加大模型润色，关闭后若命中该插件，则直接返回插件回复内容"
                onClose={onRemoveTip}
                priorityLevel={0}
                type="primary"
            >
                <span
                    className={classnames('iconfont icon-imagine text-sm', {
                        'cursor-pointer hover:text-primary active:text-textDisabled': !disabledBtn,
                        'cursor-not-allowed opacity-30': disabledBtn,
                    })}
                    onClick={onPolishModalOpen}
                />
            </CustomPopover>
            <StyledModal
                centered
                width={600}
                open={openPolishModal}
                title="设置模型润色"
                onCancel={handleCancel}
                footer={[
                    <button
                        key="cancel"
                        className="mr-[12px] rounded-[6px] border border-solid border-[#DEE0E3] px-[16px] py-[5px]"
                        onClick={handleCancel}
                    >
                        取消
                    </button>,
                    <button
                        key="confirm"
                        className="rounded-[6px] bg-primary px-[16px] py-[5px] text-white"
                        onClick={handleConfirm}
                    >
                        确定
                    </button>,
                ]}
                maskClosable={false}
            >
                <p className="text-sm text-gray-tertiary">
                    插件配置样式后，支持选择是否叠加润色。关闭后若命中该插件，则只返回插件结果，不再叠加大模型润色。
                </p>
                <div className="mb-1.5 mt-3 flex items-center justify-between">
                    <div className="text-sm font-medium leading-[22px]">叠加大模型润色</div>
                    <span>
                        {disabledTip ? (
                            <Tooltip title={disabledTip}>
                                <Switch checked={polishEnabled} onChange={handleSwitch} disabled={!!disabledTip} />
                            </Tooltip>
                        ) : (
                            <Switch checked={polishEnabled} onChange={handleSwitch} disabled={!!disabledTip} />
                        )}
                    </span>
                </div>
                <div className="mb-1.5 mt-3 text-sm font-medium">效果预览</div>
                <PolishPreviewImage />
            </StyledModal>
        </>
    );
}

/**
 * 卡片样式配置
 */
function PluginStyleEntry({
    pluginInfo,
    functionInfo,
    pluginStyleStatus,
    pluginStyleShow,
    isFirstPlugin,
    onRemoveStylePopover,
    onOpenBindStyle,
}: {
    pluginInfo: AgentPluginInfo;
    functionInfo: AgentPluginFunction;
    pluginStyleStatus?: PluginStyleStatus;
    pluginStyleShow: boolean;
    isFirstPlugin: boolean;
    onRemoveStylePopover: () => void;
    onOpenBindStyle: (pluginInfo: AgentPluginInfo, functionInfo: AgentPluginFunction) => void;
}) {
    const disabledBtn =
        pluginInfo.pluginTag === PluginTag.Official ||
        pluginInfo.pluginTag === PluginTag.Independent ||
        pluginInfo.pluginStatus === PluginStatus.Offline ||
        pluginInfo.pluginScope === PluginPublishType.Store;

    return (
        <CustomPopover
            placement="top"
            align={{offset: [0, -16]}}
            autoAdjustOverflow={false}
            open={pluginStyleShow && isFirstPlugin}
            type="primary"
            content="插件支持配置回复气泡样式啦，快来试试吧～"
            onClose={onRemoveStylePopover}
        >
            <Tooltip
                overlayInnerStyle={{pointerEvents: 'none'}}
                title={getPluginStyleTooltip(pluginInfo, pluginStyleStatus)}
            >
                <span
                    className={classnames('iconfont text-sm', {
                        'icon-a-Property1yipeizhi text-primary': pluginStyleStatus?.hasStyle,
                        'icon-a-Property1peizhi text-black': !pluginStyleStatus?.hasStyle,
                        'cursor-pointer hover:text-primary active:text-textDisabled': !disabledBtn,
                        'cursor-not-allowed opacity-30': disabledBtn,
                    })}
                    onClick={() => {
                        !disabledBtn && onOpenBindStyle(pluginInfo, functionInfo);
                    }}
                />
            </Tooltip>
        </CustomPopover>
    );
}

// eslint-disable-next-line max-statements
export default function BindPluginFunctions({onChange}: Props) {
    const {appId, aiRecommendPlugins, setAgentPlugins, setAiRecommendPlugins, setHasChangeForm} = usePromptEditStoreV2(
        store => ({
            appId: store.agentConfig.agentInfo.appId,
            aiRecommendPlugins: store.aiRecommendPlugins,
            setAgentPlugins: store.setAgentPlugins,
            setAiRecommendPlugins: store.setAiRecommendPlugins,
            setHasChangeForm: store.setHasChangeForm,
        })
    );

    const {readonly} = usePromptEditContext();
    const {isNoFunctionCallModel} = useNoFunctionCallModelContext();
    const [building] = useAIBuilding('plugin');

    const {pluginData} = usePluginDataStore(store => ({
        pluginData: store.pluginData,
    }));

    // 校验插件是否正常
    const showAgentPluginList = useMemo(
        () =>
            pluginData.map(pluginInfo => ({
                ...pluginInfo,
                isValid: checkPluginNormal(pluginInfo),
            })),
        [pluginData]
    );

    const {showLog, clickLog} = useUbcLogV3();

    const [pluginStyleStatusMap, setPluginStyleStatusMap] = useState<Map<string, PluginStyleStatus>>(new Map());

    const fetchPluginStyleStatus = useCallback(async () => {
        const functions = showAgentPluginList
            // 先筛选出未下线的插件
            .filter(pluginInfo => pluginInfo.pluginStatus !== PluginStatus.Offline)
            .map(
                pluginInfo =>
                    pluginInfo.functionList?.map(functionInfo => ({
                        operationId: functionInfo.operationId,
                        pluginId: pluginInfo.pluginId,
                    }))
            )
            .flat();

        if (appId && functions.length) {
            const uniqIdResMap = new Map<string, PluginStyleStatus>();

            (await queryPluginStyleStatus({appId, functions})).forEach(item => {
                uniqIdResMap.set(getPluginStyleStatusId(item.pluginId, item.operationId), item);
            });

            const map = new Map<string, PluginStyleStatus>();
            showAgentPluginList.forEach(
                pluginInfo =>
                    pluginInfo.functionList?.forEach(functionInfo => {
                        const item = uniqIdResMap.get(
                            getPluginStyleStatusId(pluginInfo.pluginId, functionInfo.operationId)
                        );
                        item && map.set(getPluginStyleStatusId(pluginInfo.pluginId, functionInfo.operationId), item);
                    })
            );
            setPluginStyleStatusMap(map);
        }
    }, [showAgentPluginList, appId]);

    useEffect(() => {
        fetchPluginStyleStatus();
    }, [fetchPluginStyleStatus]);

    const [hasNewPlugin, setHasNewPlugin] = useState<boolean>(false);
    const [pluginStyleShowServer, setPluginStyleShowServer] = useState<boolean>(false);

    const hasCanConfigStyle = showAgentPluginList.some(pluginInfo => pluginInfo.pluginTag !== PluginTag.Official);
    const pluginStyleShow = hasNewPlugin && hasCanConfigStyle && pluginStyleShowServer;

    const [pluginPolishTipShowServer, setPluginPolishTipShowServer] = useState<boolean>(false);
    const pluginPolishTipShow = hasNewPlugin && !pluginStyleShow && pluginPolishTipShowServer;

    useEffect(() => {
        if (!appId) {
            return;
        }
        // api.getPopup({name: PopupName.PluginReply, appId}).then(({show}) => setPluginStyleShowServer(show));
        // api.getPopup({name: PopupName.PluginPolish, appId}).then(({show}) => setPluginPolishTipShowServer(show));

        // 因 C 端官方插件样式优化暂未上线，引导气泡隐藏，直接 set 为 false
        setPluginStyleShowServer(false);
        setPluginPolishTipShowServer(false);
    }, [appId]);

    const removePluginStylePopover = useCallback(() => {
        if (!appId) {
            return;
        }

        if (pluginStyleShow) {
            setPluginStyleShowServer(false);
            api.recordPopup({name: PopupName.PluginReply, appId});
        }
    }, [setPluginStyleShowServer, appId, pluginStyleShow]);

    const removePluginPolishPopover = useCallback(() => {
        if (!appId) {
            return;
        }

        if (pluginPolishTipShow) {
            setPluginPolishTipShowServer(false);
            api.recordPopup({name: PopupName.PluginPolish, appId});
        }
    }, [setPluginPolishTipShowServer, appId, pluginPolishTipShow]);

    const [pluginPolishFunctionInfo, setPluginPolishFunctionInfo] = useState<AgentPluginFunction | null>(null);
    const openPluginPolishPopover = useCallback(
        (pluginInfo: AgentPluginInfo, functionInfo: AgentPluginFunction) => {
            removePluginPolishPopover();
            setPluginPolishFunctionInfo(functionInfo);
            clickLog(EVENT_VALUE_CONST.PLUGIN_REPLY_SET, {
                [EVENT_EXT_KEY_CONST.PLUGIN_ID]: pluginInfo.pluginId,
                [EVENT_EXT_KEY_CONST.E_OPERATION_ID]: functionInfo.operationId,
            } as LJExtData);
            showLog(EVENT_VALUE_CONST.PLUGIN_REPLY_SET_BOX, {
                [EVENT_EXT_KEY_CONST.PLUGIN_ID]: pluginInfo.pluginId,
                [EVENT_EXT_KEY_CONST.E_OPERATION_ID]: functionInfo.operationId,
            } as LJExtData);
        },
        [removePluginPolishPopover, clickLog, showLog]
    );

    const changePolish = useCallback(
        async (isPolish: boolean, {pluginId}: AgentPluginInfo, func: AgentPluginFunction) => {
            if (!appId) {
                return;
            }

            clickLog(EVENT_VALUE_CONST.PLUGIN_REPLY_POLISH, {
                [EVENT_EXT_KEY_CONST.PLUGIN_ID]: pluginId,
                [EVENT_EXT_KEY_CONST.E_OPERATION_ID]: func.operationId,
                [EVENT_EXT_KEY_CONST.IS_OPEN]: +isPolish,
            } as LJExtData);
            await bindPluginStyle({appId, pluginId, operationId: func.operationId, isPolish});
            fetchPluginStyleStatus();
            // 手动触发表单有改动，否则智能体会以为没有改动而无法发布
            setHasChangeForm(true);
        },
        [appId, fetchPluginStyleStatus, clickLog, setHasChangeForm]
    );

    // 删除插件某function
    const deletePluginFunction = useCallback(
        (e: React.MouseEvent<HTMLSpanElement>) => {
            if (building) {
                return;
            }

            const {pluginId, operationId} = parseSelectPluginValue(e.currentTarget.dataset.key);

            const pluginIndex = pluginData.findIndex(item => item.pluginId === pluginId);
            if (pluginIndex === -1) {
                return;
            }

            const functionList = [...pluginData[pluginIndex].functionList];
            const delFunctionIndex = functionList.findIndex(item => item.operationId === operationId);

            if (delFunctionIndex === -1) {
                return;
            }

            // 更新agentPluginList
            const newAgentPluginList = cloneDeep(pluginData);
            if (functionList.length > 1) {
                functionList.splice(delFunctionIndex, 1);
                newAgentPluginList[pluginIndex].functionList = functionList;
            } else {
                newAgentPluginList.splice(pluginIndex, 1);
            }

            setAgentPlugins(newAgentPluginList);

            // 更新PluginFunctionIdsList
            const pluginFunctionIdsList: AgentPluginFunctionIds[] = newAgentPluginList.map(item => ({
                pluginId: item.pluginId,
                operationIds: item.functionList.map(apiItem => apiItem.operationId),
            }));

            removePluginStylePopover();
            removePluginPolishPopover();

            onChange && onChange(pluginFunctionIdsList);
        },
        [building, pluginData, setAgentPlugins, onChange, removePluginStylePopover, removePluginPolishPopover]
    );

    // 打开插件详情页
    const openPluginDetail = useCallback((pluginId: PluginId) => {
        window.open(
            urls.pluginDetail.fill({
                id: pluginId,
                type: PLUGIN_TYPE_EN_LOWER_NAME[AIServiceType.Ability],
                tab: PluginTabType.Overview,
            })
        );
    }, []);
    const checkIsAiRecommend = useCallback(
        ({pluginId, operationId}: CheckIsAiRecommend) => {
            if (aiRecommendPlugins.length === 0) {
                return false;
            }

            const isAiRecommend = !!aiRecommendPlugins.find(
                item => item.pluginId === pluginId && item.functionList?.some(e => e.operationId === operationId)
            );

            if (isAiRecommend) {
                setTimeout(() => {
                    setAiRecommendPlugins([]);
                }, 1000);
            }
            return isAiRecommend;
        },
        [aiRecommendPlugins, setAiRecommendPlugins]
    );

    const [lastAgentPluginList, setLastAgentPluginList] = useState<AgentPluginInfo[] | null>(null);
    useEffect(() => {
        if (!lastAgentPluginList) {
            setLastAgentPluginList(showAgentPluginList);
        } else if (showAgentPluginList !== lastAgentPluginList) {
            const lastIds = getAllFunctions(lastAgentPluginList);
            const currentIds = getAllFunctions(showAgentPluginList);
            const newAdd = difference(currentIds, lastIds);
            if (newAdd.length) {
                setHasNewPlugin(true);
            }

            setLastAgentPluginList(showAgentPluginList);
        }
    }, [showAgentPluginList, lastAgentPluginList]);

    const [bindStyleModalData, setBindStyleModalData] = useState<null | BindStyleModalData>(null);

    const [xReturnRaw, setXReturnRaw] = useState<string>('');

    const openBindStyleModal = useCallback(
        ({pluginId}: AgentPluginInfo, functionInfo: AgentPluginFunction) => {
            // eslint-disable-next-line complexity
            async function open() {
                if (!appId) {
                    return;
                }

                const loadingData: BindStyleModalData = {
                    titleSuffix: functionInfo.operationId,
                    response: null,
                    hasCustomStyle: false,
                    hasMarkdownStyle: false,
                    pluginId: pluginId,
                    operationId: functionInfo.operationId,
                    bindInfo: undefined,
                    isPluginStyleUpdate: false,
                    loading: true,
                };

                setBindStyleModalData(loadingData);

                const {uiJson, styleFormat, openApi, isUpdate} = await queryPluginOperationDetail({
                    appId,
                    pluginId,
                    operationId: functionInfo.operationId,
                });

                const openApiYaml: OpenApiYaml = openApi
                    ? (yaml.load(openApi) as OpenApiYaml)
                    : ({paths: {}} as OpenApiYaml);

                const operation = Object.values(openApiYaml.paths)
                    .map(x => Object.values(x))
                    .flat()
                    .find(x => x.operationId === functionInfo.operationId);

                if (!operation) {
                    console.error('plugin function 有误', openApiYaml, functionInfo);
                    return;
                }

                setXReturnRaw(uiJson?.dataSource || operation?.['x-return-raw']?.name || '');

                let data: BindStyleModalData = {
                    titleSuffix: functionInfo.operationId,
                    response: getOperationResponse(openApiYaml, operation),
                    hasCustomStyle: styleFormat === 2,
                    hasMarkdownStyle: styleFormat === 3,
                    pluginId: pluginId,
                    operationId: functionInfo.operationId,
                    bindInfo: undefined,
                    isPluginStyleUpdate: isUpdate,
                    loading: false,
                };

                if (uiJson) {
                    data = {
                        ...data,
                        bindInfo: {
                            styleId: uiJson?.styleId || DefaultStyleId,
                            styleVersion: uiJson?.styleVersion || '',
                            styleJson: uiJson?.styleJson || '',
                            styleJsonObject: {} as any,
                        },
                    };

                    try {
                        if (data.bindInfo) {
                            data.bindInfo.styleJsonObject = JSON.parse(uiJson?.styleJson || '{}');
                        }
                    } catch (e) {
                        console.error('getOfficialPluginStyle JSON解析失败:', uiJson, e);
                    }
                }

                removePluginStylePopover();
                setBindStyleModalData(data);

                clickLog(EVENT_VALUE_CONST.PLUGIN_REPLY_CONFIG, {
                    [EVENT_EXT_KEY_CONST.PLUGIN_ID]: pluginId,
                    [EVENT_EXT_KEY_CONST.E_OPERATION_ID]: operation.operationId,
                } as LJExtData);
            }

            open();
        },
        [appId, clickLog, removePluginStylePopover]
    );

    const onSwitchStyle = useCallback(
        (styleId: number) => {
            if (!bindStyleModalData) {
                return;
            }

            clickLog(EVENT_VALUE_CONST.PLUGIN_REPLY_TEMPLATE, {
                [EVENT_EXT_KEY_CONST.PLUGIN_ID]: bindStyleModalData.pluginId,
                [EVENT_EXT_KEY_CONST.E_OPERATION_ID]: bindStyleModalData.operationId,
                [EVENT_EXT_KEY_CONST.E_STYLE_ID]: styleId,
                [EVENT_EXT_KEY_CONST.C_REPLY_TEMPLATE_TYPE]: 1,
            } as LJExtData);
        },
        [clickLog, bindStyleModalData]
    );

    const onBindStyleModalConfirm = useCallback(
        async (bindInfo: StyleBindInfo) => {
            if (!appId || !bindStyleModalData) {
                return;
            }

            await bindPluginStyle({
                appId: appId,
                pluginId: bindStyleModalData.pluginId,
                operationId: bindStyleModalData.operationId,
                styleId: bindInfo.styleId,
                styleVersion: bindInfo.styleVersion,
                styleJson: bindInfo.styleJson,
                dataSource: xReturnRaw,
            });
            message.success('回复卡片样式已更新');
            // 绑定样式后主动触发一次保存才能在预览容器中生效新的样式
            previewContainerEmitter.emit('refresh');
            setBindStyleModalData(null);
            fetchPluginStyleStatus();
        },
        [appId, bindStyleModalData, xReturnRaw, fetchPluginStyleStatus]
    );

    const onBindStyleModalCancel = useCallback(() => {
        setBindStyleModalData(null);
    }, [setBindStyleModalData]);

    return (
        <ul>
            {!!bindStyleModalData && (
                <BindStyleModal
                    open={!!bindStyleModalData}
                    titleSuffix={bindStyleModalData.titleSuffix}
                    response={bindStyleModalData.response}
                    hasCustomStyle={bindStyleModalData.hasCustomStyle}
                    hasMarkdownStyle={bindStyleModalData.hasMarkdownStyle}
                    bindInfo={bindStyleModalData.bindInfo}
                    xReturnRaw={xReturnRaw}
                    setXReturnRaw={setXReturnRaw}
                    onCancel={onBindStyleModalCancel}
                    onConfirm={onBindStyleModalConfirm}
                    onSwitchStyle={onSwitchStyle}
                    isPluginStyleUpdate={bindStyleModalData.isPluginStyleUpdate}
                    isLoading={bindStyleModalData.loading}
                />
            )}
            {showAgentPluginList.map((pluginInfo: AgentPluginInfo, pluginIndex) => (
                <li key={pluginInfo.pluginId} className="mt-2 first:mt-0">
                    <div className="w-full text-colorTextDefault">
                        <div>
                            {pluginInfo.functionList?.map(
                                // eslint-disable-next-line complexity
                                (functionInfo: AgentPluginFunction, functionIndex) => {
                                    const pluginStyleStatus = pluginStyleStatusMap?.get(
                                        getPluginStyleStatusId(pluginInfo.pluginId, functionInfo.operationId)
                                    );

                                    return (
                                        <div
                                            key={functionInfo.operationId}
                                            className={`flex w-full items-center justify-between gap-[6px] overflow-hidden  border-t-[0.5px] px-3 py-[9px] text-sm first:rounded-t-[9px] first:border-0 last:rounded-b-[9px] ${
                                                checkIsAiRecommend({
                                                    pluginId: pluginInfo.pluginId,
                                                    operationId: functionInfo.operationId,
                                                })
                                                    ? 'bg-[rgba(85,98,242,0.1)]'
                                                    : pluginInfo.isValid
                                                    ? 'bg-colorBgFormList'
                                                    : 'bg-[rgba(255,77,79,0.04)]'
                                            }`}
                                        >
                                            <div className="flex flex-grow items-center gap-[6px] overflow-hidden">
                                                {/* 插件信息 */}
                                                <PluginInfo pluginInfo={pluginInfo} />

                                                <div>/</div>

                                                {/* function 信息 */}
                                                <div
                                                    // function 不存在时 functionName 飘红
                                                    className={classnames('flex-grow overflow-hidden truncate', {
                                                        'text-error': !functionInfo.functionDesc,
                                                        'text-gray-tertiary': readonly || isNoFunctionCallModel,
                                                    })}
                                                >
                                                    <FunctionInfo functionInfo={functionInfo} />
                                                </div>
                                            </div>

                                            {!readonly && !isNoFunctionCallModel && (
                                                <div className="flex items-center gap-[18px]">
                                                    {/* 我的插件显示详情按钮 */}
                                                    {pluginInfo.self && !isIndependent(pluginInfo) && (
                                                        <span
                                                            className="iconfont icon-app cursor-pointer text-sm hover:text-primary active:text-textDisabled"
                                                            onClick={() => openPluginDetail(pluginInfo.pluginId)}
                                                        ></span>
                                                    )}

                                                    {/* 是否叠加大模型润色开关 */}
                                                    <PluginPolishEntry
                                                        openPolishModal={pluginPolishFunctionInfo === functionInfo}
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        onPolishModalOpen={() =>
                                                            pluginInfo?.pluginStatus !== PluginStatus.Offline &&
                                                            openPluginPolishPopover(pluginInfo, functionInfo)
                                                        }
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        onPolishModalClose={() => setPluginPolishFunctionInfo(null)}
                                                        openTip={
                                                            pluginPolishTipShow &&
                                                            pluginIndex === 0 &&
                                                            functionIndex === 0
                                                        }
                                                        onRemoveTip={removePluginPolishPopover}
                                                        isPolish={!!pluginStyleStatus?.isPolish}
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        onChangePolish={isPolish =>
                                                            changePolish(isPolish, pluginInfo, functionInfo)
                                                        }
                                                        disabledTip={getPolishDisabledTip(
                                                            pluginInfo.pluginTag,
                                                            pluginStyleStatus
                                                        )}
                                                        disabledBtn={pluginInfo?.pluginStatus === PluginStatus.Offline}
                                                    />

                                                    {/* 卡片样式配置 */}
                                                    <PluginStyleEntry
                                                        pluginInfo={pluginInfo}
                                                        functionInfo={functionInfo}
                                                        pluginStyleStatus={pluginStyleStatus}
                                                        pluginStyleShow={pluginStyleShow}
                                                        isFirstPlugin={pluginIndex === 0 && functionIndex === 0}
                                                        onRemoveStylePopover={removePluginStylePopover}
                                                        onOpenBindStyle={openBindStyleModal}
                                                    />

                                                    {/* 删除 */}
                                                    <span
                                                        className={`text-sm ${
                                                            building
                                                                ? 'opacity-40'
                                                                : 'cursor-pointer hover:text-primary active:text-textDisabled'
                                                        } iconfont icon-delete`}
                                                        data-key={getSelectPluginValue(
                                                            pluginInfo,
                                                            functionInfo.operationId
                                                        )}
                                                        onClick={deletePluginFunction}
                                                    ></span>
                                                </div>
                                            )}
                                        </div>
                                    );
                                }
                            )}
                        </div>

                        {pluginInfo.pluginStatus !== PluginStatus.Online && (
                            <ToFixPlugin pluginInfo={pluginInfo}>插件已下线，将不会在智能体中生效</ToFixPlugin>
                        )}

                        {!!pluginInfo.functionList?.find(item => !item.functionDesc) && (
                            <ToFixPlugin pluginInfo={pluginInfo}>
                                {'未在插件内找到' +
                                    pluginInfo.functionList
                                        ?.filter(item => !item.functionDesc)
                                        .map(item => item.operationId)
                                        .join('、') +
                                    '，将不会在智能体中生效'}
                            </ToFixPlugin>
                        )}
                    </div>
                </li>
            ))}
        </ul>
    );
}
