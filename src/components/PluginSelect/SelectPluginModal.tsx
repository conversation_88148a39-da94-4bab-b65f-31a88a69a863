/**
 * @file 插件自定义插件选择弹窗
 * <AUTHOR>
 */

import {Modal, message, Button, ConfigProvider, Select} from 'antd';
import {ChangeEvent, useCallback, useEffect, useMemo, useState} from 'react';
import flatten from 'lodash/flatten';
import intersection from 'lodash/intersection';
import isEqual from 'lodash/isEqual';
import {AgentPluginFunction, AgentPluginInfo, AgentPluginFunctionIds, batchPluginFunctions} from '@/api/agentEdit';
import {PluginStatus} from '@/api/pluginCenter/interface';
import urls from '@/links';
import {PluginListDomain} from '@/api/agentEdit/interface';
import ThemeConfig from '@/styles/lingjing-light-theme';
import Search from '@/components/Input/Search';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import guideEmitter from '@/store/agent/GuideEmitter';
import {usePluginDataStore} from '@/store/plugin/usePluginDataStore';
import {
    SelectPluginListSearchParams,
    getCenterPluginFunctions,
    getFavouritePluginFunctions,
    getMyPluginPublishTags,
    MyPluginListType,
    updatePluginFavoriteStatus,
    getPluginStoreDetail,
} from '@/api/plugin/index';
import {CenterPluginOrderType, CenterPluginTab} from '@/api/plugin/index';
import {getUserPluginFunctions} from '@/api/agentEditV2';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {usePagination} from '@/utils/usePagination';
import {FavoriteStatus} from '@/api/center/interface';
import Scrollbar from '../Scrollbar';
import SearchPluginList from './SearchPluginList';
import AbnormalMsg from './AbnormalMsg';
import IconImage from './IconImage';

const getList = async (params: SelectPluginListSearchParams) => {
    const MethodsMap = {
        [MyPluginListType.MY]: getUserPluginFunctions,
        [MyPluginListType.FAVOURITE]: getFavouritePluginFunctions,
        [MyPluginListType.STORE]: getCenterPluginFunctions,
    };

    const res = await MethodsMap[params.listType](params);
    return {total: res.total, list: res.dataList};
};

export default function SelectPluginModal(props: {
    value: AgentPluginFunctionIds[];
    onSelect?: (value: AgentPluginInfo[]) => void;
    onClose: () => void;
    open: boolean;
    /** 最多可选Function个数 */
    maxFunctionsNum: number;
    /** 查询工具列表时的作用域 */
    domain: PluginListDomain;
}) {
    const {clickLog, showLog} = useUbcLogV3();
    const {value = [], onSelect, maxFunctionsNum, domain, onClose, open} = props;

    const [pluginCenterTags, setPluginCenterTags] = useState<CenterPluginTab[]>([]);
    const getStoreTags = useCallback(async () => {
        const res = await getMyPluginPublishTags(false);
        setPluginCenterTags(res);
    }, []);

    const showPluginTags = [
        {id: '', description: '全部'},
        ...pluginCenterTags.map(tags => ({id: tags.id + '', description: tags.description})),
    ];

    useEffect(() => {
        if (!open) return;
        getStoreTags();
    }, [getStoreTags, open]);

    const [keyword, setKeyword] = useState('');
    const onKeywordChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        setKeyword(e.target.value);
    }, []);

    const {
        list: pluginList,
        params,
        setParams,
        loading,
        total,
        setList: setPluginList,
    } = usePagination<SelectPluginListSearchParams, AgentPluginInfo>({
        initParams: {
            pageNo: 1,
            pageSize: 10,
            keyword: '',
            domain,
            orderType: CenterPluginOrderType.HOTEST,
            tagId: '',
            listType: MyPluginListType.STORE,
            filterStream: domain !== PluginListDomain.AGENT,
        },
        getList,
    });

    const onReachBottom = useCallback(() => {
        if (pluginList.length < total && !loading) {
            setParams(prev => ({...prev, pageNo: prev.pageNo + 1}));
        }
    }, [loading, pluginList.length, setParams, total]);

    const handleClickMyPlugin = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CATEGORY, {ePluginCategoryId: '我的插件'});
        setKeyword('');
        setParams(prev => ({...prev, tagId: '', pageNo: 1, keyword, listType: MyPluginListType.MY}));
    }, [clickLog, keyword, setParams]);

    const handleClickFavourite = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CATEGORY, {ePluginCategoryId: '收藏'});
        setKeyword('');
        setParams(prev => ({...prev, tagId: '', pageNo: 1, keyword, listType: MyPluginListType.FAVOURITE}));
    }, [clickLog, keyword, setParams]);

    const handleClickTag = useCallback(
        (tagId: string) => () => {
            clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CATEGORY, {ePluginCategoryId: tagId || ''});
            setParams(prev => ({...prev, tagId, pageNo: 1, keyword, listType: MyPluginListType.STORE}));
        },
        [clickLog, keyword, setParams]
    );

    const handleOrderTypeChange = useCallback(
        (orderType: CenterPluginOrderType) => {
            clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_RANK, {eRankType: orderType});
            setParams(prev => ({
                ...prev,
                pageNo: 1,
                keyword,
                orderType,
                listType: MyPluginListType.STORE,
            }));
        },
        [clickLog, keyword, setParams]
    );

    const {pluginData, setPluginData} = usePluginDataStore(store => ({
        pluginData: store.pluginData,
        setPluginData: store.setPluginData,
    }));

    // 缓存初始选中的插件列表和每次搜索的插件列表
    const [allPluginsInfo, setAllPluginsInfo] = useState<Record<string, AgentPluginInfo>>({});
    useEffect(() => {
        setAllPluginsInfo(prev => {
            pluginData.forEach(plugin => {
                prev[plugin.pluginId] = plugin;
            });
            pluginList.forEach(plugin => {
                prev[plugin.pluginId] = plugin;
            });
            return {
                ...prev,
            };
        });
    }, [pluginData, pluginList]);

    const resetSearch = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_REFRESH);
        setKeyword('');
        setParams(prev => ({
            ...prev,
            pageNo: 1,
            keyword: '',
            orderType: CenterPluginOrderType.HOTEST,
        }));
    }, [clickLog, setParams]);

    const handleSearch = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_SEARCH);
        setParams(prev => ({
            ...prev,
            keyword,
            pageNo: 1,
            listType: MyPluginListType.STORE,
        }));
    }, [clickLog, keyword, setParams]);

    // 当前选中的 插件-工具 列表
    const [selectedFunctions, setSelectedFunctions] = useState<AgentPluginFunctionIds[]>(value);
    // 当前选中的工具id
    const selectedOperationIds = useMemo(
        () => selectedFunctions.reduce((pre: string[], cur) => [...pre, ...cur.operationIds], []),
        [selectedFunctions]
    );

    const [checkLoading, setCheckLoading] = useState(false);
    /** 校验插件最新状态，过滤下线插件和不存在function
     * 并保存到Agent表单插件数据（表单插件展示数据+agentConfig插件数据）
     */
    // eslint-disable-next-line complexity,max-statements
    const checkPlugins = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CONFIRM);
        if (selectedOperationIds.length > maxFunctionsNum) {
            message.error('已超过可添加上限，请重新选择');
            return;
        }

        setCheckLoading(true);
        try {
            // 根据PluginIds请求插件最新公开/私有发布状态、是否上线状态、是否function存在，filter上线&function存在的插件
            const {dataList} = selectedFunctions.length
                ? await batchPluginFunctions({
                      pageNo: 1,
                      pageSize: maxFunctionsNum,
                      pluginIds: selectedFunctions.map(pluginItem => pluginItem.pluginId),
                  })
                : {dataList: [] as AgentPluginInfo[]};

            const checkErrorList: AgentPluginFunctionIds[] = [];
            const filteredPluginList: AgentPluginInfo[] = [];

            selectedFunctions.forEach(pluginInfo => {
                const {pluginId, operationIds} = pluginInfo;
                const newPluginInfo = dataList.find(item => item.pluginId === pluginId);

                if (newPluginInfo?.pluginStatus === PluginStatus.Online) {
                    // check 是否最新插件信息中不存在选择的function
                    const filteredFunctionList: AgentPluginFunction[] = [];

                    operationIds.forEach(operationId => {
                        const existFunction = newPluginInfo.functionList.find(a => a.operationId === operationId);
                        if (existFunction) {
                            filteredFunctionList.push(existFunction);
                        } else {
                            let errorPlugin = checkErrorList.find(item => item.pluginId === pluginId);
                            if (!errorPlugin) {
                                errorPlugin = {
                                    ...pluginInfo,
                                    operationIds: [],
                                };
                                checkErrorList.push(errorPlugin);
                            }

                            errorPlugin.operationIds = [...errorPlugin.operationIds, operationId];
                        }
                    });

                    if (filteredFunctionList.length > 0) {
                        filteredPluginList.push({
                            ...newPluginInfo,
                            functionList: filteredFunctionList,
                        });
                    }
                }
                // 插件未上线
                else {
                    checkErrorList.push({
                        ...pluginInfo,
                        operationIds: [],
                    });
                }
            });

            if (checkErrorList.length > 0) {
                message.success(<AbnormalMsg abnormalList={checkErrorList} allPluginsInfo={allPluginsInfo} />);
            }

            /** 新增工作流、插件表单 ，需要触发 【人设回复引导气泡】展示状态变化  注：[pluginFunctionIdsList 是打开添加插件弹窗用户选中的初始化插件信息] */
            if (filteredPluginList.length > 0) {
                const isAddNewFunction = !!intersection(
                    flatten(filteredPluginList.map(item => item.operationIds)),
                    flatten(value.map(item => item.operationIds))
                ).length;

                const isAddNewPlugin = !!intersection(
                    filteredPluginList.map(item => item.pluginId),
                    value.map(item => item.pluginId)
                ).length;

                (isAddNewPlugin || isAddNewFunction) && guideEmitter.emit('openChangeSystemPrompt');
            }

            const selectedValue = filteredPluginList.map(item => ({
                ...item,
                operationIds: item.functionList?.map(item => item.operationId),
            }));
            if (
                !isEqual(
                    value.map(({pluginId, operationIds}) =>
                        operationIds.map(operationId => `${pluginId}-${operationId}`)
                    ),
                    selectedValue.map(({pluginId, operationIds}) =>
                        operationIds.map(operationId => `${pluginId}-${operationId}`)
                    )
                )
            ) {
                onSelect?.(selectedValue);
            }

            setPluginData(filteredPluginList);

            setCheckLoading(false);
            onClose();
        } catch (err: any) {
            message.error(err?.msg || `插件校验失败，请稍后重试`);
            setCheckLoading(false);
        }
    }, [
        clickLog,
        selectedOperationIds.length,
        maxFunctionsNum,
        selectedFunctions,
        value,
        setPluginData,
        onClose,
        allPluginsInfo,
        onSelect,
    ]);

    const addAbilityPlugin = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CREATE);
        window.open(urls.pluginAbilityCreate.raw());
    }, [clickLog]);

    const onFavourite = useCallback(
        async (plugin: AgentPluginInfo) => {
            const favorite = !plugin.isFavorited;
            try {
                // 临时变更收藏状态
                setPluginList(prev => {
                    const target = prev.find(item => item.pluginId === plugin.pluginId);
                    if (target) {
                        target.isFavorited = favorite;
                    }
                    return [...prev];
                });
                await updatePluginFavoriteStatus(
                    plugin.pluginId,
                    favorite ? FavoriteStatus.Favorited : FavoriteStatus.UnFavorited
                );
                message.success(favorite ? '收藏成功' : '取消收藏');
            } catch (error) {
                console.error(error);
            } finally {
                // 更新实际状态
                const detail = await getPluginStoreDetail(plugin.pluginId);
                setPluginList(prev => {
                    const target = prev.find(item => item.pluginId === plugin.pluginId);
                    if (target) {
                        target.isFavorited = detail.isFavorited;
                        target.favorNum = detail.favorNum;
                    }
                    return [...prev];
                });
            }
        },
        [setPluginList]
    );

    useEffect(() => {
        showLog(EVENT_VALUE_CONST.ADD_TOOL_BOX);
    }, [showLog]);

    const handleClickCancel = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_BOX_CANCEL);
        onClose();
    }, [clickLog, onClose]);

    return (
        <Modal
            centered
            open={open}
            title={
                <div className="flex items-center">
                    <span className="text-lg font-medium leading-none">添加插件</span>
                    <div className="flex-grow"></div>
                    <span
                        className="iconfont icon-close cursor-pointer rounded-[6px] p-1 text-base font-semibold leading-none hover:bg-colorBgFormList hover:text-primary"
                        onClick={handleClickCancel}
                    ></span>
                </div>
            }
            closable={false}
            width={800}
            footer={
                <ConfigProvider
                    theme={{
                        components: {
                            Button: {
                                controlHeight: 30,
                                contentLineHeight: 1,
                            },
                        },
                    }}
                >
                    <div className="flex justify-between">
                        <Button type="link" className="mr-4 px-0 font-normal" disabled={loading} onClick={resetSearch}>
                            <span className="flex items-center">
                                <i className="iconfont icon-update mr-[3px] text-sm" />
                                刷新
                            </span>
                        </Button>
                        <div className="flex-grow"></div>
                        <Button
                            className="border-[1px] border-solid border-[#EDEEF0] hover:text-gray-tertiary"
                            onClick={handleClickCancel}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={checkPlugins}
                            style={{marginLeft: 10}}
                            loading={checkLoading}
                            disabled={
                                (value.length === 0 && selectedOperationIds?.length === 0) ||
                                selectedOperationIds.length > maxFunctionsNum
                            }
                        >
                            确认({selectedOperationIds.length}/{maxFunctionsNum})
                        </Button>
                    </div>
                </ConfigProvider>
            }
            maskClosable={false}
            transitionName=""
            styles={{
                content: {
                    padding: 24,
                },
                header: {
                    lineHeight: 1,
                    marginBottom: 12,
                },
                body: {
                    height: 452,
                    display: 'flex',
                },
                footer: {
                    marginTop: 18,
                },
            }}
        >
            <div className="flex w-[120px] flex-shrink-0 flex-col font-medium leading-none">
                <ConfigProvider
                    theme={{
                        components: {
                            Button: {
                                controlHeight: 40,
                            },
                        },
                    }}
                >
                    <Button type="primary" className="flex-shrink-0 rounded-[9px] px-5" onClick={addAbilityPlugin}>
                        <span className="iconfont icon-plus font-normal leading-none" />
                        <span className="font-normal leading-none">新建插件</span>
                    </Button>
                </ConfigProvider>
                <IconImage
                    description="我的插件"
                    className="mt-3"
                    active={params.listType === MyPluginListType.MY}
                    onClick={handleClickMyPlugin}
                />
                <IconImage
                    description="收藏"
                    active={params.listType === MyPluginListType.FAVOURITE}
                    onClick={handleClickFavourite}
                />
                <hr className="mt-3" />
                <div className="mt-3 text-xs font-medium text-gray-tertiary">插件商店</div>
                <Scrollbar className="mt-2 min-h-0 flex-grow overflow-y-auto">
                    {showPluginTags.map(tag => (
                        <IconImage
                            key={tag.id}
                            onClick={handleClickTag(tag.id)}
                            description={tag.description}
                            active={params.tagId === tag.id && params.listType === MyPluginListType.STORE}
                        />
                    ))}
                </Scrollbar>
            </div>
            <div className="ml-4 flex flex-grow flex-col">
                {params.listType === MyPluginListType.STORE && (
                    <div className="mb-3 flex h-10 w-full flex-shrink-0 items-center justify-end">
                        <ConfigProvider
                            theme={{
                                token: {
                                    lineWidth: 0,
                                    colorBgContainer: '#F5F6FA',
                                },
                                components: {
                                    Select: {
                                        controlHeight: 36,
                                        optionSelectedColor: ThemeConfig.token.colorPrimary,
                                        optionSelectedBg: 'transparent',
                                        optionSelectedFontWeight: 400,
                                        controlItemBgHover: '#F5F6FA',
                                    },
                                },
                            }}
                        >
                            <Select
                                className="w-[121px]"
                                disabled={loading}
                                value={params.orderType}
                                options={[
                                    {value: 2, label: '最受欢迎'},
                                    {value: 1, label: '最新发布'},
                                ]}
                                onChange={handleOrderTypeChange}
                            />

                            <Search
                                className="ml-2 w-[121px]"
                                placeholder="插件名称"
                                value={keyword}
                                onChange={onKeywordChange}
                                disabled={loading}
                                onSearch={handleSearch}
                            />
                        </ConfigProvider>
                    </div>
                )}
                <SearchPluginList
                    listType={params.listType}
                    loading={loading}
                    pluginList={pluginList}
                    maxFunctionsNum={maxFunctionsNum}
                    selectedFunctions={selectedFunctions}
                    setSelectedFunctions={setSelectedFunctions}
                    onReachBottom={onReachBottom}
                    onFavourite={onFavourite}
                />
            </div>
        </Modal>
    );
}
