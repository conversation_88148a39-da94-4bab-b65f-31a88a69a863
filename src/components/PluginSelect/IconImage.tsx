import classNames from 'classnames';

const CommonTabsClassName = 'mt-1 flex items-center cursor-pointer rounded-[9px] p-3 hover:bg-colorBgFormList';
const staticImagePrefix = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/plugin/';
export default function IconImage(props: {
    active: boolean;
    description: string;
    onClick: () => void;
    className?: string;
}) {
    return (
        <div className={classNames(CommonTabsClassName, props.className)} onClick={props.onClick}>
            <img
                style={{display: props.active ? 'inline-block' : 'none', width: '16px', height: '16px'}}
                src={staticImagePrefix + props.description + '_p.svg'}
            />
            <img
                style={{display: props.active ? 'none' : 'inline-block', width: '16px', height: '16px'}}
                src={staticImagePrefix + props.description + '.svg'}
            />
            <span className="ml-2 truncate">{props.description}</span>
        </div>
    );
}
