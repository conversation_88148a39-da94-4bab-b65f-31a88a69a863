/**
 * @file 插件自定义插件选择弹窗错误信息
 * <AUTHOR>
 */

import {AgentPluginFunctionIds, AgentPluginInfo} from '@/api/agentEdit';

export default function AbnormalMsg({
    abnormalList,
    allPluginsInfo,
}: {
    abnormalList: AgentPluginFunctionIds[];
    allPluginsInfo: Record<string, AgentPluginInfo>;
}) {
    return (
        <>
            {abnormalList.map(({pluginId, operationIds}) => {
                const pluginName = allPluginsInfo[pluginId].pluginName;

                if (operationIds.length > 0) {
                    return (
                        <span key={pluginId}>
                            <span className="font-medium">{pluginName}</span>中
                            <span className="font-medium">{operationIds.join('、')}</span>
                            不存在，
                        </span>
                    );
                }

                return (
                    <span key={pluginId}>
                        <span className="font-medium">{pluginName}</span>已下线，
                    </span>
                );
            })}
            <span>已为您自动从添加列表中移除，请检查插件状态后重新尝试添加</span>
        </>
    );
}
