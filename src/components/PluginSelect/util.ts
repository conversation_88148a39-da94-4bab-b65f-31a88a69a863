/**
 * @file 插件自定义工具模块 utils.tsx
 * <AUTHOR>
 */

import {AgentPluginInfo, PluginTag} from '@/api/agentEdit';
import {PluginPublishType, PluginStatus} from '@/api/pluginCenter/interface';

/** 发布范围默认全部时值=-1 */
export const PluginPublishTypeNone = -1;

export type PluginScopeType = PluginPublishType | typeof PluginPublishTypeNone;

export enum PluginCategory {
    /** 精选插件 */
    Official = '0',
    /** 我的插件 */
    UserPlugin = '1',
}

/**
 * 最大可添加插件数量
 */
export const MaxAddPluginCount = 8;

/**
 * 最大可添加工作流数量
 */
export const MaxAddWorkflowCount = 5;

/** 分页个数 */
export const PAGE_SIZE = 20;

/**
 * pluginId和operationId链接符号
 * pluginId生成规则只有字母数字，operationId只要求字符串可能包含特殊字符
 */
export const CONNECT_CHARACTER = '###';

/**
 * 获取插件选择的值（Radio 和 Checkbox 使用的 value 值）
 *
 * @param pluginInfo 插件数据
 * @param operationId
 */
export function getSelectPluginValue(pluginInfo: {pluginId: string; workflowId?: string}, operationId: string): string {
    return `${pluginInfo.pluginId}${CONNECT_CHARACTER}${operationId}${CONNECT_CHARACTER}${pluginInfo.workflowId || ''}`;
}

/**
 * 解析插件选择的值（将 Radio 和 Checkbox 使用的 value 值解析回 pluginId、operationId 和 workflowId）
 * @param value
 */
export function parseSelectPluginValue(value?: string): {pluginId: string; operationId: string; workflowId?: string} {
    const [pluginId, operationId, workflowId] = (value || '').split(CONNECT_CHARACTER);

    return {pluginId, operationId: operationId || '', workflowId};
}

/**
 * check 插件是否正常
 * 插件已下线不正常
 * 插件中function不存在不正常（function描述是必须的，描述不存在则插件不存在）
 */
export function checkPluginNormal(pluginInfo: AgentPluginInfo) {
    return (
        pluginInfo.pluginStatus === PluginStatus.Online && !pluginInfo.functionList?.find(item => !item.functionDesc)
    );
}

/**
 * check 插件列表是否都正常
 */
export function checkAllPluginsNormal(pluginList: AgentPluginInfo[]): boolean {
    return !pluginList.find(pluginInfo => !checkPluginNormal(pluginInfo));
}

// 判断是否是独立授权的插件
export function isIndependent(pluginInfo: AgentPluginInfo): boolean {
    return pluginInfo.pluginTag === PluginTag.Independent;
}
