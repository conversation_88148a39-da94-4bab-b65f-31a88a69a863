/**
 * @file 代运营模式 - 底部悬浮条
 * <AUTHOR>
 */

import {useCallback, useEffect, useState} from 'react';
import {Avatar, Tooltip} from 'antd';
import Draggable from 'react-draggable';
import {css} from '@emotion/css';
import {gsap} from 'gsap';
import {getProxyUserInfo} from '@/api/proxyUserInfo';
import {GetAgentUserInfoResponse} from '@/api/proxyUserInfo/interface';
import {DEFAULT_PORTRAIT} from '@/modules/home/<USER>/utils';
import {isProd} from '@/modules/agentPromptEditV2/components/PreviewContainer/constant';
import {getUserInfoId} from '@/utils/tp/tpProxyMode';
import {DEFAULT_DEV_ENV_PREFIX, TP_DEV_ENV_PREFIX} from '@/utils/tp/constant';
import FlashPoint from '@/modules/center/components/pc/FlashPoint';
import {TimeLineContext} from '@/modules/center/index-pc';
import CustomPopover from '@/components/Popover';

const BAR_WIDTH = 365;

/** 闪点气泡 */
const customPopoverStyle = css`
    z-index: 999 !important;

    .ant-popover-arrow {
        --antd-arrow-background-color: #3378ff !important;
    }

    .ant-popover-inner {
        padding: 0 !important;
    }
`;

const globalTimeLine = gsap.timeline();

/**
 * 退出代运营模式后的跳转URL
 */
const relocationUrl = (): string => {
    if (isProd()) {
        // 生产环境
        return `${location.origin}/tp`;
    } else {
        /**
         * 开发环境能跳转的前提，不满足则无法跳转成功。
         * 服务商平台的开发环境命名符合 https://agents-tp-${name}.now.baidu.com
         * 平台开发环境命名符合  https://lingjing-${name}.now.baidu.com
         * 二者的 name 需完全一致，比如 https://agents-tp-qatest.now.baidu.com 和 lingjing-qatest.now.baidu.com
         */
        const res = location.hostname.split(DEFAULT_DEV_ENV_PREFIX);
        if (res.length <= 1) {
            console.error('开发环境地址命名不符合 lingjing-${name} 规则');
            return '';
        }
        return res.length > 1 ? `${location.protocol}//${TP_DEV_ENV_PREFIX}${res[1]}` : '';
    }
};

export default function ProxyModeFloatBar() {
    const [proxyUserInfo, setProxyUserInfo] = useState<GetAgentUserInfoResponse | null>(null);
    const [showFlashPoint, setShowFlashPoint] = useState(true);

    useEffect(() => {
        const proxyModeFlashPoint = window.localStorage.getItem('proxyModeFlashPoinst') || '0';

        setShowFlashPoint(!parseInt(proxyModeFlashPoint, 10));
        (async () => {
            const res = await getProxyUserInfo({
                userInfoId: getUserInfoId(),
            });
            setProxyUserInfo(res);
        })();
    }, []);

    const quitProxyMode = useCallback(() => {
        document.cookie = `tpId=;path=/;`;
        document.cookie = `userInfoId=;path=/;`;
        setProxyUserInfo(null);
        window.location.href = relocationUrl();
    }, []);

    const handleDrag = useCallback(() => {
        window.localStorage.setItem('proxyModeFlashPoinst', '1');
        setShowFlashPoint(false);
    }, []);

    // Draggable 组件文档：https://github.com/react-grid-layout/react-draggable
    return (
        proxyUserInfo && (
            // -1000px 是为了避免默认节点遮盖内容。于是挪出屏幕外，再通过 positionOffset 属性补回来计算结果
            <div className="fixed bottom-0 left-[-1000px] z-[9999]">
                <Draggable
                    axis="x"
                    handle=".handle"
                    bounds={{left: 0, right: document.body.clientWidth - BAR_WIDTH}}
                    positionOffset={{x: 1000, y: 0}}
                    defaultPosition={{x: (document.body.clientWidth - BAR_WIDTH) / 2, y: 0}}
                    onDrag={handleDrag}
                >
                    <div className="flex h-12 w-[365px] items-center justify-between rounded-t-xl bg-[rgba(0,0,0,0.7)] p-[9px]">
                        <div className="flex items-center">
                            <div className="flex items-center">
                                <TimeLineContext.Provider value={{timeLine: globalTimeLine}}>
                                    <CustomPopover
                                        placement="top"
                                        align={{
                                            offset: [0, -8],
                                        }}
                                        overlayClassName={customPopoverStyle}
                                        content="按住左键拖动，可调整悬浮栏位置"
                                        type="primary"
                                    >
                                        {showFlashPoint && (
                                            <div className="absolute -top-[10px] cursor-pointer">
                                                <FlashPoint />
                                            </div>
                                        )}
                                    </CustomPopover>
                                </TimeLineContext.Provider>
                                {/* 拖拽按钮 */}
                                <div className="handle mr-2 cursor-pointer">
                                    <span className="iconfont icon-drag ml-1 text-[14px] text-gray-quaternary"></span>
                                </div>
                                <div className="mr-3 h-6 rounded-[6px] bg-gray-border-secondary px-2 text-sm font-medium leading-6 text-[#787B94]">
                                    代运营
                                </div>
                                <Tooltip
                                    title={proxyUserInfo?.customerName}
                                    placement="top"
                                    align={{
                                        offset: [0, -24],
                                    }}
                                >
                                    <div className="flex w-[188px] items-center">
                                        <div className="mr-2 w-6">
                                            <Avatar
                                                src={proxyUserInfo?.portrait || DEFAULT_PORTRAIT}
                                                className="mr-2 h-6 w-6 border-[1px] border-solid border-[#FFFFFF99]"
                                            />
                                        </div>
                                        <span className="cursor-default overflow-hidden overflow-ellipsis whitespace-nowrap text-sm font-medium text-white">
                                            {proxyUserInfo?.displayName || ''}
                                        </span>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                        <button
                            className="h-[30px] w-[52px] rounded-lg bg-primary text-sm font-medium text-white"
                            onClick={quitProxyMode}
                        >
                            退出
                        </button>
                    </div>
                </Draggable>
            </div>
        )
    );
}
