/**
 * 此组件用于页面中单独处理的异常兜底展示，自动处理接口和通用异常
 * 在局部组件内使用时指定size=small，组件高度会进行自适应
 */
import {ComponentProps, ReactNode, useMemo} from 'react';
import {Typography} from 'antd';
import omit from 'lodash/omit';
import classNames from 'classnames';
import {ERROR_REQUEST} from '@/api/error';
import LoadError from '../Loading/LoadError';
import {isMobileDevice} from '../Login/utils/isMobileDevice';
interface RenderErrorExtraProps {
    // 不使用LoadError的异常兜底样式
    defaultTips?: string;
    // 透传进入的error
    error?: any;
}

export const RenderError = (props: ComponentProps<typeof LoadError> & RenderErrorExtraProps) => {
    const {defaultTips, error, extraClassName} = props;
    const content = useMemo(() => {
        let tips = defaultTips;
        let extra: ReactNode = null;

        const custClassName = classNames(
            "text-center text-gray-quaternary break-all font-['PingFang SC']",
            isMobileDevice() ? 'text-sm max-w-[260px]' : 'text-base max-w-[480px]',
            extraClassName || ''
        );

        // 接口异常类
        if (typeof error === 'object' && error?.errno) {
            tips = ERROR_REQUEST[error.errno]?.msg || error.msg;
            extra = error.logId ? (
                <div className={classNames('font-normal', custClassName)}>
                    请求ID：
                    <Typography.Text
                        className={classNames('font-normal', custClassName)}
                        copyable={{
                            icon: (
                                <span
                                    className={classNames(
                                        'iconfont icon-copy cursor-pointer pl-2',
                                        isMobileDevice() ? 'text-sm' : 'text-base'
                                    )}
                                />
                            ),
                        }}
                    >
                        {error.logId}
                    </Typography.Text>
                </div>
            ) : null;
        } else {
            // 其他异常默认直接字符串化
            const msg = error instanceof Error ? error?.message : JSON.stringify(error);
            extra = msg ? (
                <Typography.Paragraph
                    ellipsis={{
                        rows: 3,
                        expandable: true,
                    }}
                    className={classNames('mb-0 font-normal', custClassName)}
                >
                    失败详情：
                    {msg}({window.location.pathname}
                    {window.location.search})
                </Typography.Paragraph>
            ) : null;
        }
        return {
            tips,
            extra,
        };
    }, [error, defaultTips, extraClassName]);

    const loadProps = props ? omit(props, ['defaultTips', 'error']) : {};

    return <LoadError tips={content.tips} extra={content.extra} {...loadProps} />;
};
