import React, {useCallback} from 'react';
import omit from 'lodash/omit';
import {Boundary} from 'react-suspense-boundary';
import {SuspenseBoundaryProps} from 'react-suspense-boundary/dist/index.d';
import {reportError} from '@/utils/monitor/useCommonError';
import {defaultRenderError, DefaultRenderOptions} from './util';

type ErrorInfo = any;

export default function CommonErrorBoundary(props: SuspenseBoundaryProps & {errorOption?: DefaultRenderOptions}) {
    const onErrorCaught = useCallback((error: any, info: ErrorInfo) => {
        console.error('CommonErrorBoundary', error, info);
        const errorString = error.toString();
        console.info(errorString);
        // 上报错误
        reportError(error);
    }, []);

    const bProps = omit(props, ['defaultTips', 'header']);

    return (
        <Boundary
            {...bProps}
            onErrorCaught={onErrorCaught}
            renderError={props.renderError || defaultRenderError(props.errorOption)}
        >
            {props.children}
        </Boundary>
    );
}
