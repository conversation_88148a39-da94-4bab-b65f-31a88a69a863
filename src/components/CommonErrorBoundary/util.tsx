import {ComponentProps, ReactNode} from 'react';
import omit from 'lodash/omit';
import LoadError from '../Loading/LoadError';
import Header from '../Header';
import {RenderError} from './renderError';

export type DefaultRenderOptions = ComponentProps<typeof LoadError> & {
    defaultTips?: string;
    errorType?: 'header' | 'smallTitle';
    title?: ReactNode;
};

/**
 * 此方法用于CommonErrorBoundary组件的renderError属性的通用函数处理
 * @param options
 * @returns
 */
export const defaultRenderError = (options?: DefaultRenderOptions) => {
    return (error: any) => {
        const rProps = omit(options, ['errorType', 'title']);
        const renderErr = <RenderError {...rProps} error={error} />;
        if (options?.errorType) {
            switch (options?.errorType) {
                case 'header':
                    return (
                        <div>
                            {options?.title || <Header />}
                            {renderErr}
                        </div>
                    );
                case 'smallTitle':
                    return (
                        <div>
                            {!!options?.title && (
                                <div className="pt-[0.75rem] text-base font-medium leading-[22px] text-colorTextDefault">
                                    {options?.title}
                                </div>
                            )}

                            {renderErr}
                        </div>
                    );
            }
        }
        return renderErr;
    };
};
