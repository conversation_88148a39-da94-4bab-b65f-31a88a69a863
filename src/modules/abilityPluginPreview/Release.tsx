/**
 * @file 能力插件预览线上版
 *
 */

import {CacheProvider} from 'react-suspense-boundary';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import {PluginPreview} from '@/components/PluginPreview';

const CHAT_VIEW_CONFIG = {
    nav: {
        type: 'slide',
        // 隐藏分享按钮
        share: false,
    },
    watermark: true,
    history: false,
};

export default function AbilityPluginReleasePreview() {
    return (
        <CacheProvider>
            <CommonErrorBoundary pendingFallback={<Loading />} errorOption={{errorType: 'header'}}>
                <PluginPreview chatViewConfig={CHAT_VIEW_CONFIG} />
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
