/**
 * @file 能力插件预览分享版
 *
 */
import {CacheProvider} from 'react-suspense-boundary';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import LoadError from '@/components/Loading/LoadError';
import {PluginPreview} from '@/components/PluginPreview';
import {RenderError as CommonRenderError} from '@/components/CommonErrorBoundary/renderError';
import Header from '@/components/Header';

const CHAT_VIEW_CONFIG = {
    nav: {
        type: 'slide',
        // 隐藏分享按钮
        share: false,
    },
    watermark: true,
    history: false,
};

enum ErrorCode {
    // 分享链接已超时
    ShareTimeout = 5005,
}

const renderError = (error: any) => {
    if (error?.errno === ErrorCode.ShareTimeout) {
        return <LoadError tips="分享链接已超时，您可以联系开发者重新分享" btnText="返回首页" btnAction="center" />;
    }

    return (
        <div>
            <Header />
            <CommonRenderError error={error} />
        </div>
    );
};

export default function AbilityPluginSharePreview() {
    return (
        <CacheProvider>
            <CommonErrorBoundary pendingFallback={<Loading />} renderError={renderError}>
                <PluginPreview pluginMode="debug" chatViewConfig={CHAT_VIEW_CONFIG} />
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
