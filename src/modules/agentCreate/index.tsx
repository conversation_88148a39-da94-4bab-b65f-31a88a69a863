import {Button, message} from 'antd';
import classNames from 'classnames';
import {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {AppBuildType} from '@/api/appList/interface';
import urls from '@/links';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import FlowAgentCreateModal from './components/FlowAgentCreateModal';
import zeroCodePng from './assets/zeroCode.png';
import lowCodePng from './assets/lowCode.png';
import {AGENT_TYPE_EN_NAME, AGENT_TYPE_NAME, AgentType, AgentTypeItem, agentTypeKeys} from './interface';

const agentTypeMaps: Record<agentTypeKeys, AgentTypeItem> = {
    zeroCode: {
        key: AgentType.ZeroCode,
        name: AGENT_TYPE_NAME[AgentType.ZeroCode],
        enName: AGENT_TYPE_EN_NAME[AgentType.ZeroCode],
        desc: '通过沉浸式对话的方式，表达意图，提供行为说明，引入知识库、官方和自定义插件等能力，创建智能体',
        sampleImg: zeroCodePng,
        enabled: true,
    },
    lowCode: {
        key: AgentType.LowCode,
        name: AGENT_TYPE_NAME[AgentType.LowCode],
        enName: AGENT_TYPE_EN_NAME[AgentType.LowCode],
        desc: '通过拖拽方式快速搭建业务流，结合大模型，知识库，插件等组件，完成智能体开发',
        sampleImg: lowCodePng,
        enabled: true,
    },
    // fullCode: {
    //     key: AgentType.FullCode,
    //     name: AGENT_TYPE_NAME[AgentType.FullCode],
    //     enName: AGENT_TYPE_EN_NAME[AgentType.FullCode],
    //     desc: '基于 SDK 或 API 的灵活开发模式，支持多范式开发，多能力集成，提供云端一体解决方案',
    //     sampleImg: fullCodePng,
    //     enabled: false,
    // },
};

// const AgentTypeList = [agentTypeMaps.zeroCode, agentTypeMaps.lowCode, agentTypeMaps.fullCode];
const AgentTypeList = [agentTypeMaps.zeroCode, agentTypeMaps.lowCode];

export default function SelectAgentType() {
    const navigate = useNavigate();
    // 打开应用弹窗
    const [appCreateModalOpen, setAppCreateModalOpen] = useState(false);
    const {ubcClickLog} = useUbcLog();
    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin();

    const changeAgentType = ({key: AgentKeyType, enabled}: AgentTypeItem) => {
        if (!enabled) {
            return;
        }

        if (!isLogin) {
            uniformLogin();
        } else if (AgentKeyType === AgentType.ZeroCode) {
            ubcClickLog(EVENT_TRACKING_CONST.AgentCreatePromptBtn);
            // 跳转零代码创建页面
            navigate(urls.agentPromptQuickStart.raw());
        } else if (AgentKeyType === AgentType.LowCode) {
            ubcClickLog(EVENT_TRACKING_CONST.AgentCreateFlowBtn);
            // 跳转低代码创建页面（应用）
            setAppCreateModalOpen(true);
        } else if (AgentKeyType === AgentType.FullCode) {
            // 跳转全代码创建页面
            message.info('系统升级中，待开放');
        }
    };

    return (
        <div className="relative">
            <div className="mb-10">
                <div className="-mt-1 w-[8.5rem] text-2xl font-semibold">创建智能体</div>
                <div className="mt-1.5 h-5 w-[37.6rem] text-sm font-normal">
                    智能体是基于LLM有能力主动思考和行动的智能体，你可以选择以下适合的方式进行智能体创建
                </div>
            </div>

            <ul className="flex gap-6">
                {AgentTypeList.map(item => (
                    <li
                        className={classNames(
                            'flex w-1/2 items-start rounded-[1.125rem] bg-white py-[72px] pl-[26px] pr-[30px] text-[#1e1f24]',
                            {
                                'transition-all hover:-translate-y-[0.3125rem] hover:cursor-pointer hover:shadow-card-selected':
                                    item.enabled,
                                'hover:cursor-not-allowed': !item.enabled,
                            }
                        )}
                        key={item.key}
                        onClick={() => changeAgentType(item)}
                    >
                        <div className="mr-[26px] aspect-square w-[37%]">
                            <img src={item.sampleImg} />
                        </div>
                        <div className="flex h-full flex-1 flex-col justify-between">
                            <div>
                                <div className={classNames('text-4xl font-medium')}>{item.name}</div>
                                <div className={classNames('mt-3 text-base font-medium leading-6')}>{item.enName}</div>
                                <div
                                    className={classNames('mb-[2.8125rem] mt-6 text-sm font-normal leading-[1.295rem]')}
                                >
                                    {item.desc}
                                </div>
                            </div>
                            <Button
                                type="primary"
                                size="large"
                                className={classNames('h-[2.625rem] w-full font-medium', {
                                    'bg-[rgba(183,185,193,0.2)] text-[#848691]': !item.enabled,
                                })}
                                disabled={!item.enabled}
                            >
                                {item.enabled ? '立即创建' : '敬请期待'}
                            </Button>
                        </div>
                    </li>
                ))}
            </ul>
            <FlowAgentCreateModal
                open={appCreateModalOpen}
                setModalOpen={setAppCreateModalOpen}
                buildType={AppBuildType.LowCode}
            />
        </div>
    );
}
