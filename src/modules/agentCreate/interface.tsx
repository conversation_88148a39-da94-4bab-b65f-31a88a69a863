/**
 * Agent类型信息
 */
export interface AgentTypeItem {
    /**
     * 类型枚举key
     */
    key: AgentType;
    /**
     * 类型名称
     */
    name: string;
    /**
     * 类型英文名称
     */
    enName: string;
    /**
     * 类型介绍
     */
    desc: string;
    /**
     * 示例图片
     */
    sampleImg: string;
    /**
     * 是否可用
     */
    enabled: boolean;
}

// export type agentTypeKeys = 'zeroCode' | 'lowCode' | 'fullCode';
export type agentTypeKeys = 'zeroCode' | 'lowCode';

/**
 * Agent类型添加
 */
export enum AgentType {
    /**
     * 零代码
     */
    ZeroCode = 0,
    /**
     * 低代码
     */
    LowCode = 1,
    /**
     * 全代码
     */
    FullCode = 2,
}

export const AGENT_TYPE_NAME = {
    [AgentType.ZeroCode]: '零代码',
    [AgentType.LowCode]: '低代码',
    [AgentType.FullCode]: '全代码',
} as const;

export const AGENT_TYPE_EN_NAME = {
    [AgentType.ZeroCode]: 'Prompt Arrangement',
    [AgentType.LowCode]: 'Visual Orchestration',
    [AgentType.FullCode]: 'Assistant API',
} as const;
