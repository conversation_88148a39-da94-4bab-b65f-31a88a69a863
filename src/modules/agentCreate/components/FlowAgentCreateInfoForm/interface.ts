import {Key} from 'react';
import {getCharacterLen} from '@/utils/text';
import api from '@/api/appList';
import {AppInfo, CheckAppInfoStatus} from '@/api/appList/interface';

export interface AppInfoFormRef {
    submit: () => void;
}

/**
 * 获取appId
 * TODO：Ugly Code，先解bug，排期优化
 */
export const getAppId = (): string => {
    const caches = {
        appId: '',
    };
    const pathname = window.location.pathname;
    if (!pathname.includes('/overview')) {
        return '';
    }

    caches.appId = caches.appId || pathname.split('/')[3] || '';

    return caches.appId;
};

export type FieldNames = 'appName' | 'appAvatar' | 'appDesc' | 'frameworkType';

/**
 * 表单数据类型
 */
export type FieldData = Required<Pick<AppInfo, FieldNames>>;

/**
 * 表单项配置数据类型
 */
interface FieldItemConfig {
    /**
     * Form.Item props 属性
     */
    formItemProps: {
        /**
         * React.key
         */
        key: Key;
        /**
         * 字段名
         */
        name: string;
        /**
         * 字段标签的文本
         */
        label: string;
        /**
         * 是否必填
         */
        required: boolean;
    };
    /**
     * Input框可输入最长字符串字节长度（一汉字=2字节）
     */
    maxCharLen?: number;
    /**
     * Input框placeholder
     */
    placeholder?: string;
    /**
     * 为空校验错误文案
     */
    requiredErrorMsg?: string;
    /**
     * 自定义校验方法
     * @param value 字段值
     * @returns void
     */
    validate?: (value: string) => Promise<unknown>;
}

/**
 * 表单配置数据类型
 */
export const appFormConfig: Record<FieldNames, FieldItemConfig> = {
    appName: {
        formItemProps: {
            key: '1',
            name: 'appName',
            label: '应用名称',
            required: true,
        },
        maxCharLen: 16,
        placeholder: '请输入应用名称，16个字符以内',
        requiredErrorMsg: '应用名称不能为空，请输入',
        // 有值时校验（字符数限制&异步机审）
        validate: async (value: string) => {
            // 为空校验放到submit时校验
            if (!value) {
                return Promise.resolve();
            }

            if (getCharacterLen(value) > 16) {
                return Promise.reject(new Error('字符数超过限制'));
            }

            try {
                const appId = getAppId();
                const params = appId ? {appName: value, appId} : {appName: value};
                const {nameRes, nameMessage} = (await api.checkAppInfo(params)) || {};

                if (nameRes === CheckAppInfoStatus.CheckFail) {
                    return Promise.reject(new Error(nameMessage));
                }

                return Promise.resolve();
            } catch (error: any) {
                return Promise.reject(new Error(error.msg || '机审接口调用错误'));
            }
        },
    },
    appAvatar: {
        formItemProps: {
            key: '2',
            name: 'appAvatar',
            label: '应用头像',
            required: true,
        },
        requiredErrorMsg: '应用头像不能为空，请上传',
    },
    appDesc: {
        formItemProps: {
            key: '3',
            name: 'appDesc',
            label: '应用简介',
            required: true,
        },
        placeholder: '请输入应用简介，80字符以内',
        maxCharLen: 80,
        requiredErrorMsg: '应用简介不能为空，请输入',
        // 有值时校验（字符数限制&异步机审）
        validate: async (value: string) => {
            if (!value) {
                // return Promise.reject(new Error('应用简介不能为空，请输入'));
                return Promise.resolve();
            }

            if (getCharacterLen(value) > 80) {
                return Promise.reject(new Error('字符数超过限制'));
            }

            try {
                const appId = getAppId();
                const params = appId ? {appDesc: value, appId} : {appDesc: value};
                const {descRes, descMessage} = (await api.checkAppInfo(params)) || {};

                if (descRes === CheckAppInfoStatus.CheckFail) {
                    return Promise.reject(new Error(descMessage));
                }

                return Promise.resolve();
            } catch (error: any) {
                return Promise.reject(new Error(error.msg || '机审接口调用错误'));
            }
        },
    },
    frameworkType: {
        formItemProps: {
            key: '4',
            name: 'frameworkType',
            label: '',
            required: false,
        },
        // requiredErrorMsg: '类型不能为空，请选择',
    },
} as const;

/** 低代码Agent名称限制字数12 */
const appNameMaxLen = 12;
/** 低代码Agent描述限制字数40 */
const appDescMaxLen = 40;

export const agentFormConfig: Record<FieldNames, FieldItemConfig> = {
    appName: {
        formItemProps: {
            key: '1',
            name: 'appName',
            label: '名称',
            required: true,
        },
        maxCharLen: appNameMaxLen,
        placeholder: '请输入名称',
        requiredErrorMsg: '名称不能为空，请输入',
        // 有值时校验（字符数限制&异步机审）
        validate: async (value: string) => {
            // 为空校验放到submit时校验
            if (!value) {
                return Promise.resolve();
            }
            if (value.length > appNameMaxLen) {
                return Promise.reject(new Error('字符数超过限制'));
            }

            try {
                const appId = getAppId();
                const params = appId ? {appName: value, appId} : {appName: value};
                const {nameRes, nameMessage} = (await api.checkAppInfo(params)) || {};

                if (nameRes === CheckAppInfoStatus.CheckFail) {
                    return Promise.reject(new Error(nameMessage));
                }

                return Promise.resolve();
            } catch (error: any) {
                return Promise.reject(new Error(error.msg || '机审接口调用错误'));
            }
        },
    },
    appAvatar: {
        formItemProps: {
            key: '2',
            name: 'appAvatar',
            label: '头像',
            required: true,
        },
        requiredErrorMsg: '头像不能为空，请上传',
    },
    appDesc: {
        formItemProps: {
            key: '3',
            name: 'appDesc',
            label: '简介',
            required: true,
        },
        placeholder: '请输入简介',
        maxCharLen: appDescMaxLen,
        requiredErrorMsg: '简介不能为空，请输入',
        // 有值时校验（字符数限制&异步机审）
        validate: async (value: string) => {
            if (!value) {
                // return Promise.reject(new Error('简介不能为空，请输入'));
                return Promise.resolve();
            }

            if (value.length > appDescMaxLen) {
                return Promise.reject(new Error('字符数超过限制'));
            }

            try {
                const appId = getAppId();
                const params = appId ? {appDesc: value, appId} : {appDesc: value};
                const {descRes, descMessage} = (await api.checkAppInfo(params)) || {};

                if (descRes === CheckAppInfoStatus.CheckFail) {
                    return Promise.reject(new Error(descMessage));
                }

                return Promise.resolve();
            } catch (error: any) {
                return Promise.reject(new Error(error.msg || '机审接口调用错误'));
            }
        },
    },
    frameworkType: {
        formItemProps: {
            key: '4',
            name: 'frameworkType',
            label: '',
            required: false,
        },
        // requiredErrorMsg: '类型不能为空，请选择',
    },
} as const;

export interface FieldValidateInfo {
    status: 'success' | 'error';
    help: string;
}

export const initValidateInfo: Record<FieldNames, FieldValidateInfo> = {
    appName: {
        status: 'success',
        help: '',
    },
    appDesc: {
        status: 'success',
        help: '',
    },
    appAvatar: {
        status: 'success',
        help: '',
    },
    frameworkType: {
        status: 'success',
        help: '',
    },
} as const;

export interface FlowAgentCreateInfoFormRef {
    submit: () => void;
}
