import {Form, Input} from 'antd';
import {useCallback, useEffect, forwardRef, useState, ChangeEvent, useImperativeHandle, useRef} from 'react';
import {useLocation} from 'react-router-dom';
import {getCharacterLen} from '@/utils/text';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
// TODO：FormWrapper 可以放到公共样式
import {AppBuildType, AppFrameworkType} from '@/api/appList/interface';
import {FormWrapper} from '@/modules/dataset/components/customStyle';
// TODO: 后续改引用公共上传图片组件
import UploadAvatar from '../FlowAgentCreateUploadAvatar';
import {
    FieldNames,
    FieldValidateInfo,
    FieldData,
    appFormConfig,
    agentFormConfig,
    getAppId,
    initValidateInfo,
} from './interface';

/**
 * 应用信息表单
 */
const FlowAgentCreateInfoForm = forwardRef(
    (
        {
            buildType,
            initFieldData,
            submitOK,
            validateFail,
        }: {
            buildType?: AppBuildType;
            initFieldData: FieldData;
            submitOK: (fieldData: FieldData) => void;
            validateFail?: (errorInfo: any) => void;
        },
        ref
    ) => {
        const [form] = Form.useForm();
        const {ubcClickLog} = useUbcLog();

        const ROUTE_AGENT_INDEX = 1;

        // 判断是否是由agent路由进入
        const isFromAgentRoute = useLocation().pathname.split('/')[ROUTE_AGENT_INDEX] === 'agent';
        const formConfig = isFromAgentRoute ? agentFormConfig : appFormConfig;
        const [appAvatarUrl, setAppAvatarUrl] = useState(initFieldData.appAvatar);
        const [validateInfo, setValidateInfo] = useState<Record<FieldNames, FieldValidateInfo>>(initValidateInfo);

        // validateInfoRef.current 及时获取上一次校验的信息
        const validateInfoRef = useRef<Record<FieldNames, FieldValidateInfo>>(initValidateInfo);
        useEffect(() => {
            validateInfoRef.current = validateInfo;
        }, [validateInfo]);

        // 校验表单为空字字段
        const validateFormRequired = useCallback(async () => {
            const fieldsValue = form.getFieldsValue();
            const currentValidateInfo = {...validateInfoRef.current};

            (Object.keys(fieldsValue) as FieldNames[]).forEach(fieldName => {
                const formItemConfig = formConfig[fieldName];

                if (!fieldsValue[fieldName] && formItemConfig.formItemProps.required) {
                    currentValidateInfo[fieldName] = {
                        status: 'error',
                        help: formItemConfig.requiredErrorMsg || '',
                    };
                }
            });

            validateInfoRef.current = currentValidateInfo;
            setValidateInfo({
                ...currentValidateInfo,
            });
        }, [form, formConfig]);

        // check表单是否校验成功
        const checkFormValidated = useCallback(() => {
            const findError = (Object.keys(validateInfoRef.current) as FieldNames[]).find(
                fieldName => validateInfoRef.current[fieldName].status === 'error'
            );
            return !findError;
        }, []);

        // 对外暴露submit事件
        useImperativeHandle(
            ref,
            () => {
                return {
                    submit() {
                        // submit时校验表单为空字段
                        validateFormRequired();

                        // 表单校验通过执行form submit
                        if (checkFormValidated()) {
                            return form.submit();
                        }

                        // 校验失败回调
                        return validateFail && validateFail(validateInfoRef.current);
                    },
                };
            },
            [checkFormValidated, form, validateFail, validateFormRequired]
        );

        // submit表单校验成功回调
        const submitFinish = useCallback(
            async (values: FieldData) => {
                submitOK(values);
            },
            [submitOK]
        );

        // submit表单校验失败回调
        const submitFail = useCallback(
            (errorInfo: any) => {
                validateFail && validateFail(errorInfo);
                // eslint-disable-next-line no-console
                console.log('Failed:', errorInfo);
            },
            [validateFail]
        );

        // InputBlur 时校验小程序名称/简介
        const validateInputBlur = useCallback(
            async (event: ChangeEvent<HTMLInputElement>) => {
                const fieldName = event.target.dataset.name as FieldNames;
                const inputValue = event.target.value;
                const formItemValidate = formConfig[fieldName].validate;

                formItemValidate &&
                    formItemValidate(inputValue)
                        .then(() => {
                            setValidateInfo({
                                ...validateInfoRef.current,
                                [fieldName]: {
                                    status: 'success',
                                    help: '',
                                },
                            });
                        })
                        .catch((err: Error) => {
                            setValidateInfo({
                                ...validateInfoRef.current,
                                [fieldName]: {
                                    status: 'error',
                                    help: err.message,
                                },
                            });
                        });
            },
            [formConfig]
        );

        // onChange时 清空校验
        const clearFieldValidateInfo = useCallback(async (event: ChangeEvent<HTMLInputElement>) => {
            const fieldName = event.target.dataset.name as FieldNames;

            setValidateInfo({
                ...validateInfoRef.current,
                [fieldName]: {
                    status: 'success',
                    help: '',
                },
            });
        }, []);

        const appAvatarUrlChange = useCallback(
            (imgUrl: string) => {
                setAppAvatarUrl(imgUrl);

                setValidateInfo({
                    ...validateInfoRef.current,
                    appAvatar: {
                        status: 'success',
                        help: '',
                    },
                });
            },
            [setAppAvatarUrl]
        );

        const uploadAvatarClick = useCallback(() => {
            // 创建应用点击头像上传打点
            if (buildType !== undefined && !getAppId()) {
                const logEventNameMaps = {
                    [AppBuildType.LowCode]: EVENT_TRACKING_CONST.LowCodeUploadAvatar,
                    [AppBuildType.SelfDevelop]: EVENT_TRACKING_CONST.SelfDevelopUploadAvatar,
                    [AppBuildType.Prompt]: EVENT_TRACKING_CONST.PromptUploadAvatar,
                };
                ubcClickLog(logEventNameMaps[buildType]);
            }
        }, [buildType, ubcClickLog]);

        return (
            <FormWrapper className="">
                <Form
                    name="app-info-form"
                    layout="horizontal"
                    colon={false}
                    form={form}
                    onFinish={submitFinish}
                    onFinishFailed={submitFail}
                >
                    <Form.Item
                        {...formConfig.appName.formItemProps}
                        validateStatus={validateInfo.appName.status}
                        help={validateInfo.appName.help}
                        initialValue={initFieldData.appName}
                    >
                        <Input
                            autoComplete="off"
                            showCount={{
                                formatter: ({value}) => (
                                    <span>
                                        {isFromAgentRoute ? value.length : getCharacterLen(value)}&nbsp;/&nbsp;
                                        {formConfig.appName.maxCharLen}
                                    </span>
                                ),
                            }}
                            placeholder={formConfig.appName.placeholder}
                            data-name={formConfig.appName.formItemProps.name}
                            onBlur={validateInputBlur}
                            onChange={clearFieldValidateInfo}
                        />
                    </Form.Item>
                    <Form.Item
                        {...formConfig.appAvatar.formItemProps}
                        validateStatus={validateInfo.appAvatar.status}
                        help={validateInfo.appAvatar.help}
                        valuePropName="imgUrl"
                        trigger="setImgUrl"
                        initialValue={initFieldData.appAvatar}
                    >
                        <UploadAvatar
                            imgUrl={appAvatarUrl}
                            setImgUrl={appAvatarUrlChange}
                            onClick={uploadAvatarClick}
                        />
                    </Form.Item>
                    <Form.Item
                        {...formConfig.appDesc.formItemProps}
                        validateStatus={validateInfo.appDesc.status}
                        help={validateInfo.appDesc.help}
                        initialValue={initFieldData.appDesc}
                    >
                        <Input
                            autoComplete="off"
                            showCount={{
                                formatter: ({value}) => (
                                    <span>
                                        {isFromAgentRoute ? value.length : getCharacterLen(value)}&nbsp;/&nbsp;
                                        {formConfig.appDesc.maxCharLen}
                                    </span>
                                ),
                            }}
                            placeholder={formConfig.appDesc.placeholder}
                            data-name={formConfig.appDesc.formItemProps.name}
                            onBlur={validateInputBlur}
                            onChange={clearFieldValidateInfo}
                        />
                    </Form.Item>
                    {initFieldData.frameworkType === AppFrameworkType.Chat && (
                        <Form.Item
                            {...formConfig.frameworkType.formItemProps}
                            validateStatus={validateInfo.frameworkType.status}
                            help={validateInfo.frameworkType.help}
                            initialValue={initFieldData.frameworkType}
                        >
                            {/* MVP只有对话式应用，tag样式写死 */}
                            {/* <div>
                                <Tag
                                    color="default"
                                    className="inline-flex h-[1.6875rem] items-center text-sm font-medium"
                                    bordered={false}
                                >
                                    对话式应用
                                </Tag>
                                <Tooltip
                                    title="通过对话问答的方式理解并满足用户需求"
                                    arrow={false}
                                    overlayStyle={{height: '45px', maxWidth: 'none'}}
                                >
                                    <span className="iconfont icon-questionCircle text-gray-quaternary hover:cursor-pointer"></span>
                                </Tooltip>
                            </div> */}
                            {/* <Radio.Group value={initFieldData.frameworkType}>
                                <Space direction="vertical">
                                    <Radio value={AppFrameworkType.Chat}>
                                        对话式应用
                                        <span className="ml-4 text-gray-500">对话式应用说明说明</span>
                                    </Radio>
                                    <Radio value={AppFrameworkType.Generic}>
                                        框架式
                                    <span className="ml-4 text-gray-500">框架式应用说明说明</span>
                                </Radio>
                                </Space>
                            </Radio.Group> */}
                        </Form.Item>
                    )}
                </Form>
            </FormWrapper>
        );
    }
);

export default FlowAgentCreateInfoForm;
