import React, {MouseEvent, useCallback, useRef, useState} from 'react';
import {Upload, Image} from 'antd';
import type {UploadChangeParam} from 'antd/es/upload';
import type {RcFile, UploadFile, UploadProps} from 'antd/es/upload/interface';
import {getBase64, getImgWH} from '@/utils/image';

const {Dragger} = Upload;

const props: UploadProps = {
    name: 'image',
    multiple: false,
    action: '/lingjing/app/uploadPhoto',
    accept: 'image/jpeg,image/png,image/jpg,image/webp',
    showUploadList: false,
    // onDrop(e) {
    //     console.log('Dropped files', e.dataTransfer.files);
    // },
};

interface UploadAvatarProps {
    imgUrl: string;
    setImgUrl: (imgUrl: string) => void;
    onClick?: () => void;
}

export default function UploadAvatar({imgUrl, setImgUrl, onClick}: UploadAvatarProps) {
    const [loading, setLoading] = useState(false);
    const [loadPercent, setLoadPercent] = useState<number | undefined>(0);
    const [errMsg, setErrMsg] = useState('');
    const [base64ImgUrl, setBase64ImageUrl] = useState<string>('');
    const [previewOpen, setPreviewOpen] = useState(false);

    // 上传前校验
    const beforeUpload: UploadProps['beforeUpload'] = useCallback(
        async (file: RcFile): Promise<RcFile | boolean> => {
            setImgUrl('');
            setBase64ImageUrl('');
            setErrMsg('');

            // 图片格式，仅支持png、jpeg、jpg、webp
            const isImageFormat = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'].some(
                (type: string) => file.type === type
            );
            if (!isImageFormat) {
                setErrMsg('图片格式仅支持png、jpeg、jpg、webp，请重新上传!');
                return Promise.resolve(false);
            }

            // 通过bos自动裁切&压缩图片，参考文档：https://cloud.baidu.com/doc/BOS/s/Yldh5wq5b
            // bos服务处理图片上限大小20M
            const isSizeLimit = file.size < 20 * 1024 * 1024;
            if (!isSizeLimit) {
                setErrMsg('头像大小需小于20MB，请重新上传!');
                return Promise.resolve(false);
            }

            try {
                // 前端文件流生成base64图片
                const url = await getBase64(file);
                setBase64ImageUrl(url);

                // bos服务处理图片接受原图尺寸上限30000像素*30000像素
                const {width, height} = await getImgWH(url);
                if (width > 30000 || height > 30000) {
                    setErrMsg('头像大于30000像素*30000像素，请重新上传!');
                    return Promise.resolve(false);
                }

                return Promise.resolve(file);
            } catch (error) {
                return Promise.resolve(file);
            }
        },
        [setImgUrl]
    );

    // 上传时状态处理
    const handleChange: UploadProps['onChange'] = useCallback(
        async (info: UploadChangeParam<UploadFile>) => {
            if (info.file.status === 'uploading') {
                setLoading(true);

                const percent = Math.min(Math.floor(info.file.percent || 0), 99);
                setLoadPercent(percent);

                // 前端文件流生成base64图片
                if (!base64ImgUrl) {
                    const url = await getBase64(info.file.originFileObj as RcFile);
                    setBase64ImageUrl(url);
                }
            } else if (info.file.status === 'done') {
                setLoading(false);

                const {errno, msg, data: url} = info.file.response;
                if (errno === 0 && url) {
                    setImgUrl(url);
                } else {
                    setErrMsg(msg || '图片上传失败，请重新上传!');
                }
            } else if (info.file.status === 'error') {
                setLoading(false);
                setErrMsg('图片上传失败，请重新上传!');
            }
        },
        [base64ImgUrl, setImgUrl]
    );

    const uploadRef = useRef<any>(null);

    const comProps = {
        ...props,
        ref: uploadRef,
        beforeUpload,
        onChange: handleChange,
    };

    /**
     * 点击上传区域，部分场景禁止冒泡调起Dragger组件的上传事件
     * 上传中、已上传完成，阻止冒泡调起上传文件选择框
     */
    const draggerAreaClick = useCallback(
        (e: MouseEvent) => {
            if (loading || imgUrl) {
                e.stopPropagation();
            }

            onClick && onClick();
        },
        [imgUrl, loading, onClick]
    );

    /**
     * 删除图片
     */
    const removeImg = useCallback(
        (e: MouseEvent) => {
            setImgUrl('');
            setBase64ImageUrl('');

            // 删除图片，阻止冒泡调起上传文件选择框
            e.stopPropagation();
        },
        [setImgUrl]
    );
    /**
     * 预览图片
     */
    const previewImg = useCallback((e: MouseEvent) => {
        e.stopPropagation();
        setPreviewOpen(true);
    }, []);
    /**
     * 关闭预览
     */
    const handleCancel = useCallback(() => {
        setPreviewOpen(false);
    }, []);

    return (
        <Dragger {...comProps} style={{border: '1px #eceef3 solid'}}>
            <div
                className={`relative h-[12.1875rem] hover:${!loading && !imgUrl ? 'cursor-pointer' : 'cursor-default'}`}
                onClick={draggerAreaClick}
            >
                <div className="flex items-center justify-center">
                    {/* 图片不存在且未上传中时展示默认图标、上传按钮和提示 */}
                    {!imgUrl && !loading && (
                        <div className="flex flex-col items-center">
                            <div
                                className={`${
                                    errMsg ? 'mt-5' : 'mt-8'
                                } flex h-[5.0625rem] w-[5.0625rem] items-center justify-center rounded-full bg-[#f1f1ff]`}
                            >
                                <span className="iconfont icon-picture text-2xl text-primary"></span>
                            </div>
                            <div className="flex flex-col items-center">
                                <p className="text-black-base mt-[0.94rem] text-sm">
                                    将图片拖到此处，或者
                                    <a className="underline">点击上传</a>
                                </p>
                                <p className="flex h-[1.35rem] w-[120%] scale-[0.8333] items-center justify-center text-xs text-gray-quaternary">
                                    头像支持png、jpeg、jpg、webp格式 / 建议比例1:1 / 比例超限自动裁剪
                                </p>
                                {errMsg && <p className="text-sm leading-[1.375rem] text-error">{errMsg}</p>}
                            </div>
                        </div>
                    )}
                    {/* 图片上传时展示图片+百分比 */}
                    {loading && (
                        <div className="relative mt-6 flex h-36 w-36 items-center justify-center rounded-full bg-[#f1f1ff]">
                            <img className="h-full w-full rounded-full object-cover" src={base64ImgUrl} alt="avatar" />
                            <div className="absolute left-0 right-0 flex h-full w-full items-center justify-center rounded-full bg-black/[.6] text-white">
                                <span className="text-xl font-bold">{loadPercent}%</span>
                            </div>
                        </div>
                    )}
                    {/* 图片上传成功展示图片 */}
                    {imgUrl && (
                        <div className="group relative mt-6 h-36 w-36">
                            <img
                                className="absolute left-0 right-0 h-full w-full rounded-full object-cover"
                                src={base64ImgUrl || imgUrl}
                                alt="avatar"
                            />
                            <div className="absolute left-0 right-0 hidden h-full w-full items-center justify-around rounded-full bg-black/[.4] group-hover:flex">
                                <span
                                    className={'iconfont icon-visual text-4xl text-white hover:cursor-pointer'}
                                    onClick={previewImg}
                                ></span>
                                <span
                                    className={'iconfont icon-delete text-4xl text-white hover:cursor-pointer'}
                                    onClick={removeImg}
                                ></span>
                            </div>
                            <Image
                                style={{display: 'none'}}
                                preview={{
                                    visible: previewOpen,
                                    src: imgUrl,
                                    onVisibleChange: handleCancel,
                                    toolbarRender: () => <></>,
                                }}
                            />
                        </div>
                    )}
                </div>
            </div>
        </Dragger>
    );
}
