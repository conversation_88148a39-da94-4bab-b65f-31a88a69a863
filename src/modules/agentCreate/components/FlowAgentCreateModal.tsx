import {Button, Modal} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import styled from '@emotion/styled';
import urls from '@/links';
import api from '@/api/appList';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {AppBuildType, AppFrameworkType, AppTemplateId, CreateAppParams} from '@/api/appList/interface';
import FlowAgentCreateInfoForm from './FlowAgentCreateInfoForm';
import {FlowAgentCreateInfoFormRef} from './FlowAgentCreateInfoForm/interface';

const StyleModal = styled(Modal)`
    .ant-modal-content {
        padding: 1.5rem 4rem !important;
    }
`;
/**
 * 获取应用(框架)类型
 * MVP版本 buildType 和 frameworkType 是一一对应关系
 * @param buildType 应用创建类型
 * @param frameworkType 应用(框架)类型
 * @returns frameworkType 应用(框架)类型
 */
const getFrameworkType = (
    buildType: AppBuildType = AppBuildType.LowCode,
    frameworkType?: AppFrameworkType
): AppFrameworkType => {
    return frameworkType || (buildType === AppBuildType.SelfDevelop ? AppFrameworkType.Generic : AppFrameworkType.Chat);
};

/**
 * 初始化表单提交数据
 * @param buildType 创建应用类型 默认可视化创建
 * @param frameworkType 应用框架类型 默认对话式应用
 * @param templateId 模版应用Id
 * @returns CreateAppParams 创建应用提交数据
 */
const getInitFormData = ({
    buildType = AppBuildType.LowCode,
    frameworkType,
    templateId,
}: {
    buildType?: AppBuildType;
    frameworkType?: AppFrameworkType;
    templateId?: AppTemplateId;
}): CreateAppParams => {
    const initBuildType = buildType || AppBuildType.LowCode;

    return {
        appName: '',
        appAvatar: '',
        // appAvatar: 'https://mbs1.bdstatic.com/static/smartprogram-developer/img/<EMAIL>',
        appDesc: '',
        buildType: initBuildType,
        frameworkType: getFrameworkType(initBuildType, frameworkType),
        templateId: templateId || '',
    };
};

/**
 * 创建应用表单弹窗
 */
export default function AppCreateModal({
    open,
    setModalOpen,
    buildType,
    templateId,
    frameworkType,
}: {
    open: boolean;
    setModalOpen: (open: boolean) => void;
    buildType: AppBuildType;
    templateId?: AppTemplateId;
    frameworkType?: AppFrameworkType;
}) {
    const navigate = useNavigate();
    const {ubcShowLog, ubcClickLog} = useUbcLog();

    const [formData, setFormData] = useState<CreateAppParams>(
        getInitFormData({
            buildType,
            frameworkType,
            templateId,
        })
    );

    const [submitLoading, setSubmitLoading] = useState(false);

    const closeModal = useCallback(() => {
        setModalOpen(false);
        setSubmitLoading(false);
    }, [setModalOpen]);

    const formRef = useRef<FlowAgentCreateInfoFormRef>(null);

    // 弹窗打开初始化表单数据
    useEffect(() => {
        if (open === true) {
            const initFormData = getInitFormData({
                buildType,
                frameworkType,
                templateId,
            });

            setFormData(initFormData);

            ubcShowLog(EVENT_TRACKING_CONST.PageAppPluginCreate);
        }
    }, [open, buildType, templateId, frameworkType, ubcShowLog]);

    // submit表单
    const submitForm = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.AppPluginCreateSubmit);
        // 有机审校验请求接口，所以submit时loading
        setSubmitLoading(true);

        formRef.current?.submit();
    }, [ubcClickLog]);

    // submit表单校验成功回调
    const submitOK = useCallback(
        async (values: Partial<CreateAppParams>) => {
            // 调用创建应用接口
            setSubmitLoading(true);

            const createAppParams = {
                ...formData,
                ...values,
            };

            try {
                const appId = await api.postCreateApp(createAppParams);

                // 关闭弹窗
                closeModal();

                // 创建应用成功时，从后端获取应用id，然后低代码跳转到应用编辑页，其他跳转应用概览页
                if (createAppParams.buildType === AppBuildType.LowCode) {
                    navigate(urls.agentFlowEdit.fill({appId}));
                } else {
                    navigate(urls.agentFlowDetail.fill({id: appId, tab: PluginTabType.Overview}));
                }
            } catch (error: any) {
                setSubmitLoading(false);
            }
        },
        [closeModal, navigate, formData]
    );

    // submit表单校验失败回调
    const validateFail = useCallback((err: any) => {
        // eslint-disable-next-line no-console
        console.log('validateFail', err);
        setSubmitLoading(false);
    }, []);

    // 取消关闭创建应用弹窗
    const closeModalByCancelBtn = useCallback(() => {
        closeModal();
        ubcClickLog(EVENT_TRACKING_CONST.AppPluginCreateCancel);
    }, [closeModal, ubcClickLog]);

    // X按钮关闭创建应用弹窗
    const closeModalByXBtn = useCallback(() => {
        closeModal();
        ubcClickLog(EVENT_TRACKING_CONST.AppPluginCreateClose);
    }, [closeModal, ubcClickLog]);

    return (
        <StyleModal
            width={730}
            centered
            footer={null}
            title={'可视化编排'}
            forceRender
            open={open}
            onOk={submitForm}
            onCancel={closeModalByXBtn}
        >
            <div className="relative flex  items-center rounded-[18px] bg-white pt-4">
                <div className="relative h-[27.8rem] w-[730px] rounded-[1rem] bg-white">
                    {/* 创建表单 */}
                    {open && (
                        <FlowAgentCreateInfoForm
                            key="2"
                            ref={formRef}
                            buildType={formData.buildType}
                            initFieldData={{
                                appName: formData.appName,
                                appAvatar: formData.appAvatar,
                                appDesc: formData.appDesc,
                                frameworkType: formData.frameworkType,
                            }}
                            submitOK={submitOK}
                            validateFail={validateFail}
                        />
                    )}
                    <div className="absolute bottom-[2.62rem] left-0 flex w-full justify-center">
                        <Button size="large" className="w-60" onClick={closeModalByCancelBtn}>
                            取消
                        </Button>
                        <Button
                            loading={submitLoading}
                            size="large"
                            type="primary"
                            className="ml-6 w-60"
                            onClick={submitForm}
                        >
                            创建
                        </Button>
                    </div>
                </div>
            </div>
        </StyleModal>
    );
}
