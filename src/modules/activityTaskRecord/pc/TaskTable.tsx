/* eslint-disable complexity */
/* eslint-disable react/jsx-no-bind */
// 任务列表
import {Button, Skeleton, Table} from 'antd';
import type {ColumnsType} from 'antd/es/table';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useLottery} from '@/components/Prize/useLottery';
import {TaskListItem, TaskStatus, SubTaskStatus} from '@/api/task/interface';
import api from '@/api/task';
import Empty from '@/components/Empty';
import {getDateAndTime} from '@/utils/date';
import urls from '@/links';

// 任务进度
const taskProgressMap: Record<string, {text: string; color: string}> = {
    [TaskStatus.IN_PROGRESS]: {text: '进行中', color: '#1890FF'},
    [TaskStatus.FINISHED]: {text: '已结束', color: '#E0E0E0'},
};

// 子任务进度
const subTaskProgressMap: Record<string, {text: string; color: string}> = {
    [SubTaskStatus.IN_PROGRESS]: {text: '进行中', color: '#1890FF'},
    [SubTaskStatus.WAIT_DRAW]: {text: '待抽奖', color: '#FBA800'},
    [SubTaskStatus.DRAWN]: {text: '已抽奖', color: '#00B47E'},
    [SubTaskStatus.STOP]: {text: '过期已终止', color: '#E0E0E0'},
    [SubTaskStatus.NO_WIN]: {text: '已抽奖', color: '#00B47E'},
};

// 区分是否是个人进度/任务进度的进度
const ProgressText = ({
    isTaskProgress,
    progress,
    record,
}: {
    isTaskProgress: boolean;
    progress: string;
    record: TaskListItem;
}) => {
    // 如果未配置奖品，个人进度显示为已完成
    if (!isTaskProgress && record.rewardFlag === 0 && progress !== SubTaskStatus.IN_PROGRESS.toString()) {
        return (
            <div className="flex items-center gap-[10px]">
                已完成
                <div style={{backgroundColor: '#00B47E'}} className="h-[6px] w-[6px] rounded-full" />
            </div>
        );
    }

    // 如果未配置奖品，任务进度显示为已结束
    if (isTaskProgress && record.rewardFlag === 0 && progress !== TaskStatus.IN_PROGRESS.toString()) {
        return (
            <div className="flex items-center gap-[10px]">
                已结束
                <div style={{backgroundColor: '#E0E0E0'}} className="h-[6px] w-[6px] rounded-full" />
            </div>
        );
    }

    const progressMap: Record<string, {text: string; color: string}> = isTaskProgress
        ? taskProgressMap
        : subTaskProgressMap;

    return (
        <div className="flex items-center gap-[10px]">
            {progressMap[progress].text}
            <div style={{backgroundColor: progressMap[progress].color}} className="h-[6px] w-[6px] rounded-full" />
        </div>
    );
};

const PAGE_SIZE = 20;

// 拆分后的组件
function TaskFinishedRender({record, openPrizeDetail}: {record: TaskListItem; openPrizeDetail: (params: any) => void}) {
    if (record.showRewardFlag === 0) {
        return <div className="text-gray-quaternary">已完成</div>;
    }

    return (
        <Button
            type="link"
            className="truncate p-0"
            onClick={() =>
                openPrizeDetail({
                    recordId: record.id,
                    subTaskId: record.taskId,
                    taskPageId: record.taskPageId,
                    taskPkgId: record.taskPkgId,
                })
            }
        >
            查看详情
        </Button>
    );
}

// 抽离奖品详情渲染逻辑
function RewardRender({
    record,
    openPrizeDetail,
    openLottery,
}: {
    record: TaskListItem;
    openPrizeDetail: (params: any) => void;
    openLottery: (params: any) => void;
}) {
    const handleLotteryClick = () => {
        openLottery({
            subTaskId: record.taskId,
            taskPageId: record.taskPageId,
            taskPkgId: record.taskPkgId,
        });
    };

    const renderLotteryButton = () => (
        <Button type="link" className="truncate p-0" onClick={handleLotteryClick}>
            去抽奖
        </Button>
    );

    const isTaskInProgress = record.status === TaskStatus.IN_PROGRESS;
    const isTaskFinished = record.status === TaskStatus.FINISHED;
    const isSubTaskInProgress = record.subStatus === SubTaskStatus.IN_PROGRESS;
    const isSubTaskWaitDraw = record.subStatus === SubTaskStatus.WAIT_DRAW;
    const isSubTaskDrawn = record.subStatus === SubTaskStatus.DRAWN;
    const isSubTaskNoWin = record.subStatus === SubTaskStatus.NO_WIN;

    // 如果未配置奖品，显示--
    if (record.rewardFlag === 0 || record.subStatus === SubTaskStatus.STOP) {
        return <div className="truncate">--</div>;
    }

    // 任务已结束，子任务未抽奖，则显示去抽奖
    if (isTaskFinished && isSubTaskWaitDraw) {
        return renderLotteryButton();
    }

    // 任务进行中或已结束，子任务进行中
    if ((isTaskInProgress || isTaskFinished) && isSubTaskInProgress) {
        return <div className="truncate text-gray-quaternary">请先完成任务</div>;
    }

    // 任务进行中，子任务待抽奖，则显示去抽奖
    if (isTaskInProgress && isSubTaskWaitDraw) {
        return renderLotteryButton();
    }

    // 如果已抽奖但未中奖，显示未中奖
    if (isSubTaskNoWin) {
        return <div className="truncate text-gray-quaternary">未中奖</div>;
    }

    // 任务进行中/已结束，子任务已抽奖，则显示查看详情
    if (isSubTaskDrawn) {
        return <TaskFinishedRender record={record} openPrizeDetail={openPrizeDetail} />;
    }

    return null;
}

export default function TaskTable() {
    const {openPrizeDetail, openLottery} = useLottery();

    const [pageNo, setPageNo] = useState(1);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(true);
    const [isFirstLoad, setIsFirstLoad] = useState(true);
    const [taskList, setTaskList] = useState<TaskListItem[]>([]);
    const navigate = useNavigate();

    const getTaskList = useCallback(async () => {
        setLoading(true);
        const ans = await api.getTaskList({pageSize: PAGE_SIZE, pageNo});
        setTaskList(ans.taskList);
        setTotal(ans.total);
        setLoading(false);
        setIsFirstLoad(false);
    }, [pageNo]);

    const context = useLottery();
    context.refreshTaskRecord = getTaskList;

    useEffect(() => {
        // 如果数据总量小于等于20条，强制设置为第一页
        if (total <= PAGE_SIZE && pageNo > 1) {
            setPageNo(1);
        } else {
            getTaskList();
        }
    }, [getTaskList, pageNo, total]);

    const columns: ColumnsType<TaskListItem> = useMemo(
        () => [
            {
                title: '任务名称',
                dataIndex: 'taskName',
                key: 'taskName',
                width: '25%',
                ellipsis: true,
            },
            {
                title: '个人进度',
                dataIndex: 'subStatus',
                key: 'subStatus',
                width: '15%',
                fixed: 'left',
                render: (subStatus: TaskListItem['subStatus'], record: TaskListItem) => (
                    <ProgressText isTaskProgress={false} progress={subStatus.toString()} record={record} />
                ),
            },
            {
                title: '任务进度',
                dataIndex: 'status',
                key: 'status',
                width: '15%',
                fixed: 'left',
                render: (status: TaskListItem['status'], record: TaskListItem) => (
                    <ProgressText isTaskProgress progress={status.toString()} record={record} />
                ),
            },
            {
                title: '任务完成时间',
                dataIndex: 'finishTime',
                key: 'finishTime',
                width: '20%',
                ellipsis: true,
                render: (finishTime: number | null) => (finishTime ? getDateAndTime(finishTime * 1000) : '--'),
            },
            {
                title: '奖品详情',
                key: 'reward',
                width: '25%',
                fixed: 'right',
                ellipsis: true,
                render: (_, record: TaskListItem) => (
                    <RewardRender record={record} openPrizeDetail={openPrizeDetail} openLottery={openLottery} />
                ),
            },
        ],
        [openPrizeDetail, openLottery]
    );

    return isFirstLoad && loading ? (
        <Skeleton active />
    ) : total > 0 ? (
        <div className="mt-[26px] rounded-lg border border-[#ECEEF366] bg-white p-6">
            <Table
                dataSource={taskList}
                columns={columns}
                rowKey="id"
                className="border border-[#ECEEF380]"
                loading={loading}
                scroll={{y: 'calc(100vh - 300px)'}}
                pagination={false}
            />
            <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-gray-quaternary">
                    {pageNo}/{Math.ceil(total / PAGE_SIZE)} 总共：{total} 项
                </div>
                {total > PAGE_SIZE ? (
                    <div className="flex items-center">
                        <Button
                            type="link"
                            disabled={pageNo === 1}
                            onClick={() => setPageNo(pageNo - 1)}
                            className="p-0"
                        >
                            &lt;
                        </Button>
                        {(() => {
                            const totalPages = Math.ceil(total / PAGE_SIZE);
                            const pages = [];

                            // 计算要显示的页码范围
                            let startPage = Math.max(1, pageNo - 2);
                            const endPage = Math.min(totalPages, startPage + 4);

                            // 如果当前页靠近末尾，调整起始页
                            if (endPage - startPage < 4) {
                                startPage = Math.max(1, endPage - 4);
                            }

                            // 添加页码按钮
                            for (let i = startPage; i <= endPage; i++) {
                                pages.push(
                                    <Button
                                        key={i}
                                        type="link"
                                        onClick={() => setPageNo(i)}
                                        className={`mx-1 flex h-8 w-8 items-center justify-center p-0 ${
                                            i === pageNo ? 'rounded border border-blue-500' : ''
                                        }`}
                                    >
                                        {i}
                                    </Button>
                                );
                            }

                            return pages;
                        })()}
                        <Button
                            type="link"
                            disabled={pageNo >= Math.ceil(total / PAGE_SIZE)}
                            onClick={() => setPageNo(pageNo + 1)}
                            className="p-0"
                        >
                            &gt;
                        </Button>
                    </div>
                ) : (
                    <div className="text-sm text-gray-quaternary">共{total}条数据</div>
                )}
            </div>
        </div>
    ) : (
        <Empty desc={'您还没有完成任务，快去完成任务参与抽奖吧！'}>
            <Button
                type="primary"
                className=" mt-[18px] rounded-full text-sm font-medium"
                onClick={() => navigate(urls.activityList.raw())}
            >
                去完成任务
            </Button>
        </Empty>
    );
}
