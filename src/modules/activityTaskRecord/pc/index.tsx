import {useEffect} from 'react';
import ContentHeader from '@/modules/pluginCenter/components/ContentHeader';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import TaskTable from './TaskTable';

export default function TaskRecordList() {
    const handleBackActivityList = () => {
        history.back();
    };

    const {displayLog} = useUbcLogV3();
    useEffect(() => {
        displayLog();
    }, [displayLog]);
    return (
        <div className="min-w-7xl relative h-full max-w-[1680px] overflow-hidden">
            {/* 页面标题 */}
            <div className="flex items-center font-pingfang font-medium">
                <span
                    className="iconfont icon-a-leftbar text-black-base mr-2 cursor-pointer text-[19px] hover:text-primary"
                    onClick={handleBackActivityList}
                ></span>
                <ContentHeader title={'任务记录'} />
            </div>
            <TaskTable />
        </div>
    );
}
