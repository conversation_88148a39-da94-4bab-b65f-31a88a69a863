/**
 * @file 任务记录列表页入口组件 - PC 端
 * <AUTHOR>
 */
import {CacheProvider} from 'react-suspense-boundary';
import {LogContextProvider} from '@/utils/loggerV2/context';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import {LotteryProvider} from '@/components/Prize';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import TaskRecordList from './pc';

export default function IndexPc() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.TASK_RECORD}>
            <LotteryProvider>
                <CacheProvider>
                    <CommonErrorBoundary pendingFallback={<Loading />}>
                        <TaskRecordList />
                    </CommonErrorBoundary>
                </CacheProvider>
            </LotteryProvider>
        </LogContextProvider>
    );
}
