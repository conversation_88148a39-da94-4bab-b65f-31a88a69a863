/**
 * @file 活动详情页 - PC 端
 * <AUTHOR>
 */
import {An<PERSON>, But<PERSON>} from 'antd';
import {Fragment, useCallback, useRef, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import copy from 'copy-to-clipboard';
import useMessage from 'antd/es/message/useMessage';
import ContentHeader from '@/modules/pluginCenter/components/ContentHeader';
import RichText from '@/components/RichText';
import ActivityTags from '@/modules/activityList/components/ActivityTags';
import useActivityDetailHook, {RequestStatus} from '@/modules/activityDetail/hooks/useActivityDetailHook';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';

const StyledAnchor = styled(Anchor)`
    & .ant-anchor {
        &::before {
            display: none;
        }
        .ant-anchor-ink-visible {
            display: none !important;
        }
        .ant-anchor-link {
            padding: 12px 9px !important;
            line-height: 14px;
            font-size: 14px;
            width: 180px;
        }
        .ant-anchor-link-active {
            background: #5562f21a;
            font-weight: 500;
            border-radius: 6px;
        }
        .ant-anchor-link-title {
            color: #272933 !important;
        }
        .ant-anchor-link-title-active {
            color: #000311 !important;
        }
    }
`;

export default function ActivityDetail() {
    const [messageApi, MessageContext] = useMessage();
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const {
        anchorItems,
        navigateBack,
        requestStatus,
        activityDetailData,
        buttonConfig,
        buttonAction,
        countdownTimeConfig,
        requestError,
    } = useActivityDetailHook();

    const getContainer = useCallback(
        () => scrollContainerRef.current?.parentElement?.parentElement!,
        [scrollContainerRef]
    );

    const copyLink = useCallback(() => {
        copy(window.location.href);
        messageApi.open({
            type: 'success',
            content: '分享链接已复制到剪切板',
            style: {
                marginTop: '16px',
            },
        });
    }, [messageApi]);

    // 状态用于存储第一个锚点元素的 offsetTop 距离
    const [firstAnchorOffsetTop, setFirstAnchorOffsetTop] = useState<number>(0);

    // 更新第一个锚点元素的 offsetTop 距离的函数
    const updateFirstAnchorOffsetTop = useCallback(() => {
        const firstTitle = anchorItems?.[0]?.title;
        const element = document.getElementById(firstTitle);
        if (element) {
            const rect = element.getBoundingClientRect();
            setFirstAnchorOffsetTop(rect.top + 1);
        } else {
            setFirstAnchorOffsetTop(0);
        }
    }, [anchorItems]);

    // 打印日志
    useEffect(() => {
        updateFirstAnchorOffsetTop();
    }, [updateFirstAnchorOffsetTop]);

    return (
        <div className="box-border flex flex-col" ref={scrollContainerRef}>
            {requestStatus === RequestStatus.Loading ? (
                <Loading />
            ) : requestStatus === RequestStatus.Failed ? (
                <RenderError error={requestError} />
            ) : (
                <div className="flex w-full justify-center gap-6">
                    <div className="h-full min-w-[754px] max-w-[914px] xlg:w-[754px] xl:w-[914px]">
                        {/* 页面标题 */}
                        <div className="mb-[21px] flex items-center font-pingfang font-medium">
                            <span
                                className="iconfont icon-left cursor-pointer pr-2 text-2xl hover:text-primary"
                                onClick={navigateBack}
                            />
                            <ContentHeader title="活动详情" />
                        </div>
                        <div className="mb-6 flex justify-center gap-3 rounded-xl bg-white p-4">
                            <img className="h-[118px] w-[208px] rounded-[9px]" src={activityDetailData?.coverImg} />
                            <div className="flex-1 overflow-hidden">
                                <div className="mb-[6px] overflow-hidden overflow-ellipsis whitespace-nowrap text-[21px] font-medium leading-[24px] text-black">
                                    {activityDetailData?.title}
                                </div>
                                <div className="mb-[6px]">
                                    <ActivityTags activityData={activityDetailData} />
                                </div>
                                {countdownTimeConfig?.prefix && (
                                    <div className="mb-[6px] text-sm leading-[22px] text-gray-secondary">
                                        <span>{countdownTimeConfig.prefix}</span>
                                        {countdownTimeConfig?.date ? (
                                            <span>{countdownTimeConfig.date}</span>
                                        ) : (
                                            <Fragment>
                                                <span className="text-[#F62F00]">{countdownTimeConfig.day}</span>天
                                                <span className="text-[#F62F00]">{countdownTimeConfig.hour}</span>时
                                                <span className="text-[#F62F00]">{countdownTimeConfig.minute}</span>分
                                            </Fragment>
                                        )}
                                    </div>
                                )}
                                {!!buttonConfig?.name?.length && (
                                    <div className="text-right">
                                        <Button
                                            className="h-[35px] w-[180px] rounded-full border-none font-medium text-white"
                                            style={{background: buttonConfig?.bgColor}}
                                            onClick={buttonAction}
                                            disabled={buttonConfig?.disabled}
                                        >
                                            {buttonConfig.name}
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="rounded-xl bg-white p-6 pt-0">
                            {activityDetailData?.content?.map(item => (
                                <div key={item.title} className="pt-6" id={item.title}>
                                    <div className="mb-[18px] text-lg font-bold leading-[25px] text-black">
                                        {item.title}
                                    </div>
                                    <RichText className="break-words text-gray-secondary" content={item.detail} />
                                </div>
                            ))}
                        </div>
                    </div>
                    <div>
                        {/* 锚点位置 = ue(top) - 32(padding间距) - 4(Anchor组件自带的margin-top) */}
                        <div className="bg-gray-light sticky top-[223px] min-w-[182px]">
                            {anchorItems?.length > 1 && (
                                <StyledAnchor
                                    replace
                                    bounds={firstAnchorOffsetTop}
                                    items={anchorItems}
                                    showInkInFixed={false}
                                    getContainer={getContainer}
                                />
                            )}
                        </div>
                        <div className="fixed bottom-[18px] cursor-pointer" onClick={copyLink}>
                            <div className="mb-[9px] flex h-[50px] w-[50px] items-center justify-center rounded-full bg-white">
                                <span className="iconfont icon-sharing text-2xl text-black" />
                            </div>
                            <div className="text-center text-sm leading-[22px] text-gray-secondary">转发</div>
                        </div>
                    </div>
                </div>
            )}
            {MessageContext}
        </div>
    );
}
