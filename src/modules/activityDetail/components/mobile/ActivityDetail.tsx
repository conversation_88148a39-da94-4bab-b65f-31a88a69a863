/**
 * @file 活动详情页 - 移动端
 * <AUTHOR>
 */
import {Fragment, useCallback, useEffect, useState} from 'react';
import {Anchor, Button} from 'antd';
import styled from '@emotion/styled';
import classNames from 'classnames';
import Header from '@/modules/activityForm/components/mobile/Header';
import RichText from '@/components/RichText';
import useActivityDetailHook, {RequestStatus} from '@/modules/activityDetail/hooks/useActivityDetailHook';
import Loading from '@/components/Loading';
import ActivityTags from '@/modules/activityList/components/ActivityTags';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';

// 上滑后固定的顶部高度
const StickyHeaderHeight = 137;

const StyledAnchor = styled(Anchor)`
    background: #fff !important;

    &::before {
        display: none;
    }

    & .ant-anchor {
        padding: 6px 0 !important;

        .ant-anchor-ink,
        .ant-anchor-ink-visible {
            display: none !important;
        }
        .ant-anchor-link {
            margin-right: 8px;
            padding: 8px 0 !important;
            background: #f5f6fa;
            border-radius: 9px;
            line-height: 17px;
            font-size: 14px;

            a {
                padding: 0 12px !important;
            }

            &:last-child {
                margin-right: 0;
            }
        }
        .ant-anchor-link-title {
            color: #1e1f24 !important;
        }
        .ant-anchor-link-title-active {
            color: #5562f2 !important;
        }
    }
`;

export default function ActivityDetail() {
    const [isSticky, setSticky] = useState(false);
    const {
        anchorItems,
        requestStatus,
        activityDetailData,
        buttonConfig,
        buttonAction,
        countdownTimeConfig,
        requestError,
    } = useActivityDetailHook();

    const handleScroll = useCallback(() => {
        setSticky(window.scrollY > 295);
    }, []);

    useEffect(() => {
        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [handleScroll]);

    if (requestStatus === RequestStatus.Loading) {
        return <Loading />;
    }

    return (
        <div className="min-h-[100vh] bg-white">
            <Header
                className="sticky top-0 bg-white px-3 text-center text-lg font-medium text-[#000]"
                title="活动详情"
            />
            {requestStatus === RequestStatus.Failed ? (
                <RenderError error={requestError} />
            ) : (
                <div>
                    {!!activityDetailData?.coverImg?.length && (
                        <img className="h-[232px] w-full" src={activityDetailData?.coverImg} />
                    )}
                    <div className="px-3 pt-[18px]">
                        <div className="mb-[6px] overflow-hidden overflow-ellipsis whitespace-nowrap text-base font-medium leading-[22px] text-black">
                            {activityDetailData?.title}
                        </div>
                        <div className="h-4">
                            <ActivityTags activityData={activityDetailData} />
                        </div>
                        <div
                            className={classNames({
                                'sticky top-[49px]': isSticky,
                            })}
                        >
                            <div className="flex items-center justify-between bg-white py-[6px]">
                                {isSticky ? (
                                    <div className="overflow-hidden overflow-ellipsis whitespace-nowrap text-base font-medium text-black">
                                        {activityDetailData?.title}
                                    </div>
                                ) : (
                                    countdownTimeConfig?.prefix && (
                                        <span className="text-sm text-gray-secondary">
                                            <span>{countdownTimeConfig.prefix}</span>
                                            {countdownTimeConfig?.date ? (
                                                <span>{countdownTimeConfig.date}</span>
                                            ) : (
                                                <Fragment>
                                                    <span className="text-[#F62F00]">{countdownTimeConfig.day}</span>天
                                                    <span className="text-[#F62F00]">{countdownTimeConfig.hour}</span>时
                                                    <span className="text-[#F62F00]">{countdownTimeConfig.minute}</span>
                                                    分
                                                </Fragment>
                                            )}
                                        </span>
                                    )
                                )}
                                {!!buttonConfig?.name?.length && (
                                    <Button
                                        className="h-[30px] w-[85px] rounded-full font-medium leading-none"
                                        type="primary"
                                        disabled={buttonConfig?.disabled}
                                        onClick={buttonAction}
                                    >
                                        {buttonConfig?.name}
                                    </Button>
                                )}
                            </div>
                            {anchorItems?.length > 1 && (
                                <StyledAnchor
                                    replace
                                    direction="horizontal"
                                    targetOffset={StickyHeaderHeight}
                                    items={anchorItems}
                                />
                            )}
                        </div>
                        <div className="w-full pt-[6px]">
                            {activityDetailData?.content?.map(item => (
                                <div key={item.title} className="">
                                    <div
                                        className="my-[3px] text-base font-bold leading-[22px] text-black"
                                        id={item.title}
                                    >
                                        {item.title}
                                    </div>
                                    <RichText className="break-words text-gray-secondary" content={item.detail} />
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
