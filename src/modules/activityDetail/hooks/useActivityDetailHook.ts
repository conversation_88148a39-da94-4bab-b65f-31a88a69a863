/**
 * @file 活动详情页 PC 端和移动端公用的 hook
 * <AUTHOR>
 */
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {matchPath, useLocation, useNavigate, useParams, useSearchParams} from 'react-router-dom';
import dayjs from 'dayjs';
import {ACTIVITY_NOT_FOUND, ActivityButtonStatus, ActivityDetailData} from '@/api/activityDetail/interface';
import api from '@/api/activityDetail';
import urls from '@/links';
import {ActivityStatus} from '@/api/activityList/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';

import useLogin from '@/components/Login/hooks/useLogin';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {DateFormat} from '@/utils/date';

export enum RequestStatus {
    Loading = 'loading',
    Success = 'success',
    Failed = 'failed',
}

function openUrl(url: string) {
    setTimeout(() => {
        window.open(url, '_blank');
    });
}

/**
 * 获取倒计时时间
 *
 * @param time 目标时间
 */
function getDayHourMinuteSeconds(time: number) {
    const timeInterval = Math.max(time * 1000 - Date.now(), 0);

    const day = Math.floor(timeInterval / (1000 * 60 * 60 * 24));
    const hour = Math.floor((timeInterval % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minute = Math.floor((timeInterval % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeInterval % (1000 * 60)) / 1000);

    return {day, hour, minute, seconds, date: ''};
}

/**
 * 根据各按钮的 status 获取当前要展示的按钮下标
 * @param activityDetailData 活动数据
 */
function getActivityCurrentButtonIndex(activityDetailData: ActivityDetailData) {
    // 总共3个按钮，因此按钮 3 阶段完成后则显示 【已完成】按钮（下标 3）
    if (activityDetailData.button3Status === ActivityButtonStatus.Completed) {
        if (activityDetailData.isButton3RepeatSubmit === 1) return 4;
        return 3;
    }

    // 按钮 2 阶段完成后，如果配置了按钮 3 则显示按钮 3（下标 2），反之则显示 【已完成】按钮（下标 3）
    if (activityDetailData.button2Status === ActivityButtonStatus.Completed) {
        if (activityDetailData.isButton2RepeatSubmit === 1) return 4;
        return activityDetailData.button3Name ? 2 : 3;
    }

    // 按钮 1 阶段完成后，首先判断 isButton1RepeatSubmit 是否为 1；若是为 1 则返回按钮4（下标 3）； 否则按照之前逻辑显示
    if (activityDetailData.button1Status === ActivityButtonStatus.Completed) {
        if (activityDetailData.isButton1RepeatSubmit === 1) return 4;

        // 如果配置了按钮 2 则显示按钮 2（下标 1），否则继续判断是否配置了按钮 3，如果配置了则显示按钮 3（下标 2），否则显示【已完成】按钮（下标 3）
        return activityDetailData.button2Name ? 1 : activityDetailData.button3Name ? 2 : 3;
    }

    // 默认显示按钮 1
    return 0;
}

/**
 * 动态获取属性值
 * @param activityDetailData 活动数据
 * @param property 属性名（如 'button1Url', 'button2FormId' 等）
 */
function getDynamicProperty(activityDetailData: ActivityDetailData, valueKey: string): string {
    for (let i = 1; i <= 3; i++) {
        const repeatSubmitKey = `isButton${i}RepeatSubmit` as keyof ActivityDetailData;
        if (activityDetailData[repeatSubmitKey] === 1) {
            // 直接在这里生成动态键，而无需调用外部函数
            const dynamicKey = `button${i}${valueKey.charAt(0).toUpperCase()}${valueKey.slice(
                1
            )}` as keyof ActivityDetailData;
            return String(activityDetailData[dynamicKey] || '');
        }
    }
    return '';
}

/**
 * 获取按钮配置数组
 * @param activityDetailData 活动数据
 */
function getActivityButtonsConfig(activityDetailData?: ActivityDetailData) {
    if (!activityDetailData) {
        return [];
    }

    return [
        {
            name: activityDetailData.button1Name,
            url: activityDetailData.button1Url,
            formId: activityDetailData.button1FormId,
            status: activityDetailData.button1Status,
            disabled: activityDetailData.button1Status === ActivityButtonStatus.Completed,
            bgColor: '#5562F2',
            logExtValue: 1,
        },
        {
            name: activityDetailData.button2Name,
            url: activityDetailData.button2Url,
            formId: activityDetailData.button2FormId,
            status: activityDetailData.button2Status,
            disabled: activityDetailData.button2Status === ActivityButtonStatus.Completed,
            bgColor: '#5562F2',
            logExtValue: 2,
        },
        {
            name: activityDetailData.button3Name,
            url: activityDetailData.button3Url,
            formId: activityDetailData.button3FormId,
            status: activityDetailData.button3Status,
            disabled: activityDetailData.button3Status === ActivityButtonStatus.Completed,
            bgColor: '#5562F2',
            logExtValue: 3,
        },
        {
            name: '已完成',
            url: '',
            formId: '',
            disabled: true,
            bgColor: '#5562F266',
            logExtValue: 3,
        },
        {
            name: '再次填写',
            url: getDynamicProperty(activityDetailData, 'Url'),
            formId: getDynamicProperty(activityDetailData, 'FormId'),
            disabled: false,
            bgColor: '#5562F2',
            logExtValue: 3,
        },
    ];
}

export default function useActivityDetailHook() {
    const {id} = useParams();
    const [searchParams] = useSearchParams();
    const source = searchParams.get('source');
    const navigate = useNavigate();
    const location = useLocation();
    // 请求状态
    const [requestStatus, setRequestStatus] = useState(RequestStatus.Loading);
    const [requestError, setRequestError] = useState<any>();
    const [activityDetailData, setActivityDetailData] = useState<ActivityDetailData>();
    const countdownTimeInterval = useRef<NodeJS.Timer>();
    const [counter, setCounter] = useState(0);
    const buttonsConfig = useMemo(() => getActivityButtonsConfig(activityDetailData), [activityDetailData]);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {loginCheck} = useLogin();

    const {clickLog, displayLog} = useUbcLogV3();

    const anchorItems = useMemo(() => {
        return (activityDetailData?.content || []).map(item => ({
            key: item.title,
            href: `#${item.title}`,
            title: item.title,
        }));
    }, [activityDetailData?.content]);

    // 倒计时区域显示数据
    const countdownTimeConfig = useMemo(() => {
        if (
            activityDetailData?.status === ActivityStatus.Draft ||
            activityDetailData?.status === ActivityStatus.NotStarted
        ) {
            return {
                counter,
                prefix: '活动开始时间：还有',
                ...getDayHourMinuteSeconds(activityDetailData.beginTime),
            };
        }

        if (activityDetailData?.status === ActivityStatus.InProgress) {
            return {
                counter,
                prefix: '活动结束时间：还有',
                ...getDayHourMinuteSeconds(activityDetailData.endTime),
            };
        }

        if (
            activityDetailData?.status === ActivityStatus.Ended ||
            activityDetailData?.status === ActivityStatus.Offline
        ) {
            return {
                prefix: '活动结束时间：',
                ...getDayHourMinuteSeconds(activityDetailData.endTime),
                date: dayjs(activityDetailData.endTime * 1000).format(DateFormat.YMD_CHINESE),
            };
        }
    }, [activityDetailData, counter]);

    // 按钮数据
    const buttonConfig = useMemo(() => {
        if (activityDetailData?.status === ActivityStatus.NotStarted) {
            return {
                name: '未开始',
                url: '',
                formId: '',
                disabled: true,
                bgColor: '#5562F266',
                logExtValue: 3,
            };
        }

        if (activityDetailData?.status === ActivityStatus.InProgress) {
            const buttonIndex = getActivityCurrentButtonIndex(activityDetailData);

            return buttonsConfig[buttonIndex];
        }

        if (
            activityDetailData?.status === ActivityStatus.Ended ||
            activityDetailData?.status === ActivityStatus.Offline
        ) {
            return {
                name: '评奖中',
                url: '',
                formId: '',
                disabled: true,
                bgColor: '#5562F266',
                logExtValue: 3,
            };
        }
    }, [activityDetailData, buttonsConfig]);

    // 按钮点击事件
    const buttonAction = useCallback(() => {
        if (!isLogin) {
            loginCheck();
            return;
        }

        if (buttonConfig?.formId) {
            const sourceParamsString = source ? `&source=${source}` : '';
            openUrl(
                `${urls.activityFormDetail.raw()}?formId=${buttonConfig.formId}&activityId=${id}${sourceParamsString}`
            );
        }

        if (buttonConfig?.url) {
            const url = new URL(buttonConfig.url);
            // 如果跳转的地址是活动表单详情页，则需要拼接 activityId
            if (matchPath(urls.activityFormDetail.raw(), url.pathname)) {
                url.searchParams.append('activityId', id!);
                source && url.searchParams.append('source', source);
            }

            openUrl(url.toString());
        }

        clickLog(EVENT_VALUE_CONST.ACTIVITY_PARTICIPATE, {
            eActivityStage: buttonConfig?.logExtValue,
        });
    }, [isLogin, loginCheck, buttonConfig, id, clickLog, source]);

    const navigateBack = useCallback(() => {
        if (location.key === 'default' || window.history.length === 1) {
            navigate(urls.activityList.raw());
        } else {
            navigate(-1);
        }
    }, [location.key, navigate]);

    useEffect(() => {
        if (!id) {
            setRequestStatus(RequestStatus.Failed);
            setRequestError(new Error('缺少参数ID'));
            return;
        }

        setRequestStatus(RequestStatus.Loading);
        api.getActivityDetail({activityId: id})
            .then(res => {
                setActivityDetailData(res);
                setRequestStatus(RequestStatus.Success);
            })
            .catch(e => {
                if (e?.errno === ACTIVITY_NOT_FOUND) {
                    navigate(urls.activityList.raw(), {replace: true});
                } else {
                    setRequestStatus(RequestStatus.Failed);
                    setRequestError(e);
                }
            });

        displayLog();
    }, [id, navigate, displayLog]);

    useEffect(() => {
        clearInterval(countdownTimeInterval.current);
        if (
            activityDetailData?.status === ActivityStatus.InProgress ||
            activityDetailData?.status === ActivityStatus.NotStarted
        ) {
            countdownTimeInterval.current = setInterval(() => {
                setCounter(prevState => prevState + 1);
            }, 1000);
        }

        return () => {
            clearInterval(countdownTimeInterval.current);
        };
    }, [activityDetailData?.status]);

    return {
        anchorItems,
        countdownTimeConfig,
        buttonConfig,
        buttonAction,
        navigateBack,
        requestStatus,
        activityDetailData,
        requestError,
    };
}
