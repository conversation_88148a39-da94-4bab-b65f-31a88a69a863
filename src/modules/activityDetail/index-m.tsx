/**
 * @file 活动详情页入口 - 移动端
 * <AUTHOR>
 */
import {useParams} from 'react-router-dom';
import {CacheProvider} from 'react-suspense-boundary';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import ActivityDetail from '@/modules/activityDetail/components/mobile/ActivityDetail';

export default function ActivityDetailM() {
    const {id} = useParams();

    return (
        <div>
            <CacheProvider>
                <CommonErrorBoundary pendingFallback={<Loading />}>
                    <LogContextProvider page={EVENT_PAGE_CONST.ACTIVITY_DETAIL} ext={{activityId: String(id)}}>
                        <ActivityDetail />
                    </LogContextProvider>
                </CommonErrorBoundary>
            </CacheProvider>
        </div>
    );
}
