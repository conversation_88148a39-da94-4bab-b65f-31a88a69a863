/**
 * @file 创建工作流模式智能体
 * <AUTHOR>
 */

import {message} from 'antd';
import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useAgentNumModal} from '@/components/Sidebar/hooks/useAgentNum';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {createAgent} from '@/api/agentEdit';
import urls from '@/links';

export function useCreateWorkflowAgent() {
    const {setOpenAgentWarningModal, appNum, appLimit} = useAgentNumModal();
    const agentConfig = usePromptEditStoreV2(store => store.agentConfig);
    const navigate = useNavigate();

    // 判断是否正在调用 testAndPreviewAgent 方法
    const [isRequesting, setIsRequesting] = useState(false);

    const navigateToWorkflowAgent = useCallback(
        async (workflowId?: string) => {
            // 如果应用数达到上限，则弹出应用数达到上限的弹窗
            if (appNum && appLimit && appLimit <= appNum) {
                setOpenAgentWarningModal(true);
                return;
            }

            // 创建工作流模式智能体初始化参数
            const newAgentConfig = {
                ...agentConfig,
                agentInfo: {
                    ...agentConfig.agentInfo,
                    modeType: 1,
                },
                agentJson: {
                    ...agentConfig.agentJson,
                    workflowConf: {
                        id: '',
                        versionCode: 0,
                        workflowTempId: workflowId || '',
                    },
                },
            };

            try {
                setIsRequesting(true);

                // 调用 preview 方法
                const {appId} = await createAgent(newAgentConfig);
                // 跳转至智能体编辑页面
                if (appId) {
                    navigate(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=create`);
                }
            } catch (error: any) {
                console.error(error);
                message.error(error?.msg || '创建失败');
            } finally {
                setIsRequesting(false);
            }
        },
        [appNum, appLimit, agentConfig, setOpenAgentWarningModal, navigate]
    );

    return {
        navigateToWorkflowAgent,
        isRequesting,
    };
}
