/**
 * @file 搜索增强插件更新为联网搜索功能的提示
 * <AUTHOR>
 */
import {useCallback, useEffect, useState} from 'react';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import api from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';

export default function useWebSearchTip() {
    const [showContent, setShowContent] = useState('');
    const [isShowTips, setIsShowTips] = useState(false);

    const {extraData, appId} = usePromptEditStoreV2(store => ({
        extraData: store.extraData,
        appId: store.agentConfig.agentInfo.appId,
    }));

    const closeTips = useCallback(() => {
        setIsShowTips(false);
    }, []);

    useEffect(() => {
        if (extraData?.hasWebSearchPlugin && appId) {
            api.getPopup({name: PopupName.WebSearchPlugin, appId, autoView: true}).then(({show, content}) => {
                setIsShowTips(show);
                setShowContent(content);
            });
        }
    }, [extraData?.hasWebSearchPlugin, appId]);

    return {
        showContent,
        isShowTips,
        closeTips,
    };
}
