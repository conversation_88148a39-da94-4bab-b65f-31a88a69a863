import {message} from 'antd';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import {useCallback, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import Toast from 'antd-mobile/es/components/toast';
import urls from '@/links';
import {publishTestedAgent} from '@/api/agentEdit';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {AgentTabType} from '@/modules/agentList/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {startDatasetStrategy} from '@/api/agentEditV2';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {useWeChatAuthStore, WxDeployMapType} from '@/store/agent/WeChatAuthStore';
import {AgentPublishChannelType, WxAccountType, XmiChannelType} from '@/api/agentDeploy/interface';
import {generateSessionId, generateOperationModule, OperationModuleType} from '@/utils/monitor/utils';
import {reportError, CustomError, ErrorType} from '@/utils/monitor';
import {ViewAgentPermission, ViewPermissionMap} from '@/store/agent/utils';

const PUBLISH_SUCCESS_MESSAGE = '审核中，预计1小时反馈审核结果';

export const usePublishAction = (setOpen: (open: boolean) => void, openPublishSuccessModal?: () => void) => {
    const navigate = useNavigate();
    const [popupShow, setPopupShow] = useState(false);
    const {
        appId,
        deployChannels,
        fetchUpdateAgentPlugins,
        fetchUpdateAgentWorkflows,
        isSaving,
        setHasPublished,
        setShowPublishErrorModal,
        fetchAgentConfig,
        publishLoading,
        setPublishLoading,
        hasChangeForm,
        display,
        workflowModified,
        lastViewPermission,
    } = usePromptEditStoreV2(store => ({
        appId: store.agentConfig.agentInfo.appId,
        deployChannels: store.deployChannels,
        fetchUpdateAgentPlugins: store.fetchUpdateAgentPlugins,
        fetchUpdateAgentWorkflows: store.fetchUpdateAgentWorkflows,
        isSaving: store.isSaving,
        setHasPublished: store.setHasPublished,
        setShowPublishErrorModal: store.setShowPublishErrorModal,
        fetchAgentConfig: store.fetchAgentConfig,
        publishLoading: store.publishLoading,
        setPublishLoading: store.setPublishLoading,
        hasChangeForm: store.hasChangeForm,
        display: store.display,
        workflowModified: store.workflowModified,
        lastViewPermission: store.getViewPermission(),
    }));

    const {wxDeployMap} = useWeChatAuthStore(store => ({
        wxDeployMap: store.wxDeployMap,
    }));

    const [viewPermission, setViewPermission] = useState<ViewAgentPermission>(lastViewPermission);

    const [publishWorkflow, setPublishWorkflow] = useState(false);

    /** 已发布的智能体，必须在编辑后或者更改发布类型的时候才可以发布 */
    const allowClickPublish = appId && viewPermission && !isSaving;

    const getNewDeployChannels = (wxDeployMap: WxDeployMapType): AgentPublishChannelType[] => {
        const newDeployChannels = [];
        if (wxDeployMap[WxAccountType.WxServer]) {
            newDeployChannels.push(WxAccountType.WxServer);
        }

        if (wxDeployMap[WxAccountType.WxSubscribe]) {
            newDeployChannels.push(WxAccountType.WxSubscribe);
        }

        if (wxDeployMap[WxAccountType.WxMiniApp]) {
            newDeployChannels.push(WxAccountType.WxMiniApp);
        }

        if (wxDeployMap[XmiChannelType.XmiAppStore]) {
            newDeployChannels.push(XmiChannelType.XmiAppStore);
        }
        return newDeployChannels;
    };

    const canPublishAfterEdit = useMemo(() => {
        // workflowModified 默认值为 false，工作流模式，工作流修改了值改为 true，此时也可以发布
        if (display.change || workflowModified) {
            return true;
        }

        if (isMobileDevice()) {
            return hasChangeForm ? true : lastViewPermission !== viewPermission;
        } else {
            const newDeployChannels = getNewDeployChannels(wxDeployMap);
            const hasChangeDeploy = newDeployChannels.toString() !== deployChannels?.toString();
            return hasChangeForm ? true : lastViewPermission !== viewPermission || hasChangeDeploy;
        }
    }, [
        display.change,
        workflowModified,
        hasChangeForm,
        lastViewPermission,
        viewPermission,
        wxDeployMap,
        deployChannels,
    ]);

    const form = useFormInstance<AgentConfigV2>();

    // eslint-disable-next-line complexity, max-statements
    const handlePublish = useCallback(async () => {
        const isMobile = isMobileDevice();

        if (!canPublishAfterEdit) {
            return isMobile && Toast.show('请更新智能体配置或修改发布类型');
        }

        if (!allowClickPublish || publishLoading) {
            return;
        }

        // 标识进入发布逻辑
        setPublishLoading(true);
        try {
            if (isMobile) {
                await form.validateFields();
            }
        } catch (e) {
            if (isMobile) {
                Toast.show('请检查智能体配置填写正确');
            } else {
                message.error('请检查智能体配置填写正确');
            }

            // 关闭权限设置
            setPopupShow(false);
            setPublishLoading(false);
            return;
        }

        try {
            // 处理微信部署渠道
            const deployChannels = getNewDeployChannels(wxDeployMap);
            await publishTestedAgent(
                appId,
                ViewPermissionMap[viewPermission].permission,
                ViewPermissionMap[viewPermission].shareTag,
                deployChannels,
                publishWorkflow
            );

            setHasPublished(true);
            // 私有的发布不提示审核时间
            if (viewPermission === ViewAgentPermission.PRIVATE) {
                if (isMobile) {
                    Toast.show('发布成功');
                    navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
                } else {
                    message.success('发布成功');
                }
            } else {
                startDatasetStrategy(appId);
                // 拉取最新的 agentConfig
                await fetchAgentConfig({appId, formInstance: form});

                if (isMobile) {
                    Toast.show(PUBLISH_SUCCESS_MESSAGE);
                    navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
                } else {
                    // 打开发布成功
                    openPublishSuccessModal?.();
                }
            }

            // 关闭权限选项
            setOpen(false);
        } catch (e) {
            console.error(e);
            const error = e as {errno: number; msg: string};
            setShowPublishErrorModal(true, error);

            // 发布失败，获取最新插件列表状态信息，以便展示异常插件信息
            fetchUpdateAgentPlugins();
            fetchUpdateAgentWorkflows();

            // 上报发布失败原因-待细化
            reportError(new CustomError(ErrorType.BusinessError, e?.toString()));
        } finally {
            setPublishLoading(false);

            // 标识流程结束
            generateOperationModule(OperationModuleType.Other);
            generateSessionId();
        }
    }, [
        canPublishAfterEdit,
        allowClickPublish,
        publishLoading,
        setPublishLoading,
        form,
        appId,
        viewPermission,
        setHasPublished,
        fetchAgentConfig,
        openPublishSuccessModal,
        setShowPublishErrorModal,
        fetchUpdateAgentPlugins,
        fetchUpdateAgentWorkflows,
        setOpen,
        wxDeployMap,
        navigate,
        publishWorkflow,
    ]);

    return {
        handlePublish,
        viewPermission,
        setViewPermission,
        popupShow,
        canPublishAfterEdit,
        allowClickPublish,
        publishWorkflow,
        setPublishWorkflow,
    };
};
