/**
 * @file AI 生成可中断接口调用封装
 * <AUTHOR>
 * @date 2024/08/21
 */

import {useCallback, useRef, useState} from 'react';
import {Form} from 'antd';
import {buildConfigWithAI} from '@/api/buildConfig';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {MaxAddPluginCount} from '@/components/PluginSelect/util';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {AIBuildName} from '../interface';

type BuildType = 'system' | 'welcome' | 'recommend' | 'plugin';

export const useAIBuilding = (type: BuildType): [boolean, (building: boolean) => void] => {
    const {
        systemBuilding,
        setSystemBuilding,
        welcomeBuilding,
        setWelcomeBuilding,
        recommendBuilding,
        setRecommendBuilding,
        pluginBuilding,
        setPluginBuilding,
    } = usePromptEditStoreV2(store => ({
        systemBuilding: store.systemBuilding,
        welcomeBuilding: store.welcomeBuilding,
        recommendBuilding: store.recommendBuilding,
        pluginBuilding: store.pluginBuilding,
        setSystemBuilding: store.setSystemBuilding,
        setWelcomeBuilding: store.setWelcomeBuilding,
        setRecommendBuilding: store.setRecommendBuilding,
        setPluginBuilding: store.setPluginBuilding,
    }));

    return type === 'system'
        ? [systemBuilding, setSystemBuilding]
        : type === 'welcome'
        ? [welcomeBuilding, setWelcomeBuilding]
        : type === 'recommend'
        ? [recommendBuilding, setRecommendBuilding]
        : [pluginBuilding, setPluginBuilding];
};

export const useAIBuildHook = <T>(type: BuildType) => {
    const [building, setBuilding] = useAIBuilding(type);

    const controller = useRef<AbortController>();

    const [generateResult, setGenerateResult] = useState<T>();

    const form = Form.useFormInstance<AgentConfigV2>();

    const startGenerateResult = useCallback(async () => {
        setBuilding(true);

        const {agentJson, agentInfo} = (form.getFieldValue([]) || {}) as Partial<AgentConfigV2>;
        const {name, overview, description, recommends} = agentInfo || {};
        const {system, newPlugins} = agentJson || {};

        controller.current = new AbortController();
        /** AI 生成接口传参映射  */
        const AIBuildConfig: Record<
            BuildType,
            {
                buildName: AIBuildName;
                recommendCount?: number;
            }
        > = {
            system: {
                buildName: AIBuildName.SYSTEM,
                recommendCount: undefined,
            },
            welcome: {
                buildName: AIBuildName.WELCOME,
                recommendCount: undefined,
            },
            recommend: {
                buildName: AIBuildName.RECOMMEND,
                recommendCount: recommends?.length || 3,
            },
            plugin: {
                buildName: AIBuildName.PLUGIN,
                recommendCount: MaxAddPluginCount, // 插件最多可选 8 条
            },
        };

        try {
            const config = AIBuildConfig[type];
            const res = await buildConfigWithAI({
                signal: controller.current?.signal,
                buildType: config.buildName,
                recommendCount: config.recommendCount,
                name,
                overview,
                description,
                recommends,
                system: system || '',
                newPlugins: config.buildName === AIBuildName.PLUGIN ? newPlugins || [] : [],
            });
            return res;
        } catch (error) {
            console.error(error);
        } finally {
            setBuilding(false);
        }
    }, [setBuilding, form, type]);

    const abortGenerateResult = useCallback(() => {
        controller.current?.abort();
        setBuilding(false);
    }, [setBuilding]);

    return {
        generateResult,
        setGenerateResult,
        startGenerateResult,
        abortGenerateResult,
        building,
    };
};
