import {useCallback, useRef, useState} from 'react';

const loadingTextGroup = ['正在生成智能体头像…', '正在生成智能体简介…', '正在生成智能体指令…', '正在生成智能体开场白…'];

export function useLoadingTimer(): [string, {startTimer: () => void; cancelTimer: () => void}] {
    const [loadingTextIndex, setLoadingTextIndex] = useState(0);
    const timer = useRef<NodeJS.Timeout>();

    const startTimer = useCallback(() => {
        const duration = 10000;
        timer.current = setInterval(() => {
            setLoadingTextIndex(prev => {
                if (prev < loadingTextGroup.length - 1) {
                    return prev + 1;
                }

                return prev;
            });
        }, duration);
    }, []);

    const cancelTimer = useCallback(() => {
        if (timer.current) {
            clearInterval(timer.current);
        }
        setLoadingTextIndex(0);
    }, []);

    return [loadingTextGroup[loadingTextIndex], {startTimer, cancelTimer}];
}
