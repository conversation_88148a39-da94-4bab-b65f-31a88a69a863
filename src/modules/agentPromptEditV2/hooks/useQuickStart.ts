import React, {use<PERSON>allback, useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import cloneDeep from 'lodash/cloneDeep';
import {message} from 'antd';
import {useLoadingTimer} from '@/modules/agentPromptEditV2/hooks/useLoadingTimer';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/index';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {auditText} from '@/api/audit';
import {generateAgentConfigV2, GenerateAgentInfo, getIntroductionPrompts} from '@/api/agentEditV2';
import useSkipModal from '@/modules/agentPromptEditV2/hooks/useSkipModal';
import {CreateType, LoadType} from '@/utils/loggerV2/interface';
import {checkAgentNameRules, getAgentSource} from '@/modules/agentPromptEditV2/utils';
import {Prompt} from '@/api/agentEditV2/interface';
import guideEmitter from '@/store/agent/GuideEmitter';
import {createAgent} from '@/api/agentEdit';
import {defaultAgentConfig, AgentMode} from '@/store/agent/initState';
import urls from '@/links';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {AgentTab} from '../interface';

export const getAgentEditUrl = (appId: string) => {
    const agentSource = getAgentSource();
    return isMobileDevice()
        ? `${urls.agentPromptEdit.raw()}?appId=${appId}${agentSource ? `&agentSource=${agentSource}` : ''}`
        : `${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}${
              agentSource ? `&agentSource=${agentSource}` : ''
          }`;
};

export const mergeAgentConfig = (genConfig: GenerateAgentInfo) => {
    const config = cloneDeep(defaultAgentConfig);
    config.agentInfo.name = genConfig.name;
    config.agentInfo.description = genConfig.description;
    config.agentInfo.logoUrl = genConfig.logoUrl;
    config.agentInfo.overview = genConfig.overview;
    config.agentInfo.recommends = genConfig.recommends;
    config.agentJson.system = genConfig.system;
    return config;
};

// 示例智能体每组展示数量
const SELECTION_COUNT_PER_GROUP = 3;
/**
 * @description
 * 使用 Quick Start 组件的 hook。返回一个对象，包括以下属性：
 * - loading: boolean，表示是否正在加载中
 * - setLoading: (show: boolean) => void，更新 loading 状态
 * - loadingText: string，加载提示文本
 * - name: string，初始化的智能体名称
 * - introduction: string，初始化的智能体简介
 * - nameAuditErrorText: string，名称审核错误信息
 * - introAuditErrorText: string，简介审核错误信息
 * - handleNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void，处理名称变化事件
 * - handleIntroChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void，处理简介变化事件
 * - auditInputText: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void，处理输入内容审核
 * - handleCreate: () => void，创建智能体并关闭快速开始页面
 * - skipModalProps: SkipModalProps，跳过模态框相关属性
 * - handleSkip: () => void，处理跳过按钮点击事件
 * - controller: React.MutableRefObject<AbortController>, 控制器引用
 * - canCreate: boolean，判断是否可以创建智能体
 *
 * @param setShowQuickStart function，更新是否显示快速开始页面的函数
 * @param setInitAgentInfo function，更新初始化智能体信息的函数
 * @param parentLoading 父级是否处于loading态，影响思路推荐的Popover弹出时机
 * @returns {object} 返回一个对象，包含上述属性
 */
// eslint-disable-next-line max-statements
export default function useQuickStart(parentLoading?: boolean) {
    const [name, setName] = useState('');
    const [introduction, setIntroduction] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingText, {startTimer, cancelTimer}] = useLoadingTimer();
    const [nameAuditErrorText, setAuditErrorText] = useState('');
    const [introAuditErrorText, setIntroAuditErrorText] = useState('');
    const [createType, setCreateType] = useState<'' | 'empty' | 'gen'>('');
    // 输入框是否显示清除按钮，仅在选择示例智能体时显示
    const [showClear, setShowClear] = useState(false);
    const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setAuditErrorText(checkAgentNameRules(e.target.value) ? '' : '智能体名称仅支持中英文和数字');
        setName(e.target.value);
    }, []);

    const handleIntroChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setIntroduction(e.target.value);
    }, []);

    // 打点
    const {clickLog} = useUbcLogV2();

    const auditInput = useCallback(async (text: string, name: string) => {
        const prefixText = name === 'name' ? '智能体名称' : '智能体设定';
        let errMsg = '';
        let passAudit = false;

        if (text === '') {
            passAudit = true;
        } else if (name === 'name' && !checkAgentNameRules(text)) {
            errMsg = '智能体名称仅支持中英文和数字';
            passAudit = false;
        } else {
            try {
                await auditText(text, prefixText);
                passAudit = true;
            } catch (error: any) {
                errMsg = error.msg || error.message;
            }
        }

        if (name === 'name') {
            setAuditErrorText(errMsg);
        }

        if (name === 'introduction') {
            setIntroAuditErrorText(errMsg);
        }

        return passAudit;
    }, []);

    const auditInputText = useCallback(
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            if (e.target.value === '') {
                return;
            }

            auditInput(e.target.value, e.target.name);
        },
        [auditInput]
    );

    const navigate = useNavigate();
    const controller = useRef<AbortController>();
    const handleCreate = useCallback(async () => {
        try {
            clickLog(EVENT_VALUE_CONST.CREATE, EVENT_PAGE_CONST.QUICK_CREATE, {
                [EVENT_EXT_KEY_CONST.CREATE_TYPE]: CreateType.QUICK,
            });
            startTimer();
            setLoading(true);
            setCreateType('gen');
            const auditRes = await Promise.all([auditInput(name, 'name'), auditInput(introduction, 'introduction')]);
            if (auditRes.some(pass => !pass)) {
                return;
            }

            controller.current = new AbortController();
            const res = await generateAgentConfigV2(
                {introduction, mode: AgentMode.Assistant},
                controller.current.signal
            );
            const {appId} = await createAgent(mergeAgentConfig({...res, name: name || res.name}));
            if (!appId) return;

            navigate(getAgentEditUrl(appId), {replace: true});
        } catch (e: any) {
            message.error(e?.msg || '创建失败');
            console.error('创建失败', e);
            throw e;
        } finally {
            setLoading(false);
        }
    }, [clickLog, startTimer, auditInput, name, introduction, navigate]);

    useEffect(() => {
        return () => cancelTimer();
    }, [cancelTimer]);

    const handleCancel = useCallback(async () => {
        try {
            setCreateType('empty');
            setLoading(true);
            const {appId} = await createAgent(defaultAgentConfig);
            if (!appId) {
                return;
            }

            const url = getAgentEditUrl(appId);
            navigate(url, {replace: true});
            guideEmitter.emit('sticky-openPromptTemplate');
        } catch (e: any) {
            message.error(e?.msg || '创建失败');
            console.error('创建失败', e);
        } finally {
            setLoading(false);
        }
    }, [navigate]);

    const {skipModalProps, setConfirmOpen} = useSkipModal(controller, setLoading, handleCancel);

    const handleSkip = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.SKIP, EVENT_PAGE_CONST.QUICK_CREATE, {
            [EVENT_EXT_KEY_CONST.IS_LOADING]: loading ? LoadType.YES : LoadType.NO,
        });
        // 如果已经在生成配置，则需要二次确认
        if (loading) {
            return setConfirmOpen(true);
        }

        handleCancel();
    }, [clickLog, loading, setConfirmOpen, handleCancel]);

    const canCreate = introduction !== '' && (name ? checkAgentNameRules(name) : true);

    // 选择示例智能体后的提示气泡
    const [showChangeIntroductionPopover, setShowChangeIntroductionPopover] = useState(false);
    const [showPopoverTimeout, setShowPopoverTimeout] = useState<any>(null);

    // 气泡展示三秒关闭
    const openChangePopover = useCallback(() => {
        showPopoverTimeout && clearTimeout(showPopoverTimeout);
        setShowChangeIntroductionPopover(true);
        const timeout = setTimeout(() => {
            setShowChangeIntroductionPopover(false);
        }, 3000);
        setShowPopoverTimeout(timeout);
    }, [showPopoverTimeout]);

    // 示例智能体选择气泡
    const [selections, setSelections] = useState<Prompt[]>([]);
    const [currentGroupIndex, setCurrentGroupIndex] = useState<number>(0);
    const [currentSelectionIndex, setCurrentSelectionIndex] = useState<number>(-1);
    const currentSelections = useMemo(
        () =>
            selections.slice(
                currentGroupIndex * SELECTION_COUNT_PER_GROUP,
                (currentGroupIndex + 1) * SELECTION_COUNT_PER_GROUP
            ),
        [selections, currentGroupIndex]
    );
    const showSelectionPopover = useMemo(
        () => currentSelections?.length > 0 && !loading && !parentLoading,
        [currentSelections, loading, parentLoading]
    );

    // 选择示例智能体，填充名称和标题
    const handleSelect = useCallback(
        (selection: Prompt, index: number) => {
            const sampleAgentIndex = currentGroupIndex * SELECTION_COUNT_PER_GROUP + index;
            setCurrentSelectionIndex(sampleAgentIndex);
            // 用于打点的索引字符串，从 1 开始
            const logSampleAgentIndex = `${sampleAgentIndex + 1}`;
            clickLog(EVENT_VALUE_CONST.SAMPLE_AGENT_CHOOSE, EVENT_PAGE_CONST.QUICK_CREATE, {
                [EVENT_EXT_KEY_CONST.SAMPLE_AGENT_INDEX]: logSampleAgentIndex,
                [EVENT_EXT_KEY_CONST.CREATE_TYPE]: CreateType.QUICK,
            });
            setShowClear(true);
            setName(selection.title);
            setIntroduction(selection.introduction);
            openChangePopover();
        },
        [clickLog, openChangePopover, currentGroupIndex]
    );

    const isSelected = useCallback(
        (index: number) => {
            return index === currentSelectionIndex - currentGroupIndex * SELECTION_COUNT_PER_GROUP;
        },
        [currentSelectionIndex, currentGroupIndex]
    );

    // 更换示例智能体选项，三个一组循环展示
    const handleSwitch = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.SAMPLE_AGENT_CHANGE, EVENT_PAGE_CONST.QUICK_CREATE, {
            [EVENT_EXT_KEY_CONST.CREATE_TYPE]: CreateType.QUICK,
        });
        const totalGroup = Math.ceil(selections?.length / SELECTION_COUNT_PER_GROUP);
        setCurrentGroupIndex(pre => (pre + 1) % totalGroup);
    }, [clickLog, selections]);

    // 清空示例智能体名称和设定
    const handleClear = useCallback(() => {
        // 用于打点的索引字符串，从 1 开始
        const logSampleAgentIndex = `${currentSelectionIndex + 1}`;
        clickLog(EVENT_VALUE_CONST.QUICKSTART_CLEAR_INTRODUCTION, EVENT_PAGE_CONST.QUICK_CREATE, {
            [EVENT_EXT_KEY_CONST.SAMPLE_AGENT_INDEX]: logSampleAgentIndex,
            [EVENT_EXT_KEY_CONST.CREATE_TYPE]: CreateType.QUICK,
        });
        setCurrentSelectionIndex(-1);
        setShowClear(false);
        setName('');
        setIntroduction('');
    }, [clickLog, currentSelectionIndex]);

    useEffect(() => {
        (async () => {
            const data = (await getIntroductionPrompts()) || [];

            setSelections(data);
        })();
    }, []);

    return {
        loading,
        setLoading,
        loadingText,
        name,
        introduction,
        showClear,
        isSelected,
        nameAuditErrorText,
        introAuditErrorText,
        handleNameChange,
        handleIntroChange,
        setIntroduction,
        auditInput,
        auditInputText,
        handleCreate,
        skipModalProps,
        handleSkip,
        controller,
        canCreate,
        showSelectionPopover,
        currentSelections,
        handleSelect,
        handleSwitch,
        handleClear,
        showChangeIntroductionPopover,
        createType,
        setCreateType,
    };
}
