/**
 * @file AI 生成按钮配置 hook
 * <AUTHOR>
 * @date 2024/08/21
 */

import {useWatch} from 'antd/es/form/Form';
import {useMemo} from 'react';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AIBuildConfig} from '@/api/buildConfig';
import {AIBuildType} from '../interface';
import {useAIBuildHook} from './useAbortableRequest';

interface CommonAIProps {
    /** 按钮不可点击 */
    disabled: boolean;
    /** 提示文案 */
    tooltip: string;
    /** 按钮文案 */
    label: string;
    /** 打点时按钮类型 1: 生成; 2: 优化 */
    log: AIBuildType;
}

const AUTO_BUILD_CONFIG: Omit<CommonAIProps, 'disabled'> = {
    tooltip: '自动生成',
    label: 'AI生成',
    log: AIBuildType.BUILD,
};

const AUTO_PERF_CONFIG: Omit<CommonAIProps, 'disabled'> = {
    tooltip: '根据您的内容自动优化',
    label: 'AI优化',
    log: AIBuildType.PERF,
};

type AIBuildSystem = Partial<AIBuildConfig['pluginList']>;

export const useSystemButtonProps = () => {
    const name = useWatch(['agentInfo', 'name']);
    const overview = useWatch(['agentInfo', 'overview']);
    const system = useWatch<string>(['agentJson', 'system']);

    const systemAIButtonProps = useMemo<CommonAIProps>(() => {
        let config: CommonAIProps = {disabled: false, tooltip: '', label: '', log: AIBuildType.BUILD};
        if (system?.trim() !== '' && (name?.trim() === '' || overview?.trim() === '')) {
            config = {
                ...AUTO_PERF_CONFIG,
                disabled: true,
                tooltip: '请先填写名称和简介',
            };
        } else if (system?.trim() !== '') {
            config = {
                ...AUTO_PERF_CONFIG,
                disabled: false,
            };
        } else if (name?.trim() !== '' && overview?.trim() !== '') {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: false,
            };
        } else {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: true,
                tooltip: '请先填写名称和简介',
            };
        }

        return config;
    }, [system, name, overview]);

    return {systemAIButtonProps};
};

export const useWelcomeButtonProps = () => {
    const {hasDescription, hasName, hasOverview} = usePromptEditStoreV2(store => ({
        hasDescription: !!store.agentConfig.agentInfo.description?.trim(),
        hasName: !!store.agentConfig.agentInfo.name?.trim(),
        hasOverview: !!store.agentConfig.agentInfo.overview?.trim(),
    }));

    const welcomeAIButtonProps = useMemo<CommonAIProps>(() => {
        let config: CommonAIProps = {disabled: false, tooltip: '', label: '', log: AIBuildType.BUILD};
        if (hasDescription) {
            config = {
                ...AUTO_PERF_CONFIG,
                disabled: false,
            };
        } else if (hasName && hasOverview) {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: false,
            };
        } else {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: true,
                tooltip: '请先填写名称和简介',
            };
        }

        return config;
    }, [hasDescription, hasName, hasOverview]);

    return {welcomeAIButtonProps};
};

export const useRecommendButtonProps = () => {
    const {recommendBuilding, hasName, hasOverview} = usePromptEditStoreV2(store => ({
        recommendBuilding: store.recommendBuilding,
        hasName: !!store.agentConfig.agentInfo.name?.trim(),
        hasOverview: !!store.agentConfig.agentInfo.overview?.trim(),
    }));

    const recommendAIButtonProps = useMemo<CommonAIProps>(() => {
        let config: CommonAIProps = {disabled: false, tooltip: '', label: '', log: AIBuildType.BUILD};
        if (recommendBuilding) {
            config = {...AUTO_BUILD_CONFIG, disabled: true, tooltip: '正在生成开场白问题'};
        } else if (hasName && hasOverview) {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: false,
            };
        } else {
            config = {
                ...AUTO_BUILD_CONFIG,
                disabled: true,
                tooltip: '请先填写名称和简介',
            };
        }

        return config;
    }, [hasName, hasOverview, recommendBuilding]);

    return {recommendAIButtonProps};
};

export const usePluginButtonProps = () => {
    const {building} = useAIBuildHook<AIBuildSystem>('plugin');
    const name = useWatch(['agentInfo', 'name']);
    const overview = useWatch(['agentInfo', 'overview']);

    const systemAIButtonProps = useMemo(() => {
        let config = {disabled: false, tooltip: '', label: '', building: false};
        if (name?.trim() !== '' && overview?.trim() !== '') {
            config = {
                tooltip: '可根据你填写的配置信息自动添加插件',
                label: 'AI推荐',
                disabled: false,
                building: false,
            };
        } else {
            config = {
                label: 'AI推荐',
                disabled: true,
                tooltip: '请先填写名称和简介',
                building: false,
            };
        }

        if (building) {
            config = {
                label: '添加中',
                disabled: true,
                tooltip: '',
                building: true,
            };
        }

        return config;
    }, [name, overview, building]);

    return {systemAIButtonProps};
};
