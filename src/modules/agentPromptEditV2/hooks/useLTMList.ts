/**
 * @file 长期记忆记录数据，处理分页请求、轮询等逻辑
 * <AUTHOR>
 */

import {UIEvent, useState, useRef, useCallback, useEffect, MutableRefObject} from 'react';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import useBottomHint from '@/modules/center/hooks/useBottomHint';
import {getLTMList, summayLTM} from '@/api/agentEditV2';
import {
    LTMSummary,
    HasMore,
    SummaryTriggeredStatus,
    SummaryType,
    SummaryStatus,
    LTMSummaryProgress,
} from '@/api/agentEditV2/interface';
import {formatLTMSummaries, FormattedLTMSummary} from '@/modules/agentPromptEditV2/pc/components/LongTermMemory/utils';
import LTMSummaryProgressCmpt from '@/modules/agentPromptEditV2/components/LTMSummayProgress';

// 分页参数
const DEFAULT_PAGINATION_SETTINGS = {
    pageSize: 20,
    pageNo: 1,
};

// 底部提醒参数
const MIN_BOTTOM = 20;
const reachBottom = (container: HTMLDivElement): boolean =>
    Math.abs(container.scrollHeight - container.clientHeight - container.scrollTop) < MIN_BOTTOM;

// 轮询参数
const MAX_POLL_COUNT = 8;
const POLL_DELAY = 3000;

const useLTMList = ({containerRef}: {containerRef: MutableRefObject<HTMLDivElement | null>}) => {
    const {agentInfo} = usePromptEditStoreV2(store => ({
        agentInfo: store.agentConfig.agentInfo,
    }));

    const [summaries, setSummaries] = useState<LTMSummary[]>([]);
    const [dateToSummariesMap, setDateToSummariesMap] = useState<Map<string, FormattedLTMSummary[]>>();
    const [dateList, setDateList] = useState<string[]>([]);

    // 是否需要轮询
    const pollingRef = useRef(false);
    // 是否处于 loading 态
    const [isLoading, setIsLoading] = useState(true);
    // 是否展示底部loading
    const [showBottomLoading, setShowBottomLoading] = useState(false);
    // 是否请求到了最后一页
    const [couldRequestMore, setCouldRequestMore] = useState(false);
    // 是否处于请求中状态
    const inRequestingRef = useRef(false);
    // 列表滚动时的当前页码
    const pageNoRef = useRef(DEFAULT_PAGINATION_SETTINGS.pageNo);
    // 底部提醒
    const {BottomHint} = useBottomHint({showBottomLoading, couldRequestMore, list: summaries});
    // 手动总结状态
    const [summaryProgressInfos, setSummaryProgressInfos] = useState<Record<string, LTMSummaryProgress>>({});
    // 当前轮询次数
    const pollCount = useRef(0);
    // 当前轮询定时器
    const pollIntervalRef = useRef<NodeJS.Timer>();

    const loadSummaries = useCallback(() => {
        inRequestingRef.current = true;

        return getLTMList({
            appId: agentInfo.appId || '',
            pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
            pageNo: pageNoRef.current,
        }).finally(() => {
            setIsLoading(false);
            inRequestingRef.current = false;
        });
    }, [agentInfo.appId]);

    const updateSummaries = useCallback(
        async (oldSummaries?: LTMSummary[]) => {
            const {summaryList, hasMore} = await loadSummaries();
            setShowBottomLoading(false);
            setSummaries((oldSummaries || []).concat(summaryList));
            setCouldRequestMore(hasMore === HasMore.Yes);
            if (summaryList.some(summary => summary.summaryText === '')) {
                pollingRef.current = true;
            }
        },
        [loadSummaries]
    );

    // 处理滚动事件
    const handleScroll = useCallback(
        async (event: UIEvent<HTMLDivElement>) => {
            if (inRequestingRef.current || !couldRequestMore) {
                return;
            }
            if (!reachBottom(event.currentTarget)) {
                return;
            }
            pageNoRef.current = pageNoRef.current + 1;

            try {
                setShowBottomLoading(true);
                updateSummaries(summaries);
            } catch (e) {
                pageNoRef.current = pageNoRef.current - 1;
                setShowBottomLoading(false);
            }
        },
        [updateSummaries, summaries, couldRequestMore, inRequestingRef]
    );

    // 重置参数，resetSummaries为true时重置summaries
    const resetToInitialValues = useCallback((resetSummaries: boolean) => {
        if (resetSummaries) {
            setIsLoading(true);
            setSummaries([]);
            setDateToSummariesMap(new Map());
            setDateList([]);
            setSummaryProgressInfos({});
        }
        setShowBottomLoading(false);
        setCouldRequestMore(false);
        inRequestingRef.current = false;
        pageNoRef.current = DEFAULT_PAGINATION_SETTINGS.pageNo;
        pollCount.current = 0;
    }, []);

    // 停止轮询
    const stopPolling = useCallback(() => {
        pollIntervalRef.current && clearInterval(pollIntervalRef.current);
    }, []);

    // 更新总结进度
    const updateProgress = useCallback((summaries: LTMSummary[], isError?: boolean) => {
        const newProgressInfos: Record<string, LTMSummaryProgress> = {};
        summaries.forEach(summary => {
            const {summaryId, summaryText} = summary;
            if (isError && summaryText === '') {
                newProgressInfos[summaryId] = {
                    summaryStatus: SummaryStatus.Error,
                    summaryText,
                };
            } else {
                newProgressInfos[summaryId] = {
                    summaryStatus: summaryText === '' ? SummaryStatus.Doing : SummaryStatus.Finished,
                    summaryText,
                };
            }
        });
        setSummaryProgressInfos(newProgressInfos);
    }, []);

    // 开始轮询
    const startPolling = useCallback(() => {
        const fetchData = async () => {
            const {summaryList} = await getLTMList({
                appId: agentInfo.appId || '',
                pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
                pageNo: DEFAULT_PAGINATION_SETTINGS.pageNo,
            });
            try {
                if (pollCount.current >= MAX_POLL_COUNT) {
                    stopPolling();
                    return;
                }
                pollCount.current = pollCount.current + 1;
                updateProgress(summaryList);
                if (summaryList.every(summary => summary.summaryText !== '')) {
                    stopPolling();
                }
            } catch (err) {
                updateProgress(summaryList, true);
                stopPolling();
            }
        };

        if (pollCount.current < MAX_POLL_COUNT) {
            stopPolling();
            pollIntervalRef.current = setInterval(() => {
                fetchData();
            }, POLL_DELAY);
        }
    }, [agentInfo.appId, stopPolling, updateProgress]);

    // 触发手动总结
    const handleManualSummary = useCallback(
        async (showToast: (text: string) => void) => {
            const {status, text} = await summayLTM({appId: agentInfo.appId || ''});
            // 暂无可总结内容
            if (status === SummaryTriggeredStatus.Fail) {
                showToast(text);
            } else {
                resetToInitialValues(false);
                stopPolling();
                setTimeout(async () => {
                    await updateSummaries();
                    // 滚动到顶部
                    containerRef.current?.scrollTo(0, 0);
                    if (summaries.length > 0) {
                        const latestSummary = summaries[0];
                        if (latestSummary.summaryText === '' && latestSummary.summaryType === SummaryType.Auto) {
                            showToast('正在自动总结，请稍后再试');
                        } else {
                            startPolling();
                        }
                    }
                }, 100);
            }
        },
        [agentInfo.appId, containerRef, resetToInitialValues, startPolling, stopPolling, summaries, updateSummaries]
    );

    // 打开弹窗时初始化所有参数并加载
    const loadInitialSummary = useCallback(async () => {
        resetToInitialValues(true);
        await updateSummaries();
        pollingRef.current && startPolling();
    }, [resetToInitialValues, startPolling, updateSummaries]);

    // 整理数据
    useEffect(() => {
        const {dateToSummariesMap, dateList} = formatLTMSummaries(summaries, false);
        setDateToSummariesMap(dateToSummariesMap);
        setDateList(dateList);
    }, [summaries]);

    return {
        dateList,
        dateToSummariesMap,
        summaryProgressInfos,
        isLoading,
        loadInitialSummary,
        handleScroll,
        handleManualSummary,
        stopPolling,
        BottomHint,
        LTMSummaryProgressCmpt,
    };
};

export default useLTMList;
