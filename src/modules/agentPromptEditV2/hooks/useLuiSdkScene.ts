import {useEffect, useRef} from 'react';
import {LuiSdkScene} from '@/dicts';

/**
 * 区分编辑页的不同场景，用于区分预览对话的日志统计
 * @param configView
 * @param searchParams
 * @param appId
 */
export default function useLuiSdkScene(configView: boolean, searchParams: URLSearchParams, appId: string) {
    const editTypeRef = useRef(0);
    const createBy = searchParams.get('createBy') || '';

    // 区分编辑页的不同场景，用于区分预览对话的日志统计
    useEffect(() => {
        if (configView) {
            editTypeRef.current = LuiSdkScene.ViewConfig;
            return;
        }
        if (createBy === 'dup') {
            editTypeRef.current = LuiSdkScene.NewDuplicate;
            return;
        }
        if (appId) {
            editTypeRef.current = LuiSdkScene.Edit;
        } else {
            editTypeRef.current = LuiSdkScene.Create;
        }
        // 仅首次进页面时判断，不根据依赖变化而变化
        // eslint-disable-next-line
    }, []);

    return editTypeRef;
}
