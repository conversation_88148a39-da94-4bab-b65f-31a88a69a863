import React, {useCallback, useState} from 'react';
// import SkipModalComp from '@/modules/agentPromptEditV2/components/SkipModal/index';

export default function useSkipModal(
    controller: React.MutableRefObject<AbortController | undefined>,
    setLoading: (loading: boolean) => void,
    onConfirm: () => void
) {
    const [confirmOpen, setConfirmOpen] = useState(false);

    const waitForGen = useCallback(() => {
        setConfirmOpen(false);
    }, []);

    const cancelGen = useCallback(() => {
        setConfirmOpen(false);
        controller.current?.abort();
        setLoading(false);
        onConfirm();
    }, [controller, setLoading, onConfirm]);

    const skipModalProps = {
        confirmOpen,
        cancelGen,
        waitForGen,
    };

    return {
        setConfirmOpen,
        skipModalProps,
    };
}
