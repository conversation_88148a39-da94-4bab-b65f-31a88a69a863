import {useCallback, useState} from 'react';

const useEditHistory = <T>({
    initialHistory,
    onHistoryCurrentChange,
}: {
    initialHistory: T;
    onHistoryCurrentChange: (current: T) => void;
}) => {
    const [history, setHistory] = useState<{past: T[]; current: T; future: T[]}>({
        past: [],
        current: initialHistory,
        future: [],
    });

    const resetHistory = useCallback((initialHistory: T) => {
        setHistory({
            past: [],
            current: initialHistory,
            future: [],
        });
    }, []);

    const recordHistory = useCallback((current: T) => {
        setHistory(history => {
            if (history.current === current) {
                return history;
            }
            return {
                past: [...history.past, history.current],
                current,
                future: [],
            };
        });
    }, []);

    const undo = useCallback(() => {
        setHistory(history => {
            if (!history.past.length) {
                return history;
            }

            const current = history.past[history.past.length - 1];
            onHistoryCurrentChange(current);
            return {
                past: history.past.slice(0, -1),
                current,
                future: [...history.future, history.current],
            };
        });
    }, [onHistoryCurrentChange]);

    const redo = useCallback(() => {
        setHistory(history => {
            if (!history.future.length) {
                return history;
            }

            const current = history.future[history.future.length - 1];
            onHistoryCurrentChange(current);
            return {
                past: [...history.past, history.current],
                current,
                future: history.future.slice(0, -1),
            };
        });
    }, [onHistoryCurrentChange]);

    return {recordHistory, undo, redo, resetHistory, history, setHistory};
};

export default useEditHistory;
