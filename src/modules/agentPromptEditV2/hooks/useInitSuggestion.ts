/**
 * @file 获取自动追问提示词钩子
 * <AUTHOR>
 */

import {useEffect} from 'react';
import {FormInstance} from 'antd';
import {getDefaultSuggestionTemplate} from '@/api/agentEditV2';

export function useInitSuggestion(form: FormInstance) {
    useEffect(() => {
        (async () => {
            if (!form.getFieldValue(['agentJson', 'autoSuggestion', 'prompt'])) {
                const res = await getDefaultSuggestionTemplate();
                form.setFieldValue(['agentJson', 'autoSuggestion', 'prompt'], res.tpl);
            }
        })();
    }, [form]);
}
