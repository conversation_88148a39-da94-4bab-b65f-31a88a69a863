/**
 * @file 新版智能体编辑页面
 * <AUTHOR>
 */

import {useEffect, useState} from 'react';
import {useNavigate, useParams, useSearchParams} from 'react-router-dom';
import {ConfigProvider, Form} from 'antd';
import duplicateApi from '@/api/agentDuplicate';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import Loading from '@/components/Loading/LingJingLoading';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {usePromptViewConfigStore} from '@/store/agent/promptViewConfigStore';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import useLuiSdkScene from '@/modules/agentPromptEditV2/hooks/useLuiSdkScene';
import AgentWebSearchTip from '@/modules/agentPromptEditV2/mobile/components/AgentWebSearchTip';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {getFeatureAccessList} from '@/api/agentEditV2';
import {FeatureName} from '@/api/agentEditV2/interface';
import {getBjhSourceInfo} from '@/api/agentEdit';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import AudioSelect from '@/modules/agentPromptEditV2/mobile/components/AudioSelect/index';
import {PromptEditContext, isConfigView, isFromBaijiahao, usePromptEditContext} from '../utils';
import AgentForm from './components/AgentForm';
import Header from './Header';
import {PublishErrorModal, VersionCodeErrorModal} from './components/GlobalModal';
import Base from './Base';
import PublishButton from './components/PublishBtn';
import ProviderContext from './components/PublishBtn/Provider';
import DigitalFigure from './components/DigitalFigure';
import LongTermMemory from './components/LongTermMemory';
import DuplicateButton from './DuplicateButton';
import AutoSuggestion from './AutoSuggestion';
import WebSearch from './components/WebSearch';

function BaseLayout() {
    const {displayLog} = useUbcLogV3();
    const [searchParams] = useSearchParams();

    const {reset, fetchAgentConfig, initAllStatusDatasets} = usePromptEditStoreV2(store => ({
        reset: store.reset,
        fetchAgentConfig: store.fetchAgentConfig,
        initAllStatusDatasets: store.initAllStatusDatasets,
    }));

    const {setPublicAgentConfig} = usePromptViewConfigStore(store => ({
        setPublicAgentConfig: store.setPublicAgentConfig,
    }));

    const form = Form.useFormInstance<AgentConfigV2>();
    const {id} = useParams();
    const configView = isConfigView();

    useEffect(() => {
        if (!configView) {
            displayLog();
        }
    }, [displayLog, configView]);

    // 获取白名单权限
    const [setFeatureAccess] = useUserInfoStore(store => [store.setFeatureAccess]);

    useEffect(() => {
        async function init() {
            const figureAccess = await getFeatureAccessList({
                featureNameList: [FeatureName.Figure, FeatureName.RoleFramework],
            });
            setFeatureAccess(figureAccess);
        }

        init();
    }, [setFeatureAccess]);

    const [loading, setLoading] = useState(true);
    const appId = searchParams.get('appId') || '';
    const navigate = useNavigate();
    useEffect(() => {
        (async () => {
            if (configView && id) {
                // 作为配置查看页面打开时，设置 agentConfig
                const res = await duplicateApi.getAgentConf({appId: id});
                const config = {agentInfo: res.agentInfo, agentJson: res.agentJson} as unknown as AgentConfigV2;
                form.setFieldsValue(config);

                setPublicAgentConfig(res);
                setLoading(false);

                // 公开配置页面展现打点
                displayLog(EVENT_PAGE_CONST.AGENT_COPY_CONFIG, {agentId: id, agentName: res.agentInfo?.name});
            } else if (appId) {
                setLoading(true);
                await Promise.all([
                    fetchAgentConfig({appId, formInstance: form, isInit: true}),
                    initAllStatusDatasets().catch(),
                ]);
                setLoading(false);
            } else {
                setLoading(false);
            }
        })();

        return () => {
            reset();
        };
    }, [
        form,
        fetchAgentConfig,
        appId,
        navigate,
        configView,
        id,
        setPublicAgentConfig,
        displayLog,
        initAllStatusDatasets,
        reset,
    ]);

    //  URL 中有 agentSource =10001 、无 appId，则为百家号创建入口跳转过来的，首次渲染时需要调用getBjhSourceInfo来获取 来自百家号的头像和名称
    useEffect(() => {
        (async () => {
            if (isFromBaijiahao() && !appId) {
                const {logoUrl, name} = await getBjhSourceInfo();
                form.setFieldValue(['agentInfo', 'logoUrl'], logoUrl);
                form.setFieldValue(['agentInfo', 'name'], name);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const luiSdkScene = useLuiSdkScene(configView, searchParams, appId);

    if (loading) {
        return <Loading />;
    }

    return (
        <ProviderContext>
            {!configView && <AgentWebSearchTip />}
            <PublishErrorModal />
            <VersionCodeErrorModal />
            <Header luiSdkScene={luiSdkScene.current} />
            <Base />

            {/* 联网搜索 */}
            <WebSearch />

            {/* 引导追问 */}
            <CommonErrorBoundary pendingFallback={<Loading />}>
                <AutoSuggestion />
            </CommonErrorBoundary>

            <DigitalFigure />
            {/* 声音 */}
            <AudioSelect />
            {/* 快捷插件保留FormValue便于移动端更新不要漏掉参数 */}
            <Form.Item noStyle hidden name={['agentInfo', 'shortcuts']} />
            <LongTermMemory />
            {/* 移动端暂不支持商业化能力分发分成授权编辑，数据透传 */}
            <Form.Item name={['agentJson', 'businessComponent']} noStyle />
            <div className="mb-[43px] mt-[11px] px-[13px] leading-[23px] text-gray-tertiary">
                {!configView && (
                    <div>
                        知识库、插件、商业化能力仅支持在电脑端配置，请使用电脑进行编辑
                        <span>agents.baidu.com</span>
                    </div>
                )}
            </div>
            {configView ? <DuplicateButton /> : <PublishButton />}
        </ProviderContext>
    );
}

const BaseLayoutWithLogContext: typeof BaseLayout = () => {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId') || '';
    const {currentPageType, name, modeType} = usePromptEditStoreV2(store => ({
        currentPageType: store.currentPageType,
        name: store.agentConfig.agentInfo.name,
        modeType: store.agentConfig.agentInfo.modeType,
    }));

    return (
        <LogContextProvider
            ext={{agentId: appId, agentName: name, createPageType: currentPageType, pAgentModeType: modeType}}
            page={EVENT_PAGE_CONST.CODELESS_CREATE}
        >
            <BaseLayout />
        </LogContextProvider>
    );
};

export default function AgentPromptEditWise() {
    const userInfoData = useUserInfoStore(store => store.userInfoData);

    const contextData = usePromptEditContext();

    const readonly = isConfigView() || userInfoData?.hasTpProxy || false;

    contextData.readonly = readonly;

    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        fontSize: 16,
                        fontWeight: 600,
                    },
                    Input: {
                        colorBorder: 'transparent',
                        fontSize: 16,
                        colorErrorText: 'red',
                        colorTextPlaceholder: '#848691',
                    },
                },
            }}
        >
            <PromptEditContext.Provider value={contextData}>
                <AgentForm className="bg-gray-bg-base px-3 leading-none">
                    <CommonErrorBoundary>
                        <BaseLayoutWithLogContext />
                    </CommonErrorBoundary>
                </AgentForm>
            </PromptEditContext.Provider>
        </ConfigProvider>
    );
}
