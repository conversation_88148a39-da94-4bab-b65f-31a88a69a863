import {useC<PERSON>back, useEffect, useMemo, useState} from 'react';
import {TextAreaProps} from 'antd/es/input';
import Toast from 'antd-mobile/es/components/toast';
import PopupM, {PopupMProps} from '@/components/mobile/Popup';
import {auditText} from '@/api/audit';
import {useAIBuildHook} from '@/modules/agentPromptEditV2/hooks/useAbortableRequest';
import {SystemAssistant} from '@/api/agentEdit/interface';
import AIPending from '@/modules/agentPromptEditV2/components/AIPending';
import {ModalExpandType} from '@/modules/agentPromptEditV2/interface';
import {getThreeInOnePrompt} from '@/utils/agent-compatible';
import {getCountConfig} from '@/utils/text';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {SlotTextarea, TextAreaEvent} from '@/modules/agentPromptEditV2/components/Textarea';
import {InputPlaceholder} from '@/modules/agentPromptEditV2/pc/components/system/Normal';

const EditPopup = ({
    value,
    onChange,
    onClose,
    visible,
    expandType,
    ...props
}: {value?: string; onChange?: (v: string) => void; expandType: ModalExpandType} & PopupMProps & {
        inputProps?: TextAreaProps;
    }) => {
    const [text, setText] = useState('');

    const handleTextChange = useCallback((e: TextAreaEvent) => {
        setText(e.target.value);
    }, []);

    const {startGenerateResult, abortGenerateResult, building} = useAIBuildHook<Partial<SystemAssistant>>('system');
    const handleClose = useCallback(() => {
        abortGenerateResult();
        onClose && onClose();
    }, [abortGenerateResult, onClose]);
    const handleBuildSystem = useCallback(async () => {
        const res = await startGenerateResult();
        if (res) {
            setText(getThreeInOnePrompt(res.systemAssistant));
        }
    }, [startGenerateResult]);
    useEffect(() => {
        // 以生成态打开弹窗时，触发AI生成功能
        if (visible && expandType === ModalExpandType.BUILD) {
            handleBuildSystem();
        }
    }, [expandType, handleBuildSystem, visible]);

    const handleConfirm = useCallback(async () => {
        if (!text || !text.trim()) {
            return Toast.show('内容不能为空');
        }

        try {
            await auditText(text);
        } catch (error) {
            return Toast.show(typeof error === 'string' ? error : '验证失败');
        }

        onChange && onChange(text);
        onClose && onClose();
    }, [onChange, onClose, text]);

    useEffect(() => {
        if (visible) {
            setText(value || '');
        }
    }, [value, visible]);

    const {readonly} = usePromptEditContext();
    const countConfig: TextAreaProps['count'] = useMemo(
        () => ({
            ...getCountConfig(3000),
            show: () => (
                <span className="operation flex items-center gap-[15px]">
                    {!readonly && (
                        <div className="pointer-events-auto flex gap-[15px]" onClick={() => setText('')}>
                            清空
                        </div>
                    )}
                </span>
            ),
        }),
        [readonly]
    );

    return (
        <PopupM
            bodyStyle={{height: 'calc(100% - 54px)'}}
            onConfirm={handleConfirm}
            confirmButtonProps={{disabled: !text}}
            confirmText={expandType === ModalExpandType.BUILD ? '使用' : '确定'}
            cancelText="重新生成"
            onCancel={expandType === ModalExpandType.BUILD ? handleBuildSystem : undefined}
            showCloseButton
            onClose={handleClose}
            visible={visible}
            getContainer={null}
            {...props}
        >
            <SlotTextarea
                readonly={readonly}
                value={text}
                onValueChange={handleTextChange}
                count={countConfig}
                placeholder={InputPlaceholder}
                className="rounded-[9px] bg-colorBgFormList p-0 pb-9 pr-0 pt-2 
                [&_.editor-container]:h-[calc(100vh-250px)] 
                [&_.editor-container]:overflow-y-scroll  
                [&_.editor-container]:pr-3
                [&_.tiptap_p]:text-base
                [&_.tiptap_p]:leading-[28px]
                "
            />
            {building && (
                <div className="absolute left-0 top-10 h-[calc(100%-40px)] w-full">
                    <AIPending />
                </div>
            )}
        </PopupM>
    );
};

export default EditPopup;
