/**
 * @file 移动端人设指令 AI 生成弹窗
 * <AUTHOR>
 * @date 2024/08/21
 */

import {ChangeEvent, useCallback, useEffect} from 'react';
import styled from '@emotion/styled';
import {Button, Input} from 'antd';
import {css} from '@emotion/css';
import Popup from 'antd-mobile/es/components/popup';
import omit from 'lodash/omit';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import Scrollbar from '@/components/Scrollbar';
import {useAIBuildHook} from '@/modules/agentPromptEditV2/hooks/useAbortableRequest';
import {getCountConfigInteger, getStringLength} from '@/utils/text';
import AIPending from '@/modules/agentPromptEditV2/components/AIPending';
import {PRESET_SYSTEM} from '@/modules/agentPromptEditV2/utils';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {AIBuildConfig} from '@/api/buildConfig';

const StyledButton = styled(Button)`
    &.ant-btn.ant-btn-default:disabled {
        background-color: #bbc0fa;
        opacity: 1;
        color: #bbc0fa !important;
    }

    &.ant-btn.ant-btn-default {
        background-color: #ebecfd !important;
        border: none !important;
        color: #5562f2 !important;
    }
`;

const StyledPopupClass = css`
    .adm-popup-close-icon {
        right: 18px !important;
        top: 13px !important;
        font-size: 20px;
        color: #b7b9c1;
    }
`;

// 计算剩余字数
const remain = (modalValue: AIBuildSystem | undefined, name: string) =>
    Object.values(omit(modalValue, name)).reduce(
        (prev: number, field: any) => prev - getStringLength(field || ''),
        6000
    );

type AIBuildSystem = Partial<AIBuildConfig['systemAssistant']>;

export default function AIBuildSystemModal(props: {
    show: boolean;
    onClose: () => void;
    onChange?: (payload: AIBuildSystem) => void;
    saveHistory: () => void;
}) {
    const form = useFormInstance();
    const {generateResult, setGenerateResult, startGenerateResult, abortGenerateResult, building} =
        useAIBuildHook<AIBuildSystem>('system');

    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();

    const handleRebuildSystem = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.CHARSET_BOX_REGENERATE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        const res = await startGenerateResult();
        if (res) {
            setGenerateResult(res.systemAssistant);
        }
    }, [clickLog, extLog, setGenerateResult, startGenerateResult]);

    const handleUseConfigClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.CHARSET_BOX_USE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        props.onChange?.(generateResult as AIBuildSystem);
        props.saveHistory();
        props.onClose();
    }, [clickLog, extLog, generateResult, props]);

    const handleClickClose = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.CHARSET_BOX_CLOSE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        abortGenerateResult();
        props.onClose();
    }, [abortGenerateResult, clickLog, extLog, props]);

    useEffect(() => {
        if (props.show) {
            setGenerateResult(form.getFieldValue(['agentJson', 'systemAssistant']));
            handleRebuildSystem();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.show]);

    return (
        <Popup
            visible={props.show}
            bodyStyle={{
                borderTopLeftRadius: '21px',
                borderTopRightRadius: '21px',
                height: '90%',
                background: '#F5F6F9',
                display: 'flex',
                flexDirection: 'column',
            }}
            className={StyledPopupClass}
            showCloseButton
            onClose={handleClickClose}
        >
            <header>
                <div className="h-40px w-full flex-shrink-0 pb-[18px] pt-[13px] text-center text-[18px] font-semibold">
                    生成人物设定
                </div>
            </header>
            <Scrollbar
                className={
                    'relative mx-3 mb-6 flex-grow rounded-[12px] bg-white px-3 py-[18px] text-[#787B94] ' +
                    (building ? 'overflow-hidden' : 'overflow-y-auto')
                }
            >
                <div>
                    <span className="rounded-[3px] bg-gray-border-secondary p-[3px] text-[11px] font-medium leading-[11px]">
                        {PRESET_SYSTEM.instructions.label}
                    </span>
                </div>
                <Input.TextArea
                    className="mt-3 border-none p-0"
                    placeholder={PRESET_SYSTEM.instructions.value}
                    count={getCountConfigInteger(remain(generateResult, 'instructions'))}
                    autoSize
                    value={generateResult?.instructions || ''}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
                        setGenerateResult(prev => ({
                            ...prev,
                            instructions: event.target.value,
                        }))
                    }
                />
                <div className="mt-[18px]">
                    <span className="rounded-[3px] bg-gray-border-secondary p-[3px] text-[11px] font-medium leading-[11px] text-[#787B94]">
                        {PRESET_SYSTEM.thoughtInstructions.label}
                    </span>
                </div>
                <Input.TextArea
                    className="mt-3 border-none p-0"
                    placeholder={PRESET_SYSTEM.thoughtInstructions.value}
                    count={getCountConfigInteger(remain(generateResult, 'thoughtInstructions'))}
                    autoSize
                    value={generateResult?.thoughtInstructions || ''}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
                        setGenerateResult(prev => ({
                            ...prev,
                            thoughtInstructions: event.target.value,
                        }))
                    }
                />
                <div className="mt-[18px]">
                    <span className="rounded-[3px] bg-gray-border-secondary p-[3px] text-[11px] font-medium leading-[11px] text-[#787B94]">
                        {PRESET_SYSTEM.chatInstructions.label}
                    </span>
                </div>
                <Input.TextArea
                    className="mt-3 border-none p-0"
                    placeholder={PRESET_SYSTEM.chatInstructions.value}
                    count={getCountConfigInteger(remain(generateResult, 'chatInstructions'))}
                    value={generateResult?.chatInstructions || ''}
                    autoSize
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
                        setGenerateResult(prev => ({
                            ...prev,
                            chatInstructions: event.target.value,
                        }))
                    }
                />
                {building && (
                    <div className="absolute left-0 top-0 h-full w-full">
                        <AIPending />
                    </div>
                )}
            </Scrollbar>
            <footer className="mb-6 flex w-full px-[14px]">
                <StyledButton
                    disabled={building}
                    type="default"
                    key="cancel"
                    className="mr-2 h-[50px] flex-1 rounded-xl text-base font-medium"
                    onClick={handleRebuildSystem}
                >
                    重新生成
                </StyledButton>

                <StyledButton
                    key="confirm"
                    disabled={building}
                    type="primary"
                    className="h-[50px] flex-1 rounded-xl text-base font-medium"
                    onClick={handleUseConfigClick}
                >
                    使用
                </StyledButton>
            </footer>
        </Popup>
    );
}
