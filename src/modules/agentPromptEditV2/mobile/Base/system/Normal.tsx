/**
 * @file 智能体（原指令）部分
 * <AUTHOR>
 * 2023/03/16
 * 注意！此组件被  公开配置 - 查看配置页 复用，需要注意功能区分
 * isConfigView() 判断是否为配置页
 */
import {HTMLAttributes, useCallback, useEffect, useState} from 'react';
import {Form} from 'antd';
import classNames from 'classnames';
import Scrollbar from '@/components/Scrollbar';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import Icon from '@/components/Icon';
import {HtmlValidationMessage} from '@/components/Input/HtmlValidationMessage';
import useEditHistory from '@/modules/agentPromptEditV2/hooks/useEditHistory';
import {ModalExpandType} from '@/modules/agentPromptEditV2/interface';
import {useSystemButtonProps} from '@/modules/agentPromptEditV2/hooks/useAIBuildConfig';
import AIButton from '@/modules/agentPromptEditV2/components/AIButton';
import PopupM from '@/components/mobile/Popup';
import Example from '@/modules/agentPromptEditV2/components/Example';
import {SlotTextarea} from '@/modules/agentPromptEditV2/components/Textarea';
import EditPopup from './EditPopup';

const InputPlaceholder =
    '请详细描述智能体用途及希望智能体与用户交互的方式：比如他应该帮助用户解决什么问题、他应该以怎样的语气回答用户的问题、在回答的过程中他应该规避些什么事项等';
const useExample = () => {
    const [visible, setVisible] = useState(false);
    const handleClose = useCallback(() => {
        setVisible(false);
    }, []);
    const openExample = useCallback(() => {
        setVisible(true);
    }, []);
    return [
        openExample,
        <PopupM
            bodyStyle={{height: 'calc(100% - 54px)'}}
            visible={visible}
            showCloseButton
            onClose={handleClose}
            key="popup-example"
            title="人设与回复逻辑示例"
        >
            <div className="no-scrollbar h-full overflow-y-auto pb-10">
                <Example className="text-base" />
            </div>
        </PopupM>,
    ] as const;
};

const InputPreviewCard = ({
    value,
    label,
    required,
    suffix,
    className,
    onInput,
    readonly,
    ...props
}: {
    value?: string;
    label: React.ReactNode;
    suffix?: React.ReactNode;
    required?: boolean;
    onInput?: HTMLAttributes<HTMLDivElement>['onClick'];
    readonly?: boolean;
} & HTMLAttributes<HTMLDivElement>) => {
    return (
        <section
            className={classNames('rounded-[12px] bg-white px-[13px] py-[14px] leading-none', className)}
            {...props}
        >
            <header className="flex items-center pb-[9px]">
                <div className="flex-shrink-0 text-base font-semibold" key="label">
                    {label}
                </div>
                {required && (
                    <span
                        className="ml-1 mr-2 mt-1 font-semibold text-error"
                        style={{fontFamily: 'SimSong'}}
                        key="require"
                    >
                        *
                    </span>
                )}
                {suffix}
            </header>
            <Scrollbar
                className={classNames('h-60 overflow-y-auto whitespace-pre-wrap text-base', {
                    'text-[#848691]': !value,
                })}
                onClick={onInput}
            >
                <SlotTextarea
                    value={value || '请详细描述智能体的角色设定、目标任务、思考路径及个性化要求等'}
                    readonly
                    showHighlight={!readonly}
                    className={classNames('[&_.tiptap_p]:text-base [&_.tiptap_p]:leading-[28px]', {
                        '[&_.tiptap]:text-[#000311]': !readonly,
                    })}
                />
            </Scrollbar>
        </section>
    );
};

export default function FormSystem({
    promptSimilarity = 0,
    isTourOpen = false,
}: {
    promptSimilarity?: number;
    isTourOpen?: boolean;
}) {
    const {readonly} = usePromptEditContext();

    const form = Form.useFormInstance<AgentConfigV2>();

    const [visible, setVisible] = useState(false);
    const [expandType, setExpandType] = useState(ModalExpandType.EDIT);
    const openModal = useCallback((mode: ModalExpandType) => {
        setExpandType(mode);
        setVisible(true);
    }, []);

    const handleClose = useCallback(() => {
        setVisible(false);
    }, []);
    const handlePreviewClick = useCallback(() => {
        openModal(ModalExpandType.EDIT);
    }, [openModal]);
    const handleAiBuild = useCallback(() => {
        openModal(ModalExpandType.BUILD);
    }, [openModal]);

    const onHistoryCurrentChange = useCallback(
        (current: string) => {
            form.setFieldValue(['agentJson', 'system'], current);
        },
        [form]
    );
    const {resetHistory, undo, redo, history, recordHistory} = useEditHistory({
        initialHistory: '',
        onHistoryCurrentChange,
    });
    useEffect(() => {
        resetHistory(form.getFieldValue(['agentJson', 'system']) || '');
    }, [form, resetHistory]);

    const {systemAIButtonProps} = useSystemButtonProps();
    const [openExample, ExampleHolder] = useExample();

    return (
        <>
            <Form.Item name={['agentJson', 'system']} noStyle>
                <InputPreviewCard
                    onInput={handlePreviewClick}
                    readonly={readonly}
                    className="mt-3 pb-[23px]"
                    label="人设与回复逻辑"
                    required
                    suffix={
                        <div className="flex w-full items-center justify-between">
                            <Icon name="questionCircle" className="text-gray-quaternary" onClick={openExample} />
                            {!readonly && (
                                <div className="ml-auto flex items-center font-normal">
                                    <Icon name="revoke" onClick={undo} disabled={!history.past.length} />
                                    <Icon
                                        name="restore"
                                        className="ml-[15px]"
                                        onClick={redo}
                                        disabled={!history.future.length}
                                    />
                                    <AIButton {...systemAIButtonProps} onClick={handleAiBuild} className="" />
                                </div>
                            )}
                        </div>
                    }
                />
            </Form.Item>
            <Form.Item name={['agentJson', 'system']} noStyle>
                <EditPopup
                    title={expandType === ModalExpandType.BUILD ? '生成人设与回复逻辑' : '人设与回复逻辑'}
                    className="touch-auto"
                    visible={visible}
                    expandType={expandType}
                    onClose={handleClose}
                    onChange={recordHistory}
                    inputProps={{
                        required: true,
                        placeholder: InputPlaceholder,
                    }}
                />
            </Form.Item>
            <HtmlValidationMessage path={['agentJson', 'system']} />
            {ExampleHolder}
            {promptSimilarity >= 0.8 && !isTourOpen && (
                <div className="mt-[6px] text-sm text-[#FF8200]">
                    请修改人设与回复，与原智能体过度相似将影响流量效果及使用
                </div>
            )}
        </>
    );
}
