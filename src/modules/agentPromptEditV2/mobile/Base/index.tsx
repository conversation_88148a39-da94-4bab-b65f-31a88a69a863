import {useCallback, useState, useMemo} from 'react';
import {Form, ConfigProvider} from 'antd';
import {getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import Card from '@/components/mobile/Card';
import Logo from '@/modules/agentPromptEditV2/mobile/components/Logo';
import InputWithLabel from '@/components/mobile/InputWithLabel';
import AgentName from '@/components/mobile/AgentName';
import {getCountConfig} from '@/utils/text';
import {validateDatasetIds} from '@/modules/agentPromptEditV2/components/dataset/util';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {excludeOverviewType} from '@/modules/agentPromptEditV2/utils';
import {isFromBaijiahao, usePromptEditContext} from '../../utils';
import AIButton from '../../components/AIButton';
import {useWelcomeButtonProps} from '../../hooks/useAIBuildConfig';
import {useExtLog} from '../../hooks/useExtLog';
import {useTourContext} from '../../pc/context/TourContext';
import FormSystem from './system/Normal';
import Welcome from './welcome/index';
import Recommdends from './recommends';
import AIBuildWelcomePopup from './welcome/AIPopup';
import AIBuildRecommend from './recommends/AIBuildRecommend';

const AIBuildButton = ({onAIBuild}: {onAIBuild: () => void}) => {
    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();
    const {welcomeAIButtonProps} = useWelcomeButtonProps();
    const handleAIBuild = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PROLOGUE_GENERATE, EVENT_PAGE_CONST.CODELESS_CREATE, {
            ...extLog,
            [EVENT_EXT_KEY_CONST.PROLOGUE_GENERATE_TYPE]: welcomeAIButtonProps.log,
        });
        onAIBuild();
    }, [clickLog, extLog, onAIBuild, welcomeAIButtonProps.log]);
    return <AIButton {...welcomeAIButtonProps} onClick={handleAIBuild} />;
};

export default function Base() {
    const {ubcClickLog} = useUbcLog();

    const handleNameClick = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.NAME_INPUT);
    }, [ubcClickLog]);

    const handleIntroductionClick = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.INTRODUCTION_INPUT);
    }, [ubcClickLog]);

    const {allStatusDatasets} = usePromptEditStoreV2(store => ({
        allStatusDatasets: store.allStatusDatasets,
    }));

    const {readonly} = usePromptEditContext();

    const [welcomeBuild, setWelcomeBuild] = useState(false);
    const onAIBuild = useCallback(() => {
        setWelcomeBuild(true);
    }, []);

    const onWelcomeBuildClose = useCallback(() => {
        setWelcomeBuild(false);
    }, []);
    const {logoSimilarity, nameSimilarity, promptSimilarity, repeatType} = usePromptEditStoreV2(store => ({
        logoSimilarity: store.similarity.logoSimilarity,
        nameSimilarity: store.similarity.nameSimilarity,
        promptSimilarity: store.similarity.promptSimilarity,
        repeatType: store.similarity.repeatType,
    }));

    const {open: isTourOpen} = useTourContext().dup;

    const showOverviewTip = useMemo(() => {
        return !!repeatType && !excludeOverviewType.includes(repeatType);
    }, [repeatType]);

    return (
        <>
            <Form.Item noStyle name={['agentInfo', 'logoUrl']}>
                <Logo disabled={isFromBaijiahao()} logoSimilarity={logoSimilarity} isTourOpen={isTourOpen} />
            </Form.Item>

            <ConfigProvider
                theme={{
                    components: {
                        Input: {
                            colorBgContainerDisabled: 'transparent',
                            colorTextDisabled: '#848691',
                        },
                    },
                }}
            >
                {/* <Form.Item
                    name={['agentInfo', 'name']}
                    rules={[
                        {
                            required: true,
                            message: '请输入智能体名称',
                        },
                        getAuditSpaceRules('智能体名称', 'onBlur'),
                        getAuditTextRules('智能体名称', 'onBlur'),
                        getAgentNameRules('智能体名称', ['onBlur', 'onChange']),
                    ]}
                    validateTrigger={['onBlur', 'onChange']}
                    noStyle
                >
                    <InputWithLabel
                        disabled={isFromBaijiahao() || readonly}
                        label="名称"
                        count={getCountConfig(20, false)}
                        onClick={handleNameClick}
                        required
                        placeholder="起个响亮的名字吧～"
                    />
                </Form.Item> */}

                <AgentName
                    name={['agentInfo', 'name']}
                    disabled={isFromBaijiahao() || readonly}
                    onAgentNameClick={handleNameClick}
                    nameSimilarity={nameSimilarity}
                    repeatType={repeatType}
                    isTourOpen={isTourOpen}
                />

                <Form.Item
                    name={['agentInfo', 'overview']}
                    rules={[
                        {
                            required: true,
                            message: '请输入智能体简介',
                        },
                        getAuditSpaceRules('智能体简介', 'onBlur'),
                        getAuditTextRules('智能体简介', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="简介"
                        count={getCountConfig(50, false)}
                        onClick={handleIntroductionClick}
                        required
                        placeholder="简短的介绍"
                    />
                </Form.Item>

                {showOverviewTip && (
                    <div className="mt-[6px] text-sm text-[#FF8200]">
                        当前简介与其他智能体相同，可能影响流量效果及使用，建议修改
                    </div>
                )}

                <FormSystem promptSimilarity={promptSimilarity} isTourOpen={isTourOpen} />

                <Card
                    title="开场文案"
                    required
                    suffix={
                        <div className="text-right">
                            <AIBuildButton onAIBuild={onAIBuild} />
                        </div>
                    }
                >
                    <Welcome />
                </Card>

                <Card
                    title="开场白问题"
                    required
                    suffix={
                        <div className="text-right">
                            <Form.Item name={['agentInfo', 'recommends']} noStyle>
                                <AIBuildRecommend />
                            </Form.Item>
                        </div>
                    }
                >
                    <Recommdends />
                </Card>

                <Form.Item noStyle name={['agentJson']} />

                {/* 知识库（只校验不展示） */}
                <Form.Item
                    hidden
                    name={['agentJson', 'datasetIds']}
                    rules={[
                        {
                            validator(rule, value) {
                                return validateDatasetIds(value, allStatusDatasets);
                            },
                        },
                    ]}
                />

                <Form.Item name={['agentInfo', 'description']} noStyle>
                    <AIBuildWelcomePopup show={welcomeBuild} onClose={onWelcomeBuildClose} />
                </Form.Item>
            </ConfigProvider>
        </>
    );
}
