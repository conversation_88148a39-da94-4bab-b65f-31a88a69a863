/**
 * @file 开场白
 * <AUTHOR>
 * @date 2024/08/21
 */

import {Checkbox} from 'antd';
import {useCallback} from 'react';
import {getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
import {getCountConfig} from '@/utils/text';
import {StyledWelcomeTextarea} from '@/modules/agentPromptEditV2/mobile/components/StyledWelcomeTextarea';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {WelcomeFormItem} from '../../components/StyledFormItem';

export default function Welcome() {
    const {clickLog} = useUbcLogV3();

    const {readonly} = usePromptEditContext();

    const changeDynamicDescription = useCallback(
        (e: any) => {
            clickLog(EVENT_VALUE_CONST.DYNAMIC_DESCRIPTION_SWITCH, {
                isOpen: e.target.checked ? 1 : 0,
            });
        },
        [clickLog]
    );

    return (
        <>
            <WelcomeFormItem
                className="mb-0"
                name={['agentInfo', 'description']}
                rules={[
                    {
                        required: true,
                        message: '请输入开场文案',
                    },
                    getAuditSpaceRules('开场文案', 'onBlur'),
                    getAuditTextRules('开场文案', 'onBlur'),
                ]}
                validateTrigger={['onBlur']}
            >
                <StyledWelcomeTextarea
                    autoSize={{
                        maxRows: 4,
                    }}
                    className="px-0"
                    placeholder="活泼热情的开场文案可以吸引用户和智能体互动"
                    count={getCountConfig(200, false)}
                />
            </WelcomeFormItem>

            <WelcomeFormItem
                name={['agentInfo', 'dynamicDescription', 'isEnabled']}
                valuePropName="checked"
                className="mb-0 pt-2"
            >
                <Checkbox disabled={readonly} onChange={changeDynamicDescription}>
                    总结历史对话，对老用户动态生成开场文案
                </Checkbox>
            </WelcomeFormItem>
        </>
    );
}
