/**
 * @file 移动端 AI 生成开场白弹窗
 * <AUTHOR>
 * @date 2024/08/21
 */

import {ChangeEvent, useCallback, useEffect} from 'react';
import styled from '@emotion/styled';
import {Button, Input} from 'antd';
import {css} from '@emotion/css';
import Popup from 'antd-mobile/es/components/popup';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import Scrollbar from '@/components/Scrollbar';
import {useAIBuildHook} from '@/modules/agentPromptEditV2/hooks/useAbortableRequest';
import {getCountConfig} from '@/utils/text';
import AIPending from '@/modules/agentPromptEditV2/components/AIPending';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';

const StyledButton = styled(Button)`
    &.ant-btn.ant-btn-default:disabled {
        background-color: #bbc0fa;
        opacity: 1;
        color: #bbc0fa !important;
    }

    &.ant-btn.ant-btn-default {
        background-color: #ebecfd !important;
        border: none !important;
        color: #5562f2 !important;
    }
`;

const StyledPopupClass = css`
    .adm-popup-close-icon {
        right: 18px !important;
        top: 13px !important;
        font-size: 20px;
        color: #b7b9c1;
    }
`;

export default function AIBuildWelcomePopup(props: {
    show: boolean;
    onClose: () => void;
    onChange?: (payload: string) => void;
}) {
    const form = useFormInstance();
    const {generateResult, setGenerateResult, startGenerateResult, abortGenerateResult, building} =
        useAIBuildHook<string>('welcome');

    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();
    const handleUseConfigClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PROLOGUE_BOX_USE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        props.onChange?.(generateResult || '');
        form.validateFields([['agentInfo', 'description']]);
        props.onClose();
    }, [clickLog, extLog, form, generateResult, props]);

    const handleRebuildWelcome = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.PROLOGUE_BOX_REGENERATE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        const res = await startGenerateResult();
        if (res) {
            setGenerateResult(res.description);
        }
    }, [clickLog, extLog, setGenerateResult, startGenerateResult]);

    const handleClickClose = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PROLOGUE_BOX_CLOSE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        abortGenerateResult();
        props.onClose();
    }, [abortGenerateResult, clickLog, extLog, props]);

    useEffect(() => {
        if (props.show) {
            setGenerateResult(form.getFieldValue(['agentInfo', 'description']));
            handleRebuildWelcome();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.show]);

    const onResultChange = useCallback(
        (event: ChangeEvent<HTMLTextAreaElement>) => setGenerateResult(event.target.value),
        [setGenerateResult]
    );

    return (
        <Popup
            visible={props.show}
            bodyStyle={{
                borderTopLeftRadius: '21px',
                borderTopRightRadius: '21px',
                background: '#F5F6F9',
                height: '90%',
                display: 'flex',
                flexDirection: 'column',
            }}
            className={StyledPopupClass}
            showCloseButton
            onClose={handleClickClose}
        >
            <header>
                <div className="h-40px w-full flex-shrink-0 pb-[18px] pt-[13px] text-center text-[18px] font-semibold">
                    生成开场文案
                </div>
            </header>
            <Scrollbar
                className={
                    'relative mx-3 mb-6 flex-grow rounded-[12px] bg-white px-3 py-[18px] text-[#787B94] ' +
                    (building ? 'overflow-hidden' : 'overflow-y-auto')
                }
            >
                <Input.TextArea
                    className="h-full border-none p-0"
                    placeholder="活泼热情的开场文案可以吸引用户和智能体互动"
                    count={getCountConfig(200, false)}
                    autoSize
                    value={generateResult}
                    onChange={onResultChange}
                />
                {building && (
                    <div className="absolute left-0 top-0 h-full w-full">
                        <AIPending />
                    </div>
                )}
            </Scrollbar>
            <footer className="mb-6 flex w-full px-[14px]">
                <StyledButton
                    disabled={building}
                    type="default"
                    key="cancel"
                    className="mr-2 h-[50px] flex-1 rounded-xl text-base font-medium"
                    onClick={handleRebuildWelcome}
                >
                    重新生成
                </StyledButton>

                <StyledButton
                    key="confirm"
                    disabled={building}
                    type="primary"
                    className="h-[50px] flex-1 rounded-xl text-base font-medium"
                    onClick={handleUseConfigClick}
                >
                    使用
                </StyledButton>
            </footer>
        </Popup>
    );
}
