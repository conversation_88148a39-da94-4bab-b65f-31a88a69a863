/**
 * @file agent1.1 引导示例-普通
 * <AUTHOR>
 * 2024/03/16
 */

import {Form} from 'antd';
import {getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import AIPending from '@/modules/agentPromptEditV2/components/AIPending';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {RecommendFormItem} from '../../components/StyledFormItem';
import OrdinaryInput from './OrdinaryInput';

export default function Ordinary() {
    const {ubcClickLog} = useUbcLog();

    const {clickLog} = useUbcLogV3();

    const recommends = Form.useWatch(['agentInfo', 'recommends']) || [];

    const {readonly} = usePromptEditContext();

    const {recommendBuilding} = usePromptEditStoreV2(store => ({
        recommendBuilding: store.recommendBuilding,
    }));

    return (
        <div className="relative">
            <Form.List
                name={['agentInfo', 'recommends']}
                rules={[
                    {
                        async validator(rule, value) {
                            if (value.length < 3) {
                                return '至少填写三个开场白问题';
                            } else if (value.length > 6) {
                                return '最多填写六个开场白问题';
                            }
                        },
                    },
                ]}
            >
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(field => (
                            <RecommendFormItem
                                key={field.name}
                                className="mb-[9px]"
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入引导内容',
                                    },
                                    getAuditSpaceRules('开场白问题', 'onBlur'),
                                    getAuditTextRules('开场白问题', 'onBlur'),
                                ]}
                                name={field.name}
                                validateTrigger={['onBlur']}
                            >
                                <OrdinaryInput
                                    // eslint-disable-next-line react/jsx-no-bind
                                    remove={() => {
                                        clickLog(EVENT_VALUE_CONST.DESCRIPTION_DELETE_QUESTION);
                                        remove(field.name);
                                    }}
                                    field={field}
                                />
                            </RecommendFormItem>
                        ))}
                        {!readonly && recommends.length < 6 && (
                            <div className="mt-[15px] text-center">
                                <div
                                    className="inline-block rounded-[9px] bg-[#EBECFD] px-[18px] py-[11px] font-semibold leading-none text-primary active:opacity-20"
                                    // eslint-disable-next-line react/jsx-no-bind
                                    onClick={() => {
                                        add('');
                                        ubcClickLog(DIGITAL_LOG_CONSTANTS.NEW_RECOMMEND_ICON, {
                                            guide: 1,
                                            [EVENT_EXT_KEY_CONST.GUIDE_NUM]: recommends.length,
                                        });
                                        clickLog(EVENT_VALUE_CONST.DESCRIPTION_ADD_QUESTION);
                                    }}
                                >
                                    添加问题
                                </div>
                            </div>
                        )}
                    </>
                )}
            </Form.List>
            {recommendBuilding && (
                <div className="absolute left-0 top-0 h-full w-full">
                    <AIPending />
                </div>
            )}
        </div>
    );
}
