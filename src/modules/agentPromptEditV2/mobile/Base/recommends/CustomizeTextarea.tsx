import {FormListFieldData, Row} from 'antd';
import {useCallback} from 'react';
import {TextAreaProps} from 'antd/es/input';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {StyledTextareaWise} from '@/components/mobile/InputWithLabel';
import {getCountConfig} from '@/utils/text';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';

interface OrdinaryInputProps extends TextAreaProps {
    remove: (name: number) => void;
    field: FormListFieldData;
    showRemove: boolean;
}

export default function CustomizeTextarea(props: OrdinaryInputProps) {
    const {agentConfig} = usePromptEditStoreV2(store => ({
        agentConfig: store.agentConfig,
    }));
    const {ubcClickLog} = useUbcLog();

    const handleInputClick = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.RECOMMEND_INPUT, {
            guide: 1,
            [EVENT_EXT_KEY_CONST.GUIDE_NUM]: agentConfig.agentInfo.recommends.length,
        });
    }, [agentConfig.agentInfo.recommends.length, ubcClickLog]);

    const handleRemove = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.DELETE_CLICK, {
            guide: 1,
            [EVENT_EXT_KEY_CONST.GUIDE_NUM]: agentConfig.agentInfo.recommends.length,
        });
        props.remove(props.field.name);
    }, [agentConfig.agentInfo.recommends.length, props, ubcClickLog]);

    const {readonly} = usePromptEditContext();

    return (
        <Row align={'middle'}>
            <StyledTextareaWise
                onChange={props.onChange}
                onBlur={props.onBlur}
                className="w-0 flex-grow"
                placeholder="当用户于欢迎气泡内点击该问题时，将按照您预置的答案完成回答"
                onClick={handleInputClick}
                value={props.value}
                autoSize={{
                    minRows: 2,
                    maxRows: 2,
                }}
                count={getCountConfig(500, false)}
            />
            {props.showRemove && !readonly && (
                <Row
                    className="ml-2 h-[40px] w-[40px] flex-shrink-0 rounded-[9px] bg-gray-bg-base"
                    align={'middle'}
                    justify={'center'}
                    onClick={handleRemove}
                >
                    <div className="iconfont icon-close text-gray-tertiary active:opacity-20" />
                </Row>
            )}
        </Row>
    );
}
