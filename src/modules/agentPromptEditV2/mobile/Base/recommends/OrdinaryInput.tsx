import {FormListFieldData, Input, InputProps, Row} from 'antd';
import {useCallback} from 'react';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {getCountConfig} from '@/utils/text';

interface OrdinaryInputProps extends InputProps {
    remove: (name: number) => void;
    field: FormListFieldData;
}

export default function OrdinaryInput(props: OrdinaryInputProps) {
    const {agentConfig} = usePromptEditStoreV2(store => ({
        agentConfig: store.agentConfig,
    }));
    const {ubcClickLog} = useUbcLog();

    const handleInputClick = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.RECOMMEND_INPUT, {
            guide: 1,
            [EVENT_EXT_KEY_CONST.GUIDE_NUM]: agentConfig.agentInfo.recommends.length,
        });
    }, [agentConfig.agentInfo.recommends.length, ubcClickLog]);

    const handleRemove = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.DELETE_CLICK, {
            guide: 1,
            [EVENT_EXT_KEY_CONST.GUIDE_NUM]: agentConfig.agentInfo.recommends.length,
        });
        props.remove(props.field.name);
    }, [agentConfig.agentInfo.recommends.length, props, ubcClickLog]);

    return (
        <Row align={'middle'}>
            <Input
                onChange={props.onChange}
                onBlur={props.onBlur}
                value={props.value}
                className="w-0 flex-grow border-none"
                placeholder="欢迎气泡内的推荐问题"
                count={getCountConfig(50, false)}
                onClick={handleInputClick}
            />
            {agentConfig.agentInfo.recommends.length > 3 && (
                <Row
                    className="ml-2 h-[38px] w-[38px] flex-shrink-0 rounded-[9px] bg-gray-bg-base"
                    align={'middle'}
                    justify={'center'}
                    onClick={handleRemove}
                >
                    <div className="iconfont icon-close text-gray-tertiary active:opacity-20" />
                </Row>
            )}
        </Row>
    );
}
