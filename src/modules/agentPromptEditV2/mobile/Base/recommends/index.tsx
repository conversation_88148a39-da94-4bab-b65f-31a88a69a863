/**
 * @file agent1.1 引导示例入口
 * <AUTHOR>
 * 2024/03/16
 */

import {ConfigProvider} from 'antd';
import Ordinary from './Ordinary';

export default function Recommdends() {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        borderRadius: 12,
                    },
                    Input: {
                        colorBgContainer: '#F5F6F9',
                        colorBgContainerDisabled: '#F5F6F9',
                        paddingBlock: 10,
                        lineHeight: 1,
                        colorTextPlaceholder: '#848691',
                        colorTextDisabled: '#848691',
                    },
                },
            }}
        >
            <Ordinary />
        </ConfigProvider>
    );
}
