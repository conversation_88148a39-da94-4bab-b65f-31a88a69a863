import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import {useCallback, useState} from 'react';
import {useAIBuildHook} from '@/modules/agentPromptEditV2/hooks/useAbortableRequest';
import AIButton from '@/modules/agentPromptEditV2/components/AIButton';
import {useRecommendButtonProps} from '@/modules/agentPromptEditV2/hooks/useAIBuildConfig';
import ModalM from '@/components/mobile/Modal';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';

export default function AIBuildRecommend(props: {onChange?: (payload: string[]) => void}) {
    const form = useFormInstance();
    const {recommendAIButtonProps} = useRecommendButtonProps();
    const {startGenerateResult, abortGenerateResult} = useAIBuildHook<string[]>('recommend');

    const [showModal, setShowModal] = useState(false);
    const {clickLog, showLog} = useUbcLogV2();
    const extLog = useExtLog();

    const handleBuildRecommend = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.GUIDE_EXAMPLE_GENERATE, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        if (form.getFieldValue(['agentInfo', 'recommends']).some((recommend: string) => recommend?.trim() !== '')) {
            showLog(EVENT_VALUE_CONST.GUIDEEX_REPLACE_BOX, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
            setShowModal(true);
        } else {
            const res = await startGenerateResult();
            if (res) {
                props.onChange?.(res?.recommends);
                form.validateFields([['agentInfo', 'recommends']], {recursive: true});
            }
        }
    }, [clickLog, extLog, form, props, showLog, startGenerateResult]);

    const onOk = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.GUIDEEX_REPLBOX_CONFIRM, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        setShowModal(false);
        const res = await startGenerateResult();
        if (res) {
            props.onChange?.(res?.recommends);
            form.validateFields([['agentInfo', 'recommends']], {recursive: true});
        }
    }, [clickLog, extLog, form, props, startGenerateResult]);

    const onCancel = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.GUIDEEX_REPLBOX_CANCEL, EVENT_PAGE_CONST.CODELESS_CREATE, extLog);
        abortGenerateResult();
        setShowModal(false);
    }, [abortGenerateResult, clickLog, extLog]);

    return (
        <>
            <AIButton {...recommendAIButtonProps} onClick={handleBuildRecommend} />
            <ModalM okText="确认" cancelText="取消" open={showModal} onOk={onOk} onCancel={onCancel}>
                <div className="text-center">
                    请确认是否使用AI生成的内容，替换当
                    <br />
                    前开场白问题
                </div>
            </ModalM>
        </>
    );
}
