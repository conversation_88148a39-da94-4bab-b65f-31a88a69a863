import {useCallback, useState} from 'react';
import {ConfigProvider, Radio, Row} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import PopupM from '@/components/mobile/Popup';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {ViewAgentPermission} from '@/store/agent/utils';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import {usePublishAction} from '../hooks/usePublishAction';

export default function PublishButton() {
    const [popupShow, setPopupShow] = useState(false);

    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const allowCreate = !userInfoData?.hasTpProxy;
    const isRoleFramework = useUserInfoStore(store => store.userFeatureAccess.role_framework);

    const {canPublishResult, publishLoading} = usePromptEditStoreV2(store => ({
        canPublishResult: store.canPublish({isRoleFramework}),
        publishLoading: store.publishLoading,
    }));

    const publishBtnEnabled = canPublishResult.canPublish && allowCreate;

    const handlePublishClick = useCallback(() => {
        if (!allowCreate) {
            showDisallowOperateToast();
            return;
        }

        if (canPublishResult.canPublish) {
            setPopupShow(true);
        } else {
            Toast.show({content: canPublishResult.reason});
        }
    }, [allowCreate, canPublishResult.canPublish, canPublishResult.reason]);

    const handleWisePopupClose = useCallback(() => {
        setPopupShow(false);
    }, []);

    const {handlePublish, viewPermission, setViewPermission, canPublishAfterEdit} = usePublishAction(setPopupShow);
    const options = [
        {
            auth: ViewAgentPermission.SHARE_CONFIG,
            label: '公开访问与配置',
        },
        {
            auth: ViewAgentPermission.PUBLIC,
            label: '公开访问',
        },
        {
            auth: ViewAgentPermission.LINK,
            label: '仅链接可访问',
        },
        {
            auth: ViewAgentPermission.PRIVATE,
            label: '仅自己可访问 (免审)',
        },
    ];

    return (
        <ConfigProvider
            theme={{
                components: {
                    Radio: {
                        colorBorder: '#B7B9C1',
                    },
                },
            }}
        >
            <div className="sticky bottom-6 z-10 flex text-center">
                <div
                    className={`mx-[14px] flex-grow rounded-[12px] py-4 text-[18px] font-semibold text-white ${
                        publishBtnEnabled ? 'bg-primary active:bg-[#DDE0FC]' : 'bg-[#bbc0fa]'
                    }`}
                    onClick={handlePublishClick}
                >
                    发布智能体
                </div>
                <PopupM
                    title="发布权限"
                    visible={popupShow}
                    background="#F5F6F9"
                    confirmText="确认发布"
                    suffix={
                        <span
                            className="iconfont icon-close mr-[18px] text-[20px] text-gray-quaternary"
                            onClick={handleWisePopupClose}
                        ></span>
                    }
                    confirmButtonProps={{
                        loading: publishLoading,
                        style: {
                            background: canPublishAfterEdit ? '' : 'rgba(85, 98, 242, 0.3)',
                        },
                    }}
                    onConfirm={handlePublish}
                    onClose={handleWisePopupClose}
                >
                    {options.map(option => (
                        <Row
                            key={option.auth}
                            className="mb-2 w-full rounded-[12px] bg-white px-[13px] py-[15px] text-base "
                            justify={'space-between'}
                            align={'middle'}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => setViewPermission(option.auth)}
                        >
                            <div className="font-semibold">{option.label}</div>
                            <Radio className="me-0" checked={viewPermission === option.auth} />
                            {viewPermission === ViewAgentPermission.SHARE_CONFIG &&
                                option.auth === ViewAgentPermission.SHARE_CONFIG && (
                                    <div className="mt-1.5 text-sm text-gray-tertiary">
                                        公开配置后，其他开发者可以查看和复制智能体非私有资源配置。智能体会额外在体验中心的“公开配置”中分发，有机会获得更多流量哦~
                                        <p>私有资源包括知识库、私有插件等</p>
                                    </div>
                                )}
                        </Row>
                    ))}
                    <div className="h-[24px]" />
                </PopupM>
            </div>
        </ConfigProvider>
    );
}
