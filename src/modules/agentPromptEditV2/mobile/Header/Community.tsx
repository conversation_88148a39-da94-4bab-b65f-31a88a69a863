/**
 * @file 智能客服 icon 组件
 * <AUTHOR>
 */
import classNames from 'classnames';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import Tippy from '@tippyjs/react';
import api from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import CommunityModal from '@/components/mobile/CommunityModal';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {LJExtData} from '@/utils/loggerV2/utils';

const POPOVER_AUTO_OPEN_TIME = 2 * 60 * 1000;
const POPOVER_CLOSE_TIME = 10 * 1000;

export default function Community({className = ''}) {
    const [showPopover, setShowPopover] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const autoPopoverTimeoutRef = useRef<NodeJS.Timeout>();
    const closePopoverTimeoutRef = useRef<NodeJS.Timeout>();
    const {readonly} = usePromptEditContext();
    const {showLog} = useUbcLogV3();
    const extLog = useExtLog();

    const closePopover = useCallback(() => {
        api.recordPopup({name: PopupName.MobileCommunityBubble});
        setShowPopover(false);
    }, []);

    const openModal = useCallback(() => {
        showLog(EVENT_VALUE_CONST.MOBILE_COMMUNITY_MODAL, extLog as LJExtData, EVENT_PAGE_CONST.CODELESS_CREATE);
        setShowModal(true);
        closePopover();
    }, [closePopover, showLog, extLog]);

    const closeModal = useCallback(() => {
        setShowModal(false);
    }, []);

    useEffect(() => {
        if (readonly) {
            return;
        }

        api.getPopup({name: PopupName.MobileCommunityBubble}).then(({show}) => {
            if (!show) {
                return;
            }

            clearTimeout(autoPopoverTimeoutRef.current);
            autoPopoverTimeoutRef.current = setTimeout(() => {
                setShowPopover(true);
                showLog(
                    EVENT_VALUE_CONST.MOBILE_COMMUNITY_BUBBLE,
                    extLog as LJExtData,
                    EVENT_PAGE_CONST.CODELESS_CREATE
                );

                closePopoverTimeoutRef.current = setTimeout(closePopover, POPOVER_CLOSE_TIME);
            }, POPOVER_AUTO_OPEN_TIME);
        });

        return () => {
            clearTimeout(autoPopoverTimeoutRef.current);
            clearTimeout(closePopoverTimeoutRef.current);
        };
    }, [closePopover, readonly, extLog, showLog]);

    return (
        <>
            <Tippy
                offset={[30, 8]}
                visible={showPopover}
                interactive
                theme="mobile-community-tooltip"
                onClickOutside={closePopover}
                placement="bottom-end"
                zIndex={99}
                content={
                    <div className="font-['PingFang SC'] inline-flex max-w-[302px] justify-start gap-2 rounded-lg bg-gradient-to-b from-[#4352FF] to-[#7F8AFF] py-2.5 pl-3.5 pr-3 text-sm leading-[22px] text-white">
                        <span>创建遇到困难了吗？扫码加入官方社群，即刻获取专业解答～</span>
                        <span
                            className="iconfont icon-close cursor-pointer text-sm leading-[22px] text-[#FFFFFFB2]"
                            onClick={closePopover}
                        />
                    </div>
                }
            >
                <span className={classNames('iconfont icon-community2', className)} onClick={openModal} />
            </Tippy>

            <CommunityModal visible={showModal} onCloseBtnClick={closeModal} onMaskClick={closeModal} showBottomTip />
        </>
    );
}
