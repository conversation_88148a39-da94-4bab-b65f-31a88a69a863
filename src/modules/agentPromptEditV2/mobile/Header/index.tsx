import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import {message} from 'antd';
import {LuiSdkScene} from '@/dicts';
import urls from '@/links';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentTabType} from '@/modules/agentList/interface';
import PreviewContainer from '@/modules/agentPromptEditV2/components/PreviewContainer/index';
import PopupM from '@/components/mobile/Popup';
import {Variant} from '@/components/mobile/Popup/constants';
import useSafeBack from '@/utils/useSafeBack';
import Community from '@/modules/agentPromptEditV2/mobile/Header/Community';
import {AgentStatusLabel, serverToClientLabel} from '@/api/agentList/interface';
import {isConfigView, usePromptEditContext} from '../../utils';
import {usePublishContext} from '../components/PublishBtn/context';

const getChatViewConfig = (readonly: boolean) => {
    return {
        // 按照 wise 样式每行欢迎语占一行
        narrow: true,
        // 顶部导航栏隐藏分享和下拉菜单
        nav: {
            type: 'top' as const,
            menu: false,
            hide: true,
            businessCard: false,
            previewTitle: readonly ? '预览' : '预览调试',
        },
        // 隐藏脚注
        footer: false,
        // 隐藏背景水印
        watermark: false,
        // 开场白配置
        welcome: {
            defaultDescription: '请在配置页面中填写开场白进行预览~',
        },
        coverCard: true,
    };
};

const BackBtn = (props: React.HTMLAttributes<HTMLDivElement>) => {
    const navigate = useNavigate();
    const {hasChangeForm, displayStatus} = usePromptEditStoreV2(store => ({
        hasChangeForm: store.hasChangeForm,
        displayStatus: store.display.status,
    }));

    const [showPublishConfirm, setShowPublishConfirm] = useState(false);

    const handleCloseConfirm = useCallback(() => setShowPublishConfirm(false), []);

    const {handlePublishClick, publishEnabled} = usePublishContext();
    const safeBack = useSafeBack(urls.agentList.fill({tab: AgentTabType.Codeless}));
    const handleBack = useCallback(() => {
        if (hasChangeForm) {
            return navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
        }

        safeBack();
    }, [hasChangeForm, navigate, safeBack]);

    const handleBackClick = useCallback(() => {
        if (
            hasChangeForm &&
            publishEnabled &&
            [AgentStatusLabel.PUBLISHED, AgentStatusLabel.ONLINE].includes(serverToClientLabel(displayStatus))
        ) {
            return setShowPublishConfirm(true);
        }

        safeBack();
    }, [displayStatus, hasChangeForm, publishEnabled, safeBack]);

    return (
        <div {...props}>
            <span className="iconfont icon-left text-[24px]" onClick={handleBackClick}></span>
            <PopupM
                visible={showPublishConfirm}
                onClose={handleCloseConfirm}
                onCancel={handleBack}
                cancelText="取消"
                onConfirm={handlePublishClick}
                confirmText="发布"
                title="退出编辑"
                bodyStyle={{
                    minHeight: '0',
                }}
                onMaskClick={handleCloseConfirm}
            >
                <div className="mb-6 px-[14px] text-base">
                    智能体配置内容有更新，需再次发布后生效。请确认是否需要发布？
                </div>
            </PopupM>
        </div>
    );
};

export default function Header({luiSdkScene}: {luiSdkScene: LuiSdkScene}) {
    const {appId, canPublishResult} = usePromptEditStoreV2(store => ({
        appId: store.agentConfig?.agentInfo?.appId,
        canPublishResult: store.canPublish({}),
    }));
    const [previewVisible, setPreviewVisible] = useState(false);

    const configView = isConfigView();
    const {readonly} = usePromptEditContext();

    // 预览按钮是否可用
    const previewEnabled = appId && canPublishResult?.canPublish;

    const openPreview = useCallback(() => {
        if (configView) {
            setPreviewVisible(true);
            return;
        }

        if (!appId) {
            message.error('请先填写内容创建智能体');
            return;
        }

        if (!canPublishResult.canPublish) {
            message.error(canPublishResult.reason);
            return;
        }

        setPreviewVisible(true);
    }, [setPreviewVisible, canPublishResult?.canPublish, canPublishResult?.reason, appId, configView]);

    const closePreview = useCallback(() => {
        setPreviewVisible(false);
    }, [setPreviewVisible]);

    return (
        <header className="sticky top-0 z-[11] flex items-center bg-gray-bg-base py-4">
            {/* 返回按钮 */}
            <BackBtn className="w-1/3" />

            {/* 标题 */}
            <div className="w-1/3 text-center text-[18px] font-semibold">
                {configView ? '查看智能体' : '创建智能体'}
            </div>

            <div className="flex w-1/3 flex-row items-center justify-end gap-1">
                {/* 社群 */}
                <Community className="mr-2" />

                {/* 预览按钮 */}
                <div
                    className={classNames('flex items-center gap-1 font-semibold text-[#bbc0fa]', {
                        'text-primary active:opacity-20': previewEnabled || configView,
                    })}
                    onClick={openPreview}
                >
                    <span className="iconfont icon-preview text-xs"></span>
                    <div className="text-base">预览</div>
                </div>
            </div>
            <PopupM
                visible={previewVisible}
                onClose={closePreview}
                variant={Variant.Fullscreen}
                bodyStyle={{
                    paddingLeft: '0px',
                    paddingRight: '0px',
                }}
            >
                <PreviewContainer
                    luiSdkScene={luiSdkScene}
                    platform="wise"
                    chatViewConfig={getChatViewConfig(readonly)}
                    onClose={closePreview}
                />
            </PopupM>
        </header>
    );
}
