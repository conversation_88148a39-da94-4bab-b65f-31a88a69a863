/**
 * @file 快速开始组件，不是一个新的路由，而是一个全屏组件
 * <AUTHOR>
 */

import {Button, ConfigProvider, Form, message} from 'antd';
import React, {useCallback, useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {useForm, useWatch} from 'antd/es/form/Form';
import PopupM from '@/components/mobile/Popup/index';
import SkipModalComp from '@/modules/agentPromptEditV2/mobile/components/SkipModal';
import useQuickStart, {getAgentEditUrl, mergeAgentConfig} from '@/modules/agentPromptEditV2/hooks/useQuickStart';
import InputWithLabel from '@/components/mobile/InputWithLabel';
import {getAgentNameRules, getAuditTextRules} from '@/api/audit';
import {generateAgentConfigV2} from '@/api/agentEditV2';
import loadingAnimation from '@/assets/loading-animation.gif';
import {useLoadingTimer} from '@/modules/agentPromptEditV2/hooks/useLoadingTimer';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {checkAgentNameRules} from '@/modules/agentPromptEditV2/utils';
import {createAgent} from '@/api/agentEdit';
import Loading from '@/components/Loading/LingJingLoading';
import {usePreLoadPage} from '@/context';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';

function QuickStart() {
    const {loading, setLoading, skipModalProps, handleSkip, controller, createType, setCreateType} = useQuickStart();

    const {displayLog} = useUbcLogV3();
    const {ubcClickLog} = useUbcLog();

    const [loadingText, {startTimer, cancelTimer}] = useLoadingTimer();

    const [quickStartForm] = useForm<{
        name: string;
        introduction: string;
    }>();

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    const navigate = useNavigate();
    const introduction = useWatch(['introduction'], quickStartForm);
    const handleConfirm = useCallback(async () => {
        try {
            ubcClickLog(EVENT_TRACKING_CONST.quickStartGen);
            startTimer();
            setLoading(true);
            setCreateType('gen');

            const res = await quickStartForm.validateFields();
            if (!res) {
                return;
            }

            controller.current = new AbortController();
            const generateRes = await generateAgentConfigV2({introduction}, controller.current.signal);

            const values = quickStartForm.getFieldsValue();
            const {appId} = await createAgent(
                mergeAgentConfig({...generateRes, name: values.name || generateRes.name})
            );
            if (!appId) return;

            navigate(getAgentEditUrl(appId), {replace: true});
        } catch (e: any) {
            message.error(e?.msg || '创建失败');
            console.error('创建失败', e);
        } finally {
            setLoading(false);
        }
    }, [controller, introduction, navigate, quickStartForm, setCreateType, setLoading, startTimer, ubcClickLog]);

    useEffect(() => {
        return () => cancelTimer();
    }, [cancelTimer]);

    const nameValue = useWatch(['name'], quickStartForm);

    const preLoad = usePreLoadPage();
    useEffect(() => {
        preLoad('AgentPromptEdit');
    }, [preLoad]);

    if (loading && createType === 'empty') {
        return <Loading zIndex={49} />;
    }

    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: {
                        colorBgContainerDisabled: '#fff !important',
                        colorTextDisabled: '#B7B9C1',
                    },
                },
            }}
        >
            <PopupM
                visible
                bodyStyle={{height: '90%'}}
                background="#F5F6F9"
                title="👏 快速创建智能体"
                suffix={
                    <Button
                        type="text"
                        className="-mt-[2px] text-base font-medium text-gray-secondary active:opacity-20"
                        onClick={handleSkip}
                    >
                        跳过
                    </Button>
                }
                getContainer={null}
                confirmText={loading ? '生成中' : '一键生成配置'}
                disabled={!introduction || (nameValue ? !checkAgentNameRules(nameValue) : false) || loading}
                onConfirm={handleConfirm}
            >
                <Form
                    form={quickStartForm}
                    initialValues={{
                        name: '',
                        introduction: '',
                    }}
                    className="relative"
                >
                    {/* 跳过弹窗 */}
                    <SkipModalComp {...skipModalProps} key="skip" />

                    <Form.Item
                        name="name"
                        noStyle
                        rules={[
                            getAuditTextRules('智能体名称', 'onBlur'),
                            getAgentNameRules('智能体名称', ['onBlur', 'onChange']),
                        ]}
                    >
                        <InputWithLabel label="名称" disabled={loading} placeholder="起一个响亮的名字吧" />
                    </Form.Item>

                    <Form.Item
                        name="introduction"
                        rules={[
                            {
                                required: true,
                                message: '请输入设定',
                            },
                            getAuditTextRules('智能体设定', 'onBlur'),
                        ]}
                        noStyle
                        required
                        validateTrigger={['onBlur']}
                    >
                        <InputWithLabel.Textarea
                            autoSize={{
                                minRows: 9,
                                maxRows: 9,
                            }}
                            required
                            className="mt-[15px]"
                            disabled={loading}
                            label="设定"
                            placeholder="示例：你是一位经验丰富的英语老师，拥有激发学生学习热情的教学方法，你善于运用幽默和实际应用案例，使对话充满趣味。"
                        />
                    </Form.Item>

                    {loading && (
                        <div
                            className="absolute top-0 flex h-full w-full flex-col items-center justify-end"
                            style={{
                                background: 'rgba(245,246,249, 80%)',
                            }}
                        >
                            <img src={loadingAnimation} className="mb-3 w-12" alt="加载中"></img>
                            <div className="mb-[29px] text-sm">{loadingText}</div>
                        </div>
                    )}
                </Form>
            </PopupM>
        </ConfigProvider>
    );
}

export default function QuickStartWithLogContext() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.QUICK_CREATE}>
            <QuickStart />
        </LogContextProvider>
    );
}
