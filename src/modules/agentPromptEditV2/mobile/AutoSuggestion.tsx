/**
 * @file 自动追问组件
 * <AUTHOR>
 */

import {ConfigProvider, Form, Input, Switch} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import styled from '@emotion/styled';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {getAuditTextRules} from '@/api/audit';
const SwitchContainer = styled.div`
    .ant-switch-checked:hover:not(.ant-switch-disabled) {
        background-color: ${ThemeConfig.token.colorPrimary} !important;
    }

    .ant-switch {
        background-color: rgba(0, 0, 0, 0.2);
    }

    .ant-switch:hover:not(.ant-switch-disabled) {
        background-color: rgba(0, 0, 0, 0.2);
    }
`;
const OnlyValidFormItem = styled(Form.Item)`
    &.ant-form-item .ant-form-item-control-input {
        min-height: 0;
    }
    & .ant-form-item-explain-error {
        margin-top: 12px;
        margin-bottom: 0px;
        padding-left: 11px;
        line-height: 100%;
    }
`;

export default function AutoSuggestion() {
    const form = useFormInstance();
    const isEnabled = useWatch(['agentJson', 'autoSuggestion', 'isEnabled'], form);
    const promptEnabled = useWatch(['agentJson', 'autoSuggestion', 'promptEnabled'], form);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Switch: {
                        trackMinWidth: 36,
                        trackHeight: 20,
                        handleSize: 14,
                        trackPadding: 3,
                    },
                },
            }}
        >
            <SwitchContainer className="mt-3 rounded-[12px] bg-white px-[13px] py-[14px] leading-none">
                <div className="flex items-center justify-between">
                    <div className="flex-grow text-base font-semibold">自动追问</div>
                    <Form.Item noStyle name={['agentJson', 'autoSuggestion', 'isEnabled']}>
                        <Switch className="ml-2" />
                    </Form.Item>
                    <Form.Item hidden name={['agentJson', 'autoSuggestion', 'promptEnabled']}>
                        <Switch />
                    </Form.Item>
                </div>
                <div className={'text-gray-tertiary transition-all' + (isEnabled ? ' mt-2' : ' h-0 opacity-0')}>
                    自定义规则仅支持在电脑端配置
                </div>
                <OnlyValidFormItem
                    className="mb-0"
                    name={['agentJson', 'autoSuggestion', 'prompt']}
                    rules={promptEnabled ? [getAuditTextRules('自定义规则', 'onBlur')] : []}
                >
                    <Input className="hidden" />
                </OnlyValidFormItem>
            </SwitchContainer>
        </ConfigProvider>
    );
}
