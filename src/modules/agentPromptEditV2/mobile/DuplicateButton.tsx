/**
 * @file 配置查看页面顶底部的复制按钮
 * <AUTHOR>
 */

import {useCallback, useEffect, useMemo, useState} from 'react';
import {ConfigProvider, Form, Input, Spin, InputProps} from 'antd';
import debounce from 'lodash/debounce';
import Toast from 'antd-mobile/es/components/toast';
import {useNavigate, useParams} from 'react-router-dom';
import {useWatch} from 'antd/es/form/Form';
import ModalM from '@/components/mobile/Modal';
import urls from '@/links';
import duplicateApi, {duplicateAgentValidate} from '@/api/agentDuplicate';
import spinIndicator from '@/modules/agentPromptEditV2/pc/static/duplicate-spin.png';
import {ErrorResponseCode} from '@/api/error';
import {DuplicateAgentSource} from '@/api/agentDuplicate/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {usePromptViewConfigStore} from '@/store/agent/promptViewConfigStore';
import {useAgentNum} from '@/components/Sidebar/hooks/useAgentNum';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import PopupM from '@/components/mobile/Popup';
import {getAgentNameRules, getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
import {getCountConfig} from '@/utils/text';

const AgentName = (props: InputProps) => {
    return (
        <div className="flex items-center gap-3 rounded-lg bg-white px-3 py-4">
            <span className="flex items-center justify-center whitespace-nowrap font-semibold">
                <span>名称</span>
                <span className="ml-0.5 font-pingfang text-red-500">*</span>
            </span>
            <Input className="border-none bg-white" {...props} />
        </div>
    );
};

export default function DuplicateButton() {
    const navigate = useNavigate();

    const [reachLimitModalOpen, setReachLimitConfirmOpen] = useState(false);
    const {id: appId} = useParams();

    const [loading, setLoading] = useState(false);
    const {ubcClickLog, ubcShowLog} = useUbcLog();
    const {appLimit, updateAgentListNum} = useAgentNum();

    const [formVisible, setFormVisible] = useState(false);

    const [form] = Form.useForm();
    const [canSubmit, setCanSubmit] = useState(false);
    const name = Form.useWatch('name', form);
    const validateFields = useMemo(() => {
        return debounce(async () => {
            try {
                await form.validateFields({validateOnly: true});
                setCanSubmit(true);
            } catch (error) {
                setCanSubmit(false);
            }
        }, 300);
    }, [form]);

    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const allowCreate = !userInfoData?.hasTpProxy;

    useEffect(() => {
        updateAgentListNum();
    }, [updateAgentListNum]);

    const agentName = useWatch(['agentInfo', 'name']);

    const logExt = useMemo(() => {
        return {
            [EVENT_EXT_KEY_CONST.AGENT_ID]: appId,
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
        };
    }, [agentName, appId]);
    const {display} = usePromptViewConfigStore(store => ({
        display: store.display,
    }));
    const handleDuplicate = useCallback(async () => {
        try {
            setLoading(true);
            if (!appId) {
                return;
            }

            await duplicateAgentValidate(appId);
            const res = await duplicateApi.duplicate({
                appId,
                from: DuplicateAgentSource.Center,
                name: name,
            });
            setLoading(false);
            navigate(`${urls.agentPromptEdit.raw()}?appId=${res.appId}&createBy=dup`);
            Toast.show({content: '同款智能体已复制成功'});
        } catch (error: any) {
            if (error.errno === ErrorResponseCode.AgentReachLimit) {
                ubcShowLog(EVENT_TRACKING_CONST.agentDuplicateReachLimitShow, logExt);
                setReachLimitConfirmOpen(true);
            }
        } finally {
            setLoading(false);
        }
    }, [appId, name, navigate, ubcShowLog, logExt]);

    const handleOpenForm = useCallback(() => {
        if (!allowCreate) {
            showDisallowOperateToast();
            return;
        }

        if (display.canCopy === false) {
            return Toast.show('您的智能体只有在人工审核通过后，复制功能才会被激活，请到【我的智能体】页面查看审核进度');
        }

        ubcClickLog(EVENT_TRACKING_CONST.configurationAgentDuplicate, logExt);
        setFormVisible(true);
    }, [allowCreate, display.canCopy, logExt, ubcClickLog]);

    const jumpToMyAgent = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.agentDuplicateReachLimitManage, logExt);
        navigate(urls.agentList.raw());
    }, [navigate, ubcClickLog, logExt]);

    const closeModal = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.agentDuplicateReachLimitCancel, logExt);
        setReachLimitConfirmOpen(false);
    }, [ubcClickLog, logExt]);

    const handleClose = useCallback(() => {
        setFormVisible(false);
    }, []);
    const btnDisabled = !allowCreate || display.canCopy === false;

    return (
        <ConfigProvider
            theme={{
                components: {
                    Radio: {
                        colorBorder: '#B7B9C1',
                    },
                },
            }}
        >
            <ModalM
                open={reachLimitModalOpen}
                okText="管理"
                cancelText="取消"
                onOk={jumpToMyAgent}
                onCancel={closeModal}
            >
                <p className="px-[38px] text-black opacity-90">
                    已达到智能体创建上限（${appLimit}），请前往“我的智能体”进行管理
                </p>
            </ModalM>
            <div
                className={
                    'sticky bottom-6 z-10 mx-[14px] flex h-[50px] flex-grow justify-center rounded-[12px] py-4 text-[18px] font-semibold text-white ' +
                    (btnDisabled ? 'bg-[#bbc0fa]' : 'bg-primary')
                }
                onClick={handleOpenForm}
            >
                {loading ? (
                    <Spin
                        indicator={<img className="ant-spin-dot ant-spin-dot-spin mr-1" src={spinIndicator} />}
                        size="small"
                    />
                ) : (
                    <span>复制</span>
                )}
            </div>
            <PopupM
                visible={formVisible}
                bodyStyle={{height: '80%'}}
                background="#F5F6F9"
                title="复制智能体"
                showCloseButton
                onClose={handleClose}
                confirmButtonProps={{loading}}
                confirmText={loading ? '正在复制' : '确认'}
                disabled={!canSubmit || loading}
                onConfirm={handleDuplicate}
            >
                <Form disabled={false} form={form} onValuesChange={validateFields}>
                    <Form.Item
                        labelAlign="left"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: '请输入智能体名称',
                            },
                            getAuditSpaceRules('智能体名称', 'onBlur'),
                            getAuditTextRules('智能体名称', 'onBlur'),
                            getAgentNameRules('智能体名称', ['onBlur', 'onChange']),
                        ]}
                        validateTrigger={['onBlur', 'onChange']}
                        className="mb-[10px]"
                    >
                        <AgentName placeholder="起一个响亮的名字吧" count={getCountConfig(20)} />
                    </Form.Item>
                    <div className="mb-[18px] text-sm font-normal text-gray-tertiary">
                        建议区别于原智能体，否则影响上架和分发
                    </div>
                </Form>
            </PopupM>
        </ConfigProvider>
    );
}
