/**
 * @file 创建语音弹窗
 * <AUTHOR>
 *
 */
import React, {useCallback, Fragment, useEffect, useState, useRef} from 'react';
import {useSearchParams} from 'react-router-dom';
import {message} from 'antd';
import {uploadSpeechWithToken} from '@/api/agentEditV2';
import useCreateAudio, {CreateAudioStatus, MinRecordDuration} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';
import {decodeJWTToken} from '@/utils/text';
import RecordSuccess from '@/modules/agentPromptEditV2/mobile/components/CreateAudioWithToken/RecordSuccess';
import TokenExpired from '@/modules/agentPromptEditV2/mobile/components/CreateAudioWithToken/TokenExpired';
import RecordAudio from '@/components/mobile/RecordAudio';

const CreateAudioTitleMap = {
    [CreateAudioStatus.NONE]: '请朗读',
    [CreateAudioStatus.RECORDING]: '录制中',
    [CreateAudioStatus.UPLOADING]: '录制中',
    [CreateAudioStatus.SUCCESS]: '录制成功',
    [CreateAudioStatus.FAIL]: '录制失败',
};

/**
 * 获取 token 的过期时间(毫秒)
 *
 * @param token 需要解析的 token 字符串
 * @returns 返回 token 的过期时间戳，如果解析失败则返回 0
 */
function getTokenExpireTime(token: string | null): number {
    try {
        const {expire_time: expireTime} = decodeJWTToken(token || '');

        return +expireTime;
    } catch (e) {
        console.error(e);
        return 0;
    }
}

export default function CreateAudio() {
    const [searchParams] = useSearchParams();
    const token = searchParams.get('token');

    const [tokenValid, setTokenValid] = useState<boolean>(getTokenExpireTime(token) > Date.now());
    const setTokenInvalidTimeoutRef = useRef<NodeJS.Timeout>();

    const {status, setStatus, description, setDescription, recordDuration, startRecord, stopRecord, showCountdown} =
        useCreateAudio();

    // 停止录音并提交录音文件
    const stopRecordAction = useCallback(async () => {
        if (recordDuration < MinRecordDuration && status === CreateAudioStatus.RECORDING) {
            setStatus(CreateAudioStatus.FAIL);
            setDescription('录音不到10秒，请重新录制');
            stopRecord();
            return;
        }

        try {
            const blob = await stopRecord();
            const formData = new FormData();
            formData.append('token', token as string);
            formData.append('speechFile', new File([blob], 'speech.wav'));
            setStatus(CreateAudioStatus.UPLOADING);
            await uploadSpeechWithToken(formData);
            setStatus(CreateAudioStatus.SUCCESS);
            setTokenInvalidTimeoutRef.current && clearTimeout(setTokenInvalidTimeoutRef.current);
        } catch (e: any) {
            setStatus(CreateAudioStatus.FAIL);
            message.error('录制失败，请重新录制');
            setDescription(e?.message || e?.msg || '录制失败，请重新录制');
        }
    }, [recordDuration, status, stopRecord, token, setStatus, setDescription]);

    // 校验 token 是否有效，有效时启动 setTimeout，待 token 失效后，停止录音并设置为 token 失效状态
    useEffect(() => {
        // 成功状态和上传状态不校验 token，1：成功状态只需要展示成功状态，2：上传中状态如果 token 失效等接口返回数据时如果录制成功则设置为成功状态，否则设置为失败状态的同时校验 token 状态即可
        if (status !== CreateAudioStatus.SUCCESS && status !== CreateAudioStatus.UPLOADING) {
            const expireTime = getTokenExpireTime(token);
            const isTokenValid = expireTime > Date.now();
            setTokenValid(isTokenValid);

            setTokenInvalidTimeoutRef.current && clearTimeout(setTokenInvalidTimeoutRef.current);
            setTokenInvalidTimeoutRef.current = isTokenValid
                ? setTimeout(() => {
                      stopRecord();
                      setTokenValid(false);
                  }, expireTime - Date.now())
                : undefined;
        }

        return () => {
            setTokenInvalidTimeoutRef.current && clearTimeout(setTokenInvalidTimeoutRef.current);
        };
    }, [status, token, stopRecord]);

    return (
        <div className="flex min-h-full flex-col items-center bg-gray-bg-base px-3 leading-none">
            {tokenValid ? (
                status === CreateAudioStatus.SUCCESS ? (
                    <RecordSuccess />
                ) : (
                    <Fragment>
                        <div className="mb-3 mt-[50px] text-2xl font-medium leading-none text-black">
                            {CreateAudioTitleMap[status]}
                        </div>
                        <RecordAudio
                            recordStatus={status}
                            guideText={description}
                            showCountdown={showCountdown}
                            recordDuration={recordDuration}
                            onStartRecordClick={startRecord}
                            onStopRecordClick={stopRecordAction}
                        />
                    </Fragment>
                )
            ) : (
                <TokenExpired />
            )}
        </div>
    );
}
