/**
 * @file 声音录制成功页面
 */

import audioRecordSuccess from '@/modules/agentPromptEditV2/mobile/static/audio-record-success.png';

export default function RecordSuccess() {
    return (
        <div className="flex flex-col items-center">
            <img className="absolute left-[51px] w-[calc(100vw-122.15px)]" src={audioRecordSuccess} />
            <div className="mb-[21px] mt-[140px] h-[136px] w-[136px] rounded-full border-[20px] border-[#DEE0FF] bg-primary text-center">
                <span className="iconfont icon-check text-[56px] leading-[96px] text-white" />
            </div>
            <span className="mb-[18px] text-lg font-medium leading-none text-[#6773F7]">恭喜你！</span>
            <span className="mb-6 text-[28px] font-medium leading-none text-black">声音已提交</span>
            <span className="text-sm leading-none text-gray-tertiary">快去电脑端查看结果吧</span>
        </div>
    );
}
