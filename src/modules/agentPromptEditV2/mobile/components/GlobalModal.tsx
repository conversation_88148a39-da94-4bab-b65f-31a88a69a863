/**
 * @file 一些用到的全局 modal
 * <AUTHOR>
 */

import {Form, Modal} from 'antd';
import {useCallback} from 'react';
import {useSearchParams} from 'react-router-dom';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {AgentTab} from '../../interface';
import {agentUpdateToastText} from '../../utils';

export const VersionCodeErrorModal = () => {
    const form = Form.useFormInstance<AgentConfigV2>();
    const [searchParams] = useSearchParams();
    const activeTab = searchParams.get('activeTab') || AgentTab.Create;

    const {lastUpdateTime, showModal, setCurrentAgentConfig, setLatestAgentConfig} = usePromptEditStoreV2(store => ({
        lastUpdateTime: store.lastUpdateTime,
        showModal: store.showVersionCodeErrorModal,
        setCurrentAgentConfig: store.setCurrentAgentConfig,
        setLatestAgentConfig: store.setLatestAgentConfig,
    }));

    const handleOk = useCallback(() => {
        setLatestAgentConfig(form);
    }, [form, setLatestAgentConfig]);
    const handleCancel = useCallback(() => {
        setCurrentAgentConfig();
    }, [setCurrentAgentConfig]);

    const open = activeTab === AgentTab.Create && showModal;

    return (
        <Modal
            centered
            title="更新"
            open={open}
            okText="更新"
            cancelText="使用当前页面版本"
            keyboard={false}
            maskClosable={false}
            closeIcon={null}
            onOk={handleOk}
            onCancel={handleCancel}
        >
            {agentUpdateToastText(lastUpdateTime)}
        </Modal>
    );
};

export const PublishErrorModal = () => {
    const {showModal, errorMsg, setShowPublishErrorModal} = usePromptEditStoreV2(store => ({
        showModal: store.showPublishErrorModal,
        errorMsg: store.publishError?.msg,
        setShowPublishErrorModal: store.setShowPublishErrorModal,
    }));

    const handleOk = useCallback(() => {
        setShowPublishErrorModal(false);
    }, [setShowPublishErrorModal]);

    return (
        <Modal
            centered
            title="发布失败"
            open={showModal}
            okText="我知道了"
            keyboard={false}
            maskClosable={false}
            closeIcon={null}
            cancelButtonProps={{style: {display: 'none'}}}
            onOk={handleOk}
        >
            {errorMsg || '未知失败，请稍后重试'}
        </Modal>
    );
};
