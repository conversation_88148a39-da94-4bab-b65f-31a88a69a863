import {useCallback, useMemo, useState} from 'react';
import {Toast} from 'antd-mobile';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import {publishContext, PublishContext} from './context';

const PublishContextProvider = publishContext.Provider;

export default function ProviderContext({children}: {children: React.ReactNode}) {
    const [popupShow, setPopupShow] = useState(false);

    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const allowCreate = !userInfoData?.hasTpProxy;
    const isRoleFramework = useUserInfoStore(store => store.userFeatureAccess.role_framework);

    const {canPublishResult} = usePromptEditStoreV2(store => ({
        canPublishResult: store.canPublish({isRoleFramework}),
    }));
    const publishEnabled = canPublishResult.canPublish && allowCreate;

    const handlePublishClick = useCallback(() => {
        if (!allowCreate) {
            showDisallowOperateToast();
            return;
        }

        if (canPublishResult.canPublish) {
            setPopupShow(true);
        } else {
            Toast.show({content: canPublishResult.reason});
        }
    }, [allowCreate, canPublishResult.canPublish, canPublishResult.reason]);

    const context: PublishContext = useMemo(
        () => ({setPopupShow, popupShow, handlePublishClick, publishEnabled}),
        [handlePublishClick, popupShow, publishEnabled]
    );

    return <PublishContextProvider value={context}>{children}</PublishContextProvider>;
}
