import {createContext, useContext} from 'react';

export interface PublishContext {
    handlePublishClick: () => void;
    setPopupShow: React.Dispatch<React.SetStateAction<boolean>>;
    popupShow: boolean;
    publishEnabled: boolean;
}

export const publishContext = createContext<PublishContext>({
    handlePublishClick: () => {
        throw new Error('未找到 ContextProvider');
    },
    setPopupShow: () => {
        throw new Error('未找到 ContextProvider');
    },
    popupShow: false,
    publishEnabled: false,
});

export const usePublishContext = () => useContext(publishContext);
