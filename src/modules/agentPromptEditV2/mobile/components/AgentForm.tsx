/**
 * @file 整体的表单容器
 * <AUTHOR>
 */
import {Form} from 'antd';
import {useCallback} from 'react';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {usePromptEditContext} from '../../utils';

export default function AgentForm({children, className}: {children?: React.ReactNode; className?: string}) {
    const [form] = Form.useForm<AgentConfigV2>();
    const {setAgentConfig, debounceSaveAgent} = usePromptEditStoreV2(store => ({
        setAgentConfig: store.setAgentConfig,
        debounceSaveAgent: store.debounceSaveAgent,
    }));

    const handleValuesChange = useCallback(async () => {
        const value = form.getFieldsValue();

        setAgentConfig(value);
        debounceSaveAgent();
    }, [debounceSaveAgent, form, setAgentConfig]);

    const {readonly} = usePromptEditContext();

    return (
        <Form
            id="promptAgentForm"
            form={form}
            disabled={readonly}
            className={className}
            colon={false}
            autoComplete="off"
            onValuesChange={handleValuesChange}
            scrollToFirstError
        >
            {children}
        </Form>
    );
}
