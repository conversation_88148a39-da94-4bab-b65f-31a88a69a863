/**
 * @file 在低版本浏览器中，兼容 dvh 单位，改用 innerHeight 计算
 * <AUTHOR>
 */

import {useEffect} from 'react';

let innerHeight = window.innerHeight;

const setInnerHeight = () => {
    if (CSS.supports('height', '10dvh')) {
        document.documentElement.style.setProperty('--dvh', '1dvh');
        return;
    }

    innerHeight = window.innerHeight;
    const vh = innerHeight * 0.01;
    document.documentElement.style.setProperty('--dvh', `${vh}px`);
};

/**
 * 引入这个 hook 可以使用 var(--dvh) 单位，兼容低版本浏览器
 */
export function useToVh() {
    setInnerHeight();

    useEffect(() => {
        window.addEventListener('resize', setInnerHeight);
        return () => {
            window.removeEventListener('resize', setInnerHeight);
        };
    });
}
