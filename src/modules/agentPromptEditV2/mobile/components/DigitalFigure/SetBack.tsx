import {But<PERSON>, ConfigProvider, Segmented} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useContext, useState, useRef, useEffect} from 'react';
import classNames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import {SegmentedValue} from 'antd/es/segmented';
import {AgentDigitalFigure, DigitalCreateStep, DigitalType} from '@/api/agentEdit/interface';
import {COLORS_MAP} from '@/modules/agentPromptEditV2/components/DigitalFigure/constants';

import {
    ContextType,
    DigitalFigureContext,
} from '@/modules/agentPromptEditV2/components/DigitalFigure/DigitalFigureContext';
import {getNewDigitalFigure} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';
import {BackgroundConfigListItem, FigureType} from '@/api/agentEditV2/interface';
import {figureCreate, getFigureBackgroundConfig, imageColorPick} from '@/api/agentEditV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {WiseCharacterBoxType, CharacterSelectType} from '@/utils/loggerV2/interface';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {FigureBackgroundType} from '@/modules/agentPromptEditV2/interface';
import {pickBackgroundColor} from '@/modules/agentPromptEditV2/utils';
import {Theme} from '@/modules/agentPromptEditV2/mobile/components/DigitalFigure/constants';

const NoScrollBarContainer = styled.div`
    &::-webkit-scrollbar {
        display: none;
    }

    /* Edge */
    -ms-overflow-style: none;
    /* Firefox */
    scrollbar-width: none;

    margin-bottom: calc(6 * var(--dvh));
    padding: calc(1 * var(--dvh)) 0;
`;

const StyledSegmented = styled(Segmented)`
    border-radius: 20px !important;
    .ant-segmented-group {
        gap: 4px;
        .ant-segmented-item {
            border-radius: 20px !important;

            .ant-segmented-item-label {
                overflow: visible;
                min-height: 26px;
                line-height: 26px;
                padding: 0 12px;
                font-size: 14px;
                border-radius: 20px !important;
            }
        }
        .ant-segmented-item-selected {
            font-weight: 500;
            border-radius: 20px !important;
        }
    }
    .ant-segmented-thumb-motion-appear-active {
        border-radius: 20px !important;
    }
`;

export default function SetBack({
    data,
    onChange,
    onClose,
    setDigitalFigureContext,
    figureType,
    getDigitalFigure,
    appId,
}: {
    data: AgentDigitalFigure;
    onChange?: (data: AgentDigitalFigure) => void;
    onClose?: () => void;
    originalImage: string; // 原始图片，用于回退上一步
    setDigitalFigureContext: React.Dispatch<React.SetStateAction<ContextType>>;
    figureType: FigureType;
    getDigitalFigure?: (digitalFigure: AgentDigitalFigure) => void;
    theme?: Theme;
    appId: string;
}) {
    const [selectedBg, setSelectedBg] = useState('#D9CECE');
    const [selectedConfig, setSelectedConfig] = useState<BackgroundConfigListItem>();
    const digitalFigureContext = useContext(DigitalFigureContext);
    const abortControllerRef = useRef<AbortController | null>(null);
    const scrollRef = useRef<HTMLDivElement>(null);

    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();

    const handleSelectBg = useCallback((color: string) => {
        setSelectedBg(color);
    }, []);
    const handleSelectConfig = useCallback((config: BackgroundConfigListItem) => {
        setSelectedConfig(config);
    }, []);

    const [confirmLoading, setConfirmLoading] = useState(false);
    const [backgroundType, setBackgroundType] = useState<FigureBackgroundType>(FigureBackgroundType.COLOR);
    const [backgroundConfig, setBackgroundConfig] = useState<BackgroundConfigListItem[]>([]);

    const handleSegmentedChange = useCallback((value: SegmentedValue) => {
        setBackgroundType(value as FigureBackgroundType);

        scrollRef.current?.scrollTo({left: 0, behavior: 'smooth'});
    }, []);

    const getBackgroundConfig = useCallback(async () => {
        const res = await getFigureBackgroundConfig({
            bgType: 1,
        });
        setBackgroundConfig(res);
        setSelectedConfig(res[0]);
    }, []);

    useEffect(() => {
        if (figureType === FigureType.Dynamic) {
            getBackgroundConfig();
        }
    }, [figureType, getBackgroundConfig]);

    // 【静态】确认背景选择，完成创建 。 这一步只需存储数据，没有请求接口
    const handleStaticCreate = useCallback(async () => {
        setConfirmLoading(true);
        let digitalFigure = cloneDeep(data);
        try {
            digitalFigure = getNewDigitalFigure(digitalFigureContext.splitedImage, selectedBg, true);

            // 重置上下文数据
            setDigitalFigureContext({
                splitedImage: '',
                originalImage: '',
                currentSetp: DigitalCreateStep.OnceCrop,
                figureType: FigureType.Static,
            });
        } catch (error: any) {
            digitalFigure && (digitalFigure.backgroundPicUrl = '');
            throw error;
        } finally {
            clickLog(EVENT_VALUE_CONST.CHARACTER_BOX_NEXT, EVENT_PAGE_CONST.CODELESS_CREATE, {
                ...extLog,
                [EVENT_EXT_KEY_CONST.C_CHARACTER_BOX_TYPE]: WiseCharacterBoxType.SET_BACKGROUND_COLOR_MODAL,
                [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]: CharacterSelectType.STATIC,
            });

            onChange && onChange(digitalFigure);
            setConfirmLoading(false);
            onClose && onClose();
        }
    }, [
        data,
        selectedBg,
        digitalFigureContext.splitedImage,
        onChange,
        setDigitalFigureContext,
        onClose,
        clickLog,
        extLog,
    ]);

    // 【动态】 请求创建动态数字人接口
    const handleDynamicCreate = useCallback(async () => {
        setConfirmLoading(true);
        let digitalFigure = cloneDeep(data);
        abortControllerRef.current = new AbortController();
        try {
            // 创建动态数字形象
            const res = await figureCreate(
                {
                    imgUrlList: [digitalFigureContext.splitedImage],
                    appId,
                    figureType: FigureType.Dynamic,
                },
                abortControllerRef.current?.signal
            );
            setDigitalFigureContext({
                ...digitalFigureContext,
                currentSetp: DigitalCreateStep.SetBackground,
            });
            // 更新数据
            if (backgroundType === FigureBackgroundType.COLOR) {
                digitalFigure = getNewDigitalFigure(res.foregroundImageUrl, selectedBg, true, FigureType.Dynamic);
            } else {
                const hex = await imageColorPick(selectedConfig?.bgUrl || '');
                // 更新数据
                digitalFigure = getNewDigitalFigure(
                    res.foregroundImageUrl,
                    pickBackgroundColor(hex),
                    true,
                    FigureType.Dynamic,
                    selectedConfig?.bgUrl
                );
            }

            const newDigitalFigure: AgentDigitalFigure = {
                ...digitalFigure,
                taskUuid: res.taskUuid,
                // 清空有审核版本的figureId
                cloudFigureId: null,
            };

            onChange?.(newDigitalFigure);
            getDigitalFigure && (await getDigitalFigure(newDigitalFigure));
            // 重置上下文数据
            setDigitalFigureContext({
                splitedImage: '',
                originalImage: '',
                currentSetp: DigitalCreateStep.OnceCrop,
                figureType: FigureType.Static,
            });
        } catch (e) {
            // 更新数据
            const newDigitalFigure: AgentDigitalFigure = {
                ...digitalFigure,
                // 更新表单中的「无背景的图片」「背景色」
                backgroundPicUrl: '',
                backgroundPicColor: '',
                splitPicTag: true,
                digitalType: DigitalType.CHARACTER,
                figureType: FigureType.Dynamic,
                canvasImgUrl: '',
                bgUrl: '',
            };
            onChange?.(newDigitalFigure);
            throw e;
        } finally {
            // 打点

            clickLog(EVENT_VALUE_CONST.CHARACTER_BOX_NEXT, EVENT_PAGE_CONST.CODELESS_CREATE, {
                ...extLog,
                [EVENT_EXT_KEY_CONST.C_CHARACTER_BOX_TYPE]: WiseCharacterBoxType.SET_BACKGROUND_COLOR_MODAL,
                [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]: CharacterSelectType.DYNAMIC,
            });

            setConfirmLoading(false);
            // 关闭背景选择弹窗
            onClose && onClose();
        }
    }, [
        data,
        digitalFigureContext,
        appId,
        setDigitalFigureContext,
        backgroundType,
        onChange,
        getDigitalFigure,
        selectedBg,
        selectedConfig?.bgUrl,
        onClose,
        clickLog,
        extLog,
    ]);

    const previewImgStyle = {
        background:
            backgroundType === FigureBackgroundType.COLOR
                ? COLORS_MAP.get(selectedBg)
                : `url(${selectedConfig?.bgUrl}) no-repeat center center / 100% 100%`,
    };

    return (
        <>
            <img
                className="mb-[15px] mt-[calc(5.8*var(--dvh))] h-0 flex-shrink flex-grow bg-cover object-cover"
                style={previewImgStyle}
                src={digitalFigureContext.splitedImage}
            />

            <div className="w-full">
                {figureType === FigureType.Dynamic && (
                    <div className="mb-[15px] text-center">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Segmented: {
                                        itemSelectedColor: ThemeConfig.token.colorPrimary,
                                        trackPadding: 1,
                                    },
                                },
                            }}
                        >
                            <StyledSegmented
                                value={backgroundType}
                                onChange={handleSegmentedChange}
                                options={[
                                    {
                                        label: '纯色',
                                        value: FigureBackgroundType.COLOR,
                                    },
                                    {
                                        label: '官方',
                                        value: FigureBackgroundType.OFFICIAL,
                                    },
                                ]}
                            />
                        </ConfigProvider>
                    </div>
                )}
                <NoScrollBarContainer
                    className="flex w-full touch-auto flex-col gap-3 overflow-x-scroll pl-[17px]"
                    ref={scrollRef}
                >
                    {backgroundType === FigureBackgroundType.COLOR ? (
                        <>
                            <div className="flex gap-3">
                                {Array.from(COLORS_MAP.keys())
                                    .slice(0, 9)
                                    .map(color => (
                                        <div
                                            onClick={() => handleSelectBg(color)}
                                            key={color}
                                            className={classNames('h-[12.5vw] w-[12.5vw] shrink-0 rounded-[9px]', {
                                                'rounded-xl outline outline-2 outline-offset-2': color === selectedBg,
                                                'outline-primary': color === selectedBg,
                                            })}
                                            style={{background: COLORS_MAP.get(color)}}
                                        ></div>
                                    ))}
                                {/* 这个元素是用来撑开末尾的间距的 */}
                                <div className="w-[5px] shrink-0"></div>
                            </div>
                            <div className="flex gap-3">
                                {Array.from(COLORS_MAP.keys())
                                    .slice(9)
                                    .map(color => (
                                        <div
                                            key={color}
                                            onClick={() => handleSelectBg(color)}
                                            className={classNames('h-[12.5vw] w-[12.5vw] shrink-0 rounded-[9px]', {
                                                'rounded-xl outline outline-2 outline-offset-2': color === selectedBg,
                                                'outline-primary': color === selectedBg,
                                            })}
                                            style={{background: COLORS_MAP.get(color)}}
                                        ></div>
                                    ))}
                                <div className="w-[0.01px] shrink-0"></div>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="flex gap-3 pr-[5px]">
                                {backgroundConfig.slice(0, Math.ceil(backgroundConfig.length / 2)).map(config => (
                                    <div
                                        key={config.bgUrl}
                                        className="relative shrink-0"
                                        onClick={() => handleSelectConfig(config)}
                                    >
                                        <img
                                            className={classNames('h-[12.5vw] w-[12.5vw] rounded-[9px] object-cover', {
                                                'rounded-xl outline outline-2 outline-offset-2':
                                                    config.bgUrl === selectedConfig?.bgUrl,

                                                'box-sha  outline-primary': config.bgUrl === selectedConfig?.bgUrl,
                                            })}
                                            src={config.bgThumbnailUrl}
                                        ></img>
                                        <div className="absolute bottom-0 w-full rounded-b-[9px] bg-[#000000A6] py-[1px] text-center text-[12px] font-medium">
                                            {config.bgCityName}
                                        </div>
                                    </div>
                                ))}
                                {/* 这个元素是用来撑开末尾的间距的 */}
                                <div className="w-[5px] shrink-0"></div>
                            </div>
                            <div className="flex gap-3">
                                {backgroundConfig.slice(Math.ceil(backgroundConfig.length / 2)).map(config => (
                                    <div
                                        key={config.bgUrl}
                                        className="relative shrink-0"
                                        onClick={() => handleSelectConfig(config)}
                                    >
                                        <img
                                            className={classNames('h-[12.5vw] w-[12.5vw] rounded-[9px] object-cover', {
                                                'rounded-xl outline outline-2 outline-offset-2':
                                                    config.bgUrl === selectedConfig?.bgUrl,

                                                'outline-primary': config.bgUrl === selectedConfig?.bgUrl,
                                            })}
                                            src={config.bgThumbnailUrl}
                                        ></img>
                                        <div className="absolute bottom-0 w-full rounded-b-[9px] bg-[#000000A6] py-[1px] text-center text-[12px] font-medium">
                                            {config.bgCityName}
                                        </div>
                                    </div>
                                ))}
                                <div className="w-[5px] shrink-0"></div>
                            </div>
                        </>
                    )}
                </NoScrollBarContainer>

                <div className="setBack-finish-btn w-full px-9 text-base font-medium">
                    <Button
                        type="primary"
                        className="h-[50px] w-full rounded-full"
                        onClick={figureType === FigureType.Static ? handleStaticCreate : handleDynamicCreate}
                        loading={confirmLoading}
                    >
                        选用
                    </Button>
                </div>
            </div>
        </>
    );
}
