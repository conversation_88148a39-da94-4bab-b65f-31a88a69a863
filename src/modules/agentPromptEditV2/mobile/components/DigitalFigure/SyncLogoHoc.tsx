/**
 * @file 移动端的 switch 外面包了一层，因为如果里面的 disable 了，Tooltip 不会显示
 * <AUTHOR>
 */
import {useMemo} from 'react';
import {Switch, Tooltip} from 'antd';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import SyncLogo from '@/modules/agentPromptEditV2/components/DigitalFigure/SyncLogo';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {getFigureUrl} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';

export default function SyncLogoMobileHoc(props: {onChange?: (val: string) => void}) {
    const {digitalFigure} = usePromptEditStoreV2(store => ({
        digitalFigure: store.agentConfig.agentInfo.digitalFigure,
    }));

    const {readonly} = usePromptEditContext();

    // 有无上传数字形象
    const digitalSyncDisabled = useMemo(
        () => readonly || !getFigureUrl(digitalFigure || undefined),
        [readonly, digitalFigure]
    );

    if (digitalSyncDisabled) {
        return (
            <div className="relative">
                <Switch value={false} size="small" disabled className="ml-2" />
                <Tooltip title={readonly ? '' : '请先配置背景形象'}>
                    <div className="absolute left-0 top-0 h-full w-full"></div>
                </Tooltip>
            </div>
        );
    }

    return <SyncLogo onChange={props.onChange} />;
}
