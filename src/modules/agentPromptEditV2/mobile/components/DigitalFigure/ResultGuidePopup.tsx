/**
 * @file 形象示例的弹窗
 * <AUTHOR>
 */

import {useCallback} from 'react';
import classNames from 'classnames';
import PopupM from '@/components/mobile/Popup';
import staticMobile from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/static-mobile.png';
import staticPC from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/static-pc.png';
import dynamicMobile from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/dynamic_mobile.png';
import dynamicMobileCall from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/dynamic_mobile_call.webm';
import dynamicrPc from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/dynamic_pc.png';
import {FigureType} from '@/api/agentEditV2/interface';
import {useVideoDestroy} from '@/hooks/useVideoDestroy';
const staticMobileCall =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/agent-figure/static-mobile-call.webp';
interface ResultGuidePopupProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    figureType: FigureType;
}

// 示例图
const staticShowCases = [
    {
        key: 'showcase_mobile_call',
        title: '移动端-打电话',
        isMobile: true,
        imgSrc: staticMobileCall,
    },
    {
        key: 'showcase_mobile',
        title: '移动端',
        isMobile: true,
        imgSrc: staticMobile,
    },
    {
        key: 'showcase_pc',
        title: '电脑端',
        isMobile: false,
        imgSrc: staticPC,
    },
];

const dynamicShowCases = [
    {
        key: 'showcase_mobile_call',
        title: '【动态】打电话',
        imgSrc: dynamicMobileCall,
    },
    {
        key: 'showcase_mobile',
        title: '【仅静态】移动端',
        imgSrc: dynamicMobile,
    },
    {
        key: 'showcase_pc',
        title: '【仅静态】电脑端',
        imgSrc: dynamicrPc,
    },
];

enum ShowCasesKey {
    MobileCall = 'showcase_mobile_call',
    Mobile = 'showcase_mobile',
    Pc = 'showcase_pc',
}

export function ResultGuidePopup({visible, setVisible, figureType}: ResultGuidePopupProps) {
    const videoRef = useVideoDestroy();

    const handleClose = useCallback(() => {
        setVisible(false);
    }, [setVisible]);
    const showCases = figureType === FigureType.Static ? staticShowCases : dynamicShowCases;

    return (
        <PopupM
            visible={visible}
            title="形象效果示例"
            closeOnMaskClick
            onClose={handleClose}
            showCloseButton
            background="#F5F6F9"
            bodyStyle={{
                height: '90%',
                paddingInline: 0,
            }}
        >
            <div
                className={classNames([
                    'grid h-full grid-cols-2 gap-x-[9px] gap-y-[9px] overflow-y-auto px-[10px] pb-[34px]',
                    {
                        'px-[13px]': figureType === FigureType.Static,
                    },
                ])}
            >
                {showCases.map(item => (
                    <div
                        key={item.key}
                        className={classNames([
                            'flex h-fit flex-col justify-center gap-3 overflow-clip rounded-xl bg-white px-[8.5px] py-[15px]',
                            {
                                'col-span-2': item.key === ShowCasesKey.Pc,
                            },
                        ])}
                    >
                        <span className="truncate whitespace-nowrap text-base font-semibold text-black">
                            {item.title}
                        </span>
                        {figureType === FigureType.Dynamic && item.key === ShowCasesKey.MobileCall ? (
                            <video
                                ref={videoRef}
                                className="rounded-xl"
                                src={dynamicMobileCall}
                                autoPlay
                                loop
                                playsInline
                                muted
                            />
                        ) : (
                            <img className="rounded-[12px]" src={item.imgSrc}></img>
                        )}
                    </div>
                ))}
            </div>
        </PopupM>
    );
}
