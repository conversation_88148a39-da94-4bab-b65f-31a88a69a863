/**
 * @file 魔法代码，让页面滚动一下，触发重绘
 * 为了避免在 iOS 真机上， popup 关闭后，全局事件都不能响应的问题
 * <AUTHOR>
 */

export function scrollALittle() {
    // 假设关闭弹窗的逻辑已经执行
    // 获取当前滚动位置
    const originalScrollPosition = window.scrollY;

    setTimeout(() => {
        // 微小滚动页面
        window.scrollTo({
            top: originalScrollPosition + 1, // 微小向下滚动1像素
            behavior: 'instant', // 立即滚动，无动画
        });
    }, 0);

    // 立即返回原始滚动位置
    setTimeout(() => {
        window.scrollTo({
            top: originalScrollPosition,
            behavior: 'instant',
        });
    }, 50); // 短暂延迟确保滚动发生
}
