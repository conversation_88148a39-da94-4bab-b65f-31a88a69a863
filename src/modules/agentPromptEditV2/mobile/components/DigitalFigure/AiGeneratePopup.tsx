/**
 * @file AI生成形象风格选择
 * <AUTHOR>
 * 2024/8/20 AI生成形象风格选择
 */

import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import Lottie from 'lottie-react';
import {But<PERSON>} from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import GenerateFigure from '@/modules/home/<USER>/generate-figure';
import {FAST_AI_FIGURE_INTRODUCTION_BUTTONS} from '@/modules/agentPromptEditV2/components/DigitalFigure/constants';
import {getCountConfig} from '@/utils/text';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {StyledTextareaWise} from '@/components/mobile/InputWithLabel';
import PickAiStyle from '@/modules/agentPromptEditV2/mobile/components/DigitalFigure/PickAiStyle';
import {auditText} from '@/api/audit';
import {generateAgentFigure, imageColorPick} from '@/api/agentEditV2';
import {AgentConfigV2, AgentDigitalFigure} from '@/api/agentEdit/interface';
import {getNewPicIntroduction, getNewDigitalFigure} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';
import AiGenerating from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/ai-generating.gif';

// 生成弹窗状态
enum GenerationStatus {
    // 等待生成
    WAITING = 0,
    // 生成中
    GENERATING = 1,
    // 生成失败
    FAILED = 2,
    // 已生成
    GENERATED = 3,
}

const fastIntroduction = FAST_AI_FIGURE_INTRODUCTION_BUTTONS;

export default function AiGeneratePopup({
    data,
    onChange,
    onClose,
}: {
    data: AgentDigitalFigure;
    onChange?: (data: AgentDigitalFigure) => void;
    onClose?: () => void;
}) {
    const [picIntroduction, setPicIntroduction] = useState<string>('');
    const [picStyle, setPicStyle] = useState<number>(1);
    const [generatedUrl, setGeneratedUrl] = useState<string>('');
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    // 生成请求状态
    const [isFailed, setIsFailed] = useState<boolean>(false);
    const [isGenerating, setIsGenerating] = useState<boolean>(false);

    // Modal 状态
    const currentStatus = useMemo(() => {
        if (isGenerating) {
            return GenerationStatus.GENERATING;
        }

        if (isFailed) {
            return GenerationStatus.FAILED;
        }
        return generatedUrl === '' ? GenerationStatus.WAITING : GenerationStatus.GENERATED;
    }, [generatedUrl, isGenerating, isFailed]);

    useEffect(() => {
        setIsGenerating(false);
        setIsFailed(false);
        setGeneratedUrl('');
    }, []);

    const handlePicIntroductionChange = useCallback((e: {target: {value: React.SetStateAction<string>}}) => {
        setPicIntroduction(e.target.value);
    }, []);

    const textInputRef = useRef<any>();
    /** 按钮操作 */
    const handleFastIntroduction = useCallback(
        (value: string) => {
            const newPicIntroduction: string = getNewPicIntroduction(picIntroduction, textInputRef, value);
            setPicIntroduction(newPicIntroduction);
        },
        [picIntroduction]
    );

    const handlePickStyle = useCallback((value: number) => {
        setPicStyle(value);
    }, []);

    const abortControllerRef = useRef<AbortController>();
    const {agentConfig} = usePromptEditStoreV2(state => ({agentConfig: state.agentConfig}));
    // 生成形象
    const handleGenerate = useCallback(async () => {
        setIsGenerating(true);
        abortControllerRef.current = new AbortController();
        const config: AgentConfigV2 = cloneDeep(agentConfig);
        config.agentInfo.picIntroduction = picIntroduction;
        config.agentInfo.picStyle = picStyle;
        try {
            if (picIntroduction.trim()) {
                await auditText(picIntroduction, '描述内容');
            }

            const res = await generateAgentFigure(config, abortControllerRef?.current?.signal).catch(() => {
                setIsFailed(true);
            });

            if (res) {
                setGeneratedUrl(res);
                setIsFailed(false);
            } else {
                setIsFailed(true);
            }
        } catch (error: any) {
            setIsFailed(true);
            throw error;
        }

        setIsGenerating(false);
    }, [agentConfig, abortControllerRef, picIntroduction, picStyle]);

    // 使用 ai 生成的形象
    const handleUseUrl = useCallback(async () => {
        setConfirmLoading(true);
        let digitalFigure: AgentDigitalFigure = cloneDeep(data);
        try {
            const color = await imageColorPick(generatedUrl);
            digitalFigure = getNewDigitalFigure(generatedUrl, color, false);
        } catch (error) {
            throw error;
        } finally {
            onChange && onChange(digitalFigure);
            setConfirmLoading(false);
            onClose && onClose();
        }
    }, [data, onChange, onClose, generatedUrl]);

    return (
        <div className="relative h-[90vh] overflow-y-auto px-3">
            {/* 形象预览区域 */}
            {currentStatus !== GenerationStatus.WAITING && (
                <div className="center m-auto mb-3 flex h-[316px] w-[178px] items-center overflow-hidden rounded-[12px]">
                    {currentStatus === GenerationStatus.GENERATING ? (
                        <div className="relative">
                            <div className="absolute left-[50%] top-[88px] z-10 flex translate-x-[-50%] flex-col items-center justify-center">
                                <img src={AiGenerating} />
                                <span className="text-sm font-medium text-white">正在生成中…</span>
                            </div>
                            <div className="z-0">
                                <Lottie className="cursor-progress" animationData={GenerateFigure} loop />
                            </div>
                        </div>
                    ) : currentStatus === GenerationStatus.FAILED ? (
                        <div className="flex h-full w-full flex-col items-center justify-center bg-white">
                            <div className="iconfont icon-tip text-[60px] leading-none text-primary opacity-20" />
                            <div className="text-center">
                                <span className="text-gray-tertiary">生成失败</span>
                                <Button
                                    onClick={handleGenerate}
                                    className="ml-[-12px] mr-0 inline-block pr-0"
                                    type="link"
                                >
                                    点击重新生成
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <img src={generatedUrl}></img>
                    )}
                </div>
            )}

            <StyledTextareaWise
                ref={textInputRef}
                value={picIntroduction}
                className="h-[140px] rounded-xl pt-[9.4px]"
                onChange={handlePicIntroductionChange}
                count={getCountConfig(200, false)}
                placeholder="请描述您希望生成的形象背景，角色、场景、情绪、风格等（未填写自动生成）"
            />
            <div className="mt-3 flex flex-wrap items-center justify-start gap-y-1">
                {fastIntroduction.map(item => (
                    <div
                        className="mr-1 flex cursor-pointer items-center rounded-[8px] bg-white px-3 py-1 active:bg-[#F2F3FE] active:text-primary"
                        key={item.key}
                        onClick={() => handleFastIntroduction(item.value)}
                    >
                        <div className="iconfont icon-prompt1 mr-1"></div>
                        {item.tag}
                    </div>
                ))}
            </div>
            {/* 风格选择区域 */}
            <div className="mb-[130px] mt-[15px] rounded-xl bg-white px-[13px] py-[18px]">
                <div className="text-bold mb-[6px]">风格选择</div>
                <div className="">
                    <PickAiStyle pickStyle={handlePickStyle} />
                </div>
            </div>
            <footer
                className="fixed bottom-[34px] z-[12] px-[13px]"
                style={{
                    width: `${window.innerWidth - 24}px`,
                }}
            >
                {(currentStatus === GenerationStatus.WAITING || currentStatus === GenerationStatus.GENERATING) && (
                    <div className="h-[50px] w-full rounded-xl border-none bg-white">
                        <Button
                            type="primary"
                            className="h-[50px] w-full rounded-xl border-none text-base font-medium"
                            onClick={handleGenerate}
                            loading={isGenerating}
                        >
                            生成形象
                        </Button>
                    </div>
                )}
                {(currentStatus === GenerationStatus.GENERATED || currentStatus === GenerationStatus.FAILED) && (
                    <div className="flex gap-x-2">
                        <Button
                            className="h-[50px] w-[calc(50%-4px)] flex-1 rounded-xl border-none bg-[#EBECFD] text-base font-medium text-primary"
                            type="text"
                            onClick={handleGenerate}
                        >
                            重新生成
                        </Button>

                        <div className="h-[50px] w-[calc(50%-4px)] rounded-xl border-none bg-white">
                            <Button
                                className="h-[50px] w-full rounded-xl border-none text-base font-medium"
                                type="primary"
                                onClick={handleUseUrl}
                                disabled={isFailed}
                                loading={confirmLoading}
                            >
                                选用
                            </Button>
                        </div>
                    </div>
                )}
            </footer>
        </div>
    );
}
