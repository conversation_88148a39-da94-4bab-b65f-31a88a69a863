/**
 * @file AI生成形象风格选择
 * <AUTHOR>
 * 2024/8/21 AI生成形象风格选择
 */

import classNames from 'classnames';
import {useCallback, useMemo, useState} from 'react';
import {STYLE_OPTIONS} from '@/modules/agentPromptEditV2/components/DigitalFigure/constants';
// 待选队列里一次展示个数
const ONE_ROW_STYLE_NUM = 4;

// 计算一个风格选择框的宽度，每行需要容纳四个
// 计算分析：
// 1. 屏幕宽度 - popup 的 padding * 2
// 2. 再 - 风格选择 div 的 padding * 2
// 3. 再 - 两个风格选择中间的 gap * 3
// 4. 再 / 4
const boxSize: number = (window.innerWidth - 12 * 2 - 13 * 2 - 12 * (ONE_ROW_STYLE_NUM - 1)) / ONE_ROW_STYLE_NUM;

// 文字与高度的比例是 30 : 82
const boxTextLineHeight: number = (boxSize / 82) * 30;
const boxFontSize: number = (boxTextLineHeight / 30) * 14;

const PickAiStyle = ({pickStyle}: {pickStyle: (value: number) => void}) => {
    // 当前选中的索引
    const [selectedIndex, setSelectedIndex] = useState(0);

    const selectedBoxPosition = useMemo(() => {
        return {
            // 计算说明：盒子大小 boxSize + 盒子间距12 - 边框偏移
            left: `calc(${(selectedIndex % ONE_ROW_STYLE_NUM) * (boxSize + 12) - 3}px)`,
            top: `calc(${Math.floor(selectedIndex / ONE_ROW_STYLE_NUM) * (boxSize + 12) - 3}px)`,
        };
    }, [selectedIndex]);
    // 选中设置风格
    const setPickedStyle = useCallback(
        (index: number) => () => {
            // 设置选中的索引
            setSelectedIndex(index);
            pickStyle(STYLE_OPTIONS[index].value);
        },
        [setSelectedIndex, pickStyle]
    );

    return (
        <div className="relative flex items-center justify-between">
            {/* 风格选择容器，一行展示4个风格 */}
            <div className="slider-container flex flex-wrap gap-x-3 gap-y-3">
                {STYLE_OPTIONS.map((option, index) => (
                    <div
                        key={option.key}
                        onClick={setPickedStyle(index)}
                        className="z-[10] flex cursor-pointer flex-col justify-end rounded-[9px]"
                        style={{
                            width: `${boxSize}px`,
                            height: `${boxSize}px`,
                            background: `url(${option.backgroundUrl}) no-repeat top/contain`,
                        }}
                    >
                        <div
                            className={classNames('w-full rounded-b-[9px] text-center', {
                                'bg-[#F2F3FE] font-medium text-primary': index === selectedIndex,
                                'bg-neutral-100': index !== selectedIndex,
                            })}
                            style={{
                                height: `${boxTextLineHeight}px`,
                                lineHeight: `${boxTextLineHeight}px`,
                                fontSize: `${boxFontSize}px`,
                            }}
                        >
                            {option.tag}
                        </div>
                    </div>
                ))}
            </div>
            <div
                className={classNames(
                    'border-highlight absolute z-[11] rounded-[12px] border-[2px] border-primary duration-100'
                )}
                style={{
                    width: `${boxSize + 6}px`,
                    height: `${boxSize + 6}px`,
                    left: selectedBoxPosition.left,
                    top: selectedBoxPosition.top,
                }}
            ></div>
        </div>
    );
};

export default PickAiStyle;
