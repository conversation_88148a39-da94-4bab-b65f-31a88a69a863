/* <AUTHOR>
 * 2023/03/25
 */

import {ConfigProvider, Form} from 'antd';
import {useState, useCallback} from 'react';
import SwitchM from '@/components/mobile/Switch';

import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {FigureType} from '@/api/agentEditV2/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {usePromptViewConfigStore} from '@/store/agent/promptViewConfigStore';
import UploadDigitalFigure from './UploadDigitalFigureV2';
import SyncLogoMobileHoc from './SyncLogoHoc';
import {ResultGuidePopup} from './ResultGuidePopup';

export default function DigitalFigure() {
    const [guidePopupVisible, setGuidePopVisible] = useState(false);
    const {readonly} = usePromptEditContext();
    const required = useUserInfoStore(state => state.userFeatureAccess.role_framework);
    const [figureType, figureUrl, backgroundPicUrl, coverUrl] = usePromptEditStoreV2(store => [
        store.agentConfig.agentInfo.digitalFigure?.figureType,
        store.agentConfig.agentInfo.digitalFigure?.figureUrl,
        store.agentConfig.agentInfo.digitalFigure?.backgroundPicUrl,
        store.agentConfig.agentInfo.digitalFigure?.coverUrl,
    ]);

    const {readOnlyFigureType} = usePromptViewConfigStore(store => ({
        readOnlyFigureType: store.agentConfig.agentInfo.digitalFigure?.figureType,
    }));

    const [activeTab, setActiveTab] = useState<FigureType>(
        (readonly ? readOnlyFigureType : figureType) || FigureType.Static
    );

    // 静态、动态tab切换
    const handleTabChange = useCallback((value: number | string) => {
        setActiveTab(value as FigureType);
    }, []);

    return (
        <ConfigProvider>
            <div className="mt-3 rounded-[12px] bg-white px-[13px] py-[14px] leading-none">
                <div className="mb-[15px] flex items-center justify-between">
                    <div>
                        <span className="text-base font-semibold">背景形象</span>
                        {required && (
                            <span
                                className="mt-1 w-2 flex-shrink-0 pl-1 font-semibold text-error"
                                style={{fontFamily: 'SimSong'}}
                                key="require"
                            >
                                *
                            </span>
                        )}
                        <span
                            className="iconfont icon-questionCircle ml-2 text-base text-gray-400"
                            onClick={() => setGuidePopVisible(true)}
                        ></span>
                    </div>
                    <div>
                        <SwitchM
                            options={[
                                {label: '静态', value: FigureType.Static},
                                {label: '动态', value: FigureType.Dynamic},
                            ]}
                            value={activeTab}
                            disabled={readonly || !!backgroundPicUrl || !!figureUrl || !!coverUrl}
                            onChange={handleTabChange}
                        />
                    </div>
                </div>

                <div className="mt-[15px]">
                    <Form.Item required name={['agentInfo', 'digitalFigure']} className="mb-2">
                        <UploadDigitalFigure figureType={activeTab} />
                    </Form.Item>

                    <div className="mb-[-2px] mt-3 flex items-center">
                        <span className="text-base text-gray-tertiary">自动用作头像</span>
                        <Form.Item noStyle name={['agentInfo', 'logoUrl']}>
                            <SyncLogoMobileHoc />
                        </Form.Item>
                    </div>
                </div>
            </div>
            <ResultGuidePopup visible={guidePopupVisible} setVisible={setGuidePopVisible} figureType={activeTab} />
        </ConfigProvider>
    );
}
