/**
 * @file 数字形象上传 & 裁剪 v2.0
 * <AUTHOR>
 * 2024/8/23 数字形象上传 & 裁剪 v2.0
 */

import {useCallback, useState, useMemo} from 'react';
import {Progress, Modal} from 'antd';

import type {UploadProps} from 'antd/es/upload/interface';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {UseType} from '@/api/agentEdit/interface';
import {AgentDigitalFigure} from '@/api/agentEdit/interface';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {getFigureBackgroundColor, getFigureUrl} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {FigureType, FigureTaskStatus} from '@/api/agentEditV2/interface';
import GeneratingSpin from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/generating-spin.png';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {CharacterSelectType} from '@/utils/loggerV2/interface';
import UploadPopup from './UploadPopup';

// 生成中的加载效果
const GeneratingStyle = styled.div`
    @keyframes rotate {
        to {
            transform: rotate(360deg);
        }
    }

    .custom-spin {
        animation: rotate 1s linear infinite;
    }
`;

// Modal.confirm 弹窗样式改写
const confirmModalStyle = css`
    .ant-modal-content {
        width: 98w;
        height: 142px;
        padding: 24px !important;
    }
`;

interface UploadImageProps extends Omit<UploadProps, 'value' | 'onChange'> {
    value?: AgentDigitalFigure;
    onChange?: (value: AgentDigitalFigure | null) => void;
    useType?: UseType;
    figureType: FigureType;
}

// eslint-disable-next-line complexity
export default function UploadDigitalFigure(props: UploadImageProps) {
    const [loading, setLoading] = useState(false);
    const [loadPercent, setLoadPercent] = useState<number | undefined>(0);
    const [modal, modalContextHolder] = Modal.useModal();

    const [base64ImgUrl, setBase64ImageUrl] = useState<string>('');
    const figureTaskStatus = usePromptEditStoreV2(store => store.display?.figureTaskStatus);

    const waitUpload = useMemo(() => !getFigureUrl(props.value) && !loading, [props.value, loading]);
    const isUploaded = useMemo(() => !!getFigureUrl(props.value) && !loading, [props.value, loading]);
    // 动态数字形象生成中，此时也有figureUrl
    const generating = useMemo(() => {
        return (
            props.figureType === FigureType.Dynamic && isUploaded && figureTaskStatus === FigureTaskStatus.Generating
        );
    }, [props.figureType, isUploaded, figureTaskStatus]);
    // 动态数字形象生成失败
    const generateFailed = useMemo(() => {
        return props.figureType === FigureType.Dynamic && isUploaded && figureTaskStatus === FigureTaskStatus.Failed;
    }, [props.figureType, isUploaded, figureTaskStatus]);

    const [isUploadPopUpVisible, setIsUploadPopUpVisible] = useState(false);

    const {readonly} = usePromptEditContext();
    const appId = usePromptEditStoreV2(state => state.agentConfig.agentInfo.appId);
    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();

    // 手动控制文件选择框的调起
    const draggerAreaClick = useCallback(() => {
        if (readonly) {
            return;
        }

        // 后续抠图时需要appid，触发自动保存获取 appid
        if (!appId) {
            props.onChange && props.onChange(null);
        }

        setIsUploadPopUpVisible(true);

        // 打点
        clickLog(EVENT_VALUE_CONST.CHARACTER_SELECT, EVENT_PAGE_CONST.CODELESS_CREATE, {
            ...extLog,
            [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]:
                props.figureType === FigureType.Dynamic ? CharacterSelectType.DYNAMIC : CharacterSelectType.STATIC,
        });
    }, [readonly, appId, props, clickLog, extLog]);

    // 删除形象二次确认弹窗
    const deleteFigure = useCallback(async () => {
        await modal.confirm({
            icon: <ExclamationCircleOutlined />,
            content: '删除后无法恢复，智能体将不具有形象和打电话功能，确认删除吗？',
            cancelText: '取消',
            okText: '删除',
            centered: true,
            okButtonProps: {
                className:
                    'font-normal h-[32px] w-[60px] absolute bottom-[26px] right-6 flex justify-center items-center',
            },
            cancelButtonProps: {
                className:
                    'font-normal h-[32px] w-[60px] absolute bottom-[26px] right-[92px] flex justify-center items-center',
            },
            autoFocusButton: null,
            className: confirmModalStyle,
            onOk() {
                if (props.value) {
                    props.onChange && props.onChange(null);
                }
            },
            onCancel() {},
        });
    }, [modal, props]);

    const previewImgStyle = {
        background: props.value?.bgUrl
            ? `url(${props.value?.bgUrl}) no-repeat center center / 100% 100%`
            : `${getFigureBackgroundColor(props.value)}`,
    };

    return (
        <>
            <div className="mb-2 flex items-center">
                {/* 删除形象二次确认弹窗 */}
                {modalContextHolder}
                {/* 图片区域 */}
                <div className={classNames('relative flex flex-col items-center')}>
                    <div className="flex h-[72px] w-[72px] items-center justify-center">
                        {/* 图片不存在且未上传中时展示默认图标、上传按钮和提示 */}
                        {!readonly && waitUpload && (
                            <div className="flex h-full w-full flex-col items-center" onClick={draggerAreaClick}>
                                <div className="group relative flex h-full w-full items-center justify-center rounded-xl bg-gray-bg-base active:opacity-20">
                                    <span className="iconfont icon-plus text-[24px] text-gray-quaternary"></span>
                                </div>
                            </div>
                        )}
                        {/* 图片上传时展示图片+百分比 */}
                        {loading && (
                            <div className="group relative flex h-full w-full items-center justify-center overflow-hidden rounded-xl bg-gray-bg-base">
                                <img className="object-cover" src={base64ImgUrl} />
                                {/* 上传进度条 */}
                                <div className="absolute left-0 right-0 flex h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] text-white group-hover:hidden">
                                    <Progress
                                        type="circle"
                                        percent={loadPercent}
                                        strokeColor={ThemeConfig.token.colorPrimary}
                                        trailColor="#FFFFFF"
                                        strokeWidth={16}
                                        size={24}
                                        showInfo={false}
                                    />
                                </div>
                            </div>
                        )}
                        {/* 图片上传成功展示图片 */}
                        {isUploaded && !generating && !generateFailed && (
                            <div className="group relative flex h-full w-full items-center justify-center overflow-hidden rounded-xl bg-gray-bg-base">
                                <img
                                    className="z-[2] h-[72px] w-[72px] object-cover object-top"
                                    src={getFigureUrl(props?.value)}
                                    style={previewImgStyle}
                                    alt="avatar"
                                />

                                {/* 删除图片 */}
                                {!readonly && (
                                    <div
                                        className="absolute left-0 right-0 z-[20] hidden h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] group-hover:flex"
                                        onClick={deleteFigure}
                                    >
                                        <span className="iconfont icon-delete text-[18px] text-white"></span>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* 动态形象生成中展示 */}
                        {generating && !generateFailed && (
                            <div
                                className={classNames(
                                    'group relative mt-[8px] flex items-center justify-center overflow-hidden rounded-lg'
                                )}
                            >
                                <img
                                    className="z-[2] h-[72px] w-[72px] object-cover object-top"
                                    src={getFigureUrl(props?.value)}
                                    style={previewImgStyle}
                                />
                                <div className="absolute left-0 right-0 z-[30] flex h-full w-full flex-col items-center justify-center rounded-[7px] bg-black/[.4] group-hover:hidden">
                                    {/* 生成中等待状态 */}
                                    <GeneratingStyle>
                                        <div className="custom-spin z-[100] h-[18px] w-[18px] rounded-full">
                                            <img src={GeneratingSpin} />
                                        </div>
                                    </GeneratingStyle>
                                    <span className="z-[30] mt-[6px] whitespace-nowrap text-xs font-medium text-white">
                                        生成中
                                    </span>
                                </div>
                                {/* 重新上传图片 */}
                                {!readonly && (
                                    <div
                                        className="absolute left-0 right-0 z-[20] hidden h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] group-hover:flex"
                                        onClick={deleteFigure}
                                    >
                                        <span className="iconfont icon-delete text-[18px] text-white"></span>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* 动态形象生成失败 */}
                        {generateFailed && (
                            <div
                                className={classNames(
                                    'group relative mt-[8px] flex items-center justify-center overflow-hidden rounded-lg border bg-[#f1f1ff]'
                                )}
                            >
                                <img
                                    className="z-[2] h-[72px] w-[72px] object-cover object-top"
                                    src={getFigureUrl(props?.value)}
                                    style={previewImgStyle}
                                />
                                {/* 重新上传图片 */}
                                {!readonly && (
                                    <div
                                        className="absolute left-0 right-0 z-[20] hidden h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] group-hover:flex"
                                        onClick={deleteFigure}
                                    >
                                        <span className="iconfont icon-delete text-[18px] text-white"></span>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
                <UploadPopup
                    visible={isUploadPopUpVisible}
                    setVisible={setIsUploadPopUpVisible}
                    setBase64ImageUrl={setBase64ImageUrl}
                    setLoadPercent={setLoadPercent}
                    setLoading={setLoading}
                    loading={loading}
                    uploadImageProps={props}
                    base64ImgUrl={base64ImgUrl}
                    figureType={props.figureType}
                    appId={appId || ''}
                />
            </div>
            {/* 生成失败文案提示 */}
            {generateFailed && <span className="text-[12px] text-error">动态数字形象生成失败，请重新上传!</span>}
        </>
    );
}
