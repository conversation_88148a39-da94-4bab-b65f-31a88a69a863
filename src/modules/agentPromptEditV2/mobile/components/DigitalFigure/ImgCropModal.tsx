/* eslint-disable complexity */
/**
 * @file 数字形象裁剪弹窗
 * <AUTHOR>
 * 2023/3/28 数字形象——人物形象图片裁剪弹窗
 */

import {useImperativeHandle, useCallback, useState, useRef, forwardRef, useEffect} from 'react';
import {Button, Form} from 'antd';
import useMessage from 'antd/es/message/useMessage';

import cloneDeep from 'lodash/cloneDeep';
import type {UploadProps} from 'antd/es/upload/interface';
import styled from '@emotion/styled';
import Cropper, {ReactCropperElement} from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import {canvasToFile, base64URLtoFormData} from '@/utils/image';
import {uploadDigitalFigure} from '@/api/agentEdit';
import {DigitalType, DigitalCreateStep} from '@/api/agentEdit/interface';
import {figureCheckAndSplit, imageColorPick} from '@/api/agentEditV2';
import {AgentDigitalFigure} from '@/api/agentEdit/interface';
import MobileFullScreen from '@/components/mobile/FullScreen';
import {FigureType} from '@/api/agentEditV2/interface';

import {
    ContextType,
    DigitalFigureContext,
} from '@/modules/agentPromptEditV2/components/DigitalFigure/DigitalFigureContext';
import {getNewDigitalFigure} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {WiseCharacterBoxType, CharacterSelectType} from '@/utils/loggerV2/interface';
import CropRegionMask from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/crop-region-mask.png';
import {Theme} from '@/modules/agentPromptEditV2/mobile/components/DigitalFigure/constants';
import SetBack from './SetBack';

export interface UploadImageProps extends Omit<UploadProps, 'value' | 'onChange'> {
    value?: AgentDigitalFigure;
    onChange?: (value: AgentDigitalFigure) => void;
}

interface ImgCropModalProps {
    props: UploadImageProps;
    base64ImgUrl: string;
    digitalType: number;
    closeGuideModal: () => void;
    onChange?: (value: AgentDigitalFigure) => void;
    figureType: FigureType;
    getDigitalFigure?: (digitalFigure: AgentDigitalFigure) => void;
    theme?: Theme;
    appId: string;
}

const ImgCropModalContainer = styled.div`
    height: calc(100 * var(--dvh) - 44px);

    .crop-area {
        margin-top: calc(10 * var(--dvh));
    }

    .setBack-finish-btn {
        margin-bottom: 24px;
    }
`;

const StyledCropper = styled(Cropper)`
    &.other {
        .cropper-view-box {
            outline: 2px solid #fff;
        }
    }
    &.character {
        .cropper-view-box {
            outline: none;
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;

                background-image: url(${CropRegionMask});
                background-size: cover;
                background-repeat: no-repeat;
                background-position: top;
            }
        }
    }
`;

interface CropBoxSize {
    width: number;
    height: number;
}

// 裁剪框距离屏幕左右间距 17px
const defaultCropBoxSize: Record<number, CropBoxSize> = {
    [DigitalType.CHARACTER]: {
        width: window.innerWidth - 17 * 2,
        height: ((window.innerWidth - 17 * 2) * 4) / 3,
    },
    [DigitalType.OTHERS]: {
        width: window.innerWidth - 17 * 2,
        height: ((window.innerWidth - 17 * 2) * 3) / 4,
    },
};

const ImgCropModal = forwardRef(
    (
        {
            props,
            base64ImgUrl,
            digitalType,
            closeGuideModal,
            onChange,
            figureType,
            getDigitalFigure,
            theme,
            appId,
        }: ImgCropModalProps,
        ref
    ) => {
        const cropperRef = useRef<ReactCropperElement>(null);

        const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
        const [cropLoading, setCropLoading] = useState<boolean>(false);
        const [cropModalOpen, setCropModalOpen] = useState<boolean>(false);
        const abortControllerRef = useRef<AbortController | null>(null);

        const [messageApi, MessageContext] = useMessage();
        const {clickLog} = useUbcLogV2();
        const extLog = useExtLog();

        // 上下文数据
        const [digitalFigureContext, setDigitalFigureContext] = useState<ContextType>({
            splitedImage: '',
            originalImage: base64ImgUrl,
            currentSetp: DigitalCreateStep.OnceCrop,
            figureType: figureType,
        });

        const originalImage = base64ImgUrl || digitalFigureContext.originalImage;

        useEffect(() => {
            // 打开弹窗时如果之前有，就清楚前一个,保证只有一个裁剪DOM
            const imgCropDomArr = document.getElementsByClassName('custom-img-crop-modal');
            if (imgCropDomArr.length > 1) {
                imgCropDomArr[0].remove();
            }
        }, []);

        // 【静态】裁剪弹窗的抠图按钮
        const handleSplit = useCallback(async () => {
            setCropLoading(true);
            cropperRef.current?.cropper.disable();
            const digitalFigure = cloneDeep(props.value);
            abortControllerRef.current = new AbortController();

            try {
                // 获取裁剪后的图片
                // const canvas = cropperRef.current?.cropper.getCroppedCanvas()!;
                // const imgFile = await canvasToFile(canvas);

                // 直接选用原图
                const imgFile = await base64URLtoFormData(base64ImgUrl, 'image');
                const imgUrl = await uploadDigitalFigure(imgFile, abortControllerRef.current?.signal);
                const imgUrlList = [imgUrl];

                // 调用接口质检+抠图
                const res = await figureCheckAndSplit(
                    {
                        imgUrlList,
                        appId,
                        figureType: FigureType.Static,
                    },
                    abortControllerRef.current?.signal
                );
                // 将抠好的图通过上下文传递到背景选择组件，不在这里存。背景没有选择未完成全流程，不存数据。
                setDigitalFigureContext({
                    ...digitalFigureContext,
                    splitedImage: res.foregroundImageUrl,
                    currentSetp: DigitalCreateStep.SetBackground,
                    figureType: FigureType.Static,
                });
                setCropLoading(false);
                cropperRef.current?.cropper.enable();
            } catch (e) {
                props.onChange && props.onChange(digitalFigure!);
                setCropLoading(false);
                setCropModalOpen(false);
                cropperRef.current?.cropper.enable();
                throw e;
            } finally {
                clickLog(EVENT_VALUE_CONST.CHARACTER_BOX_NEXT, EVENT_PAGE_CONST.CODELESS_CREATE, {
                    ...extLog,
                    [EVENT_EXT_KEY_CONST.C_CHARACTER_BOX_TYPE]: WiseCharacterBoxType.IMG_CROP_MODAL,
                    [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]: CharacterSelectType.STATIC,
                });

                closeGuideModal();
            }
        }, [appId, props, digitalFigureContext, closeGuideModal, base64ImgUrl, clickLog, extLog]);

        // 【静态】直接选用
        const handleUseUrl = useCallback(async () => {
            setConfirmLoading(true);
            let digitalFigure = cloneDeep(props.value);
            try {
                const canvas = cropperRef.current?.cropper.getCroppedCanvas()!;
                const imgFile = await canvasToFile(canvas);
                const imgUrl = await uploadDigitalFigure(imgFile);
                const color = await imageColorPick(imgUrl);
                digitalFigure = getNewDigitalFigure(imgUrl, color, false);
                onChange && onChange(digitalFigure);
                setCropModalOpen(false);
                closeGuideModal();
            } catch (error) {
                throw error;
            } finally {
                setConfirmLoading(false);
            }
        }, [props.value, onChange, closeGuideModal]);

        // 【动态】 调取质检+抠图接口
        const handleCheckAndSplit = useCallback(async () => {
            setCropLoading(true);
            abortControllerRef.current = new AbortController();
            try {
                // 获取裁剪后的图片
                const canvas = cropperRef.current?.cropper.getCroppedCanvas()!;
                const imgFile = await canvasToFile(canvas);
                const imgUrl = await uploadDigitalFigure(imgFile, abortControllerRef.current?.signal);
                const imgUrlList = [imgUrl];

                // 调用接口质检+抠图
                const res = await figureCheckAndSplit(
                    {
                        imgUrlList,
                        appId,
                        figureType: FigureType.Dynamic,
                    },
                    abortControllerRef.current?.signal
                );
                // 质检通过
                if (res.errno === 0) {
                    // 将抠好的图通过上下文传递到背景选择组件，不在这里存。背景没有选择未完成全流程，不存数据。
                    setDigitalFigureContext({
                        ...digitalFigureContext,
                        splitedImage: res.foregroundImageUrl,
                        // taskUuid: res.taskUuid,
                        currentSetp: DigitalCreateStep.SetBackground,
                        figureType: FigureType.Dynamic,
                    });
                } else {
                    // 质检失败
                    messageApi.error(res.msg);
                }
            } catch (e) {
                throw e;
            } finally {
                clickLog(EVENT_VALUE_CONST.CHARACTER_BOX_NEXT, EVENT_PAGE_CONST.CODELESS_CREATE, {
                    ...extLog,
                    [EVENT_EXT_KEY_CONST.C_CHARACTER_BOX_TYPE]: WiseCharacterBoxType.IMG_CROP_MODAL,
                    [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]: CharacterSelectType.DYNAMIC,
                });

                setCropLoading(false);
                closeGuideModal();
            }
        }, [appId, digitalFigureContext, messageApi, closeGuideModal, clickLog, extLog]);

        // 对外暴露打开弹窗事件
        useImperativeHandle(ref, () => {
            return {
                showModal() {
                    setCropModalOpen(true);
                },
                closeModal() {
                    setCropModalOpen(false);
                },
                open: cropModalOpen,
            };
        });

        // 裁剪弹窗的取消按钮
        const handleBack = useCallback(() => {
            if (cropLoading) {
                return;
            }

            if (digitalFigureContext.currentSetp === DigitalCreateStep.OnceCrop) {
                setCropModalOpen(false);
                // 重置上下文数据
                setDigitalFigureContext({
                    splitedImage: '',
                    originalImage: '',
                    currentSetp: DigitalCreateStep.OnceCrop,
                    figureType: FigureType.Static,
                });
            } else {
                setDigitalFigureContext({
                    splitedImage: '',
                    originalImage,
                    currentSetp: DigitalCreateStep.OnceCrop,
                    figureType: FigureType.Static,
                });
            }
        }, [cropLoading, digitalFigureContext.currentSetp, originalImage]);

        // 给子组件传递裁剪弹窗关闭事件
        const modalOnClose = useCallback(() => setCropModalOpen(false), []);

        const guideText =
            figureType === FigureType.Static
                ? '调整图片大小位置，确保图片效果'
                : `调整图片大小，建议主要元素位于人像提示框内，避免肩膀两侧被裁剪`;

        const fullScreenTitle =
            digitalFigureContext.currentSetp === DigitalCreateStep.OnceCrop ? '预览图片' : '选择背景预览';

        const cropperContainerRef = useRef<HTMLDivElement>(null);

        const [padding, setPadding] = useState(17);
        const [cropBoxSize, setCropBoxSize] = useState(defaultCropBoxSize[digitalType]);
        const handleResize = useCallback(() => {
            // if (window.innerWidth > 430) {
            //     setPadding(56);
            // } else if (window.innerHeight < 700) {
            //     setPadding(56);
            // } else {
            //     setPadding(17);
            // }

            // 设为宽度为屏幕宽度的75%
            const width = window.innerWidth * 0.75;
            setPadding((window.innerWidth - width) / 2);
            const height = digitalType === DigitalType.CHARACTER ? (width * 4) / 3 : (width * 3) / 4;

            setCropBoxSize({width, height});
        }, [digitalType]);

        useEffect(() => {
            handleResize();
        }, [digitalType, handleResize]);

        useEffect(() => {
            window.addEventListener('resize', handleResize);

            return () => {
                window.removeEventListener('resize', handleResize);
            };
        }, [handleResize]);

        return (
            <MobileFullScreen show={cropModalOpen} title={fullScreenTitle} onBack={handleBack}>
                {MessageContext}
                {digitalFigureContext.currentSetp === DigitalCreateStep.OnceCrop ? (
                    <ImgCropModalContainer className="relative flex w-full flex-col items-center">
                        <div className="crop-area flex flex-col items-center">
                            <span className="mb-[18px] w-[250px] text-center text-sm font-medium">{guideText}</span>
                            <div
                                ref={cropperContainerRef}
                                className="relative w-full"
                                style={{
                                    padding: `0 ${padding}px`,
                                }}
                            >
                                <StyledCropper
                                    ref={cropperRef}
                                    className={digitalType === DigitalType.CHARACTER ? 'character' : 'other'}
                                    viewMode={3}
                                    dragMode="move"
                                    src={base64ImgUrl || digitalFigureContext.originalImage}
                                    center={false}
                                    cropBoxMovable={false}
                                    cropBoxResizable={false}
                                    style={{
                                        ...cropBoxSize,
                                    }}
                                    initialAspectRatio={9 / 16}
                                    aspectRatio={9 / 16}
                                    guides={false}
                                    toggleDragModeOnDblclick={false}
                                    autoCropArea={1}
                                    background={false}
                                />
                            </div>
                        </div>

                        <div className="absolute bottom-[24px] w-full px-9 text-base font-medium">
                            {figureType === FigureType.Static ? (
                                <div className="flex gap-x-2">
                                    <div className="h-[50px] w-[calc(50%-4px)] rounded-xl border-none bg-white">
                                        <Button
                                            className="h-[50px] w-full rounded-xl border-none bg-[#EBECFD] text-base font-medium text-primary"
                                            type="text"
                                            onClick={handleSplit}
                                            loading={cropLoading}
                                        >
                                            抠图
                                        </Button>
                                    </div>
                                    <div className="h-[50px] w-[calc(50%-4px)] rounded-xl border-none bg-white">
                                        <Button
                                            className="h-[50px] w-full rounded-xl border-none text-base font-medium"
                                            type="primary"
                                            onClick={handleUseUrl}
                                            disabled={cropLoading}
                                            loading={confirmLoading}
                                        >
                                            选用
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <Button
                                    className="h-[50px] w-full rounded-full border-none text-base font-medium"
                                    type="primary"
                                    onClick={handleCheckAndSplit}
                                    loading={cropLoading}
                                >
                                    下一步
                                </Button>
                            )}
                        </div>
                    </ImgCropModalContainer>
                ) : (
                    <ImgCropModalContainer className="flex h-full w-full flex-col items-center justify-between">
                        <DigitalFigureContext.Provider value={digitalFigureContext}>
                            <Form.Item noStyle name={['agentInfo', 'digitalFigure']}>
                                <SetBack
                                    data={props.value!}
                                    onClose={modalOnClose}
                                    originalImage={base64ImgUrl || digitalFigureContext.originalImage}
                                    setDigitalFigureContext={setDigitalFigureContext}
                                    figureType={figureType}
                                    getDigitalFigure={getDigitalFigure}
                                    theme={theme}
                                    appId={appId}
                                />
                            </Form.Item>
                        </DigitalFigureContext.Provider>
                    </ImgCropModalContainer>
                )}
            </MobileFullScreen>
        );
    }
);
export default ImgCropModal;
