/**
 * @file 引导上传数字形象的弹窗
 * <AUTHOR>
 */

import {useCallback, useRef, useState} from 'react';
import {Button, Form, Upload, UploadFile} from 'antd';
import useMessage from 'antd/es/message/useMessage';
import {UploadChangeParam} from 'antd/es/upload';
import {RcFile} from 'rc-upload/lib/interface';
import styled from '@emotion/styled';
import classNames from 'classnames';
import {AgentDigitalFigure, DigitalType} from '@/api/agentEdit/interface';
import PopupM from '@/components/mobile/Popup';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import CONST from '@/dicts/home';

import {getBase64, getImgWH} from '@/utils/image';
import {FigureType} from '@/api/agentEditV2/interface';
import charactermobile0 from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/character-mobile-0.png';
import character1 from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/character-1.png';
import character2 from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/character-2.png';
import character3 from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/character-3.png';
import character4 from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/character-4.png';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {CharacterSelectType} from '@/utils/loggerV2/interface';
import Scrollbar from '@/components/Scrollbar';
import {Theme} from '@/modules/agentPromptEditV2/mobile/components/DigitalFigure/constants';
import ImgCropModal, {UploadImageProps} from './ImgCropModal';
import {scrollALittle} from './hack';
import AiGeneratePopup from './AiGeneratePopup';

const UploadSelectContainer = styled.div`
    .ant-upload-select {
        display: block !important;
    }
`;

interface UploadPopupProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    setLoading: (loading: boolean) => void;
    setLoadPercent: (percent: number) => void;
    setBase64ImageUrl: (url: string) => void;
    loading?: boolean;
    uploadImageProps: UploadImageProps;
    base64ImgUrl: string;
    figureType: FigureType;
    theme?: Theme;
    getDigitalFigure?: (digitalFigure: AgentDigitalFigure) => void;
    appId: string;
}

export default function UploadPopup({
    visible,
    setVisible,
    setLoading,
    setLoadPercent,
    setBase64ImageUrl,
    base64ImgUrl,
    uploadImageProps,
    loading,
    figureType,
    getDigitalFigure,
    theme = Theme.DEFAULT,
    appId,
}: UploadPopupProps) {
    const [showAIGenerate, setShowAIGenerate] = useState<boolean>(false);

    const handleClose = useCallback(() => {
        if (loading) {
            return;
        }

        setVisible(false);
        setShowAIGenerate(false);
        scrollALittle();
    }, [loading, setVisible]);

    const imgCropModalRef = useRef<any>(null);

    const [messageApi, MessageContext] = useMessage();
    const {clickLog} = useUbcLogV2();

    const extLog = useExtLog();

    const {readonly} = usePromptEditContext();

    const onChange = useCallback(
        // eslint-disable-next-line complexity
        async (info: UploadChangeParam<UploadFile>) => {
            if (info.file.status === 'uploading') {
                setLoading(true);

                const percent = Math.min(Math.floor(info.file.percent || 0), 99);
                setLoadPercent(percent);
                getBase64(info.file.originFileObj as RcFile).then(url => setBase64ImageUrl(url));
            } else if (info.file.status === 'done') {
                handleClose();
                // 打开图片裁剪弹窗
                imgCropModalRef?.current?.showModal();
                setLoading(false);
            } else if (info.file.status === 'error') {
                setLoading(false);
                messageApi.error('图片上传失败，请重新上传!');
            }
        },
        [imgCropModalRef, messageApi, setBase64ImageUrl, setLoadPercent, setLoading, handleClose]
    );

    const beforeUpload = useCallback(
        async (file: RcFile) => {
            const isImageFormat = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'].some(
                (type: string) => file.type === type
            );
            if (!isImageFormat) {
                messageApi.error('图片格式仅支持png、jpeg、jpg、webp，请重新上传!');
                return false;
            }

            // 通过bos自动裁切&压缩图片，参考文档：https://cloud.baidu.com/doc/BOS/s/Yldh5wq5b
            const isSizeLimit = file.size < 20 * 1024 * 1024;
            if (!isSizeLimit) {
                messageApi.error('图片应该小于20M，请重新上传!');
                return false;
            }

            const url = await getBase64(file);
            const {width, height} = await getImgWH(url);
            if (width > 30000 || height > 30000) {
                messageApi.error('图片大于30000像素*30000像素，请重新上传!');
                return false;
            }

            if (width < 400 || height < 400) {
                messageApi.error('图片小于400像素*400像素，请重新上传!');
                return false;
            }

            return isSizeLimit;
        },
        [messageApi]
    );

    const showAiGenerate = useCallback(() => {
        setShowAIGenerate(true);
    }, []);

    // 相册上传 按钮点击
    const handleUpload = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.CHARACTER_BOX_UPLPIC, EVENT_PAGE_CONST.CODELESS_CREATE, {
            ...extLog,
            [EVENT_EXT_KEY_CONST.C_CHARACTER_SELECT_TYPE]:
                figureType === FigureType.Dynamic ? CharacterSelectType.DYNAMIC : CharacterSelectType.STATIC,
        });
    }, [clickLog, extLog, figureType]);

    return (
        <PopupM
            title={showAIGenerate ? 'AI生成' : ''}
            visible={visible}
            onClose={handleClose}
            background="#F5F6F9"
            closeOnMaskClick
            showCloseButton={showAIGenerate}
            bodyStyle={{
                minHeight: '0',
                maxHeight: '90vh',
                paddingInline: 0,
            }}
        >
            <div className="w-full">
                {!readonly && showAIGenerate ? (
                    <Form.Item noStyle name={['agentInfo', 'digitalFigure']}>
                        <AiGeneratePopup data={uploadImageProps.value!} onClose={handleClose} />
                    </Form.Item>
                ) : (
                    !readonly &&
                    (figureType === FigureType.Static ? (
                        <UploadSelectContainer className="w-full px-3 pt-[15px]">
                            <Button
                                type="text"
                                className="mb-2 h-[50px] w-full rounded-xl border-none bg-white text-base font-medium text-primary"
                                disabled={loading}
                                onClick={showAiGenerate}
                            >
                                <span className="iconfont icon-AI mr-1"></span>
                                AI生成
                            </Button>
                            <Upload
                                name="image"
                                action={CONST.UPLOAD_DIGITAL_FIGTURE_ACTION}
                                showUploadList={false}
                                beforeUpload={beforeUpload}
                                onChange={onChange}
                                style={{border: 'none'}}
                                accept="image/jpeg,image/png,image/jpg,image/webp"
                                className="block w-full"
                            >
                                <Button
                                    className="mb-2 h-[50px] w-full rounded-xl border-none bg-white text-base font-medium"
                                    type="text"
                                    loading={loading}
                                    disabled={loading}
                                    onClick={handleUpload}
                                >
                                    相册上传
                                </Button>
                            </Upload>
                            <Button
                                className="mb-[34px] h-[50px] w-full rounded-xl border-none bg-white text-base font-medium"
                                type="text"
                                onClick={handleClose}
                                disabled={loading}
                            >
                                取消
                            </Button>
                        </UploadSelectContainer>
                    ) : (
                        <UploadSelectContainer
                            className={classNames('flex w-full flex-col items-center justify-center px-3 py-6')}
                        >
                            <div className="text-lg font-medium leading-none">动态形象</div>
                            <Scrollbar
                                className={classNames('mt-[18px] overflow-y-auto rounded-xl px-[13px] py-6', {
                                    'max-h-[calc(90vh-160px)] bg-white': true,
                                })}
                            >
                                <p className="text-base font-semibold leading-4">正反示例</p>
                                <p className="mt-[9px] text-sm text-gray-tertiary">
                                    建议上传竖图，分辨率不低于900*1600px，大小20M以下
                                </p>
                                <div className="mt-[18px] grid grid-cols-3 justify-items-center gap-4 text-sm text-gray-tertiary">
                                    <div className="w-full text-center">
                                        <img className="w-full" src={charactermobile0} />
                                        <span>正对着镜头</span>
                                    </div>
                                    <div className="w-full text-center">
                                        <img className="w-full" src={character1} />
                                        <span>露出牙齿</span>
                                    </div>
                                    <div className="w-full text-center">
                                        <img className="w-full" src={character2} />
                                        <span>两侧被裁剪</span>
                                    </div>
                                    <div className="w-full text-center">
                                        <img className="w-full" src={character3} />
                                        <span>多人照片</span>
                                    </div>
                                    <div className="w-full text-center">
                                        <img className="w-full" src={character4} />
                                        <span>半身露出手</span>
                                    </div>
                                </div>
                            </Scrollbar>
                            <Upload
                                name="image"
                                action={CONST.UPLOAD_DIGITAL_FIGTURE_ACTION}
                                showUploadList={false}
                                beforeUpload={beforeUpload}
                                onChange={onChange}
                                style={{border: 'none'}}
                                accept="image/jpeg,image/png,image/jpg,image/webp"
                                className={classNames('block w-full px-[14px]')}
                            >
                                <Button
                                    className="mb-8 mt-6 h-[50px] w-full rounded-full border-none text-base font-medium"
                                    type="primary"
                                    loading={loading}
                                    disabled={loading}
                                    onClick={handleUpload}
                                >
                                    相册上传
                                </Button>
                            </Upload>
                        </UploadSelectContainer>
                    ))
                )}
            </div>
            {MessageContext}
            <Form.Item noStyle name={['agentInfo', 'digitalFigure']}>
                <ImgCropModal
                    ref={imgCropModalRef}
                    props={uploadImageProps}
                    base64ImgUrl={base64ImgUrl}
                    digitalType={DigitalType.CHARACTER}
                    // 不加 key 会报错，原因不明
                    key={'_upload_digital_figure_modal'}
                    closeGuideModal={handleClose}
                    figureType={figureType}
                    getDigitalFigure={getDigitalFigure}
                    theme={theme}
                    appId={appId}
                />
            </Form.Item>
        </PopupM>
    );
}
