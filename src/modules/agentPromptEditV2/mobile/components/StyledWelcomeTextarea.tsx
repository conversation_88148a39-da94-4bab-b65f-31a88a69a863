import styled from '@emotion/styled';
import {Input} from 'antd';

export const StyledWelcomeTextarea = styled(Input.TextArea)`
    &.ant-input {
        border: none !important;
        border-radius: 12px !important;
        background: #f5f6f9;
        padding: 12px 10px !important;
        height: 66px !important;
    }
    & {
        padding-top: 12px;
        background: #f5f6f9 !important;
    }
    &::-webkit-scrollbar {
        background: transparent;
        width: 4px;
    }
    &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 5px;
    }
    &:hover {
        background: #f5f6f9;
    }
`;
