/**
 * @file 声音选择配置项
 *
 */

import {Form} from 'antd';
import Audio from '@/modules/agentPromptEditV2/mobile/components/Audio/index';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';

export default function AudioSelect() {
    const {speechInvalid, setSpeechInvalid} = usePromptEditStoreV2(store => ({
        speechInvalid: store.speechInvalid,
        setSpeechInvalid: store.setSpeechInvalid,
    }));
    const {readonly} = usePromptEditContext();

    return (
        <Form.Item noStyle name={['agentInfo', 'speech']}>
            <Audio disabled={readonly} speechInvalid={speechInvalid} setSpeechInvalid={setSpeechInvalid} />
        </Form.Item>
    );
}
