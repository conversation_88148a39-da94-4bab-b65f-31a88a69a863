/**
 * @file 联网搜索组件
 * <AUTHOR>
 */
import {ConfigProvider, Form, Switch} from 'antd';
import styled from '@emotion/styled';
import ThemeConfig from '@/styles/lingjing-light-theme';

const SwitchContainer = styled.div`
    .ant-switch-checked:hover:not(.ant-switch-disabled) {
        background-color: ${ThemeConfig.token.colorPrimary} !important;
    }

    .ant-switch {
        background-color: rgba(0, 0, 0, 0.2);
    }

    .ant-switch:hover:not(.ant-switch-disabled) {
        background-color: rgba(0, 0, 0, 0.2);
    }
`;

export default function AutoSuggestion() {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Switch: {
                        trackMinWidth: 36,
                        trackHeight: 20,
                        handleSize: 14,
                        trackPadding: 3,
                    },
                },
            }}
        >
            <SwitchContainer className="mt-3 rounded-[12px] bg-white px-[13px] py-[14px] leading-none">
                <div className="flex items-center justify-between">
                    <div className="flex-grow text-base font-semibold">联网搜索</div>
                    <Form.Item noStyle name={['agentJson', 'webSearch', 'isEnabled']}>
                        <Switch className="ml-2" />
                    </Form.Item>
                </div>
                <div className="mt-2 text-gray-tertiary">智能体将在需要时自动搜索最新的全网信息</div>
            </SwitchContainer>
        </ConfigProvider>
    );
}
