import React from 'react';
import ModalM from '@/components/mobile/Modal';

export default function SkipModalComp({
    confirmOpen,
    cancelGen,
    waitForGen,
}: {
    confirmOpen: boolean;
    cancelGen: () => void;
    waitForGen: () => void;
}) {
    return (
        <ModalM open={confirmOpen} okText="等待" cancelText="仍要跳过" onCancel={cancelGen} onOk={waitForGen}>
            <p className="ml-[38px] text-black opacity-90">若跳过则舍弃已生成的智能体配置</p>
        </ModalM>
    );
}
