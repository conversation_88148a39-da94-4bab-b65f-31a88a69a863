import type {FC} from 'react';
import React, {Fragment, memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Form, Popover, Spin} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import {CheckOutlined} from '@ant-design/icons';
import <PERSON><PERSON> from 'lottie-react';
import classNames from 'classnames';
import {AudioInfo, fetchSpeechData, previewSpeech, SpeechStatus, SpeechType} from '@/api/agentEditV2';
import AudioPlayer from '@/utils/audio-player';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import PopupM from '@/components/mobile/Popup';
import getPlayAudioJSON, {PlayAudioImgSource} from '@/modules/home/<USER>/play-audio';
import Card from '@/components/mobile/Card';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {AudioTheme} from '@/modules/agentPromptEditV2/mobile/components/Audio/interface';
import {StyledButton} from '@/components/mobile/Popup';
import Tag from '@/components/Tag';
import Loading from '@/components/Loading';
import LoadError from '@/components/Loading/LoadError';
import CreateAudioPopup from '@/modules/agentPromptEditV2/mobile/components/Audio/CreateAudioPopup';
import {AgentSpeech} from '@/api/agentEdit/interface';
import {DEFAULT_AUDIO_CONF} from '@/modules/agentPromptEditV2/pc/components/Audio/constant';

interface AudioProps {
    /** 声音是否生成失败 */
    speechInvalid: boolean;
    /** 设置声音是否生成失败 */
    setSpeechInvalid: (value: boolean) => void;
    value?: AgentSpeech;
    onChange?: (value?: AgentSpeech) => void;
    disabled?: boolean;
    /** 配置项卡片样式 */
    cardClassName?: string;
    /** 卡片标题 */
    cardTitle?: string;
    /** 主题 */
    theme?: AudioTheme;
}

interface GroupOptionsAudioInfo extends AudioInfo {
    selected?: boolean;
}

interface GroupOptions {
    speechType: SpeechType;
    label: string | React.ReactNode;
    options: GroupOptionsAudioInfo[];
}

enum RefreshStatus {
    REFRESHING = 'refreshing',
    REFRESHED = 'refreshed',
    REFRESH_FAILED = 'refreshFailed',
}

interface SpeechGroupStatusProps {
    speechType: SpeechType;
    refreshStatus: RefreshStatus;
}

/**
 * 声音列表刷新状态组件
 *
 */
function SpeechGroupStatus({speechType, refreshStatus}: SpeechGroupStatusProps) {
    if (speechType !== SpeechType.UserSpeech) {
        return null;
    }

    if (refreshStatus === RefreshStatus.REFRESHING) {
        return (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
                <Loading />
            </div>
        );
    }

    if (refreshStatus === RefreshStatus.REFRESH_FAILED) {
        return (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
                <LoadError className="h-full" tips="刷新失败，请重试" hideBtn size="small" />
            </div>
        );
    }

    return null;
}

/**
 * 声音操作区（创建 & 刷新）
 */
function AudioOperationArea({
    openCreateAudioPopup,
    refreshAudioList,
}: {
    openCreateAudioPopup: () => void;
    refreshAudioList: () => void;
}) {
    return (
        <section className="flex items-center gap-6 text-sm text-colorTextDefault">
            <div className="flex items-center gap-[3px]" onClick={openCreateAudioPopup}>
                <span className="iconfont icon-add" />
                <span>创建</span>
            </div>
            <div className="flex items-center gap-[3px]" onClick={refreshAudioList}>
                <span className="iconfont icon-change" />
                <span>刷新</span>
            </div>
        </section>
    );
}

/**
 * 确认按钮
 */
function ConfirmButton({onConfirm, disabled}: {onConfirm: () => void; disabled: boolean}) {
    return (
        <StyledButton
            key="default-theme-confirm"
            type="primary"
            className="h-[50px] flex-1 rounded-xl text-base font-medium"
            onClick={onConfirm}
            disabled={disabled}
        >
            确认
        </StyledButton>
    );
}

const Audio: FC<AudioProps> = memo(
    // eslint-disable-next-line max-statements
    ({
        speechInvalid,
        setSpeechInvalid,
        value,
        onChange,
        disabled = false,
        theme = AudioTheme.DEFAULT,
        cardClassName,
        cardTitle = '声音',
    }) => {
        const [audioList, setAudioList] = useState<AudioInfo[]>([]);
        const [showPopup, setShowPopup] = useState<boolean>(false);
        const [playSpeechId, setPlaySpeechId] = useState<AudioInfo['id'] | null>();
        const [isPause, setIsPause] = useState(true);
        const [refreshStatus, setRefreshStatus] = useState<RefreshStatus>(RefreshStatus.REFRESHED);
        const [currentSelectedValue, setCurrentSelectedValue] = useState<AudioInfo | undefined>();
        const [isFetchingPreview, setIsFetchingPreview] = useState(false);
        const {current: audioPlayer} = useRef(new AudioPlayer(() => setPlaySpeechId(null)));
        const createAudioPopupRef = useRef<{showPopup: () => void} | null>(null);
        const popupContainerRef = useRef<HTMLDivElement>(null);
        const {readonly} = usePromptEditContext();

        const speech = Form.useWatch(['agentInfo', 'speech']);

        const defaultTheme = theme === AudioTheme.DEFAULT;

        const {ubcClickLog} = useUbcLog();

        const groupOptions = useMemo<GroupOptions[] | undefined>(() => {
            if (!audioList?.length) return;
            const result = audioList.reduce<{[index: number]: GroupOptions}>(
                (groupOptions, item) => {
                    // 使用 speechType 作为 key 分组，向分组添加当前项目
                    groupOptions[item.speechType].options.push(item);

                    return groupOptions;
                },
                {
                    [SpeechType.UserSpeech]: {
                        speechType: SpeechType.UserSpeech,
                        options: [],
                        label: '我的声音',
                    },
                    [SpeechType.OfficialSpeech]: {
                        speechType: SpeechType.OfficialSpeech,
                        options: [],
                        label: '官方声音',
                    },
                }
            );

            // 将对象的值转换为数组，每个元素包含 groupName 和 children
            return Object.values(result);
        }, [audioList]);

        const playAudio = useCallback(
            async ({id, speechName, mid, ttsId, speechType, vol, spd, pit}: GroupOptionsAudioInfo) => {
                if (speechType === SpeechType.UserSpeech) {
                    if (isFetchingPreview) return;
                    setIsFetchingPreview(true);
                }

                try {
                    setPlaySpeechId(id);

                    const {previewUrl} = await previewSpeech({
                        mid: mid,
                        ttsId: ttsId,
                        speechType: speechType,
                        spd: spd ?? DEFAULT_AUDIO_CONF.spd,
                        pit: pit ?? DEFAULT_AUDIO_CONF.pit,
                        vol: vol ?? DEFAULT_AUDIO_CONF.vol,
                    });

                    const judgeIsPause = await audioPlayer.play(previewUrl, id);
                    setIsPause(judgeIsPause);

                    ubcClickLog(EVENT_TRACKING_CONST.ZeroCreatePlay, {
                        [EVENT_EXT_KEY_CONST.SOUND_ID]: mid || ttsId,
                        [EVENT_EXT_KEY_CONST.SOUND_NAME]: speechName,
                    });
                } finally {
                    if (speechType === SpeechType.UserSpeech) {
                        setIsFetchingPreview(false);
                    }
                }
            },
            [audioPlayer, ubcClickLog, isFetchingPreview]
        );

        const handleSelect = useCallback(
            (option: GroupOptionsAudioInfo) => {
                if (option.status === SpeechStatus.Failed) {
                    return;
                }

                setCurrentSelectedValue(option);
                if (option.speechType === SpeechType.OfficialSpeech || option.status === SpeechStatus.Success) {
                    playAudio(option);
                } else {
                    audioPlayer.stop();
                }
            },
            [playAudio, audioPlayer]
        );

        const openPopup = useCallback(() => {
            !disabled && setShowPopup(true);
        }, [disabled]);

        const closePopup = useCallback(() => {
            audioPlayer.stop();
            setShowPopup(false);
            setCurrentSelectedValue(undefined);
        }, [setShowPopup, audioPlayer, setCurrentSelectedValue]);

        const onConfirm = useCallback(() => {
            audioPlayer.stop();
            currentSelectedValue &&
                onChange?.({
                    speechId: currentSelectedValue.id,
                    ttsId: currentSelectedValue.ttsId,
                    mid: currentSelectedValue.mid,
                    speechName: currentSelectedValue.speechName,
                    speechSource: currentSelectedValue.speechSource,
                    spd: currentSelectedValue.spd,
                    pit: currentSelectedValue.pit,
                    vol: currentSelectedValue.vol,
                });
            setShowPopup(false);

            ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateSound, {
                [EVENT_EXT_KEY_CONST.SOUND_ID]: currentSelectedValue?.mid || currentSelectedValue?.ttsId,
                [EVENT_EXT_KEY_CONST.SOUND_NAME]: currentSelectedValue?.speechName,
            });
        }, [currentSelectedValue, onChange, audioPlayer, ubcClickLog]);

        // 获取音频列表
        useEffect(() => {
            fetchSpeechData().then(res => setAudioList(res));
        }, []);

        const refreshAudioList = useCallback(() => {
            setRefreshStatus(RefreshStatus.REFRESHING);

            fetchSpeechData()
                .then(res => {
                    setAudioList(res);
                    setRefreshStatus(RefreshStatus.REFRESHED);
                })
                .catch(() => {
                    setRefreshStatus(RefreshStatus.REFRESH_FAILED);
                });
        }, []);

        // 1. 设置 currentSelectedAudio，同步当前列表的选中状态和表单底部无效状态
        // 2. 从生成中状态变成生成成功时会缺少 mid 字段，因此需要自动触发保存，将 mid 字段补全
        // eslint-disable-next-line complexity
        useEffect(() => {
            if (readonly) {
                return;
            }

            if (!value?.ttsId || !audioList?.length) {
                setSpeechInvalid(false);
                return;
            }

            // value 中的数据不完整，因此从 audioList 中找出完整数据
            const currentSelectedValue = audioList.find(item =>
                value.speechId ? item.id === value.speechId : item.ttsId === value.ttsId
            );
            setCurrentSelectedValue(currentSelectedValue);
            // 如果当前 value 在 audioList 中不存在，将当前表单数据中的语音相关字段置空
            if (!currentSelectedValue) {
                setCurrentSelectedValue(undefined);
            } else if (!value?.mid && value?.speechId === currentSelectedValue?.id && currentSelectedValue.mid) {
                // 从生成中状态变成生成成功时会缺少 mid 字段，因此需要自动触发保存，将 mid 字段补全
                onChange?.({
                    speechId: currentSelectedValue.id,
                    mid: currentSelectedValue.mid,
                    ttsId: currentSelectedValue.ttsId,
                    speechName: currentSelectedValue.speechName,
                    speechSource: currentSelectedValue.speechSource,
                    spd: currentSelectedValue.spd,
                    pit: currentSelectedValue.pit,
                    vol: currentSelectedValue.vol,
                });
            }

            setSpeechInvalid(currentSelectedValue?.status === SpeechStatus.Failed);
        }, [value, audioList, onChange, setSpeechInvalid, showPopup, readonly]);

        // 组件卸载时停止播放
        useEffect(
            () => () => {
                audioPlayer.destroy();
            },
            [audioPlayer]
        );

        const getPopupContainer = useCallback(() => {
            return popupContainerRef?.current || document.body;
        }, [popupContainerRef]);

        const optionRender = useCallback(
            (option: GroupOptionsAudioInfo) => {
                const isFailed = option.status === SpeechStatus.Failed;
                const isProcessing = option.status === SpeechStatus.Processing;
                const isSelected = !!option?.selected;
                const isCurrentFetching = isFetchingPreview && playSpeechId === option.id;

                return (
                    <div
                        className="flex items-center justify-between border-b-[0.5px] border-[#DEE0E7] py-[18px] last:border-none"
                        onClick={e => {
                            e.stopPropagation();
                            if (isCurrentFetching) return;
                            handleSelect(option);
                        }}
                    >
                        <div className="flex items-center truncate">
                            <div
                                className={classNames(
                                    'mr-3 flex h-9 w-9 shrink-0 items-center justify-center rounded-[8px]',
                                    {
                                        'opacity-40': isFailed,
                                        'bg-[rgba(85,98,242,0.12)]': defaultTheme,
                                    }
                                )}
                            >
                                {isCurrentFetching ? (
                                    <Spin size="small" />
                                ) : playSpeechId === option.id && !isPause ? (
                                    <Lottie
                                        className="w-[18px]"
                                        animationData={getPlayAudioJSON(PlayAudioImgSource.MOfficial)}
                                        loop
                                    />
                                ) : (
                                    <span
                                        className={classNames('iconfont icon-bofang1 text-[18px]', {
                                            'text-primary': defaultTheme,
                                        })}
                                    />
                                )}
                            </div>
                            <span
                                className={classNames('mr-[3px] truncate text-sm font-semibold', {
                                    'text-[#848691]': isFailed,
                                })}
                            >
                                {option.speechName}
                            </span>
                            {isProcessing && <Tag color="processing">生成中</Tag>}
                            {isFailed && (
                                <Tag color="error" className="flex items-center gap-[3px]">
                                    <span>生成失败</span>
                                    <Popover
                                        content={<div className="min-h-[20px] max-w-[70vw]">{option?.msg}</div>}
                                        trigger="click"
                                        getPopupContainer={getPopupContainer}
                                    >
                                        <span className="iconfont icon-questionCircle text-xs" />
                                    </Popover>
                                </Tag>
                            )}
                        </div>

                        {isSelected && (
                            <CheckOutlined
                                className={classNames('mr-1 text-lg', {
                                    'text-primary': defaultTheme,
                                })}
                            />
                        )}
                    </div>
                );
            },
            [playSpeechId, handleSelect, isPause, defaultTheme, getPopupContainer, isFetchingPreview]
        );

        const validSpeechName = useMemo(() => {
            return !value?.speechDefault && (readonly ? value?.speechName || '' : speech?.speechName);
        }, [value, readonly, speech]);

        const openCreateAudioPopup = useCallback(() => {
            setShowPopup(false);
            createAudioPopupRef.current?.showPopup?.();
        }, []);

        // 通过创建声音弹窗创建成功时
        const handleCreateAudio = useCallback(
            async (audioInfo?: AudioInfo) => {
                if (audioInfo) {
                    onChange?.({
                        speechId: audioInfo.id,
                        mid: audioInfo.mid,
                        ttsId: audioInfo.ttsId,
                        speechName: audioInfo.speechName,
                        speechSource: audioInfo.speechSource,
                        spd: audioInfo.spd,
                        pit: audioInfo.pit,
                        vol: audioInfo.vol,
                    });

                    await refreshAudioList();
                    Toast.show('已克隆声音');
                }
            },
            [refreshAudioList, onChange]
        );

        return (
            <div>
                <Card
                    title={cardTitle}
                    className={classNames('pb-[14px] pt-[14px]', cardClassName)}
                    suffix={
                        <Fragment>
                            <div
                                className={`flex w-full items-center text-base ${disabled ? 'opacity-40' : ''}`}
                                onClick={openPopup}
                            >
                                <span className="text-gray-base flex-1 truncate pl-[18px] text-end">
                                    {validSpeechName || <span className="text-gray-tertiary">选择声音</span>}
                                </span>

                                {(!value?.speechDefault || !readonly) && (
                                    <span className="iconfont icon-right text-lg leading-[19px] text-gray-tertiary" />
                                )}
                            </div>

                            <PopupM
                                title="声音选择"
                                headerClassName="leading-none !pb-[18px]"
                                visible={showPopup}
                                background="#F5F6F9"
                                showCloseButton
                                onClose={closePopup}
                                closeOnMaskClick
                            >
                                <div
                                    className="relative max-h-[calc(75vh-60px)] overflow-y-auto text-left"
                                    ref={popupContainerRef}
                                >
                                    {groupOptions?.map(speechGroup => {
                                        return speechGroup.options?.length ? (
                                            <div
                                                key={speechGroup.speechType}
                                                className="mb-3 rounded-xl bg-white px-[13px] last:mb-0"
                                            >
                                                <div className="flex items-center justify-between pt-[18px]">
                                                    <div className="text-base font-semibold leading-none">
                                                        {speechGroup.label}
                                                    </div>
                                                    {speechGroup.speechType === SpeechType.UserSpeech && (
                                                        <AudioOperationArea
                                                            openCreateAudioPopup={openCreateAudioPopup}
                                                            refreshAudioList={refreshAudioList}
                                                        />
                                                    )}
                                                </div>

                                                <div className="relative">
                                                    <SpeechGroupStatus
                                                        speechType={speechGroup.speechType}
                                                        refreshStatus={refreshStatus}
                                                    />

                                                    {speechGroup.options?.map(speech => {
                                                        return optionRender({
                                                            ...speech,
                                                            selected: currentSelectedValue?.id === speech.id,
                                                        });
                                                    })}
                                                </div>
                                            </div>
                                        ) : (
                                            <section
                                                className="mb-3 flex items-center justify-center gap-[3px] rounded-xl bg-white py-6 text-base font-medium leading-none"
                                                onClick={openCreateAudioPopup}
                                            >
                                                <span className="iconfont icon-add" />
                                                <span>创建我的声音</span>
                                            </section>
                                        );
                                    })}

                                    {/* 确认按钮区域 */}
                                    <div className="sticky bottom-[9px] flex w-full">
                                        <ConfirmButton
                                            onConfirm={onConfirm}
                                            disabled={
                                                !currentSelectedValue ||
                                                currentSelectedValue?.status === SpeechStatus.Failed
                                            }
                                        />
                                    </div>
                                </div>
                            </PopupM>

                            <CreateAudioPopup ref={createAudioPopupRef} onCreate={handleCreateAudio} theme={theme} />
                        </Fragment>
                    }
                />
                {speechInvalid && (
                    <div className="mt-[6px] flex truncate text-sm leading-none text-error">
                        <span className="truncate">{value?.speechName}</span>生成失败，请重新选择
                    </div>
                )}
            </div>
        );
    }
);

export default Audio;
