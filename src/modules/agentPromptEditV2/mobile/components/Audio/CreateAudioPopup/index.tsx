/**
 * @file 创建声音弹窗
 *
 */
import {forwardRef, useCallback, useImperativeHandle, useState} from 'react';
import PopupM from '@/components/mobile/Popup';
import useCreateAudio, {SegmentedValue} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';
import {AudioInfo} from '@/api/agentEditV2';
import {AudioTheme} from '@/modules/agentPromptEditV2/mobile/components/Audio/interface';
import RecordContainer from './RecordContainer';
import AudioSegmented from './AudioSegmented';
import UploadAudio from './UploadAudio';

interface CreateAudioPopupProps {
    onCreate: (audioInfo: AudioInfo) => void;
    theme: AudioTheme;
}

const CreateAudioPopup = forwardRef(({onCreate}: CreateAudioPopupProps, ref) => {
    const [segmentedValue, setSegmentedValue] = useState<string>(SegmentedValue.RECORD);
    const [showPopup, setShowPopup] = useState<boolean>(false);
    const {reset} = useCreateAudio();

    useImperativeHandle(ref, () => {
        return {
            showPopup() {
                setShowPopup(true);
            },
        };
    });

    const closePopup = useCallback(() => {
        setShowPopup(false);
        reset();
    }, [setShowPopup, reset]);

    return (
        <PopupM
            title={<AudioSegmented value={segmentedValue} onChange={setSegmentedValue} />}
            headerClassName="leading-none !pb-[18px]"
            visible={showPopup}
            background="#F5F6F9"
            showCloseButton
            onClose={closePopup}
            closeOnMaskClick
            destroyOnClose
        >
            {segmentedValue === SegmentedValue.RECORD ? (
                <RecordContainer onCreate={onCreate} closePopup={closePopup} />
            ) : (
                <UploadAudio onCreate={onCreate} onClose={closePopup} />
            )}
        </PopupM>
    );
});

export default CreateAudioPopup;
