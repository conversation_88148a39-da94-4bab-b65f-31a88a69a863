/**
 * @file 切换音频创建方式
 * <AUTHOR>
 */
import {ConfigProvider, Segmented} from 'antd';
import {useCallback} from 'react';
import styled from '@emotion/styled';
import {SegmentedValue} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';

const options = [
    {
        label: '录制',
        value: SegmentedValue.RECORD,
    },
    {
        label: '上传',
        value: SegmentedValue.UPLOAD,
    },
];
interface AudioSegmentedProps {
    value: string;
    onChange: (value: string) => void;
}

const StyledSegmented = styled(Segmented)`
    .ant-segmented-item {
        border-radius: 9px !important;
    }
    .ant-segmented-item-label {
        height: 36px !important;
        line-height: 34px !important;
    }
`;

export default function AudioSegmented({value, onChange}: AudioSegmentedProps) {
    const onSegmentedChange = useCallback(
        (value: string | number) => {
            onChange(value as string);
        },
        [onChange]
    );

    return (
        <div className="w-full">
            <div className="mx-auto w-[110px]">
                <ConfigProvider
                    theme={{
                        components: {
                            Segmented: {
                                itemSelectedColor: '#5562F2',
                                trackBg: 'transparent',
                            },
                        },
                    }}
                >
                    <StyledSegmented
                        value={value}
                        options={options}
                        onChange={onSegmentedChange}
                        block
                        className="h-9 text-sm font-medium"
                    />
                </ConfigProvider>
            </div>
        </div>
    );
}
