/**
 * @file 上传音频
 * <AUTHOR>
 */

import styled from '@emotion/styled';
import {Button, Upload} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import Dragger from 'antd/es/upload/Dragger';
import {useCallback, useState} from 'react';
import {FileIcon} from '@/modules/dataset/components/Common';
import {formateFileSize} from '@/modules/dataset/constant';
import {AudioInfo, uploadSpeech} from '@/api/agentEditV2';
// 允许的音频格式
const allowedFormats = ['audio/mpeg', 'audio/mp4', 'audio/x-m4a', 'audio/wav'];

const StyledDragger = styled(Dragger)`
    .ant-upload-drag {
        border-radius: 9px !important;
        border: none !important;
    }
`;
interface UploadAudioProps {
    onClose: () => void;
    onCreate: (audioInfo: AudioInfo) => void;
}

export default function UploadAudio({onClose, onCreate}: UploadAudioProps) {
    const [audio, setAudio] = useState<File | null>(null);
    const suffix = audio?.name.split('.').reverse()[0].toUpperCase();
    const [loading, setLoading] = useState(false);

    const getAudioDuration = (file: File): Promise<number> => {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.src = URL.createObjectURL(file);
            audio.onloadedmetadata = () => {
                resolve(audio.duration); // 获取时长（单位：秒）
            };

            audio.onerror = (e: any) => {
                console.error(e);
                reject(new Error('无法解析音频文件'));
            };
        });
    };

    // 文件校验逻辑
    const handleBeforeUpload = useCallback(async (file: File) => {
        if (!allowedFormats.includes(file.type)) {
            Toast.show({
                content: '仅支持 MP3、M4A、WAV 格式的音频文件',
            });
            return Upload.LIST_IGNORE; // 阻止文件加入 fileList
        }

        try {
            const duration = await getAudioDuration(file);

            if (duration < 10 || duration > 60) {
                Toast.show({
                    content: '声音文件时长不符合要求，请重新上传',
                });
                return Upload.LIST_IGNORE; // 阻止文件加入 fileList
            }

            setAudio(file);
        } catch (error) {
            Toast.show({
                content: '无法解析音频文件',
            });
        }
        return false; // 阻止默认上传
    }, []);

    const handleRemove = useCallback(() => {
        setAudio(null);
    }, []);

    const handleConfirm = useCallback(async () => {
        if (audio === null) {
            Toast.show({
                content: '请上传音频文件',
            });
            return;
        }

        try {
            setLoading(true);
            const formData = new FormData();
            // 音频文件名动态生成
            formData.append('speechFile', audio, 'speech.' + suffix?.toLowerCase());
            formData.append('createType', '1');
            const audioInfo = await uploadSpeech(formData);
            onClose();
            onCreate(audioInfo);
            setLoading(false);
        } catch {
            Toast.show({
                content: '生成失败',
            });
            setLoading(false);
        }
    }, [audio, onClose, onCreate, suffix]);

    return (
        <div className=" max-h-[calc(88vh-78px)] overflow-y-auto ">
            <div className="mx-auto mb-[36px] pt-3 text-center">
                <div className="h-6 text-xl font-medium leading-6">请上传</div>
                <div className="mx-auto mt-[28px] w-[268px] text-sm font-normal text-gray-tertiary">
                    声音时长10-60s，背景安静无杂音，支持mp3、m4a、wav格式
                </div>
            </div>
            <div className="h-[195px]">
                {audio === null ? (
                    <div className="h-full">
                        <StyledDragger
                            className="h-full"
                            showUploadList={false}
                            beforeUpload={handleBeforeUpload}
                            accept=".mp3,.m4a,.wav" // 限制文件选择
                        >
                            <div className="flex flex-col items-center justify-center">+ 上传声音文件</div>
                        </StyledDragger>
                    </div>
                ) : (
                    <div className="h-full bg-colorBgFormList">
                        <div className="mx-auto flex w-[full] pl-2 pt-[11px]">
                            <div
                                id="warper"
                                className="group relative flex flex-1 content-center items-center overflow-hidden rounded-[9px] bg-white"
                            >
                                <FileIcon name={audio?.name} className="m-[6px] h-12 w-12" />
                                <div className="ml-[.375rem] flex w-[calc(100%-66px)] flex-col items-start">
                                    <span className="inline-block w-full overflow-hidden overflow-ellipsis whitespace-nowrap pr-3 text-left text-sm leading-[22px]">
                                        {audio?.name}
                                    </span>
                                    <span className="text-xs leading-[18px] text-flow-hover">
                                        {suffix && suffix + ' · '}
                                        {formateFileSize(audio?.size || 0)}
                                    </span>
                                </div>
                            </div>
                            <div className="ml-[13px] mr-[11px] flex" onClick={handleRemove}>
                                <span className="iconfont icon-delete m-auto text-lg"></span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            <div className="mb-[30px] mt-[168px] flex justify-center">
                <Button
                    className="h-[45px] w-[144px] text-sm font-medium"
                    type="primary"
                    shape="round"
                    onClick={handleConfirm}
                    disabled={audio === null}
                    loading={loading}
                >
                    确定
                </Button>
            </div>
        </div>
    );
}
