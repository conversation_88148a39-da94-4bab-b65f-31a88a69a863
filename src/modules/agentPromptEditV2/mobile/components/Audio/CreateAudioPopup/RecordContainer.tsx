/**
 * @file 录制音频 - 从/index.tsx中抽离
 * <AUTHOR>
 */
import {useCallback} from 'react';
import {message} from 'antd';
import useCreateAudio, {CreateAudioStatus, MinRecordDuration} from '@/modules/agentPromptEditV2/hooks/useCreateAudio';
import {AudioInfo, uploadSpeech} from '@/api/agentEditV2';
import RecordAudio from '@/components/mobile/RecordAudio';

const CreateAudioTitleMap = {
    [CreateAudioStatus.NONE]: '请朗读',
    [CreateAudioStatus.RECORDING]: '录制中',
    [CreateAudioStatus.UPLOADING]: '录制中',
    [CreateAudioStatus.SUCCESS]: '录制成功',
    [CreateAudioStatus.FAIL]: '录制失败',
};

enum CreateAudioGuideText {
    NONE = '请用自然的语气读完下面的句子',
    DURATION_TOO_SHORT = '录音不到10秒，请重新录制',
    FAIL = '录制失败，请重新录制',
}
interface RecordContainerProps {
    onCreate: (audioInfo: AudioInfo) => void;
    closePopup: () => void;
}
export default function RecordContainer({onCreate, closePopup}: RecordContainerProps) {
    const {status, setStatus, description, setDescription, recordDuration, startRecord, stopRecord, showCountdown} =
        useCreateAudio();

    // 停止录音并提交录音文件
    const stopRecordAction = useCallback(async () => {
        if (recordDuration < MinRecordDuration && status === CreateAudioStatus.RECORDING) {
            setStatus(CreateAudioStatus.FAIL);
            setDescription(CreateAudioGuideText.DURATION_TOO_SHORT);
            stopRecord();
            return;
        }

        try {
            const blob = await stopRecord();
            const formData = new FormData();
            formData.append('speechFile', new File([blob], 'speech.wav'));
            setStatus(CreateAudioStatus.UPLOADING);
            const audioInfo = await uploadSpeech(formData);
            closePopup();
            onCreate(audioInfo);
        } catch (e: any) {
            setStatus(CreateAudioStatus.FAIL);
            message.error('录制失败，请重新录制');
            console.error(e);
            setDescription(e?.message || e?.msg || CreateAudioGuideText.FAIL);
        }
    }, [recordDuration, status, setStatus, setDescription, stopRecord, closePopup, onCreate]);

    return (
        <section className="flex max-h-[calc(88vh-78px)] flex-col items-center overflow-y-auto px-3 leading-none">
            <div className="pt-3 text-xl font-semibold">{CreateAudioTitleMap[status]}</div>
            <RecordAudio
                recordStatus={status}
                guideText={description}
                showCountdown={showCountdown}
                recordDuration={recordDuration}
                onStartRecordClick={startRecord}
                onStopRecordClick={stopRecordAction}
            />
        </section>
    );
}
