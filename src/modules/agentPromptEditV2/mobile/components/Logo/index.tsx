/* eslint-disable complexity */
/**
 * @file 头像组件
 * <AUTHOR>
 */
import React, {useCallback, useState, useRef, useMemo} from 'react';
import {Form, Upload} from 'antd';
import type {RcFile, UploadFile, UploadProps} from 'antd/es/upload/interface';
import useMessage from 'antd/es/message/useMessage';
import type {UploadChangeParam} from 'antd/es/upload';
import Lottie from 'lottie-react';
import {css} from '@emotion/css';
import classNames from 'classnames';
import {useSearchParams} from 'react-router-dom';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import logoPlaceHolder from '@/modules/agentPromptEditV2/mobile/static/logo-placeholder.png';
import PopupM from '@/components/mobile/Popup';
import {getBase64, getImgWH} from '@/utils/image';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {uploadPhotoV2} from '@/api/agentEdit';
import GenerateLogoJSON from '@/modules/home/<USER>/generate-logo';
import ImgCropModal from '@/modules/agentPromptEditV2/mobile/components/Logo/ImgCropModal';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import GenerateLogo from './GenerateLogo';

interface UploadImageProps extends Omit<UploadProps, 'value' | 'onChange'> {
    value?: string;
    disabled?: boolean;
    onChange?: (value: string) => void;
    logoSimilarity?: number;
    isTourOpen?: boolean;
}

const uploadStyle = css`
    .ant-upload {
        width: 100%;
        height: 100%;
    }
`;

export default function Logo(props: UploadImageProps) {
    const [loading, setLoading] = useState(false);
    const [showPopup, setShowPopup] = useState(false);
    const [showAIGenerate, setShowAIGenerate] = useState(false);
    const [base64ImgUrl, setBase64ImageUrl] = useState<string>('');
    const imgCropModalRef = useRef<any>(null);
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId') || '';
    const {setLogoSimilarity} = usePromptEditStoreV2(store => ({
        setLogoSimilarity: store.setLogoSimilarity,
    }));

    const abortControllerRef = useRef<AbortController | null>(null);
    const [messageApi, MessageContext] = useMessage();
    const {ubcClickLog} = useUbcLog();

    const onUploadChange = useCallback((info: UploadChangeParam<UploadFile>) => {
        if (info.file.status === 'uploading') {
            setShowPopup(false);
            getBase64(info.file.originFileObj as RcFile).then(url => setBase64ImageUrl(url));
        }
    }, []);

    const beforeUpload = useCallback(
        async (file: RcFile) => {
            const isImageFormat = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'].some(
                (type: string) => file.type === type
            );
            if (!isImageFormat) {
                messageApi.error('图片格式仅支持png、jpeg、jpg、webp，请重新上传!');
                return false;
            }

            // 通过bos自动裁切&压缩图片，参考文档：https://cloud.baidu.com/doc/BOS/s/Yldh5wq5b
            const isSizeLimit = file.size < 20 * 1024 * 1024;
            if (!isSizeLimit) {
                messageApi.error('图片应该小于20M，请重新上传!');
                return false;
            }

            const url = await getBase64(file);
            const {width, height} = await getImgWH(url);
            if (width > 30000 || height > 30000) {
                messageApi.error('头像大于30000像素*30000像素，请重新上传!');
                return false;
            }

            // 打开图片裁剪弹窗
            imgCropModalRef?.current?.showModal();

            return true;
        },
        [messageApi]
    );

    const openPopup = useCallback(() => {
        !props?.disabled && setShowPopup(true);
    }, [props?.disabled]);
    const closePopup = useCallback(() => {
        setShowPopup(false);
        setShowAIGenerate(false);
    }, [setShowPopup]);

    const openAIGenerate = useCallback(() => {
        setShowAIGenerate(true);
    }, [setShowAIGenerate]);

    const handleClickUpload = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.UPLOAD_IMAGE_DRAGGER);
    }, [ubcClickLog]);

    const onCrop = useCallback(
        async (formData: FormData) => {
            try {
                setLoading(true);
                imgCropModalRef?.current?.closeModal();
                formData.append('appId', appId);
                const response = await uploadPhotoV2(formData, () => {}, abortControllerRef.current?.signal);
                if (response) {
                    setLogoSimilarity(response.similarity);
                    props.onChange && props.onChange(response.url);
                } else {
                    messageApi.error('图片上传失败，请重新上传!');
                }
            } catch {
                messageApi.error('图片上传失败，请重新上传!');
            } finally {
                setLoading(false);
            }
        },
        [appId, messageApi, props, setLogoSimilarity]
    );
    const {readonly} = usePromptEditContext();

    const logoSimilarity = useMemo(() => {
        return props.logoSimilarity === 1 && !props.isTourOpen;
    }, [props.logoSimilarity, props.isTourOpen]);

    return (
        <div
            className={classNames('relative mb-[24px] mt-[18px] flex justify-center', {
                'mb-[38px]': logoSimilarity,
            })}
        >
            {readonly ? (
                <div className="relative flex h-[130px] w-[130px] items-center justify-center overflow-hidden rounded-3xl">
                    <img className="pointer-events-none" src={props.value || logoPlaceHolder} />
                </div>
            ) : (
                <>
                    {/* 图片区域 */}
                    <div className="relative flex flex-col items-center" onClick={openPopup}>
                        <div onClick={handleClickUpload} className="flex items-center justify-center">
                            <div className="flex flex-col items-center">
                                <div className="relative flex h-[130px] w-[130px] items-center justify-center overflow-hidden rounded-3xl">
                                    {loading ? (
                                        <Lottie
                                            className="h-[280px] w-[280px] cursor-progress"
                                            animationData={GenerateLogoJSON}
                                            loop
                                        />
                                    ) : (
                                        <img className="pointer-events-none" src={props.value || logoPlaceHolder} />
                                    )}
                                </div>
                            </div>
                            {!props?.disabled && (
                                <div className="absolute bottom-0 right-0 box-content flex h-[30px] w-[30px] items-center justify-center rounded-full border-[2px] border-white bg-primary">
                                    <span className="iconfont icon-plus text-[18px] font-semibold text-white" />
                                </div>
                            )}
                        </div>
                    </div>
                    {MessageContext}
                    <ImgCropModal
                        key="_upload_logo_crop_img_modal"
                        ref={imgCropModalRef}
                        base64ImgUrl={base64ImgUrl}
                        onCrop={onCrop}
                    />
                    <PopupM
                        title={showAIGenerate ? 'AI生成' : ''}
                        visible={showPopup}
                        background="#F5F6F9"
                        bodyStyle={{
                            minHeight: '0',
                        }}
                        showCloseButton={showAIGenerate}
                        onClose={closePopup}
                    >
                        {showAIGenerate ? (
                            <Form.Item noStyle name={['agentInfo', 'logoUrl']}>
                                <GenerateLogo closePopup={closePopup} />
                            </Form.Item>
                        ) : (
                            <div className="pt-[15px]">
                                <div
                                    className="mb-2 flex h-[52px] w-full items-center justify-center rounded-xl bg-white text-primary"
                                    onClick={openAIGenerate}
                                >
                                    <span className="iconfont icon-AI text-[18px]"></span>
                                    <span className="pl-1 text-[16px] font-semibold">AI生成</span>
                                </div>
                                <div className="mb-2 flex h-[52px] w-full items-center justify-center rounded-xl bg-white">
                                    <Upload
                                        className={classNames(
                                            'block h-full w-full text-center text-base font-medium text-[#1f1f1f]',
                                            uploadStyle
                                        )}
                                        accept="image/jpeg,image/png,image/jpg,image/webp"
                                        beforeUpload={beforeUpload}
                                        onChange={onUploadChange}
                                        showUploadList={false}
                                        // eslint-disable-next-line react/jsx-no-bind
                                        customRequest={() => {}}
                                    >
                                        <div className="flex h-full w-full flex-row items-center justify-center">
                                            相册上传
                                        </div>
                                    </Upload>
                                </div>
                                <div
                                    className="mb-[15px] flex h-[52px] w-full items-center justify-center rounded-xl bg-white text-base font-medium text-[#1f1f1f]"
                                    onClick={closePopup}
                                >
                                    取消
                                </div>
                            </div>
                        )}
                    </PopupM>
                </>
            )}
            {logoSimilarity && (
                <div className="absolute -bottom-8 left-0 w-full truncate text-left text-sm text-[#FF8200]">
                    请修改头像，与原智能体过度相似将影响流量效果及使用
                </div>
            )}
        </div>
    );
}
