/**
 * @file 智能体 AI 生成 logo 组件
 * <AUTHOR>
 */
import {Button} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import cloneDeep from 'lodash/cloneDeep';
import <PERSON><PERSON> from 'lottie-react';
import styled from '@emotion/styled';
import Toast from 'antd-mobile/es/components/toast';
import GenerateLogoJSON from '@/modules/home/<USER>/generate-logo';
import {StyledTextareaWise} from '@/components/mobile/InputWithLabel';
import {getCountConfig} from '@/utils/text';
import {defaultAvatar, usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {generateAgentLogoV2} from '@/api/agentEditV2';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {DIGITAL_LOG_CONSTANTS} from '@/utils/logger/constants/digital';
import {auditText} from '@/api/audit';

enum GenerateStatus {
    NONE = 'none',
    LOADING = 'loading',
    ERROR = 'error',
    SUCCESS = 'success',
}

const StyledButton = styled(Button)`
    &.ant-btn {
        border-radius: 12px;
        font-weight: 500;
        font-size: 16px;

        &.ant-btn-loading,
        &.ant-btn-loading.ant-btn:active {
            opacity: 1;
            background: #bbc0fa !important;
        }
    }
`;

export default function GenerateLogo(props: {closePopup: () => void; onChange?: (value: string) => void}) {
    const {ubcClickLog} = useUbcLog();
    const [description, setDescription] = useState('');
    const handleDescriptionChange = useCallback((e: {target: {value: React.SetStateAction<string>}}) => {
        setDescription(e.target.value);
    }, []);
    const abortControllerRef = useRef<AbortController>();

    const [url, setUrl] = useState('');
    const [generateStatus, setGenerateStatus] = useState<GenerateStatus>(GenerateStatus.NONE);

    const {agentConfig, setLogoSimilarity} = usePromptEditStoreV2(state => ({
        agentConfig: state.agentConfig,
        setLogoSimilarity: state.setLogoSimilarity,
    }));

    useEffect(() => {
        setUrl('');
        setGenerateStatus(GenerateStatus.NONE);
        return () => {
            abortControllerRef.current?.abort();
        };
    }, [setUrl]);

    const handleConfirm = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.IMAGE_CONFIRM_BUTTON);
        setLogoSimilarity(0);
        props.onChange && props.onChange(url);
        props.closePopup();
    }, [props, setLogoSimilarity, ubcClickLog, url]);

    const handleYieldAiLogo = useCallback(async () => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.IMAGE_GENERATE_BUTTON);
        setGenerateStatus(GenerateStatus.LOADING);
        abortControllerRef.current = new AbortController();
        const config: any = cloneDeep(agentConfig);
        config.agentInfo.logoIntroduction = description;
        try {
            if (description.trim()) {
                await auditText(description, '描述内容');
            }

            const res = await generateAgentLogoV2(config, abortControllerRef.current.signal).catch(() => {
                generateStatus === GenerateStatus.LOADING && setGenerateStatus(GenerateStatus.ERROR);
                setUrl('');
            });
            if (res) {
                setGenerateStatus(GenerateStatus.SUCCESS);
                setUrl(res);
            } else if (generateStatus === GenerateStatus.LOADING) {
                setGenerateStatus(GenerateStatus.ERROR);
                setUrl('');
            }
        } catch (error: any) {
            Toast.show(error.message);
            setGenerateStatus(GenerateStatus.ERROR);
        }
    }, [agentConfig, description, generateStatus, ubcClickLog]);

    const handleRegenerate = useCallback(() => {
        setGenerateStatus(GenerateStatus.NONE);
    }, [setGenerateStatus]);

    const handleClickDescription = useCallback(() => {
        ubcClickLog(DIGITAL_LOG_CONSTANTS.IMAGE_DESC_INPUT);
    }, [ubcClickLog]);

    return (
        <div className="mb-6">
            <div className="relative h-0 w-full pt-[100%]">
                <div className="absolute top-0 h-full w-full">
                    {generateStatus === GenerateStatus.NONE && (
                        <StyledTextareaWise
                            value={description}
                            className="h-[300px] rounded-xl pt-[9.4px]"
                            onChange={handleDescriptionChange}
                            count={getCountConfig(200, false)}
                            placeholder="请描述您希望的生成的头像细节，比如漫画风格（非必填）"
                            onClick={handleClickDescription}
                        />
                    )}
                    {generateStatus === GenerateStatus.LOADING && (
                        <Lottie
                            className="h-full w-full cursor-progress rounded-[18px]"
                            animationData={GenerateLogoJSON}
                            loop
                        />
                    )}
                    {generateStatus === GenerateStatus.ERROR && (
                        <div className="flex h-full flex-col items-center justify-center rounded-[18px] bg-white text-center">
                            <div className="iconfont icon-tip text-[60px] leading-none text-[#DBDCE0]" />
                            <span className="mt-3 text-sm leading-none">生成失败</span>
                        </div>
                    )}
                    {generateStatus === GenerateStatus.SUCCESS && (
                        <img className="absolute top-0 h-full w-full rounded-[18px]" src={url || defaultAvatar} />
                    )}
                </div>
            </div>
            <div className="mt-8 h-[50px] w-full px-[14px] text-center">
                {generateStatus === GenerateStatus.NONE && (
                    <StyledButton type="primary" className="h-full" block onClick={handleYieldAiLogo}>
                        生成头像
                    </StyledButton>
                )}
                {generateStatus === GenerateStatus.LOADING && (
                    <div className="h-full">
                        <StyledButton type="primary" className="h-full" block loading>
                            生成中
                        </StyledButton>
                    </div>
                )}
                {(generateStatus === GenerateStatus.SUCCESS || generateStatus === GenerateStatus.ERROR) && (
                    <div className="flex h-full justify-between">
                        <StyledButton
                            className="mr-2 h-full flex-1 bg-[#EBECFD] text-primary"
                            type="text"
                            onClick={handleRegenerate}
                        >
                            重新生成
                        </StyledButton>
                        <StyledButton
                            className="h-full flex-1"
                            type="primary"
                            onClick={handleConfirm}
                            disabled={generateStatus === GenerateStatus.ERROR}
                        >
                            选用
                        </StyledButton>
                    </div>
                )}
            </div>
        </div>
    );
}
