/**
 * @file 智能体 logo 裁剪弹窗
 * <AUTHOR>
 */
import {useImperativeHandle, useCallback, useState, useRef, forwardRef} from 'react';
import {Button} from 'antd';
import styled from '@emotion/styled';
import Cropper, {ReactCropperElement} from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import MobileFullScreen from '@/components/mobile/FullScreen';
import {canvasToFile} from '@/utils/image';

interface ImgCropModalProps {
    base64ImgUrl: string;
    onCrop: (formData: FormData) => void;
    onClose?: () => void;
}

const ImgCropModalContainer = styled.div`
    height: calc(100 * var(--dvh) - 44px);
`;

const StyledCropper = styled(Cropper)`
    .cropper-view-box {
        outline: 2px solid #fff;
    }
`;

// 裁剪框距离屏幕左右间距 17px
const cropBoxSize = window.innerWidth - 17 * 2;

const ImgCropModal = forwardRef(({base64ImgUrl, onCrop, onClose}: ImgCropModalProps, ref) => {
    const cropperRef = useRef<ReactCropperElement>(null);
    const [cropLoading, setCropLoading] = useState<boolean>(false);
    const [cropModalOpen, setCropModalOpen] = useState<boolean>(false);

    // 裁剪弹窗的确定按钮
    const handleCrop = useCallback(async () => {
        setCropLoading(true);
        try {
            // 获取裁剪后的图片
            const imgFile = await canvasToFile(cropperRef.current?.cropper?.getCroppedCanvas() as HTMLCanvasElement);
            onCrop(imgFile);
        } finally {
            setCropLoading(false);
            onClose?.();
        }
    }, [onCrop, onClose]);

    // 对外暴露打开弹窗事件
    useImperativeHandle(ref, () => {
        return {
            showModal() {
                setCropModalOpen(true);
            },
            closeModal() {
                setCropModalOpen(false);
            },
            open: cropModalOpen,
        };
    });

    const handleBack = useCallback(() => {
        setCropModalOpen(false);
    }, [setCropModalOpen]);

    return (
        <MobileFullScreen show={cropModalOpen} title="调整图片" onBack={handleBack}>
            <ImgCropModalContainer className="flex w-full flex-col items-center justify-between">
                <div className="flex flex-1 items-center px-[17px]">
                    <StyledCropper
                        ref={cropperRef}
                        viewMode={3}
                        dragMode="move"
                        src={base64ImgUrl}
                        center={false}
                        cropBoxMovable={false}
                        cropBoxResizable={false}
                        style={{height: cropBoxSize, width: cropBoxSize}}
                        initialAspectRatio={1}
                        guides={false}
                        toggleDragModeOnDblclick={false}
                        autoCropArea={1}
                        background={false}
                    />
                </div>
                <div className="mb-6 w-full px-9 text-base font-medium">
                    <Button
                        onClick={handleCrop}
                        type="primary"
                        className="h-[50px] w-full rounded-xl"
                        loading={cropLoading}
                    >
                        完成
                    </Button>
                </div>
            </ImgCropModalContainer>
        </MobileFullScreen>
    );
});

export default ImgCropModal;
