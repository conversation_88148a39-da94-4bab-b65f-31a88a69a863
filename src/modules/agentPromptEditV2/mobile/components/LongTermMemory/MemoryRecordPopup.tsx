/**
 * @file 长期记忆记录弹窗
 * <AUTHOR>
 */
import {useCallback, useEffect, useRef} from 'react';
import {ConfigProvider} from 'antd';
import throttle from 'lodash/throttle';
import classNames from 'classnames';
import {css} from '@emotion/css';
import Toast from 'antd-mobile/es/components/toast';
import PopupM from '@/components/mobile/Popup';
import Scrollbar from '@/components/Scrollbar';
import useLTMList from '@/modules/agentPromptEditV2/hooks/useLTMList';
import Loading from '@/components/Loading';
import {SummaryStatus} from '@/api/agentEditV2/interface';
import StyledPopover from '@/components/Popover/StyledPopover';

interface MemoryRecordPopupProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
}

const PopoverOverlayCSS = css`
    .ant-popover-content .ant-popover-inner {
        border-radius: 12px !important;
        box-shadow: 0px 2px 30px 0px #1d22520f;
    }
    .ant-popover-content {
        transform: scale(1);
    }
`;

export function MemoryRecordPopup({visible, setVisible}: MemoryRecordPopupProps) {
    const handleClose = useCallback(() => {
        setVisible(false);
    }, [setVisible]);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const {
        isLoading,
        dateList,
        dateToSummariesMap,
        BottomHint,
        summaryProgressInfos,
        loadInitialSummary,
        handleScroll,
        handleManualSummary,
        stopPolling,
        LTMSummaryProgressCmpt,
    } = useLTMList({containerRef});

    useEffect(() => {
        visible && loadInitialSummary();
        !visible && stopPolling();
    }, [visible, loadInitialSummary, stopPolling]);

    useEffect(() => {
        return () => {
            stopPolling();
        };
    }, [stopPolling]);

    const handleClick = throttle(() => {
        handleManualSummary((content: string) => {
            Toast.show({
                content: <span className="block text-center">{content}</span>,
            });
        });
    }, 800);

    const title = (
        <div>
            <span className="absolute left-[18px] text-[16px] font-medium text-gray-secondary" onClick={handleClick}>
                手动刷新
            </span>
            <span>长期记忆记录</span>
            {/* 去除动画，解决 popover 定位两次抖动问题，对应 issue: https://github.com/ant-design/ant-design/issues/27102#issuecomment-714241022 */}
            <ConfigProvider
                theme={{
                    token: {
                        motion: false,
                    },
                }}
            >
                <StyledPopover
                    placement="bottom"
                    content={
                        <div className="h-[50px] w-full px-[8px] py-[4px]">
                            <span>默认对话结束自动总结（预计2小时内总结完成），也可通过手动刷新按钮发起主动总结</span>
                        </div>
                    }
                    trigger="click"
                    overlayClassName={classNames(PopoverOverlayCSS)}
                >
                    <span className="iconfont icon-questionCircle ml-[3px] text-base font-normal text-gray-400"></span>
                </StyledPopover>
            </ConfigProvider>
        </div>
    );

    return (
        <PopupM
            visible={visible}
            title={title}
            closeOnMaskClick
            onClose={handleClose}
            suffix={
                <span
                    className="iconfont icon-close mr-[18px] text-[20px] text-gray-quaternary"
                    onClick={handleClose}
                ></span>
            }
            background="#F5F6F9"
            bodyStyle={{
                minHeight: '224px',
            }}
        >
            <div>
                {isLoading ? (
                    <Loading />
                ) : dateList.length > 0 ? (
                    <Scrollbar onScroll={handleScroll} className="max-h-[80vh] overflow-y-auto" ref={containerRef}>
                        <>
                            {dateList.map(date => (
                                <div key={date} className="mb-2 rounded-lg bg-white px-[13px] pt-4">
                                    <span className="font-medium leading-6">{date}</span>
                                    <div className="mt-[-8px] divide-y divide-[#EDEEF0]">
                                        {dateToSummariesMap?.get(date)?.map(summary => (
                                            <div key={summary.summaryId} className="py-4">
                                                <span className="pr-3 text-gray-tertiary">
                                                    {summary.summaryTimeString}
                                                </span>
                                                {summary.summaryText === '' ? (
                                                    <LTMSummaryProgressCmpt
                                                        {...(summaryProgressInfos[summary.summaryId] || {
                                                            summaryStatus: SummaryStatus.Doing,
                                                        })}
                                                    />
                                                ) : (
                                                    <span className="whitespace-pre-line">{summary.summaryText}</span>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                            <div className="mt-[-8px] text-flow-hover">{BottomHint}</div>
                        </>
                    </Scrollbar>
                ) : (
                    <div className="flex w-full items-center justify-center">
                        <div className="mt-[12px] flex flex-col items-center justify-center">
                            <span className="iconfont icon-exclamationcircle text-[50px] leading-[50px] text-[#DBDCE0]" />
                            <span className="mt-[12px] text-sm font-normal not-italic text-black">
                                暂无内容，快去和你创建的智能体聊天吧～
                            </span>
                        </div>
                    </div>
                )}
            </div>
        </PopupM>
    );
}
