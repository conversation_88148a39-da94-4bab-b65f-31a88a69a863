/**
 * @file 长期记忆配置
 * <AUTHOR>
 */

import {useCallback, useState} from 'react';
import {ConfigProvider, Form, Switch} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import Toast from 'antd-mobile/es/components/toast';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {MemoryRecordPopup} from './MemoryRecordPopup';

const SwitchContainer = styled.div`
    .ant-switch-handle {
        top: 3px !important;
    }

    .ant-switch-checked:hover:not(.ant-switch-disabled) {
        background-color: ${ThemeConfig.token.colorPrimary} !important;
    }

    .ant-switch {
        background-color: rgba(0, 0, 0, 0.2);
    }

    .ant-switch:hover:not(.ant-switch-disabled) {
        background-color: rgba(0, 0, 0, 0.2);
    }
`;

export default function DigitalFigure() {
    const isEnabled = Form.useWatch(['agentJson', 'longTermMemory', 'isEnabled']);

    const [popupVisible, setPopupVisible] = useState(false);

    const {readonly} = usePromptEditContext();

    const {agentConfig} = usePromptEditStoreV2(state => ({
        agentConfig: state.agentConfig,
    }));
    const handleClickButton = useCallback(() => {
        if (agentConfig.agentInfo.appId) {
            setPopupVisible(true);
        } else {
            Toast.show('请先创建智能体');
        }
    }, [agentConfig.agentInfo.appId]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Switch: {
                        trackMinWidthSM: 36,
                        trackHeightSM: 20,
                        handleSizeSM: 14,
                    },
                },
            }}
        >
            <SwitchContainer>
                <div className="mt-3 rounded-[12px] bg-white px-[13px] py-[14px] leading-none">
                    <div className="flex items-center justify-between">
                        <span className="text-base font-semibold">长期记忆</span>
                        <div className="flex h-5 items-center">
                            {!readonly && (
                                <span
                                    className={classNames('text-sm font-semibold leading-5 text-primary', {
                                        hidden: !isEnabled,
                                    })}
                                    onClick={handleClickButton}
                                >
                                    查看记录
                                </span>
                            )}
                            <Form.Item noStyle name={['agentJson', 'longTermMemory', 'isEnabled']}>
                                <Switch size="small" className="ml-4" />
                            </Form.Item>
                        </div>
                    </div>
                </div>
            </SwitchContainer>
            <MemoryRecordPopup visible={popupVisible} setVisible={setPopupVisible} />
        </ConfigProvider>
    );
}
