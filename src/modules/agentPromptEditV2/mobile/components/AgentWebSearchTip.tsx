/**
 * @file 智能体纬度的提示组件
 * <AUTHOR>
 */
import useWebSearchTip from '@/modules/agentPromptEditV2/hooks/useWebSearchTip';

export default function AgentWebSearchTip() {
    const {isShowTips, showContent, closeTips} = useWebSearchTip();

    return isShowTips && showContent ? (
        <div className="fixed left-0 top-6 z-[100] flex w-full justify-center px-3 text-center">
            <div className="inline-flex rounded-[9px] border-[1px] border-gray-border-secondary bg-white px-3 py-[9px] text-sm leading-[22px] shadow-[0px_4px_10px_0px_#1E1F240F]">
                <span className="iconfont icon-tip-fill mr-2 cursor-pointer text-base text-primary" />
                <span>{showContent}</span>
                <div className="ml-3 cursor-pointer text-gray-tertiary">
                    <span
                        className="iconfont icon-close ml-2 cursor-pointer text-sm text-gray-secondary"
                        onClick={closeTips}
                    />
                </div>
            </div>
        </div>
    ) : null;
}
