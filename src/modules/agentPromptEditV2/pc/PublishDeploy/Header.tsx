/**
 * @file header.tsx
 *
 */

import React, {useCallback} from 'react';
import classNames from 'classnames';
import {Avatar} from 'antd';
import {defaultAvatar, defaultName, usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import CustomerService from '@/components/CustomerService';
import {LJExtData} from '@/utils/logger';
import {EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {CommunityPopoverType} from '@/utils/loggerV2/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';

interface HeaderProps extends React.HTMLAttributes<HTMLHeadElement> {
    publishBtn: React.ReactNode;
    onPageReturn?: () => void;
}

const Header: React.FC<HeaderProps> = ({publishBtn, onPageReturn, className, ...props}: HeaderProps) => {
    // 你的组件逻辑将在这里实现
    const {agentId, agentName, agentLogo, setShowPublishDeploy} = usePromptEditStoreV2(store => ({
        agentId: store.agentConfig.agentInfo.appId,
        agentName: store.agentConfig.agentInfo.name || defaultName,
        agentLogo: store.agentConfig.agentInfo.logoUrl || defaultAvatar,
        setShowPublishDeploy: store.setShowPublishDeploy,
    }));

    const {showLog} = useUbcLogV3();

    const handleQRCodeHoverOpen = useCallback(() => {
        showLog(
            EVENT_VALUE_CONST.COMMUNITY_POPOVER,
            {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
                [EVENT_EXT_KEY_CONST.COMMUNITY_POPOVER_TYPE]: CommunityPopoverType.ACTIVE,
            } as LJExtData,
            EVENT_PAGE_CONST.PUBLISH_SETTING
        );
    }, [showLog, agentId, agentName]);

    const handleTipsShow = useCallback(() => {
        showLog(
            EVENT_VALUE_CONST.CUSTOMER_SERVICE_BUBBLE,
            {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
            } as LJExtData,
            EVENT_PAGE_CONST.PUBLISH_SETTING
        );
    }, [showLog, agentId, agentName]);

    const handleAiBotOpen = useCallback(() => {
        showLog(
            EVENT_VALUE_CONST.CUSTOMER_SERVICE_DRAWER,
            {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
            } as LJExtData,
            EVENT_PAGE_CONST.PUBLISH_SETTING
        );
    }, [showLog, agentId, agentName]);

    // 返回智能体配置页面
    const onBack = useCallback(() => {
        setShowPublishDeploy(false);
        // 重新计算配置页面的展现
        onPageReturn && onPageReturn();
    }, [setShowPublishDeploy, onPageReturn]);

    return (
        <header className={classNames('flex min-h-[56px] items-center justify-between bg-white', className)} {...props}>
            <div className="flex items-center">
                <span
                    className="iconfont icon-left ml-5 mr-2 cursor-pointer text-xl hover:text-primary"
                    onClick={onBack}
                ></span>
                <Avatar src={agentLogo} size={34} />
                <span className="ml-1 font-medium">{agentName}</span>
            </div>
            <div className="mr-6 flex items-center">
                {publishBtn}
                <div className="mx-3 h-[22px] border-r-[1px] border-[#DEE0E7]"></div>
                <CustomerService
                    isBuilderContainer
                    onQRCodeHoverOpen={handleQRCodeHoverOpen}
                    onTipsShow={handleTipsShow}
                    onAiBotOpen={handleAiBotOpen}
                />
            </div>
        </header>
    );
};

export default Header;
