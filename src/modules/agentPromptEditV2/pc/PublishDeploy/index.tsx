/* eslint-disable max-statements */
/**
 * @file PublishDeploy.tsx  发布部署配置页面
 *
 */

import React, {useState, useCallback, useEffect, useRef, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {ConfigProvider, Row, Col, Button, Tooltip, Modal} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import urls from '@/links';
import {ViewAgentPermission, ViewPermissionMap} from '@/store/agent/utils';
import {AgentPublishChannelType, WxAccountType} from '@/api/agentDeploy/interface';
import agentListApi from '@/api/agentList';
import {AgentPermission} from '@/api/agentEdit';
import {AgentTabType} from '@/modules/agentList/interface';
import RightPanel from '@/components/RightPanel';
import {OutputType, OutputTypeConfig, CardTypeLogMap} from '@/modules/agentOutput/config';
import OutputDetail from '@/modules/agentOutput/detail';
import Card from '@/modules/agentOutput/components/OutputTypeCard';
import {useLayoutStore} from '@/store/home/<USER>';
import PublishSuccess from '@/modules/agentPromptEditV2/pc/components/PublishSuccess';
import CustomPopover from '@/components/Popover';
import FlashPoint from '@/modules/center/components/pc/FlashPoint';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {initWeChatAuthState, useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';
import {usePublishAction} from '@/modules/agentPromptEditV2/hooks/usePublishAction';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {CardType} from '@/modules/agentOutput/interface';
import {CreatePageType} from '@/utils/loggerV2/interface';
import {cancelDelayedReport, UserAction} from '@/utils/monitor/index';
import PublishSuccessModalV2 from '@/modules/agentPromptEditV2/pc/components/PublishSuccessModalV2';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import api from '@/api/agentDeploy/index';
import {deepSeekModelOptions} from '@/api/agentEdit/interface';
import {XmiChannelType} from '@/api/agentDeploy/interface';
import {ScrollContainer} from '@/components/Scrollbar';
import {useQ2CList} from '../TuningTab/q2c/hooks/useQ2CList';
import PublishMode from './PublishMode';
import DeployChannel from './DeployChannel';
import Header from './Header';

/* 卡片列表-部署类型List */
const outputTypeCardList = [
    OutputTypeConfig[OutputType.AgentURL],
    OutputTypeConfig[OutputType.AgentAPI],
    OutputTypeConfig[OutputType.JsSDK],
    OutputTypeConfig[OutputType.WeChatBot],
];

const StyledModal = styled(Modal)<{isWorkflowMode?: boolean}>`
    .ant-modal-content {
        padding: 24px 28px 24px 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
        display: flex;
        justify-content: ${props => (props.isWorkflowMode ? 'center' : 'flex-end')};
    }
`;

// PublishDeploy 组件
// eslint-disable-next-line complexity
const PublishDeploy = () => {
    // 打点
    const {displayLog, clickLog} = useUbcLogV2();

    const {appId, agentName, publishLoading, showTuningGuide, setCurrentPageType, modeType, engineType} =
        usePromptEditStoreV2(store => ({
            appId: store.agentConfig.agentInfo.appId!,
            agentName: store.agentConfig.agentInfo.name,
            publishLoading: store.publishLoading,
            showTuningGuide: store.showTuningGuide,
            setCurrentPageType: store.setCurrentPageType,
            modeType: store.agentConfig.agentInfo.modeType,
            engineType: store.agentConfig.agentJson.model?.engineType,
        }));
    const {fetchAllQ2CList} = useQ2CList(appId);

    const agentLogExt = useMemo(
        () => ({
            [EVENT_EXT_KEY_CONST.AGENT_ID]: appId,
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentName,
        }),
        [appId, agentName]
    );

    // 右侧抽屉逻辑
    const {showRightPanel, setShowRightPanel} = useLayoutStore(store => ({
        showRightPanel: store.showRightPanel,
        setShowRightPanel: store.setShowRightPanel,
    }));

    const [openPublishSuccessModalV2, setOpenPublishSuccessModalV2] = useState<boolean>(false);

    // 打开发布成功弹窗
    const openPublishSuccessModal = () => {
        // 改为打开发布成功弹窗
        setOpenPublishSuccessModalV2(true);

        if (q2cAvailable) {
            // 进入q2c页面前，提前开始获取 q2c 调优问答对
            fetchAllQ2CList();
        }

        displayLog(EVENT_PAGE_CONST.PUBLISH_SUCCESS, {...agentLogExt});
    };

    // 发布逻辑
    const {
        canPublishAfterEdit,
        allowClickPublish,
        viewPermission,
        setViewPermission,
        handlePublish,
        publishWorkflow,
        setPublishWorkflow,
    } = usePublishAction(openPublishSuccessModal);

    const {permission} = ViewPermissionMap[viewPermission];

    // 是否支持 q2c 发布, deepSeek 模型不支持 q2c 发布
    const q2cAvailable =
        AgentPermission.PUBLIC === permission &&
        modeType !== AgentModeType.Workflow &&
        !deepSeekModelOptions.includes(engineType);

    const [outputType, setOutputType] = useState<OutputType | null>(null);
    // 是否已发布
    const [showPublishSuccess, setShowPublishSuccess] = useState<boolean>(false);

    // 是否能部署(选择私有不能)
    const canDeploy = viewPermission !== ViewAgentPermission.PRIVATE;

    // 是否设置微信小程序隐私信息
    const [wxSetPrivacy, setWxSetPrivacy] = useState(false);

    // 设置部署
    const {setWxAuthMap, setWxDeploy} = useWeChatAuthStore(store => ({
        setWxAuthMap: store.setWxAuthMap,
        setWxDeploy: store.setWxDeploy,
    }));

    const [firstDeployWxMiniProgram, setFirstDeployWxMiniProgram] = useState<boolean>(false);

    const outputTypeChange = useCallback(
        (type: OutputType) => {
            setOutputType(type);
            setShowRightPanel(true);

            const value = CardTypeLogMap[type];
            clickLog(value, EVENT_PAGE_CONST.PUBLISH_SETTING, {
                ...agentLogExt,
            });
        },
        [setShowRightPanel, setOutputType, clickLog, agentLogExt]
    );

    useEffect(() => {
        if (!showRightPanel) {
            setOutputType(null);
        }
    }, [showRightPanel, setOutputType]);

    // 根据账号类型打开抽屉
    const onOpenAuthDrawer = useCallback(
        (type: AgentPublishChannelType) => {
            switch (type) {
                case WxAccountType.WxServer:
                    outputTypeChange(OutputType.WeChatServerAccount);
                    break;
                case WxAccountType.WxSubscribe:
                    outputTypeChange(OutputType.WeChatSubscribeAccount);
                    break;
                case WxAccountType.WxMiniApp:
                    outputTypeChange(OutputType.WeChatMiniProgram);
                    break;
                case XmiChannelType.XmiAppStore:
                    outputTypeChange(OutputType.XmiAppStore);
                    break;
                default:
                    break;
            }
        },
        [outputTypeChange]
    );

    const publishDeployRef = useRef<HTMLDivElement>(null);

    const navigate = useNavigate();

    // 必须在同一层级
    const execPublish = useCallback(() => {
        // 已完成发布
        if (showPublishSuccess) {
            // 返回我的智能体页面
            navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
        } else {
            // 关闭右侧抽屉
            setShowRightPanel(false);
            // 发布
            handlePublish();
            clickLog(EVENT_VALUE_CONST.PUBLISH, EVENT_PAGE_CONST.PUBLISH_SETTING, {...agentLogExt});
        }
        // 需要注意，handlePublish里面用到的依赖数据，都需要在这里添加进去
    }, [showPublishSuccess, navigate, setShowRightPanel, handlePublish, clickLog, agentLogExt]);

    const handleSetViewPermission = useCallback(
        (type: ViewAgentPermission) => {
            setViewPermission(type);
            // 关闭授权面板
            if (type === ViewAgentPermission.PRIVATE) {
                setShowRightPanel(false);
            }
        },
        [setViewPermission, setShowRightPanel]
    );

    // 打点
    useEffect(() => {
        agentLogExt[EVENT_EXT_KEY_CONST.AGENT_ID] && displayLog(EVENT_PAGE_CONST.PUBLISH_SETTING, {...agentLogExt});
    }, [agentLogExt, displayLog]);

    const onPageReturn = useCallback(() => {
        agentLogExt[EVENT_EXT_KEY_CONST.AGENT_ID] &&
            displayLog(EVENT_PAGE_CONST.CODELESS_CREATE, {
                ...agentLogExt,
                [EVENT_EXT_KEY_CONST.CREATE_PAGE_TYPE]: CreatePageType.EDIT,
            });
        setCurrentPageType(CreatePageType.EDIT);
    }, [agentLogExt, displayLog, setCurrentPageType]);

    // 获取带审核的应用信息
    const getAgentInfo = useCallback(async () => {
        const agentInfo = await agentListApi.getAgentDetail(appId);
        setFirstDeployWxMiniProgram(agentInfo.firstDeployWxMiniProgram);

        // 存储微信授权相关信息
        const wxAuthList = agentInfo?.wxAuthList || [];
        const wxAuthMap = {...initWeChatAuthState.wxAuthMap};

        wxAuthList?.forEach(item => {
            wxAuthMap[item.type] = item;
        });

        // 存储小米应用商店授权相关信息
        if (agentInfo?.xmiDeployInfo) {
            wxAuthMap[XmiChannelType.XmiAppStore] = agentInfo.xmiDeployInfo;
        }

        setWxAuthMap(wxAuthMap);

        // 存储微信选中渠道相关信息（未授权不能选中）
        const deployChannels = agentInfo?.deployChannels || [];
        const wxDeployMap = {...initWeChatAuthState.wxDeployMap};

        deployChannels.forEach(type => {
            wxDeployMap[type] = wxAuthMap[type]?.auth;
        });

        setWxDeploy(wxDeployMap);
    }, [appId, setWxAuthMap, setFirstDeployWxMiniProgram, setWxDeploy]);

    // 获取最新授权数据
    useEffect(() => {
        getAgentInfo();
    }, [getAgentInfo]);

    // 发布设置
    const elementRef = useRef(null);

    // 监控页面在视口展现
    useEffect(() => {
        const currentElement = elementRef.current;
        const observer = new IntersectionObserver(
            entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        cancelDelayedReport(UserAction.DisplayPublishDeploy);
                        // 触发只一次后可以停止观察
                        observer?.unobserve(entry.target);
                    }
                });
            },
            {threshold: 0.1} // 元素至少有10%进入视口时触发回调
        );

        if (currentElement) {
            observer?.observe(currentElement);
        }

        return () => {
            if (currentElement) {
                observer?.unobserve(currentElement);
            }
        };
    }, []);

    // 发布成功弹窗点击完成
    const handleSuccessModalOk = useCallback(() => {
        setShowPublishSuccess(false);
        execPublish();
    }, [execPublish]);

    const handleCancelSuccessModal = useCallback(() => {
        setShowPublishSuccess(false);
    }, []);

    const getModalWidth = useCallback(() => {
        return modeType === AgentModeType.Workflow ? 488 : 800;
    }, [modeType]);

    // 获取当前智能体部署微信小程序是否已经设置了隐私协议相关信息
    useEffect(() => {
        (async () => {
            const res = await api.getWxPrivacy({appId});
            if (res.wxPrivacyInfo?.user?.developerName && res.wxPrivacyInfo?.user?.contact) {
                setWxSetPrivacy(true);
            }
        })();
    }, [appId, setWxSetPrivacy]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        colorTextDisabled: 'rgba(85,98,242, 0.4)',
                    },
                },
            }}
        >
            <div
                className="absolute z-[100] flex h-full w-full min-w-[1280px] overflow-auto scroll-smooth bg-gray-layout"
                ref={publishDeployRef}
            >
                <div className="w-full flex-shrink">
                    {/** 头部 */}
                    <Header
                        publishBtn={
                            <div className="relative">
                                <Tooltip title={canPublishAfterEdit ? undefined : '请更新智能体配置或修改发布类型'}>
                                    <Button
                                        type="primary"
                                        onClick={execPublish}
                                        loading={publishLoading}
                                        disabled={!showPublishSuccess && (!allowClickPublish || !canPublishAfterEdit)}
                                        className="flex h-[30px] min-w-[58px] items-center rounded-full font-semibold"
                                    >
                                        {publishLoading ? '发布中' : showPublishSuccess ? '完成' : '发布'}
                                    </Button>
                                </Tooltip>
                                <CustomPopover
                                    open={showTuningGuide}
                                    placement="left"
                                    autoAdjustOverflow={false}
                                    content="点击发布智能体吧"
                                    type="primary"
                                    priorityLevel={100}
                                >
                                    <div className="absolute -left-[15px] top-1/2 -translate-x-1/2 -translate-y-1/2">
                                        {showTuningGuide && <FlashPoint />}
                                    </div>
                                </CustomPopover>
                            </div>
                        }
                        onPageReturn={onPageReturn}
                    />
                    <ScrollContainer
                        className="flex h-[calc(100vh-60px)] justify-center overflow-y-auto"
                        ref={elementRef}
                    >
                        <div
                            className={classNames('flex', {
                                'w-[820px]': !showRightPanel,
                            })}
                        >
                            <div className="px-6 py-4">
                                <div className="rounded-[18px] bg-white p-6">
                                    {/** 发布方式 */}
                                    <PublishMode
                                        viewPermission={viewPermission}
                                        setViewPermission={handleSetViewPermission}
                                        agentLogExt={agentLogExt}
                                        publishWorkflow={publishWorkflow}
                                        setPublishWorkflow={setPublishWorkflow}
                                    />

                                    {/** 部署渠道 */}
                                    <DeployChannel
                                        canDeploy={canDeploy}
                                        viewPermission={viewPermission}
                                        onOpenAuthDrawer={onOpenAuthDrawer}
                                        firstDeployWxMiniProgram={firstDeployWxMiniProgram}
                                        agentLogExt={agentLogExt}
                                        wxSetPrivacy={wxSetPrivacy}
                                    />
                                </div>

                                {/** 部署卡片 */}
                                <div className="p-6 text-[14px] text-gray-tertiary">
                                    更多部署方式请查看以下说明文档，您也可以在发布后前往【我的智能体-部署】查看
                                </div>
                                <Row justify="space-between">
                                    {outputTypeCardList.map(item => (
                                        <Col key={item.key} span={12} className="pb-4 odd:pr-[8px] even:pl-[8px]">
                                            <Card
                                                available={canDeploy}
                                                outputTypeInfo={item}
                                                curOutputType={outputType}
                                                outputTypeChange={outputTypeChange}
                                                cardType={CardType.Publish}
                                            />
                                        </Col>
                                    ))}
                                </Row>
                            </div>
                        </div>
                    </ScrollContainer>
                </div>

                {/** 发布成功弹窗 */}
                <StyledModal
                    open={showPublishSuccess}
                    onOk={handleSuccessModalOk}
                    onCancel={handleCancelSuccessModal}
                    width={getModalWidth()}
                    footer={[
                        <Button
                            key="submit"
                            type="primary"
                            onClick={handleSuccessModalOk}
                            className={classNames('rounded-[100px]', {
                                'w-[360px]': modeType === AgentModeType.Workflow,
                                'w-[58px]': modeType !== AgentModeType.Workflow,
                            })}
                        >
                            完成
                        </Button>,
                    ]}
                    centered
                    isWorkflowMode={modeType === AgentModeType.Workflow}
                    closeIcon={null}
                    maskClosable={false}
                >
                    <PublishSuccess agentPermission={permission} />
                </StyledModal>

                {/** 右侧对外部署面板区 */}
                <RightPanel
                    className={classNames('z-51 right-0 top-0 h-screen  flex-shrink flex-row-reverse', {
                        'w-full': showRightPanel,
                    })}
                >
                    {(outputType && (
                        <OutputDetail
                            outputType={outputType}
                            appId={appId}
                            agentLogExt={agentLogExt}
                            getAgentInfo={getAgentInfo}
                            setWxSetPrivacy={setWxSetPrivacy}
                        />
                    )) ||
                        null}
                </RightPanel>
                <PublishSuccessModalV2
                    open={openPublishSuccessModalV2}
                    appId={appId}
                    q2cAvailable={q2cAvailable}
                    agentPermission={ViewPermissionMap[permission].permission}
                />
            </div>
        </ConfigProvider>
    );
};

export default PublishDeploy;
