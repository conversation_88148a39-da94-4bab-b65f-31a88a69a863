import classNames from 'classnames';
import React, {useCallback, useEffect, useMemo} from 'react';
import {Button, Checkbox, ConfigProvider, Space, Tag, Tooltip} from 'antd';
import {WxAccountType, WxAccountLogMap, XmiChannelType, AgentPublishChannelType} from '@/api/agentDeploy/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import useUnbindWxAccount from '@/modules/agentOutput/hooks/useUnbindWxAccount';
import {useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {WechatOfficialType, AgentLogExt} from '@/utils/loggerV2/interface';
import CustomPopover from '@/components/Popover';
import {ViewAgentPermission} from '@/store/agent/utils';

interface DeployConfig {
    label: string;
    value: AgentPublishChannelType;
    icon: string;
    bindStatus?: boolean; // 是否已绑定 仅微信有 小米无此字段
    authStatus: boolean; // 授权状态
    checked: boolean; // 是否选中
    msg: string; // 提示信息
    desc?: string; // 部署渠道描述
    /** 是否需授权，是展示授权状态，仅微信需授权，小米无需授权 */
    needAuth: boolean;
}

const WX_ICON =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/wxIcon.png';
const XMI_ICON =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/milogo.png';

const deployConfigsMaps: Record<AgentPublishChannelType, DeployConfig> = {
    [XmiChannelType.XmiAppStore]: {
        label: '小米应用商店',
        value: XmiChannelType.XmiAppStore,
        icon: XMI_ICON,
        authStatus: false,
        // 小米无需授权
        needAuth: false,
        checked: false,
        msg: '',
        desc: '分发至小米应用商店的智能体不包含数字人、语音对话、拍照、快捷指令等能力',
    },
    [WxAccountType.WxServer]: {
        label: '微信公众号-服务号',
        value: WxAccountType.WxServer,
        icon: WX_ICON,
        bindStatus: false,
        needAuth: true,
        authStatus: false,
        checked: false,
        msg: '',
    },
    [WxAccountType.WxSubscribe]: {
        label: '微信公众号-订阅号',
        value: WxAccountType.WxSubscribe,
        icon: WX_ICON,
        bindStatus: false,
        needAuth: true,
        authStatus: false,
        checked: false,
        msg: '',
    },
    [WxAccountType.WxMiniApp]: {
        label: '微信小程序',
        value: WxAccountType.WxMiniApp,
        icon: WX_ICON,
        bindStatus: false,
        needAuth: true,
        authStatus: false,
        checked: false,
        msg: '',
    },
};

const defDeployConfigList = [
    deployConfigsMaps[WxAccountType.WxServer],
    deployConfigsMaps[WxAccountType.WxSubscribe],
    deployConfigsMaps[WxAccountType.WxMiniApp],
];

interface DeployChannelCardProps {
    /** 是否可以选择部署 仅自己访问不可部署到微信和小米 仅公开访问可部署到小米 */
    canDeploy: boolean;
    config: DeployConfig;
    wxSetPrivacy: boolean;
    popInfo?: {
        open: boolean;
        title: React.ReactNode;
        close: () => void;
    };
    onAuthChange: (config: DeployConfig) => void;
    changeCheckedStatus: (config: DeployConfig, checked: boolean) => void;
    onOpenAuthDrawer: (type: AgentPublishChannelType) => void;
}

// eslint-disable-next-line complexity
const DeployChannelCard: React.FC<DeployChannelCardProps> = ({
    canDeploy,
    config,
    wxSetPrivacy,
    popInfo,
    onAuthChange,
    changeCheckedStatus,
    onOpenAuthDrawer,
}) => {
    // 关闭引导气泡
    const closePop = useCallback(() => {
        if (!popInfo?.open) return;

        popInfo.close && popInfo.close();
    }, [popInfo]);

    // 切换授权
    const onAuth = useCallback(
        (e: any) => {
            // 取消事件冒泡
            e.stopPropagation();
            onAuthChange(config);
            closePop();
        },
        [onAuthChange, config, closePop]
    );

    // 点击选中
    const onChange = useCallback(
        (e: any) => {
            const checked = e.target.checked;
            changeCheckedStatus(config, checked);
            closePop();
        },
        [changeCheckedStatus, closePop, config]
    );

    const openAuthDrawer = useCallback(
        (type: AgentPublishChannelType) => {
            if (!canDeploy) return;
            onOpenAuthDrawer(type);
            closePop();
        },
        [canDeploy, closePop, onOpenAuthDrawer]
    );

    const buttonText = useMemo(() => {
        const {authStatus, bindStatus} = config;

        return authStatus ? (bindStatus ? '解绑' : '去绑定') : '点击授权';
    }, [config]);

    const checkboxTips = useMemo(() => {
        if (config.value === XmiChannelType.XmiAppStore && !canDeploy) {
            return '公开访问的智能体才可对外部署';
        }

        if (config.value !== XmiChannelType.XmiAppStore && !canDeploy) {
            return '公开访问或链接访问的智能体才可对外部署';
        }

        if (config.needAuth && !config.authStatus) {
            return '请先完成授权';
        }

        return '';
    }, [canDeploy, config.authStatus, config.needAuth, config.value]);

    const checkboxDisabled = useMemo(() => {
        return (
            !canDeploy ||
            (config.needAuth && !config.authStatus) ||
            (config.value === WxAccountType.WxMiniApp && !wxSetPrivacy)
        );
    }, [canDeploy, config.authStatus, config.needAuth, config.value, wxSetPrivacy]);

    return (
        <ConfigProvider theme={{components: {Popover: {zIndexPopup: 1000}}}}>
            <div
                className={classNames(
                    'mt-4 flex h-[56px] items-center justify-between rounded-[9px] py-5 pl-6 hover:bg-[#5562F2] hover:bg-opacity-[0.08]',
                    {
                        'border-[1px] border-[#5562F2] bg-[#5562F2] bg-opacity-[0.08]': config.checked,
                        'bg-[#F5F6F9] ': !config.checked,
                    }
                )}
            >
                <div className="flex items-center justify-center text-sm">
                    <Tooltip title={checkboxTips}>
                        {config.checked ? (
                            <Checkbox disabled={checkboxDisabled} defaultChecked checked onChange={onChange} />
                        ) : (
                            <Checkbox disabled={checkboxDisabled} defaultChecked={config.checked} onChange={onChange} />
                        )}
                    </Tooltip>
                    <img className="ml-6 mr-3 h-[29px] w-[29px]" src={config.icon} />
                    <CustomPopover
                        type="primary"
                        placement="rightTop"
                        open={popInfo?.open || false}
                        title={popInfo?.title}
                        onClose={popInfo?.close}
                        exitTime
                        arrow={{pointAtCenter: true}}
                        align={{
                            targetOffset: [0, -5],
                        }}
                        rootClassName="max-w-[276px]"
                    >
                        <Space size={3}>
                            <span className="font-medium">{config.label}</span>
                            {config.desc && (
                                <Tooltip title={config.desc} rootClassName="max-w-[283px]">
                                    <span className="iconfont icon-questionCircle cursor-pointer text-sm text-gray-tertiary"></span>
                                </Tooltip>
                            )}
                            {config.msg && (
                                <span className="ml-[5px] text-xs leading-[22px] text-gray-tertiary">{config.msg}</span>
                            )}
                        </Space>
                    </CustomPopover>
                    {config.needAuth && (
                        <Tag color={config.authStatus ? 'success' : ''} bordered={false} className="ml-8">
                            {config.authStatus ? '已授权' : '未授权'}
                        </Tag>
                    )}
                    {config.value === WxAccountType.WxMiniApp && !wxSetPrivacy && (
                        <span
                            onClick={() => openAuthDrawer(config.value)}
                            className={`ml-2 ${canDeploy ? 'cursor-pointer' : ''}`}
                        >
                            请完善服务协议
                        </span>
                    )}
                </div>
                {config.value === XmiChannelType.XmiAppStore ? (
                    <Button type="link" onClick={onAuth} className="text-[14px]" disabled={!canDeploy}>
                        查看
                    </Button>
                ) : (
                    <Tooltip
                        title={
                            canDeploy
                                ? config.authStatus
                                    ? config.value === WxAccountType.WxMiniApp
                                        ? '将微信小程序与文心智能体解绑'
                                        : config.value === WxAccountType.WxSubscribe
                                        ? '将微信公众号（订阅号）与文心智能体解绑'
                                        : '将微信公众号（服务号）与文心智能体解绑'
                                    : ''
                                : '公开访问或链接访问的智能体才可对外部署'
                        }
                    >
                        <Button type="link" onClick={onAuth} className="text-[14px]" disabled={!canDeploy}>
                            {buttonText}
                        </Button>
                    </Tooltip>
                )}
            </div>
        </ConfigProvider>
    );
};

interface DeployChannelProps {
    canDeploy: boolean;
    viewPermission: ViewAgentPermission;
    firstDeployWxMiniProgram: boolean;
    onOpenAuthDrawer: (type: AgentPublishChannelType) => void;
    agentLogExt: AgentLogExt;
    wxSetPrivacy: boolean;
}

const DeployChannel: React.FC<DeployChannelProps> = ({
    canDeploy,
    viewPermission,
    onOpenAuthDrawer,
    firstDeployWxMiniProgram,
    agentLogExt,
    wxSetPrivacy,
}) => {
    const {modalContextHolder, confirm} = useUnbindWxAccount();
    // 打点
    const {clickLog} = useUbcLogV2();

    const {agentInfo} = usePromptEditStoreV2(store => ({
        agentInfo: store.agentConfig.agentInfo,
    }));
    const {wxAuthMap, wxDeployMap, setCheckedStatus} = useWeChatAuthStore(store => ({
        wxAuthMap: store.wxAuthMap,
        wxDeployMap: store.wxDeployMap,
        setCheckedStatus: store.setCheckedStatus,
    }));
    // 小米应用商店已经全量，直接白名单设为true
    const xmiAppStoreDeployInWhite = true;

    // 初始化options(根据wxAuthMap变动进行更新)
    const options = useMemo(() => {
        const list = [...defDeployConfigList];

        // 如果开启了小米应用商店白名单，则将小米应用商店放到最前面
        if (xmiAppStoreDeployInWhite) {
            list.unshift(deployConfigsMaps[XmiChannelType.XmiAppStore]);
        }

        return list.map(item => {
            const copyItem = {...item};

            copyItem.authStatus = !!wxAuthMap?.[item.value]?.auth;

            /** 只有 3:微信小程序 需要判断是否设置了隐私协议（1:微信服务号 2:微信订阅号 无需判断） */
            copyItem.checked =
                !!wxAuthMap?.[item.value]?.auth &&
                wxDeployMap?.[item.value] &&
                (item.value === WxAccountType.WxMiniApp ? wxSetPrivacy : true);

            if (item.value !== XmiChannelType.XmiAppStore) {
                copyItem.bindStatus = !!wxAuthMap?.[item.value]?.wxAppId;
            }

            if (item.value === WxAccountType.WxMiniApp && firstDeployWxMiniProgram) {
                copyItem.msg = '(首次部署需微信审核)';
            }

            if (item.value === XmiChannelType.XmiAppStore && !wxAuthMap[XmiChannelType.XmiAppStore].deployed) {
                copyItem.msg = '(首次部署需小米审核)';
            }

            return copyItem;
        });
    }, [firstDeployWxMiniProgram, wxAuthMap, wxDeployMap, wxSetPrivacy, xmiAppStoreDeployInWhite]);

    // 切换授权状态
    const onAuthChange = useCallback(
        (config: DeployConfig) => {
            // 已经授权且绑定 弹窗确认解绑
            if (config.value !== XmiChannelType.XmiAppStore && config.authStatus && config.bindStatus) {
                confirm({
                    appId: agentInfo.appId!,
                    wxAccountType: config.value,
                    updateAuthStatus: () => {},
                });
            } else {
                // 未授权or未绑定，打开授权抽屉
                onOpenAuthDrawer(config.value);

                // 打点
                if (config.value !== XmiChannelType.XmiAppStore) {
                    const type = WxAccountLogMap[config.value] as unknown as WechatOfficialType;

                    clickLog(EVENT_VALUE_CONST.AUTHORIZE, EVENT_PAGE_CONST.PUBLISH_SETTING, {
                        ...agentLogExt,
                        [EVENT_EXT_KEY_CONST.WECHAT_OFFICIAL_TYPE]: type,
                    });
                }
            }
        },
        [confirm, onOpenAuthDrawer, agentInfo.appId, clickLog, agentLogExt]
    );

    // 切换部署平台选中状态
    const changeCheckedStatus = useCallback(
        (config: DeployConfig, checked: boolean) => {
            // 打点
            if (config.value !== XmiChannelType.XmiAppStore) {
                // 更新store数据
                const type = WxAccountLogMap[config.value] as unknown as WechatOfficialType;
                clickLog(EVENT_VALUE_CONST.PLATFORM_TICK, EVENT_PAGE_CONST.PUBLISH_SETTING, {
                    ...agentLogExt,
                    [EVENT_EXT_KEY_CONST.WECHAT_OFFICIAL_TYPE]: type,
                });
            }

            setCheckedStatus(config.value, checked);
        },
        [agentLogExt, clickLog, setCheckedStatus]
    );

    // 访问权限变更时，如果是非公开访问，小米应用商店取消选中
    useEffect(() => {
        if (!xmiAppStoreDeployInWhite) return;

        if (![ViewAgentPermission.PUBLIC, ViewAgentPermission.SHARE_CONFIG].includes(viewPermission)) {
            setCheckedStatus(XmiChannelType.XmiAppStore, false);
        }
    }, [viewPermission, setCheckedStatus, xmiAppStoreDeployInWhite]);

    return (
        <div className="pt-6">
            <div className="text-[18px] font-medium leading-[26px]">选择部署平台</div>
            {options.map(item => (
                <DeployChannelCard
                    key={item.value}
                    canDeploy={
                        // 小米应用商店仅公开访问时才可以部署
                        item.value === XmiChannelType.XmiAppStore
                            ? [ViewAgentPermission.PUBLIC, ViewAgentPermission.SHARE_CONFIG].includes(viewPermission) &&
                              canDeploy
                            : canDeploy
                    }
                    wxSetPrivacy={wxSetPrivacy}
                    config={item}
                    onAuthChange={onAuthChange}
                    changeCheckedStatus={changeCheckedStatus}
                    onOpenAuthDrawer={onOpenAuthDrawer}
                />
            ))}
            {modalContextHolder}
        </div>
    );
};

export default DeployChannel;
