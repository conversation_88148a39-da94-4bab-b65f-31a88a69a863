/* eslint-disable prettier/prettier */
import React, {useCallback, useEffect, useState} from 'react';
import {Radio, Tooltip, Space, Modal, Checkbox} from 'antd';
import {CheckboxChangeEvent} from 'antd/es/checkbox/Checkbox';
import {css} from '@emotion/css';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentPublishChannelType, WxAccountType, XmiChannelType} from '@/api/agentDeploy/interface';
import {WxDeployMapType, initWeChatAuthState, useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {PermissionTypeLogMap, ViewAgentPermission} from '@/store/agent/utils';

const customRadioStyle = css`
    .ant-space {
        flex-wrap: wrap !important;
    }
    .ant-space-item {
        // max-width: 48% !important;
    }
`;

let CACHE = {...initWeChatAuthState};

const permissionOptions = [
    {
        value: ViewAgentPermission.SHARE_CONFIG,
        label: '公开访问与配置',
        disabled: false, // 默认可选
        tips: () => (
            <div>
                公开配置后，其他开发者可以查看和复制智能体非私有资源配置。智能体会额外在智能体商店的“公开配置”中分发，有机会获得更多流量哦~
                <p>私有资源包括知识库、私有插件等</p>
            </div>
        ),
    },
    {
        value: ViewAgentPermission.PUBLIC,
        label: '公开访问',
        disabled: false, // 默认可选
        tips: '',
    },
    {
        value: ViewAgentPermission.LINK,
        label: '链接可访问',
        disabled: false, // 默认可选
        tips: '',
    },
    {
        value: ViewAgentPermission.PRIVATE,
        label: '仅自己可访问（免审）',
        disabled: false, // 默认可选
        tips: '',
    },
];

interface PublishModeProps {
    // 在这里定义你的props类型
    viewPermission: ViewAgentPermission;
    setViewPermission: (viewPermission: ViewAgentPermission) => void;
    agentLogExt: AgentLogExt;
    publishWorkflow: boolean;
    setPublishWorkflow: (publishWorkflow: boolean) => void;
}

const PublishMode: React.FC<PublishModeProps> = ({
    viewPermission,
    setViewPermission,
    agentLogExt,
    publishWorkflow,
    setPublishWorkflow,
}) => {
    const [modal, modalContextHolder] = Modal.useModal();

    const [options, setOptions] = useState<any[]>(permissionOptions);

    const {lastViewPermission, modeType} = usePromptEditStoreV2(store => ({
        lastViewPermission: store.getViewPermission(),
        modeType: store.agentConfig.agentInfo.modeType,
    }));

    const {wxDeployMap, wxAuthMap, setWxDeploy} = useWeChatAuthStore(store => ({
        wxDeployMap: store.wxDeployMap,
        wxAuthMap: store.wxAuthMap,
        setWxDeploy: store.setWxDeploy,
    }));

    const {clickLog} = useUbcLogV2();

    // 切换公开方式
    const handleChange = useCallback(
        (e: any) => {
            // 如果已经选择部署渠道了
            const isDeploy = (map: WxDeployMapType) => {
                return (
                    Object.keys(map).filter(key => {
                        const accountType = key as unknown as AgentPublishChannelType;
                        return map[accountType];
                    }).length > 0
                );
            };

            if (isDeploy(wxDeployMap) && e.target.value === ViewAgentPermission.PRIVATE) {
                // 弹窗确认解绑
                modal.confirm({
                    title: '确定要修改吗？',
                    icon: <ExclamationCircleOutlined />,
                    content:
                        '您的智能体已进行部署，如将发布方式设置为【仅自己可访问】，可能会影响相关功能的使用，确认要修改吗？',
                    cancelText: '取消',
                    okText: '确定',
                    centered: true,
                    autoFocusButton: null,
                    onOk() {
                        // 确认选中
                        setViewPermission(e.target.value);
                        // 缓存
                        CACHE.wxDeployMap = {...wxDeployMap};
                        // 设置为false
                        setWxDeploy(initWeChatAuthState.wxDeployMap);
                    },
                    onCancel() {},
                });
            } else {
                // 上一次为私有的恢复
                if (viewPermission === ViewAgentPermission.PRIVATE && isDeploy(CACHE.wxDeployMap)) {
                    // 还原需要根据授权态来
                    const latestDeploy = {
                        [WxAccountType.WxServer]:
                            wxAuthMap[WxAccountType.WxServer]?.auth && CACHE.wxDeployMap[WxAccountType.WxServer],
                        [WxAccountType.WxSubscribe]:
                            wxAuthMap[WxAccountType.WxSubscribe]?.auth && CACHE.wxDeployMap[WxAccountType.WxSubscribe],
                        [WxAccountType.WxMiniApp]:
                            wxAuthMap[WxAccountType.WxMiniApp]?.auth && CACHE.wxDeployMap[WxAccountType.WxMiniApp],
                        [XmiChannelType.XmiAppStore]:
                            wxAuthMap[XmiChannelType.XmiAppStore]?.auth &&
                            CACHE.wxDeployMap[XmiChannelType.XmiAppStore],
                    };

                    CACHE.wxDeployMap = {...latestDeploy};
                    setWxDeploy(CACHE.wxDeployMap);
                    // 初始化
                    CACHE = {...initWeChatAuthState};
                }

                setViewPermission(e.target.value);
            }

            const value = e.target.value as ViewAgentPermission;
            const type = PermissionTypeLogMap[value];
            clickLog(EVENT_VALUE_CONST.AUTHORITY_SET, EVENT_PAGE_CONST.PUBLISH_SETTING, {
                [EVENT_EXT_KEY_CONST.AUTHORITY_TYPE]: type,
                ...agentLogExt,
            });
        },
        [setViewPermission, wxDeployMap, modal, setWxDeploy, wxAuthMap, viewPermission, clickLog, agentLogExt]
    );

    useEffect(() => {
        // 如果有私有插件，则默认选择私有，其余禁用
        // 如果是工作流模式，则默认选择公开访问
        let defaultPermission = ViewAgentPermission.SHARE_CONFIG;
        if (modeType === AgentModeType.Workflow) {
            defaultPermission =
                lastViewPermission === defaultPermission
                    ? ViewAgentPermission.PUBLIC
                    : lastViewPermission || ViewAgentPermission.PUBLIC;
        } else {
            defaultPermission = lastViewPermission || ViewAgentPermission.SHARE_CONFIG;
        }

        setViewPermission(defaultPermission);

        permissionOptions[0].disabled = modeType === AgentModeType.Workflow;
        setOptions(permissionOptions);
    }, [lastViewPermission, modeType, setOptions, setViewPermission]);

    const handlePublishToWorkflowChange = useCallback(
        (e: CheckboxChangeEvent) => {
            setPublishWorkflow(e.target.checked);
        },
        [setPublishWorkflow]
    );

    return (
        <div>
            {modeType === AgentModeType.Workflow && (
                <>
                    <div className="pb-1 text-[18px] font-medium leading-[26px]">发布设置</div>
                    <Checkbox checked={publishWorkflow} onChange={handlePublishToWorkflowChange} className="mb-6">
                        同时发布工作流至【我的工作流】
                    </Checkbox>
                </>
            )}
            <div className="pb-1 text-[18px] font-medium leading-[26px]">访问权限</div>
            <div className="pb-4 text-[14px] text-gray-tertiary">
                设置公开访问的智能体将上架智能体商店，优质智能体将有机会在百度搜索分发。
            </div>
            <Radio.Group value={viewPermission} onChange={handleChange} className={customRadioStyle}>
                <Space>
                    {options.map(item => {
                        return (
                            <Tooltip
                                key={item.value}
                                placement="top"
                                title={
                                    modeType === AgentModeType.Workflow &&
                                    item.value === ViewAgentPermission.SHARE_CONFIG
                                        ? '工作流模式下创建的智能体暂不支持公开访问与配置'
                                        : ''
                                }
                            >
                                <Radio value={item.value} disabled={item.disabled}>
                                    <span className="text-[14px]">{item.label}</span>
                                    {item.tips && modeType !== AgentModeType.Workflow && (
                                        <Tooltip
                                            overlayStyle={{maxWidth: '380px'}}
                                            align={{
                                                offset: [14, -10],
                                            }}
                                            title={item.tips}
                                            placement="topRight"
                                        >
                                            <span className="iconfont icon-questionCircle ml-[2px] text-sm text-gray-400"></span>
                                        </Tooltip>
                                    )}
                                </Radio>
                            </Tooltip>
                        );
                    })}
                </Space>
            </Radio.Group>
            {modalContextHolder}
        </div>
    );
};

export default PublishMode;
