import classNames from 'classnames';
import {CheckboxChangeEvent} from 'antd/es/checkbox';
import {Button, message, Pagination, Spin, Tabs, Tag, Input, Tooltip, Checkbox} from 'antd';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import debounce from 'lodash/debounce';
import Katex from '@/components/Katex';
import {
    AuditStatus,
    ConfirmationInfo,
    ConfirmationInfoStatus,
    ConfirmationStatus,
    GenerateStatus,
    ReqTypeStatus,
    VideoItem,
} from '@/api/videoGenerate/interface';
import {deleteItem, getConfirmationList, verticalDeleteBatch, submitBatchDownload} from '@/api/videoGenerate';
import ExportFileApi from '@/utils/file';
import {BaseLoadStatus} from '@/store/dataset/baijiahaoStore';
import Icon from '@/components/Icon';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import TrusteeshipBoard from '@/modules/agentPromptEditV2/pc/TuningTab/eduVideoHostingConfirm/TrusteeshipSummary';
import {useConfirmContext} from '@/modules/agentPromptEditV2/pc/TuningTab/context/ConfirmContext';
import {ScrollContainer} from '@/modules/agentPromptEditV2/pc/TuningTab/customStyle';

const PageSize = 100;

const Video = ({src, poster, className}: {src: string; poster: string; className?: string}) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const [time, setTime] = useState('');
    const videoRef = useRef<HTMLVideoElement>(null);
    // 播放暂停切换
    const togglePlay = useCallback(() => {
        if (!videoRef.current) return;

        if (isPlaying) {
            videoRef.current.pause();
        } else {
            videoRef.current.play();
        }

        setIsPlaying(!isPlaying);
    }, [isPlaying]);

    // 全屏切换
    const toggleFullscreen = useCallback(() => {
        videoRef.current?.requestFullscreen();
    }, []);

    const handleLoadedMetadata = useCallback(() => {
        const video = videoRef.current;
        if (!video) return;
        const minutes = Math.floor(video.duration / 60);
        const seconds = Math.floor(video.duration % 60);
        setTime(`${minutes}:${seconds}`);
    }, []);

    return (
        <div className={classNames('group relative', className)}>
            <video
                className="h-full w-full"
                src={src}
                poster={poster}
                ref={videoRef}
                preload="metadata"
                autoPlay={false}
                onLoadedMetadata={handleLoadedMetadata}
            />
            <div className="pointer-events-none invisible absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center gap-2 group-hover:visible">
                <Icon
                    className="pointer-events-auto text-[30px] text-white opacity-90"
                    name="preview"
                    onClick={togglePlay}
                />
                <Icon
                    className="pointer-events-auto text-[30px] text-white opacity-90"
                    name="launch"
                    onClick={toggleFullscreen}
                />
            </div>
            <div className="absolute bottom-0 left-0 flex h-[30px] w-full flex-row-reverse items-center bg-gradient-to-t from-[rgba(0,0,0,0.4)] to-[rgba(0,0,0,0)] px-1.5 text-white">
                {time}
            </div>
        </div>
    );
};

enum ViewVideoStatus {
    生成中,
    生成失败,
    审核中,
    审核失败,
    已可用,
    未知,
}

const tagConfigMap = {
    [ViewVideoStatus.生成中]: {title: '生成中', color: 'warning'},
    [ViewVideoStatus.生成失败]: {title: '生成失败', color: 'error'},
    [ViewVideoStatus.审核中]: {title: '审核中', color: '#0066FF'},
    [ViewVideoStatus.审核失败]: {title: '审核失败', color: 'error'},
    [ViewVideoStatus.已可用]: null,
    [ViewVideoStatus.未知]: null,
};

const getTag = (info: ConfirmationInfo) => {
    const config = tagConfigMap[getVideoStatus(info)];
    if (!config) {
        return null;
    }
    return (
        <Tag
            className="absolute left-[9px] top-2 z-10 rounded-[3px] border-none px-[3px] text-[11px] leading-4"
            color={config.color}
        >
            {config.title}
        </Tag>
    );
};

const failConfirmationInfoStatuses = [
    ConfirmationInfoStatus.视频生成失败,
    ConfirmationInfoStatus.慧播星视频生成失败,
    ConfirmationInfoStatus.提报失败,
];

const processingConfirmationInfoStatuses = [
    ConfirmationInfoStatus.慧播星视频生成中,
    ConfirmationInfoStatus.慧播星视频生成完成,
    ConfirmationInfoStatus.视频生成中,
    ConfirmationInfoStatus.已确权,
];

const successConfirmationInfoStatuses = [ConfirmationInfoStatus.视频生成完成, ConfirmationInfoStatus.已提报];

const getVideoStatus = (info: ConfirmationInfo) => {
    if (failConfirmationInfoStatuses.includes(info.status)) {
        return ViewVideoStatus.生成失败;
    }

    if (processingConfirmationInfoStatuses.includes(info.status)) {
        return ViewVideoStatus.生成中;
    }

    if (info.status === ConfirmationInfoStatus.视频生成完成) {
        return ViewVideoStatus.已可用;
    }

    if (info.status === ConfirmationInfoStatus.已提报) {
        if (info.auditStatus === AuditStatus.审核中) {
            return ViewVideoStatus.审核中;
        }

        if (info.auditStatus === AuditStatus.拒绝) {
            return ViewVideoStatus.审核失败;
        }

        if (info.auditStatus === AuditStatus.通过) {
            return ViewVideoStatus.已可用;
        }
    }

    return ViewVideoStatus.未知;
};

interface PageConfig {
    pageNo: number;
    total: number;
    processedNum: number;
    processingNum: number;
    failNum: number;
}

const generateStatusPageMap: Record<GenerateStatus, keyof PageConfig> = {
    [GenerateStatus.All]: 'total',
    [GenerateStatus.Done]: 'processedNum',
    [GenerateStatus.Processing]: 'processingNum',
    [GenerateStatus.Fail]: 'failNum',
};

const getDescription = (info: ConfirmationInfo) => {
    if (failConfirmationInfoStatuses.includes(info.status)) {
        return <span className="text-error">视频生成失败</span>;
    }

    if (processingConfirmationInfoStatuses.includes(info.status)) {
        return <span className="text-gray-tertiary">视频生成中请等待</span>;
    }

    if (info.status === ConfirmationInfoStatus.视频生成完成) {
        return null;
    }

    if (info.status === ConfirmationInfoStatus.已提报) {
        if (info.auditStatus === AuditStatus.审核中) {
            return <span className="text-gray-tertiary">视频审核中请等待</span>;
        }

        if (info.auditStatus === AuditStatus.拒绝) {
            return <span className="text-error">视频审核失败{info.auditMsg ? `，${info.auditMsg}` : ''}</span>;
        }

        if (info.auditStatus === AuditStatus.通过) {
            return null;
        }
    }

    return null;
};

const ConfirmHostVertical = ({appId}: {appId: string}) => {
    const {clickLog} = useUbcLogV3();

    const {showConfirmModal} = useConfirmContext();
    const [pageConfig, setPageConfig] = useState<PageConfig>({
        pageNo: 1,
        total: 0,
        processedNum: 0,
        processingNum: 0,
        failNum: 0,
    });
    const [generateStatus, setGenerateStatus] = useState<GenerateStatus>(GenerateStatus.All);
    const [loadingStatus, setLoadingStatus] = useState<BaseLoadStatus>(BaseLoadStatus.Normal);

    // 新增搜索状态标记
    const [isSearching, setIsSearching] = useState(false);
    const [keyword, setKeyword] = useState('');

    const containerRef = useRef<HTMLDivElement>(null);
    const abortControllerRef = useRef<AbortController>();
    const handlePageChange = useCallback(
        (page: number) => {
            setPageConfig(oldPage => ({...oldPage, pageNo: page}));
            abortControllerRef.current?.abort();
            const abortController = new AbortController();
            abortControllerRef.current = abortController;

            setLoadingStatus(BaseLoadStatus.Loading);
            // 仅翻页时缓存可用
            getConfirmationList(
                {
                    appId,
                    status: ConfirmationStatus.Confirmed,
                    pageNo: page,
                    pageSize: PageSize,
                    generateStatus,
                    keyword,
                    reqType: ReqTypeStatus.Hosting,
                },
                {signal: abortController.signal}
            )
                .then(data => {
                    setList(data.dataList);
                    setPageConfig({...data, pageNo: page});
                    setLoadingStatus(BaseLoadStatus.Normal);
                    // 清空选中项
                    setSelectedItem(() => new Set());
                })
                .catch(() => {
                    setLoadingStatus(BaseLoadStatus.Error);
                });
        },
        [appId, generateStatus, keyword]
    );

    const handleGenerateStatusChange = useCallback(
        (activeKey: string) => {
            const newGenerateStatus = Number(activeKey) as GenerateStatus;
            const newPageNo = 1;
            setPageConfig(oldPage => ({...oldPage, pageNo: newPageNo}));
            setGenerateStatus(newGenerateStatus);
            abortControllerRef.current?.abort();
            const abortController = new AbortController();
            abortControllerRef.current = abortController;

            setLoadingStatus(BaseLoadStatus.Loading);
            // 仅翻页时缓存可用
            getConfirmationList(
                {
                    appId,
                    status: ConfirmationStatus.Confirmed,
                    pageNo: newPageNo,
                    pageSize: PageSize,
                    generateStatus: newGenerateStatus,
                    keyword,
                    reqType: ReqTypeStatus.Hosting,
                },
                {signal: abortController.signal}
            )
                .then(data => {
                    setList(data.dataList);
                    setTimeout(() => {
                        containerRef.current?.scrollTo({top: 0, behavior: 'smooth'});
                    }, 0);
                    setPageConfig({...data, pageNo: newPageNo});
                    setLoadingStatus(BaseLoadStatus.Normal);
                    // 清空选中项
                    setSelectedItem(() => new Set());
                })
                .catch(() => {
                    setLoadingStatus(BaseLoadStatus.Error);
                });
        },
        [appId, keyword]
    );

    const [list, setList] = useState<ConfirmationInfo[]>([]);
    const [selectedItem, setSelectedItem] = useState<Set<number>>(() => new Set());
    const onSelectItem = useCallback((id: number) => {
        setSelectedItem(oldSet => {
            const newSet = new Set(oldSet);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }

            return newSet;
        });
    }, []);

    const initdRef = useRef(false);
    // 获取确权列表数据
    const getConfirmList = useCallback(() => {
        if (initdRef.current) {
            return;
        }

        let ignore = false;
        const abortController = new AbortController();
        setLoadingStatus(BaseLoadStatus.Loading);
        getConfirmationList(
            {
                appId,
                status: ConfirmationStatus.Confirmed,
                keyword,
                pageNo: 1,
                pageSize: PageSize,
                generateStatus: GenerateStatus.All,
                reqType: ReqTypeStatus.Hosting,
            },
            {signal: abortController.signal}
        )
            .then(data => {
                if (ignore) return;
                initdRef.current = true;
                const newMap = new Map<number, ConfirmationInfo[]>();
                newMap.set(1, data.dataList);
                setGenerateStatus(GenerateStatus.All);
                setList(data.dataList);
                // 当搜索结果为空时，设置 isSearching 为 true
                setIsSearching(!data.dataList?.length);
                setPageConfig({...data, pageNo: 1});
                setLoadingStatus(BaseLoadStatus.Normal);
            })
            .catch(() => {
                setLoadingStatus(BaseLoadStatus.Error);
            });

        return () => {
            ignore = true;
            abortController.abort();
        };
    }, [appId, keyword]);

    useEffect(() => {
        return getConfirmList();
    }, [getConfirmList]);

    const handleSearch: React.ChangeEventHandler<HTMLInputElement> = useMemo(
        () =>
            debounce(e => {
                const keyword = e.target.value.trim();
                setKeyword(keyword);

                abortControllerRef.current?.abort();
                const abortController = new AbortController();
                abortControllerRef.current = abortController;

                setLoadingStatus(BaseLoadStatus.Loading);
                getConfirmationList(
                    {
                        appId,
                        status: ConfirmationStatus.Confirmed,
                        pageNo: 1,
                        pageSize: PageSize,
                        generateStatus,
                        keyword,
                        reqType: ReqTypeStatus.Hosting,
                    },
                    {signal: abortController.signal}
                )
                    .then(data => {
                        setList(data.dataList);
                        // 当搜索结果为空时，设置 isSearching 为 true
                        setIsSearching(!data.dataList?.length);

                        setPageConfig({...data, pageNo: 1});
                        setLoadingStatus(BaseLoadStatus.Normal);
                        // 清空选中项
                        setSelectedItem(new Set());
                    })
                    .catch(error => {
                        console.error(error);
                        setLoadingStatus(BaseLoadStatus.Error);
                    });
            }, 500),
        [appId, generateStatus]
    );

    const handleClickSearch = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.QUESTION_KEYWORD_SEARCH);
    }, [clickLog]);

    const handleConfirm = useCallback(
        ({ids}: {ids: number[]}) =>
            (e?: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e?.stopPropagation();

                const submit = async () => {
                    // 提交删除该线索
                    await verticalDeleteBatch({appId, recordIds: ids});
                    message.success('删除成功');
                    abortControllerRef?.current?.abort();
                    const abortController = new AbortController();
                    abortControllerRef.current = abortController;
                    // 乐观更新，先视觉上删除该项目，等到从 server 拿到新数据后再更新完整的一页
                    setList(list => list.filter(info => !ids.includes(info.itemId)));

                    await getConfirmationList(
                        {
                            appId,
                            status: ConfirmationStatus.Confirmed,
                            keyword,
                            generateStatus,
                            pageNo: pageConfig.pageNo,
                            pageSize: PageSize,
                            reqType: ReqTypeStatus.Hosting,
                        },
                        {signal: abortController.signal}
                    ).then(data => {
                        setList(data.dataList);
                        setPageConfig({...data, pageNo: pageConfig.pageNo});
                        if (pageConfig.pageNo > 1 && (pageConfig.pageNo - 1) * PageSize >= data.total) {
                            handlePageChange(Math.floor(data.total / PageSize + 1));
                        }

                        setSelectedItem(oldSet => {
                            const newSet = new Set(oldSet);
                            ids?.forEach(id => {
                                if (newSet.has(id)) {
                                    newSet.delete(id);
                                }
                            });
                            return newSet;
                        });
                    });
                };

                submit();
            },
        [appId, generateStatus, handlePageChange, keyword, pageConfig.pageNo]
    );

    // 单个删除-一期接口
    const handleDelete = useCallback(
        (id: number): React.MouseEventHandler<HTMLElement> =>
            async e => {
                e.stopPropagation();

                clickLog(EVENT_VALUE_CONST.QUESTION_ADJUST_DELETE, {
                    eQuestionAdjustId: String(id),
                });

                showConfirmModal({
                    content: (
                        <span className="flex items-center gap-2">
                            <Icon name="info-circle-fill" className="text-[#FF8200]" />
                            删除后无法恢复，确认删除吗
                        </span>
                    ),
                    async onConfirm() {
                        // 提交该线索
                        await deleteItem({appId, items: [{itemId: id}]});
                        message.success('删除成功');
                        abortControllerRef?.current?.abort();
                        const abortController = new AbortController();
                        abortControllerRef.current = abortController;
                        // 乐观更新，先视觉上删除该项目，等到从 server 拿到新数据后再更新完整的一页
                        setList(list => list.filter(info => id !== info.itemId));
                        await getConfirmationList(
                            {
                                appId,
                                status: ConfirmationStatus.Confirmed,
                                keyword,
                                generateStatus,
                                pageNo: pageConfig.pageNo,
                                pageSize: PageSize,
                                reqType: ReqTypeStatus.Hosting,
                            },
                            {signal: abortController.signal}
                        ).then(data => {
                            setList(data.dataList);
                            setPageConfig({...data, pageNo: pageConfig.pageNo});
                            // 清空选中项
                            setSelectedItem(() => new Set());
                            if (pageConfig.pageNo > 1 && (pageConfig.pageNo - 1) * PageSize >= data.total) {
                                handlePageChange(Math.floor(data.total / PageSize + 1));
                            }
                        });
                    },
                });
            },
        [appId, clickLog, generateStatus, handlePageChange, keyword, pageConfig.pageNo, showConfirmModal]
    );

    // 用于处理批量删除操作
    const handleDeleteBatch = useCallback(
        (ids: number[]): React.MouseEventHandler<HTMLElement> =>
            async e => {
                e.stopPropagation();

                clickLog(EVENT_VALUE_CONST.QUESTION_BATCH_DELETE, {
                    eTickNumber: ids.length,
                });

                showConfirmModal({
                    content: (
                        <span className="flex items-center gap-2">
                            <Icon name="info-circle-fill" className="text-[#FF8200]" />
                            {ids.length}个视频删除后无法恢复，确认删除吗
                        </span>
                    ),
                    onConfirm: handleConfirm({ids}),
                });
            },
        [clickLog, handleConfirm, showConfirmModal]
    );

    // 新增下载按钮loading状态
    const [isDownloading, setIsDownloading] = useState(false);
    const handleDownloadBatch = useCallback(
        (ids: number[]): React.MouseEventHandler<HTMLElement> =>
            async e => {
                e.stopPropagation();

                if (isDownloading) return;
                setIsDownloading(true);

                // 由于 单个/批量同一个接口 但是打点数据不一样，所以这里利用ids.length 判断是单个还是批量
                if (ids.length === 1) {
                    clickLog(EVENT_VALUE_CONST.QUESTION_ADJUST_DOWNLOAD, {
                        eQuestionAdjustId: String(ids[0]),
                    });
                } else {
                    clickLog(EVENT_VALUE_CONST.QUESTION_BATCH_DOWNLOAD, {
                        eTickNumber: ids.length,
                    });
                }

                try {
                    const result = await submitBatchDownload({appId, recordIds: ids});

                    const downloadQueue = async (videos: VideoItem[]) => {
                        for (const video of videos) {
                            ExportFileApi.downloadFile({
                                url: video.videoUrlWithFilename,
                            });
                            await new Promise<void>(resolve => {
                                setTimeout(() => {
                                    resolve();
                                }, 700);
                            });
                        }
                    };

                    downloadQueue(result.list);
                } catch (error) {
                    console.error('批量下载出错:', error);
                } finally {
                    setTimeout(() => {
                        setIsDownloading(false);
                    }, 10000);
                    // 清空选中项
                    setSelectedItem(() => new Set());
                }
            },
        [appId, clickLog, isDownloading]
    );

    const selectPage = useMemo(() => list.every(info => selectedItem.has(info.itemId)), [list, selectedItem]);
    const handleSelectPage = useCallback(
        (e: CheckboxChangeEvent) => {
            if (!list.length) return;

            if (e.target.checked) {
                setSelectedItem(oldSet => {
                    const newSet = new Set(oldSet);
                    list.forEach(info => {
                        newSet.add(info.itemId);
                    });
                    return newSet;
                });
            } else {
                setSelectedItem(() => new Set()); // 清空选择
            }
        },
        [list]
    );

    const handleReload = useCallback(() => {
        handlePageChange(pageConfig.pageNo);
    }, [handlePageChange, pageConfig.pageNo]);

    // eslint-disable-next-line complexity
    const content = useMemo(() => {
        if (loadingStatus === BaseLoadStatus.Loading) {
            return (
                <div className="flex h-full w-full items-center justify-center">
                    <Spin />
                </div>
            );
        }

        if (loadingStatus === BaseLoadStatus.Error) {
            return (
                <div className="flex h-full w-full flex-col items-center justify-center gap-2">
                    加载失败
                    <Button onClick={handleReload} type="primary">
                        重试
                    </Button>
                </div>
            );
        }

        // 根据当前的 generateStatus 决定是否显示批量操作按钮
        const showBatchButtons = [GenerateStatus.Done, GenerateStatus.Fail].includes(generateStatus);

        return (
            <>
                <div
                    className={classNames('flex items-center justify-between', {
                        'mb-6': showBatchButtons,
                        'mb-3': !showBatchButtons,
                    })}
                >
                    {showBatchButtons && (
                        <div className="flex">
                            <Checkbox
                                onChange={handleSelectPage}
                                // eslint-disable-next-line react/jsx-no-duplicate-props
                                checked={selectPage && list.length > 0}
                            >
                                全选当前页
                            </Checkbox>
                            <span className="ml-[15px] text-gray-tertiary">已选择 {selectedItem.size} 项</span>
                        </div>
                    )}
                    {showBatchButtons && (
                        <div className="flex gap-3">
                            <Tooltip title={selectedItem.size ? '' : '请先选中内容'}>
                                <Button
                                    className="flex h-[30px] w-[86px] items-center justify-center rounded-full"
                                    onClick={handleDeleteBatch(Array.from(selectedItem))}
                                    disabled={!selectedItem.size}
                                >
                                    批量删除
                                </Button>
                            </Tooltip>
                            {/* 仅在不是「处理失败」标签页时显示批量下载按钮 */}
                            {generateStatus !== GenerateStatus.Fail && (
                                <Tooltip title={selectedItem.size ? '' : '请先选中内容'}>
                                    <Button
                                        type="primary"
                                        className="flex h-[30px] w-[86px] items-center justify-center rounded-full"
                                        onClick={handleDownloadBatch(Array.from(selectedItem))}
                                        disabled={!selectedItem.size || isDownloading}
                                    >
                                        批量下载
                                    </Button>
                                </Tooltip>
                            )}
                        </div>
                    )}
                </div>
                <ScrollContainer className="flex h-full flex-col gap-[18px] overflow-auto">
                    {list.length > 0 ? (
                        <>
                            {list.map(info => (
                                <div
                                    onClick={() => onSelectItem(info.itemId)}
                                    key={info.itemId}
                                    className="cursor-pointer rounded-xl bg-white p-3"
                                >
                                    <div className="flex w-full items-center gap-2 border-b border-colorBorderFormList pb-3">
                                        <div className="flex w-0 flex-grow items-center gap-2">
                                            {showBatchButtons && <Checkbox checked={selectedItem.has(info.itemId)} />}
                                            <Katex
                                                className="w-0 flex-grow overflow-hidden font-medium"
                                                keyword={keyword}
                                            >
                                                {info.query}
                                            </Katex>
                                        </div>
                                        <div className="flex flex-shrink-0 items-center gap-[18px]">
                                            {successConfirmationInfoStatuses.includes(info.status) && (
                                                <span
                                                    className="cursor-pointer text-primary hover:opacity-60"
                                                    onClick={handleDownloadBatch([info.itemId])}
                                                >
                                                    下载
                                                </span>
                                            )}
                                            <span
                                                className="cursor-pointer text-primary hover:opacity-60"
                                                onClick={handleDelete(info.itemId)}
                                            >
                                                删除
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex gap-3">
                                        <div className="w-[256px] flex-shrink-0">
                                            <div className="py-3 font-medium">生成视频:</div>
                                            <div className="relative h-[144px] w-[256px] overflow-hidden rounded-lg">
                                                {getTag(info)}
                                                {info.videoUrl ? (
                                                    <Video
                                                        className="h-full w-full"
                                                        src={info.videoUrl}
                                                        poster={info.coverUrl}
                                                    />
                                                ) : info.coverUrl ? (
                                                    <img src={info.coverUrl} className="h-full w-full" />
                                                ) : (
                                                    <div className="h-full w-full bg-gray-quaternary"></div>
                                                )}
                                                <div className="h-full w-full bg-gray-quaternary"></div>
                                                {[
                                                    ConfirmationInfoStatus.慧播星视频生成中,
                                                    ConfirmationInfoStatus.慧播星视频生成完成,
                                                    ConfirmationInfoStatus.视频生成中,
                                                    ConfirmationInfoStatus.已确权,
                                                ].includes(info.status) && (
                                                    <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center bg-black bg-opacity-40 text-white">
                                                        <Icon name="" loading className="h-12 w-12" />
                                                        正在生成中，请耐心等待
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex-1">
                                            <div className="py-3 font-medium">板书解析:</div>
                                            <div className="rounded-lg border border-colorBorderFormList py-3 pl-4 pr-1 hover:border-gray-tertiary">
                                                <Katex
                                                    className="styled-scrollbar h-[120px] overflow-auto pr-1"
                                                    keyword={keyword}
                                                >
                                                    {info.verticalText}
                                                </Katex>
                                            </div>
                                        </div>
                                        <div className="flex-1">
                                            <div className="py-3 font-medium">视频播报脚本:</div>
                                            <div className="rounded-lg border border-colorBorderFormList py-3 pl-4 pr-1 hover:border-gray-tertiary">
                                                <Katex
                                                    className="styled-scrollbar h-[120px] overflow-auto pr-1"
                                                    keyword={keyword}
                                                >
                                                    {info.script}
                                                </Katex>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="mt-3 text-xs">{getDescription(info)}</div>
                                </div>
                            ))}
                            <Pagination
                                className="mb-7 text-right"
                                current={pageConfig.pageNo}
                                total={pageConfig[generateStatusPageMap[generateStatus]]}
                                pageSize={PageSize}
                                showSizeChanger={false}
                                onChange={handlePageChange}
                                hideOnSinglePage
                            />
                        </>
                    ) : (
                        (!list.length || isSearching) && (
                            <div className="flex h-full flex-col items-center justify-center gap-4">
                                <Icon
                                    name="warn"
                                    className="text-[72px] text-[rgba(85,98,242,0.20)]"
                                    hoverStyle={false}
                                />
                                <span className="text-lg font-medium text-gray-tertiary">暂无内容</span>
                            </div>
                        )
                    )}
                </ScrollContainer>
            </>
        );
    }, [
        loadingStatus,
        generateStatus,
        handleSelectPage,
        selectPage,
        list,
        selectedItem,
        handleDeleteBatch,
        handleDownloadBatch,
        isDownloading,
        pageConfig,
        handlePageChange,
        isSearching,
        handleReload,
        keyword,
        handleDelete,
        onSelectItem,
    ]);

    return (
        <div className="relative flex h-full w-full flex-col justify-between overflow-hidden">
            {/* 托管数据看板 */}
            <div className="mt-4 w-full px-6">
                <TrusteeshipBoard />
            </div>
            <div className="flex items-center justify-between px-6">
                <Tabs
                    items={[
                        {label: '全部', key: String(GenerateStatus.All)},
                        {label: '已可用', key: String(GenerateStatus.Done)},
                        {label: '处理中', key: String(GenerateStatus.Processing)},
                    ]}
                    activeKey={String(generateStatus)}
                    onChange={handleGenerateStatusChange}
                    className="flex-1 [&_.ant-tabs-nav]:bg-transparent [&_.ant-tabs-nav]:px-0 [&_.ant-tabs-nav]:before:border-none"
                />
                <Input
                    className="w-[216px] border-[#dee0e7]"
                    placeholder="搜索题目、板书、脚本内容"
                    allowClear
                    suffix={
                        <span className="iconfont icon-search cursor-pointer text-[14px] leading-5 text-black hover:text-primary" />
                    }
                    onFocus={handleClickSearch}
                    onChange={handleSearch}
                />
            </div>
            {/* gap-[18px] */}
            <div
                ref={containerRef}
                className="flex h-[calc(100%-70px)] w-full flex-col overflow-y-auto overflow-x-hidden px-6"
            >
                {content}
            </div>
        </div>
    );
};

export default ConfirmHostVertical;
