/**
 * 未托管授权-内容
 */
import {Button, message, Pagination, Spin} from 'antd';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ConfirmationInfo, ConfirmationStatus, ReqTypeStatus} from '@/api/videoGenerate/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {confirmItem, getConfirmationList} from '@/api/videoGenerate';
import {BaseLoadStatus} from '@/store/dataset/baijiahaoStore';
import Katex from '@/components/Katex';
import Icon from '@/components/Icon';
import {ScrollContainer} from '@/modules/agentPromptEditV2/pc/TuningTab/customStyle';
import {useConfirmContext} from '@/modules/agentPromptEditV2/pc/TuningTab/context/ConfirmContext';
import VideoHostingModal from '@/modules/agentPromptEditV2/pc/TuningTab/components/VideoHostingModal';
import useCheckVideoHostingStatus from '../hooks/useCheckVideoHostingStatus';

const PageSize = 100;

const WaitingConfirmVertical = ({appId}: {appId: string}) => {
    const {clickLog} = useUbcLogV3();

    const [pageConfig, setPageConfig] = useState<{pageNo: number; total: number}>({
        pageNo: 1,
        total: 0,
    });
    const [loadingStatus, setLoadingStatus] = useState<BaseLoadStatus>(BaseLoadStatus.Normal);
    const cacheRef = useRef<Map<number, ConfirmationInfo[]>>(new Map());
    const abortControllerRef = useRef<AbortController>();
    const handlePageChange = useCallback(
        (page: number) => {
            setPageConfig(oldPage => ({...oldPage, pageNo: page}));
            abortControllerRef.current?.abort();
            const abortController = new AbortController();
            abortControllerRef.current = abortController;
            if (cacheRef.current.has(page)) {
                setTimeout(() => {
                    containerRef.current?.scrollTo({top: 0, behavior: 'smooth'});
                }, 0);
                return setList(cacheRef.current.get(page)!);
            }

            setLoadingStatus(BaseLoadStatus.Loading);
            // 仅翻页时缓存可用
            getConfirmationList(
                {
                    appId,
                    status: ConfirmationStatus.Waiting,
                    pageNo: page,
                    pageSize: PageSize,
                    reqType: ReqTypeStatus.Hosting,
                },
                {signal: abortController.signal}
            )
                .then(data => {
                    cacheRef.current.set(page, data.dataList);
                    setList(data.dataList);
                    setPageConfig({total: data.total, pageNo: page});
                    setLoadingStatus(BaseLoadStatus.Normal);
                })
                .catch(error => {
                    console.error(error);
                    setLoadingStatus(BaseLoadStatus.Error);
                });
        },
        [appId]
    );

    const containerRef = useRef<HTMLDivElement>(null);
    const [list, setList] = useState<ConfirmationInfo[]>([]);

    // 控制 托管数量配置弹框的显示状态
    const [open, setOpen] = useState(false);

    const {showConfirmModal} = useConfirmContext();

    const {quantity} = useCheckVideoHostingStatus(appId);

    const initdRef = useRef(false);
    useEffect(() => {
        if (initdRef.current) {
            return;
        }

        let ignore = false;
        const abortController = new AbortController();
        setLoadingStatus(BaseLoadStatus.Loading);

        getConfirmationList(
            {
                appId,
                status: ConfirmationStatus.Waiting,
                pageNo: 1,
                pageSize: PageSize,
                reqType: ReqTypeStatus.Hosting,
            },
            {signal: abortController.signal}
        )
            .then(data => {
                if (ignore) return;
                initdRef.current = true;
                const newMap = new Map<number, ConfirmationInfo[]>();
                newMap.set(1, data.dataList);
                cacheRef.current = newMap;
                setList(data.dataList);
                setPageConfig({total: data.total, pageNo: 1});
                setLoadingStatus(BaseLoadStatus.Normal);
            })
            .catch(error => {
                console.error(error);
                setLoadingStatus(BaseLoadStatus.Error);
            });

        return () => {
            ignore = true;
            abortController.abort();
        };
    }, [appId]);

    const handleConfirm = useCallback(
        ({
            submitReject = false,
            items,
            submitAll = false,
        }: {
            submitReject?: boolean;
            items?: number[];
            submitAll?: boolean;
        }) =>
            (e?: React.MouseEvent<HTMLElement, MouseEvent>) => {
                e?.stopPropagation();

                const submit = async () => {
                    // 提交该线索
                    await confirmItem({appId, items: items?.map(itemId => ({itemId})), submitReject, submitAll});
                    abortControllerRef.current?.abort();
                    const abortController = new AbortController();
                    abortControllerRef.current = abortController;
                    // 乐观更新，先视觉上删除该项目，等到从 server 拿到新数据后再更新完整的一页
                    setList(list => list.filter(info => !items?.includes(info.itemId)));
                    await getConfirmationList(
                        {
                            appId,
                            status: ConfirmationStatus.Waiting,
                            pageNo: pageConfig.pageNo,
                            pageSize: PageSize,
                            reqType: ReqTypeStatus.Hosting,
                        },
                        {signal: abortController.signal}
                    ).then(data => {
                        // 一旦出现任何修改操作，缓存都将过期，需要重新获取
                        const newMap = new Map<number, ConfirmationInfo[]>();
                        newMap.set(pageConfig.pageNo, data.dataList);
                        cacheRef.current = newMap;
                        setList(data.dataList);
                        setPageConfig({total: data.total, pageNo: pageConfig.pageNo});
                        // if (pageConfig.pageNo > 1 && (pageConfig.pageNo - 1) * PageSize >= data.total) {
                        //     handlePageChange(Math.floor(data.total / PageSize + 1));
                        // }
                    });
                };

                if (submitReject) {
                    clickLog(EVENT_VALUE_CONST.QUESTION_ADJUST_REJECT, {
                        ePageType: String(ReqTypeStatus.Hosting),
                        eQuestionAdjustId: String(items?.[0]),
                    });

                    return showConfirmModal({
                        content: (
                            <span className="flex items-center gap-2">
                                <Icon name="info-circle-fill" className="text-[#FF8200]" />
                                驳回后无法恢复，确认驳回吗？
                            </span>
                        ),
                        onConfirm: async () => {
                            await submit();
                            message.success('驳回成功');
                        },
                    });
                }

                submit();
            },
        [appId, clickLog, pageConfig.pageNo, showConfirmModal]
    );

    const handleReload = useCallback(() => {
        handlePageChange(pageConfig.pageNo);
    }, [handlePageChange, pageConfig.pageNo]);

    const handleOkHosting = useCallback(() => {
        setOpen(true);

        clickLog(EVENT_VALUE_CONST.CONFIRM_AND_HOST);
    }, [clickLog]);

    const content = useMemo(() => {
        switch (loadingStatus) {
            case BaseLoadStatus.Loading:
                return (
                    <div className="flex h-full w-full items-center justify-center">
                        <Spin />
                    </div>
                );
            case BaseLoadStatus.Error:
                return (
                    <div className="flex h-full w-full flex-col items-center justify-center">
                        加载失败
                        <Button type="primary" onClick={handleReload} className="mt-3">
                            请重试
                        </Button>
                    </div>
                );
            default:
                if (!list.length && !pageConfig.total) {
                    return (
                        <div className="flex h-full flex-col items-center justify-center gap-4">
                            <Icon name="warn" className="text-[72px] text-[rgba(85,98,242,0.20)]" hoverStyle={false} />
                            <span className="text-lg font-medium text-gray-tertiary">
                                正在为您生成视频主题及脚本，请明天来平台检查内容符合预期后托管
                            </span>
                        </div>
                    );
                }

                return (
                    <>
                        <div className="mb-3 px-6">
                            <span className="text-black-base text-base font-medium">解题内容</span>
                            <span className="ml-[16px] text-sm font-normal text-[#707070]">
                                抽检用于生产解题的视频的板书和脚本内容，符合预期后正式开始托管
                            </span>
                        </div>
                        <ScrollContainer
                            ref={containerRef}
                            className="flex h-full flex-col gap-[18px] overflow-auto pl-6 pr-3"
                        >
                            {list.map(info => (
                                <div key={info.itemId} className="cursor-pointer rounded-xl bg-white p-3">
                                    <div className="flex items-center justify-between gap-8 border-b border-colorBorderFormList pb-3">
                                        <div className="flex w-0 flex-grow items-center gap-2">
                                            <Katex className="w-full flex-shrink font-medium [&>p]:overflow-hidden [&>p]:text-ellipsis [&>p]:whitespace-nowrap">
                                                {info.query}
                                            </Katex>
                                        </div>
                                        <div className="flex flex-shrink-0 items-center gap-[18px]">
                                            <span
                                                className="cursor-pointer text-primary hover:opacity-60"
                                                onClick={handleConfirm({
                                                    items: [info.itemId],
                                                    submitReject: true,
                                                })}
                                            >
                                                驳回
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex gap-3">
                                        <div className="flex-1">
                                            <div className="py-3 font-medium">
                                                板书解析:
                                                <span
                                                    color="#4E6EF2"
                                                    className="ml-1 rounded-[3px] bg-[#E8F3FF] px-[3px] text-xs leading-4 text-[#4E6EF2]"
                                                >
                                                    板书动画请参考视频最终生成效果
                                                </span>
                                            </div>
                                            <div className="rounded-lg border border-colorBorderFormList py-3 pl-4 pr-1 hover:border-gray-tertiary">
                                                <Katex className="styled-scrollbar h-[120px] overflow-auto pr-1">
                                                    {info.verticalText}
                                                </Katex>
                                            </div>
                                        </div>
                                        <div className="flex-1">
                                            <div className="py-3 font-medium">视频播报脚本:</div>
                                            <div className="rounded-lg border border-colorBorderFormList py-3 pl-4 pr-1 hover:border-gray-tertiary">
                                                <Katex className="styled-scrollbar h-[120px] overflow-auto pr-1">
                                                    {info.script}
                                                </Katex>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                            <Pagination
                                current={pageConfig.pageNo}
                                className="mb-7 text-right"
                                total={pageConfig.total}
                                pageSize={PageSize}
                                showSizeChanger={false}
                                onChange={handlePageChange}
                                hideOnSinglePage
                            />
                        </ScrollContainer>
                        <div className="flex h-[65px] w-full items-center justify-center bg-white px-[65px]">
                            <Button
                                type="primary"
                                onClick={handleOkHosting}
                                className="h-[30px] w-[161px] rounded-full p-0"
                            >
                                确认并托管
                            </Button>
                        </div>
                    </>
                );
        }
    }, [
        handleConfirm,
        handleOkHosting,
        handlePageChange,
        handleReload,
        list,
        loadingStatus,
        pageConfig.pageNo,
        pageConfig.total,
    ]);

    return (
        <div className="relative flex h-full flex-col overflow-y-hidden pt-4">
            {content}
            <VideoHostingModal openHosting={open} setOpenHosting={setOpen} quantity={quantity} agreement />
        </div>
    );
};

export default WaitingConfirmVertical;
