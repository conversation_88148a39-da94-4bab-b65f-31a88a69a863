import {createContext, useContext} from 'react';

interface Context {
    fetchData: () => void;
}
const context = createContext<Context>({
    fetchData: () => {
        throw new Error('未找到 EduVideoHostingContextProvider');
    },
});

export default context;

export const EduVideoHostingContextProvider = context.Provider;

export const useEduVideoHostingContextProvider = () => useContext(context);
