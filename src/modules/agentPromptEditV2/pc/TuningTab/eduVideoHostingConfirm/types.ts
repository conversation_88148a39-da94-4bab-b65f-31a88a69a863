/**
 * @file 智能体调优-教育视频托管-类型定义文件
 * @description 定义视频托管相关的类型，包括视频模式、数字人位置、预览数据和设置状态等接口
 * <AUTHOR>
 */
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';

export interface AvatarPosition {
    x: number;
    y: number;
    scale: number;
    height: number;
}

export interface VideoPreviewData {
    status: number;
    showEdit: boolean;
    videoUrl: string;
    figureImg: string;
    backgroundImg: string;
    position: AvatarPosition;
}

export interface VideoSettingsState {
    mode: SCREEN_MODEL;
    avatar: AvatarPosition;
    videoStatus: number;
    showEdit: boolean;
    videoUrl: string;
    figureImg: string;
    backgroundImg: string;
}

export interface VideoSettingsProps {
    visible: boolean;
    onClose: () => void;
    appId: string;
    initialVideoUrl?: string;
    onSuccess?: () => void;
}
