/**
 * @file 智能体调优-教育视频托管-背景选择器组件
 * @description 提供背景图片选择功能，支持从背景库选择或自定义上传
 * <AUTHOR>
 */

/* eslint-disable react/jsx-no-bind */
/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect} from 'react';
import {message} from 'antd';
import {getHorizonBgList, getVerticalBgList, uploadBgImage} from '@/api/videoGenerate';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';
import CustomUpload from './CustomUpload';

interface CustomImage {
    url: string;
    status: 'uploading' | 'done';
    progress: number;
}

interface BackgroundSelectorProps {
    mode: SCREEN_MODEL;
    onBackgroundSelected: (url: string | null) => void;
    onBackgroundCleared: () => void;
    selectedBackgroundUrl?: string | null;
}

interface UploadError extends Error {
    msg?: string;
    message: string;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
    mode,
    onBackgroundSelected,
    onBackgroundCleared,
    selectedBackgroundUrl,
}) => {
    const [activeTab, setActiveTab] = useState<'bg' | 'custom'>('bg');
    const [bgImages, setBgImages] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedBg, setSelectedBg] = useState<number | null>(null);
    const [customBgUrl, setCustomBgUrl] = useState<string | null>(null);
    const [customImages, setCustomImages] = useState<CustomImage[]>([]);

    // 获取背景图片列表
    const fetchBgImages = React.useCallback(
        async (currentMode: SCREEN_MODEL) => {
            try {
                setIsLoading(true);
                // 清空现有背景图片列表和选中状态
                setBgImages([]);
                setSelectedBg(null);
                setCustomBgUrl(null);
                const res =
                    currentMode === SCREEN_MODEL.Horizontal ? await getHorizonBgList() : await getVerticalBgList();
                if (Array.isArray(res)) {
                    const urls = res.map(item => item.url).filter(Boolean);
                    setBgImages(urls);
                    // 自动选择第一个背景图片
                    if (urls.length > 0) {
                        setSelectedBg(0);
                        onBackgroundSelected(urls[0]);
                    }
                } else {
                    message.error('获取背景图片失败：返回数据格式不正确');
                }
            } catch (error) {
                message.error('获取背景图片失败');
            } finally {
                setIsLoading(false);
            }
        },
        [onBackgroundSelected]
    );

    // 监听模式变化，重新获取背景图片
    useEffect(() => {
        if (activeTab === 'bg') {
            fetchBgImages(mode);
        }
    }, [mode, activeTab, fetchBgImages]);

    // 监听背景图片变化，重置选中状态
    useEffect(() => {
        if (!selectedBackgroundUrl) {
            setSelectedBg(null);
            setCustomBgUrl(null);
        }
    }, [selectedBackgroundUrl]);

    // 根据 selectedBackgroundUrl 设置选中状态
    useEffect(() => {
        if (selectedBackgroundUrl) {
            const bgIndex = bgImages.findIndex(url => url === selectedBackgroundUrl);
            if (bgIndex === -1) {
                setCustomBgUrl(selectedBackgroundUrl);
                setSelectedBg(null);
            } else {
                setSelectedBg(bgIndex);
                setCustomBgUrl(null);
            }
        }
    }, [selectedBackgroundUrl, bgImages]);

    // 处理标签切换
    const handleTabChange = React.useCallback(
        (tab: 'bg' | 'custom') => {
            if (tab === activeTab) return;
            setActiveTab(tab);
            if (tab === 'bg') {
                fetchBgImages(mode);
            }
        },
        [activeTab, mode, fetchBgImages]
    );

    // 处理背景库图片选中
    const handleBgImageSelected = (idx: number) => {
        setSelectedBg(idx);
        setCustomBgUrl(null);
        onBackgroundSelected(bgImages[idx]);
    };

    // 处理自定义图片选中
    const handleCustomImageSelected = (url: string) => {
        setCustomBgUrl(url);
        setSelectedBg(null);
        onBackgroundSelected(url);
    };

    // 处理自定义图片上传
    const handleCustomImageUploaded = async (file: File) => {
        const previewUrl = URL.createObjectURL(file);
        const idx = customImages.length;
        setCustomImages(prev => [...prev, {url: previewUrl, status: 'uploading', progress: 0}]);

        try {
            const formData = new FormData();
            formData.append('image', file, 'photo-image.png');
            const imageUrl = await uploadBgImage(formData);

            if (!imageUrl) {
                throw new Error('上传失败：未获取到图片URL');
            }

            setCustomImages(prev =>
                prev.map((img, i) => (i === idx ? {url: imageUrl, status: 'done', progress: 100} : img))
            );
            handleCustomImageSelected(imageUrl);
            // 确保清理预览 URL
            URL.revokeObjectURL(previewUrl);
        } catch (error) {
            const uploadError = error as UploadError;
            message.error(uploadError.msg || uploadError.message || '图片上传失败，请重试');
            setCustomImages(prev => prev.filter((_, i) => i !== idx));
            // 确保在错误时也清理预览 URL
            URL.revokeObjectURL(previewUrl);
        }
    };

    // 处理自定义图片删除
    const handleCustomImageDeleted = (index: number) => {
        const deletedImage = customImages[index];
        if (deletedImage) {
            // 清理 URL.createObjectURL 创建的 URL
            if (deletedImage.status === 'uploading') {
                URL.revokeObjectURL(deletedImage.url);
            }
        }

        setCustomImages(prev => prev.filter((_, i) => i !== index));
        if (customBgUrl === customImages[index]?.url) {
            setCustomBgUrl(null);
            onBackgroundCleared();
        }
    };

    // 处理不使用背景
    const handleClearBackground = () => {
        setSelectedBg(null);
        setCustomBgUrl(null);
        onBackgroundCleared();
    };

    // 处理点击"不使用背景"的回调函数
    const handleClearBackgroundClick = React.useCallback(() => {
        handleClearBackground();
    }, [handleClearBackground]);

    // 处理背景库标签点击的回调函数
    const handleBgTabClick = React.useCallback(() => {
        handleTabChange('bg');
    }, [handleTabChange]);

    // 处理自定义标签点击的回调函数
    const handleCustomTabClick = React.useCallback(() => {
        handleTabChange('custom');
    }, [handleTabChange]);

    // 处理背景图片点击
    const handleBgImageClick = React.useCallback(
        (idx: number) => {
            handleBgImageSelected(idx);
        },
        [handleBgImageSelected]
    );

    return (
        <div className="relative flex h-[574px] w-[280px] flex-col items-start rounded-lg bg-white">
            <div className="absolute left-6 top-[13px] flex w-[232px] items-center justify-between">
                <div className="h-5 whitespace-nowrap text-base font-medium leading-5 text-black">背景</div>
                <div
                    className="flex h-[14px] w-[80px] cursor-pointer justify-center whitespace-nowrap text-center text-sm font-normal leading-[100%] text-primary"
                    onClick={handleClearBackgroundClick}
                >
                    不使用背景
                </div>
            </div>
            <div className="absolute left-6 top-[45px] flex h-fit w-[108px] items-center justify-between">
                <div
                    className={`relative mb-3 cursor-pointer pb-1 text-[14px] ${
                        activeTab === 'bg' ? 'font-medium text-primary' : 'text-black-base font-normal'
                    }`}
                    onClick={handleBgTabClick}
                >
                    背景库
                    {activeTab === 'bg' && (
                        <div className="absolute bottom-0 left-1/2 h-0.5 w-[30px] -translate-x-1/2 rounded-[14px] bg-primary" />
                    )}
                </div>
                <div
                    className={`relative mb-3 cursor-pointer pb-1 text-[14px] ${
                        activeTab === 'custom' ? 'font-medium text-primary' : 'text-black-base font-normal'
                    }`}
                    onClick={handleCustomTabClick}
                >
                    自定义
                    {activeTab === 'custom' && (
                        <div className="absolute bottom-0 left-1/2 h-0.5 w-[30px] -translate-x-1/2 rounded-[14px] bg-primary" />
                    )}
                </div>
            </div>
            <div className="absolute left-[23.5px]  top-[91.02px] flex h-[460px] w-[248px]">
                <div className="flex-1 overflow-y-auto overflow-x-hidden pr-2 [&::-webkit-scrollbar-thumb]:h-[72px] [&::-webkit-scrollbar-thumb]:rounded-[2px] [&::-webkit-scrollbar-thumb]:bg-[#E5E6EB] [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar]:bg-transparent">
                    {isLoading ? (
                        <div className="flex h-full items-center justify-center">
                            <span className="h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent"></span>
                        </div>
                    ) : activeTab === 'bg' ? (
                        <div
                            className={`${
                                mode === SCREEN_MODEL.Vertical ? 'grid grid-cols-2 gap-0' : 'flex flex-col gap-0'
                            }`}
                        >
                            {bgImages.map((url, idx) => (
                                <div
                                    key={url}
                                    className={`relative cursor-pointer rounded-[9px] ${
                                        mode === SCREEN_MODEL.Vertical
                                            ? 'mb-[9px] mr-[9px] h-[200px] w-[112.5px]'
                                            : 'mb-[9px] h-[131px] w-full'
                                    } ${
                                        selectedBg === idx
                                            ? 'before:absolute before:inset-0 before:z-10 before:rounded-[9px] before:border-2 before:border-primary'
                                            : 'hover:shadow-[0_0_6px_rgba(30,31,36,0.2)]'
                                    }`}
                                    onClick={() => handleBgImageClick(idx)}
                                >
                                    <div className="relative h-full w-full overflow-hidden rounded-[9px]">
                                        <img src={url} alt={`bg${idx}`} className="h-full w-full object-cover" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <CustomUpload
                            mode={mode}
                            customImages={customImages}
                            onImageUploaded={handleCustomImageUploaded}
                            onImageDeleted={handleCustomImageDeleted}
                            onImageSelected={handleCustomImageSelected}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default BackgroundSelector;
