/**
 * @file 智能体调优-教育视频托管-预览弹窗组件
 * @description 提供数字人视频预览功能，支持视频播放控制和应用设置
 * <AUTHOR>
 */

import React, {useEffect, useState, useCallback, useRef, useMemo} from 'react';
import {Button, message, Slider} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import play from '@/modules/dataset/assets/play.png';
import pause from '@/modules/dataset/assets/pause.png';
import {generatePreviewVideo, getPreviewVideoStatus} from '@/api/videoGenerate';
import {VideoPreviewStatus, VideoGenerateType, SCREEN_MODEL} from '@/api/videoGenerate/interface';
import {CONTAINER_SIZE} from '../constants';

const StyledSlider = styled(Slider)`
    border-radius: 0px;
    .ant-slider-rail,
    .ant-slider-track,
    .ant-slider-step {
        border-radius: 0px !important;
    }
    .ant-slider-step {
        background-color: rgba(0, 0, 0, 0.3) !important;
        top: 0px !important;
        z-index: 10;
    }
    .ant-slider-track {
        background-color: rgba(255, 255, 255, 0.6) !important;
        z-index: 20;
    }
    .ant-slider-handle {
        display: none;
    }
`;

export interface Position {
    x: number;
    y: number;
    width: number;
    height: number;
    scale: number;
}

interface PreviewModalProps {
    visible: boolean;
    onClose: () => void;
    onApply: () => void;
    onPreviewApply?: (position: Position) => void;
    previewImage: string;
    backgroundImg?: string;
    appId: string;
    position: Position;
    mode: SCREEN_MODEL;
    videoUrl?: string;
}

const LoadPreviewPending = () => (
    <div className="relative h-full w-full">
        <img
            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/dynamic/loading.gif"
            className="h-full w-full cursor-progress rounded-[18px]"
        />
        <div
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 whitespace-nowrap text-center text-lg font-medium text-white"
            style={{
                textShadow: '0px 4px 12.3px 0px #93000233',
            }}
        >
            数字人预览视频加载中，请稍等
        </div>
    </div>
);

const LoadPreviewError = ({onRetry}: {onRetry: () => void}) => (
    <div className="text-center text-white">
        <img
            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/agent-figure/error.png"
            className="m-auto h-[48px] w-[48px]"
        />
        <p className="mt-[10px] cursor-pointer" onClick={onRetry}>
            加载失败，请重新加载~
        </p>
    </div>
);

const PreviewModal: React.FC<PreviewModalProps> = ({
    visible,
    onClose,
    onApply,
    onPreviewApply,
    previewImage,
    backgroundImg,
    appId,
    position,
    mode,
    videoUrl: initialVideoUrl,
}) => {
    const [genStatus, setGenStatus] = useState<VideoPreviewStatus>(VideoPreviewStatus.NONE);
    const [isPlaying, setIsPlaying] = useState(false);
    const [progress, setProgress] = useState(0);
    const [duration, setDuration] = useState(0);
    const [currentVideoUrl, setCurrentVideoUrl] = useState(initialVideoUrl);
    const currentVideoMode = React.useMemo(() => mode, [mode]);
    const videoRef = useRef<HTMLVideoElement>(null);
    const timeoutRef = useRef<NodeJS.Timeout>();

    // 检查视频生成状态
    const checkVideoStatus = useCallback(async () => {
        try {
            const data = await getPreviewVideoStatus({appId});

            setGenStatus(data.status);
            if (data.status === VideoPreviewStatus.SUCCESS && data.videoUrl) {
                // 清除轮询定时器
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                }

                // 更新视频 URL
                setCurrentVideoUrl(data.videoUrl);
            } else if (data.status === VideoPreviewStatus.GENERATING) {
                // 继续轮询
                timeoutRef.current = setTimeout(checkVideoStatus, 3000);
            }
        } catch (error) {
            setGenStatus(VideoPreviewStatus.ERROR);
            // 清除轮询定时器
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        }
    }, [appId]);

    // 前端容器尺寸
    const CURRENT_CONTAINER_SIZE = useMemo(() => {
        return mode === SCREEN_MODEL.Vertical ? CONTAINER_SIZE.VERTICAL : CONTAINER_SIZE.HORIZONTAL;
    }, [mode]);

    // 慧播星画布尺寸
    const HUABU_SIZE = useMemo(() => {
        const VERTICAL = {
            WIDTH: 1080,
            HEIGHT: 1920,
        };
        const HORIZONTAL = {
            WIDTH: 1920,
            HEIGHT: 1080,
        };
        return mode === SCREEN_MODEL.Vertical ? VERTICAL : HORIZONTAL;
    }, [mode]);

    // 慧播星画布等比的像素值
    const absolutePosition = useMemo(() => {
        // 大小位置相对值，相对画布的比例计算。为0-1之间数字
        const x = position.x / CURRENT_CONTAINER_SIZE.WIDTH;
        const y = position.y / CURRENT_CONTAINER_SIZE.HEIGHT;
        const width = position.width / CURRENT_CONTAINER_SIZE.WIDTH;
        const height = position.height / CURRENT_CONTAINER_SIZE.HEIGHT;

        // 乘以慧播星画布宽高，转为绝对像素值
        return {
            x: Math.floor(x * HUABU_SIZE.WIDTH),
            y: Math.floor(y * HUABU_SIZE.HEIGHT),
            width: Math.floor(width * HUABU_SIZE.WIDTH),
            height: Math.floor(height * HUABU_SIZE.HEIGHT),
        };
    }, [position, CURRENT_CONTAINER_SIZE, HUABU_SIZE]);

    // 开始生成视频
    const startGenerateVideo = useCallback(async () => {
        try {
            setGenStatus(VideoPreviewStatus.GENERATING);
            setCurrentVideoUrl(''); // 清除旧的视频 URL

            // 使用VideoGenerateType.PREVIEW作为type参数
            await generatePreviewVideo({
                type: VideoGenerateType.PREVIEW,
                previewVideoGen: true,
                appId,
                backgroundImg: backgroundImg || '',
                previewImage,
                position: absolutePosition,
                screen: mode === SCREEN_MODEL.Horizontal ? 1 : 0,
            });

            // 开始轮询检查状态
            checkVideoStatus();
        } catch (error) {
            setGenStatus(VideoPreviewStatus.ERROR);
            message.error('生成视频失败，请重试');
        }
    }, [appId, backgroundImg, absolutePosition, checkVideoStatus, previewImage, mode]);

    // 处理视频播放/暂停
    const handlePlayPause = useCallback(() => {
        if (!videoRef.current) return;

        if (videoRef.current.paused) {
            const playPromise = videoRef.current.play();
            if (playPromise instanceof Promise) {
                playPromise.then(() => setIsPlaying(true)).catch(() => setIsPlaying(false));
            } else {
                setIsPlaying(true);
            }
        } else {
            videoRef.current.pause();
            setIsPlaying(false);
        }
    }, []);

    // 处理视频进度更新
    const onTimeUpdate = useCallback(() => {
        if (!videoRef.current) return;
        setProgress(videoRef.current.currentTime);
    }, []);

    // 处理视频结束
    const handleEnded = useCallback(() => {
        setIsPlaying(false);
    }, []);

    // 处理视频加载完成
    const onCanPlay = useCallback(() => {
        if (!videoRef.current) return;
        setDuration(videoRef.current.duration);
    }, []);

    // 处理关闭弹窗
    const handleClose = useCallback(() => {
        // 清除轮询定时器
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // 暂停视频播放
        if (videoRef.current) {
            videoRef.current.pause();
        }

        // 重置所有状态
        setIsPlaying(false);
        setGenStatus(VideoPreviewStatus.NONE);
        setProgress(0);
        setDuration(0);
        onClose();
    }, [onClose]);

    // 处理确定应用
    const handleApply = useCallback(async () => {
        if (genStatus === VideoPreviewStatus.SUCCESS) {
            try {
                // 确认应用时调用生成接口
                await generatePreviewVideo({
                    type: VideoGenerateType.APPLY,
                    previewVideoGen: false,
                    appId,
                    backgroundImg: backgroundImg || '',
                    previewImage,
                    position: absolutePosition,
                    screen: mode === SCREEN_MODEL.Horizontal ? 1 : 0,
                });

                // 保存应用的position
                onPreviewApply?.(position);

                // 关闭预览弹窗
                handleClose();

                // 调用 onApply 回调，这将关闭画面设置弹框
                onApply?.();
            } catch (error) {
                message.error('应用失败，请重试');
            }
        }
    }, [
        genStatus,
        appId,
        backgroundImg,
        position,
        handleClose,
        previewImage,
        mode,
        onApply,
        onPreviewApply,
        absolutePosition,
    ]);

    // 初始化时开始生成视频
    useEffect(() => {
        if (visible) {
            startGenerateVideo();
        }

        // 缓存 ref 值
        const timeout = timeoutRef.current;
        const video = videoRef.current;

        return () => {
            // 清理工作
            if (timeoutRef) {
                clearTimeout(timeout);
            }

            if (video) {
                video.pause();
            }

            setIsPlaying(false);
            setProgress(0);
            setDuration(0);
        };
    }, [visible, startGenerateVideo]);

    if (!visible) return null;

    return (
        <>
            <div className="fixed inset-0 z-[1000] bg-black/40" onClick={handleClose} />
            <div className="fixed left-1/2 top-1/2 z-[1001] flex h-[442.86px] w-[600px] -translate-x-1/2 -translate-y-1/2 flex-col rounded-[18px] bg-white p-6">
                <div className="mx-auto flex h-[394.86px] w-[552px] flex-col justify-between">
                    <div className="flex h-[24px] w-[552px] items-center justify-between">
                        <span className="text-black-base font-['PingFang_SC'] text-[18px] font-[500] leading-[24px]">
                            数字人预览
                        </span>
                        <CloseOutlined
                            className="cursor-pointer text-[16px] text-[#8c8c8c] hover:text-[#2764FF]"
                            onClick={handleClose}
                        />
                    </div>
                    <div
                        className={`relative my-[15px] h-[310.86px] w-[552px] overflow-hidden rounded-[18px] ${
                            currentVideoMode === SCREEN_MODEL.Vertical ? 'bg-black' : ''
                        }`}
                    >
                        {genStatus === VideoPreviewStatus.SUCCESS && currentVideoUrl ? (
                            <>
                                <div className="relative flex h-full w-full items-center justify-center">
                                    <video
                                        ref={videoRef}
                                        className={`${
                                            currentVideoMode === SCREEN_MODEL.Vertical
                                                ? 'h-full'
                                                : 'h-[310.86px] w-[552px]'
                                        } object-contain`}
                                        src={currentVideoUrl}
                                        onTimeUpdate={onTimeUpdate}
                                        onEnded={handleEnded}
                                        onCanPlay={onCanPlay}
                                    />
                                </div>
                                <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                                    <img
                                        className="h-[48px] w-[48px] cursor-pointer"
                                        src={isPlaying ? pause : play}
                                        onClick={handlePlayPause}
                                    />
                                </div>
                                {isPlaying && (
                                    <div className="absolute bottom-0 h-11 w-full bg-gradient-to-t">
                                        <StyledSlider
                                            min={0}
                                            max={duration}
                                            value={progress}
                                            className="absolute bottom-0 m-0 h-1 w-full p-0"
                                        />
                                    </div>
                                )}
                            </>
                        ) : genStatus === VideoPreviewStatus.GENERATING ? (
                            <LoadPreviewPending />
                        ) : genStatus === VideoPreviewStatus.ERROR ? (
                            <LoadPreviewError onRetry={startGenerateVideo} />
                        ) : null}
                    </div>
                    <div className="flex h-[30px] w-[552px] items-center justify-end gap-[6px]">
                        <Button
                            className="flex h-[30px] w-[86px] items-center justify-center rounded-[100px] border border-solid border-[#d9d9d9]"
                            onClick={handleClose}
                        >
                            返回编辑
                        </Button>
                        <Button
                            type="primary"
                            className="flex h-[30px] w-[86px] items-center justify-center rounded-[100px]"
                            onClick={handleApply}
                            disabled={genStatus !== VideoPreviewStatus.SUCCESS}
                        >
                            确定应用
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default PreviewModal;
