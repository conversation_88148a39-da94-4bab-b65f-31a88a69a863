/**
 * @file 智能体调优-教育视频托管-数字人预览组件
 * @description 提供数字人预览功能，支持拖拽调整位置和滚轮缩放
 * <AUTHOR>
 */

import React, {useState, useEffect, useCallback, useRef, useLayoutEffect} from 'react';
import {Button} from 'antd';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';
import {CONTAINER_SIZE, SCALE_CONFIG, UI_SIZE} from '../constants';
import PreviewModal, {Position} from './PreviewModal';

interface AvatarPosition {
    x: number;
    y: number;
    scale: number;
    height: number;
}

interface AvatarPreviewProps {
    mode: SCREEN_MODEL;
    avatar: AvatarPosition;
    onAvatarChange: (newPosition: AvatarPosition) => void;
    figureImg: string;
    backgroundImg?: string;
    videoUrl?: string;
    onApply?: () => void;
    appId: string;
    figureWidth: number;
    figureHeight: number;
    initialPosition?: {x: number; y: number; width: number; height: number};
}

const AvatarPreview: React.FC<AvatarPreviewProps> = ({
    mode,
    avatar,
    onAvatarChange,
    figureImg,
    backgroundImg,
    videoUrl,
    onApply,
    appId,
    figureWidth,
    figureHeight,
    initialPosition,
}) => {
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({x: 0, y: 0});
    const [startPosition, setStartPosition] = useState({x: 0, y: 0});
    const [showPreview, setShowPreview] = useState(false);
    const [isImageLoading, setIsImageLoading] = useState(true);
    const timestampRef = useRef(Date.now());
    const imageRef = useRef<HTMLImageElement>(null);

    // 获取当前模式的容器尺寸
    const getContainerSize = useCallback(() => {
        return mode === SCREEN_MODEL.Horizontal ? CONTAINER_SIZE.HORIZONTAL : CONTAINER_SIZE.VERTICAL;
    }, [mode]);

    // 处理拖拽开始
    const handleDragStart = useCallback(
        (e: React.MouseEvent<HTMLImageElement>) => {
            e.preventDefault();
            setIsDragging(true);
            setDragStart({x: e.clientX, y: e.clientY});
            setStartPosition({x: avatar.x, y: avatar.y});
        },
        [avatar.x, avatar.y]
    );

    // 处理拖拽中
    const handleDrag = useCallback(
        (e: React.MouseEvent<HTMLImageElement>) => {
            if (!isDragging) return;

            const deltaX = e.clientX - dragStart.x;
            const deltaY = e.clientY - dragStart.y;

            const containerSize = getContainerSize();
            // 先转换为像素值计算
            const startX = startPosition.x * containerSize.WIDTH;

            const startY = startPosition.y * containerSize.HEIGHT;

            // 计算新的像素位置
            const newPixelX = startX + deltaX;

            const newPixelY = startY + deltaY;

            // 最后转换回相对值
            const newX = newPixelX / containerSize.WIDTH;

            const newY = newPixelY / containerSize.HEIGHT;

            onAvatarChange({
                ...avatar,
                x: newX,
                y: newY,
            });
        },
        [isDragging, dragStart, startPosition, avatar, onAvatarChange, getContainerSize]
    );

    // 处理拖拽结束
    const handleDragEnd = useCallback(() => {
        setIsDragging(false);
    }, []);

    // 鼠标滚轮缩放处理
    const handleAvatarWheel = useCallback(
        (e: WheelEvent) => {
            e.preventDefault();
            const delta = e.deltaY < 0 ? SCALE_CONFIG.WHEEL_STEP : -SCALE_CONFIG.WHEEL_STEP;
            const newScale = Math.max(SCALE_CONFIG.MIN, Math.min(SCALE_CONFIG.MAX, avatar.scale + delta));

            if (newScale !== avatar.scale) {
                const aspectRatio = avatar.scale / avatar.height;
                const newHeight = newScale / aspectRatio;

                onAvatarChange({
                    ...avatar,
                    scale: newScale,
                    height: newHeight,
                    x: avatar.x,
                    y: avatar.y,
                });
            }
        },
        [avatar, onAvatarChange]
    );

    // 添加滚轮事件监听
    useLayoutEffect(() => {
        const element = imageRef.current;
        if (element) {
            const wheelHandler = (e: WheelEvent) => {
                handleAvatarWheel(e);
            };

            element.addEventListener('wheel', wheelHandler, {passive: false});
            return () => {
                element.removeEventListener('wheel', wheelHandler);
            };
        }
    }, [handleAvatarWheel]);

    // 添加全局鼠标事件监听
    useEffect(() => {
        if (isDragging) {
            const handleMouseMove = (e: MouseEvent) => {
                if (!isDragging) return;

                const deltaX = e.clientX - dragStart.x;
                const deltaY = e.clientY - dragStart.y;

                const containerSize = getContainerSize();
                const newX = startPosition.x + deltaX / containerSize.WIDTH;

                const newY = startPosition.y + deltaY / containerSize.HEIGHT;

                onAvatarChange({
                    ...avatar,
                    x: newX,
                    y: newY,
                });
            };

            const handleMouseUp = () => {
                setIsDragging(false);
            };

            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);

            return () => {
                window.removeEventListener('mousemove', handleMouseMove);
                window.removeEventListener('mouseup', handleMouseUp);
            };
        }
    }, [isDragging, dragStart, startPosition, avatar, onAvatarChange, getContainerSize]);

    const handlePreviewClick = useCallback(() => {
        setShowPreview(true);
    }, []);

    const handlePreviewClose = useCallback(() => {
        setShowPreview(false);
    }, []);

    const handlePreviewApply = useCallback(() => {
        setShowPreview(false);
        onApply?.();
    }, [onApply]);

    const handleImageLoad = useCallback(
        (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
            const img = e.target as HTMLImageElement;
            if (img.complete && img.naturalWidth > 0) {
                // 只有在没有初始高度时才计算
                if (!avatar.height) {
                    const actualAspectRatio = figureWidth / figureHeight;
                    onAvatarChange({
                        ...avatar,
                        height: avatar.scale / actualAspectRatio,
                    });
                }

                setIsImageLoading(false);
            }
        },
        [avatar, onAvatarChange, figureWidth, figureHeight]
    );

    const handleImageError = useCallback((e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        const img = e.target as HTMLImageElement;
        // 只有当图片确实加载失败时才报错
        if (!img.complete || img.naturalWidth === 0) {
            // 图片加载失败的处理
        }
    }, []);

    // 监听图片URL变化，重置加载状态
    useEffect(() => {
        if (figureImg) {
            setIsImageLoading(true);
        }
    }, [figureImg]);

    // 转换相对坐标到绝对坐标（用于前端展示）
    const convertToAbsolutePosition = useCallback((): {
        x: number;
        y: number;
        width: number;
        height: number;
        scale: number;
    } => {
        const containerSize = getContainerSize();

        const containerWidth =
            mode === SCREEN_MODEL.Horizontal ? CONTAINER_SIZE.HORIZONTAL.WIDTH : CONTAINER_SIZE.VERTICAL.WIDTH;
        const containerHeight =
            mode === SCREEN_MODEL.Horizontal ? CONTAINER_SIZE.HORIZONTAL.HEIGHT : CONTAINER_SIZE.VERTICAL.HEIGHT;

        const actualAspectRatio = figureWidth / figureHeight;
        const widthPercentage = ((avatar.height * containerHeight * actualAspectRatio) / containerWidth) * 100;

        const actualPixels = (containerWidth * widthPercentage) / 100;

        // 将相对值（0-1）转换为像素值
        return {
            x: avatar.x * containerSize.WIDTH,
            y: avatar.y * containerSize.HEIGHT,
            width: actualPixels,
            height: avatar.height * containerSize.HEIGHT,
            scale: avatar.scale,
        };
    }, [avatar, getContainerSize, mode, figureWidth, figureHeight]);

    // 转换绝对坐标到相对坐标（用于发送给后端）
    const convertFromAbsolutePosition = useCallback(
        (position: {x: number; y: number; width: number; height: number}) => {
            const containerSize = getContainerSize();

            // 将像素值转换为相对值（0-1）
            return {
                x: position.x / containerSize.WIDTH,
                y: position.y / containerSize.HEIGHT,
                scale: position.width / containerSize.WIDTH,
                height: position.height / containerSize.HEIGHT,
            };
        },
        [getContainerSize]
    );

    useEffect(() => {
        // 如果有初始position，且不是从接口直接返回的数据，才进行反向转换
        if (initialPosition && !avatar.x && !avatar.y && !avatar.scale && !avatar.height) {
            const convertedPosition = convertFromAbsolutePosition(initialPosition);
            onAvatarChange(convertedPosition);
        }
    }, [initialPosition, convertFromAbsolutePosition, onAvatarChange, avatar]);

    const onPreviewApply = useCallback(
        (position: Position) => {
            const convertedPosition = convertFromAbsolutePosition(position);
            onAvatarChange(convertedPosition);
        },
        [convertFromAbsolutePosition, onAvatarChange]
    );

    return (
        <div className="flex flex-1 flex-col items-center justify-center rounded-lg bg-colorBgFormList">
            <div
                className="relative flex items-center justify-center overflow-hidden rounded-[18px] bg-[#eee]"
                style={{
                    width:
                        mode === SCREEN_MODEL.Vertical
                            ? `${CONTAINER_SIZE.VERTICAL.WIDTH}px`
                            : `${CONTAINER_SIZE.HORIZONTAL.WIDTH}px`,
                    height:
                        mode === SCREEN_MODEL.Vertical
                            ? `${CONTAINER_SIZE.VERTICAL.HEIGHT}px`
                            : `${CONTAINER_SIZE.HORIZONTAL.HEIGHT}px`,
                }}
            >
                {backgroundImg && (
                    <img
                        src={backgroundImg}
                        alt="default bg"
                        className="absolute left-0 top-0 h-full w-full object-cover"
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                    />
                )}
                {figureImg && (
                    <div
                        style={{
                            position: 'absolute',
                            left: `${avatar.x * 100}%`,
                            top: `${avatar.y * 100}%`,
                            width: (() => {
                                const containerWidth =
                                    mode === SCREEN_MODEL.Horizontal
                                        ? CONTAINER_SIZE.HORIZONTAL.WIDTH
                                        : CONTAINER_SIZE.VERTICAL.WIDTH;
                                const containerHeight =
                                    mode === SCREEN_MODEL.Horizontal
                                        ? CONTAINER_SIZE.HORIZONTAL.HEIGHT
                                        : CONTAINER_SIZE.VERTICAL.HEIGHT;

                                const actualAspectRatio = figureWidth / figureHeight;
                                const widthPercentage =
                                    ((avatar.height * containerHeight * actualAspectRatio) / containerWidth) * 100;
                                return `${widthPercentage}%`;
                            })(),
                            height: `${avatar.height * 100}%`,
                            boxSizing: 'border-box',
                            zIndex: 2,
                        }}
                        className="hover:shadow-[0_0_0_2px_#5562F2]"
                    >
                        <img
                            ref={imageRef}
                            src={`${figureImg}?timestamp=${timestampRef.current}`}
                            crossOrigin="anonymous"
                            alt="avatar"
                            className={`avatar-preview-image absolute cursor-move transition-opacity duration-300 ${
                                isImageLoading ? 'opacity-0' : 'opacity-100'
                            }`}
                            style={{
                                left: 0,
                                top: 0,
                                width: '100%',
                                height: 'auto',
                                position: 'absolute',
                                objectFit: 'cover',
                            }}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            onMouseDown={handleDragStart}
                            onMouseMove={handleDrag}
                            onMouseUp={handleDragEnd}
                            onMouseLeave={handleDragEnd}
                        />
                    </div>
                )}
            </div>
            <Button
                className="custom-hover-btn mt-[18px] flex items-center justify-center rounded-[100px] font-['PingFang_SC'] text-[14px] font-[500] leading-[20px] tracking-[0] text-colorTextDefault hover:text-gray-tertiary"
                style={{
                    height: `${UI_SIZE.BUTTON.HEIGHT}px`,
                    width: `${UI_SIZE.BUTTON.WIDTH}px`,
                    borderColor: '#EDEEF0',
                }}
                onClick={handlePreviewClick}
            >
                预览视频
            </Button>

            <PreviewModal
                visible={showPreview}
                onClose={handlePreviewClose}
                onApply={handlePreviewApply}
                onPreviewApply={onPreviewApply}
                previewImage={`${figureImg}?timestamp=${timestampRef.current}`}
                backgroundImg={backgroundImg}
                appId={appId}
                position={convertToAbsolutePosition()}
                mode={mode}
                videoUrl={videoUrl}
            />
        </div>
    );
};

export default AvatarPreview;
