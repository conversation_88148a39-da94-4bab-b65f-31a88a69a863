/**
 * @file 智能体调优-教育视频托管-自定义上传组件
 * @description 提供自定义背景图片上传功能，支持图片预览和删除
 * <AUTHOR>
 */

import React, {useState, useEffect, useRef} from 'react';
import {message} from 'antd';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';

/** 文件上传配置 */
const UPLOAD_CONFIG = {
    /** 最大文件大小（5MB） */
    MAX_FILE_SIZE: 5 * 1024 * 1024,
    /** 允许的文件类型 */
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/jpg'] as const,
    /** 错误提示信息 */
    ERROR_MESSAGES: {
        SIZE: '图片大小不能超过5MB',
        TYPE: '请上传jpg、jpeg或png格式的图片',
    },
} as const;

/** 上传框尺寸配置 */
const UPLOAD_BOX_SIZE = {
    VERTICAL: {
        WIDTH: '112.5px',
        HEIGHT: '200px',
    },
    HORIZONTAL: {
        WIDTH: 'full',
        HEIGHT: '131px',
    },
} as const;

/** 字体尺寸配置 */
const FONT_SIZE = {
    PLUS_ICON: '30px',
    VERTICAL_TEXT: '12px',
    HORIZONTAL_TEXT: '14px',
    UPLOADING_TEXT: '16px',
} as const;

interface CustomImage {
    url: string;
    status: 'uploading' | 'done';
    progress: number;
}

interface CustomUploadProps {
    mode: SCREEN_MODEL;
    customImages: CustomImage[];
    onImageUploaded?: (file: File) => void;
    onImageDeleted?: (index: number) => void;
    onImageSelected?: (url: string) => void;
}

const CustomUpload: React.FC<CustomUploadProps> = ({
    mode,
    customImages,
    onImageUploaded,
    onImageDeleted,
    onImageSelected,
}) => {
    // 记录上一次的图片状态
    const prevImagesRef = useRef<CustomImage[]>([]);
    const [selectedCustomIdx, setSelectedCustomIdx] = useState<number | null>(null);

    // 监听图片状态变化，当图片从uploading变为done时自动选中
    useEffect(() => {
        if (customImages.length === 0) return;

        // 找到状态从uploading变为done的图片的索引
        const newDoneImageIdx = customImages.findIndex((img, idx) => {
            const prevImage = prevImagesRef.current[idx];
            return prevImage?.status === 'uploading' && img.status === 'done';
        });

        if (newDoneImageIdx !== -1) {
            setSelectedCustomIdx(newDoneImageIdx);
            onImageSelected?.(customImages[newDoneImageIdx].url);
        }

        // 更新ref中存储的状态
        prevImagesRef.current = customImages;
    }, [customImages, onImageSelected]);

    // 验证文件
    const validateFile = (file: File): boolean => {
        // 检查文件类型
        if (!UPLOAD_CONFIG.ALLOWED_TYPES.includes(file.type as (typeof UPLOAD_CONFIG.ALLOWED_TYPES)[number])) {
            message.error(UPLOAD_CONFIG.ERROR_MESSAGES.TYPE);
            return false;
        }

        // 检查文件大小
        if (file.size > UPLOAD_CONFIG.MAX_FILE_SIZE) {
            message.error(UPLOAD_CONFIG.ERROR_MESSAGES.SIZE);
            return false;
        }

        return true;
    };

    // 处理文件选择
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!validateFile(file)) return;

        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', mode === SCREEN_MODEL.Horizontal ? 'horizontal' : 'vertical');

        onImageUploaded?.(file);

        // 重置 input 元素的值，这样相同的文件可以再次上传
        event.target.value = '';
    };

    const handleImageSelect = (img: CustomImage, idx: number) => {
        if (img.status === 'done') {
            setSelectedCustomIdx(idx);
            onImageSelected?.(img.url);
        }
    };

    const uploadBoxStyle =
        mode === SCREEN_MODEL.Vertical
            ? `w-[${UPLOAD_BOX_SIZE.VERTICAL.WIDTH}] h-[${UPLOAD_BOX_SIZE.VERTICAL.HEIGHT}]`
            : `w-[${UPLOAD_BOX_SIZE.HORIZONTAL.WIDTH}] h-[${UPLOAD_BOX_SIZE.HORIZONTAL.HEIGHT}]`;

    return (
        <div className={`${mode === SCREEN_MODEL.Vertical ? 'grid grid-cols-2 gap-0' : 'flex flex-col gap-0'}`}>
            {/* 上传按钮 */}
            <label
                className={`group ${uploadBoxStyle} flex cursor-pointer flex-col items-center justify-center rounded-[9px] border-2 border-dashed border-[#E5E6EB] bg-[#F5F6FA] transition-all duration-300 hover:border-[#5562F2] ${
                    mode === SCREEN_MODEL.Vertical ? 'mb-[9px] mr-[9px]' : 'mb-[9px]'
                }`}
            >
                <input
                    type="file"
                    className="hidden"
                    accept={UPLOAD_CONFIG.ALLOWED_TYPES.join(',')}
                    onChange={handleFileSelect}
                />
                <span className={`iconfont icon-plus text-[${FONT_SIZE.PLUS_ICON}] text-[#DADEEA]`}></span>
                <span
                    className={`mt-2 text-[${
                        mode === SCREEN_MODEL.Vertical ? FONT_SIZE.VERTICAL_TEXT : FONT_SIZE.HORIZONTAL_TEXT
                    }] text-[#86909C]`}
                >
                    点击上传
                </span>
            </label>

            {/* 已上传的图片列表 */}
            {customImages.map((img, idx) => (
                <div
                    key={img.url}
                    className={`${uploadBoxStyle} group relative flex cursor-pointer items-center justify-center overflow-hidden rounded-[9px] transition-all ${
                        selectedCustomIdx === idx ? 'border-2 border-[#1677ff]' : 'border-0'
                    } hover:shadow-[0_6px_21px_rgba(30,31,36,0.2)] ${
                        mode === SCREEN_MODEL.Vertical ? 'mb-[9px] mr-[9px]' : 'mb-[9px]'
                    }`}
                    onClick={() => handleImageSelect(img, idx)}
                >
                    <img src={img.url} alt="custom" className="h-full w-full object-cover" />
                    {/* 上传中遮罩 */}
                    {img.status === 'uploading' && (
                        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40">
                            <span className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-solid border-white border-t-transparent"></span>
                            <span
                                className={`text-[${FONT_SIZE.UPLOADING_TEXT}] origin-center scale-75 transform whitespace-nowrap text-white`}
                            >
                                背景正在上传 {img.progress}%
                            </span>
                        </div>
                    )}
                    {/* 删除按钮 - 仅在非上传状态显示 */}
                    {img.status !== 'uploading' && (
                        <button
                            className="absolute left-1/2 top-1/2 flex h-[30px] w-[30px] -translate-x-1/2 -translate-y-1/2 items-center justify-center opacity-0 transition-opacity group-hover:opacity-100"
                            onClick={e => {
                                e.stopPropagation();
                                onImageDeleted?.(idx);
                            }}
                            style={{border: 'none'}}
                        >
                            <span className="iconfont icon-delete text-[30px] text-white"></span>
                        </button>
                    )}
                </div>
            ))}
        </div>
    );
};

export default CustomUpload;
