/**
 * @file 智能体调优-教育视频托管-头部组件
 * @description 提供画面设置的标题、横竖版切换和关闭功能
 * <AUTHOR>
 */

import React from 'react';
import {Button} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';

interface HeaderProps {
    mode: SCREEN_MODEL;
    onModeChange: (mode: SCREEN_MODEL) => void;
    onClose: () => void;
}

const Header: React.FC<HeaderProps> = ({mode, onModeChange, onClose}) => {
    return (
        <div className="ml-6 mt-6 flex h-6 w-[1068px] items-center justify-between gap-[3px]">
            <div className="flex items-center">
                <div className="text-black-base h-6 w-[85px] whitespace-nowrap text-lg font-medium leading-6">
                    画面设置
                </div>
            </div>
            <div className="flex items-center">
                <div className="flex h-7 w-[110px] items-center gap-[10px] rounded-[20px] bg-gray-bg-base px-[1px]">
                    <div
                        className={`flex h-[26px] flex-1 cursor-pointer items-center justify-center rounded-[20px] text-center text-sm transition-all ${
                            mode === SCREEN_MODEL.Horizontal
                                ? 'bg-white font-medium text-primary'
                                : 'text-black-base font-normal'
                        }`}
                        onClick={() => onModeChange(SCREEN_MODEL.Horizontal)}
                    >
                        横版
                    </div>
                    <div
                        className={`flex h-[26px] flex-1 cursor-pointer items-center justify-center rounded-[20px] text-center text-sm transition-all ${
                            mode === SCREEN_MODEL.Vertical
                                ? 'bg-white font-medium text-primary'
                                : 'text-black-base font-normal'
                        }`}
                        onClick={() => onModeChange(SCREEN_MODEL.Vertical)}
                    >
                        竖版
                    </div>
                </div>
            </div>
            <div className="flex h-7 items-center justify-center">
                <Button
                    className="flex h-7 w-7 items-center justify-center border-none bg-transparent p-0 text-[#8c8c8c] shadow-none hover:bg-gray-bg-base hover:text-[#2764FF]"
                    icon={<CloseOutlined />}
                    onClick={onClose}
                />
            </div>
        </div>
    );
};

export default Header;
