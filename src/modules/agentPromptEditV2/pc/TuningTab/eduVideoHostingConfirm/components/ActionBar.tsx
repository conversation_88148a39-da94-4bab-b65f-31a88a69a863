/**
 * @file 智能体调优-教育视频托管-操作栏组件
 * @description 包含取消和确认应用按钮，负责视频设置的最终提交
 * <AUTHOR>
 */

import React, {useCallback} from 'react';
import {Button, message} from 'antd';
import {generatePreviewVideo} from '@/api/videoGenerate';
import {VideoPreviewGenerateParams, VideoGenerateType, SCREEN_MODEL} from '@/api/videoGenerate/interface';

interface ActionBarProps {
    onCancel: () => void;
    onApply: () => void;
    appId: string;
    backgroundImg?: string;
    previewImage: string;
    position: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    mode: SCREEN_MODEL;
}

const validateParams = (appId: string, position: ActionBarProps['position'], previewImage: string): boolean => {
    if (!appId) {
        message.error('缺少必要参数：appId');
        return false;
    }

    if (!previewImage) {
        message.error('缺少必要参数：数字人形象');
        return false;
    }

    if (
        !position ||
        typeof position.x !== 'number' ||
        typeof position.y !== 'number' ||
        typeof position.width !== 'number' ||
        typeof position.height !== 'number'
    ) {
        message.error('位置参数不合法');
        return false;
    }

    return true;
};

const ActionBar: React.FC<ActionBarProps> = ({
    onCancel,
    onApply,
    appId,
    backgroundImg,
    previewImage,
    position,
    mode,
}) => {
    const handleApply = useCallback(async () => {
        try {
            // 参数校验
            if (!validateParams(appId, position, previewImage)) {
                return;
            }

            // position已经是相对值，直接使用
            const relativePosition = {
                x: position.x,
                y: position.y,
                width: position.width,
                height: position.height,
            };

            // 构造请求参数
            const params: VideoPreviewGenerateParams = {
                type: VideoGenerateType.APPLY,
                previewVideoGen: false,
                appId,
                backgroundImg,
                previewImage,
                position: relativePosition,
                screen: mode === SCREEN_MODEL.Horizontal ? 1 : 0,
            };

            await generatePreviewVideo(params);

            // 调用成功后执行原有的 onApply 回调
            onApply();
        } catch (error: any) {
            // 显示更详细的错误信息
            message.error(error?.msg || error?.message || '应用失败，请重试');
        }
    }, [appId, backgroundImg, previewImage, position, onApply, mode]);

    return (
        <div className="flex h-[84px] w-full items-center justify-end px-6">
            <Button
                className="flex h-[30px] w-[58px] items-center justify-center gap-[10px] rounded-[100px] border border-solid border-[#d9d9d9] px-[15px] py-[5px]"
                onClick={onCancel}
            >
                取消
            </Button>
            <Button
                type="primary"
                className="ml-[6px] flex h-[30px] w-[86px] items-center justify-center rounded-[100px]"
                onClick={handleApply}
            >
                确认应用
            </Button>
        </div>
    );
};

export default ActionBar;
