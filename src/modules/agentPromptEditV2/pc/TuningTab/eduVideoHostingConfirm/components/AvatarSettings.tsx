/**
 * @file 智能体调优-教育视频托管-数字人设置组件
 * @description 提供数字人位置和大小的调整功能，包括坐标输入和滑块控制
 * <AUTHOR>
 */

import React, {useEffect, useState, useCallback} from 'react';
import {Button, InputNumber, Slider, ConfigProvider} from 'antd';
import {UpOutlined, DownOutlined} from '@ant-design/icons';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';
import {CONTAINER_SIZE, SCALE_CONFIG, UI_SIZE} from '../constants';

interface AvatarPosition {
    x: number;
    y: number;
    scale: number;
    height: number;
}

interface AvatarSettingsProps {
    avatar: AvatarPosition;
    onAvatarChange: (newPosition: AvatarPosition) => void;
    onReset: () => void;
    mode: SCREEN_MODEL;
}

// 自定义上下箭头图标
interface StepButtonProps {
    disabled?: boolean;
    icon: any;
}

const AvatarSettings: React.FC<AvatarSettingsProps> = ({avatar, onAvatarChange, onReset, mode}) => {
    // 获取当前容器尺寸
    const getContainerSize = useCallback(() => {
        return mode === SCREEN_MODEL.Horizontal ? CONTAINER_SIZE.HORIZONTAL : CONTAINER_SIZE.VERTICAL;
    }, [mode]);

    // 相对值转换为显示值
    const relativeToDisplay = (value: number, isX: boolean) => {
        const containerSize = getContainerSize();
        return Math.round(value * (isX ? containerSize.WIDTH : containerSize.HEIGHT));
    };

    // 缩放值转换函数
    const scaleToDisplay = (scale: number): number => Math.round(scale * 100);
    const displayToScale = (display: number): number => display / 100;

    // 保存原始宽高比
    const [aspectRatio, setAspectRatio] = useState(() => avatar.scale / avatar.height);

    // 处理初始化和更新时的默认值
    useEffect(() => {
        // 只在 x/y/scale/height 全部为 0/null/undefined 时才自动居中
        const isInvalid = (v: any) => v === 0 || v === null || v === undefined;
        if (isInvalid(avatar.x) && isInvalid(avatar.y) && isInvalid(avatar.scale) && isInvalid(avatar.height)) {
            const defaultScale = 1;
            const defaultHeight = 1;
            onAvatarChange({
                ...avatar,
                scale: defaultScale,
                height: defaultHeight,
                x: 0,
                y: 0,
            });
        }
    }, [avatar, onAvatarChange, mode]);

    // 更新宽高比
    useEffect(() => {
        if (avatar.scale && avatar.height) {
            const newAspectRatio = avatar.scale / avatar.height;
            if (Math.abs(newAspectRatio - aspectRatio) > SCALE_CONFIG.ASPECT_RATIO_TOLERANCE) {
                setAspectRatio(newAspectRatio);
            }
        }
    }, [avatar.scale, avatar.height, aspectRatio]);

    // 横竖切换时，自动调整数字人比例和居中
    useEffect(() => {
        // 只在mode变化时做自适应，不影响其他逻辑
        const maxScaleByWidth = 1;
        const maxScaleByHeight = aspectRatio;
        const maxScale = Math.min(maxScaleByWidth, maxScaleByHeight, SCALE_CONFIG.MAX);
        let newScale = avatar.scale;
        if (newScale > maxScale) newScale = maxScale;
        const newHeight = newScale / aspectRatio;

        // 保持和横版一样的居中贴底效果
        const centerX = (1 - newScale) / 2;
        const bottomY = 1 - newHeight;
        onAvatarChange({
            ...avatar,
            scale: newScale,
            height: newHeight,
            x: centerX,
            y: bottomY,
        });
        // eslint-disable-next-line
    }, [mode]);

    // 处理位置变化
    const handlePositionChange = useCallback(
        (axis: 'x' | 'y', displayValue: number) => {
            if (displayValue === null) return;

            const containerSize = getContainerSize();
            const relativeValue = displayValue / (axis === 'x' ? containerSize.WIDTH : containerSize.HEIGHT);

            onAvatarChange({
                ...avatar,
                [axis]: relativeValue,
            });
        },
        [avatar, onAvatarChange, getContainerSize]
    );

    // 处理X轴位置变化
    const handleXChange = useCallback(
        (value: number | null) => {
            if (value !== null) {
                handlePositionChange('x', value);
            }
        },
        [handlePositionChange]
    );

    // 处理Y轴位置变化
    const handleYChange = useCallback(
        (value: number | null) => {
            if (value !== null) {
                handlePositionChange('y', value);
            }
        },
        [handlePositionChange]
    );

    // 处理大小变化，保持宽高比
    const handleScaleChange = useCallback(
        (relativeScale: number) => {
            // 如果是初始值为0或null，设置为默认大小
            let newScale = !relativeScale || relativeScale === 0 ? 1 : relativeScale;

            // 限制缩放范围
            newScale = Math.max(SCALE_CONFIG.MIN, Math.min(SCALE_CONFIG.MAX, newScale));
            const newHeight = newScale / aspectRatio;

            onAvatarChange({
                ...avatar,
                scale: newScale,
                height: newHeight,
                x: avatar.x,
                y: avatar.y,
            });
        },
        [avatar, aspectRatio, onAvatarChange]
    );

    // 处理数值输入框的大小变化
    const handleInputScaleChange = useCallback(
        (displayValue: number | null) => {
            if (displayValue !== null) {
                handleScaleChange(displayToScale(displayValue));
            }
        },
        [handleScaleChange]
    );

    // 自定义上下箭头图标
    const StepButton = ({icon: Icon}: StepButtonProps) => (
        <div className="flex h-[18px] w-[20.5px] cursor-pointer items-center justify-center bg-colorBgFormList text-black">
            <Icon className="text-[0.5rem]" />
        </div>
    );

    return (
        <div
            className="flex flex-col gap-[18px] rounded-lg bg-white"
            style={{
                height: `${UI_SIZE.WRAPPER.HEIGHT}px`,
                width: `${UI_SIZE.WRAPPER.WIDTH}px`,
            }}
        >
            <div className="mb-[9px] ml-6 mt-[13px] h-5 whitespace-nowrap text-base font-medium leading-5 text-black">
                数字人设置
            </div>
            <div className="mb-3 px-6">
                <div className="mb-2 flex h-5 w-7 items-end whitespace-nowrap text-sm font-normal leading-5 text-colorTextDefault">
                    位置
                </div>
                <div className="mt-2 flex items-center gap-3">
                    <div
                        className="mt-2 flex items-center bg-colorBgFormList px-2"
                        style={{
                            height: `${UI_SIZE.INPUT.HEIGHT}px`,
                            width: `${UI_SIZE.INPUT.X_WIDTH}px`,
                            borderRadius: 9,
                        }}
                    >
                        <span className="text-black-base mr-2 text-sm">X</span>
                        <InputNumber
                            max={getContainerSize().WIDTH}
                            value={relativeToDisplay(avatar.x, true)}
                            onChange={handleXChange}
                            className="ml-[9px] h-[32px] w-[80px] shrink-0"
                            style={{
                                height: `${UI_SIZE.INPUT.HEIGHT}px`,
                                width: `${UI_SIZE.INPUT.X_WIDTH}px`,
                                background: 'transparent',
                                border: 'none',
                                borderRadius: 0,
                                boxShadow: 'none',
                            }}
                            controls={{
                                upIcon: <StepButton icon={UpOutlined} />,
                                downIcon: <StepButton icon={DownOutlined} />,
                            }}
                        />
                    </div>
                    <div
                        className="mt-2 flex items-center bg-colorBgFormList px-2"
                        style={{
                            height: `${UI_SIZE.INPUT.HEIGHT}px`,
                            width: `${UI_SIZE.INPUT.Y_WIDTH}px`,
                            borderRadius: 9,
                        }}
                    >
                        <span className="text-black-base mr-2 text-sm">Y</span>
                        <InputNumber
                            max={getContainerSize().HEIGHT}
                            value={relativeToDisplay(avatar.y, false)}
                            onChange={handleYChange}
                            className="ml-[9px] h-[32px] w-[80px] shrink-0"
                            style={{
                                height: `${UI_SIZE.INPUT.HEIGHT}px`,
                                width: `${UI_SIZE.INPUT.Y_WIDTH}px`,
                                background: 'transparent',
                                border: 'none',
                                borderRadius: 0,
                                boxShadow: 'none',
                            }}
                            controls={{
                                upIcon: <StepButton icon={UpOutlined} />,
                                downIcon: <StepButton icon={DownOutlined} />,
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className="mb-3 px-6">
                <div className="mb-2 flex h-5 w-7 items-end whitespace-nowrap text-sm font-normal leading-5 text-colorTextDefault">
                    大小
                </div>
                <div
                    className="flex items-center justify-between"
                    style={{
                        height: `${UI_SIZE.SCALE_WRAPPER.HEIGHT}px`,
                        width: `${UI_SIZE.SCALE_WRAPPER.WIDTH}px`,
                    }}
                >
                    <div
                        className="flex items-center"
                        style={{
                            height: `${UI_SIZE.INPUT.HEIGHT}px`,
                            width: `${UI_SIZE.SLIDER.WIDTH}px`,
                        }}
                    >
                        <ConfigProvider
                            theme={{
                                components: {
                                    Slider: {
                                        handleSize: 14,
                                        handleSizeHover: 14,
                                        handleLineWidth: 2,
                                        handleLineWidthHover: 2,
                                        handleColor: '#5562F2',
                                    },
                                },
                            }}
                        >
                            <Slider
                                min={SCALE_CONFIG.MIN}
                                max={SCALE_CONFIG.MAX}
                                step={SCALE_CONFIG.STEP}
                                value={avatar.scale}
                                onChange={handleScaleChange}
                                tooltip={{formatter: v => `${scaleToDisplay(v as number)}%`}}
                                className="w-full"
                                style={{
                                    height: `${UI_SIZE.SLIDER.HEIGHT}px`,
                                    width: '100%',
                                }}
                            />
                        </ConfigProvider>
                    </div>
                    <div
                        className="flex items-center justify-end bg-colorBgFormList px-2"
                        style={{
                            height: `${UI_SIZE.INPUT.HEIGHT}px`,
                            width: '80px',
                            borderRadius: 9,
                        }}
                    >
                        <InputNumber
                            min={SCALE_CONFIG.DISPLAY_MIN}
                            max={SCALE_CONFIG.DISPLAY_MAX}
                            controls={false}
                            value={scaleToDisplay(avatar.scale)}
                            onChange={handleInputScaleChange}
                            className="border-none bg-transparent text-right text-sm hover:border-transparent focus:border-transparent focus:shadow-none active:border-transparent"
                            style={{
                                height: `${UI_SIZE.INPUT.HEIGHT}px`,
                                width: '70px',
                                outline: 'none',
                                boxShadow: 'none',
                            }}
                        />
                        <span className="text-black-base ml-1 h-[20px] w-[14px] text-sm">%</span>
                    </div>
                </div>
            </div>
            <div className="flex justify-center">
                <Button
                    className="custom-hover-btn mb-[9px] mt-[5px] flex items-center justify-center rounded-[100px] font-['PingFang_SC'] text-[14px] font-[500] leading-[20px] tracking-[0] text-colorTextDefault hover:text-gray-tertiary"
                    style={{
                        height: `${UI_SIZE.BUTTON.HEIGHT}px`,
                        width: `${UI_SIZE.BUTTON.WIDTH}px`,
                        borderColor: '#EDEEF0',
                    }}
                    onClick={onReset}
                >
                    恢复默认
                </Button>
            </div>
        </div>
    );
};

export default AvatarSettings;
