/**
 * @file 智能体调优-教育视频托管-视频设置组件
 * @description 包含视频背景选择、数字人位置和大小调整等功能
 * <AUTHOR>
 */

import React, {useEffect, useCallback} from 'react';
import {message} from 'antd';
import Header from './components/Header';
import BackgroundSelector from './components/BackgroundSelector';
import AvatarPreview from './components/AvatarPreview';
import AvatarSettings from './components/AvatarSettings';
import ActionBar from './components/ActionBar';
import {useVideoSettings} from './hooks/useVideoSettings';
import type {VideoSettingsProps} from './types';

const VideoSettings: React.FC<VideoSettingsProps> = ({visible, onClose, appId, initialVideoUrl, onSuccess}) => {
    const {state, actions, figureWidth, figureHeight} = useVideoSettings(appId, initialVideoUrl);
    const {mode, avatar, videoUrl, figureImg, backgroundImg} = state;
    const {fetchVideoDemo} = actions;

    const handleApply = useCallback(() => {
        message.success('设置已应用');
        onClose();
        onSuccess?.();
    }, [onClose, onSuccess]);

    const handleBackgroundClear = useCallback(() => {
        actions.handleBackgroundChange(null);
    }, [actions]);

    useEffect(() => {
        if (visible) {
            fetchVideoDemo();
        }
    }, [visible, fetchVideoDemo]);

    if (!visible) return null;

    return (
        <>
            <div className="fixed inset-0 z-[1000] bg-black/50" onClick={onClose} />
            <div className="fixed left-1/2 top-1/2 z-[1001] flex h-[716px] w-[1110px] -translate-x-1/2 -translate-y-1/2 flex-col rounded-[18px] bg-white">
                <Header mode={mode} onModeChange={actions.handleModeChange} onClose={onClose} />
                <div className="mt-6 flex h-[576px] w-[1110px] flex-1 rounded-lg border border-[rgba(236,238,243,0.4)]">
                    <BackgroundSelector
                        mode={mode}
                        onBackgroundSelected={actions.handleBackgroundChange}
                        onBackgroundCleared={handleBackgroundClear}
                        selectedBackgroundUrl={backgroundImg}
                    />
                    <AvatarPreview
                        mode={mode}
                        avatar={avatar}
                        onAvatarChange={actions.handleAvatarChange}
                        figureImg={figureImg}
                        backgroundImg={backgroundImg}
                        videoUrl={videoUrl}
                        onApply={handleApply}
                        appId={appId}
                        figureWidth={figureWidth}
                        figureHeight={figureHeight}
                        initialPosition={state.lastAppliedPosition}
                    />
                    <AvatarSettings
                        mode={mode}
                        avatar={avatar}
                        onAvatarChange={actions.handleAvatarChange}
                        onReset={actions.handleReset}
                    />
                </div>
                <ActionBar
                    onCancel={onClose}
                    onApply={handleApply}
                    appId={appId}
                    backgroundImg={backgroundImg}
                    previewImage={figureImg}
                    position={{
                        x: avatar.x,
                        y: avatar.y,
                        width: avatar.scale,
                        height: avatar.height,
                    }}
                    mode={mode}
                />
            </div>
        </>
    );
};

export default VideoSettings;
