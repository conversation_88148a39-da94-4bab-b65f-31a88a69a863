import {useSearchParams} from 'react-router-dom';
import {<PERSON>ton, ConfigProvider} from 'antd';
import classNames from 'classnames';
import {useCallback, useMemo, useState, useRef, useEffect} from 'react';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {LogContextProvider} from '@/utils/loggerV2/context';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import LoadError from '@/components/Loading/LoadError';
import Loading from '@/components/Loading';
import {HostingTabKey} from '@/api/videoGenerate/interface';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import guideEmitter from '@/store/agent/GuideEmitter';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {ScrollContainer} from '@/modules/agentPromptEditV2/pc/TuningTab/customStyle';
import ImageVideoContent from '@/modules/agentPromptEditV2/pc/TuningTab/eduVideoHostingConfirm/ImageVideoContent';
import WaitingConfirmVertical from '@/modules/agentPromptEditV2/pc/TuningTab/eduVideoHostingConfirm/WaitingConfirmVertical';
import {useImageAuthLicenseBar} from '@/modules/agentPromptEditV2/pc/TuningTab/hooks/useVideoHostingLicenseBar';
import {
    firstDefault,
    secondDefault,
    firstActive,
    secondActive,
} from '@/modules/agentPromptEditV2/pc/TuningTab/constant';
import {CheckDynamicFigureStatus} from '../hooks/useCheckDynamicFigureStatus';
import useCheckVideoHostingStatus from '../hooks/useCheckVideoHostingStatus';
import {useExtLog} from '../hooks/useExtLog';
import {EduVideoHostingContextProvider} from './context';
import ConfirmHostVertical from './ConfirmHostVertical';

const TabItem = ({
    tab,
    activeTab,
    title,
    description,
    handleTabClick,
}: {
    tab: HostingTabKey;
    activeTab: HostingTabKey;
    title: string;
    description: string;
    handleTabClick: (tab: HostingTabKey) => void;
}) => {
    return (
        <div
            className="flex h-[79px] cursor-pointer items-center justify-between gap-4 rounded-[9px] bg-white pb-[15px] pl-4 pt-[18px]"
            onClick={() => handleTabClick(tab)}
        >
            <div
                className={classNames('text-[50px] font-semibold leading-[40px] text-[#C2C7FF]', {
                    'text-primary': activeTab === tab,
                })}
            >
                {tab === HostingTabKey.imageVideo ? (
                    <img
                        className="h-[40px] w-[51px] object-cover"
                        src={activeTab === tab ? firstActive : firstDefault}
                    />
                ) : (
                    <img
                        className="h-[40px] w-[51px] object-cover"
                        src={activeTab === tab ? secondActive : secondDefault}
                    />
                )}
            </div>
            <div className="flex-1">
                <p
                    className={classNames('text-black-base text-base font-semibold leading-4', {
                        'text-primary': activeTab === tab,
                    })}
                >
                    {title}
                </p>
                <p className="mt-3 text-sm leading-4 text-[#707070]">{description}</p>
            </div>
        </div>
    );
};

export default function EduVideoHostingConfirm({isVideoGenerateHosting = true}: {isVideoGenerateHosting?: boolean}) {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;
    const logExt = useExtLog();

    const {isLoading, fetchData, quantity, dynamicFigureStatus} = useCheckVideoHostingStatus(appId);

    const videoRef = useRef<HTMLVideoElement>(null);
    const [activeTab, setActiveTab] = useState<HostingTabKey>(HostingTabKey.imageVideo);

    const [checkImageLicenseBar, ImageLicenseModalHolder] = useImageAuthLicenseBar(setActiveTab, appId);

    // 获取agentInfo.vertical参数
    const vertical = usePromptEditStoreV2(store => store.agentConfig.agentInfo.vertical);

    // 根据vertical参数判断是否展示两个tab
    const shouldShowTwoTabs = useMemo(() => {
        if (vertical === null) {
            // 为null的时候就走已经做好的tab接口为false的逻辑
            return !isVideoGenerateHosting;
        }

        if (vertical === 44) {
            // 当vertical为44的时候，直接展示两个tab，去掉原来的hosting判断逻辑
            return true;
        }
        // 非44且不为null的时候展示单tab
        return false;
    }, [vertical, isVideoGenerateHosting]);

    const checkAuthorization = useCallback(async () => {
        // 检查形象协议授权
        const isImageLicensed = await checkImageLicenseBar();
        if (!isImageLicensed) return;

        setActiveTab(HostingTabKey.contentHosting);
    }, [checkImageLicenseBar]);

    // 当数字人状态成功且未托管配置授权时，检查形象授权与百家号
    useEffect(() => {
        dynamicFigureStatus === CheckDynamicFigureStatus.Success && !quantity && checkAuthorization();
    }, [checkAuthorization, dynamicFigureStatus, quantity]);

    const dynamicFigure = usePromptEditStoreV2(store => store.agentConfig.agentInfo.dynamicFigure);
    const [, setSearchParams] = useSearchParams();
    const navigateCreate = useCallback(() => {
        setSearchParams(
            prev => {
                const res: Record<string, string> = {};
                res.activeTab = AgentTab.Create;
                const appId = prev.get('appId');
                appId && (res.appId = appId);
                return res;
            },
            {replace: true}
        );
    }, [setSearchParams]);
    const handelCreateDynamicFigure = useCallback(() => {
        guideEmitter.emit('sticky-openDynamicFigureModal');
        navigateCreate();
    }, [navigateCreate]);

    /** 开启托管授权-页面 */
    const confirmContent = useMemo(() => {
        return !!quantity && <ConfirmHostVertical appId={appId} />;
    }, [quantity, appId]);

    const content = useMemo(() => {
        const dynamicFigureStatusConfig = {
            [CheckDynamicFigureStatus.NoDynamicFigure]: {
                title: '😫️ 没有数字人形象',
                describe: '前往「创建页」生成数字人并发布智能体后可生产视频',
                action: handelCreateDynamicFigure,
                actionText: '去创建',
            },
            [CheckDynamicFigureStatus.Generating]: {
                title: '⌛️ 数字人生成中',
                describe: `预计需要${Math.ceil((dynamicFigure?.estimateGenDuration || 0) / 60)}小时，请耐心等待`,
            },
            [CheckDynamicFigureStatus.GenerateFail]: {
                title: '😫️ 数字人生成失败',
                describe: dynamicFigure?.msg,
                action: handelCreateDynamicFigure,
                actionText: '重新生成',
            },
            [CheckDynamicFigureStatus.WaitingPublish]: {
                title: '🚀 数字人未发布',
                describe: '数字人已生成完成，发布智能体后可创建数字人视频',
                action: navigateCreate,
                actionText: '去发布',
            },
        };
        if (dynamicFigureStatus === null) {
            return (
                <div className="relative h-full w-full">
                    <Loading />
                </div>
            );
        }

        if (dynamicFigureStatus === CheckDynamicFigureStatus.Success) {
            return (
                <>
                    {activeTab === HostingTabKey.imageVideo && (
                        <div className="flex h-full w-full flex-col items-center justify-center rounded-lg bg-white">
                            <ImageVideoContent
                                open={activeTab === HostingTabKey.imageVideo}
                                isVideoGenerateHosting={isVideoGenerateHosting}
                                shouldShowTwoTabs={shouldShowTwoTabs}
                            />
                        </div>
                    )}
                    {activeTab === HostingTabKey.contentHosting && <WaitingConfirmVertical appId={appId} />}
                    {!quantity && shouldShowTwoTabs && ImageLicenseModalHolder}
                </>
            );
        }

        const config = dynamicFigureStatusConfig[dynamicFigureStatus];

        return (
            <div className="flex h-full flex-col items-center justify-center gap-1.5 rounded-xl bg-gradient-to-b from-gray-bg-base to-white p-6 ease-linear">
                <div className="w-full text-center text-2xl font-medium">{config.title}</div>
                <div className="w-full text-center text-lg text-gray-tertiary">{config.describe}</div>
                {'action' in config && (
                    <Button type="primary" onClick={config.action}>
                        {config.actionText}
                    </Button>
                )}
            </div>
        );
    }, [
        ImageLicenseModalHolder,
        activeTab,
        appId,
        dynamicFigure?.estimateGenDuration,
        dynamicFigure?.msg,
        dynamicFigureStatus,
        handelCreateDynamicFigure,
        navigateCreate,
        quantity,
        isVideoGenerateHosting,
        shouldShowTwoTabs,
    ]);

    // tab切换逻辑
    const handleTabClick = useCallback(
        async (tab: HostingTabKey) => {
            if (dynamicFigureStatus === CheckDynamicFigureStatus.Success) {
                // 检测 形象是否授权、百家号生成视频弹窗是否授权；如果都授权，则切换tab
                if (!(await checkImageLicenseBar())) {
                    return;
                }

                setActiveTab(tab);
            }
        },
        [checkImageLicenseBar, dynamicFigureStatus]
    );

    const loadError = useCallback(() => <LoadError />, []);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Tag: {
                        colorErrorBg: '#FF3333',
                        colorError: '#FFFFFF',
                        colorInfoBg: '#0066FF',
                        colorInfo: '#FFFFFF',
                        colorWarningBg: '#FAA90E',
                        colorWarning: '#FFFFFF',
                        borderRadiusSM: 3,
                    },
                    Progress: {
                        circleTextColor: '#5562F2',
                        circleTextFontSize: '15px',
                    },
                },
            }}
        >
            <LogContextProvider page={EVENT_PAGE_CONST.CODELESS_ADJUST} ext={logExt}>
                <CommonErrorBoundary renderError={loadError}>
                    <EduVideoHostingContextProvider value={{fetchData}}>
                        {isLoading ? (
                            <div className="relative flex h-[calc(100vh-115px)] w-full items-center justify-center">
                                <Loading />
                            </div>
                        ) : (
                            <div className="h-[calc(100vh-115px)] w-screen">
                                {!quantity && (
                                    <div className="h-full w-full min-w-[1200px] p-[16px_24px_24px_24px]">
                                        <div className="flex h-full overflow-hidden rounded-3xl border border-solid border-colorBorderFormList">
                                            {/* 左右布局 */}
                                            <ScrollContainer className="flex h-full w-[480px] flex-shrink-0 flex-col overflow-y-auto bg-[#F0F4FF] p-6">
                                                <p className="text-black-base text-lg font-semibold leading-6">
                                                    完成托管生成海量视频
                                                </p>
                                                <p className="mt-[7px] text-sm text-[#707070]">
                                                    授权基于您个人真实形象生成的数字人，检查视频板书和脚本内容符合预期后确认并托管，开启海量解题视频生产
                                                </p>
                                                {/* tab 标题 */}
                                                <div className="mt-4 flex flex-col gap-[9px]">
                                                    <TabItem
                                                        tab={HostingTabKey.imageVideo}
                                                        activeTab={activeTab}
                                                        title="形象授权及demo视频确认"
                                                        description={
                                                            shouldShowTwoTabs
                                                                ? '授权使用专属数字人形象,确认视频的样式.细节是否符合预期,确认后后续海量视频将按照此模版生产'
                                                                : '授权使用专属数字人形象，确认视频的样式'
                                                        }
                                                        handleTabClick={handleTabClick}
                                                    />
                                                    {shouldShowTwoTabs && (
                                                        <TabItem
                                                            tab={HostingTabKey.contentHosting}
                                                            activeTab={activeTab}
                                                            title="内容托管"
                                                            description="检查内容符合预期后确认并托管"
                                                            handleTabClick={handleTabClick}
                                                        />
                                                    )}
                                                </div>
                                                <div className="mt-[23px]">
                                                    <p className="text-black-base text-lg font-semibold leading-6">
                                                        视频示例
                                                    </p>
                                                    <div className="mt-4 h-[244px] rounded-[9px] bg-purple-300">
                                                        <video
                                                            ref={videoRef}
                                                            className="mt-4 h-full w-full rounded-[9px] object-contain"
                                                            controls
                                                            loop
                                                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/tuning/edu_videoGenerate_demo.mp4"
                                                        />
                                                    </div>
                                                </div>
                                            </ScrollContainer>
                                            <div className="relative flex-1 flex-shrink bg-white">{content}</div>
                                        </div>
                                    </div>
                                )}
                                {confirmContent}
                            </div>
                        )}
                    </EduVideoHostingContextProvider>
                </CommonErrorBoundary>
            </LogContextProvider>
        </ConfigProvider>
    );
}
