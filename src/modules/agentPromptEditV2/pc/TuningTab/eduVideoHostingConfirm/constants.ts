/**
 * @file 智能体调优-教育视频托管-常量配置文件
 * @description 定义视频托管相关的常量配置，包括容器尺寸、缩放参数和UI组件尺寸等
 * <AUTHOR>
 */

/** 容器尺寸配置 */
export const CONTAINER_SIZE = {
    HORIZONTAL: {
        WIDTH: 528,
        HEIGHT: 297.7,
    },
    VERTICAL: {
        WIDTH: 214.27,
        HEIGHT: 380.98,
    },
} as const;

/** 缩放配置 */
export const SCALE_CONFIG = {
    MIN: 0.2, // 最小值为0.2，对应20%
    MAX: 2, // 最大值为2，对应200%
    DISPLAY_MIN: 20, // 最小显示值为20%
    DISPLAY_MAX: 200, // 最大显示值为200%
    STEP: 0.01,
    WHEEL_STEP: 0.05, // 滚轮缩放步长
    /** 宽高比容差值 */
    ASPECT_RATIO_TOLERANCE: 0.001,
} as const;

/** UI尺寸配置 */
export const UI_SIZE = {
    WRAPPER: {
        WIDTH: 280,
        HEIGHT: 574,
    },
    INPUT: {
        HEIGHT: 30,
        X_WIDTH: 114,
        Y_WIDTH: 114,
        SCALE_WIDTH: 45,
    },
    SLIDER: {
        WIDTH: 151,
        HEIGHT: 18,
    },
    SCALE_WRAPPER: {
        WIDTH: 240,
        HEIGHT: 30,
    },
    BUTTON: {
        WIDTH: 86,
        HEIGHT: 30,
    },
} as const;
