/**
 * 确权托管数据看板
 * <AUTHOR>
 */
import classNames from 'classnames';
import {useSearchParams} from 'react-router-dom';
import {Flex, Divider, Progress, Tooltip} from 'antd';
import {useCallback, useState, useEffect, useMemo} from 'react';
import {LoadingSize} from '@/components/Loading/interface';
import ThemeConfig from '@/styles/lingjing-light-theme';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import CustomPopover from '@/components/Popover';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {numFormatForDataAnalysis} from '@/utils/text';
import {getVideoHostSummary} from '@/api/videoGenerate';
import {VideoHostingSummaryRes} from '@/api/videoGenerate/interface';
import VideoHostingModal from '@/modules/agentPromptEditV2/pc/TuningTab/components/VideoHostingModal';
import useCheckVideoHostingStatus from '../hooks/useCheckVideoHostingStatus';

const HOSTING_TOPLIMIT = 2000000;

// eslint-disable-next-line complexity
export default function TrusteeshipBoard() {
    const {clickLog} = useUbcLogV3();

    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;

    const [loading, setLoading] = useState(true);
    const [loadError, setLoadError] = useState(false);
    const [requestError, setRequestError] = useState<any>();
    const [data, setData] = useState<VideoHostingSummaryRes | null>(null);

    const {quantity, complete} = useCheckVideoHostingStatus(appId);
    const [showCompleteTip, setShowCompleteTip] = useState(false);

    const getSummaryInfo = useCallback(async () => {
        try {
            setLoading(true);
            const res = await getVideoHostSummary({appId});
            setData(res);

            setLoadError(false);
        } catch (error) {
            setLoadError(true);
            setRequestError(error);
        } finally {
            setLoading(false);
        }
    }, [appId]);

    useEffect(() => {
        getSummaryInfo();

        if (complete && quantity < HOSTING_TOPLIMIT) {
            setShowCompleteTip(true);
        }
    }, [complete, getSummaryInfo, quantity]);

    const [openHosting, setOpenHosting] = useState(false);
    const handleOpenModal = useCallback(() => {
        setOpenHosting(true);

        clickLog(EVENT_VALUE_CONST.SETTING);
    }, [clickLog]);

    const onRemoveTip = useCallback(() => {
        // 关闭生产完成提示
        setShowCompleteTip(false);
    }, []);

    const settingEle = useMemo(() => {
        const isDisabled = quantity >= HOSTING_TOPLIMIT;
        return (
            <div
                className={classNames('ml-1 mt-[13px] text-sm', {
                    'cursor-pointer text-primary hover:opacity-60': complete && !isDisabled,
                    'cursor-not-allowed text-gray-400': !complete || isDisabled,
                })}
                onClick={() => !isDisabled && complete && handleOpenModal()}
            >
                设置
            </div>
        );
    }, [complete, handleOpenModal, quantity]);

    const percent = +(((data?.produced ?? 0) / (data?.quantity ?? 0)) * 100).toFixed(1);
    const strokeColor = percent === 100 ? '#39B362' : ThemeConfig.token.colorPrimary;

    return (
        <div className="relative flex min-h-[97px] items-center rounded-[9px] bg-white pl-[33px]">
            {loading ? (
                <Loading size={LoadingSize.small} />
            ) : loadError ? (
                <RenderError size="small" className="!py-0" onBtnClick={getSummaryInfo} error={requestError} />
            ) : (
                <Flex className="items-center">
                    <Flex className="mr-[19px] w-[290px] items-start justify-between">
                        <Flex className="flex-col">
                            <div className="mb-1">
                                <span className="text-sm font-normal text-[#878787]">已生产数量</span>
                            </div>
                            <Flex className="items-center justify-between">
                                <div className="text-left">
                                    <span className="text-[30px] font-semibold text-primary">
                                        {numFormatForDataAnalysis(data?.produced ?? 0)}
                                    </span>
                                    <span className="text-black-base text-lg font-semibold">
                                        /{numFormatForDataAnalysis(data?.quantity ?? 0)}
                                    </span>
                                </div>
                                {quantity >= HOSTING_TOPLIMIT ? (
                                    // 数量 >= 2000000 时直接显示设置（不可点击）
                                    settingEle
                                ) : complete ? (
                                    // 数量 < 2000000 且已完成
                                    showCompleteTip ? (
                                        // 显示完成提示
                                        <CustomPopover
                                            placement="bottomLeft"
                                            open={showCompleteTip}
                                            content="托管生产的视频已完成，快去设置提升托管额度吧！"
                                            onClose={onRemoveTip}
                                            priorityLevel={0}
                                            type="primary"
                                        >
                                            {settingEle}
                                        </CustomPopover>
                                    ) : (
                                        // 不显示提示，直接显示设置
                                        settingEle
                                    )
                                ) : (
                                    // 数量 < 2000000 且未完成，显示 Tooltip
                                    <Tooltip title="设定托管视频生产完成后，才可设置修改托管上限" placement="bottom">
                                        {settingEle}
                                    </Tooltip>
                                )}
                            </Flex>
                        </Flex>
                        <Flex className="self-center">
                            <Progress
                                size={58}
                                type="circle"
                                percent={percent}
                                strokeColor={strokeColor}
                                trailColor="rgba(85, 98, 242, 0.4)"
                                strokeWidth={10}
                                style={{fontWeight: 600}}
                                // eslint-disable-next-line react/jsx-no-bind
                                format={percent =>
                                    percent === 100 ? (
                                        <span className="iconfont icon-success text-[48px] text-[#39B362]"></span>
                                    ) : (
                                        <div className="text-sm">{percent}%</div>
                                    )
                                }
                            />
                        </Flex>
                    </Flex>

                    <Divider type="vertical" className="mr-[47px] h-[55px]" />

                    <Flex className="items-center justify-between gap-[78px]">
                        <div className="gap-[10px]">
                            <div className="text-sm leading-[22px] text-[#878787]">全部视频</div>
                            <div className="text-black-base mt-[10px] text-lg font-semibold leading-[22px]">
                                {numFormatForDataAnalysis(data?.total ?? 0)}
                            </div>
                        </div>
                        <div className="gap-[10px]">
                            <div className="text-sm leading-[22px] text-[#878787]">已生产数量</div>
                            <div className="text-black-base mt-[10px] text-lg font-semibold leading-[22px]">
                                {numFormatForDataAnalysis(data?.availableNum ?? 0)}
                            </div>
                        </div>
                        <div className="gap-[10px]">
                            <div className="text-sm leading-[22px] text-[#878787]">处理中</div>
                            <div className="text-black-base mt-[10px] text-lg font-semibold leading-[22px]">
                                {numFormatForDataAnalysis(data?.producing ?? 0)}
                            </div>
                        </div>
                    </Flex>
                </Flex>
            )}
            <VideoHostingModal
                quantity={quantity}
                openHosting={openHosting}
                setOpenHosting={setOpenHosting}
                agreement
            />
        </div>
    );
}
