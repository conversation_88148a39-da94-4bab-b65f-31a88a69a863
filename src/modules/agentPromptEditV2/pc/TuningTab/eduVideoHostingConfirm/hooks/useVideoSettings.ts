/**
 * @file 智能体调优-教育视频托管-视频设置Hook
 * @description 管理视频设置的状态和操作，包括模式切换、背景更改、数字人位置调整等功能
 * <AUTHOR>
 */

import {useState, useCallback} from 'react';
import {message} from 'antd';
import {getVideoDemo} from '@/api/videoGenerate';
import {SCREEN_MODEL} from '@/api/videoGenerate/interface';
import {AvatarPosition} from '../types';

export interface VideoSettingsState {
    mode: SCREEN_MODEL;
    avatar: AvatarPosition;
    videoUrl: string;
    figureImg: string;
    backgroundImg: string;
    lastAppliedPosition?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

const DEFAULT_AVATAR: AvatarPosition = {
    x: 0.25, // (1 - 0.5) / 2 水平居中
    y: 0.5, // 1 - 0.5 贴底
    scale: 0.5, // 对应滑动条的100%
    height: 0.5, // 对应滑动条的100%
};
const DEFAULT_STATE: VideoSettingsState = {
    mode: SCREEN_MODEL.Horizontal,
    avatar: {
        x: 0.5,
        y: 0.5,
        scale: 0.5,
        height: 0.5,
    },
    videoUrl: '',
    figureImg: '',
    backgroundImg: '',
    lastAppliedPosition: undefined,
};

export const useVideoSettings = (appId: string, initialVideoUrl: string = '') => {
    const [state, setState] = useState<VideoSettingsState>({
        ...DEFAULT_STATE,
        videoUrl: initialVideoUrl,
    });
    const [isLoading, setIsLoading] = useState(false);
    const [originalBackgroundImg, setOriginalBackgroundImg] = useState('');
    const [figureWidth, setFigureWidth] = useState(0);
    const [figureHeight, setFigureHeight] = useState(0);

    // 慧播星画布尺寸
    const getHuaBuSize = useCallback((mode?: number) => {
        const VERTICAL = {
            WIDTH: 1080,
            HEIGHT: 1920,
        };
        const HORIZONTAL = {
            WIDTH: 1920,
            HEIGHT: 1080,
        };
        return mode === 0 ? VERTICAL : HORIZONTAL;
    }, []);

    const fetchVideoDemo = useCallback(async () => {
        try {
            setIsLoading(true);
            const response = await getVideoDemo({appId});

            if (response) {
                setOriginalBackgroundImg(response.backgroundImg || '');
                setFigureWidth(response.width || 0);
                setFigureHeight(response.height || 0);
                setState(prev => ({
                    ...prev,
                    videoStatus: response.status,
                    showEdit: response.showEdit,
                    videoUrl: response.videoUrl || prev.videoUrl,
                    figureImg: response.figureImg || '',
                    backgroundImg: prev.backgroundImg || response.backgroundImg || '',
                    avatar: response.position
                        ? {
                              x: response.position.x / getHuaBuSize(response.screen).WIDTH,
                              y: response.position.y / getHuaBuSize(response.screen).HEIGHT,
                              width: response.position.width / getHuaBuSize(response.screen).WIDTH,
                              scale: response.position.height / getHuaBuSize(response.screen).HEIGHT,
                              height: response.position.height / getHuaBuSize(response.screen).HEIGHT,
                          }
                        : DEFAULT_AVATAR,
                    lastAppliedPosition: response.position,
                }));
            }
        } catch (error) {
            message.error('获取视频预览数据失败');
            console.error('获取视频预览数据失败:', error);
        } finally {
            setIsLoading(false);
        }
    }, [appId, getHuaBuSize]);

    const handleModeChange = useCallback((newMode: SCREEN_MODEL) => {
        setState(prev => ({...prev, mode: newMode}));
    }, []);

    const handleBackgroundChange = useCallback((newBackgroundImg: string | null) => {
        setState(prev => ({...prev, backgroundImg: newBackgroundImg || ''}));
    }, []);

    const handleAvatarChange = useCallback((newAvatar: AvatarPosition) => {
        setState(prev => ({...prev, avatar: newAvatar}));
    }, []);

    const handleReset = useCallback(() => {
        setState(prev => ({...prev, avatar: DEFAULT_AVATAR}));
    }, []);

    const handlePreviewApply = useCallback((position: {x: number; y: number; width: number; height: number}) => {
        setState(prev => ({
            ...prev,
            lastAppliedPosition: position,
        }));
    }, []);

    return {
        state,
        actions: {
            handleModeChange,
            handleBackgroundChange,
            handleAvatarChange,
            handleReset,
            fetchVideoDemo,
            handlePreviewApply,
        },
        figureWidth,
        figureHeight,
        isLoading,
        originalBackgroundImg,
    };
};
