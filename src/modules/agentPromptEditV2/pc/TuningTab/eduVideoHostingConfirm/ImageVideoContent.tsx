/**
 * 数字人形象视频
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import styled from '@emotion/styled';
import {Button, Slider, message} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import guideEmitter from '@/store/agent/GuideEmitter';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';

import {getVideoHostingDemo} from '@/api/videoGenerate';
import {VideoHostingDemoRes} from '@/api/videoGenerate/interface';
import play from '@/modules/dataset/assets/play.png';
import pause from '@/modules/dataset/assets/pause.png';
import VideoSettings from './VideoSettings';

const StyledSlider = styled(Slider)`
    border-radius: 0px;
    .ant-slider-rail,
    .ant-slider-track,
    .ant-slider-step {
        border-radius: 0px !important;
    }
    .ant-slider-step {
        background-color: rgba(0, 0, 0, 0.3) !important;
        top: 0px !important;
        z-index: 10;
    }
    .ant-slider-track {
        background-color: rgba(255, 255, 255, 0.6) !important;
        z-index: 20;
    }
    .ant-slider-handle {
        display: none;
    }
`;

const CustomButton = styled(Button)`
    &:hover {
        color: #848691 !important;
    }
`;

const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
};

const LoadPreviewPending = () => {
    const dynamicFigure = usePromptEditStoreV2(store => store.agentConfig.agentInfo.dynamicFigure);
    return (
        <>
            <div className="flex flex-col items-center justify-center">
                <div className="text-2xl font-medium">⌛️ 数字人生成中</div>
                <div className="mt-4 text-base text-gray-tertiary">
                    预计需要{Math.ceil((dynamicFigure?.estimateGenDuration || 0) / 60)}小时，请耐心等待
                </div>
            </div>
        </>
    );
};

const LoadPreviewError = ({getPreviewData}: {getPreviewData: () => void}) => (
    <div className="text-center">
        <span className="iconfont icon-warn inline-block text-[40px] leading-none text-error"></span>
        <div className="mt-4">
            加载失败，请
            <Button color="default" type="link" onClick={getPreviewData} className="px-0 font-normal">
                重新加载
            </Button>
        </div>
    </div>
);

const LoadingSpinner = () => (
    <div className="flex h-full w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
    </div>
);

// eslint-disable-next-line complexity
export default function ImageVideoContent({
    open,
    isVideoGenerateHosting = true,
    shouldShowTwoTabs = false,
}: {
    open: boolean;
    isVideoGenerateHosting?: boolean;
    shouldShowTwoTabs?: boolean;
}) {
    const [searchParams, setSearchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;
    const videoRef = useRef<HTMLVideoElement>(null);
    const [previewData, setPreviewData] = useState<VideoHostingDemoRes & {status?: number}>({
        videoUrl: '',
        confirmStatus: false,
    });
    const [showSettings, setShowSettings] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const getPreviewData = useCallback(async () => {
        if (!appId) {
            message.error('缺少必要参数：appId');
            return;
        }

        if (open) {
            setIsLoading(true);
            try {
                const res = await getVideoHostingDemo({appId});
                setPreviewData(res);
            } catch (error) {
                setPreviewData({
                    videoUrl: '',
                    confirmStatus: false,
                });
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        }
    }, [appId, open]);

    useEffect(() => {
        if (open) {
            getPreviewData();
        }

        const video = videoRef.current;
        return () => {
            video?.pause();
            setIsPlaying(false);
            setPreviewData({
                videoUrl: '',
                confirmStatus: false,
                status: 0,
            });
        };
    }, [getPreviewData, open]);

    const [duration, setDuration] = useState<number>(0);
    const onCanPlay = () => {
        setDuration(videoRef.current?.duration || 0);
    };

    const [progress, setProgress] = useState(0);
    const onTimeUpdate = useCallback(() => {
        const video = videoRef.current;
        if (!video) return;
        setProgress(video.currentTime);
    }, []);

    const [isPlaying, setIsPlaying] = useState(false);
    const handleEnded = useCallback(() => {
        setIsPlaying(false);
    }, []);

    const handlePlayPause = useCallback(() => {
        const video = videoRef.current;
        if (!video) return;
        setIsPlaying(true);
        if (video.paused) {
            const playPromise = video.play();
            if (playPromise instanceof Promise) {
                playPromise.then(() => setIsPlaying(true)).catch(() => setIsPlaying(false));
            } else {
                setIsPlaying(true);
            }
        } else {
            video.pause();
            setIsPlaying(false);
        }
    }, []);

    const handleProgressClick = useCallback(
        (process: number) => {
            const video = videoRef.current;
            if (!video || !isPlaying) return;
            video.currentTime = process;
        },
        [isPlaying]
    );

    const handleSettingsClick = useCallback(() => {
        if (!appId) {
            message.error('缺少必要参数：appId');
            return;
        }

        setShowSettings(true);
    }, [appId]);

    const handleSettingsClose = useCallback(() => {
        setShowSettings(false);
    }, []);

    const handleResetFigure = useCallback(() => {
        guideEmitter.emit('sticky-openDynamicFigureModal');
        setSearchParams(
            prev => {
                const res: Record<string, string> = {};
                res.activeTab = AgentTab.Create;
                const appId = prev.get('appId');
                appId && (res.appId = appId);
                return res;
            },
            {replace: true}
        );
    }, [setSearchParams]);

    return (
        <>
            <div className="relative flex h-[440px] w-[780px] items-center justify-center overflow-hidden rounded-[18px]">
                {isLoading ? (
                    <LoadingSpinner />
                ) : previewData.status === 1 ? (
                    <LoadPreviewPending />
                ) : previewData.status === 2 ? (
                    <>
                        <div
                            className={`relative flex h-full w-full items-center justify-center ${
                                previewData.screen === 0 ? 'bg-black' : ''
                            }`}
                        >
                            <video
                                className={`${
                                    previewData.screen === 0 ? 'h-full' : 'h-[440px] w-[780px]'
                                } object-contain`}
                                ref={videoRef}
                                onTimeUpdate={onTimeUpdate}
                                src={previewData.videoUrl}
                                onEnded={handleEnded}
                                onCanPlay={onCanPlay}
                            ></video>
                        </div>
                        {duration > 0 && (
                            <span
                                className="absolute bottom-[14px] right-[14px] text-[12px] font-medium leading-none text-white"
                                style={{
                                    textShadow: '0px 1.67px 3.34px #0000004D',
                                }}
                            >
                                {formatTime(duration)}
                            </span>
                        )}
                        <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                            <img
                                className="h-[48px] w-[48px] cursor-pointer"
                                src={isPlaying ? pause : play}
                                onClick={handlePlayPause}
                            />
                        </div>
                        {isPlaying && (
                            <div className="absolute bottom-0 h-11 w-full bg-gradient-to-t">
                                <StyledSlider
                                    min={0}
                                    max={duration && Math.round(duration)}
                                    value={progress}
                                    onChange={handleProgressClick}
                                    className="absolute bottom-0 m-0 h-1 w-full p-0"
                                    tooltip={{open: false}}
                                />
                            </div>
                        )}
                    </>
                ) : (
                    <LoadPreviewError getPreviewData={getPreviewData} />
                )}
            </div>
            {previewData.status === 2 && (
                <>
                    <p className="text-black-base mt-[17px] text-lg font-medium">当前数字形象预览</p>
                    {shouldShowTwoTabs && (
                        <div className="text-black-base mt-2 flex flex-col items-center text-sm font-medium">
                            <p>预览demo视频效果，满意则点击确认无误，开启托管后生产的海量视频将采用demo视频样式；</p>
                            <p>不满意联系对接人重新调整</p>
                        </div>
                    )}
                    {!shouldShowTwoTabs && (
                        <div className="mt-2 flex gap-2">
                            <CustomButton
                                className="flex h-[30px] w-[86px] items-center justify-center whitespace-nowrap rounded-[100px] font-['PingFang_SC'] text-[14px] font-[500] leading-[20px] tracking-[0] text-colorTextDefault"
                                style={{
                                    borderColor: '#EDEEF0',
                                }}
                                onClick={handleResetFigure}
                            >
                                重置形象
                            </CustomButton>
                            <CustomButton
                                className="flex h-[30px] w-[86px] items-center justify-center whitespace-nowrap rounded-[100px] font-['PingFang_SC'] text-[14px] font-[500] leading-[20px] tracking-[0] text-colorTextDefault"
                                style={{
                                    borderColor: '#EDEEF0',
                                }}
                                onClick={handleSettingsClick}
                            >
                                设置画面
                            </CustomButton>
                        </div>
                    )}
                </>
            )}
            {appId && (
                <VideoSettings
                    visible={showSettings}
                    onClose={handleSettingsClose}
                    appId={appId}
                    initialVideoUrl={previewData.videoUrl}
                    onSuccess={getPreviewData}
                />
            )}
        </>
    );
}
