/**
 * 检查生产托管配置状态
 */
import {useEffect, useMemo, useState, useCallback} from 'react';
import {getVideoHostingConfig} from '@/api/videoGenerate/index';
import {VideoHostingConfigResponse} from '@/api/videoGenerate/interface';
import {getPreviewVideoStatus} from '@/api/videoGenerate';
import useCheckDynamicFigureStatus from '../hooks/useCheckDynamicFigureStatus';

const useCheckVideoHostingStatus = (appId: string) => {
    const [videoHostComplete, setVideoHostComplete] = useState<VideoHostingConfigResponse>();
    const [loading, setLoading] = useState(true);
    const [demoStatus, setDemoStatus] = useState<number>(0);
    const dynamicFigureStatus = useCheckDynamicFigureStatus(appId);

    const fetchData = useCallback(async () => {
        if (!appId) {
            return;
        }

        setLoading(true);

        try {
            const [hostingRes, demoRes] = await Promise.all([
                getVideoHostingConfig({appId}),
                getPreviewVideoStatus({appId}),
            ]);
            setVideoHostComplete(hostingRes);
            setDemoStatus(demoRes.status);
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }, [appId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const {quantity, complete} = useMemo(() => {
        if (loading || !videoHostComplete) {
            return {quantity: 0, complete: true};
        }
        return {quantity: videoHostComplete.quantity, complete: videoHostComplete.complete};
    }, [loading, videoHostComplete]);

    return {
        isLoading: loading,
        quantity,
        complete,
        dynamicFigureStatus,
        demoStatus,
        fetchData,
    };
};

export default useCheckVideoHostingStatus;
