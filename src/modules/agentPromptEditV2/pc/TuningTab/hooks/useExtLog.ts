import {useMemo} from 'react';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {LJExtData} from '@/utils/loggerV2/utils';
import {TuningTabKey} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';

export function useExtLog() {
    const [appId, name, currentPageType] = usePromptEditStoreV2(store => [
        store.agentConfig.agentInfo.appId!,
        store.agentConfig.agentInfo.name,
        store.currentPageType,
    ]);

    const extLog = useMemo(
        () =>
            ({
                [EVENT_EXT_KEY_CONST.AGENT_ID]: appId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
                [EVENT_EXT_KEY_CONST.CREATE_PAGE_TYPE]: currentPageType,
                [EVENT_EXT_KEY_CONST.P_AGENT_OPTIMISE_TAB]: +TuningTabKey.videoGenerate,
            }) as LJExtData,
        [appId, name, currentPageType]
    );
    // 返回扩展日志数据
    return extLog;
}
