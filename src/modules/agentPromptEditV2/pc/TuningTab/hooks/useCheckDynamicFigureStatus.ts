import {useEffect, useMemo, useState} from 'react';
import {DynamicFigureStatus} from '@/api/agentEdit/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {getIsDigitalExist} from '@/api/q2c';

export enum CheckDynamicFigureStatus {
    // 已可用
    Success,
    // 没有数字人
    NoDynamicFigure,
    // 数字人生成中
    Generating,
    // 生成失败
    GenerateFail,
    // 生成成功但是未发布过
    WaitingPublish,
}

const GeneratingStatusSet = new Set([
    DynamicFigureStatus.NEW,
    DynamicFigureStatus.VIDEO_QUALITING,
    DynamicFigureStatus.VIDEO_QUALITING_DONE,
    DynamicFigureStatus.VIDEO_MAKING,
]);

const GenerateFailStatusSet = new Set([
    DynamicFigureStatus.PROCESS_ERROR,
    DynamicFigureStatus.VIDEO_QUALITING_FAIL,
    DynamicFigureStatus.VIDEO_MAKING_DONE_FAIL,
]);

const useCheckDynamicFigureStatus = (appId?: string | null) => {
    const dynamicFigure = usePromptEditStoreV2(store => store.agentConfig.agentInfo.dynamicFigure);
    const [isDigitalExist, setIsDigitalExist] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (!appId) return;
        getIsDigitalExist({appId})
            .then(exist => {
                setIsDigitalExist(exist);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [appId]);

    const dynamicFigureStatusConfig = useMemo(() => {
        if (loading) return null;

        if (isDigitalExist) return CheckDynamicFigureStatus.Success;

        if (!dynamicFigure) return CheckDynamicFigureStatus.NoDynamicFigure;

        if (GeneratingStatusSet.has(dynamicFigure.status)) return CheckDynamicFigureStatus.Generating;

        if (GenerateFailStatusSet.has(dynamicFigure.status)) return CheckDynamicFigureStatus.GenerateFail;

        return CheckDynamicFigureStatus.WaitingPublish;
    }, [dynamicFigure, isDigitalExist, loading]);

    return dynamicFigureStatusConfig;
};

export default useCheckDynamicFigureStatus;
