import {CheckboxChangeEvent} from 'antd/es/checkbox';
import {useState, useCallback, useMemo, useRef, useEffect} from 'react';
import {Checkbox, Button} from 'antd';
import {HostingTabKey} from '@/api/videoGenerate/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/pc/TuningTab/hooks/useExtLog';
import DICTS from '@/dicts/home';
import beginnerGuideApi from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {videoDemoConfirm} from '@/api/videoGenerate/index';
import {getVideoHostingDemo} from '@/api/videoGenerate';

// 形象授权 & demo视频确认
const useVideoAuthorization = (appId: string) => {
    const [isAgreed, setIsAgreed] = useState(false);
    const updateAgree = useCallback(async () => {
        const [popupResult, demoResult] = await Promise.all([
            beginnerGuideApi.getPopup({name: PopupName.EducationAddBjhVideo}),
            getVideoHostingDemo({appId}),
        ]);
        const newStatus = !popupResult.show && demoResult.confirmStatus;
        setIsAgreed(newStatus);
        return !popupResult.show && demoResult.confirmStatus;
    }, [appId]);

    const agree = useCallback(async () => {
        await beginnerGuideApi.recordPopup({name: PopupName.EducationAddBjhVideo});
        setIsAgreed(true);
    }, []);

    // demo视频确认无误
    const confirmDemoVideoAccess = useCallback(async () => {
        if (appId) {
            await videoDemoConfirm({appId});
        }
    }, [appId]);

    useEffect(() => {
        updateAgree();
    }, [updateAgree]);

    return [isAgreed, agree, confirmDemoVideoAccess] as const;
};

const useImageAuthLicenseBar = (setActiveTab: (tab: HostingTabKey) => void, appId: string) => {
    const {clickLog} = useUbcLogV3();
    const logExt = useExtLog();

    const [open, setOpen] = useState(false);
    const [isAgreed, agree, confirmDemoVideoAccess] = useVideoAuthorization(appId);
    // 新增一个状态来维护复选框的选中状态，初始值设为 true
    const [isChecked, setIsChecked] = useState(true);
    // 根据 isChecked 来设置按钮的禁用状态
    const [okButton, setOkButton] = useState({disabled: !isChecked});

    const handleOk = useCallback(async () => {
        setOpen(false);
        await confirmDemoVideoAccess();
        await agree();
        setActiveTab(HostingTabKey.contentHosting);
        promiseCallbackRef.current?.resolve(true);
        promiseCallbackRef.current = null;

        clickLog(EVENT_VALUE_CONST.NEXT_STEP, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
    }, [confirmDemoVideoAccess, agree, clickLog, logExt, setActiveTab]);

    const promiseCallbackRef = useRef<{
        resolve: (value: boolean | PromiseLike<boolean>) => void;
        reject: (reason?: any) => void;
    } | null>(null);

    // 返回一个函数，用于检查是否需要显示许可协议弹窗
    const checkImageLicenseBar = useCallback(async () => {
        if (isAgreed) {
            return Promise.resolve(true);
        }

        setOpen(true);
        return new Promise<boolean>((resolve, reject) => {
            promiseCallbackRef.current = {resolve, reject};
        });
    }, [isAgreed]);

    const handleSetAgreement = useCallback((e: CheckboxChangeEvent) => {
        const agreed = e.target.checked;
        setIsChecked(agreed);
        setOkButton({disabled: !agreed});
    }, []);

    const handleOpenDoc = useCallback(
        (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
            e?.preventDefault();
            clickLog(EVENT_VALUE_CONST.LIGHTHOUSE_RULE_LINK, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);

            window.open(DICTS.LIGHTHOUSE_RULE);
        },
        [clickLog, logExt]
    );

    const contextHolder = useMemo(() => {
        return (
            <>
                {!isAgreed && open && (
                    <div className="absolute bottom-0 left-0 flex h-[65px] w-full items-center justify-between bg-white px-[65px]">
                        <Checkbox onChange={handleSetAgreement} defaultChecked>
                            阅读并同意
                            <a target="_blank" className="mx-[4px]" onClick={handleOpenDoc}>
                                《灯塔项目规则》
                            </a>
                            ，授权数字人及活动素材用于账号视频全部生产
                        </Checkbox>
                        <Button type="primary" onClick={handleOk} disabled={okButton.disabled} className="rounded-full">
                            确认无误
                        </Button>
                    </div>
                )}
            </>
        );
    }, [isAgreed, handleOk, handleOpenDoc, handleSetAgreement, okButton.disabled, open]);

    return [checkImageLicenseBar, contextHolder] as const;
};

export {useImageAuthLicenseBar};
