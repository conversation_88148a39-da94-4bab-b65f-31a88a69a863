import {useCallback, useMemo, useState} from 'react';
import {<PERSON><PERSON>, Mo<PERSON>} from 'antd';
import urls from '@/links';
import {AnnInfo, QaDetail, QaDetailAction, QaStatus} from '@/api/agentEditV2/interface';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import FeedbackButton from '@/modules/agentPromptEditV2/pc/TuningTab/components/Feedback';
import Markdown from '@/modules/agentPromptEditV2/pc/TuningTab/components/Markdown';

const supportedOperations: Omit<
    Record<QaStatus, Partial<Record<QaDetailAction, string>>>,
    QaStatus.Effective | QaStatus.Expired | QaStatus.Submitted | QaStatus.Distributed
> = {
    [QaStatus.Pending]: {
        [QaDetailAction.Adopt]: '采纳',
        [QaDetailAction.Feedback]: '反馈',
        [QaDetailAction.Featured]: '采纳并精选',
    },
    [QaStatus.Feedbacked]: {
        [QaDetailAction.Feedback]: '继续反馈',
    },
    [QaStatus.Adopted]: {
        [QaDetailAction.Feedback]: '反馈',
        [QaDetailAction.Featured]: '精选',
    },
    [QaStatus.Featured]: {
        [QaDetailAction.CancelFeature]: '取消精选',
    },
    [QaStatus.Fixed]: {
        [QaDetailAction.Feedback]: '继续反馈',
    },
    [QaStatus.Generating]: {
        [QaDetailAction.Feedback]: '生成中',
    },
};

export default function useQaDetailModal({
    onAdopt,
    onFeedback,
    onFeature,
    onCancelFeature,
    canFeature,
}: {
    onFeedback: () => void;
    onFeature: (record: QaDetail) => () => Promise<void>;
    onCancelFeature: (record: QaDetail) => () => Promise<void>;
    onAdopt: (record: QaDetail) => () => Promise<void>;
    canFeature: boolean;
}) {
    const [detail, setDetail] = useState<QaDetail>();
    const [open, setOpen] = useState(false);
    const handleAdopt = useCallback(() => {
        if (!detail) {
            return;
        }
        onAdopt(detail)().then(() => setOpen(false));
    }, [detail, onAdopt]);
    const handleFeature = useCallback(() => {
        if (!detail) {
            return;
        }
        onFeature(detail)().then(() => setOpen(false));
    }, [detail, onFeature]);
    const handleCancelFeature = useCallback(() => {
        if (!detail) {
            return;
        }
        onCancelFeature(detail)().then(() => setOpen(false));
    }, [detail, onCancelFeature]);
    const handleOpen = useCallback((detail: QaDetail) => {
        setDetail(detail);
        setOpen(true);
    }, []);
    const handleClose = useCallback(() => {
        setOpen(false);
    }, []);

    const onFinishFeedback = useCallback(() => {
        setOpen(false);
        onFeedback();
    }, [onFeedback]);

    const handleParagraphClick = useCallback(
        (annInfo: AnnInfo) => () => {
            window.open(
                `${urls.datasetParagraphDetail.fill({id: annInfo.datasetId})}?fileIds=${annInfo.fileId}&textId=${
                    annInfo.id
                }`,
                '_blank'
            );
        },
        []
    );

    const footer = useMemo(() => {
        if (!detail) return null;
        return (
            <div className="flex justify-end gap-3">
                {supportedOperations[detail.status][QaDetailAction.Feedback] && (
                    <FeedbackButton onFinish={onFinishFeedback} detail={detail}>
                        {supportedOperations[detail.status][QaDetailAction.Feedback]}
                    </FeedbackButton>
                )}
                {supportedOperations[detail.status][QaDetailAction.Adopt] && (
                    <Button onClick={handleAdopt}>{supportedOperations[detail.status][QaDetailAction.Adopt]}</Button>
                )}
                {supportedOperations[detail.status][QaDetailAction.CancelFeature] && (
                    <Button onClick={handleCancelFeature}>
                        {supportedOperations[detail.status][QaDetailAction.CancelFeature]}
                    </Button>
                )}
                {supportedOperations[detail.status][QaDetailAction.Featured] && (
                    <Button onClick={handleFeature} type="primary" disabled={!canFeature}>
                        {supportedOperations[detail.status][QaDetailAction.Featured]}
                    </Button>
                )}
            </div>
        );
    }, [canFeature, detail, handleAdopt, handleCancelFeature, handleFeature, onFinishFeedback]);

    const modal = useMemo(() => {
        if (!detail) return null;
        return (
            <Modal
                width={750}
                open={open}
                title="问答详情"
                okText="确认"
                cancelText="取消"
                onCancel={handleClose}
                centered
                footer={footer}
            >
                <div className="flex max-h-[600px] flex-col gap-6">
                    <div className="flex flex-shrink overflow-hidden rounded-xl bg-gray-bg-base py-4 pr-[6px]">
                        <ScrollContainer scrollY scrollbarWidth={4} className="w-full pl-4 pr-[6px]">
                            <div className="flex w-full justify-end">
                                <div className="mb-4 max-w-[40rem] flex-col rounded-[.9375rem] rounded-br-[.1875rem] bg-[#7365FF] px-[13px] py-[9px] text-white">
                                    <Markdown>{detail.question}</Markdown>
                                </div>
                            </div>
                            <div className="inline-flex max-w-[40rem] flex-col rounded-[.9375rem] rounded-tl-[.1875rem] bg-white px-[13px] py-[9px] text-zinc-800">
                                <Markdown>{detail.answer}</Markdown>
                            </div>
                        </ScrollContainer>
                    </div>
                    {!!detail.annText?.length && (
                        <div className="flex flex-shrink-0 flex-col gap-3">
                            <h2 className="text-base font-medium leading-none">关联知识库</h2>
                            <div className="flex flex-wrap gap-[6px]">
                                {detail.annText.map(item => (
                                    <button
                                        className="whitespace-nowrap rounded-md bg-gray-bg-base px-3 py-2 font-medium leading-none text-primary"
                                        key={item.id}
                                        onClick={handleParagraphClick(item)}
                                    >{`${item.datasetName} - 段落${item.number}`}</button>
                                ))}
                            </div>
                        </div>
                    )}
                    {detail.actionData && (detail.actionData.idealAnswer || !!detail.actionData.reasonList?.length) && (
                        <div className="flex flex-col gap-3 ">
                            <h2 className="text-base font-medium leading-none">反馈原因</h2>
                            <div className="flex max-h-[150px] overflow-hidden rounded-xl bg-gray-bg-base py-4 pr-[6px]">
                                <ScrollContainer
                                    scrollbarWidth={4}
                                    scrollY
                                    className="flex flex-col gap-3 pl-4 pr-[6px]"
                                >
                                    {!!detail.actionData?.reasonList?.length && (
                                        <div className="flex gap-[6px]">
                                            {detail.actionData?.reasonList?.map(item => (
                                                <div className="rounded-md bg-white px-3 py-2 leading-none" key={item}>
                                                    {item}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                    {detail.actionData.idealAnswer && (
                                        <div className="">
                                            <span className="text-gray-tertiary">理想回答：</span>
                                            <span>{detail.actionData.idealAnswer}</span>
                                        </div>
                                    )}
                                </ScrollContainer>
                            </div>
                        </div>
                    )}
                </div>
            </Modal>
        );
    }, [detail, footer, handleClose, handleParagraphClick, open]);

    return [modal, handleOpen] as const;
}
