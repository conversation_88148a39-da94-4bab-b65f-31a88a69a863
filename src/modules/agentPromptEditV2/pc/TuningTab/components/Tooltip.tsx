import classNames from 'classnames';
import {Tooltip, TooltipProps} from 'antd';
import {css} from '@emotion/css';

const tooltipRootClassName = css`
    max-width: none !important;
    .ant-tooltip-arrow {
        &::before {
            background-color: #fff !important;
        }
        &::after {
            background-color: #fff !important;
        }
    }
    .ant-tooltip-content {
        border-radius: 1.125rem;
        background: #fff;
        max-width: 60rem;
        .ant-tooltip-inner {
            box-shadow: 0px 30px 200px 0px rgba(29, 34, 82, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            background-color: #fff;
            color: #000;
        }
    }
`;

export default function CustomTooltip({rootClassName, ...props}: TooltipProps) {
    return <Tooltip rootClassName={classNames(tooltipRootClassName, rootClassName)} {...props} />;
}
