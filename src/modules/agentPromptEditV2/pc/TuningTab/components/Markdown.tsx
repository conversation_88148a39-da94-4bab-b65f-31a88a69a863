import {useEffect, useMemo, useRef} from 'react';
import {Marked} from 'marked';
import classNames from 'classnames';
import DOMPurify from 'dompurify';
import {css} from '@emotion/css';

const marked = new Marked();
const defaultStyle = css`
    li {
        display: list-item;
    }

    ol {
        list-style-type: decimal;
    }

    ul {
        list-style-type: disc;
    }

    ol,
    ul,
    dir,
    menu,
    dd {
        margin-left: 24px;
    }

    ol ul,
    ul ol,
    ul ul,
    ol ol {
        margin-top: 0;
        margin-bottom: 0;
    }

    table {
        color: rgba(39, 39, 42);
        border-spacing: 0;
        border-collapse: collapse;
    }

    table th,
    table td {
        padding: 6px 12px;
        border: 1px solid #dfe2e5;
    }

    table tr {
        background-color: #fff;
    }

    table thead tr,
    table tbody tr:nth-child(even) {
        background-color: #f8f8f8;
    }

    code {
        white-space: pre-wrap;
        word-break: break-all;
    }

    // 以下为 q2c 新增样式
    hr {
        margin-top: 6px;
        margin-bottom: 6px;
    }

    blockquote {
        border-left: 3px solid #848691;
        padding-left: 6px;
        color: #848691;
    }

    h1,
    h2,
    h3 {
        font-weight: 500;
        margin-top: 4px;
        margin-bottom: 4px;
    }

    h1,
    h2 {
        font-size: 15px;
        line-height: 24px;
    }

    h3 {
        font-size: 14px;
        line-height: 22px;
    }
`;

const Markdown = ({children, className}: {children: string; className?: string}) => {
    const ref = useRef<HTMLDivElement>(null);

    const innerHTML = useMemo(() => {
        if (!children) {
            return '';
        }
        // 创建一个临时的 DOM 元素来操作 HTML
        const tempDiv = document.createElement('div');

        // bca-disable-line
        tempDiv.innerHTML = DOMPurify.sanitize(marked.parse(children) as string);

        // a标签链接用新页面打开。
        tempDiv?.querySelectorAll('a')?.forEach(link => {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
        });

        return tempDiv.innerHTML;
    }, [children]);

    useEffect(() => {
        // bca-disable-line
        ref.current && (ref.current.innerHTML = innerHTML);
    }, [innerHTML]);

    return <div className={classNames(defaultStyle, className)} ref={ref}></div>;
};

export default Markdown;
