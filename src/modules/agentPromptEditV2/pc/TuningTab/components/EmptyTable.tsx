/**
 * @file 智能体调优 Tab 空态提示页
 * <AUTHOR>
 *
 */
import {useCallback, useMemo} from 'react';
import {Button} from 'antd';
import {useSearchParams} from 'react-router-dom';
import isEmpty from 'lodash/isEmpty';
import Lot<PERSON> from 'lottie-react';
import empty from '@/assets/version_empty_3.png';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentPermission} from '@/api/agentEdit/interface';
import guideEmitter from '@/store/agent/GuideEmitter';
import {AuditSign} from '@/modules/agentList/interface';
import {TuningQASelectItems} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';
import {AgentTab, TuningTabLogMap} from '@/modules/agentPromptEditV2/interface';
import Loading from '@/modules/agentPromptEditV2/pc/TuningTab/Loading.json';
import {LJExtData} from '@/utils/logger';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST, EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {QaSource} from '@/api/agentEditV2/interface';

interface ActionConfig {
    desc: string;
    loading?: boolean;
    actionName?: string;
    action?: () => void;
}

const EmptyTable = ({
    source,
    activeQaStatus,
    processingDataset,
    hasSearchParams,
}: {
    source: QaSource;
    activeQaStatus: TuningQASelectItems;
    processingDataset: boolean;
    hasSearchParams: boolean;
}) => {
    const {display, agentConfig, permission, appName} = usePromptEditStoreV2(store => ({
        display: store.display,
        agentConfig: store.agentConfig,
        permission: store.permission,
        appName: store.agentConfig?.agentInfo?.name,
    }));
    const [searchParams, setSearchParams] = useSearchParams();
    const appId = searchParams.get('appId');
    const {clickLog} = useUbcLogV3();

    const toCreate = useCallback(() => {
        setSearchParams(
            (prev): Record<string, string> => {
                const appId = prev.get('appId');
                if (appId) {
                    return {appId, activeTab: AgentTab.Create};
                }
                return {activeTab: AgentTab.Create};
            },
            {replace: true}
        );
    }, [setSearchParams]);

    const getProcessingActionConfig = useCallback(() => {
        // 未挂载知识库，提示添加知识库
        if (isEmpty(agentConfig?.agentJson?.datasetIds)) {
            return {
                actionName: '去添加知识库',
                desc: '💪 立即去添加智能体挂载的知识库，平台将为您自动生成优质问答',
                action: () => {
                    guideEmitter.emit('sticky-dataset');
                    toCreate();
                    clickLog(
                        EVENT_VALUE_CONST.ADD_DATASET,
                        {
                            [EVENT_EXT_KEY_CONST.AGENT_ID]: appId ?? '',
                            [EVENT_EXT_KEY_CONST.AGENT_NAME]: appName,
                            [EVENT_EXT_KEY_CONST.ADJUST_TAB_NAME]: TuningTabLogMap[activeQaStatus],
                        } as LJExtData,
                        EVENT_PAGE_CONST.CODELESS_ADJUST
                    );
                },
            };
        }

        const isPublic = permission === AgentPermission.PUBLIC;
        const isNotAuditFailed = display.auditSign !== AuditSign.FAIL;
        const isAuditing = display.auditSign === AuditSign.AUDITING;

        // 未公开发布
        if (!isPublic) {
            return {
                actionName: '去发布',
                desc: '💪 立即公开发布智能体，尝试获取智能体分发机会～',
                action: () => {
                    guideEmitter.emit('sticky-publish');
                    toCreate();
                },
            };
        }

        // 公开发布 & 审核中
        if (isAuditing) {
            return {
                loading: true,
                desc: '审核中，预计1小时反馈审核结果，审核通过后将自动为你生成优质问答',
            };
        }

        // 公开发布 & 审核通过
        if (isNotAuditFailed) {
            // 问答数据生成中
            if (processingDataset) {
                return {
                    loading: true,
                    desc: '正在努力为您生成优质问答，请稍后查看',
                };
            }

            // 无正在生成的问答数据，提示添加知识库
            return {
                actionName: '去添加知识库',
                desc: '暂无可生成优质问答的知识库内容，点击去添加更多知识库~',
                action: () => {
                    guideEmitter.emit('sticky-dataset');
                    toCreate();
                },
            };
        }

        return {
            actionName: '去发布',
            desc: '💪 立即公开发布智能体，尝试获取智能体分发机会～',
            action: () => {
                guideEmitter.emit('sticky-publish');
                toCreate();
            },
        };
    }, [
        agentConfig?.agentJson?.datasetIds,
        permission,
        display.auditSign,
        toCreate,
        clickLog,
        appId,
        appName,
        activeQaStatus,
        processingDataset,
    ]);

    // 空态引导提示配置
    const actionConfig: ActionConfig = useMemo(() => {
        if (source === QaSource.Preview) {
            return {desc: '暂无数据，在预览模块进行调优的问答将在此处展示'};
        }

        if (hasSearchParams) {
            return {desc: '暂无检索结果'};
        }

        if (activeQaStatus === TuningQASelectItems.processed) {
            return {desc: '暂无问答数据，快去优化问答数据吧'};
        }

        if (activeQaStatus === TuningQASelectItems.processing) {
            return getProcessingActionConfig();
        }

        return {desc: '暂无问答数据，快去优化问答数据吧'};
    }, [activeQaStatus, getProcessingActionConfig, hasSearchParams, source]);

    return (
        <div className="my-[65px] flex min-h-[calc(100vh-657px)] flex-col items-center">
            {actionConfig.loading ? (
                <Lottie className="w-[8.75rem]" animationData={Loading} autoPlay loop />
            ) : (
                <img src={empty} className="w-[7.5rem]" />
            )}

            <div className="mt-2.5 text-sm text-gray-secondary">{actionConfig.desc}</div>

            <div className="mt-6 h-[36px]">
                {actionConfig.actionName && (
                    <Button type="primary" onClick={actionConfig.action} className="rounded-[100px]">
                        {actionConfig.actionName}
                    </Button>
                )}
            </div>
        </div>
    );
};

export default EmptyTable;
