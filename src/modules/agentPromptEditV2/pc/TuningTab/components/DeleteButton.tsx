/**
 * 删除调优信息按钮
 */

import React, {useCallback} from 'react';
import {But<PERSON>, Modal} from 'antd';
import useMessage from 'antd/es/message/useMessage';
import {useSearchParams} from 'react-router-dom';
import {css} from '@emotion/css';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {deleteTuningData} from '@/api/agentEditV2';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {LJExtData} from '@/utils/loggerV2/utils';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST, EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {TuningTabLogMap} from '@/modules/agentPromptEditV2/interface';
import {TuningQASelectItems} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';

const confirmModalStyle = css`
    .ant-modal-content {
        padding: 1.5rem !important;
    }
`;

export default function DeleteButton({
    tuningId,
    onDelete,
    activeQaStatus,
}: {
    tuningId: string;
    onDelete: () => void;
    activeQaStatus: TuningQASelectItems;
}) {
    const [modal, modalContextHolder] = Modal.useModal();
    const [messageApi, MessageContext] = useMessage();
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId');
    const appName = usePromptEditStoreV2(store => store.agentConfig.agentInfo.name);
    const {clickLog} = useUbcLogV3();

    const handleDelete = useCallback(async () => {
        clickLog(
            EVENT_VALUE_CONST.DELETE_TUNING_DATA_BTN,
            {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: appId ?? '',
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: appName,
                [EVENT_EXT_KEY_CONST.ADJUST_TAB_NAME]: TuningTabLogMap[activeQaStatus],
            } as LJExtData,
            EVENT_PAGE_CONST.CODELESS_ADJUST
        );

        await modal.confirm({
            icon: <ExclamationCircleOutlined />,
            content: '删除后数据不可恢复，确认删除？',
            cancelText: '取消',
            okText: '确定',
            centered: true,
            autoFocusButton: null,
            className: confirmModalStyle,
            onOk() {
                return deleteTuningData({tuningId})
                    .then(onDelete)
                    .catch(() => {
                        messageApi.error('删除失败，请稍后重试');
                    });
            },
        });
    }, [tuningId, onDelete, modal, messageApi, activeQaStatus, appId, appName, clickLog]);

    return (
        <>
            {modalContextHolder}
            {MessageContext}

            <Button
                key={Math.random()}
                className="font-normal"
                type="link"
                size="small"
                icon={<span className="iconfont icon-delete text-sm" />}
                onClick={handleDelete}
            >
                删除
            </Button>
        </>
    );
}
