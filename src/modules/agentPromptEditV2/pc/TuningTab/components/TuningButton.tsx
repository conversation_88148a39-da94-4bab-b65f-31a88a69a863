/**
 * 调优按钮
 *
 */
import {Button, Form, ButtonProps, message} from 'antd';
import type {FormInstance} from 'antd/lib/form';
import {useCallback, useState, useMemo} from 'react';
import Tippy from '@tippyjs/react';
import {QaDetail, QaStatus} from '@/api/agentEditV2/interface';
import {updateTuningData} from '@/api/agentEditV2';
import {getCountConfig} from '@/utils/text';
import {auditText} from '@/api/audit';
import {TextArea} from '@/modules/agentPromptEditV2/pc/TuningTab/customStyle';
import {EVENT_VALUE_CONST, EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {LJExtData} from '@/utils/logger';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {TuningTabLogMap} from '@/modules/agentPromptEditV2/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {TuningQASelectItems} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';

interface FormValue {
    answer: string;
    chatAnswer: string;
}

interface TuningFormProps {
    className?: string;
    detail: QaDetail;
    onSubmit: (values: FormValue) => Promise<boolean>;
    onCancel: () => void;
    form: FormInstance<FormValue>;
    submitBtnText?: string;
}

/**
 * 调优表单
 */
function TuningForm({detail, onSubmit, onCancel, form, submitBtnText = '提交'}: TuningFormProps) {
    const [submitLoading, setSubmitLoading] = useState(false);
    const answer = Form.useWatch('answer', form);
    const chatAnswer = Form.useWatch('chatAnswer', form);
    const submitDisabled = !answer && !chatAnswer;

    const hasSubmittedData = (detail?.status as QaStatus) === QaStatus.Submitted;
    const initialAnswer = hasSubmittedData ? detail?.tuningData?.answer : '';
    const initialChatAnswer = hasSubmittedData ? detail?.tuningData?.chatAnswer : '';

    const handleSubmit = useCallback(() => {
        const fieldsValue = form.getFieldsValue();

        setSubmitLoading(true);
        onSubmit(fieldsValue).finally(() => {
            setSubmitLoading(false);
        });
    }, [form, onSubmit]);

    return (
        <Form form={form}>
            <div className="font-normal">
                <div className="mb-3 text-base font-medium">思考路径调优</div>
                <Form.Item initialValue={initialAnswer} className="mb-4 h-[8.125rem] w-[19.5rem]" name="answer">
                    <TextArea
                        // 限制输入 100 个中文字符
                        count={getCountConfig(100, true)}
                        placeholder="描述您希望智能体在收到该问题或类似问题时需遵循的思考路径，如以什么逻辑回答，调用什么工具等"
                        className="h-[8.125rem] rounded-[9px]"
                    />
                </Form.Item>

                <div className="mb-3 text-base font-medium">个性化调优</div>
                <Form.Item initialValue={initialChatAnswer} className="mb-4 h-[8.125rem] w-[19.5rem]" name="chatAnswer">
                    <TextArea
                        count={getCountConfig(100, true)}
                        placeholder="描述您希望智能体在收到该问题或类似问题时的语气偏好、回复格式等"
                        className="h-[8.125rem] rounded-[9px]"
                    />
                </Form.Item>

                <div className="text-right">
                    <Button className="mr-4 font-medium" onClick={onCancel}>
                        取消
                    </Button>
                    <Button
                        className="font-medium"
                        type="primary"
                        disabled={submitDisabled}
                        onClick={handleSubmit}
                        loading={submitLoading}
                    >
                        {submitBtnText}
                    </Button>
                </div>
            </div>
        </Form>
    );
}

export default function TuningButton({
    detail,
    onFinish,
    submitBtnText,
    activeQaStatus,
    tuningTableContainer = null,
    ...props
}: ButtonProps & {
    detail: QaDetail;
    onFinish?: () => void;
    submitBtnText?: string;
    activeQaStatus: TuningQASelectItems;
    tuningTableContainer?: HTMLElement | null;
}) {
    const {appId, appName} = usePromptEditStoreV2(store => ({
        appId: store.agentConfig?.agentInfo?.appId,
        appName: store.agentConfig?.agentInfo?.name,
    }));
    const [form] = Form.useForm<FormValue>();
    const {clickLog, showLog} = useUbcLogV3();
    const [tippyVisible, setTippyVisible] = useState(false);

    const logExt = useMemo(() => {
        return {
            [EVENT_EXT_KEY_CONST.AGENT_ID]: appId ?? '',
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: appName,
            [EVENT_EXT_KEY_CONST.ADJUST_TAB_NAME]: TuningTabLogMap[activeQaStatus],
        } as LJExtData;
    }, [appId, appName, activeQaStatus]);

    const cancelTuning = useCallback(() => {
        // 关闭调优表单
        setTippyVisible(false);
        // 重置数据
        form.resetFields();
    }, [form]);

    const handleCancelBtnClick = useCallback(() => {
        cancelTuning();
        clickLog(EVENT_VALUE_CONST.TUNING_FORM_CANCEL_BTN, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
    }, [clickLog, logExt, cancelTuning]);

    const handleTuningFormShow = useCallback(() => {
        // 调优表单展现打点
        showLog(EVENT_VALUE_CONST.TUNING_FORM, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
    }, [showLog, logExt]);

    const handleClickOutside = useCallback(() => {
        // 点击外部区域关闭调优表单
        cancelTuning();
    }, [cancelTuning]);

    const handleTuningButtonClick = useCallback(() => {
        setTippyVisible(true);
        clickLog(EVENT_VALUE_CONST.TUNING_BTN, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
    }, [logExt, clickLog]);

    const handleSubmitBtnClick = useCallback(
        async (tuningData: FormValue) => {
            clickLog(EVENT_VALUE_CONST.TUNING_FORM_SUBMIT_BTN, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
            // 校验内容是否含有敏感词汇
            const {chatAnswer = '', answer = ''} = tuningData;
            try {
                await Promise.all([
                    ...(chatAnswer ? [auditText(chatAnswer)] : []),
                    ...(answer ? [auditText(answer)] : []),
                ]);
            } catch (error) {
                message.error('内容含敏感词汇，请重新输入');
                return false;
            }

            return updateTuningData({
                appId: detail.appId,
                tuningId: detail.tuningId,
                ...tuningData,
            }).then(
                () => {
                    setTippyVisible(false);
                    onFinish?.();
                    return true;
                },
                () => false
            );
        },
        [detail, onFinish, clickLog, logExt]
    );

    const getTippyContainer = useCallback(
        (ref: Element) => {
            // 为解决调优浮层点开后，滚动表格浮层穿透的问题
            // 获取表格的 body 容器，如果容器可滚动，则将 popover 挂在父节点下
            const tuningTableBody = tuningTableContainer?.querySelector('.ant-table-body');
            if (tuningTableBody && tuningTableBody?.scrollHeight > tuningTableBody?.clientHeight) {
                return ref.parentElement as HTMLElement;
            }

            return document.body;
        },
        [tuningTableContainer]
    );

    return (
        <Tippy
            offset={[-10, 8]}
            visible={tippyVisible}
            interactive
            theme="tuning-form"
            placement="bottom"
            zIndex={99}
            appendTo={getTippyContainer}
            content={
                <TuningForm
                    form={form}
                    submitBtnText={submitBtnText}
                    detail={detail}
                    onCancel={handleCancelBtnClick}
                    onSubmit={handleSubmitBtnClick}
                />
            }
            onShow={handleTuningFormShow}
            onClickOutside={handleClickOutside}
        >
            <Button onClick={handleTuningButtonClick} {...props} />
        </Tippy>
    );
}
