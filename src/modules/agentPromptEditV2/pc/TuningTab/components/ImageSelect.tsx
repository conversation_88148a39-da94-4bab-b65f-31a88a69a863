import classNames from 'classnames';
import styled from '@emotion/styled';
import {useMemo, useState} from 'react';
import Icon from '@/components/Icon';

const ImageSelectContainer = styled.ul`
    display: flex;
    align-items: center;
    gap: 9px;
    background-color: white;
    position: relative;
    .image-select-item {
        width: 100%;

        .select-image-container {
            position: relative;
            height: 60px;
            width: 100%;
            border-width: 2px;
            background-size: cover;
            border-color: transparent;
            border-radius: 8px;
            cursor: pointer;

            .icon-button {
                position: absolute;
                height: 22px;
                width: 22px;
                border-radius: 9999px;
                background-color: white;
                display: flex;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                box-shadow: 0px 1.333px 6.667px 0px rgba(0, 0, 0, 0.1);

                .icon {
                    font-size: 14px;
                }

                &:hover .icon {
                    color: #5562f2;
                }
            }

            .icon-button-left {
                left: 0px;
                top: 50%;
                translate: -50% -50%;
            }

            .icon-button-right {
                right: 0px;
                top: 50%;
                translate: 50% -50%;
            }

            .select-image {
                height: 100%;
                width: 100%;
                border-radius: 6px;
            }
        }

        .image-select-title {
            margin-top: 5px;
            text-align: center;
        }

        &.selected {
            .select-image-container {
                border-color: #5562f2;
            }
            .image-select-title {
                color: #5562f2;
            }
        }
    }
`;

export default function ImageSelect({
    value,
    onChange,
    list,
    className,
    pageSize = 4,
    ...props
}: {
    value?: string;
    onChange?: (value: string) => void;
    list: Array<{key: string; title?: string; link: string}>;
    pageSize?: number;
} & Omit<React.HTMLAttributes<HTMLUListElement>, 'onChange'>) {
    const [page, setPage] = useState(0);
    const [selectedKey, setSelectedKey] = useState<string | undefined>(value);

    // 当外部 value 变化时更新内部状态
    useMemo(() => {
        setSelectedKey(value);
    }, [value]);

    const pageList = useMemo(() => list.slice(page * pageSize, (page + 1) * pageSize), [list, page, pageSize]);

    const handleSelect = (key: string) => {
        setSelectedKey(key);
        onChange?.(key);
    };

    return (
        <ImageSelectContainer className={classNames(className, 'no-scrollbar')} {...props}>
            {pageList.map(({key, link, title}, index) => (
                <li
                    key={key}
                    className={classNames('image-select-item', {
                        'image-select-item-first': index === 0,
                        'image-select-item-last': index === pageSize - 1,
                        selected: key === selectedKey,
                    })}
                    onClick={() => handleSelect(key)}
                >
                    <div className="select-image-container">
                        <img src={link} alt={link} className="select-image" />
                        {page > 0 && index === 0 && (
                            <div
                                onClick={e => {
                                    e.stopPropagation();
                                    setPage(v => v - 1);
                                }}
                                className="icon-button icon-button-left"
                            >
                                <Icon
                                    name="left"
                                    className="icon text-xs group-hover:text-primary"
                                    hoverStyle={false}
                                />
                            </div>
                        )}
                        {(page + 1) * pageSize < list.length && index === pageSize - 1 && (
                            <div
                                onClick={e => {
                                    e.stopPropagation();
                                    setPage(v => v + 1);
                                }}
                                className="icon-button icon-button-right"
                            >
                                <Icon name="right" className="icon" hoverStyle={false} />
                            </div>
                        )}
                    </div>
                    {title && <div className="image-select-title">{title}</div>}
                </li>
            ))}
            {/* 用空白元素填补空白区域，仅用于占位，防止剩余元素不够时被拉伸 */}
            {new Array(pageSize - pageList.length)
                .fill(0)
                .map((_, index) => index)
                .map(key => (
                    <li key={`empty-${key}`} className="image-select-item image-select-item-empty" />
                ))}
        </ImageSelectContainer>
    );
}
