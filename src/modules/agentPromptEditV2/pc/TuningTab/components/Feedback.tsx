import styled from '@emotion/styled';
import {Button, Col, Form, Input, Row, ButtonProps} from 'antd';
import classNames from 'classnames';
import {Key, useCallback, useRef, useState} from 'react';
import loadingPng from '@/assets/loading.png';
import {QaDetail, QaDetailAction} from '@/api/agentEditV2/interface';
import {processQaDetail} from '@/api/agentEditV2';
import Tooltip from './Tooltip';

enum FeedbackReason {
    偏离人设 = '偏离人设',
    语气僵硬 = '语气僵硬',
    答非所问 = '答非所问',
    事实错误 = '事实错误',
    有害信息 = '有害信息',
    没有帮助 = '没有帮助',
}

const options = [
    {value: FeedbackReason.偏离人设},
    {value: FeedbackReason.语气僵硬},
    {value: FeedbackReason.答非所问},
    {value: FeedbackReason.事实错误},
    {value: FeedbackReason.有害信息},
    {value: FeedbackReason.没有帮助},
];

const SelectButtons = <T extends Key>({
    value = [],
    onChange,
    options,
}: {
    value?: T[];
    onChange?: (newValue: T[]) => void;
    options: Array<{label?: React.ReactNode; value: T}>;
}) => {
    const handleSelect = useCallback(
        (newValue: T) => {
            if (value.includes(newValue)) {
                onChange && onChange(value.filter(v => v !== newValue));
            } else {
                onChange && onChange([...value, newValue]);
            }
        },
        [onChange, value]
    );
    return (
        <Row justify="start">
            {options.map(({value: itemValue, label}) => {
                const active = value.includes(itemValue);
                return (
                    <Col span={8} key={itemValue}>
                        <button
                            onClick={() => handleSelect(itemValue)}
                            className={classNames('mb-2 rounded-[.5625rem] px-[1.125rem] py-[.5313rem]', {
                                'bg-[#EBE9FF] text-[#5240FF]': active,
                                'bg-[#F5F6F9]': !active,
                            })}
                        >
                            {label || itemValue}
                        </button>
                    </Col>
                );
            })}
        </Row>
    );
};

const TextArea = styled(Input.TextArea)`
    resize: none !important;
    border: none !important;
    background-color: #f5f6f9 !important;
    &:focus-visible {
        outline: none !important;
    }
`;

interface FormValue {
    reasonList: FeedbackReason[];
    idealAnswer: string;
}

interface Props {
    className?: string;
    onFinish: (values: FormValue) => Promise<boolean>;
    onCancel: () => void;
}

function FeedbackForm({className, onFinish, onCancel}: Props) {
    const [form] = Form.useForm<FormValue>();
    const [loading, setLoading] = useState(false);
    const handleSubmit = useCallback(() => {
        const fieldsValue = form.getFieldsValue();
        setLoading(true);
        onFinish(fieldsValue).then(() => {
            setLoading(false);
        });
    }, [form, onFinish]);
    return (
        <Form<FormValue> form={form} className={classNames('max-w-[19.5rem] px-2', className)}>
            <p className="mb-3 text-base font-medium">反馈原因</p>
            <Form.Item className="mb-4" name="reasonList">
                <SelectButtons options={options} />
            </Form.Item>
            <p className="mb-3 text-base font-medium">理想回答</p>
            <Form.Item className="mb-4" name="idealAnswer">
                <TextArea
                    placeholder="请针对问题，输入您认为的理想回答，我们将学习您的回答风格，优化智能体配置"
                    className="h-[14.375rem]"
                />
            </Form.Item>
            <div className="text-right">
                <button
                    className="mr-4 rounded-3xl border-none bg-[#F7F7FE] px-[1.1875rem] py-[.375rem] font-medium"
                    onClick={onCancel}
                >
                    取消
                </button>
                <button
                    className="rounded-3xl border-none bg-[#EBE9FF] px-[1.1875rem] py-[.375rem] font-medium text-[#5240FF]"
                    onClick={handleSubmit}
                >
                    {loading ? <img src={loadingPng} className="anticon-spin h-4 w-4" /> : '提交'}
                </button>
            </div>
        </Form>
    );
}

export default function FeedbackButton({
    detail,
    onFinish,
    ...props
}: ButtonProps & {
    detail: QaDetail;
    onFinish?: (values: FormValue) => void;
}) {
    const feedbackButtonRef = useRef<HTMLButtonElement>(null);
    const handleCancelFeedback = useCallback(() => {
        feedbackButtonRef.current?.click();
    }, []);
    const onFinishFeedback = useCallback(
        (actionData: FormValue) =>
            processQaDetail({
                appId: detail.appId,
                tuningIds: [detail.tuningId],
                action: QaDetailAction.Feedback,
                actionData,
            }).then(
                () => {
                    feedbackButtonRef.current?.click();
                    onFinish && onFinish(actionData);
                    return true;
                },
                () => false
            ),
        [detail, onFinish]
    );
    return (
        <Tooltip title={<FeedbackForm onCancel={handleCancelFeedback} onFinish={onFinishFeedback} />} trigger="click">
            <Button ref={feedbackButtonRef} {...props} />
        </Tooltip>
    );
}
