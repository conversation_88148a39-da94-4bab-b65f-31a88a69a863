import {useSearchParams} from 'react-router-dom';
import {useCallback, useMemo, useEffect} from 'react';
import {Form, Modal, Button, Select, message} from 'antd';
import styled from '@emotion/styled';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useExtLog} from '@/modules/agentPromptEditV2/pc/TuningTab/hooks/useExtLog';
import {submitVideoHosting, cancelVideoHosting} from '@/api/videoGenerate';
import {HostingSelectOptions} from '@/modules/agentPromptEditV2/pc/TuningTab/constant';
import {useEduVideoHostingContextProvider} from '../eduVideoHostingConfirm/context';

const StyledForm = styled(Form)`
    .ant-form-item-row .ant-form-item-control .ant-form-item-control-input-content {
        display: flex !important;
        justify-content: flex-end !important;
    }
`;

export default function VideoHostingModal({
    openHosting,
    setOpenHosting,
    quantity, // 托管数量 会出现 0 的情况
    agreement = true,
}: {
    agreement: boolean;
    openHosting: boolean;
    setOpenHosting: (openHosting: boolean) => void;
    quantity: number;
}) {
    const logExt = useExtLog();
    const {clickLog} = useUbcLogV3();

    const {fetchData} = useEduVideoHostingContextProvider();
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;
    const [form] = Form.useForm();

    const values = Form.useWatch([], form);

    const highestValue = useMemo(() => {
        return HostingSelectOptions.reduce((max, option) => {
            return option.value > max ? option.value : max;
        }, 0);
    }, []);

    useEffect(() => {
        if (openHosting) {
            form.setFieldsValue({quantity: highestValue});
        }
    }, [openHosting, highestValue, form]);

    const handleOk = useCallback(async () => {
        const {quantity} = values;
        // 检查是否已经同意形象授权，如果已经同意，则提交视频生产托管配置；否则，展示百家号授权弹窗
        try {
            // 提交视频生产托管配置；前提是托管配置接口调用成功后，关闭当前托管配置弹窗；
            await submitVideoHosting({
                appId,
                quantity,
                agreement,
            });
            // 提交成功后，关闭当前托管配置弹窗；
            setOpenHosting(false);
            message.success('已授权');

            fetchData();
        } catch (error) {
            console.error(error);
        }

        clickLog(EVENT_VALUE_CONST.CONFIRM_AUTHORIZATION, {
            ...logExt,
            eHostAuthorizationNumber: quantity,
        });
    }, [agreement, appId, clickLog, fetchData, logExt, setOpenHosting, values]);

    const handleCancel = useCallback(async () => {
        // 取消视频生产托管配置；前提若是之前同意过授权，则不调用cancelVideoHosting 接口；反之 若是之前从未授权托管，那么便调用cancelVideoHosting 接口进行短信通知；
        try {
            if (quantity === 0) {
                await cancelVideoHosting({
                    appId,
                });
            }
        } catch (error) {
            console.error(error);
        }

        message.success('已取消');
        setOpenHosting(false);

        clickLog(EVENT_VALUE_CONST.HOST_AUTHORIZATION_CANCEL);
    }, [appId, clickLog, quantity, setOpenHosting]);

    const isButtonDisabled = useMemo(() => {
        return !values?.quantity;
    }, [values?.quantity]);

    const filteredOptions = useMemo(() => {
        return HostingSelectOptions.map(option => {
            return {
                ...option,
                disabled: option.value <= quantity,
            };
        });
    }, [quantity]);

    return (
        <Modal
            centered
            forceRender
            width={400}
            open={openHosting}
            closeIcon={false}
            maskClosable={false}
            title={<div className="font-pingfang text-lg font-medium leading-6">托管数量</div>}
            onOk={handleOk}
            onCancel={handleCancel}
            footer={[
                <Button key="cancel" onClick={handleCancel} className="h-[30px] w-[58px] rounded-full p-0">
                    取消
                </Button>,
                <Button
                    key="ok"
                    type="primary"
                    onClick={handleOk}
                    disabled={isButtonDisabled}
                    className="h-[30px] w-[86px] rounded-full p-0"
                >
                    确认授权
                </Button>,
            ]}
        >
            <div className="mb-[18px] text-sm text-gray-tertiary">
                确认后，后续平台会根据您选择托配额，自动结合大模型训练合成脚本，制作生成数字人视频并发布。您可随时在已确权栏目中查看和管理已发布的视频内容
            </div>
            <StyledForm form={form} labelAlign="left" colon={false}>
                <Form.Item
                    label={
                        <div className="flex items-center">
                            <span className="text-sm">授权托管数量</span>
                        </div>
                    }
                    name="quantity"
                    className="mb-[18px]"
                >
                    <Select options={filteredOptions} placeholder="请选择托管数量" className="w-[244px]" />
                </Form.Item>
            </StyledForm>
        </Modal>
    );
}
