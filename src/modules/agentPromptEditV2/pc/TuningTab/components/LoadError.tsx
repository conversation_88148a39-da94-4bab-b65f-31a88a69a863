import {useCallback} from 'react';
import {Button} from 'antd';
import classNames from 'classnames';
import {useNavigate} from 'react-router-dom';
import urls from '@/links';

export default function LoadError({
    tips,
    hideBtn,
    btnText,
    className,
    onBtnClick,
    btnAction = 'refresh',
}: {
    tips?: string;
    hideBtn?: boolean;
    btnText?: string;
    className?: string;
    onBtnClick?: () => void;
    btnAction?: 'refresh' | 'center';
}) {
    const navigate = useNavigate();

    const handleClick = useCallback(() => {
        if (btnAction === 'center') {
            navigate(urls.center.raw(), {replace: true});
        }

        window.location.reload();
    }, [btnAction, navigate]);

    return (
        <div className={classNames('flex w-full items-center justify-center py-8', className)}>
            <div className="flex flex-col items-center">
                <span className="iconfont icon-exclamationcircle h-[120px] w-[120px] text-[120px] leading-none text-[#ddddf2] opacity-60" />
                <div className="mt-8 text-sm text-gray-tertiary">{tips || '加载失败，请稍后重试'}</div>
                {!hideBtn && (
                    <Button type="primary" className="mt-3 rounded-[100px]" onClick={onBtnClick || handleClick}>
                        {btnText || '重新加载'}
                    </Button>
                )}
            </div>
        </div>
    );
}
