import {useCallback, useMemo, useRef} from 'react';
import {message} from 'antd';
import {BJHAccountStatus, BJHStatusCode} from '@/api/business/interface';
import {useBJHAccountState} from '@/modules/agentPromptEditV2/pc/components/Business/goods/useBJHAccountState';
import {useConfirmContext} from './ConfirmContext';
import {BjhAccountContext} from './BjhAccountContext';

export default function BjhAccountContextProvider({children}: {children: React.ReactNode}) {
    // 百家号账号状态
    const {bjhState, createBJHAccount} = useBJHAccountState(false);
    const {showConfirmModal} = useConfirmContext();

    const openAccountErrorModal = useCallback(
        (text?: React.ReactNode) =>
            showConfirmModal({
                content: (
                    <div className="flex">
                        <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-[#FF8200]"></span>
                        {text || (
                            <div className="text-[14px] leading-[22px]">
                                账号异常，请前往
                                <a
                                    href="https://baijiahao.baidu.com/builder/rc/settings/accountSet"
                                    target="_blank"
                                    rel="noreferrer"
                                    className="text-primary"
                                >
                                    百家号官网
                                </a>
                                处理
                            </div>
                        )}
                    </div>
                ),
            }),
        [showConfirmModal]
    );

    const isBjhRegistered = useCallback(
        (currentBJHState: BJHAccountStatus) => {
            if (
                currentBJHState.bidStatus === BJHStatusCode.Banned ||
                currentBJHState.bidStatus === BJHStatusCode.Deactivated
            ) {
                // 百家号账号异常(封禁/注销)
                openAccountErrorModal();

                return false;
            } else if (currentBJHState.bidStatus === BJHStatusCode.Unregistered) {
                return false;
            }
            return true;
        },
        [openAccountErrorModal]
    );

    const openCreateModal = useCallback(
        () =>
            showConfirmModal({
                content: (
                    <div className="flex">
                        <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-primary"></span>
                        <div className="text-[14px] leading-[22px]">
                            生成数字人视频需开通百家号账号，同时生成的视频将会同步在百家号发布。确认开通代表您同意
                            <a
                                href="https://baijiahao.baidu.com/docs/#/argument/BaiJiaHaoFuWuXieYiZaiYao/"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                《百家号平台服务协议》
                            </a>
                        </div>
                    </div>
                ),
                onCancel: () => {
                    checkBjhAccountCallbackRef.current?.resolve(false);
                },
                onConfirm: async () => {
                    try {
                        const bjhState = await createBJHAccount();
                        message.success('百家号账号已开通');

                        if (bjhState && isBjhRegistered(bjhState)) {
                            checkBjhAccountCallbackRef.current?.resolve(true);
                            return true;
                        }
                    } catch (error) {
                        checkBjhAccountCallbackRef.current?.reject(error);
                        console.error(error);
                    }

                    checkBjhAccountCallbackRef.current?.resolve(false);
                    return false;
                },
            }),
        [createBJHAccount, isBjhRegistered, showConfirmModal]
    );

    const checkBjhAccountCallbackRef = useRef<{
        resolve: (value: boolean | PromiseLike<boolean>) => void;
        reject: (reason?: any) => void;
    } | null>(null);

    const bjhStateRef = useRef(bjhState);
    bjhStateRef.current = bjhState;
    const checkBjhAccount = useCallback(async () => {
        const bjhState = bjhStateRef.current;
        if (!bjhState) {
            return false;
        }

        // 百家号账号异常(封禁/注销)
        if (bjhState.bidStatus === BJHStatusCode.Banned || bjhState.bidStatus === BJHStatusCode.Deactivated) {
            openAccountErrorModal();
            return false;
        }

        // 未注册，注册弹窗
        if (bjhState.bidStatus === BJHStatusCode.Unregistered) {
            openCreateModal();
            return new Promise<boolean>((resolve, reject) => {
                checkBjhAccountCallbackRef.current = {resolve, reject};
            });
        }

        return true;
    }, [openAccountErrorModal, openCreateModal]);

    const contextValue = useMemo(
        () => ({
            checkBjhAccount,
        }),
        [checkBjhAccount]
    );

    return <BjhAccountContext.Provider value={contextValue}>{children}</BjhAccountContext.Provider>;
}
