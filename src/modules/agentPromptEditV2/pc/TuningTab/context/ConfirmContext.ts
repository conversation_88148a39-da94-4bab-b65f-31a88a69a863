/**
 * @file 确认弹窗上下文
 * <AUTHOR>
 */

import {createContext, useContext, ReactNode} from 'react';

export interface ModalProps {
    content: ReactNode;
    title?: ReactNode;
    onConfirm?: () => void;
    onCancel?: () => void;
}

export interface ConfirmContextProps {
    showConfirmModal: (props: ModalProps) => void;
}

export const ConfirmContext = createContext<ConfirmContextProps>({
    showConfirmModal: () => {
        throw new Error('未找到 ConfirmModalContextProvider');
    },
});

export const useConfirmContext = () => useContext(ConfirmContext);
