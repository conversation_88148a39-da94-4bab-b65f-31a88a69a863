/**
 * @file 确认弹窗上下文
 * <AUTHOR>
 */

import {ReactNode, useCallback, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {Button, Modal} from 'antd';
import {ConfirmContext, ConfirmContextProps, ModalProps} from './ConfirmContext';

export const StyleModal = styled(Modal)`
    .ant-modal-header {
        margin-bottom: 3px !important;
    }
    .ant-modal-content {
        padding: 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

export default function ConfirmProvider({children}: {children: ReactNode}) {
    const [modalConfig, setModalConfig] = useState<ModalProps | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const showConfirmModal = useCallback((config: ModalProps) => {
        setModalConfig(config);
        setIsOpen(true);
    }, []);

    const close = useCallback(() => {
        modalConfig?.onCancel && modalConfig?.onCancel();
        setIsOpen(false);
    }, [modalConfig]);

    const confirm = useCallback(() => {
        modalConfig?.onConfirm && modalConfig?.onConfirm();
        setIsOpen(false);
    }, [modalConfig]);

    const contextValue: ConfirmContextProps = useMemo(() => ({showConfirmModal}), [showConfirmModal]);

    return (
        <ConfirmContext.Provider value={contextValue}>
            {children}
            <StyleModal
                centered
                width={400}
                forceRender
                open={isOpen}
                closeIcon={false}
                zIndex={1003}
                title={modalConfig?.title}
                footer={
                    <>
                        <Button
                            type="default"
                            shape="round"
                            className="h-[30px] border-[#EDEEF0] px-[15px] py-1 font-medium leading-[20px] hover:text-gray-tertiary"
                            onClick={close}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            shape="round"
                            className="h-[30px] px-[15px] py-1 font-medium leading-[20px]"
                            onClick={confirm}
                        >
                            确认
                        </Button>
                    </>
                }
                onCancel={close}
            >
                {modalConfig?.content}
            </StyleModal>
        </ConfirmContext.Provider>
    );
}
