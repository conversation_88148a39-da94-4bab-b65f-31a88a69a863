import {UIE<PERSON>, useCallback, useMemo} from 'react';
import {<PERSON><PERSON>, ConfigProvider, Flex} from 'antd';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {reachBottom} from '@/modules/agentList/utils';
import {AllQ2CSubmitTypes, Q2CStateKey, QAListTabTitle} from '../../constant';
import {useQ2CList} from '../../hooks/useQ2CList';
import {StyledListTabs} from '../../styles';
import {QAList} from './QAList';

export default function QAListContainer({
    statusList,
    totalTraffic,
}: {
    statusList: Q2CStateKey[];
    totalTraffic?: number;
}) {
    const {currentTab, q2cList, couldRequestMore, requesting, q2cPageNo, setQ2CPageNo, setCurrentTab} = useQ2CStore(
        store => ({
            currentTab: store.currentTab,
            q2cList: store.q2cList,
            couldRequestMore: store.couldRequestMore,
            requesting: store.requesting,
            q2cPageNo: store.q2cPageNo,
            setQ2CPageNo: store.setQ2CPageNo,
            setCurrentTab: store.setCurrentTab,
        })
    );

    const {getQ2CList} = useQ2CList();

    const handleScroll = useCallback(
        (event: UIEvent<HTMLDivElement>) => {
            if (requesting[currentTab] || !reachBottom(event.currentTarget) || !couldRequestMore[currentTab]) {
                return;
            }

            try {
                getQ2CList(q2cList[currentTab], currentTab);
                setQ2CPageNo(q2cPageNo[currentTab] + 1, currentTab);
            } catch (e) {
                console.error(e);
            }
        },
        [couldRequestMore, currentTab, getQ2CList, q2cList, q2cPageNo, requesting, setQ2CPageNo]
    );

    const handleTabChange = useCallback(
        (key: string) => {
            const q2cStateKey = key as Q2CStateKey;
            setCurrentTab(q2cStateKey);
            if (requesting[q2cStateKey] && q2cList[q2cStateKey].length === 0) {
                getQ2CList([], q2cStateKey);
            }
        },
        [getQ2CList, q2cList, requesting, setCurrentTab]
    );

    const tabItems = useMemo(() => {
        const res = [];
        for (const key of statusList) {
            res.push({
                key,
                label: (
                    <Badge
                        status="error"
                        dot={key === Q2CStateKey.SubmittedAuditFail && q2cList[key].length > 0}
                        offset={[0, 3]}
                    >
                        <div>{QAListTabTitle[key]}</div>
                    </Badge>
                ),
                children: (
                    <QAList
                        data={q2cList[currentTab]}
                        handleScroll={handleScroll}
                        couldRequestMore={couldRequestMore[currentTab]}
                        requesting={requesting[currentTab]}
                    />
                ),
            });
        }
        return res;
    }, [couldRequestMore, currentTab, handleScroll, q2cList, requesting, statusList]);

    /**
     * Tabs 旁边的文案
     */
    const textBesideTabs = useMemo(() => {
        if (AllQ2CSubmitTypes.includes(currentTab) && q2cList[currentTab].length > 0) {
            return (
                <>
                    <span className="mr-1 text-[24px]">🎉 </span>提交的调优问答已获取{totalTraffic}流量～
                </>
            );
        }
        return null;
    }, [currentTab, totalTraffic, q2cList]);

    return (
        <div className="relative h-full pt-4">
            {statusList.length === 1 ? (
                <QAList
                    data={q2cList[currentTab]}
                    handleScroll={handleScroll}
                    couldRequestMore={couldRequestMore[currentTab]}
                    requesting={requesting[currentTab]}
                />
            ) : (
                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {
                                itemColor: '#1E1F24',
                                titleFontSizeLG: 14,
                                horizontalItemPaddingLG: '0 0 5px 0',
                                horizontalItemGutter: 24,
                            },
                        },
                    }}
                >
                    <StyledListTabs
                        defaultActiveKey={statusList[0]}
                        items={tabItems}
                        size="large"
                        onChange={handleTabChange}
                        destroyInactiveTabPane
                    />
                    {textBesideTabs && (
                        <Flex className="absolute right-6 top-4 text-gray-secondary" align="center">
                            {textBesideTabs}
                        </Flex>
                    )}
                </ConfigProvider>
            )}
        </div>
    );
}
