/**
 * @file editor状态管理hook，编辑器内容及编辑态由 q2cStore 统一管理
 * <AUTHOR>
 */

import {useCallback, useState} from 'react';
import {message} from 'antd';
import dayjs from 'dayjs';
import {useSearchParams} from 'react-router-dom';
import {JSONContent} from '@tiptap/core';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {getBjhVideoStatus, saveTuning, submitTuning} from '@/api/q2c';
import {QA, QAStatus, SubmitStatus} from '@/api/q2c/interface';
import {useQ2CList} from '../../hooks/useQ2CList';
import {sanitizeContent, containsNonMediaUrl, extractVideoContent, mlVideoMarkdownRegex} from '../../utils';
import {VideoAuditStatus} from '../../constant';

interface EditorHookProps {
    errorText: string;
    saveTime: string;
    setErrorText: (errorText: string) => void;
    setSaveTime: (saveTime: string) => void;
    saveContent: (showMessage?: boolean, currentState?: {content: string; jsonContent: JSONContent}) => void;
    submitContent: () => Promise<string | undefined>;
}

export interface TiptapEditorProps extends EditorHookProps {
    tips?: string;
    hovered: boolean;
}

export default function useTiptapEditor({qaData}: {qaData: QA}): EditorHookProps {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;
    const {fetchAllQ2CList} = useQ2CList();

    const tuningId = qaData.tuningId;

    const {editorState, updateIsEditing} = useQ2CStore(store => ({
        editorState: store.editorStates[qaData.tuningId],
        updateIsEditing: store.updateIsEditing,
    }));

    // 错误态文案
    const [errorText, setErrorText] = useState('');
    // 保存时间
    const [saveTime, setSaveTime] = useState('');

    const getEditorContent = useCallback((content: string) => {
        const nonMlVideoContent = content.replace(mlVideoMarkdownRegex, '');

        if (content === '') {
            setErrorText('内容不能为空');
            return '';
        }

        if (containsNonMediaUrl(nonMlVideoContent)) {
            setErrorText('编辑器暂不支持添加链接，请删除后提交');
            return '';
        }
        return content;
    }, []);
    const saveContent = useCallback(
        (showMessage = false, currentState?: {content: string; jsonContent: JSONContent}) => {
            const editorContent = getEditorContent(currentState?.content || editorState?.content || '');
            const editorJsonContent = currentState?.jsonContent || editorState?.jsonContent || {};
            editorContent !== '' &&
                saveTuning({
                    appId,
                    tuningId,
                    answer: sanitizeContent(editorContent),
                    question: qaData.question,
                    submit: qaData?.status === QAStatus.Submitted ? SubmitStatus.Submit : SubmitStatus.Unsubmit,
                    videoContent: extractVideoContent(editorJsonContent),
                })
                    .then(() => {
                        setSaveTime(dayjs().format('HH:mm'));
                        showMessage && message.success({content: '保存成功', duration: 5});
                    })
                    .catch(error => {
                        setErrorText(error.msg);
                    });
        },
        [
            getEditorContent,
            editorState?.content,
            editorState?.jsonContent,
            appId,
            tuningId,
            qaData.question,
            qaData?.status,
        ]
    );

    // 内容提交前，删除审核失败的视频；修改后的内容会返回，由调用处进行更新
    const submitContent = useCallback(() => {
        let editorContent = getEditorContent(editorState?.content || '');
        let videoContent = extractVideoContent(editorState?.jsonContent || {});

        const submitTuningToServer = () => {
            submitTuning({
                appId,
                tuningId,
                answer: sanitizeContent(editorContent),
                question: qaData.question,
                videoContent,
            })
                .then(() => {
                    message.success({content: '提交成功', duration: 5});
                    fetchAllQ2CList();
                    updateIsEditing(false, tuningId);
                })
                .catch(error => {
                    setErrorText(error.msg);
                });
        };

        return getBjhVideoStatus({tuningId}).then(res => {
            if (res.videoStatuses.some(video => video.status === VideoAuditStatus.AuditFail)) {
                editorContent = editorContent.replace(mlVideoMarkdownRegex, '');
                videoContent = [];
                if (editorContent === '') {
                    message.error('视频审核不通过，请重新上传');
                } else {
                    message.error('视频审核不通过，已自动删除');
                    submitTuningToServer();
                }
                return editorContent;
            }

            editorContent !== '' && submitTuningToServer();
        });
    }, [
        getEditorContent,
        editorState?.content,
        editorState?.jsonContent,
        tuningId,
        appId,
        qaData.question,
        fetchAllQ2CList,
        updateIsEditing,
    ]);

    return {
        errorText,
        saveTime,
        setErrorText,
        setSaveTime,
        saveContent,
        submitContent,
    };
}
