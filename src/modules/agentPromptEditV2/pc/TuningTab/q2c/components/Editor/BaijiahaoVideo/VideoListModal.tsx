/**
 * @file 添加视频-百家号视频列表弹窗
 * <AUTHOR>
 */

import {Button, ConfigProvider, Dropdown, MenuProps, message, Modal, Upload} from 'antd';
import React, {ChangeEvent, useCallback, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import {UploadChangeParam, UploadFile} from 'antd/es/upload';
import {Editor} from '@tiptap/core';
import classnames from 'classnames';
import Search from '@/components/Input/Search';
import ThemeConfig from '@/styles/lingjing-light-theme';
import uploadIcon from '@/assets/dataset/dataset-local-upload.png';
import {BjhVideoDetail, BjhVideoListParams} from '@/api/q2c/interface';
import {getBjhVideoInfo, getBjhVideoList, submitVideoCover} from '@/api/q2c';
import {usePagination} from '@/utils/usePagination';
import Scrollbar from '@/components/Scrollbar';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import Loading from '@/components/Loading';
import {DynamicFigureStatus} from '@/api/agentEdit/interface';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {getPopupContainer} from '@/utils/getPopupContainer';
import {deleteVideo} from '@/api/q2c';
import {formatVideoDuration} from '../../../utils';
import {useDigitalImageVideoContext} from '../../../context/DigitalImageVideoContext';
import VideoTag from '../VideoTag';
import {OverlayType} from '../constant';
import {useBjhVideoContext} from '../../../context/BjhVideoContext';
import {VideoAuditStatus, VideoType} from '../../../constant';
import {useConfirmContext} from '../../../../context/ConfirmContext';
import {useQ2CList} from '../../../hooks/useQ2CList';
import {VideoAction} from '../constant';
import UploadVideoModal from './UploadVideoModal';

interface VideoListModalProps {
    editor?: Editor;
    open: boolean;
    setOpen: (value: boolean) => void;
    setOverlayType?: (overlayType: OverlayType) => void;
}

interface VideoListParams extends BjhVideoListParams {
    pageNo: number;
}

const StyledModal = styled(Modal)`
    .ant-modal-header {
        margin-bottom: 12px !important;
    }
    .ant-modal-content {
        padding: 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

export default function VideoListModal({open, setOpen, editor, setOverlayType}: VideoListModalProps) {
    const {currentQA, changeVideoCover} = useBjhVideoContext();
    const {clickLog} = useUbcLogV3();
    const {fetchAllQ2CList} = useQ2CList();

    const [selectedVideo, setSelectedVideo] = useState<BjhVideoDetail>();

    // 分页
    const {
        list: videoList,
        setParams,
        setList,
        loading,
        total,
    } = usePagination<VideoListParams, BjhVideoDetail>({
        initParams: {
            pageNo: 1,
            pageSize: 14,
            search: '',
        },
        getList: async (params: BjhVideoListParams) => {
            if (!open) {
                return {total: 0, list: []};
            }

            const res = await getBjhVideoList(params);
            return {total: res.total, list: res.dataList};
        },
    });

    useEffect(() => {
        setParams({
            pageNo: 1,
            pageSize: 14,
            search: '',
        });
    }, [open, setParams]);

    // 本地上传
    const [openUploadVideoModal, setOpenUploadVideoModal] = useState(false);
    const [uploadVideoFile, setUploadVideoFile] = useState<File>();
    const handleUploadChange = useCallback(
        (info: UploadChangeParam<UploadFile>) => {
            /** B-37 上传视频弹窗-上传视频点击 */
            clickLog(EVENT_VALUE_CONST.VIDEO_BOX_UPLOAD);
            setUploadVideoFile(info.file.originFileObj);
            setOpenUploadVideoModal(true);
            setOpen(false);
        },
        [clickLog, setOpen]
    );

    // 关键词搜索
    const [search, setSearch] = useState('');
    const handleKeywordChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        setSearch(e.target.value);
    }, []);
    const handleSearch = useCallback(() => {
        setParams(prev => ({
            ...prev,
            search,
            pageNo: 1,
        }));
        setSelectedVideo(undefined);
    }, [search, setParams]);

    const handleReachBottom = useCallback(() => {
        if (videoList.length < total && !loading) {
            setParams(prev => ({...prev, pageNo: prev.pageNo + 1}));
        }
    }, [loading, videoList.length, setParams, total]);

    const handleClose = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    const handleVideoClick = useCallback((video: BjhVideoDetail) => {
        setSelectedVideo(video);
    }, []);

    const [coverLoading, setCoverLoading] = useState(false);
    const {changeDigitalVideoCover} = useDigitalImageVideoContext();
    const {dynamicFigure} = usePromptEditStoreV2(store => ({
        dynamicFigure: store.agentConfig.agentInfo.dynamicFigure,
    }));
    const handleAddClick = useCallback(() => {
        /** B-38 上传视频弹窗-选中后添加 */
        clickLog(EVENT_VALUE_CONST.VIDEO_BOX_ADD, {
            eIsUploaded: 0,
        });

        if (!selectedVideo || !currentQA || !editor || !setOverlayType) return;

        // 数字人视频打开数字人视频编辑弹窗
        if (selectedVideo.videoType === VideoType.DigitalHuman && selectedVideo.videoId) {
            if (dynamicFigure?.status !== DynamicFigureStatus.VIDEO_MAKING_DONE) {
                setOpen(false);
                setOverlayType(OverlayType.VideoInfo);
                getBjhVideoInfo({
                    tuningId: currentQA.tuningId,
                    downloadUrl: selectedVideo.videoUrl,
                    nid: selectedVideo.nid,
                    title: selectedVideo.title,
                })
                    .then(({videoUrl, duration, height, width, videoId, coverUrl}) => {
                        editor
                            .chain()
                            .focus()
                            .setMlVideo({
                                src: videoUrl,
                                duration,
                                poster: coverUrl,
                                videoId,
                                auditStatus: selectedVideo.status,
                                height,
                                width,
                                title: selectedVideo.title,
                                videoType: VideoType.DigitalHuman,
                            })
                            .run();
                        setOverlayType(OverlayType.None);
                    })
                    .catch(() => {
                        setOverlayType(OverlayType.None);
                    });
                return;
            }

            setCoverLoading(true);
            changeDigitalVideoCover({
                videoId: selectedVideo.videoId,
                QA: currentQA,
                async onConfirm(props) {
                    try {
                        // 将新的封面图提交给server
                        await submitVideoCover({tuningId: currentQA.tuningId, coverUrl: props.coverUrl});
                        setOpen(false);
                        setOverlayType(OverlayType.VideoInfo);
                        getBjhVideoInfo({
                            tuningId: currentQA.tuningId,
                            downloadUrl: selectedVideo.videoUrl,
                            nid: selectedVideo.nid,
                            title: selectedVideo.title,
                        })
                            .then(({videoUrl, duration, height, width, videoId}) => {
                                editor
                                    .chain()
                                    .focus()
                                    .setMlVideo({
                                        src: videoUrl,
                                        duration,
                                        poster: props.coverUrl,
                                        videoId,
                                        auditStatus: selectedVideo.status,
                                        height,
                                        width,
                                        title: selectedVideo.title,
                                        videoType: VideoType.DigitalHuman,
                                    })
                                    .run();
                                setOverlayType(OverlayType.None);
                                setCoverLoading(false);
                            })
                            .catch(() => {
                                setOverlayType(OverlayType.None);
                                setCoverLoading(false);
                            });

                        return true;
                    } catch (error) {
                        console.error(error);
                        setCoverLoading(false);
                        return false;
                    }
                },
                onCancel() {
                    setCoverLoading(false);
                },
            });
            return;
        }

        setCoverLoading(true);
        getBjhVideoInfo({
            tuningId: currentQA.tuningId,
            downloadUrl: selectedVideo.videoUrl,
            nid: selectedVideo.nid,
            title: selectedVideo.title,
        })
            .then(({videoUrl, duration, height, width, videoId}) => {
                // 普通视频打开封面编辑弹窗，由于已经有了 currentQA 这里不需要再传入
                changeVideoCover({
                    videoUrl: videoUrl,
                    async onConfirm(newCoverUrl) {
                        try {
                            // 将新的封面图提交给server
                            await submitVideoCover({tuningId: currentQA.tuningId, coverUrl: newCoverUrl});
                            setOpen(false);
                            setOverlayType(OverlayType.VideoInfo);
                            editor
                                .chain()
                                .focus()
                                .setMlVideo({
                                    src: videoUrl,
                                    duration,
                                    poster: newCoverUrl,
                                    videoId,
                                    auditStatus: selectedVideo.status,
                                    height,
                                    width,
                                    title: selectedVideo.title,
                                })
                                .run();
                            setOverlayType(OverlayType.None);
                            setCoverLoading(false);
                        } catch (error) {
                            return false;
                        }
                        return true;
                    },
                    onCancel() {
                        setCoverLoading(false);
                    },
                });
            })
            .catch(() => {
                setOverlayType(OverlayType.None);
                setCoverLoading(false);
            });
    }, [
        clickLog,
        selectedVideo,
        currentQA,
        editor,
        setOverlayType,
        dynamicFigure,
        changeDigitalVideoCover,
        setOpen,
        changeVideoCover,
    ]);

    // 删除并下架
    const deleteAndRemoveVideo = useCallback(
        async (nid: string, videoId?: number) => {
            try {
                await deleteVideo({nid: nid, videoId: videoId});
                // server数据库同步延迟，在删除成功后前端移除
                setList(list => list.filter(info => info.nid !== nid));
                // 刷新Q2C列表
                fetchAllQ2CList();
                message.success('删除成功');
            } catch (error: any) {
                message.error(error?.msg || '删除失败');
            }
        },
        [fetchAllQ2CList, setList]
    );

    const {showConfirmModal} = useConfirmContext();

    const openDeleteModal = useCallback(
        (nid: string, videoId?: number) => {
            showConfirmModal({
                content: (
                    <div className="flex">
                        <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-[#FF8200]"></span>
                        <div className="text-[14px] leading-[22px]">
                            删除视频后，将同步在所有搜索问答和百家号中删除此条视频。视频将不可恢复，确认删除并下架吗？
                        </div>
                    </div>
                ),
                onConfirm: () => deleteAndRemoveVideo(nid, videoId),
            });
        },
        [deleteAndRemoveVideo, showConfirmModal]
    );

    const getVideoItems = (video: BjhVideoDetail) => {
        const videoItems: MenuProps['items'] = [
            {
                key: VideoAction.DeleteAndUnlist,
                label: '删除并下架',
                onClick: () => openDeleteModal(video.nid, video?.videoId),
            },
        ];
        return video.status === VideoAuditStatus.Auditing ? [] : videoItems;
    };

    return (
        <>
            <StyledModal
                centered
                title="添加视频"
                width={804}
                forceRender
                open={open}
                footer={
                    <div className="flex items-center justify-between">
                        <div className="text-[12px] leading-[12px] text-gray-tertiary">
                            上传的视频将同步在百家号发布
                        </div>
                        <div className="flex gap-[6px]">
                            <Button
                                type="default"
                                shape="round"
                                className="h-[30px] border-[#EDEEF0] px-[15px] py-1 font-medium leading-[20px] hover:text-gray-tertiary"
                                onClick={handleClose}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                shape="round"
                                className="h-[30px] px-[15px] py-1 font-medium leading-[20px]"
                                disabled={
                                    selectedVideo === undefined ||
                                    selectedVideo.status === VideoAuditStatus.AuditFail ||
                                    loading
                                }
                                onClick={handleAddClick}
                                loading={coverLoading}
                            >
                                添加
                            </Button>
                        </div>
                    </div>
                }
                onCancel={handleClose}
            >
                <div>
                    <div className="flex justify-end gap-[9px]">
                        <ConfigProvider
                            theme={{
                                token: {
                                    lineWidth: 0,
                                    colorBgContainer: '#F5F6FA',
                                },
                                components: {
                                    Select: {
                                        controlHeight: 36,
                                        optionSelectedColor: ThemeConfig.token.colorPrimary,
                                        optionSelectedBg: 'transparent',
                                        optionSelectedFontWeight: 400,
                                        controlItemBgHover: '#F5F6FA',
                                    },
                                },
                            }}
                        >
                            <Search
                                className="w-[300px]"
                                placeholder="视频名称"
                                value={search}
                                onChange={handleKeywordChange}
                                onSearch={handleSearch}
                                disabled={loading}
                            />
                        </ConfigProvider>
                    </div>
                    <Scrollbar
                        className={`mt-3 ${
                            videoList.length === 0 && !loading ? 'h-[200px]' : 'h-[400px] overflow-y-auto'
                        } rounded-[6px]`}
                        onReachBottom={handleReachBottom}
                        reachOffset={100}
                    >
                        <div className="flex flex-wrap gap-3">
                            {/* 视频上传入口 */}
                            {!(loading && videoList.length === 0) && (
                                <Upload
                                    accept=".mp4, .mov, .mkv, .avi, .flv, .mpeg, .webm, .wmv"
                                    onChange={handleUploadChange}
                                    showUploadList={false}
                                    // eslint-disable-next-line react/jsx-no-bind
                                    customRequest={() => {}}
                                    className="border-[2px] border-solid border-transparent"
                                >
                                    <div
                                        className={`flex ${
                                            videoList.length === 0 ? 'h-[200px] w-[754px]' : 'h-[129px] w-[240px]'
                                        } cursor-pointer items-center rounded-[9px] border-[1px] border-dashed border-gray-quaternary`}
                                    >
                                        <div className="flex w-full flex-col items-center">
                                            <img src={uploadIcon} className="h-10 w-10" />
                                            <span className="mt-[6px] text-[14px] leading-[22px] text-[#000]">
                                                上传视频
                                            </span>
                                            <span className="mt-[3px] text-[12px] leading-[12px] text-gray-quaternary">
                                                大小限制200M以内
                                            </span>
                                        </div>
                                    </div>
                                </Upload>
                            )}

                            {/* 视频列表 */}
                            {videoList.map(video => {
                                const items = getVideoItems(video);
                                return (
                                    <Dropdown
                                        align={{offset: [0, 4]}}
                                        menu={{items: items}}
                                        placement="bottomRight"
                                        getPopupContainer={getPopupContainer}
                                        key={video.nid}
                                        overlayStyle={{minWidth: 'auto', width: items.length > 0 ? 'auto' : 0}}
                                    >
                                        <div
                                            className={classnames(
                                                'relative',
                                                'h-[135px]',
                                                'w-[240px]',
                                                'cursor-pointer',
                                                'overflow-hidden',
                                                'rounded-[12px]',
                                                'border-[3px]',
                                                'border-solid',
                                                'bg-cover',
                                                'bg-no-repeat',
                                                video.nid === selectedVideo?.nid ? 'border-primary' : 'border-white'
                                            )}
                                            onClick={() => handleVideoClick(video)}
                                            style={{
                                                backgroundImage: `url(${video.coverUrl})`,
                                            }}
                                        >
                                            <div className="absolute left-[6px] top-[6px]">
                                                <VideoTag auditStatus={video.status} />
                                            </div>
                                            {video.videoType === VideoType.DigitalHuman && (
                                                <div className="absolute right-1.5 top-1.5 flex h-4 justify-center rounded-[3px] bg-white bg-opacity-20 px-[3px] py-[2px] text-xs leading-none text-white">
                                                    数字人
                                                </div>
                                            )}
                                            <span
                                                className="iconfont icon-preview absolute left-[50%] top-[50%] text-[30px] text-white opacity-90"
                                                style={{
                                                    transform: 'translate(-50%, -50%)',
                                                }}
                                            ></span>
                                            <div
                                                className="absolute bottom-[0] flex w-[234px] justify-between overflow-hidden px-[6px] pb-[3px] pt-[7px] text-[12px] font-medium leading-[20px] text-white"
                                                style={{
                                                    background:
                                                        'linear-gradient(180deg, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.4) 100%)',
                                                }}
                                            >
                                                <span className="overflow-hidden overflow-ellipsis whitespace-nowrap">
                                                    {video.title}
                                                </span>
                                                <span className="no-wrap shrink-0">
                                                    {formatVideoDuration(video.duration)}
                                                </span>
                                            </div>
                                        </div>
                                    </Dropdown>
                                );
                            })}
                        </div>
                        {loading &&
                            (videoList.length > 0 ? (
                                <div className="flex items-center justify-center pb-6 pt-[12px]">
                                    <span className="text-xs leading-3 text-flow-hover">加载中...</span>
                                </div>
                            ) : (
                                <Loading />
                            ))}
                    </Scrollbar>
                </div>
            </StyledModal>
            <UploadVideoModal
                file={uploadVideoFile}
                open={openUploadVideoModal}
                setOpen={setOpenUploadVideoModal}
                setVideoListModalOpen={setOpen}
                editor={editor}
                setOverlayType={setOverlayType}
            />
        </>
    );
}
