/**
 * @file 添加视频-创建百家号账号弹窗
 * <AUTHOR>
 */

import {Button, Checkbox, CheckboxProps, Modal} from 'antd';
import React, {useCallback, useState} from 'react';
import styled from '@emotion/styled';
import {CheckboxChangeEvent} from 'antd/es/checkbox';

interface CreateAccountModalProps {
    open: boolean;
    content: string;
    onCancel: () => void;
    onConfirm: () => void;
}

const StyledModal = styled(Modal)`
    .ant-modal-header {
        margin-bottom: 3px !important;
    }
    .ant-modal-content {
        padding: 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

export default function CreateAccountModal({open, content, onCancel, onConfirm}: CreateAccountModalProps) {
    const [okDisabled, setOkDisabled] = useState(true);

    const handleCheckboxChange: CheckboxProps['onChange'] = useCallback((e: CheckboxChangeEvent) => {
        const agreed = e.target.checked;
        setOkDisabled(!agreed);
    }, []);

    return (
        <StyledModal
            centered
            width={400}
            forceRender
            open={open}
            closeIcon={false}
            footer={
                <>
                    <Button
                        type="default"
                        shape="round"
                        className="h-[30px] border-[#EDEEF0] px-[15px] py-1 font-medium leading-[20px] hover:text-gray-tertiary"
                        onClick={onCancel}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        shape="round"
                        className="h-[30px] px-[15px] py-1 font-medium leading-[20px]"
                        onClick={onConfirm}
                        disabled={okDisabled}
                    >
                        开通
                    </Button>
                </>
            }
            onCancel={onCancel}
        >
            <div className="flex">
                <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-[#FF8200]"></span>
                <div className="text-[14px] leading-[22px] text-colorTextDefault">
                    {content}
                    <div className="mt-[6px]">
                        <Checkbox className="mr-2" onChange={handleCheckboxChange} />
                        <span className="mr-[3px]">阅读并同意</span>
                        <a
                            href={'https://baijiahao.baidu.com/docs/#/markdownsingle/BaiJiaHaoFuWuXieYi'}
                            target="_blank"
                            rel="noreferrer"
                        >
                            《百家号平台服务协议》
                        </a>
                    </div>
                </div>
            </div>
        </StyledModal>
    );
}
