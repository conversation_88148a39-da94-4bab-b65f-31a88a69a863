import styled from '@emotion/styled';

/**
 * 编辑器内部样式
 */
export const StyledTiptap = styled.div`
    // 非编辑态的编辑器不响应hover事件
    .non-editable {
        .ml-video-wrapper {
            pointer-events: none;
        }
    }

    .tiptap {
        font-size: 14px;
        line-height: 20px;
        height: 100%;

        :focus-visible {
            outline: none !important;
        }

        *::selection {
            background-color: #cbd6ef !important;
        }

        img {
            display: block;
            width: 220px;
            margin: 12px 0;
            border-radius: 9px;

            &.ProseMirror-selectednode {
                outline: 1px solid #5562f2;
            }
        }

        > .node-mlVideo:first-child {
            margin-top: 0 !important;
        }

        .node-mlVideo {
            width: 246px;
            border-radius: 12px;
            margin: 12px 0;
            border: 2px solid transparent;

            &.ProseMirror-selectednode {
                border: 2px solid #5562f2;
                .ml-video-wrapper {
                    border: 1px solid #5562f2 !important;
                }
            }
        }

        // 修改插入多媒体后的光标
        .ProseMirror-gapcursor:after {
            width: 1px;
            height: 20px;
            border-right: 1px solid black;
            border-top: none;
        }

        // markdown 样式
        li {
            display: list-item;
        }

        ol {
            list-style-type: decimal;
        }

        ul {
            list-style-type: disc;
        }

        ol,
        ul,
        dir,
        menu,
        dd {
            margin-left: 24px;
        }

        ol ul,
        ul ol,
        ul ul,
        ol ol {
            margin-top: 0;
            margin-bottom: 0;
        }

        table {
            color: rgba(39, 39, 42);
            border-spacing: 0;
            border-collapse: collapse;
        }

        table th,
        table td {
            padding: 6px 12px;
            border: 1px solid #dfe2e5;
        }

        table tr {
            background-color: #fff;
        }

        table thead tr,
        table tbody tr:nth-child(even) {
            background-color: #f8f8f8;
        }

        code {
            white-space: pre-wrap;
            word-break: break-all;
        }

        hr {
            margin-top: 6px;
            margin-bottom: 6px;
        }

        blockquote {
            border-left: 3px solid #848691;
            margin-top: 9px;
            margin-bottom: 9px;
            padding-left: 6px;
            color: #848691;
            font-size: 14px;
            line-height: 20px;
        }

        video::-webkit-media-controls-fullscreen-button {
            display: none;
        }
        video::-webkit-media-controls-volume-control-container {
            display: none;
        }
        video::-webkit-media-controls-timeline {
            display: none;
        }

        h1,
        h2,
        h3 {
            font-weight: 500;
            margin-top: 6px;
            margin-bottom: 6px;
        }

        h1,
        h2 {
            font-size: 21px;
            line-height: 33px;
        }

        h3 {
            font-size: 18px;
            line-height: 30px;
        }
    }
`;
