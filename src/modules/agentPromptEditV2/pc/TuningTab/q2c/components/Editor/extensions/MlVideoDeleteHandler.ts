/**
 * @file 处理 MlVideo 组件的删除事件
 * @description 参考实现：https://github.com/ueberdosis/tiptap/issues/181
 * <AUTHOR>
 */

import {Extension} from '@tiptap/core';
import {Plugin, PluginKey, Transaction} from '@tiptap/pm/state';
import {VideoType} from '../../../constant';
import editorExtensionEmitter from './emitter';

// 判断是否为删除 tr
function isDeleteTransaction(transaction: Transaction) {
    return transaction?.steps.some((step: any) => {
        if (step.slice && step.slice.size === 0) {
            return true;
        }
        return false;
    });
}

export const MlVideoDeleteHandler = Extension.create<void>({
    name: 'MlVideoDeleteHandler',

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('MlVideoDeleteHandler'),
                filterTransaction: (transaction, state) => {
                    // 为 true 则正常执行 tr
                    let result = true;
                    // 非删除操作，直接正常执行；豁免 forceDelete 和拖拽操作导致的删除
                    if (
                        transaction.getMeta('forceDelete') ||
                        transaction.getMeta('uiEvent') === 'drop' ||
                        !isDeleteTransaction(transaction)
                    ) {
                        return result;
                    }

                    const replaceSteps: number[] = [];
                    transaction.steps.forEach((step: any, index) => {
                        if (step.jsonID === 'replace') {
                            replaceSteps.push(index);
                        }
                    });

                    replaceSteps.forEach(index => {
                        const map = transaction.mapping.maps[index] as any;
                        const oldStart = map.ranges[0];
                        const oldEnd = map.ranges[0] + map.ranges[1];
                        state.doc.nodesBetween(oldStart, oldEnd, node => {
                            if (node.type.name === 'mlVideo' && node.attrs.videoType === VideoType.DigitalHuman) {
                                result = false;
                                editorExtensionEmitter.emit('deleteMlVideo');
                            }
                        });
                    });
                    return result;
                },
            }),
        ];
    },
});
