/**
 * @file Q2C 编辑器遮罩，包括各种 loading 态
 * <AUTHOR>
 */
import {Editor} from '@tiptap/core';
import {useCallback} from 'react';
import {Button, Skeleton} from 'antd';
import {useSearchParams} from 'react-router-dom';
import styled from '@emotion/styled';
import loadingAnimation from '@/assets/loading-animation.gif';
import Exclamation from '@/assets/exclamation-circle-icon.png';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {regenerateAnswer} from '@/api/q2c';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {AnswerStatus, QAStatus} from '@/api/q2c/interface';
import {EEditToolCancelId} from '@/utils/loggerV2/interface';
import {useLogExt} from '../../hooks/logExt';
import {useQ2CList} from '../../hooks/useQ2CList';
import {formatAnswer} from '../../utils';
import {OverlayType} from './constant';

interface OverlayProps {
    editor: Editor;
    overlayType: OverlayType;
    setOverlayType: (type: OverlayType) => void;
    onCancelLoading: () => void;
    failStatus?: AnswerStatus;
}

// 有取消按钮的遮罩
type OverlayTypeWithCancelButton =
    | OverlayType.Emoji
    | OverlayType.Expand
    | OverlayType.AITuning
    | OverlayType.Summarize
    | OverlayType.UploadImage
    | OverlayType.UploadVideo;

const OverlayInfoMap: Record<OverlayTypeWithCancelButton, {title: string; cancelButton: string; logValue: number}> = {
    [OverlayType.AITuning]: {title: '智能润色中', cancelButton: '取消润色', logValue: EEditToolCancelId.AITuning},
    [OverlayType.Emoji]: {title: '添加表情中...', cancelButton: '取消表情', logValue: EEditToolCancelId.Emoji},
    [OverlayType.Expand]: {title: '智能扩写中...', cancelButton: '取消扩写', logValue: EEditToolCancelId.Expand},
    [OverlayType.Summarize]: {title: '智能总结中...', cancelButton: '取消总结', logValue: EEditToolCancelId.Summarize},
    [OverlayType.UploadImage]: {title: '上传中...', cancelButton: '取消上传', logValue: EEditToolCancelId.Image},
    [OverlayType.UploadVideo]: {title: '上传中...', cancelButton: '取消上传', logValue: EEditToolCancelId.Video},
};

export const StyledSkeleton = styled(Skeleton.Input)`
    width: 100% !important;
    background-image: linear-gradient(90deg, #f5f6fa 25%, #ffffff 37%, #f5f6fa 63%) !important;
    border-radius: 12px;
`;

const Overlay = ({editor, overlayType, setOverlayType, onCancelLoading, failStatus}: OverlayProps) => {
    const {updateQ2CListByIndex} = useQ2CList();

    const {question, QA} = useQ2CStore(store => ({
        question: store.activeQA?.question || '',
        QA: store.activeQA,
    }));

    const {logExt} = useLogExt();

    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId');

    const {clickLog} = useUbcLogV3();

    const handleRegenerate = useCallback(() => {
        /** B-3 调优页面重新获取 */
        clickLog(EVENT_VALUE_CONST.REGAIN_QA, logExt);

        if (!QA) {
            return;
        }

        setOverlayType(OverlayType.Loading);

        updateQ2CListByIndex(
            {
                ...QA,
                status: QAStatus.Generating,
                failStatus: AnswerStatus.Correct,
            },
            QA.index
        );
        regenerateAnswer({appId: appId || '', question, tuningId: QA.tuningId})
            .then(res => {
                editor?.commands.setContent(
                    formatAnswer({
                        ...QA,
                        ...res,
                    }),
                    true
                );
                setOverlayType(OverlayType.None);

                updateQ2CListByIndex(
                    {
                        ...QA,
                        ...res,
                    },
                    QA.index
                );
            })
            .catch(() => {
                setOverlayType(OverlayType.Regenerate);

                updateQ2CListByIndex(
                    {
                        ...QA,
                        status: QAStatus.Generating,
                        failStatus: AnswerStatus.Error,
                    },
                    QA.index
                );
            });
    }, [QA, appId, clickLog, editor?.commands, logExt, question, setOverlayType, updateQ2CListByIndex]);

    const handleLoadingCancel = useCallback(() => {
        /** B-31 编辑器顶部AI按钮取消按钮 */
        clickLog(EVENT_VALUE_CONST.EDIT_TOOL_CANCEL, {
            ...logExt,
            eEditToolCancelId: OverlayInfoMap[overlayType as keyof typeof OverlayInfoMap].logValue,
        });

        setOverlayType(OverlayType.None);
        onCancelLoading();
    }, [clickLog, logExt, onCancelLoading, overlayType, setOverlayType]);

    return (
        <>
            {overlayType !== OverlayType.None && (
                <div
                    className={`${
                        overlayType === OverlayType.Loading ? 'bg-opacity-60' : 'bg-opacity-80'
                    } absolute top-0 flex h-full w-full flex-col items-center justify-center rounded-xl bg-white`}
                >
                    {/* 骨架屏 */}
                    {overlayType === OverlayType.Loading && (
                        <div className="w-full overflow-hidden rounded-b-xl">
                            <StyledSkeleton
                                active
                                style={{
                                    height: 206,
                                    borderTopLeftRadius: '12px',
                                    borderTopRightRadius: '12px',
                                }}
                            />
                        </div>
                    )}
                    {/* AI润色加载遮罩 */}
                    {overlayType in OverlayInfoMap && (
                        <>
                            <img src={loadingAnimation} className="mx-auto w-[40px]" alt="加载中"></img>
                            <div className="mt-3 text-[14px] leading-[22px]">
                                {OverlayInfoMap[overlayType as keyof typeof OverlayInfoMap].title}
                            </div>
                            <Button
                                type="primary"
                                shape="round"
                                onClick={handleLoadingCancel}
                                className="mt-[18px] border-colorBorderFormList bg-white text-[14px] font-medium text-primary hover:opacity-40"
                            >
                                {OverlayInfoMap[overlayType as keyof typeof OverlayInfoMap].cancelButton}
                            </Button>
                        </>
                    )}
                    {/* 重新获取遮罩 */}
                    {overlayType === OverlayType.Regenerate && (
                        <>
                            <img src={Exclamation} className="mb-[9px] w-[60px]" />
                            <p className="text-[14px] leading-[22px]">
                                {failStatus === AnswerStatus.RateLimitError
                                    ? '当前加载人数过多，请稍后重试，'
                                    : '加载失败，'}
                                <span className="cursor-pointer text-primary" onClick={handleRegenerate}>
                                    点击重新加载
                                </span>
                            </p>
                        </>
                    )}
                    {/* 获取视频信息遮罩 */}
                    {overlayType === OverlayType.VideoInfo && (
                        <>
                            <img src={loadingAnimation} className="mx-auto w-[40px]" alt="加载中"></img>
                            <div className="mt-3 text-[14px] leading-[22px]">视频信息获取中...</div>
                        </>
                    )}
                </div>
            )}
        </>
    );
};

export default Overlay;
