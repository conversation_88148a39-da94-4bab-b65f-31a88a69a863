/**
 * @file Q2C 编辑器划词浮动气泡
 * <AUTHOR>
 */

import {Button, message, Skeleton} from 'antd';
import styled from '@emotion/styled';
import copy from 'copy-to-clipboard';
import {Editor} from '@tiptap/core';
import {useCallback, useMemo} from 'react';
import Exclamation from '@/assets/exclamation-circle-icon.png';
import {TuningAction} from '@/api/q2c/interface';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {PopoverStatus} from '../constant';
import {useLogExt} from '../../../hooks/logExt';

const StyledSkeleton = styled(Skeleton)`
    .ant-skeleton-title {
        display: none !important;
    }
    .ant-skeleton-title + .ant-skeleton-paragraph {
        margin-block-start: 9px !important;
    }
    .ant-skeleton-paragraph > li {
        height: 12px !important;
        background-image: linear-gradient(
            90deg,
            rgba(51, 91, 255, 0) 32.78%,
            rgba(51, 91, 255, 0.05) 77.15%,
            rgba(51, 91, 255, 0) 119.03%
        ) !important;
        border-radius: 12px !important;
    }
    .ant-skeleton-paragraph > li + li {
        margin-block-start: 10px !important;
    }
`;

export interface ActionInfo {
    type?: TuningAction;
    name: string;
    icon: string;
    errorText?: string;
    logValue: number;
}

interface ActionPopoverProps {
    editor: Editor;
    status: PopoverStatus;
    content: string;
    actionInfo?: ActionInfo;
    setStatus: (status: PopoverStatus) => void;
    regenerate: (actionInfo?: TuningAction) => void;
    handleCancel: () => void;
}

const ActionPopover = ({
    status,
    content,
    editor,
    actionInfo,
    setStatus,
    regenerate,
    handleCancel,
}: ActionPopoverProps) => {
    const {clickLog} = useUbcLogV3();
    const {logExt} = useLogExt();

    const newLogExt = useMemo(() => {
        return {
            ...logExt,
            eWordSlideFunctionId: actionInfo?.logValue,
        };
    }, [actionInfo?.logValue, logExt]);

    const replaceSelectedText = useCallback(() => {
        /** B-29 划词—替换原文按钮 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_REPLACE, newLogExt);

        editor.chain().focus().insertContent(content).run();
    }, [clickLog, content, editor, newLogExt]);

    const copyToClipboard = useCallback(() => {
        /** B-26 划词—复制按钮点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_COPY, newLogExt);

        try {
            copy(content);
            message.success('复制成功');
        } catch (e) {
            message.error('复制失败');
        }
    }, [clickLog, content, newLogExt]);

    const closePopover = useCallback(() => {
        /** B-30 划词—关闭弹窗点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_CLOSE, newLogExt);

        handleCancel();
        setStatus(PopoverStatus.None);
    }, [clickLog, newLogExt, handleCancel, setStatus]);

    const deprecate = useCallback(() => {
        /** B-28 划词—弃用按钮点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_ABANDON, newLogExt);

        setStatus(PopoverStatus.None);
    }, [clickLog, newLogExt, setStatus]);

    const handleRegenerate = useCallback(() => {
        /** B-27 划词—刷新按钮点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_REFRESH, newLogExt);

        regenerate(actionInfo?.type);
    }, [actionInfo?.type, clickLog, newLogExt, regenerate]);

    return (
        <div
            className="w-[340px] rounded-[9px] p-[12px] text-colorTextDefault"
            // 防止选中的文本失焦
            onMouseDown={e => {
                e.preventDefault();
            }}
        >
            {/* 顶部 */}
            <div className="flex content-center justify-between text-[14px] leading-[14px]">
                <div className="flex content-center gap-[6px]">
                    <span className={`iconfont ${actionInfo?.icon}`}></span>
                    <span className="font-medium text-colorTextDefault">{actionInfo?.name}</span>
                </div>
                <span className="iconfont icon-close cursor-pointer" onClick={closePopover}></span>
            </div>
            {/* 文本展示 */}
            {status === PopoverStatus.Content && (
                <div className="mt-3 text-colorTextDefault">
                    <ScrollContainer
                        className="mr-1 h-full max-h-[100px] pr-4 text-[12px] leading-[20px]"
                        scrollbarColor="#D9D9D9"
                        scrollbarWidth={4}
                        scrollY
                    >
                        {content}
                    </ScrollContainer>
                    <p className="mt-[6px] text-[10px] leading-[10px] text-gray-quaternary">以上内容为AI生成</p>
                    <div className="mt-[9px] flex justify-between">
                        <div className="flex gap-[7px]">
                            <span
                                className="iconfont icon-copy flex h-[23px] w-[23px] cursor-pointer justify-center rounded-full bg-colorBgFormList text-[12px] leading-[23px] hover:text-primary"
                                onClick={copyToClipboard}
                            ></span>
                            <span
                                className="iconfont icon-exchange flex h-[23px] w-[23px] cursor-pointer justify-center rounded-full bg-colorBgFormList text-[12px] leading-[23px] hover:text-primary"
                                onClick={handleRegenerate}
                            ></span>
                        </div>
                        <div className="flex gap-[6px]">
                            <Button
                                type="default"
                                className="h-[23px] rounded-full border-[#EDEEF0] px-[10px] py-0 text-[10px] leading-[21px] hover:text-gray-tertiary"
                                onClick={deprecate}
                            >
                                弃用
                            </Button>
                            <Button
                                type="primary"
                                className="hover:[bg-#3644D9] h-[23px] rounded-full px-[10px] py-0 text-[10px] leading-[21px]"
                                onClick={replaceSelectedText}
                            >
                                替换原文
                            </Button>
                        </div>
                    </div>
                </div>
            )}
            {/* 骨架屏 */}
            {status === PopoverStatus.Loading && <StyledSkeleton active />}
            {/* 错误态 */}
            {status === PopoverStatus.Error && (
                <div className="flex w-full flex-col items-center pb-3">
                    <img src={Exclamation} className="mb-[9px] w-[60px]" />
                    <p className="text-[14px] leading-[22px] text-gray-tertiary">
                        {actionInfo?.errorText || actionInfo?.name}失败，
                        <span className="cursor-pointer text-primary" onClick={handleRegenerate}>
                            点击重试
                        </span>
                    </p>
                </div>
            )}
        </div>
    );
};

export default ActionPopover;
