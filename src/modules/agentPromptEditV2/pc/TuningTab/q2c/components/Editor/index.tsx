/* eslint-disable complexity */
/**
 * @file 开发者调优 Q2C 编辑器
 * @description 基于：https://github.com/ueberdosis/tiptap，目前仅支持：纯文本编辑、插入 image、插入 video
 * <AUTHOR>
 */

import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import {Editor as CoreEditor, EditorContent, useEditor} from '@tiptap/react';
import CharacterCount from '@tiptap/extension-character-count';
import {Markdown} from 'tiptap-markdown';
import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {message} from 'antd';
import Placeholder from '@tiptap/extension-placeholder';
import {JSONContent, posToDOMRect} from '@tiptap/core';
import {EditorState} from '@tiptap/pm/state';
import {Node} from '@tiptap/pm/model';
import Scrollbar from '@/components/Scrollbar';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {AnswerStatus, QA, QAStatus} from '@/api/q2c/interface';
import {Q2CAuditStatus} from '../../constant';
import Q2CEditorContext from '../../context/Q2CEditorContext';
import {convertMlVideoTagToMarkdown, formatAnswer, isErrorStatus, isNoAnswerStatus} from '../../utils';
import {ImageUploader} from './extensions/ImageUploader';
import {Link} from './extensions/Link';
import MlVideo from './extensions/MlVideo';
import MenuBar from './MenuBar';
import BubbleMenu from './BubbleMenu';
import Overlay from './Overlay';
import {OverlayType} from './constant';
import {StyledTiptap} from './StyledTiptap';
import {TiptapEditorProps} from './useTiptapEditor';
import {MlVideoDeleteHandler} from './extensions/MlVideoDeleteHandler';

const PLACEHOLDER_MAP = {
    [AnswerStatus.NoAnswer]: '智能化生成失败，请自行编写后提交',
    [AnswerStatus.UIJsonNoAnswer]: '由于智能体配置uijson插件，无法在编辑器中智能生成回答，请自行编写后提交',
};

export interface EditorRef {
    overlayType: OverlayType;
    setContent: (content: string) => void;
}

interface EditorProps {
    QA: QA;
    editorProps: TiptapEditorProps;
    index: number;
}

const Editor = forwardRef(({QA, editorProps, index}: EditorProps, ref: React.Ref<any>) => {
    const {updateEditorContent, isEditing, updateIsEditing} = useQ2CStore(store => ({
        updateEditorContent: store.updateEditorContent,
        isEditing: store.editorStates[QA.tuningId]?.isEditing || false,
        updateIsEditing: store.updateIsEditing,
    }));

    const auditing = QA.auditStatus === Q2CAuditStatus.Auditing;

    const {saveTime, setSaveTime, setErrorText, saveContent, tips, hovered} = editorProps;
    const [overlayType, setOverlayType] = useState(OverlayType.Loading);

    const menuBarRef = useRef<any>(null);
    const scrollContainer = useRef<any>(null);
    const [isSelectionInView, setIsSelectionInView] = useState(true);

    // 删除视频时，进行提示
    const previousState = useRef<EditorState>();
    const onMlVideoDeleted = useCallback(
        (content: string, jsonContent: JSONContent) => {
            message.info('删除后可在上传视频处再次添加');
            saveContent(false, {content, jsonContent});
        },
        [saveContent]
    );
    // 监听 mlVideo 组件的插入和删除：删除弹窗，插入主动调用保存
    const observeMlVideo = useCallback(
        ({editor}: {editor: CoreEditor}) => {
            const prevNodesByNid: Record<string, Node> = {};
            previousState.current?.doc.descendants(node => {
                if (node.attrs.videoId) {
                    prevNodesByNid[node.attrs.videoId] = node;
                }
            });

            const nodesByNid: Record<string, Node> = {};
            editor.state.doc.descendants(node => {
                if (node.attrs.videoId) {
                    nodesByNid[node.attrs.videoId] = node;
                }
            });

            previousState.current = editor.state;

            for (const [videoId] of Object.entries(prevNodesByNid)) {
                if (nodesByNid[videoId] === undefined) {
                    const content = convertMlVideoTagToMarkdown(editor.storage.markdown.getMarkdown());
                    const jsonContent = editor.getJSON();
                    isEditing && onMlVideoDeleted(content, jsonContent);
                }
            }

            for (const [videoId] of Object.entries(nodesByNid)) {
                const isInsert = prevNodesByNid[videoId] === undefined;
                const isUpdatePoster =
                    prevNodesByNid[videoId] &&
                    prevNodesByNid[videoId].attrs.poster !== nodesByNid[videoId].attrs.poster;
                if ((isInsert || isUpdatePoster) && !auditing && isEditing) {
                    const content = convertMlVideoTagToMarkdown(editor.storage.markdown.getMarkdown());
                    const jsonContent = editor.getJSON();
                    saveContent(false, {content, jsonContent});
                }
            }
        },
        [auditing, isEditing, onMlVideoDeleted, saveContent]
    );

    const editor = useEditor({
        parseOptions: {
            preserveWhitespace: false,
        },
        onBlur: () => {
            // 失焦触发自动保存
            !isEditing && saveContent();
        },
        onUpdate: e => {
            observeMlVideo(e);
            setErrorText('');
            if (editor) {
                const content = convertMlVideoTagToMarkdown(editor.storage.markdown.getMarkdown());
                const jsonContent = editor.getJSON();

                updateEditorContent({content, jsonContent}, QA.tuningId);
            }
        },
        onSelectionUpdate: () => {
            setIsSelectionInView(true);
        },
        extensions: [
            StarterKit.configure({
                dropcursor: {
                    color: '#5562F2',
                },
            }),
            Markdown,
            Image,
            ImageUploader,
            MlVideo,
            CharacterCount.configure({
                // 字数暂无限制
                // limit: 2048,
                textCounter: text => text.replaceAll('\n', '').length,
            }),
            Link,
            // 仅在由于uijson生成失败时展示
            Placeholder.configure({
                placeholder: isNoAnswerStatus(QA.failStatus)
                    ? PLACEHOLDER_MAP[QA.failStatus as AnswerStatus.NoAnswer | AnswerStatus.UIJsonNoAnswer]
                    : '',
                emptyEditorClass: isNoAnswerStatus(QA.failStatus)
                    ? 'cursor-text before:content-[attr(data-placeholder)] before:absolute before:text-mauve-11 before:text-[#848691] before-pointer-events-none'
                    : '',
                showOnlyWhenEditable: false,
            }),
            MlVideoDeleteHandler,
        ],
    });

    useEffect(() => {
        return () => {
            editor?.destroy();
        };
    }, [editor]);

    useImperativeHandle(ref, () => {
        return {
            overlayType,
            setContent(content: string) {
                editor?.commands.setContent(formatAnswer({...QA, answer: content}), true);
            },
        };
    });

    useEffect(() => {
        setSaveTime(QA.saveTime);
    }, [QA.saveTime, setSaveTime]);

    useEffect(() => {
        if (!QA) {
            return;
        }

        if (isErrorStatus(QA.failStatus)) {
            setOverlayType(OverlayType.Regenerate);
        } else if (isNoAnswerStatus(QA.failStatus)) {
            message.info({
                key: 'Q2CNoAnswer',
                content: '请编辑更符合您智能体的回答',
            });
            setOverlayType(OverlayType.None);
        } else if (QA.status === QAStatus.Generating) {
            setOverlayType(OverlayType.Loading);
        } else {
            editor?.commands.setContent(formatAnswer(QA), true);
            setOverlayType(OverlayType.None);
        }
    }, [editor, QA, setOverlayType]);

    useEffect(() => {
        const editable = isEditing && overlayType === OverlayType.None;
        editor?.setEditable(editable);
        editable && editor?.commands.focus(0, {scrollIntoView: false});
    }, [isEditing, editor, overlayType]);

    const handleEditorClick = useCallback(() => {
        // 审核态或处于生成态，不可编辑
        const generating = QA.status === QAStatus.Generating && QA.failStatus === AnswerStatus.Correct;
        !(auditing || generating) && updateIsEditing(true, QA.tuningId);
    }, [QA.failStatus, QA.status, QA.tuningId, auditing, updateIsEditing]);

    // 判断选中文本是否在视区，不在视区则隐藏 bubbleMenu
    const handleScroll = useCallback(() => {
        if (!editor?.view) {
            return;
        }

        const from = editor?.view.state.selection.from || 0;
        const to = editor?.view.state.selection.to || 0;
        const selectionRect = posToDOMRect(editor?.view, from, to);
        const containerRect = scrollContainer.current.getBoundingClientRect();
        setIsSelectionInView(
            selectionRect.top >= containerRect.top &&
                selectionRect.bottom <= containerRect.bottom &&
                selectionRect.left >= containerRect.left &&
                selectionRect.right <= containerRect.right
        );
    }, [editor?.view]);

    const onCancelLoading = useCallback(() => {
        menuBarRef.current?.cancelRequest();
    }, []);

    const q2cEditorContextValue = useMemo(() => ({QA}), [QA]);

    if (!editor) {
        return null;
    }

    return (
        <Q2CEditorContext.Provider value={q2cEditorContextValue}>
            {isEditing && (
                <div className="sticky top-[39px] z-[9] bg-white">
                    <MenuBar
                        editor={editor}
                        overlayType={overlayType}
                        setOverlayType={setOverlayType}
                        editorProps={editorProps}
                        QA={QA}
                        index={index}
                        ref={menuBarRef}
                    />
                </div>
            )}
            <div
                className={`relative h-fit w-full rounded-xl border ${
                    isEditing
                        ? 'border-primary bg-colorBgFormList'
                        : hovered && !auditing
                        ? 'cursor-pointer border-gray-tertiary'
                        : 'border-colorBorderFormList'
                }`}
                onClick={handleEditorClick}
            >
                <BubbleMenu editor={editor} overlayType={overlayType} isSelectionInView={isSelectionInView} />
                <div>
                    <StyledTiptap className={`${isEditing ? 'pt-3' : 'py-3'}`}>
                        <Scrollbar
                            className={`mr-[6px] overflow-y-auto transition-all duration-300 ${
                                isEditing ? 'h-[323px]' : 'h-[181px]'
                            }`}
                            ref={scrollContainer}
                            onScroll={handleScroll}
                        >
                            {isEditing && tips && tips !== '' && (
                                <div className="text-black-base mb-3 ml-2 mr-1 flex h-10 items-center gap-2 rounded-[9px] bg-[#ECEFFE] pl-4 text-[14px] leading-[22px]">
                                    <span className="iconfont icon-info-circle-fill text-[16px] text-[#4F6FF9]"></span>
                                    <span>{tips}</span>
                                </div>
                            )}
                            <EditorContent
                                editor={editor}
                                className={`h-[calc(100%-52px)] pl-3 pr-1 ${
                                    isEditing ? 'cursor-text text-colorTextDefault' : 'non-editable text-[#595959]'
                                }`}
                            />
                        </Scrollbar>
                    </StyledTiptap>
                    {isEditing && (
                        <div className="flex items-center justify-end gap-[15px] px-3 py-1 text-[12px] leading-[20px] text-gray-tertiary">
                            <span>{saveTime && saveTime !== '' && `保存于 ${saveTime}`}</span>
                            <span>字数：{editor.storage.characterCount.characters()}</span>
                        </div>
                    )}
                </div>
                <Overlay
                    editor={editor}
                    overlayType={overlayType}
                    setOverlayType={setOverlayType}
                    onCancelLoading={onCancelLoading}
                    failStatus={QA.failStatus}
                />
            </div>
        </Q2CEditorContext.Provider>
    );
});

export default Editor;
