import {But<PERSON>, Modal} from 'antd';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {getCoverList} from '@/api/q2c';
import {BaseLoadingStatus} from '@/types';
import Loading from '@/components/Loading';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import ImageSelect from '../../../../components/ImageSelect';
import {useBjhVideoContext} from '../../../context/BjhVideoContext';

interface CreateAccountModalProps {
    open: boolean;
    onCancel: () => void;
    onConfirm: (url: string) => void;
}

const StyledModal = styled(Modal)`
    .ant-modal-header {
        margin-bottom: 3px !important;
    }
    .ant-modal-content {
        padding: 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

// const loadImages = (imageUrls: string[]) => {
//     return Promise.all(
//         imageUrls.map(url => {
//             return new Promise((resolve, reject) => {
//                 const img = new Image();
//                 img.src = url;
//                 img.onload = resolve;
//                 img.onerror = reject;
//             });
//         })
//     );
// };

export default function CoverEditModal({open, onCancel, onConfirm}: CreateAccountModalProps) {
    const {clickLog} = useUbcLogV3();
    const {changeVideoCoverPropsRef, currentQA} = useBjhVideoContext();

    const [value, setValue] = useState<string>();

    const [loadStatus, setLoadStatus] = useState<BaseLoadingStatus>(BaseLoadingStatus.Normal);
    const [coverUrlList, setCoverUrlList] = useState<string[]>([]);
    const loadCoverUrlList = useCallback(async () => {
        if (!currentQA) {
            return;
        }

        const changeVideoCoverProps = changeVideoCoverPropsRef.current;
        if (!changeVideoCoverProps) return;

        try {
            setLoadStatus(BaseLoadingStatus.Loading);
            const {coverUrlList} = await getCoverList({
                tuningId: currentQA.tuningId,
                videoUrl: changeVideoCoverProps.videoUrl,
            });
            // await loadImages(coverUrlList);
            setCoverUrlList(coverUrlList);
            setValue(coverUrlList[0]);
        } catch (error) {
            setLoadStatus(BaseLoadingStatus.Error);
            console.error(error);
        }

        setLoadStatus(BaseLoadingStatus.Normal);
    }, [changeVideoCoverPropsRef, currentQA]);

    useEffect(() => {
        if (!open) {
            setValue('');
            return;
        }

        loadCoverUrlList();
    }, [loadCoverUrlList, open]);

    const handleConfirm = useCallback(() => {
        clickLog('set_cover_confirm');
        if (!value) return;
        onConfirm(value);
    }, [clickLog, onConfirm, value]);

    const content = useMemo(() => {
        switch (loadStatus) {
            case BaseLoadingStatus.Loading:
                return <Loading />;
            case BaseLoadingStatus.Error:
                return (
                    <div className="flex h-[454px] items-center justify-center">
                        <Button onClick={loadCoverUrlList} type="primary">
                            重新加载
                        </Button>
                    </div>
                );
            default:
                return (
                    <div>
                        <div>
                            <img className="mx-auto h-[321px] w-[570px] rounded-xl" src={value} />
                            <div className="mx-auto my-4 text-center">智能封面推荐</div>
                        </div>
                        <div>
                            <ImageSelect
                                className="gap-[18px] [&_.icon-button]:h-8 [&_.icon-button]:w-8 [&_.icon]:text-lg [&_.select-image-container]:h-[90px]"
                                value={value}
                                onChange={setValue}
                                list={coverUrlList.map(item => ({key: item, link: item}))}
                            />
                        </div>
                    </div>
                );
        }
    }, [coverUrlList, loadCoverUrlList, loadStatus, value]);

    return (
        <StyledModal
            centered
            width={800}
            forceRender
            open={open}
            closeIcon={false}
            title="选择封面"
            zIndex={1001}
            footer={
                <>
                    <Button
                        type="default"
                        shape="round"
                        className="h-[30px] border-[#EDEEF0] px-[15px] py-1 font-medium leading-[20px] hover:text-gray-tertiary"
                        onClick={onCancel}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        shape="round"
                        className="h-[30px] px-[15px] py-1 font-medium leading-[20px]"
                        onClick={handleConfirm}
                        disabled={!value || loadStatus !== BaseLoadingStatus.Normal}
                    >
                        确定
                    </Button>
                </>
            }
            onCancel={onCancel}
        >
            <div className="min-h-[454px]">{content}</div>
        </StyledModal>
    );
}
