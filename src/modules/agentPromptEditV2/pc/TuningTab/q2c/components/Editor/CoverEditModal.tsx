/**
 * @file 封面首图编辑弹窗
 * <AUTHOR>
 */

import {Button, Modal} from 'antd';
import React, {useCallback, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import {useSearchParams} from 'react-router-dom';
import {getCoverDetail, saveTuning, setCover} from '@/api/q2c';
import {CoverDetail, QAStatus, SubmitStatus} from '@/api/q2c/interface';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useQ2CContext} from '../../context/Q2CContext';
import {useLogExt} from '../../hooks/logExt';

export const StyleModal = styled(Modal)`
    &.ant-modal .ant-modal-header {
        margin-bottom: 3px;
    }
`;

export default function CoverEditModal({open, onClose}: {open: boolean; onClose: () => void}) {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;

    const {logExt} = useLogExt();

    const {tuningId, editorStates, question, activeQA} = useQ2CStore(store => ({
        tuningId: store.activeTuningId,
        editorStates: store.editorStates,
        question: store.activeQA?.question || '',
        activeQA: store.activeQA,
    }));
    const [coverList, setCoverList] = useState<CoverDetail[]>([]);
    const [selectIndex, setSelectIndex] = useState(0);
    const [hoverIndex, setHoverIndex] = useState(-1);
    const {clickLog} = useUbcLogV3();

    const {previewRef} = useQ2CContext();

    const handleMouseEnter = useCallback((index: number) => {
        setHoverIndex(index);
    }, []);

    const handleMouseLeave = useCallback(() => {
        setHoverIndex(-1);
    }, []);

    const handleSetCover = useCallback(
        (index: number) => {
            /** B-16 调优页面设为封面点击 */
            clickLog(EVENT_VALUE_CONST.OPT_SET_COVER, logExt);

            setSelectIndex(index);
        },
        [clickLog, logExt]
    );

    const onSubmit = useCallback(() => {
        /** B-17 调优页面设为封面确认点击 */
        clickLog(EVENT_VALUE_CONST.OPT_SET_COVER_CONFIRM, logExt);

        setCover({coverUrl: coverList[selectIndex]?.url, tuningId, appId});

        previewRef?.current?.preview(editorStates[tuningId]?.content || '');
        onClose();
    }, [appId, clickLog, coverList, editorStates, logExt, onClose, previewRef, selectIndex, tuningId]);

    const handleCancel = useCallback(() => {
        /** B-18 调优页面设为封面确认点击 */
        clickLog(EVENT_VALUE_CONST.OPT_SET_COVER_CANCEL, logExt);

        onClose();
    }, [clickLog, logExt, onClose]);

    // 先保存编辑器内容，后请求 coverList，保证 coverList 是最新的
    const fetchCoverList = useCallback(async () => {
        try {
            const editorContent = editorStates[tuningId]?.content || '';
            await saveTuning({
                appId,
                tuningId,
                answer: editorContent,
                question,
                submit: activeQA?.status === QAStatus.Submitted ? SubmitStatus.Submit : SubmitStatus.Unsubmit,
            });
            getCoverDetail({
                appId,
                tuningId,
                answer: editorContent,
            }).then(res => {
                setCoverList(res);
                res.forEach((coverDetail, index) => {
                    if (coverDetail.isCover) {
                        setSelectIndex(index);
                    }
                });
            });
        } catch (error) {
            console.error(error);
        }
    }, [activeQA?.status, appId, editorStates, question, tuningId]);

    useEffect(() => {
        setCoverList([]);
        open && fetchCoverList();
    }, [fetchCoverList, open]);

    return (
        <StyleModal
            centered
            title="封面首图"
            width={800}
            forceRender
            open={open}
            footer={
                <>
                    <Button onClick={handleCancel} type="default" shape="round">
                        取消
                    </Button>
                    <Button onClick={onSubmit} type="primary" shape="round">
                        确定
                    </Button>
                </>
            }
            onCancel={onClose}
        >
            <div className="text-[14px] text-gray-tertiary">封面首图将展示在搜索结果卡中，吸引用户点击查看</div>
            <ScrollContainer
                scrollY
                scrollbarWidth={4}
                className="mt-3 flex max-h-[400px] min-h-[250px] flex-wrap gap-x-3 gap-y-[10px] rounded-[6px] bg-[#E4E7F180] p-3"
            >
                {/* 多媒体列表 */}
                {coverList.map((cover, index) => (
                    <div
                        className="relative h-[120px] overflow-hidden rounded-[3px]"
                        key={`cover_${cover.url}`}
                        onMouseEnter={() => handleMouseEnter(index)}
                        onMouseLeave={handleMouseLeave}
                    >
                        <img src={cover.url} className="h-[120px] w-[172px] object-cover"></img>
                        {index === hoverIndex && (
                            <div className="absolute top-0 flex h-full w-full items-center justify-center bg-black bg-opacity-50">
                                <span
                                    className="cursor-pointer rounded-full bg-white px-[15px] py-[5px] text-[14px] font-medium leading-[20px] text-primary"
                                    onClick={() => handleSetCover(index)}
                                >
                                    设为封面
                                </span>
                            </div>
                        )}
                        {index === selectIndex && index !== hoverIndex && (
                            <div className="absolute bottom-0 left-0 flex h-[27px] w-full items-center justify-center bg-black bg-opacity-50 text-[14px] text-white">
                                封面
                            </div>
                        )}
                    </div>
                ))}
            </ScrollContainer>
        </StyleModal>
    );
}
