/**
 * @file 添加视频-上传本地视频弹窗
 * <AUTHOR>
 */

import {Button, Form, Input, Modal} from 'antd';
import React, {useCallback, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import {useSearchParams} from 'react-router-dom';
import {Editor} from '@tiptap/core';
import auditManagementApi from '@/api/auditManagement';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {FileType} from '@/api/auditManagement/interface';
import loadingAnimation from '@/assets/loading-animation.gif';
import {getBjhVideoInfo, submitBjhVideo, submitVideoCover} from '@/api/q2c';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import beginnerGuideApi from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {VideoAuditStatus} from '../../../constant';
import {useLogExt} from '../../../hooks/logExt';
import {OverlayType} from '../constant';
import {useBjhVideoContext} from '../../../context/BjhVideoContext';
import {useConfirmContext} from '../../../../context/ConfirmContext';
import {SetMlVideoOptions} from '../extensions/MlVideo';

interface UploadVideoModalProps {
    file?: File;
    open: boolean;
    setOpen: (value: boolean) => void;
    editor?: Editor;
    setOverlayType?: (overlayType: OverlayType) => void;
    setVideoListModalOpen: (value: boolean) => void;
}

const StyleModal = styled(Modal)`
    .ant-modal-header {
        margin-bottom: 12px !important;
    }
    .ant-modal-content {
        padding: 24px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

const StyledFormItem = styled(Form.Item)`
    & .ant-form-item-explain-error {
        font-size: 12px;
        line-height: 18px;
        margin-top: 4px;
    }
`;

const titleRegex = /^[\u4e00-\u9fa5a-zA-Z]+$/;

export default function UploadVideoModal({
    file,
    open,
    setOpen,
    editor,
    setOverlayType,
    setVideoListModalOpen,
}: UploadVideoModalProps) {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;
    // TODO 这里也是在维护全局的 activeTuning，最好改成维护局部的 QA
    const {tuningId} = useQ2CStore(store => ({
        tuningId: store.activeTuningId,
    }));

    const {logExt} = useLogExt();
    const {clickLog} = useUbcLogV3();

    const [form] = Form.useForm();
    const [bosUrl, setBosUrl] = useState<string>();
    const [isLoading, setIsLoading] = useState(false);
    const [isError, setIsError] = useState(false);
    const [titleValidate, setTitleValidate] = useState(false);

    // 法务确认同意弹窗
    const {showConfirmModal} = useConfirmContext();
    const openAgreeSubmitModal = useCallback(
        (onConfirm: () => void) =>
            showConfirmModal({
                content: (
                    <div className="flex">
                        <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-primary"></span>
                        <div className="text-[14px] leading-[22px]">
                            您上传的视频将会同步在百家号发布，点击确定代表您同意
                            <a
                                href="https://baijiahao.baidu.com/docs/#/argument/BaiJiaHaoFuWuXieYiZaiYao/"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                《百家号平台服务协议》
                            </a>
                        </div>
                    </div>
                ),
                onConfirm: async () => {
                    await beginnerGuideApi.recordPopup({name: PopupName.Q2CSubmitBjhVideo});
                    onConfirm();
                },
                onCancel: async () => {
                    setOverlayType?.(OverlayType.None);
                },
            }),
        [setOverlayType, showConfirmModal]
    );

    // 监听表单字段变化，更新按钮禁用状态
    const values = Form.useWatch([], form);
    useEffect(() => {
        form.validateFields({validateOnly: false})
            .then(() => {
                setTitleValidate(true);
            })
            .catch(() => {
                setTitleValidate(false);
            });
    }, [values, form]);

    const handleReupload = useCallback(() => {
        setOpen(false);
        setVideoListModalOpen(true);
    }, [setOpen, setVideoListModalOpen]);

    const uploadToBos = useCallback(() => {
        setIsError(false);
        setIsLoading(true);
        setBosUrl(undefined);
        // 去掉文件名后缀
        const fileName = file?.name.substring(0, file?.name.lastIndexOf('.')) || file?.name;
        form.setFieldValue('name', fileName);

        file &&
            auditManagementApi
                .uploadFileToBos({
                    file,
                    appId,
                    tuningId,
                    type: FileType.q2cVideo,
                })
                .then(res => {
                    setBosUrl(res.url);
                })
                .catch(() => {
                    setIsError(true);
                })
                .finally(() => {
                    setIsLoading(false);
                });
    }, [appId, file, form, tuningId]);

    const handleClose = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    const addMlVideo = useCallback(
        (videoOptions: SetMlVideoOptions) => {
            if (!editor || !setOverlayType) return;
            const {src, duration, poster, videoId, height, width, title} = videoOptions;
            editor
                .chain()
                .focus()
                .setMlVideo({
                    src,
                    duration,
                    poster,
                    videoId,
                    auditStatus: VideoAuditStatus.Auditing,
                    height,
                    width,
                    title,
                })
                .run();
            setOverlayType(OverlayType.None);
        },
        [editor, setOverlayType]
    );

    const {changeVideoCover} = useBjhVideoContext();
    const [loading, setLoading] = useState(false);
    const handleAddClick = useCallback(async () => {
        if (!setOverlayType) return;
        /** B-38 上传视频弹窗-选中后添加 */
        clickLog(EVENT_VALUE_CONST.VIDEO_BOX_ADD, {
            ...logExt,
            eQaId: `${tuningId}`,
            eIsUploaded: 1,
        });
        setOverlayType(OverlayType.VideoInfo);
        const title = form.getFieldValue('name');
        if (!bosUrl || !title) {
            return;
        }

        const {show} = await beginnerGuideApi.getPopup({name: PopupName.Q2CSubmitBjhVideo});

        const submitVideo = () => {
            setLoading(true);
            getBjhVideoInfo({
                tuningId,
                videoUrl: bosUrl,
                title,
            })
                .then(async ({videoUrl, duration, coverUrl, width, height}) => {
                    return submitBjhVideo({
                        tuningId,
                        videoUrl,
                        title,
                        coverUrl,
                    }).then(async ({videoId}) => {
                        handleClose();
                        setLoading(false);
                        const videoOptions = {
                            src: videoUrl,
                            duration,
                            poster: coverUrl,
                            videoId,
                            auditStatus: VideoAuditStatus.Auditing,
                            height,
                            width,
                            title,
                        };

                        changeVideoCover({
                            videoUrl,
                            async onConfirm(newCoverUrl) {
                                try {
                                    await submitVideoCover({tuningId, coverUrl: newCoverUrl});
                                    addMlVideo({...videoOptions, poster: newCoverUrl});
                                    return true;
                                } catch (error) {
                                    return false;
                                }
                            },
                            onCancel() {
                                setOverlayType(OverlayType.None);
                            },
                        });
                    });
                })
                .catch(() => {
                    setLoading(false);
                    setOverlayType(OverlayType.None);
                });
        };

        if (show) {
            openAgreeSubmitModal(submitVideo);
        } else {
            submitVideo();
        }
    }, [
        addMlVideo,
        bosUrl,
        changeVideoCover,
        clickLog,
        form,
        handleClose,
        logExt,
        openAgreeSubmitModal,
        setOverlayType,
        tuningId,
    ]);

    useEffect(() => {
        uploadToBos();
    }, [uploadToBos]);

    return (
        <StyleModal
            centered
            width={600}
            open={open}
            title="添加视频"
            footer={
                <>
                    <Button
                        type="default"
                        shape="round"
                        className="h-[30px] border-[#EDEEF0] px-[15px] py-1 font-medium leading-[20px] hover:text-gray-tertiary"
                        onClick={handleClose}
                    >
                        取消
                    </Button>
                    <Button
                        type="primary"
                        shape="round"
                        className="h-[30px] px-[15px] py-1 font-medium leading-[20px]"
                        onClick={handleAddClick}
                        loading={loading}
                        disabled={isLoading || isError || !bosUrl || !titleValidate}
                    >
                        添加
                    </Button>
                </>
            }
            onCancel={handleClose}
        >
            {/* 加载态、失败态 */}
            <div className="bg-[#DCDDE04D] text-[14px] leading-[14px] text-[#000]">
                {isLoading && (
                    <div className="flex h-[200px] flex-col items-center justify-center gap-5">
                        <img src={loadingAnimation} className="w-[38px]" alt="加载中"></img>
                        <span>上传视频中，请勿离开</span>
                    </div>
                )}
                {isError && (
                    <div className="flex h-[200px] flex-col items-center justify-center gap-5">
                        <span className="iconfont icon-warn text-[38px] leading-[38px] text-[#FF0000]"></span>
                        <span>
                            上传失败,请
                            <span className="cursor-pointer text-[#4E6EF2]" onClick={handleReupload}>
                                重新上传
                            </span>
                        </span>
                    </div>
                )}
            </div>
            {/* 展示视频并设置视频名称 */}
            {bosUrl && (
                <div>
                    <video className="h-[310px] w-full rounded-xl" src={bosUrl} loop playsInline controls />
                    <div className="mt-[18px] flex h-[30px] gap-6">
                        <span className="shrink-0 text-[14px] leading-[30px]">
                            名称
                            <span className="ml-[6px] text-[#FF4D4F]" style={{fontFamily: 'SimSong'}}>
                                *
                            </span>
                        </span>
                        <Form className="w-full" form={form}>
                            <StyledFormItem
                                name="name"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            if (value.length > 30 || value.length < 8 || !titleRegex.test(value)) {
                                                return Promise.reject(
                                                    new Error('视频名称需为8-30个中英文字符，请重新输入')
                                                );
                                            }
                                            return Promise.resolve();
                                        },
                                    },
                                ]}
                                validateTrigger={['onBlur', 'onChange']}
                            >
                                <Input placeholder={'请输入视频名称'} className="border-none bg-colorBgFormList" />
                            </StyledFormItem>
                        </Form>
                    </div>
                </div>
            )}
        </StyleModal>
    );
}
