/**
 * @file Q2C 编辑器划词浮动菜单栏
 * <AUTHOR>
 */

import styled from '@emotion/styled';
import {Editor, BubbleMenu as DefaultBubbleMenu} from '@tiptap/react';
import {Dropdown, MenuProps} from 'antd';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import {getPopupContainer} from '@/utils/getPopupContainer';
import {TuningAction} from '@/api/q2c/interface';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {answerAITuning} from '@/api/q2c';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {BubbleMenuActionInfoMap, OverlayType, PopoverStatus} from '../constant';
import {useLogExt} from '../../../hooks/logExt';
import ActionPopover, {ActionInfo} from './ActionPopover';
import ActionButton from './ActionButton';

interface BubbleMenuProps {
    editor: Editor;
    overlayType: OverlayType;
    isSelectionInView: boolean;
}

const expandItems: MenuProps['items'] = [
    {key: TuningAction.Expand, label: <div className="text-[14px] leading-[20px] text-colorTextDefault">内容扩充</div>},
    {
        key: TuningAction.Amplify,
        label: <div className="text-[14px] leading-[20px] text-colorTextDefault">情感强化</div>,
    },
];

const tuningItems: MenuProps['items'] = [
    {
        key: TuningAction.Proofread,
        label: <div className="text-[14px] leading-[20px] text-colorTextDefault">文本校对</div>,
    },
    {
        key: TuningAction.Clarity,
        label: <div className="text-[14px] leading-[20px] text-colorTextDefault">结构清晰</div>,
    },
    {
        key: TuningAction.Enhance,
        label: <div className="text-[14px] leading-[20px] text-colorTextDefault">吸引力增强</div>,
    },
];

const StyledDropDownContainer = styled.div`
    .ant-dropdown-menu {
        padding: 6px !important;
    }
    .ant-dropdown-menu-item:hover {
        background-color: #f5f6fa !important;
    }
`;

const BubbleMenu = ({editor, overlayType, isSelectionInView}: BubbleMenuProps) => {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;

    const {tuningId, editorStates} = useQ2CStore(store => ({
        tuningId: store.activeTuningId,
        editorStates: store.editorStates,
    }));

    const abortControllerRef = useRef<AbortController>();
    const [popoverStatus, setPopoverStatus] = useState<PopoverStatus>(PopoverStatus.None);
    const [content, setContent] = useState('');
    const [actionInfo, setActionInfo] = useState<ActionInfo>();
    const [selectedText, setSelectedText] = useState('');

    const {clickLog} = useUbcLogV3();
    const {logExt} = useLogExt();

    const selectedAiTuning = useCallback(
        async (action?: TuningAction) => {
            // 获取选中文本内容
            const from = editor?.view.state.selection.from;
            const to = editor?.view.state.selection.to;
            const selectedText = editor?.state.doc.textBetween(from || 0, to || 0, '');

            action &&
                setActionInfo({
                    ...BubbleMenuActionInfoMap[action],
                    type: action,
                });

            abortControllerRef.current = new AbortController();
            setPopoverStatus(PopoverStatus.Loading);

            const fetchAnswer = async () => {
                try {
                    const res = await answerAITuning(
                        {
                            appId,
                            tuningId,
                            answer: editorStates[tuningId]?.content || '',
                            selected: selectedText,
                            action,
                        },
                        abortControllerRef.current?.signal
                    );
                    setContent(res.answer);
                    setPopoverStatus(PopoverStatus.Content);
                } catch (error) {
                    if (abortControllerRef.current?.signal.aborted) {
                        setPopoverStatus(PopoverStatus.None);
                    } else {
                        setPopoverStatus(PopoverStatus.Error);
                    }
                }
            };

            fetchAnswer();
        },
        [appId, editor, editorStates, tuningId]
    );

    const onClick: MenuProps['onClick'] = ({key}) => {
        /** B-25 划词功能使用点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_FUNCTION, {
            ...logExt,
            eWordSlideFunctionId: BubbleMenuActionInfoMap[Number(key) as TuningAction].logValue,
        });
        selectedAiTuning(Number(key));
    };

    // 确保监听只绑定一次
    useEffect(() => {
        const handleSelectionUpdate = () => {
            const {view, state} = editor;
            const {from, to} = view.state.selection;
            const selectedText = state.doc.textBetween(from, to, '');
            setSelectedText(selectedText);

            setPopoverStatus(PopoverStatus.None);
            setContent('');
        };

        editor.on('selectionUpdate', handleSelectionUpdate);
        return () => {
            editor.off('selectionUpdate', handleSelectionUpdate);
        };
    }, [editor]);

    // 满足以下三个条件，显示浮动菜单栏：
    // 1. 选中态且不处于上传图片状态
    // 2. 选中文本不为空
    // 3. 选中文本在视区内
    const showBubbleMenu = useMemo(() => {
        return (
            editor.isFocused &&
            overlayType !== OverlayType.UploadImage &&
            overlayType !== OverlayType.UploadVideo &&
            selectedText !== '' &&
            isSelectionInView
        );
    }, [editor.isFocused, isSelectionInView, overlayType, selectedText]);

    const cancelRequest = useCallback((e?: MouseEvent) => {
        abortControllerRef.current?.abort();
        e && e.stopPropagation();
    }, []);

    return (
        <DefaultBubbleMenu
            editor={editor}
            tippyOptions={{
                duration: 100,
                arrow: false,
                theme: 'q2c-editor-bubble-menu',
                placement: 'bottom-start',
                offset: [10, 12],
            }}
        >
            {showBubbleMenu &&
                (popoverStatus === PopoverStatus.None ? (
                    <div
                        className="flex gap-[6px] p-[6px] text-[14px] leading-[14px] text-colorTextDefault"
                        // 防止选中的文本失焦
                        onMouseDown={e => {
                            e.preventDefault();
                        }}
                    >
                        <StyledDropDownContainer>
                            <Dropdown
                                menu={{items: expandItems, onClick}}
                                placement="bottomRight"
                                trigger={['hover']}
                                align={{offset: [37, 12], overflow: {adjustX: false, adjustY: false}}}
                                getPopupContainer={getPopupContainer}
                                overlayStyle={{width: '115px'}}
                            >
                                <div>
                                    <ActionButton
                                        actionInfo={BubbleMenuActionInfoMap[TuningAction.Expand]}
                                        isDropdown
                                    />
                                </div>
                            </Dropdown>
                        </StyledDropDownContainer>

                        <ActionButton
                            actionInfo={BubbleMenuActionInfoMap[TuningAction.Summarize]}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => selectedAiTuning(TuningAction.Summarize)}
                        />

                        <StyledDropDownContainer>
                            <Dropdown
                                menu={{items: tuningItems, onClick}}
                                placement="bottomRight"
                                trigger={['hover']}
                                align={{offset: [44, 12], overflow: {adjustX: false, adjustY: false}}}
                                getPopupContainer={getPopupContainer}
                                overlayStyle={{width: '115px'}}
                            >
                                <div>
                                    <ActionButton
                                        actionInfo={BubbleMenuActionInfoMap[TuningAction.Proofread]}
                                        isDropdown
                                    />
                                </div>
                            </Dropdown>
                        </StyledDropDownContainer>

                        <ActionButton
                            actionInfo={BubbleMenuActionInfoMap[TuningAction.Emoji]}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => selectedAiTuning(TuningAction.Emoji)}
                        />
                    </div>
                ) : (
                    <ActionPopover
                        actionInfo={actionInfo}
                        status={popoverStatus}
                        content={content}
                        editor={editor}
                        setStatus={setPopoverStatus}
                        regenerate={selectedAiTuning}
                        handleCancel={cancelRequest}
                    />
                ))}
        </DefaultBubbleMenu>
    );
};

export default BubbleMenu;
