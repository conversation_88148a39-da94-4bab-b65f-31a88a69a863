import {TuningAction} from '@/api/q2c/interface';
import {EEditToolId, EWordSlideFunctionId} from '@/utils/loggerV2/interface';

/**
 * 编辑器蒙层类型
 */
export enum OverlayType {
    None = 'none',
    AITuning = 'aiTuning',
    UploadImage = 'uploadImage',
    UploadVideo = 'uploadVideo',
    Loading = 'loaing',
    Regenerate = 'regenerate',
    Emoji = 'emoji',
    Summarize = 'summarize',
    Expand = 'expand',
    VideoInfo = 'videoInfo',
}

/**
 * 划词气泡状态
 */
export enum PopoverStatus {
    None = 'none',
    Loading = 'loading',
    Error = 'error',
    Content = 'content',
}

/**
 * 划词气泡按钮信息
 */
export const BubbleMenuActionInfoMap = {
    [TuningAction.Proofread]: {
        icon: 'icon-imagine1',
        name: '润色',
        logValue: EWordSlideFunctionId.Proofread,
    },
    [TuningAction.Clarity]: {
        icon: 'icon-imagine1',
        name: '润色',
        logValue: EWordSlideFunctionId.Clarity,
    },
    [TuningAction.Enhance]: {
        icon: 'icon-imagine1',
        name: '润色',
        logValue: EWordSlideFunctionId.Enhance,
    },
    [TuningAction.Expand]: {
        icon: 'icon-Write',
        name: '扩写',
        logValue: EWordSlideFunctionId.Expand,
    },
    [TuningAction.Amplify]: {
        icon: 'icon-Write',
        name: '扩写',
        logValue: EWordSlideFunctionId.Amplify,
    },
    [TuningAction.Summarize]: {
        icon: 'icon-Summarize',
        name: '总结',
        logValue: EWordSlideFunctionId.Summarize,
    },
    [TuningAction.Emoji]: {
        icon: 'icon-emoji',
        name: '表情',
        errorText: '添加表情',
        logValue: EWordSlideFunctionId.Emoji,
    },
};

/**
 * 顶部菜单按钮信息
 */
export const MenuActionInfoMap = {
    [TuningAction.Proofread]: {overlayType: OverlayType.AITuning, logValue: EEditToolId.Proofread},
    [TuningAction.Clarity]: {overlayType: OverlayType.AITuning, logValue: EEditToolId.Clarity},
    [TuningAction.Enhance]: {overlayType: OverlayType.AITuning, logValue: EEditToolId.Enhance},
    [TuningAction.Emoji]: {overlayType: OverlayType.Emoji, logValue: EEditToolId.Emoji},
    [TuningAction.Amplify]: {overlayType: OverlayType.Expand, logValue: EEditToolId.Amplify},
    [TuningAction.Expand]: {overlayType: OverlayType.Expand, logValue: EEditToolId.Expand},
    [TuningAction.Summarize]: {overlayType: OverlayType.Summarize, logValue: EEditToolId.Summarize},
};

// 视频操作
export enum VideoAction {
    EditPoster = 1, // 编辑封面
    DeleteAndUnlist = 3, // 删除并下架
}
