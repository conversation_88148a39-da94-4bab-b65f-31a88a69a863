/**
 * @file 编辑器顶部菜单栏
 * <AUTHOR>
 */

import {type Editor} from '@tiptap/core';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {Dropdown, MenuProps, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useSearchParams} from 'react-router-dom';
import {FileType} from '@/api/auditManagement/interface';
import auditManagementApi from '@/api/auditManagement';
import {answerAITuning, generateVideo, regenerateAnswer} from '@/api/q2c';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AnswerStatus, QA, QAStatus, QAVideoContentItem, TuningAction} from '@/api/q2c/interface';
import {getPopupContainer} from '@/utils/getPopupContainer';
import {EEditToolId} from '@/utils/loggerV2/interface';
import beginnerGuideApi from '@/api/beginnerGuide';
import {PopupName} from '@/api/beginnerGuide/interface';
import {TiptapEditorProps} from '../useTiptapEditor';
import {MenuActionInfoMap, OverlayType} from '../constant';
import {useQ2CList} from '../../../hooks/useQ2CList';
import {useDigitalImageVideoContext} from '../../../context/DigitalImageVideoContext';
import {useCoverEditContext} from '../../../context/CoverEditContext';
import {useBjhVideoContext} from '../../../context/BjhVideoContext';
import {containsVideo, removeVideoAndExtract, formatAnswer, extractLatestVideoContent} from '../../../utils';
import {BjhModalType, VideoGenerateStatus, VideoType} from '../../../constant';
import {useConfirmContext} from '../../../../context/ConfirmContext';
import ActionButton from './ActionButton';

interface MenuBarProps {
    /**
     * 编辑器对象
     */
    editor: Editor;
    /**
     * 遮罩类型
     */
    overlayType: OverlayType;
    /**
     * 修改加载态类型
     */
    setOverlayType: (overlayType: OverlayType) => void;
    editorProps: TiptapEditorProps;
    QA: QA;
    index: number;
}

// q2c v0.3暂时隐藏图片、视频上传入口
const showImage = false;

const itemClassName = 'text-black-base text-[14px] leading-[20px]';

const expandItems: MenuProps['items'] = [
    {key: TuningAction.Expand, label: <div className={itemClassName}>内容扩充</div>},
    {key: TuningAction.Amplify, label: <div className={itemClassName}>情感强化</div>},
];
const tuningItems: MenuProps['items'] = [
    {key: TuningAction.Proofread, label: <div className={itemClassName}>文本校对</div>},
    {key: TuningAction.Clarity, label: <div className={itemClassName}>结构清晰</div>},
    {key: TuningAction.Enhance, label: <div className={itemClassName}>吸引力增强</div>},
];

enum VideoAction {
    Bjh = 1,
    Digital = 2,
}

const StyledDropDownContainer = styled.div`
    .ant-dropdown-menu {
        padding: 6px !important;
    }
    .ant-dropdown-menu-item:hover {
        background-color: #f5f6fa !important;
    }
`;

const MAX_IMAGE_COUNT = 9;
const MAX_VIDEO_COUNT = 1;

const DEFAULT_VIDEO_TIP = '添加视频，视频大小200M以下，支持格式mp4, .mov, .mkv, .avi, .flv, .mpeg, .webm, .wmv';
const DEFAULT_IMAGE_TIP = `上传图片，图片大小50M以下，最多上传${MAX_IMAGE_COUNT}张，支持格式jpg、jpeg、png`;

const MenuBar = forwardRef(({editor, overlayType, setOverlayType, editorProps, QA, index}: MenuBarProps, ref) => {
    const {tuningId, question} = QA;
    const abortControllerRef = useRef<AbortController>();
    const {saveContent} = editorProps;

    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId')!;

    const {updateQ2CListByIndex} = useQ2CList();

    const {editorStates, q2cVideoEnabled} = useQ2CStore(store => ({
        editorStates: store.editorStates,
        q2cVideoEnabled: store.q2cVideoEnabled,
    }));
    const isEditing = useMemo(() => {
        return editorStates[tuningId]?.isEditing || false;
    }, [editorStates, tuningId]);
    const {clickLog} = useUbcLogV3();

    const containerRef = useRef<HTMLDivElement>(null);

    const fetchAnswer = useCallback(
        // eslint-disable-next-line complexity
        async (action?: TuningAction) => {
            let editorContent = editorStates[tuningId]?.content || '';
            let videoContentMarkdown = '';
            const isRegenerate = editorContent === '';
            const isContainsVideo = containsVideo(editorContent);
            const isTuning = action && action !== TuningAction.Emoji && isContainsVideo;
            const videoContent = extractLatestVideoContent(
                editorStates[tuningId]?.jsonContent || {}
            ) as unknown as QAVideoContentItem[];
            try {
                // 只有重新生成需要更新状态
                isRegenerate &&
                    updateQ2CListByIndex(
                        {
                            ...QA,
                            status: QAStatus.Generating,
                            failStatus: AnswerStatus.Correct,
                        },
                        index
                    );
                if (isTuning) {
                    const {content, matchesVideo} = removeVideoAndExtract(editorContent);
                    editorContent = content;
                    videoContentMarkdown = matchesVideo;
                }

                let res =
                    editorContent === ''
                        ? await regenerateAnswer({appId, question, tuningId}, abortControllerRef.current?.signal)
                        : await answerAITuning(
                              {
                                  appId,
                                  tuningId,
                                  answer: editorContent,
                                  action,
                              },
                              abortControllerRef.current?.signal
                          );
                if (isTuning) {
                    res = {
                        answer: videoContentMarkdown + res.answer,
                    };
                }

                editor.commands.setContent(
                    formatAnswer({
                        ...QA,
                        videoContent,
                        answer: res.answer,
                    }),
                    true
                );
                setOverlayType(OverlayType.None);
                isRegenerate &&
                    updateQ2CListByIndex(
                        {
                            ...QA,
                            ...res,
                        },
                        index
                    );
            } catch (error) {
                setOverlayType(OverlayType.None);
                isRegenerate &&
                    updateQ2CListByIndex(
                        {
                            ...QA,
                            status: QAStatus.Generating,
                            failStatus: AnswerStatus.Error,
                        },
                        index
                    );
            }
        },
        [editorStates, tuningId, updateQ2CListByIndex, QA, index, appId, question, editor.commands, setOverlayType]
    );

    // 全文AI润色功能
    const aiTuning = useCallback(
        async (action?: TuningAction) => {
            /** B-8 调优页面AI润色 */
            !action && clickLog(EVENT_VALUE_CONST.OPT_AI_POLISH);

            abortControllerRef.current = new AbortController();
            setOverlayType(action ? MenuActionInfoMap[action].overlayType : OverlayType.AITuning);

            fetchAnswer(action);
        },
        [clickLog, setOverlayType, fetchAnswer]
    );

    // 上传图片
    const uploadImageToServer = useCallback(
        async (file: File): Promise<string | undefined> => {
            abortControllerRef.current = new AbortController();
            setOverlayType(OverlayType.UploadImage);

            return auditManagementApi
                .uploadFileToBos(
                    {
                        file,
                        appId,
                        tuningId,
                        type: FileType.qaImage,
                    },
                    abortControllerRef.current?.signal
                )
                .then(res => {
                    setOverlayType(OverlayType.None);
                    return res.url;
                })
                .catch(() => {
                    setOverlayType(OverlayType.None);
                    return undefined;
                });
        },
        [appId, setOverlayType, tuningId]
    );

    const uploadImage = useCallback(() => {
        /** B-4 调优页面上传图片 */
        clickLog(EVENT_VALUE_CONST.OPT_UPLOAD_IMAGE);

        editor.chain().focus().uploadImage({upload: uploadImageToServer}).run();
    }, [clickLog, editor, uploadImageToServer]);

    // 每隔 2 分钟执行一次 saveTuning
    useEffect(() => {
        const intervalId = setInterval(() => {
            isEditing && overlayType === OverlayType.None && saveContent();
        }, 120 * 1000);

        // 卸载时清除定时器
        return () => clearInterval(intervalId);
    }, [isEditing, saveContent, overlayType]);

    // 编辑封面弹窗
    const {addCover} = useCoverEditContext();
    const handleEditCover = useCallback(() => {
        /** B-15 调优页面修改封面点击 */
        clickLog(EVENT_VALUE_CONST.OPT_CHANGE_COVER);

        addCover();
    }, [addCover, clickLog]);

    useImperativeHandle(ref, () => {
        return {
            cancelRequest: () => {
                abortControllerRef.current?.abort();
            },
        };
    });

    // 根据插入多媒体的个数，决定是否展示『修改封面』按钮
    // 多媒体数量限制：
    // 1. 图片数量限制9个
    // 2. 视频数量限制1个
    // 3. 不允许同时上传图片和视频
    const [imageCount, setImageCount] = useState(0);
    const [videoCount, setVideoCount] = useState(0);
    const [videoTip, setVideoTip] = useState(DEFAULT_VIDEO_TIP);
    const [imageTip, setImageTip] = useState(DEFAULT_IMAGE_TIP);
    useEffect(() => {
        const handleUpdate = () => {
            const html = editor.getHTML();
            const imageCount = (html.match(/<img [^>]*src="[^"]*"[^>]*>/g) || []).length;
            const videoCount = (html.match(/<\/ml-video>/g) || []).length;
            setImageCount(imageCount);
            setVideoCount(videoCount);
            setImageTip(videoCount > 0 ? '单条内容仅支持上传图片或视频的一种' : DEFAULT_IMAGE_TIP);
            setVideoTip(imageCount > 0 ? '单条内容仅支持上传图片或视频的一种' : DEFAULT_VIDEO_TIP);
            setVideoTip(videoCount > 0 ? '单个调优内容只允许挂载1条视频' : DEFAULT_VIDEO_TIP);
        };

        editor.on('update', handleUpdate);
        return () => {
            editor.off('update', handleUpdate);
        };
    }, [editor]);

    // 撤销
    const handleUndo = useCallback(() => {
        editor.can().undo() && editor.chain().focus().undo().run();
    }, [editor]);

    // 重做
    const handleRedo = useCallback(() => {
        editor.can().redo() && editor.chain().focus().redo().run();
    }, [editor]);

    const onClick: MenuProps['onClick'] = ({key}) => {
        /** B-24 编辑器顶部按钮点击 */
        clickLog(EVENT_VALUE_CONST.EDIT_TOOL, {
            eEditToolId: MenuActionInfoMap[Number(key) as TuningAction].logValue,
            eQaId: `${tuningId}`,
        });
        aiTuning(Number(key));
    };

    // 法务确认同意弹窗
    const {showConfirmModal} = useConfirmContext();
    const openAgreeSubmitModal = useCallback(
        (onConfirm: () => void) =>
            showConfirmModal({
                content: (
                    <div className="flex">
                        <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-primary"></span>
                        <div className="text-[14px] leading-[22px]">
                            您创建的数字人视频将会同步在百家号发布，点击确定代表您同意
                            <a
                                href="https://baijiahao.baidu.com/docs/#/argument/BaiJiaHaoFuWuXieYiZaiYao/"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                《百家号平台服务协议》
                            </a>
                        </div>
                    </div>
                ),
                onConfirm: async () => {
                    await beginnerGuideApi.recordPopup({name: PopupName.Q2CSubmitDigitalHumanVideo});
                    onConfirm();
                },
            }),
        [showConfirmModal]
    );

    const {createDigitalImageVideo, dynamicFigureAccess} = useDigitalImageVideoContext();
    const {createBjhVideo, checkBjhAccount} = useBjhVideoContext();
    const videoItems: MenuProps['items'] = [
        {
            key: VideoAction.Bjh,
            label: (
                <Tooltip title={videoTip} placement="right" align={{offset: [16, 0]}}>
                    <div className={itemClassName}>添加视频</div>
                </Tooltip>
            ),
            onClick: async () => {
                /** B-6 调优页面上传视频（Q2C0.6新增eQaId） */
                clickLog(EVENT_VALUE_CONST.OPT_UPLOAD_VIDEO);

                const accountAvailable = await checkBjhAccount(BjhModalType.AddVideo);
                if (!accountAvailable) return;

                createBjhVideo({
                    QA,
                    editor,
                    setOverlayType,
                });
            },
        },
        {
            key: VideoAction.Digital,
            label: (
                <Tooltip title={editor?.isEmpty ? '请先填写调优回答' : ''} placement="right" align={{offset: [16, 0]}}>
                    <div className={itemClassName}>创建数字人视频</div>
                </Tooltip>
            ),
            onClick: async () => {
                /** B-39 创建数字人视频按钮点击 */
                clickLog(EVENT_VALUE_CONST.CREATE_SZR_VIDEO);

                const accountAvailable = await checkBjhAccount(BjhModalType.GenerateDigitalImage);
                if (!accountAvailable) return;

                createDigitalImageVideo({
                    QA,
                    async onConfirm(values) {
                        try {
                            const {show} = await beginnerGuideApi.getPopup({
                                name: PopupName.Q2CSubmitDigitalHumanVideo,
                            });
                            const generate = async () => {
                                setOverlayType(OverlayType.VideoInfo);
                                try {
                                    const {coverUrl, videoId} = await generateVideo(values);

                                    if (!videoId) {
                                        throw new Error('videoId 为空');
                                    }

                                    editor
                                        .chain()
                                        .focus()
                                        .setMlVideo({
                                            src: '',
                                            poster: coverUrl,
                                            videoId,
                                            generateStatus: VideoGenerateStatus.Generating,
                                            videoType: VideoType.DigitalHuman,
                                            title: values.title,
                                        })
                                        .run();

                                    setOverlayType(OverlayType.None);
                                    return true;
                                } catch (error) {
                                    setOverlayType(OverlayType.None);
                                    return false;
                                }
                            };

                            if (show) {
                                openAgreeSubmitModal(generate);
                                return true;
                            } else {
                                return generate();
                            }
                        } catch (error) {
                            setOverlayType(OverlayType.None);
                            console.error(error);
                            return false;
                        }
                    },
                });
            },
            disabled: editor?.isEmpty,
        },
    ].filter(item => !(item.key === VideoAction.Digital && !dynamicFigureAccess));

    return (
        <StyledDropDownContainer>
            <div
                className="no-scrollbar flex h-[34px] w-full items-center justify-between overflow-y-auto rounded-t-xl bg-white px-3"
                ref={containerRef}
            >
                <div
                    className={`${
                        overlayType === OverlayType.None ? '' : 'pointer-events-none opacity-40'
                    } flex cursor-default items-center gap-[14px] text-[16px] font-normal text-gray-secondary`}
                >
                    <ActionButton
                        icon="icon-redo"
                        disabled={!editor.can().undo()}
                        onClick={handleUndo}
                        tooltipOptions={{
                            content: '撤销',
                        }}
                        logValue={EEditToolId.Undo}
                    />
                    <ActionButton
                        icon="icon-undo"
                        disabled={!editor.can().redo()}
                        onClick={handleRedo}
                        tooltipOptions={{
                            content: '恢复',
                        }}
                        logValue={EEditToolId.Redo}
                    />
                    <span className="mb-[2px] text-[14px] text-colorBorderFormList">|</span>
                    <ActionButton
                        icon="icon-title"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => editor.chain().focus().toggleHeading({level: 3}).run()}
                        tooltipOptions={{
                            content: '标题',
                        }}
                        logValue={EEditToolId.Heading}
                    />
                    <ActionButton
                        icon="icon-a-Unorderedlist1"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => editor.chain().focus().toggleBulletList().run()}
                        tooltipOptions={{
                            content: '无序列表',
                        }}
                        logValue={EEditToolId.BulletList}
                    />
                    <ActionButton
                        icon="icon-a-OrderedList"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => editor.chain().focus().toggleOrderedList().run()}
                        tooltipOptions={{
                            content: '有序列表',
                        }}
                        logValue={EEditToolId.OrderedList}
                    />
                    <ActionButton
                        icon="icon-Summarize"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => aiTuning(TuningAction.Summarize)}
                        tooltipOptions={{
                            content: '总结',
                        }}
                        logValue={EEditToolId.Summarize}
                    />

                    <Dropdown
                        menu={{items: expandItems, onClick}}
                        placement="topLeft"
                        align={{offset: [0, -6]}}
                        getPopupContainer={getPopupContainer}
                        overlayStyle={{width: '115px'}}
                    >
                        <ActionButton
                            icon="icon-Write"
                            tooltipOptions={{
                                content: '扩写',
                            }}
                            isDropdown
                        />
                    </Dropdown>

                    <span className="mb-[2px] text-[14px] text-colorBorderFormList">|</span>
                    <ActionButton
                        icon="icon-References"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => editor.chain().focus().toggleBlockquote().run()}
                        tooltipOptions={{
                            content: '引用',
                        }}
                        logValue={EEditToolId.Blockquote}
                    />
                    <ActionButton
                        icon="icon-a-DividingLine"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => editor.chain().focus().setHorizontalRule().run()}
                        tooltipOptions={{
                            content: '分割线',
                        }}
                        logValue={EEditToolId.HorizontalRule}
                    />
                    <Dropdown
                        menu={{items: tuningItems, onClick}}
                        placement="topLeft"
                        align={{offset: [0, -6]}}
                        getPopupContainer={getPopupContainer}
                        overlayStyle={{width: '115px'}}
                    >
                        <ActionButton
                            icon="icon-imagine1"
                            tooltipOptions={{
                                content: '全文智能润色',
                                align: {offset: [40, -12]},
                            }}
                            isDropdown
                        />
                    </Dropdown>
                    <span className="mb-[2px] text-[14px] text-colorBorderFormList">|</span>
                    {showImage && (
                        <ActionButton
                            icon="icon-picture"
                            onClick={uploadImage}
                            tooltipOptions={{
                                content: imageTip,
                                align: {offset: [20, -12]},
                            }}
                            disabled={videoCount > 0 || imageCount >= MAX_IMAGE_COUNT}
                        />
                    )}
                    {q2cVideoEnabled && (
                        <Dropdown
                            align={{offset: [0, -6]}}
                            menu={{items: videoItems}}
                            disabled={videoCount >= MAX_VIDEO_COUNT}
                            placement="topLeft"
                            getPopupContainer={getPopupContainer}
                        >
                            <ActionButton
                                icon="icon-video"
                                tooltipOptions={{
                                    content: videoCount >= MAX_VIDEO_COUNT ? videoTip : '',
                                    align: {offset: [18, -12]},
                                }}
                                disabled={videoCount >= MAX_VIDEO_COUNT}
                            />
                        </Dropdown>
                    )}
                    <ActionButton
                        icon="icon-emoji"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => aiTuning(TuningAction.Emoji)}
                        tooltipOptions={{
                            content: '添加表情',
                        }}
                        logValue={EEditToolId.Emoji}
                    />
                    {videoCount + imageCount > 1 && showImage && (
                        <>
                            <span className="mb-[2px] text-[14px] text-colorBorderFormList">|</span>
                            <span
                                className="flex-shrink-0 text-[14px] font-semibold hover:text-primary"
                                onClick={handleEditCover}
                            >
                                修改封面
                            </span>
                        </>
                    )}
                </div>
            </div>
        </StyledDropDownContainer>
    );
});

export default MenuBar;
