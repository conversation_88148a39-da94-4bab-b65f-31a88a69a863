import {Editor, Extension} from '@tiptap/core';

interface UploadImageOptions {
    upload: (file: File) => Promise<string | undefined>;
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        imageUploader: {
            uploadImage: (options: UploadImageOptions) => ReturnType;
        };
    }
}

export const ImageUploader = Extension.create({
    name: 'imageUploader',

    addCommands() {
        return {
            uploadImage:
                (options: UploadImageOptions) =>
                ({editor}: {editor: Editor}) => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.jpg,.jpeg,.png';
                    input.onchange = async () => {
                        const file = input?.files?.[0];
                        if (file) {
                            const src = await options.upload(file);
                            src && editor.chain().focus().setImage({src}).createParagraphNear().scrollIntoView().run();
                        }
                    };

                    input.click();
                    return true;
                },
        };
    },
});
