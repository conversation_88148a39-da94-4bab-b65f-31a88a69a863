/**
 * @file 视频状态 Tag 组件
 * <AUTHOR>
 */

import React, {useMemo} from 'react';
import {VideoAuditStatus, VideoGenerateStatus} from '../../constant';

interface VideoTagProps {
    auditStatus?: VideoAuditStatus;
    generateStatus?: VideoGenerateStatus;
}

interface TagInfo {
    color: string;
    text: string;
}

const AuditStatusTagMap: Record<VideoAuditStatus, TagInfo | undefined> = {
    [VideoAuditStatus.Auditing]: {
        color: '#0066FF',
        text: '审核中',
    },
    [VideoAuditStatus.AuditFail]: {
        color: '#FF3333',
        text: '审核失败',
    },
    [VideoAuditStatus.AuditSuccess]: undefined,
};

const GeneratStatusTagMap: Record<VideoGenerateStatus, TagInfo | undefined> = {
    [VideoGenerateStatus.Generating]: {
        color: '#E87400',
        text: '生成中',
    },
    [VideoGenerateStatus.GenerateFail]: {
        color: '#FF3333',
        text: '生成失败',
    },
    [VideoGenerateStatus.New]: {
        color: '#E87400',
        text: '生成中',
    },
    [VideoGenerateStatus.GenerateSuccess]: undefined,
    [VideoGenerateStatus.Default]: undefined,
};

export default function VideoTag({auditStatus, generateStatus}: VideoTagProps) {
    const tag: TagInfo | undefined = useMemo(() => {
        // 优先展示生成状态 tag
        if (generateStatus) {
            return GeneratStatusTagMap[generateStatus];
        }

        if (auditStatus) {
            return AuditStatusTagMap[auditStatus];
        }
    }, [auditStatus, generateStatus]);

    return (
        tag && (
            <div
                className="rounded-[3px] px-[3px] py-[2.5px] text-[11px] font-medium leading-[11px] text-white"
                style={{
                    backgroundColor: tag.color,
                }}
            >
                {tag.text}
            </div>
        )
    );
}
