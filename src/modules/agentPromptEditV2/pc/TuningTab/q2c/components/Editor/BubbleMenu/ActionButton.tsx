/**
 * @file 编辑器浮动菜单栏按钮
 * <AUTHOR>
 */

import {useCallback} from 'react';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';

interface ActionButtonProps {
    actionInfo: {
        icon: string;
        name: string;
        logValue: number;
    };
    isDropdown?: boolean;
    onClick?: () => void;
}

const ActionButton = ({actionInfo, isDropdown = false, onClick}: ActionButtonProps) => {
    const {clickLog} = useUbcLogV3();

    const handleClick = useCallback(() => {
        /** B-25 划词功能使用点击 */
        clickLog(EVENT_VALUE_CONST.WORD_SLIDE_FUNCTION, {
            eWordSlideFunctionId: actionInfo.logValue,
        });
        onClick && onClick();
    }, [actionInfo.logValue, clickLog, onClick]);

    return (
        <div
            className="flex cursor-pointer items-center gap-[3px] rounded-[6px] px-[6px] py-[7px] text-colorTextDefault hover:bg-colorBgFormList"
            onClick={handleClick}
        >
            <span className={`iconfont ${actionInfo.icon}`} />
            <span>{actionInfo.name}</span>
            {isDropdown && <span className="iconfont icon-expand1 pl-[1px] text-[7px]"></span>}
        </div>
    );
};

export default ActionButton;
