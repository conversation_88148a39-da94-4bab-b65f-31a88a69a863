/* eslint-disable new-cap */
/**
 * @file MlVideo 自定义 tiptap extension
 * <AUTHOR>
 */

import {Node} from '@tiptap/core';
import {ReactNodeViewRenderer} from '@tiptap/react';
import {VideoAuditStatus, VideoGenerateStatus} from '../../../constant';
import {MlVideoComponent} from './components/MlVideo';

export interface SetMlVideoOptions {
    src: string;
    poster: string;
    duration?: number;
    auditStatus?: VideoAuditStatus;
    auditMsg?: string;
    generateStatus?: VideoGenerateStatus;
    generateMsg?: string;
    videoType?: number;
    title?: string;
    // 以下属性不做展示，仅为了记录
    width?: number;
    height?: number;
    videoId?: number;
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        MlVideo: {
            setMlVideo: (options: SetMlVideoOptions) => ReturnType;
        };
    }
}

const MlVideo = Node.create({
    name: 'mlVideo',

    group: 'block',

    atom: true,

    draggable: true,

    addAttributes() {
        return {
            src: {
                default: null,
            },
            duration: {
                default: null,
            },
            auditStatus: {
                default: null,
            },
            poster: {
                default: null,
            },
            width: {
                default: null,
            },
            height: {
                default: null,
            },
            generateStatus: {
                default: null,
            },
            generateMsg: {
                default: null,
            },
            auditMsg: {
                default: null,
            },
            videoType: {
                default: null,
            },
            videoId: {
                default: null,
            },
            title: {
                default: null,
            },
        };
    },

    parseHTML() {
        return [
            {
                tag: 'ml-video',
            },
        ];
    },

    renderHTML({HTMLAttributes}) {
        return ['ml-video', HTMLAttributes];
    },

    addNodeView() {
        return ReactNodeViewRenderer(MlVideoComponent);
    },

    addCommands() {
        return {
            setMlVideo:
                options =>
                ({commands}) => {
                    return commands.insertContent({
                        type: this.name,
                        attrs: options,
                    });
                },
        };
    },
});

export default MlVideo;
