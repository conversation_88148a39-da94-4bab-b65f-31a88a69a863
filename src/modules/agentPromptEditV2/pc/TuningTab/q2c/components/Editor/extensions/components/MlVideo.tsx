/* eslint-disable complexity */
/**
 * @file MlVideo 组件
 * <AUTHOR>
 */

import {NodeViewWrapper, NodeViewProps} from '@tiptap/react';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ConfigProvider, Dropdown, message, Slider, Tooltip} from 'antd';
import {Slice} from '@tiptap/pm/model';
import spinPng from '@/assets/spin-white.png';
import {submitVideoCover, getBjhVideoStatus, deleteVideo} from '@/api/q2c';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {getPopupContainer} from '@/utils/getPopupContainer';
import VideoTag from '../../VideoTag';
import {formatVideoDuration} from '../../../../utils';
import {VideoAuditStatus, VideoGenerateStatus, VideoType} from '../../../../constant';
import editorExtensionEmitter from '../emitter';
import {useConfirmContext} from '../../../../../context/ConfirmContext';
import {useBjhVideoContext} from '../../../../context/BjhVideoContext';
import {useDigitalImageVideoContext} from '../../../../context/DigitalImageVideoContext';
import {useQ2CEditorContext} from '../../../../context/Q2CEditorContext';
import {useQ2CList} from '../../../../hooks/useQ2CList';
import {VideoAction} from '../../constant';

interface MlVideoProps extends NodeViewProps {
    updateAttributes: (attrs: Record<string, any>) => void;
}

const BottomTitle = ({title, duration}: {title: string; duration?: string}) => {
    return (
        <div
            className="absolute bottom-[0] flex h-[30px] w-full justify-between overflow-hidden rounded-b-[9px] px-[8px] text-[12px] font-medium leading-[30px] text-white"
            style={{
                background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.4) 100%)',
            }}
        >
            <span className="overflow-hidden overflow-ellipsis whitespace-nowrap">{title}</span>
            <span className="no-wrap shrink-0">{duration}</span>
        </div>
    );
};

// eslint-disable-next-line complexity, max-statements
export const MlVideoComponent: React.FC<MlVideoProps> = props => {
    const {fetchAllQ2CList} = useQ2CList();
    const {clickLog} = useUbcLogV3();
    const {src, auditStatus, poster, duration, generateStatus, generateMsg, auditMsg, videoType, videoId, title} =
        props.node.attrs;

    const [isPlaying, setIsPlaying] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);
    const togglePlay = useCallback(() => {
        if (!videoRef.current) return;

        if (isPlaying) {
            videoRef.current.pause();
        } else {
            videoRef.current.play();
        }

        setIsPlaying(!isPlaying);
    }, [isPlaying]);

    const [displayTime, setDisPlayTime] = useState(duration);
    // 数字人视频生成完成（包括 Default，Default 代表不需要生成），展示 video 组件
    const showVideo = useMemo(() => {
        if (videoType === VideoType.DigitalHuman) {
            return (
                generateStatus === VideoGenerateStatus.Default || generateStatus === VideoGenerateStatus.GenerateSuccess
            );
        }
        return true;
    }, [generateStatus, videoType]);

    // video time 更新监听事件
    useEffect(() => {
        const videoElement = videoRef.current;
        if (!videoElement) {
            return;
        }

        const timeUpdateEventCallback = () => {
            const currentTime = Math.floor(videoElement.currentTime);
            setDisPlayTime(currentTime);
        };

        videoElement.addEventListener('timeupdate', timeUpdateEventCallback);

        return () => {
            videoElement.removeEventListener('timeupdate', timeUpdateEventCallback);
        };
    }, []);

    // video progress 更新监听事件
    const [progress, setProgress] = useState(0);
    const handleTimeUpdate = useCallback(() => {
        if (videoRef.current) {
            const percentage = (videoRef.current.currentTime / videoRef.current.duration) * 100;
            setProgress(percentage);
        }
    }, []);
    const handleSeek = useCallback((value: number) => {
        if (videoRef.current) {
            const newTime = (value / 100) * videoRef.current.duration;
            videoRef.current.currentTime = newTime;
            setProgress(value);
        }
    }, []);

    const {QA} = useQ2CEditorContext();
    const {changeVideoCover} = useBjhVideoContext();
    const {changeDigitalVideoCover, dynamicFigureAccess} = useDigitalImageVideoContext();
    const handleChangePoster = useCallback(() => {
        /** B-47 编辑封面按钮点击 */
        clickLog(EVENT_VALUE_CONST.COVER_EDIT);
        if (videoType === VideoType.DigitalHuman) {
            changeDigitalVideoCover({
                QA,
                videoId,
                async onConfirm(data) {
                    try {
                        // 将新的封面图提交给server
                        await submitVideoCover({tuningId: QA.tuningId, coverUrl: data.coverUrl});
                        props.updateAttributes({
                            poster: data.coverUrl,
                        });
                        return true;
                    } catch (error) {
                        console.error(error);
                        return false;
                    }
                },
            });
            return;
        }

        changeVideoCover({
            QA,
            videoUrl: src,
            async onConfirm(newCover) {
                try {
                    // 将新的封面图提交给server
                    await submitVideoCover({tuningId: QA.tuningId, coverUrl: newCover});
                    props.updateAttributes({
                        poster: newCover,
                    });
                } catch (error) {
                    return false;
                }
                return true;
            },
        });
    }, [QA, changeDigitalVideoCover, changeVideoCover, clickLog, props, src, videoId, videoType]);

    const confirmDelete = useCallback(() => {
        const from = props.getPos();
        const to = from + props.node.nodeSize;
        const emptySlice = Slice.empty;
        const transaction = props.editor.state.tr.replace(from, to, emptySlice);

        transaction.setMeta('forceDelete', true);
        props.editor.view.dispatch(transaction);
    }, [props]);

    const {showConfirmModal} = useConfirmContext();

    const openModal = useCallback(() => {
        showConfirmModal({
            content: (
                <div className="flex">
                    <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-[#FF8200]"></span>
                    <div className="text-[14px] leading-[22px]">
                        确认删除视频吗？删除视频后可点击撤销恢复，重开页面后将不可恢复。
                    </div>
                </div>
            ),
            onConfirm: confirmDelete,
        });
    }, [confirmDelete, showConfirmModal]);

    useEffect(() => {
        editorExtensionEmitter.on('deleteMlVideo', openModal);
        return () => {
            editorExtensionEmitter.off('deleteMlVideo', openModal);
        };
    }, [confirmDelete, openModal, showConfirmModal]);

    // 删除并下架
    const deleteAndRemoveVideo = useCallback(async () => {
        try {
            await deleteVideo({videoId: videoId});
            // 刷新列表
            fetchAllQ2CList();
            message.success('删除成功');
        } catch (error: any) {
            message.error(error?.msg || '删除失败');
        }
    }, [fetchAllQ2CList, videoId]);

    const openDeleteModal = useCallback(() => {
        showConfirmModal({
            content: (
                <div className="flex">
                    <span className="iconfont icon-info-circle-fill mr-2 text-[16px] leading-[22px] text-[#FF8200]"></span>
                    <div className="text-[14px] leading-[22px]">
                        删除视频后，将同步在所有搜索问答和百家号中删除此条视频。视频将不可恢复，确认删除并下架吗？
                    </div>
                </div>
            ),
            onConfirm: deleteAndRemoveVideo,
        });
    }, [deleteAndRemoveVideo, showConfirmModal]);

    // 生成失败、审核中、审核失败的视频不支持编辑封面，生成中支持编辑封面
    const showEditPoster =
        (generateStatus !== VideoGenerateStatus.GenerateFail &&
            auditStatus !== VideoAuditStatus.AuditFail &&
            auditStatus !== VideoAuditStatus.Auditing) ||
        generateStatus === VideoGenerateStatus.Generating ||
        generateStatus === VideoGenerateStatus.New;

    const showChangePoster =
        !!poster && showEditPoster && (dynamicFigureAccess || videoType !== VideoType.DigitalHuman);

    const errorMsg =
        generateStatus === VideoGenerateStatus.GenerateFail
            ? generateMsg
            : auditStatus === VideoAuditStatus.AuditFail
            ? auditMsg
            : '';

    // 生成中、生成失败、审核中不支持 删除并下架， 审核失败、生成成功支持 删除并下架
    const showDeleteAndRemove =
        generateStatus !== VideoGenerateStatus.Generating &&
        generateStatus !== VideoGenerateStatus.GenerateFail &&
        auditStatus !== VideoAuditStatus.Auditing &&
        (auditStatus === VideoAuditStatus.AuditFail ||
            generateStatus === VideoGenerateStatus.GenerateSuccess ||
            auditStatus === VideoAuditStatus.AuditSuccess);

    const isPolling = useRef(false);
    const pollIntervalRef = useRef<NodeJS.Timer>();

    // 停止轮询
    const stopPolling = useCallback(() => {
        if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current);
            pollIntervalRef.current = undefined;
        }

        isPolling.current = false;
    }, []);

    // 轮询视频状态并更新
    const pollVideoStatus = useCallback(async () => {
        if (!QA?.tuningId || !videoId) {
            return;
        }

        try {
            const {videoStatuses} = await getBjhVideoStatus({tuningId: QA.tuningId});
            const videoStatus = videoStatuses.find(status => status.videoId === videoId);

            if (!videoStatus) {
                stopPolling();
                return;
            }

            // 更新审核状态
            if (videoStatus.status && videoStatus.status !== auditStatus) {
                props.updateAttributes({
                    auditStatus: videoStatus.status,
                    auditMsg: videoStatus.reason || '',
                });
            }

            // 更新生成状态
            if (videoStatus.generateStatus && videoStatus.generateStatus !== generateStatus) {
                props.updateAttributes({
                    generateStatus: videoStatus.generateStatus,
                    generateMsg: videoStatus.generateMsg || '',
                });
            }

            // 更新视频 url
            videoStatus.videoUrl &&
                props.updateAttributes({
                    src: videoStatus.videoUrl,
                });

            // 更新视频时长
            if (videoStatus.duration) {
                props.updateAttributes({
                    duration: videoStatus.duration,
                });
                setDisPlayTime(videoStatus.duration);
            }

            const needPolling =
                videoStatus.status === VideoAuditStatus.Auditing ||
                videoStatus.generateStatus === VideoGenerateStatus.Generating ||
                videoStatus.generateStatus === VideoGenerateStatus.New ||
                videoStatus.status === undefined;

            if (!needPolling) {
                stopPolling();
            }
        } catch (error) {
            stopPolling();
        }
    }, [QA.tuningId, videoId, auditStatus, generateStatus, props, stopPolling]);

    useEffect(() => {
        const shouldStartPolling =
            auditStatus === VideoAuditStatus.Auditing ||
            generateStatus === VideoGenerateStatus.Generating ||
            generateStatus === VideoGenerateStatus.New;

        if (shouldStartPolling && !isPolling.current) {
            isPolling.current = true;
            pollIntervalRef.current = setInterval(pollVideoStatus, 30 * 1000);
        }
    }, [auditStatus, generateStatus, pollVideoStatus]);

    useEffect(() => {
        return () => {
            stopPolling();
        };
    }, [stopPolling]);

    const videoActionItems = useMemo(() => {
        const actions = [
            {
                condition: showChangePoster,
                key: VideoAction.EditPoster,
                label: '编辑封面',
                onClick: handleChangePoster,
            },
            {
                condition: showDeleteAndRemove,
                key: VideoAction.DeleteAndUnlist,
                label: '删除并下架',
                onClick: openDeleteModal,
            },
        ];

        return actions
            .filter(action => action.condition)
            .map(({key, label, onClick}) => ({
                key,
                label,
                onClick,
            }));
    }, [handleChangePoster, openDeleteModal, showChangePoster, showDeleteAndRemove]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Slider: {
                        dotSize: 0,
                        handleLineWidth: 0,
                        handleLineWidthHover: 0,
                        handleSize: 0,
                        handleSizeHover: 0,
                        handleColor: 'rgba(255, 255, 255, 0.6)',
                        handleActiveColor: 'rgba(255, 255, 255, 0.6)',
                        colorBgElevated: ThemeConfig.token.colorPrimary,
                        railBg: 'rgba(0, 0, 0, 0.3)',
                        railHoverBg: 'rgba(0, 0, 0, 0.3)',
                        trackBg: 'rgba(255, 255, 255, 0.6)',
                        trackHoverBg: 'rgba(255, 255, 255, 0.6)',
                    },
                },
            }}
        >
            <Tooltip title={errorMsg} placement="top" align={{offset: [0, -10]}}>
                <Dropdown
                    align={{offset: [0, 4]}}
                    menu={{items: videoActionItems}}
                    placement="bottomRight"
                    getPopupContainer={getPopupContainer}
                    overlayStyle={{minWidth: 'auto', width: videoActionItems.length > 0 ? 'auto' : 0}}
                >
                    <NodeViewWrapper
                        className="ml-video-wrapper cursor-grab select-none rounded-[10px] border-[1px] border-solid border-[#DCDDE04D] active:cursor-grabbing"
                        draggable="true"
                        data-drag-handle
                    >
                        {showVideo ? (
                            <div className="relative rounded-[9px] bg-black">
                                <video
                                    src={src}
                                    poster={poster}
                                    ref={videoRef}
                                    onClick={togglePlay}
                                    className="m-auto h-[135px] w-[240px] rounded-[9px]"
                                    onTimeUpdate={handleTimeUpdate}
                                />

                                {/* 审核状态 */}
                                <div className="absolute left-[6px] top-[6px]">
                                    <VideoTag auditStatus={auditStatus} />
                                </div>

                                {/* 标题、时长 */}
                                {title ? (
                                    <BottomTitle title={title} duration={formatVideoDuration(displayTime)} />
                                ) : (
                                    <div
                                        className="absolute bottom-[0] flex w-[240px] justify-end overflow-hidden px-[6px] pb-[3px] pt-[7px] text-[12px] font-medium leading-[20px] text-white"
                                        style={{
                                            background:
                                                'linear-gradient(180deg, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.4) 100%)',
                                        }}
                                    >
                                        <span className="no-wrap shrink-0">{formatVideoDuration(displayTime)}</span>
                                    </div>
                                )}

                                {isPlaying ? (
                                    // 进度条 & 播放进度
                                    <div className="absolute bottom-[-4px] flex h-[30px] w-full items-end overflow-hidden rounded-[15px]">
                                        <Slider
                                            min={0}
                                            max={100}
                                            value={progress}
                                            onChange={handleSeek}
                                            className="m-0 w-full"
                                            tooltip={{open: false}}
                                        />
                                    </div>
                                ) : (
                                    // 播放按钮
                                    <span
                                        className="iconfont icon-preview absolute left-[50%] top-[50%] text-[30px] text-white opacity-90"
                                        style={{
                                            transform: 'translate(-50%, -50%)',
                                        }}
                                        onClick={togglePlay}
                                    ></span>
                                )}
                            </div>
                        ) : (
                            <div
                                className="relative h-[135px] overflow-hidden rounded-[9px] bg-contain"
                                style={{backgroundImage: `url(${poster})`}}
                            >
                                {generateStatus !== VideoGenerateStatus.GenerateFail && (
                                    <div className="absolute flex h-full w-full flex-col items-center justify-center gap-[10px] rounded-[8px] bg-[rgba(0,0,0,0.5)]">
                                        <img src={spinPng} className="m-0 h-6 w-6 animate-spin" />
                                        <div className="text-[11px] leading-none text-white">
                                            正在生成中，请耐心等待
                                        </div>
                                    </div>
                                )}
                                <div className="absolute left-2 top-2">
                                    <VideoTag generateStatus={generateStatus} />
                                </div>
                                {title && <BottomTitle title={title} />}
                            </div>
                        )}
                    </NodeViewWrapper>
                </Dropdown>
            </Tooltip>
        </ConfigProvider>
    );
};
