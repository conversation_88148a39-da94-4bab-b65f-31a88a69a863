/**
 * @file 编辑器顶部菜单栏按钮
 * <AUTHOR>
 */

import {TooltipPlacement} from 'antd/es/tooltip';
import {ReactNode, useCallback} from 'react';
import {Tooltip} from 'antd';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {EEditToolId} from '@/utils/loggerV2/interface';
import {useLogExt} from '../../../hooks/logExt';

interface ActionButtonProps {
    icon: string;
    tooltipOptions: {
        content: string;
        align?: {offset: [x: number, y: number]};
    };
    logValue?: EEditToolId;
    disabled?: boolean;
    isDropdown?: boolean;
    onClick?: () => void;
}

const OneLineTooltip = ({
    children,
    content,
    placement = 'bottomLeft',
    align,
}: {
    children: ReactNode;
    content: string;
    placement?: TooltipPlacement;
    align?: {offset: [x: number, y: number]};
}): ReactNode => {
    return (
        <Tooltip
            title={content}
            placement={placement}
            overlayInnerStyle={{width: 'max-content', whiteSpace: 'nowrap'}}
            align={align}
        >
            {children}
        </Tooltip>
    );
};

const ActionButton = ({
    icon,
    disabled = false,
    tooltipOptions,
    isDropdown = false,
    logValue,
    onClick,
    ...props
}: ActionButtonProps) => {
    const {clickLog} = useUbcLogV3();
    const {logExt} = useLogExt();
    const {tuningId} = useQ2CStore(store => ({
        tuningId: store.activeTuningId,
    }));

    const handleClick = useCallback(() => {
        /** B-24 编辑器顶部按钮点击 */
        clickLog(EVENT_VALUE_CONST.EDIT_TOOL, {
            ...logExt,
            eEditToolId: logValue,
            eQaId: `${tuningId}`,
        });
        !disabled && onClick && onClick();
    }, [clickLog, disabled, logExt, logValue, onClick, tuningId]);

    return (
        <OneLineTooltip content={tooltipOptions.content} placement="top" align={tooltipOptions.align}>
            <div onClick={handleClick} {...props}>
                <div className="flex h-5 items-center justify-center gap-[3px] rounded-[3px] px-[2px] leading-5 text-gray-secondary hover:bg-colorBgFormList">
                    <span className={`iconfont ${icon}  ${disabled ? 'opacity-40' : ''}`} />
                    {isDropdown && <span className="iconfont icon-expand1 pl-[1px] text-[7px]"></span>}
                </div>
            </div>
        </OneLineTooltip>
    );
};

export default ActionButton;
