/**
 * @file 开发者问答调优 Q2C 预览页面组件
 * <AUTHOR>
 */
import {ConfigProvider, Flex} from 'antd';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import {getPageADetail, getPageCDetail} from '@/api/q2c';
import {QAStatus, VideoContentItem} from '@/api/q2c/interface';
import {useQ2CStore} from '@/store/agent/q2cStore';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {EOptimisePreTab} from '@/utils/loggerV2/interface';
import {ScrollContainer} from '@/components/ScrollContainer';
import {useLogExt} from '../../hooks/logExt';
import {StyledPreviewTabs} from '../../styles';
import {extractVideoContent} from '../../utils';
import PageA from './PageA';
import PageC from './PageC';
import {LoadStatus, PageADetailData, PageCDetailData, TabKey} from './interface';

export interface PreviewRef {
    preview: (answer?: string, videoContent?: VideoContentItem[]) => void;
}

const Preview = forwardRef((_, ref: React.Ref<PreviewRef>) => {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId');

    const {tuningId, editorStates, activeQA} = useQ2CStore(store => ({
        tuningId: store.activeTuningId,
        editorStates: store.editorStates,
        activeQA: store.activeQA,
    }));

    const [previousId, setPreviousId] = useState(tuningId);

    // 预览页面数据
    const [pageADetail, setPageADetail] = useState<PageADetailData>({} as PageADetailData);
    const [pageCDetail, setPageCDetail] = useState<PageCDetailData>({} as PageCDetailData);

    // 预览页面的状态：分为骨架屏、成功态、失败态
    const [pageAStatus, setPageAStatus] = useState(LoadStatus.skeleton);
    const [pageCStatus, setPageCStatus] = useState(LoadStatus.skeleton);

    const {clickLog} = useUbcLogV3();
    const {logExt} = useLogExt();

    const handlePreviewAPage = useCallback(
        // 此处为何依赖外部传入 answer 和 videoContent，待解答
        async (answer?: string, videoContent?: VideoContentItem[]) => {
            try {
                let pageADetail = await getPageADetail({
                    tuningId,
                    appId: appId!,
                    answer: answer || editorStates[tuningId]?.content || '',
                    videoContent: videoContent || extractVideoContent(editorStates[tuningId]?.jsonContent || {}),
                });
                pageADetail = {
                    ...pageADetail,
                    time: dayjs().format('HH:mm'),
                } as PageADetailData;

                setPageADetail(pageADetail as PageADetailData);
                setPageAStatus(LoadStatus.success);
            } catch (e) {
                setPageAStatus(LoadStatus.error);
            }
        },
        [appId, editorStates, tuningId]
    );

    const handlePreviewCPage = useCallback(
        async (answer?: string, videoContent?: VideoContentItem[]) => {
            try {
                /**
                 * ATTENTION：
                 * uisdk markdown 的渲染逻辑适用于追加更新的数据，因为 markdown 组件的原本设计是服务于 sse 数据流的场景。
                 * 但 q2c 预览场景，每次的数据都是完整而不是追加的，且上次的数据可能会在编辑时删除，所以无法计算“追加的新增数据”。
                 * 因此，这里强行在预览C页刷新时，设置一次骨架屏，销毁上次渲染的 markdown，保证每次预览刷新都是重新渲染收到的数据。
                 */
                setPageCStatus(LoadStatus.skeleton);
                let pageCDetail = await getPageCDetail({
                    tuningId,
                    appId: appId!,
                    answer: answer || editorStates[tuningId]?.content || '',
                    videoContent: videoContent || extractVideoContent(editorStates[tuningId]?.jsonContent || {}),
                });

                pageCDetail = {
                    ...pageCDetail,
                    question: activeQA?.question || '',
                    time: dayjs().format('HH:mm'),
                } as PageCDetailData;
                setPageCDetail(pageCDetail as PageCDetailData);
                setPageCStatus(LoadStatus.success);
            } catch (e) {
                setPageCStatus(LoadStatus.error);
            }
        },
        [activeQA, appId, editorStates, tuningId]
    );

    const handlePreview = useCallback(
        (answer?: string, videoContent?: VideoContentItem[]) => {
            if (!tuningId) {
                return;
            }

            handlePreviewAPage(answer, videoContent);
            handlePreviewCPage(answer, videoContent);
        },
        [handlePreviewAPage, handlePreviewCPage, tuningId]
    );

    const setSkeleton = useCallback(() => {
        setPageAStatus(LoadStatus.skeleton);
        setPageCStatus(LoadStatus.skeleton);
    }, []);

    const handleTabClick = useCallback(
        (key: string) => {
            clickLog(EVENT_VALUE_CONST.OPTIMISE_PREVIEW_TAB, {
                ...logExt,
                eOptimisePreTab: key === TabKey.pageA ? EOptimisePreTab.PageA : EOptimisePreTab.PageC,
            });
        },
        [clickLog, logExt]
    );

    useImperativeHandle(ref, () => {
        return {
            preview: handlePreview,
        };
    });

    const autoPreview = useMemo(
        () =>
            debounce((editorContent: string, videoContent: VideoContentItem[]) => {
                handlePreview(editorContent, videoContent);
            }, 700),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [tuningId]
    );

    useEffect(() => {
        // 比较 id 是为了区分content变化是来自于切换 qa 对还是对当前 qa 对编辑回答内容。
        if (tuningId !== previousId) {
            setPreviousId(tuningId);
            setSkeleton();
            handlePreview(
                editorStates[tuningId]?.content || '',
                extractVideoContent(editorStates[tuningId]?.jsonContent || {})
            );
            return;
        }

        if (!activeQA || activeQA.status === QAStatus.Generating) {
            setSkeleton();
            return;
        }

        autoPreview(
            editorStates[tuningId]?.content || '',
            extractVideoContent(editorStates[tuningId]?.jsonContent || {})
        );

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editorStates[tuningId]?.content]);

    const tabItems = [
        {
            key: TabKey.pageA,
            label: '搜索页',
            children: <PageA data={pageADetail} status={pageAStatus} reload={handlePreviewAPage} />,
        },
        {
            key: TabKey.pageC,
            label: '对话页',
            children: <PageC data={pageCDetail} status={pageCStatus} reload={handlePreviewCPage} />,
        },
    ];

    return (
        <ScrollContainer className="flex h-full w-full flex-col overflow-y-scroll border-l-[1px] border-colorBorderFormList px-[31px] py-4 ">
            <Flex align="center" className="mb-4">
                <span className="text-black-base text-[18px] font-medium leading-6">效果预览</span>
                {!isNaN(tuningId) && (
                    <span className="ml-3 text-[14px] text-gray-tertiary">实际效果以最终发布为准</span>
                )}
            </Flex>
            <Flex className="h-full max-h-[667px] min-h-[530px]" vertical>
                <div className="flex-1 overflow-hidden">
                    <ConfigProvider
                        theme={{
                            components: {
                                Tabs: {
                                    titleFontSizeLG: 14,
                                    horizontalItemPaddingLG: '0 0 3px 0',
                                    horizontalItemGutter: 18,
                                },
                            },
                        }}
                    >
                        <StyledPreviewTabs
                            defaultActiveKey={TabKey.pageA}
                            items={tabItems}
                            size="large"
                            onTabClick={handleTabClick}
                        />
                    </ConfigProvider>
                </div>
            </Flex>
        </ScrollContainer>
    );
});

export default Preview;
