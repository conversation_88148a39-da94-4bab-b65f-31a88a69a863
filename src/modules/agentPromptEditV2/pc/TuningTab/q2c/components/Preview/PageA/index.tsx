/* eslint-disable complexity */
/**
 * @file 开发者问答调优 Q2C A 页预览
 * <AUTHOR>
 */

import {Flex} from 'antd';
import {useMemo} from 'react';
import Markdown from '@/modules/agentPromptEditV2/pc/TuningTab/components/Markdown';
import {BlockComponentType} from '@/api/q2c/interface';
import StatusBar from '../StatusBar';
import {LoadStatus, PageADetailData} from '../interface';
import Error from '../Error';
import {formatVideoDuration} from '../../../utils';
import PageAAgentItem from './AgentItem';

const DEFAULT_POSTER =
    'https://gips2.baidu.com/it/u=2590552663,1638303207&fm=3028&app=3028&f=PNG&fmt=auto&q=75&size=f1138_640';

// Q2C0.6期 - 隐藏 A 页视频
const showVideo = false;

export default function PageA({data, status, reload}: {data: PageADetailData; status: LoadStatus; reload: () => void}) {
    const showSkeleton = status === LoadStatus.skeleton;

    const agentItemData = useMemo(() => {
        if (data?.displayData?.agentContent && data.displayData?.agentContent[0]) {
            const agentContent = data.displayData?.agentContent[0];
            return {
                logo: agentContent.logo || '',
                title: agentContent.agentName || '',
                subTitle: agentContent.tagList?.[0] || '',
                guideList: agentContent.guideList || {},
            };
        }
        return {};
    }, [data]);

    const agcContent = useMemo(() => {
        const agentContent = data?.displayData?.agentContent;
        if (!agentContent || !agentContent[0]) {
            return;
        }

        // TODO:兼容旧协议，0.6一期上线后删除
        if (!Array.isArray(agentContent[0].blocks)) {
            const blocks = agentContent[0].blocks;
            return (
                <Markdown className="mt-2 line-clamp-2 max-h-[40px] overflow-hidden text-ellipsis">
                    {blocks?.data?.content}
                </Markdown>
            );
        }

        const block = agentContent[0].blocks[0];
        if (!block) {
            return;
        }

        if (block.component === BlockComponentType.Markdown) {
            return (
                <div className="mt-2 line-clamp-4 max-h-[80px] overflow-hidden text-ellipsis">
                    <Markdown>{block.data.value || ''}</Markdown>
                </div>
            );
        }

        if (block.component === BlockComponentType.VideoText) {
            if (!block.data.videoList || !block.data.videoList[0]) {
                return;
            }

            const videoData = block.data.videoList[0];
            const vertical = videoData.height > videoData.width;

            const duration = formatVideoDuration(videoData.duration);
            return (
                <div className="mt-2">
                    {showVideo && (
                        <Flex
                            className="relative max-h-[160px] w-full overflow-hidden rounded-[9px]"
                            justify="center"
                            align="center"
                        >
                            {vertical ? (
                                <>
                                    {/* 封面图 */}
                                    <img src={videoData.poster || DEFAULT_POSTER} className="scale-[1.3] blur-lg " />
                                    {/* 视频 */}
                                    <video
                                        src={videoData.url}
                                        poster={videoData.poster}
                                        className=" z-1 absolute h-full"
                                    />
                                </>
                            ) : (
                                <>
                                    <video src={videoData.url} poster={videoData.poster} className="h-full" />
                                </>
                            )}
                            {/* 播放按钮 */}
                            <div className="iconfont icon-preview absolute text-white"></div>
                            {/* 播放时长 */}
                            <div className="absolute bottom-1 right-[6.5px] text-[9px] font-medium text-white">
                                {duration}
                            </div>
                        </Flex>
                    )}
                    <Markdown className="mt-2 line-clamp-5 overflow-hidden text-ellipsis">
                        {block.data.text.value}
                    </Markdown>
                </div>
            );
        }
    }, [data]);

    return (
        <div className="relative mx-3 h-full rounded-3xl border-[1px] border-solid border-colorBorderFormList text-[13.4px]">
            {status === LoadStatus.error && <Error reload={reload} />}

            {(showSkeleton || status === LoadStatus.success) && (
                <>
                    {/* 透明遮罩，禁止与预览区域交互 */}
                    <div className="absolute left-0 top-0 z-10 h-full w-full bg-transparent"></div>
                    {/* 顶部区域，包含状态栏、搜索框和 Tab 栏 */}
                    <div className="px-3 pt-2">
                        <div className="px-1">
                            <StatusBar time={data.time} />
                        </div>
                        {/* 搜索框 */}
                        <Flex
                            className="relative mt-2 h-[34x] rounded-[8px] border-[1px] border-solid border-[#4E6EF2] p-2 leading-none"
                            justify="space-between"
                            align="center"
                        >
                            <Flex align="center" className="relative w-[calc(100%-15px)]">
                                <div className="iconfont icon-a-Frame2147223714 mr-1.5 w-[11px] text-[14px] text-[#4E6EF2]"></div>
                                <div className="h-full w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal leading-none">
                                    {showSkeleton ? '这里是搜索内容' : data.title}
                                </div>
                            </Flex>
                            <div className="iconfont icon-a-camera ml-1 w-[11px] text-[13px] text-gray-secondary"></div>
                        </Flex>
                        {/* Tab 栏 */}
                        <Flex justify="space-between" align="center" className="mt-2 text-[12px] leading-none">
                            <span className="text-black-base font-semibold">综合</span>
                            <span className="text-gray-tertiary">笔记</span>
                            <span className="text-gray-tertiary">视频</span>
                            <span className="text-gray-tertiary">图片</span>
                            <span className="text-gray-tertiary">AI助手</span>
                            <span className="iconfont icon-a-Component12 mr-[3px] text-[11.5px] text-gray-tertiary"></span>
                        </Flex>
                        <div className="ml-1 mt-[5px] h-0.5 w-[11px] rounded-[2px] bg-[#4E6EF2]"></div>
                    </div>

                    {/* 智能体卡片 */}
                    <Flex vertical className="h-[calc(100%-165px)] " justify="center">
                        <div className="border-b-[1px] border-t-[1px] border-solid border-b-colorBorderFormList border-t-colorBorderFormList p-3">
                            <div className="line-clamp-2 max-h-[32px] overflow-hidden text-ellipsis font-semibold leading-[15px]">
                                {showSkeleton ? '这里是标题' : data.title + '- 智能体 实时免费回复'}
                            </div>
                            <Flex className="mt-[11px] gap-[5px]">
                                {showSkeleton ? (
                                    <PageAAgentItem />
                                ) : (
                                    <PageAAgentItem
                                        logo={agentItemData.logo}
                                        title={agentItemData.title}
                                        subTitle={agentItemData.subTitle}
                                    />
                                )}
                            </Flex>

                            {showSkeleton ? (
                                <div className="mt-2 line-clamp-4 max-h-[80px] overflow-hidden text-ellipsis">
                                    这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述
                                </div>
                            ) : (
                                <>{agcContent}</>
                            )}
                            {/* 继续问 */}
                            <Flex
                                justify="space-between"
                                align="center"
                                className="mt-3 h-[33px] rounded-[17px] bg-gray-bg-base pl-3"
                            >
                                {showSkeleton ? (
                                    <div className="h-3 w-[137px] rounded-[9px] bg-white"></div>
                                ) : (
                                    <span className="text-[12px] leading-none text-gray-tertiary">
                                        {agentItemData.guideList?.text || '自动化引导词'}
                                    </span>
                                )}
                                <Flex
                                    className="mr-0.5 h-[27px] w-[51px] rounded-[20px] bg-white text-center text-[11px] font-medium leading-none text-[#4E6EF2]"
                                    align="center"
                                    justify="center"
                                >
                                    <span>{status === LoadStatus.success ? '继续问' : ''}</span>
                                </Flex>
                            </Flex>
                        </div>
                    </Flex>
                    {/* 底 Bar */}
                    <Flex className="absolute bottom-0 h-[60px] w-full" vertical align="center">
                        <Flex className="w-full px-[30px] text-colorTextDefault" justify="space-between" align="center">
                            <span className="iconfont icon-zuoyitubiaohezi text-[18px]"></span>
                            <span className="iconfont icon-menu-more text-[18px]"></span>
                            <Flex
                                className="h-[28px] w-[32px] rounded-[8px] bg-[#4E6EF2]"
                                justify="center"
                                align="center"
                            >
                                <span className="iconfont icon-mic text-[16px] text-white"></span>
                            </Flex>
                            <span className="iconfont icon-a-1 text-[18px]"></span>
                            <span className="iconfont icon-a-Frame2147223714 text-[18px]"></span>
                        </Flex>
                        <div className="absolute bottom-1.5 h-1 w-[100px] rounded-[60px] bg-colorTextDefault"></div>
                    </Flex>
                </>
            )}
        </div>
    );
}
