/* eslint-disable complexity */
import {Flex} from 'antd';
import {useEffect, useMemo, useRef} from 'react';
import uisdk from '@baidu/cosmic-agent-ui/chat-view-core';
import styled from '@emotion/styled';
import {AgentDefaultAvatar} from '@/dicts';
import {ScrollContainerM} from '@/components/ScrollContainer/index-m';
import StatusBar from '../StatusBar';
import {LoadStatus, PageCDetailData} from '../interface';
import Error from '../Error';
import {markdownUIMeta} from '../../../constant';

const StyledMarkdown = styled.div`
    .cosd-markdown .marklang * {
        font-size: 12px !important;
        line-height: 1.57 !important;
    }
`;

uisdk.init({
    env: {
        type: 'chat-view',
        platform: 'pc',
    },
    showProgress: true,
});

export default function PageC({data, status, reload}: {data: PageCDetailData; status: LoadStatus; reload: () => void}) {
    const markdownRef = useRef<HTMLDivElement>(null);

    const showSkeleton = status === LoadStatus.skeleton;

    // TODO: 兼容旧协议，0.6一期上线后删除
    const answer = useMemo(() => {
        try {
            const content = JSON.parse(data.gendata?.component[0]?.data || '');
            return content.value;
        } catch (e) {
            return '';
        }
    }, [data.gendata?.component]);

    useEffect(() => {
        if (!markdownRef.current) {
            return;
        }

        uisdk.renderMessage(markdownRef.current, {
            uiMeta: markdownUIMeta,
            // TODO: 兼容旧协议，0.6一期上线后删除
            data: {
                content: data.gendata ? answer : data.agentData?.blocks[0].data.value,
            },
        });
    }, [answer, data.agentData?.blocks, data.gendata, status]);

    return (
        <div className="relative mx-3 h-full overflow-hidden rounded-3xl border-[1px] border-solid border-colorBorderFormList bg-[#F9FAFC] text-[10.4px]">
            {status === LoadStatus.error && <Error reload={reload} />}

            {(showSkeleton || status === LoadStatus.success) && (
                <>
                    <div className="px-4 pt-2">
                        <StatusBar time={data.time} />
                    </div>

                    {/* 智能体标题 顶 bar */}
                    <Flex align="center" justify="center">
                        <Flex className="mx-9 rounded-[60px] bg-white p-1" align="center">
                            <div className="relative">
                                <div className="h-[18px] w-[18px] overflow-hidden  rounded-[16px] border-[1px] border-[#DCDDE0B3] bg-gray-bg-base">
                                    <img src={data?.agentInfo?.logo?.labelValue || AgentDefaultAvatar} />
                                </div>
                            </div>
                            <div className="ml-[3px] line-clamp-1 max-h-[10px] overflow-hidden text-ellipsis text-[11px] font-medium leading-none">
                                {showSkeleton ? '文心智能体' : data?.agentInfo?.name}
                            </div>
                            <span className="iconfont icon-expand1 ml-[3px] -rotate-90 text-[6px] text-gray-tertiary"></span>
                        </Flex>
                        {/* 抽屉按钮 */}
                        <span className="iconfont icon-a-Frame2147223746 text-black-base absolute right-2 text-[13px]"></span>
                    </Flex>

                    {/* 对话流区域，可滚动 */}
                    <ScrollContainerM className="relative mt-[9px] h-[calc(100%-125px)] overflow-scroll px-[9px] pb-4 text-[12px] font-medium">
                        {/* 问题气泡 */}
                        <div className="text-right first:mt-0">
                            <div className="inline-block rounded-[9px] rounded-tr-[3px] bg-[#7365ff] px-[10px] py-[3px] text-left leading-[22px] text-white">
                                <div>{showSkeleton ? '这里是设置的问题' : data?.question}</div>
                            </div>
                        </div>
                        {/* 回答气泡 */}
                        <div className="pointer-events-none mt-2 rounded-[9px] rounded-tl-[3px] bg-white px-[10px] pb-[9px] pt-3">
                            {showSkeleton ? (
                                <div className="line-clamp-1 overflow-hidden text-ellipsis leading-none">
                                    这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述这里是描述
                                </div>
                            ) : (
                                <StyledMarkdown ref={markdownRef} />
                            )}

                            {/* 气泡底部功能按钮 */}
                            <Flex justify="space-between" align="center" className="mt-[10px]">
                                <Flex>
                                    <Flex
                                        className="h-[22px] w-[77px] rounded-[12px] bg-gray-bg-base"
                                        justify="center"
                                        align="center"
                                    >
                                        {!showSkeleton && (
                                            <>
                                                <span className="iconfont icon-exchange text-black-base text-[12px]"></span>
                                                <span className="ml-[3px] text-[10px] font-medium">重新回答</span>
                                            </>
                                        )}
                                    </Flex>
                                    {!showSkeleton && (
                                        <Flex
                                            className="ml-1.5 h-[22px] w-[22px] rounded-[12px] bg-gray-bg-base"
                                            justify="center"
                                            align="center"
                                        >
                                            <span className="iconfont icon-volume text-black-base text-[12px]"></span>
                                        </Flex>
                                    )}
                                </Flex>
                                <Flex
                                    className="h-[22px] w-[58px] rounded-[24px] bg-gray-bg-base px-[10px]"
                                    justify="space-between"
                                    align="center"
                                >
                                    {!showSkeleton && (
                                        <>
                                            <span className="iconfont icon-thumbs-up text-black-base text-[12px]"></span>
                                            <span className="iconfont icon-thumbs-up text-black-base rotate-180 text-[12px]"></span>
                                        </>
                                    )}
                                </Flex>
                            </Flex>
                        </div>
                    </ScrollContainerM>
                    {/* 底 Bar */}
                    <Flex className="absolute bottom-0 h-[65px] w-full bg-white pt-[6px]" vertical align="center">
                        <Flex
                            className="w-full gap-[7px] px-[12px] text-[16px] text-colorTextDefault"
                            justify="space-between"
                            align="center"
                        >
                            {/* 返回按钮 */}
                            <span className="iconfont icon-zuoyitubiaohezi text-[16px]"></span>
                            {/* 输入框 */}
                            <Flex
                                justify="space-between"
                                align="center"
                                className="h-[32px] w-full rounded-[17px] bg-gray-bg-base px-3 py-[10px]"
                            >
                                <Flex align="center">
                                    <span className="iconfont icon-icon text-[14px] text-[#5240FF]"></span>
                                    <span className="ml-[10px] text-[12px] leading-none text-gray-tertiary">
                                        可以问我任何问题...
                                    </span>
                                </Flex>
                                <div>
                                    <span className="iconfont icon-lll text-[15px]"></span>
                                    <span className="iconfont icon-a-addagent ml-[14px] text-[15px]"></span>
                                </div>
                            </Flex>
                            <span className="iconfont icon-a-Frame2147223714 text-[17px]"></span>
                        </Flex>
                        <div className="absolute bottom-1.5 h-1 w-[100px] rounded-[60px] bg-colorTextDefault"></div>
                    </Flex>
                </>
            )}
        </div>
    );
}
