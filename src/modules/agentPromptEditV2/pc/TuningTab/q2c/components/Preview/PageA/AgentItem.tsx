/**
 * @file A页智能体概要信息组件
 * <AUTHOR>
 */

import {Flex} from 'antd';
import aiIcon from '@/assets/ai-icon.png';

export default function PageAAgentItem({
    logo,
    title,
    subTitle,
    showAIIcon,
}: {
    /** 头像 */
    logo?: string;
    /** 名称 */
    title?: string;
    /**  副标题 */
    subTitle?: string;
    /** 是否显示右下角的AI图标 */
    showAIIcon?: boolean;
}) {
    return (
        <Flex vertical align="center">
            <Flex className="w-full gap-[5px] rounded-[8px]" align="center" justify="center">
                {/* 头像区域 */}
                <div className="relative">
                    <div className="h-[30px] w-[30px] overflow-hidden rounded-[16px] bg-colorBgFormList">
                        {logo && <img src={logo} />}
                    </div>
                    {showAIIcon && <img src={aiIcon} className="absolute bottom-0 right-0 w-[9px] text-[#6E4BFA]" />}
                </div>
                {/* 文字内容区域 */}
                <Flex vertical className="w-fit gap-[7px] leading-none">
                    {title ? (
                        <span className="h-[13px] overflow-hidden text-ellipsis whitespace-nowrap text-[12px] font-medium">
                            {title}
                        </span>
                    ) : (
                        <div className="h-3 w-[96px] rounded-[9px] bg-colorBgFormList"></div>
                    )}

                    <Flex align="center">
                        {subTitle ? (
                            <span className="h-[11px] w-[70px] overflow-hidden text-ellipsis whitespace-nowrap text-[10px] text-gray-tertiary">
                                {subTitle}
                            </span>
                        ) : (
                            <div className="h-3 w-[63px] rounded-[9px] bg-colorBgFormList"></div>
                        )}
                    </Flex>
                </Flex>
            </Flex>
        </Flex>
    );
}
