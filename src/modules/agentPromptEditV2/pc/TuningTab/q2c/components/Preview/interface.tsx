import {GetPageADetailResponse, GetPageCDetailResponse} from '@/api/q2c/interface';

export const enum LoadStatus {
    success = 0,
    error = 1,
    skeleton = 2,
}

export type PageCDetailData = GetPageCDetailResponse & {question: string; time: string};

export type PageADetailData = GetPageADetailResponse & {
    time: string;
};

export const enum TabKey {
    pageC = 'pageC',
    pageA = 'pageA',
}
