/**
 * @file 开发者问答调优 Q2C 预览页面系统状态栏
 * <AUTHOR>
 */
import {Flex} from 'antd';

export default function StatusBar({time}: {time: string}) {
    return (
        <Flex justify="space-between" align="center">
            <span className="text-[12px] font-medium">{time}</span>
            <div>
                <span className="iconfont icon-a-signal text-[13px]"></span>
                <span className="iconfont icon-a-wifi text-[13px]"></span>
                <span className="iconfont icon-battery text-[12px]"></span>
            </div>
        </Flex>
    );
}
