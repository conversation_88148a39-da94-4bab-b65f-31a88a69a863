import {Flex} from 'antd';
import {useCallback} from 'react';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useLogExt} from '../../hooks/logExt';

export default function Error({reload}: {reload: () => void}) {
    const {clickLog} = useUbcLogV3();

    const {logExt} = useLogExt();

    const handleReload = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.OPT_REGENERATE, logExt);
        reload();
    }, [clickLog, logExt, reload]);

    return (
        <Flex className="h-full w-full  bg-colorBgFormList" align="center" justify="center" vertical>
            <div className="iconfont icon-warn text-[48px] text-[#5562F233]"></div>
            <Flex>
                <p className="text-xs text-gray-tertiary">生成失败</p>
                <p className="ml-1 cursor-pointer text-xs text-[#4E6EF2]" onClick={handleReload}>
                    重新生成
                </p>
            </Flex>
        </Flex>
    );
}
