/**
 * 问答优化引导
 *
 */
import Tippy from '@tippyjs/react';
import tuningGuidePng from '@/assets/tuning-step-guide.png';

export default function TuningGuide() {
    return (
        <div className="flex items-center justify-start text-lg font-medium leading-6">
            <span>知识库优质问答</span>
            <Tippy
                placement="bottom"
                offset={[180, 8]}
                theme="tuning-step-guide"
                content={
                    <div>
                        <div className="mb-6 text-lg font-medium leading-[18px]">如何优化问答</div>
                        <img className="w-[30.5rem]" src={tuningGuidePng} />
                    </div>
                }
            >
                <span className="iconfont icon-questionCircle ml-1.5 text-lg leading-6 text-gray-tertiary"></span>
            </Tippy>
        </div>
    );
}
