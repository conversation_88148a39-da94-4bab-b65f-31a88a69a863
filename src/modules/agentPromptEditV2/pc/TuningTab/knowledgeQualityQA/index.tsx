/**
 * @file 优质问答tab
 * <AUTHOR>
 */
import {useSearchParams} from 'react-router-dom';
import useSWR from 'swr';
import {ChangeEvent, useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {Input, Popover, Table} from 'antd';
import debounce from 'lodash/debounce';
import {ColumnsType} from 'antd/es/table';
import classNames from 'classnames';
import {
    TuningQASelectItems,
    TuningQASelectItemsText,
    TuningTabKey,
} from '@/modules/agentPromptEditV2/pc/TuningTab/interface';
import {getQaDetailList, GET_TUNING_LIST_API} from '@/api/agentEditV2';
import {QaDetail, QaDetailListParam, QaSource, QaStatus} from '@/api/agentEditV2/interface';
import {
    OverEllipsisContainer,
    popoverClassName,
    StyledSelect,
    StyledSelectDropdown,
    TableStyle,
} from '@/modules/agentPromptEditV2/pc/TuningTab/customStyle';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import EmptyTable from '@/modules/agentPromptEditV2/pc/TuningTab/components/EmptyTable';
import TuningButton from '@/modules/agentPromptEditV2/pc/TuningTab/components/TuningButton';
import DeleteButton from '@/modules/agentPromptEditV2/pc/TuningTab/components/DeleteButton';
import Highlight from '@/components/Highlight';
import {AgentTab, TuningTabLogMap} from '@/modules/agentPromptEditV2/interface';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {LJExtData} from '@/utils/logger';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import StaticTips from '@/components/StaticTips';
import TuningStepGuide from '@/modules/agentPromptEditV2/pc/TuningTab/knowledgeQualityQA/TuningStepGuide';
import BottomTuningTutorial from '@/modules/agentPromptEditV2/pc/TuningTab/tuningTabTutorial/BottomTuningTutorial';
import {DatesetQAStatusMap, PAGE_NO, PAGE_SIZE} from '../constant';

const activeQaStatusOptions = [
    {label: TuningQASelectItemsText[TuningQASelectItems.processing], value: TuningQASelectItems.processing},
    {label: TuningQASelectItemsText[TuningQASelectItems.processed], value: TuningQASelectItems.processed},
];

const getQuestionColumn = (keyword: string) => ({
    title: '问题',
    key: 'question',
    dataIndex: 'question',
    width: '30%',
    render: (question: string) => (
        <OverEllipsisContainer>
            <Highlight keyword={keyword}>{question}</Highlight>
        </OverEllipsisContainer>
    ),
});

const getAnswerColumn = () => ({
    title: '答案',
    key: 'answer',
    dataIndex: 'answer',
    width: '70%',
    render: (answer: string) => {
        const answerPopoverContent = answer ? <div className="max-w-[60.375rem]">{answer}</div> : null;

        return (
            <Popover overlayClassName={popoverClassName} content={answerPopoverContent} placement="bottom">
                <OverEllipsisContainer>{answer}</OverEllipsisContainer>
            </Popover>
        );
    },
});

const getTuningDataColumn = () => ({
    title: '调优信息',
    key: 'tuningData',
    width: '70%',
    // eslint-disable-next-line complexity
    render: (_: unknown, record: QaDetail) => {
        // 新增数据：展示思考路径 & 个性化
        if ((record?.status as QaStatus) === QaStatus.Submitted) {
            const {answer, chatAnswer} = record?.tuningData ?? {};

            const tuningDataPopoverContent = (
                <div className="max-w-[60.375rem]">
                    {answer && <div>思考路径: {answer}</div>}
                    {chatAnswer && <div>个性化: {chatAnswer}</div>}
                </div>
            );

            return (
                <div>
                    <Popover overlayClassName={popoverClassName} content={tuningDataPopoverContent} placement="bottom">
                        {answer && <OverEllipsisContainer>思考路径: {answer}</OverEllipsisContainer>}
                        {chatAnswer && <OverEllipsisContainer>个性化: {chatAnswer}</OverEllipsisContainer>}
                    </Popover>
                </div>
            );
        }

        // 存量数据-知识库：优先展示理想回答，否则展示自动生成的优质答案
        const idealAnswer = record?.actionData?.idealAnswer ? record?.actionData?.idealAnswer : record?.answer;
        const answerPopoverContent = idealAnswer ? <div className="max-w-[60.375rem]">{idealAnswer}</div> : null;
        return (
            <Popover overlayClassName={popoverClassName} content={answerPopoverContent} placement="bottom">
                <OverEllipsisContainer>{idealAnswer}</OverEllipsisContainer>
            </Popover>
        );
    },
});

const getProcessingColumns = ({
    keyword,
    onTuning,
    onDelete,
    activeQaStatus,
    tuningTableContainer,
}: {
    keyword: string;
    onTuning: () => void;
    onDelete: () => void;
    activeQaStatus: TuningQASelectItems;
    tuningTableContainer: HTMLDivElement | null;
}): ColumnsType<QaDetail> => {
    return [
        // 问题
        getQuestionColumn(keyword),
        // 答案
        getAnswerColumn(),
        // 操作
        {
            title: '操作',
            key: 'action',
            width: 200,
            className: 'table-fixed',
            render: (_, record) => {
                return (
                    <div className="flex" key={`${record.tuningId}`}>
                        <TuningButton
                            key={Math.random()}
                            className="font-normal"
                            type="link"
                            size="small"
                            detail={record}
                            onFinish={onTuning}
                            activeQaStatus={activeQaStatus}
                            icon={<span className="iconfont icon-optimize text-sm" />}
                            submitBtnText="提交"
                            tuningTableContainer={tuningTableContainer}
                        >
                            调优
                        </TuningButton>
                        <DeleteButton activeQaStatus={activeQaStatus} tuningId={record.tuningId} onDelete={onDelete} />
                    </div>
                );
            },
        },
    ];
};

const getProcessedColumns = ({
    keyword,
    onTuning,
    onDelete,
    activeQaStatus,
    tuningTableContainer,
}: {
    keyword: string;
    onTuning: () => void;
    onDelete: () => void;
    activeQaStatus: TuningQASelectItems;
    tuningTableContainer: HTMLDivElement | null;
}): ColumnsType<QaDetail> => {
    return [
        // 问题
        getQuestionColumn(keyword),
        // 调优信息
        getTuningDataColumn(),
        {
            title: '操作',
            key: 'action',
            width: 200,
            className: 'table-fixed',
            render: (_, record) => {
                return (
                    <div className="flex" key={`${record.tuningId}`}>
                        <TuningButton
                            key={Math.random()}
                            tuningTableContainer={tuningTableContainer}
                            className="font-normal"
                            type="link"
                            size="small"
                            detail={record}
                            onFinish={onTuning}
                            activeQaStatus={activeQaStatus}
                            icon={<span className="iconfont icon-optimize text-sm" />}
                            submitBtnText={
                                record?.tuningData?.answer || record?.tuningData?.chatAnswer ? '重新提交' : '提交'
                            }
                        >
                            调优
                        </TuningButton>
                        <DeleteButton activeQaStatus={activeQaStatus} tuningId={record.tuningId} onDelete={onDelete} />
                    </div>
                );
            },
        },
    ];
};

// eslint-disable-next-line complexity, max-statements
export default function KnowledgeQualityQA() {
    const [searchParams] = useSearchParams();
    const appId = searchParams.get('appId');
    const activeTab = searchParams.get('activeTab');

    const [pageNo, setPageNo] = useState(PAGE_NO);
    const [keyword, setKeyword] = useState('');

    const [activeQaStatus, setActiveQaStatus] = useState(TuningQASelectItems.processing);

    const {displayLog, clickLog} = useUbcLogV3();
    const appName = usePromptEditStoreV2(store => store.agentConfig.agentInfo.name);
    const tuningTableContainerRef = useRef<HTMLDivElement | null>(null);

    const tuningListParams = {
        pageNo,
        pageSize: PAGE_SIZE,
        appId,
        status: DatesetQAStatusMap[activeQaStatus],
        keyword,
        source: QaSource.Dataset,
    } as QaDetailListParam;

    const {data, error, isLoading, isValidating, mutate} = useSWR(
        [GET_TUNING_LIST_API, tuningListParams],
        tuningListParams?.appId ? () => getQaDetailList(tuningListParams) : null,
        {
            // 禁用错误重试
            shouldRetryOnError: false,
            // 禁用聚焦时重新请求
            revalidateOnFocus: false,
        }
    );

    const hasSearchParams = !!keyword;
    const isRequesting = isLoading || isValidating;
    const processingNumber = data?.processingNum ?? 0;
    const processedNumber = (data?.processedNum ?? 0) + (data?.featuredNum ?? 0);
    const showTuningTable = !!data?.dataList?.length || isRequesting;

    const showTip =
        data?.processingDataSet &&
        !!data?.dataList?.length &&
        activeQaStatus === TuningQASelectItems.processing &&
        showTuningTable;

    const logExt = useMemo(() => {
        return {
            [EVENT_EXT_KEY_CONST.AGENT_ID]: appId ?? '',
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: appName,
            [EVENT_EXT_KEY_CONST.ADJUST_TAB_NAME]: TuningTabLogMap[activeQaStatus],
        } as LJExtData;
    }, [appId, appName, activeQaStatus]);

    // 展现打点
    useEffect(() => {
        if (activeTab === AgentTab.Tuning) {
            displayLog(EVENT_PAGE_CONST.CODELESS_ADJUST, {
                ...logExt,
                [EVENT_EXT_KEY_CONST.P_AGENT_OPTIMISE_TAB]: +TuningTabKey.knowledgeQualityQA,
            } as LJExtData);
        }
    }, [appId, activeTab, logExt, displayLog]);

    const handleKeywordChange = useMemo(
        () =>
            debounce((event: ChangeEvent<HTMLInputElement>) => {
                setKeyword(event.target.value);
            }, 500),
        []
    );

    const handleStatusChange = useCallback(
        (value: any) => {
            setActiveQaStatus(value as TuningQASelectItems);

            clickLog(EVENT_VALUE_CONST.SWITCH_TAB, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
        },
        [clickLog, logExt]
    );

    // 搜索框点击打点
    const handleSearchInputClick = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.SEARCH_INPUT, logExt, EVENT_PAGE_CONST.CODELESS_ADJUST);
    }, [clickLog, logExt]);

    // 获取待处理列表
    const processingColumns = getProcessingColumns({
        keyword,
        onTuning: mutate,
        onDelete: mutate,
        activeQaStatus,
        tuningTableContainer: tuningTableContainerRef.current,
    });

    // 获取已处理列表
    const processedColumns = getProcessedColumns({
        keyword,
        onTuning: mutate,
        onDelete: mutate,
        activeQaStatus,
        tuningTableContainer: tuningTableContainerRef.current,
    });

    const renderEmptyTableMessage = (showTuningTable: boolean) => {
        if (!showTuningTable) {
            return (
                <div className="mt-[3px] text-sm font-normal text-gray-tertiary">
                    智能体挂载知识库后自动生成优质问答，快去添加吧～
                </div>
            );
        }

        return (
            <div className="mt-[3px] text-sm font-normal text-gray-tertiary">
                开发者编排时调试的问答信息，调优后智能体将优化此类问题的回答
            </div>
        );
    };

    const content = useMemo(() => {
        if (error) {
            return <RenderError className="my-auto min-h-[calc(100vh-650px)]" onBtnClick={mutate} />;
        }

        if (!showTuningTable) {
            return (
                <EmptyTable
                    source={QaSource.Dataset}
                    activeQaStatus={activeQaStatus}
                    processingDataset={!!data?.processingDataSet}
                    hasSearchParams={hasSearchParams}
                />
            );
        }

        return (
            <div className="bg-white" ref={tuningTableContainerRef}>
                {activeQaStatus === TuningQASelectItems.processing && (
                    <Table
                        loading={isRequesting}
                        className={TableStyle}
                        columns={processingColumns}
                        dataSource={data?.dataList}
                        rowKey="tuningId"
                        pagination={{
                            total: processingNumber,
                            pageSize: PAGE_SIZE,
                            current: pageNo,
                            onChange: setPageNo,
                            hideOnSinglePage: true,
                            showSizeChanger: false,
                        }}
                    />
                )}
                {activeQaStatus === TuningQASelectItems.processed && (
                    <Table
                        loading={isRequesting}
                        className={TableStyle}
                        columns={processedColumns}
                        dataSource={data?.dataList}
                        rowKey="tuningId"
                        pagination={{
                            total: processedNumber,
                            pageSize: PAGE_SIZE,
                            current: pageNo,
                            onChange: setPageNo,
                            hideOnSinglePage: true,
                            showSizeChanger: false,
                        }}
                    />
                )}
            </div>
        );
    }, [
        error,
        showTuningTable,
        activeQaStatus,
        isRequesting,
        processingColumns,
        data?.dataList,
        data?.processingDataSet,
        processingNumber,
        pageNo,
        processedColumns,
        processedNumber,
        mutate,
        hasSearchParams,
    ]);

    const optionContent = useMemo(() => {
        return (
            <div className="flex justify-between gap-3">
                <StyledSelect
                    allowClear={false}
                    defaultValue={activeQaStatusOptions[0]?.value}
                    suffixIcon={<span className="iconfont icon-Down text-black" />}
                    className="h-9 w-[11.6rem]"
                    onChange={handleStatusChange}
                    options={activeQaStatusOptions}
                    popupClassName={StyledSelectDropdown}
                />
                <Input
                    placeholder="搜索用户问题"
                    className="h-9 w-[13.7rem] border-none bg-colorBgFormList"
                    allowClear
                    onChange={handleKeywordChange}
                    onClick={handleSearchInputClick}
                    suffix={
                        <span
                            className="iconfont icon-search cursor-pointer text-xl leading-5 text-[#B7BAC0] hover:text-primary"
                            onClick={handleKeywordChange.flush}
                        />
                    }
                />
            </div>
        );
    }, [handleKeywordChange, handleSearchInputClick, handleStatusChange]);

    return (
        <div className={classNames('min-w-[80rem] p-6 pt-4', !showTuningTable && 'pb-4')}>
            <div className="flex h-full w-full min-w-[77rem] flex-col rounded-2xl bg-white p-6">
                <div className="relative flex items-center justify-between">
                    <div className="w-[50%]">
                        <div className="flex-shrink">
                            <TuningStepGuide />
                            {renderEmptyTableMessage(showTuningTable)}
                        </div>
                    </div>
                    <div>{optionContent}</div>
                </div>
                {showTip && (
                    <div className="mt-4 flex">
                        <StaticTips
                            className="h-[34px] rounded-[9px] px-[9px] py-[7px] text-colorTextDefault"
                            bgColor="bg-[#5562F20A]"
                            iconfont="icon-tip-fill"
                        >
                            检测到知识库有更新，正在为您生成更多优质问答，请稍后查看
                        </StaticTips>
                    </div>
                )}
                <div className={showTip ? 'mt-4' : showTuningTable ? 'mt-6' : ''}>
                    <div className="flex-shrink overflow-hidden">{content}</div>
                </div>
            </div>
            {!showTuningTable && <BottomTuningTutorial tuningTab={TuningTabKey.knowledgeQualityQA} />}
        </div>
    );
}
