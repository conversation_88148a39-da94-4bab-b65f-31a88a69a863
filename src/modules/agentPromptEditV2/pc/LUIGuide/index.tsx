/**
 * @file LUI 分流页面
 *
 */
import {useCallback, useEffect, useRef, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import useMessage from 'antd/es/message/useMessage';
import '@baidu/lingjing-fe-agent-adapter/dist/js/agent.js';
import '@baidu/lingjing-fe-agent-adapter/dist/css/agent.css';
import {InitAgentInfo} from '@/modules/agentPromptEditV2/pc/QuickStart';
import {ChatViewEventType, VersionType} from '@/modules/agentPromptEditV2/components/PreviewContainer/interface';
import {serviceUrl} from '@/modules/agentPromptEditV2/components/PreviewContainer/constant';
import Header from '@/modules/agentPromptEditV2/pc/LUIGuide/Header';
import {useLoadingTimer} from '@/modules/agentPromptEditV2/hooks/useLoadingTimer';
import loadingAnimation from '@/assets/loading-animation.gif';
import {generateAgentConfigV2} from '@/api/agentEditV2';
import {getStringLength} from '@/utils/text';
import useSkipModal from '@/modules/agentPromptEditV2/hooks/useSkipModal';
import SkipModal from '@/modules/agentPromptEditV2/pc/components/SkipModal';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_EXT_KEY_CONST, SubPageType, UserTypeLog} from '@/utils/logger/constants/extkey';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {isProd} from '@/modules/agentPromptEditV2/components/PreviewContainer/constant';
import {StyledChatViewContainer} from '@/modules/agentPromptEditV2/pc/LUIGuide/StyledChatViewContainer';
import {chatViewConfig} from '@/modules/agentPromptEditV2/pc/LUIGuide/constant';
import {auditInput} from '@/modules/agentPromptEditV2/pc/LUIGuide/utils';
import {useSendPromptHandler} from '@/modules/agentPromptEditV2/pc/LUIGuide/hooks/useSendPromptHandler';
import {useSendLogHandler} from '@/modules/agentPromptEditV2/pc/LUIGuide/hooks/useSendLogHandler';
import {LuiSdkScene} from '@/dicts';

enum LUIAgentId {
    Prod = 'a94qFDQaHmNO7d6oCtpS42T3PBf0TQ94',
    QA = 'NShs872heKkVltfDe0fr9LjQxigLYveO',
}

const Chat = window.LingJingAgentSDK.AgentPreview;

const openNewPage = (url: string): void => {
    if (!url) {
        throw Error('【api.openNewPage】url 为空');
    }

    window.location.replace(url);
};

export default function LUIGuide({
    setShowLUIGuide,
    setInitAgentInfo,
    setShowQuickStart,
}: {
    setShowLUIGuide: (show: boolean) => void;
    setInitAgentInfo: (info: InitAgentInfo) => void;
    setShowQuickStart: (show: boolean) => void;
}) {
    const location = useLocation();
    const navigate = useNavigate();

    // 从 ui.json form 表单接收的智能体名称
    const [name, setName] = useState('');
    // 从 ui.json form 表单接收的智能体设定
    const [introduction, setIntroduction] = useState('');

    const [loading, setLoading] = useState(false);
    const [loadingText, {startTimer, cancelTimer}] = useLoadingTimer();

    const [messageApi, ContextHolder] = useMessage();
    const {ubcClickLog, ubcDisplayLog} = useUbcLog();
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const loginType = userInfoData?.userInfo?.ucUserId ? UserTypeLog.UC : UserTypeLog.PASSPORT;

    const chatViewContainerRef = useRef<HTMLDivElement>(null);
    const chatViewRef = useRef<typeof Chat | null>(null);
    const controller = useRef<AbortController>();

    // LUI 分流页面展现打点
    useEffect(() => {
        ubcDisplayLog({
            [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
        });
    }, [loginType, ubcDisplayLog]);

    const sendPromptHandler = useSendPromptHandler(setName, setIntroduction, setLoading);
    const sendLogHandler = useSendLogHandler();

    useEffect(() => {
        if (!chatViewRef.current) {
            // 初始化 ChatViewSDK
            chatViewRef.current = new Chat({
                // todo: 是否要保留 qa 环境测试智能体 id
                agentId: isProd() ? LUIAgentId.Prod : LUIAgentId.QA,
                container: chatViewContainerRef.current!,
                serviceUrl,
                versionType: VersionType.Online,
                chatViewConfig: {
                    ...chatViewConfig,
                    sendPromptHandler,
                    sendLogHandler,
                    sdkScene: LuiSdkScene.QiaoCangEntry,
                },
                platform: 'pc',
                api: {
                    // 跳转不新开页面，替换当前页面
                    openNewPage,
                },
            });

            // 渲染对话容器
            chatViewRef.current?.render();
        }

        const clearBtnClickCallback = () => {
            // 清除记忆按钮点击打点
            ubcClickLog(EVENT_TRACKING_CONST.luiGuideClear, {
                [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
            });
        };

        chatViewRef.current?.on(ChatViewEventType.ClearBtnClick, clearBtnClickCallback);
        return () => {
            chatViewRef.current?.off(ChatViewEventType.ClearBtnClick, clearBtnClickCallback);
        };
    }, [sendPromptHandler, sendLogHandler, loginType, ubcClickLog]);

    const handleAuditErrors = useCallback(
        (auditResults: Array<{passAudit: boolean; errMsg: string}>) => {
            auditResults.forEach(result => {
                if (!result.passAudit && result.errMsg) {
                    messageApi.error(result.errMsg);
                }
            });
        },
        [messageApi]
    );

    const generateAgent = useCallback(async () => {
        try {
            startTimer();

            const auditRes = await Promise.all([auditInput(name, 'name'), auditInput(introduction, 'introduction')]);
            if (auditRes.some(res => !res.passAudit)) {
                handleAuditErrors(auditRes);
                return;
            }

            controller.current = new AbortController();
            const res = await generateAgentConfigV2({introduction}, controller.current.signal);

            // 舍弃长度大于 50 的 overview
            if (getStringLength(res.overview) > 100) {
                res.overview = '';
            }

            // 当用户没填名称的时候，用 /gen 生成的名称
            setInitAgentInfo({...res, name: name || res.name, introduction});

            setTimeout(() => {
                setShowLUIGuide(false);
            });
        } catch (e) {
            throw e;
        } finally {
            setLoading(false);
        }
    }, [name, introduction, setShowLUIGuide, setInitAgentInfo, startTimer, handleAuditErrors]);

    useEffect(() => {
        // 展示创建全屏蒙层时，触发创建流程
        if (loading) {
            generateAgent();
        }
    }, [loading, generateAgent]);

    useEffect(() => {
        return () => cancelTimer();
    }, [cancelTimer]);

    const handleSkipConfirm = useCallback(() => setShowLUIGuide(false), [setShowLUIGuide]);
    const {skipModalProps, setConfirmOpen} = useSkipModal(controller, setLoading, handleSkipConfirm);
    const {cancelGen: baseCancelGen, confirmOpen, waitForGen} = skipModalProps;

    const cancelGen = useCallback(() => {
        baseCancelGen();

        // 用户在二次确认弹窗中点击跳过时，删除 state 中的 openLUIGuide，并隐藏快速创建全屏组件
        if (location.state?.openLUIGuide) {
            setShowQuickStart(false);

            const newState = {...location.state};
            delete newState.openLUIGuide;
            navigate('.', {replace: true, state: newState});
        }
    }, [baseCancelGen, navigate, location.state, setShowQuickStart]);

    const handleSkip = useCallback(() => {
        if (loading) {
            setConfirmOpen(true);
        } else {
            setShowLUIGuide(false);
        }
    }, [loading, setConfirmOpen, setShowLUIGuide]);

    return (
        <div className="absolute z-[50] h-full min-h-[800px] w-full min-w-[1280px]">
            {/* 组合背景 */}
            <div className="composite-bg">
                {/* 纯色渐变 */}
                <div
                    className="gradient-bg fixed left-0 top-0 h-full w-[100vw]"
                    style={{
                        background: 'linear-gradient(#D1DCFC, #ECF1FE, #F7F9FE)',
                    }}
                />
                {/* 方块 */}
                <div className="square-bg fixed left-[20.71vw] h-[114.19vw] w-[83.54vw] bg-scroll-bg bg-cover bg-no-repeat" />
            </div>

            <div className="fixed left-0 top-0 h-full w-full min-w-[1280px]">
                {/* 导航栏 */}
                <Header loginType={loginType} />

                {/* LUI 分流对话容器 */}
                <div className={'relative flex h-full w-full'}>
                    <StyledChatViewContainer ref={chatViewContainerRef} />
                </div>
            </div>

            {ContextHolder}

            {/* 文心智能体创建全屏蒙层 */}
            {loading && (
                <>
                    <div
                        className="fixed left-0 top-0 h-full w-[100vw] opacity-60"
                        style={{
                            background: 'linear-gradient(#D1DCFC, #ECF1FE, #F7F9FE)',
                        }}
                    ></div>

                    <div className="absolute top-0 flex h-full w-full flex-col items-center justify-center">
                        <img src={loadingAnimation} className="mb-3 w-10" alt="加载中"></img>
                        <div className="text-sm">{loadingText}</div>
                        <p className="mt-6 cursor-pointer text-gray-tertiary" onClick={handleSkip}>
                            跳过
                        </p>
                    </div>
                </>
            )}

            {/* 跳过智能体生成全屏 modal */}
            <SkipModal cancelGen={cancelGen} confirmOpen={confirmOpen} waitForGen={waitForGen} />
        </div>
    );
}
