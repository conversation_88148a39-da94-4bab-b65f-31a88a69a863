import {auditText} from '@/api/audit';
import {PREFIX_TEXT} from '@/modules/agentPromptEditV2/pc/LUIGuide/constant';

/**
 * 审核输入内容
 */
export const auditInput = async (text: string, fieldName: string) => {
    const prefixText = PREFIX_TEXT[fieldName];
    const auditRes = {
        passAudit: false,
        errMsg: '',
    };

    if (text === '') {
        auditRes.passAudit = true;
        return auditRes;
    }

    try {
        await auditText(text, prefixText);
        auditRes.passAudit = true;
    } catch (error: any) {
        auditRes.errMsg = error.msg || error.message;
    }
    return auditRes;
};
