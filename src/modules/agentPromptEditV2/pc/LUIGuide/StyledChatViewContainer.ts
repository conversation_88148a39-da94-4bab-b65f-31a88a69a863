import styled from '@emotion/styled';

export const StyledChatViewContainer = styled.div`
    height: 100%;
    width: 100%;
    background: unset;

    #lingjing-fe-agent-adapter-container-pc {
        .top-menu-nav-slot-body {
            height: calc(100vh - 84px) !important;
        }

        .top-menu-nav-body {
            background: unset !important;
            padding-top: 0 !important;
        }

        .bot-container {
            background: unset !important;
        }

        .bot-container-pc {
            background: unset !important;

            .chat-input-box {
                padding-bottom: 32px !important;
            }

            .loading-container {
                background: unset !important;
            }

            // 问题气泡背景
            .question-container .question-content {
                background-color: #5562f2;
            }

            // 停止回答图标颜色
            .control-stop .control-square {
                background-color: #5562f2;
            }

            // 停止回答文字颜色
            .stream-control-normal {
                color: #5562f2;
            }

            .control-stop:hover {
                background-color: #e0e8ff;
                color: #5562f2;
            }

            .welcome-container {
                // 引导示例背景颜色
                .rc-line-wise .recommend-item {
                    width: unset;
                    background-color: #ecf1fb;
                }

                // 引导示例 hover
                .recommend-item:hover {
                    background-color: #dfe6f3 !important;

                    .recommend-item-text {
                        color: #1e1f24;
                    }
                }

                .rc-line-wise .recommend-item-text {
                    font-weight: 400;
                }

                .rc-line-wise.rc-line-func .recommend-item {
                    flex: unset;
                    padding: 8px 12px;
                }
            }
        }

        @media only screen and (max-width: 952px) {
            .bot-container-main {
                max-width: 1280px;
            }
        }
    }
`;
