/**
 * @file 在 chat-view-sdk 注入自定义的 sendPromptHandler 拦截器，拦截 ui.json 的 sendPrompt 事件
 *
 */
import {useCallback} from 'react';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {EVENT_EXT_KEY_CONST, SubPageType, UserTypeLog} from '@/utils/logger/constants/extkey';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUbcLog} from '@/utils/logger/useUbcLogger';

export function useSendPromptHandler(
    setName: (name: string) => void,
    setIntroduction: (intro: string) => void,
    setLoading: (loading: boolean) => void
) {
    const {ubcClickLog} = useUbcLog();
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const loginType = userInfoData?.userInfo?.ucUserId ? UserTypeLog.UC : UserTypeLog.PASSPORT;

    return useCallback(
        (data: any) => {
            if (data?.msg === 'wenxin_agent_create_btn_clicked') {
                // 文心创建按钮点击打点：需要拦截上屏
                ubcClickLog(EVENT_TRACKING_CONST.luiGuideCreateAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.CREATE_ANSWER_BUBBLE_TYPE]: 2,
                });

                const name = data?.$event?.agentName;
                const introduction = data?.$event?.agentConfig;

                setName(name);
                setIntroduction(introduction);
                setLoading(true);
                return true;
            }

            if (data?.msg === 'qiaocang_agent_create_btn_clicked') {
                // 巧舱创建按钮点击打点：需要拦截上屏
                ubcClickLog(EVENT_TRACKING_CONST.luiGuideCreateAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.CREATE_ANSWER_BUBBLE_TYPE]: 1,
                });

                return true;
            }

            // 智能体描述选项点击打点：不拦截上屏
            ubcClickLog(EVENT_TRACKING_CONST.luiGuideSelectAnswerBubble, {
                [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                [EVENT_EXT_KEY_CONST.SELECT_ANSWER_BUBBLE_TYPE]: 2,
                [EVENT_EXT_KEY_CONST.SELECT_BUTTON_ORDER]: data?.$event?.order,
                [EVENT_EXT_KEY_CONST.SELECT_BUTTON_CONTENT]: data?.$event?.text,
            });
            return false;
        },
        [loginType, ubcClickLog, setName, setIntroduction, setLoading]
    );
}
