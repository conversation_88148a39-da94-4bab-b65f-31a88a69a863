/**
 * @file 在 chat-view-sdk 注入自定义的打点 api，用于巧舱、文心业务区分打点
 *
 */
import {useCallback} from 'react';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {EVENT_EXT_KEY_CONST, SubPageType, UserTypeLog} from '@/utils/logger/constants/extkey';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUbcLog} from '@/utils/logger/useUbcLogger';

export function useSendLogHandler() {
    const {ubcShowLog, ubcClickLog} = useUbcLog();
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const loginType = userInfoData?.userInfo?.ucUserId ? UserTypeLog.UC : UserTypeLog.PASSPORT;

    return useCallback(
        (params: any) => {
            // 巧舱创建气泡展现打点
            if (params?.type === 'button' && params?.data?.$event?.text === '立即创建') {
                ubcShowLog(EVENT_TRACKING_CONST.luiGuideCreateAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.CREATE_ANSWER_BUBBLE_TYPE]: 1,
                });
            }

            // 智能体描述选项气泡展现打点
            if (params?.type === 'button' && params?.data?.$event?.text === '办公工具') {
                ubcShowLog(EVENT_TRACKING_CONST.luiGuideSelectAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.SELECT_ANSWER_BUBBLE_TYPE]: 2,
                });
            }

            // 文心创建气泡展现打点
            if (params?.type === 'form' && params?.data?.$event?.agentName) {
                ubcShowLog(EVENT_TRACKING_CONST.luiGuideCreateAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.CREATE_ANSWER_BUBBLE_TYPE]: 2,
                });
            }

            // 引导语选项展现打点
            if (params?.type === 'show' && params?.value === 'recommend_list') {
                ubcShowLog(EVENT_TRACKING_CONST.luiGuideSelectAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.SELECT_ANSWER_BUBBLE_TYPE]: 1,
                });
            }

            // 引导语选项点击打点
            if (params?.value === 'question') {
                const {order, question} = params?.ext;

                ubcClickLog(EVENT_TRACKING_CONST.luiGuideSelectAnswerBubble, {
                    [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
                    [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
                    [EVENT_EXT_KEY_CONST.SELECT_ANSWER_BUBBLE_TYPE]: 1,
                    // 引导语的 order 从 0 开始计数，打点需要从 1 开始计数
                    [EVENT_EXT_KEY_CONST.SELECT_BUTTON_ORDER]: `${order + 1}`,
                    [EVENT_EXT_KEY_CONST.SELECT_BUTTON_CONTENT]: question,
                });
            }
        },
        [loginType, ubcShowLog, ubcClickLog]
    );
}
