import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import urls from '@/links';
import {LingJingIcon} from '@/dicts/index';
import {EVENT_EXT_KEY_CONST, SubPageType, UserTypeLog} from '@/utils/logger/constants/extkey';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUbcLog} from '@/utils/logger/useUbcLogger';

export default function Header({loginType}: {loginType: UserTypeLog}) {
    const navigate = useNavigate();
    const {ubcClickLog} = useUbcLog();

    // 点击跳过按钮，跳转体验中心，替换当前页面路由
    const handleSkip = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.luiGuideSkip, {
            [EVENT_EXT_KEY_CONST.SUB_PAGE_TYPE]: SubPageType.DIVERT,
            [EVENT_EXT_KEY_CONST.LOGIN_TYPE]: loginType,
        });

        navigate(urls.center.raw(), {replace: true});
    }, [navigate, ubcClickLog, loginType]);

    return (
        <div className="h-[84px] pb-1 pl-[175px] pr-[177px] pt-[30px]">
            <div className="flex h-[50px] items-center justify-between">
                <img src={LingJingIcon} className="h-10" />
                <span className="cursor-pointer text-xl text-black opacity-60" onClick={handleSkip}>
                    跳过
                </span>
            </div>
        </div>
    );
}
