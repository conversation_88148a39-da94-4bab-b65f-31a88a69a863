export const PREFIX_TEXT: {
    [key: string]: string;
} = {
    name: '智能体名称',
    introduction: '智能体设定',
};

export const chatViewConfig = {
    // 隐藏顶部导航栏
    nav: {
        type: 'top-menu',
        menu: false,
        share: false,
        dropdown: false,
    },
    // 隐藏脚注
    footer: false,
    // 隐藏背景水印
    watermark: false,
    // 数字形象配置
    digitalFigure: {
        // 隐藏背景
        background: false,
    },
    // 回答气泡配置
    answer: {
        // 隐藏互动区域
        interactiveArea: false,
        // 隐藏赞踩按钮
        thumb: false,
        // 展示调优反馈按钮
        tuning: false,
    },
    inputBox: {
        placeholder: '你可以问我任何问题',
    },
    // 欢迎语在同一行展示
    showRecommendsInOneLine: true,
    // ui.json button 按钮点击后隐藏
    isHiddenBtnOnSubmit: true,
    // 不展示历史记录
    history: false,
};
