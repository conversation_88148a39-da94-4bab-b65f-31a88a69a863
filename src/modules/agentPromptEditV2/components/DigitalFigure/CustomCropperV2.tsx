/**
 * @file 图片裁剪组件，基于CustomCropper重写，去除DigitalType为人物和其他的判断逻辑，调整裁剪比例
 * https://www.npmjs.com/package/react-easy-crop
 * <AUTHOR>
 * 2024/8/26 情感专项-数字角色 图片裁剪组件
 */

import {Slider} from 'antd';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import Cropper from 'react-easy-crop';

import type {Area, Point} from 'react-easy-crop/types';
import classNames from 'classnames';
import {FigureType} from '@/api/agentEditV2/interface';
import CropRegionMask from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/crop-region-mask.png';

import type {EasyCropProps, EasyCropRef} from './types';

const PREFIX = 'img-crop';
const ZOOM_INITIAL = 1;
const ZOOM_STEP = 0.01;

const EasyCrop = forwardRef<EasyCropRef, EasyCropProps>((props, ref) => {
    const {
        cropperRef,
        zoomSlider,
        showDesc,
        fullHeight,

        image,
        aspect,
        minZoom,
        maxZoom,
        disable,
        figureType,
    } = {showDesc: true, ...props};

    const [zoom, setZoom] = useState(ZOOM_INITIAL);

    const onZoomChange = useCallback((value: any) => {
        setZoom(value || ZOOM_INITIAL);
    }, []);

    const [crop, onCropChange] = useState<Point>({x: 0, y: 0});

    const handleZoomChange = useCallback(
        (zoom: number) => {
            if (!disable) {
                setZoom(zoom);
            }
        },
        [disable]
    );
    const handleCropChange = useCallback(
        (location: Point) => {
            if (!disable) {
                onCropChange(location);
            }
        },
        [disable]
    );

    useEffect(() => {
        setZoom(ZOOM_INITIAL);
    }, []);

    const cropPixelsRef = useRef<Area>({width: 0, height: 0, x: 0, y: 0});

    const onCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
        cropPixelsRef.current = croppedAreaPixels;
    }, []);

    useImperativeHandle(ref, () => ({
        cropPixelsRef,
    }));

    return (
        <>
            <Cropper
                ref={cropperRef}
                image={image}
                crop={crop}
                zoom={zoom}
                aspect={aspect}
                minZoom={minZoom}
                maxZoom={maxZoom}
                cropShape="rect"
                showGrid={false}
                onCropChange={handleCropChange}
                onZoomChange={handleZoomChange}
                onCropComplete={onCropComplete}
                style={
                    figureType === FigureType.Dynamic
                        ? {
                              cropAreaStyle: {
                                  backgroundImage: `url(${CropRegionMask})`,
                                  backgroundSize: 'cover',
                                  backgroundRepeat: 'no-repeat',
                                  backgroundPosition: 'top',
                              },
                          }
                        : {}
                }
                classes={{
                    containerClassName: classNames(
                        `${PREFIX}-container ![position:relative] [width:100%] [&~section:first-of-type]:[margin-top:16px] [&~section:last-of-type]:[margin-bottom:16px]`,
                        fullHeight ? 'h-full' : '[height:360px]'
                    ),
                    mediaClassName: `${PREFIX}-media`,
                }}
            />

            {zoomSlider && (
                <section className={`${PREFIX}-control items-top mx-auto mt-[20.5px] flex w-[550px]`}>
                    {/* 滑动缩放比例 */}
                    <div>
                        <Slider
                            className="w-[500px]"
                            min={1}
                            max={2}
                            step={ZOOM_STEP}
                            onChange={onZoomChange}
                            value={zoom}
                            defaultValue={1}
                        />
                    </div>
                </section>
            )}
            {showDesc && (
                <div className="mx-auto mb-6 mt-4 w-fit">
                    {figureType === FigureType.Static ? (
                        <span>调整图片大小，确保主要元素位于提示框内</span>
                    ) : (
                        <p>
                            <span className="text-gray-tertiary">调整图片大小，建议</span>
                            <b>主要元素</b>
                            <span className="text-gray-tertiary">位于人像</span>
                            <b>提示框内，避免肩膀两侧被裁剪</b>
                        </p>
                    )}
                </div>
            )}
        </>
    );
});

export default EasyCrop;
