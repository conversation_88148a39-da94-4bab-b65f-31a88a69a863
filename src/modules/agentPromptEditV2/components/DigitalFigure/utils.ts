/**
 * @file 数字形象公共方法
 * <AUTHOR>
 * 2024/8/27 将数字形象模块多次使用的方法抽取为公共方法
 */

import nth from 'lodash/nth';
import {AgentDigitalFigure, DigitalType} from '@/api/agentEdit/interface';
import {getStringLength} from '@/utils/text';
import {FigureType} from '@/api/agentEditV2/interface';
import {COLORS_MAP} from './constants';

/**
 * 更新数字形象字段，并清空旧数据字段
 *
 * @param digitalFigure 数字形象对象
 * @param backgroundPicUrl 背景图片url
 * @param backgroundPicColor 背景图片颜色
 * @param isSplit 是否抠图
 * @returns 新的数字形象对象
 */
export function getNewDigitalFigure(
    backgroundPicUrl: string,
    backgroundPicColor: string,
    isSplit: boolean,
    figureType?: FigureType,
    bgUrl?: string
) {
    const newDigitalFigure: AgentDigitalFigure = {
        backgroundPicUrl,
        backgroundPicColor,
        splitPicTag: isSplit,
        figureType: figureType || FigureType.Static,
        digitalType: DigitalType.CHARACTER,
        canvasImgUrl: '',
        bgUrl: bgUrl || '',
    };
    return newDigitalFigure;
}

/**
 * 将图片描述快捷指令文本插入到输入框的光标位置处
 *
 * @param oldPicIntroduction 原图片描述
 * @param textInputRef 输入框ref
 * @param value 快捷指令文本
 * @returns {
 *      selectionStart 新光标位置
 *      newPicIntroduction 新图片描述文本
 *  }
 */
export function getNewPicIntroduction(oldPicIntroduction: string, textInputRef: any, value: string): string {
    if (oldPicIntroduction.length === 0) {
        return value;
    } else if (textInputRef?.current) {
        // 获取光标位置，在光标处插入指令文本
        const input: any = textInputRef?.current?.resizableTextArea?.textArea;
        const start: number = input.selectionStart;
        const end: number = input.selectionEnd;
        const before: string = oldPicIntroduction.substring(0, start);
        const after: string = oldPicIntroduction.substring(end, oldPicIntroduction.length);
        // 判断光标前后是否有标点符号截断，如没有则补充中文逗号
        const END_PUNCTUATIONS = [',', '.', '!', '?', ':', ';', '，', '。', '、', '：', '；', '？', '！'];
        const insertion: string =
            (before && !END_PUNCTUATIONS.includes(nth(before, start - 1)!) ? '，' : '') +
            value +
            (after && !END_PUNCTUATIONS.includes(nth(after, 0)!) ? '，' : '');
        const newPicIntroduction = before + insertion + after;

        // 追加后的文案长度不超过 200，则进行追加
        if (+(getStringLength(newPicIntroduction) / 2).toFixed(0) <= 200) {
            return newPicIntroduction;
        }
    }
    return oldPicIntroduction;
}

/**
 * 获取真正的数字形象url，有 digitalType 时为旧数据，用旧的存储字段，没有时则用新字段
 *
 * @param digitalFigure 数字形象对象
 * @returns 数字形象url
 */
export function getFigureUrl(digitalFigure?: AgentDigitalFigure) {
    let figureUrl: string | undefined = '';
    if (digitalFigure) {
        if (digitalFigure.digitalType === DigitalType.CHARACTER) {
            figureUrl = digitalFigure?.backgroundPicUrl || digitalFigure.figureUrl;
        } else {
            figureUrl = digitalFigure.coverUrl;
        }
    }
    return figureUrl;
}

/**
 * 获取数字形象背景颜色，splitPicTag为true时，为新版抠图的背景色，为false不需要背景色，不存在时，为旧版人物形象背景色
 *
 * @param digitalFigure 数字形象对象
 * @returns 数字形象背景颜色
 */
export function getFigureBackgroundColor(digitalFigure?: AgentDigitalFigure): string {
    if (digitalFigure && digitalFigure.digitalType && digitalFigure.digitalType === DigitalType.CHARACTER) {
        if (digitalFigure.splitPicTag) {
            return COLORS_MAP.get(digitalFigure.backgroundPicColor) || 'transparent';
        } else if (digitalFigure.backgroundColor) {
            return COLORS_MAP.get(digitalFigure.backgroundColor) || 'transparent';
        }
    }
    return 'transparent';
}
