import styleRealism from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/style-realism.png';
import styleAnime from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/style-anime.png';
import style3d from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/style-3d.png';
import styleIllustration from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/style-illustration.png';

// 存储背景颜色
export const COLORS = [
    // 高级灰系列
    'linear-gradient(180deg, #D9CECE 0%, #F5F0F0 100%)',
    'linear-gradient(180deg, #D9D4CE 0%, #F4F0EC 100%)',
    'linear-gradient(180deg, #D9D7CE 0%, #F1F0EC 100%)',
    'linear-gradient(180deg, #D3D9CE 0%, #F0F6ED 100%)',
    'linear-gradient(180deg, #CED9D0 0%, #EAF3ED 100%)',
    'linear-gradient(180deg, #CED9D9 0%, #EFF2F2 100%)',
    'linear-gradient(180deg, #CED2D9 0%, #F0F3F8 100%)',
    'linear-gradient(180deg, #CFCED9 0%, #F2F1F8 100%)',
    'linear-gradient(180deg, #D9CED4 0%, #F7F0F4 100%)',
    // 彩色系列
    'linear-gradient(180deg, #FFBFBF 0%, #FFF0F0 100%)',
    'linear-gradient(180deg, #FFDFB2 0%, #FFF3E3 100%)',
    'linear-gradient(180deg, #FFF2B2 0%, #FFF9DB 100%)',
    'linear-gradient(180deg, #DFFFBF 0%, #E9FFD3 100%)',
    'linear-gradient(180deg, #C1FFBF 0%, #E0FFDF 100%)',
    'linear-gradient(180deg, #BFFFFF 0%, #DDFFFF 100%)',
    'linear-gradient(180deg, #BFDAFF 0%, #EEF5FF 100%)',
    'linear-gradient(180deg, #C6BFFF 0%, #EFEDFF 100%)',
    'linear-gradient(180deg, #FFBFE2 0%, #FFEBF6 100%)',
];

// 表单字段backgroundColor只存开始色值，但是前端需要用渐变色，故维护一个map，可以通过backgroundColor字段值取到渐变色
export const COLORS_MAP = new Map([
    // 高级灰系列
    ['#D9CECE', 'linear-gradient(180deg, #D9CECE 0%, #F5F0F0 100%)'],
    ['#D9D4CE', 'linear-gradient(180deg, #D9D4CE 0%, #F4F0EC 100%)'],
    ['#D9D7CE', 'linear-gradient(180deg, #D9D7CE 0%, #F1F0EC 100%)'],
    ['#D3D9CE', 'linear-gradient(180deg, #D3D9CE 0%, #F0F6ED 100%)'],
    ['#CED9D0', 'linear-gradient(180deg, #CED9D0 0%, #EAF3ED 100%)'],
    ['#CED9D9', 'linear-gradient(180deg, #CED9D9 0%, #EFF2F2 100%)'],
    ['#CED2D9', 'linear-gradient(180deg, #CED2D9 0%, #F0F3F8 100%)'],
    ['#CFCED9', 'linear-gradient(180deg, #CFCED9 0%, #F2F1F8 100%)'],
    ['#D9CED4', 'linear-gradient(180deg, #D9CED4 0%, #F7F0F4 100%)'],
    // 彩色系列
    ['#FFBFBF', 'linear-gradient(180deg, #FFBFBF 0%, #FFF0F0 100%)'],
    ['#FFDFB2', 'linear-gradient(180deg, #FFDFB2 0%, #FFF3E3 100%)'],
    ['#FFF2B2', 'linear-gradient(180deg, #FFF2B2 0%, #FFF9DB 100%)'],
    ['#DFFFBF', 'linear-gradient(180deg, #DFFFBF 0%, #E9FFD3 100%)'],
    ['#C1FFBF', 'linear-gradient(180deg, #C1FFBF 0%, #E0FFDF 100%)'],
    ['#BFFFFF', 'linear-gradient(180deg, #BFFFFF 0%, #DDFFFF 100%)'],
    ['#BFDAFF', 'linear-gradient(180deg, #BFDAFF 0%, #EEF5FF 100%)'],
    ['#C6BFFF', 'linear-gradient(180deg, #C6BFFF 0%, #EFEDFF 100%)'],
    ['#FFBFE2', 'linear-gradient(180deg, #FFBFE2 0%, #FFEBF6 100%)'],
]);

export const CANVAS_COLORS = [
    // 高级灰系列
    {start: '#D9CECE', end: '#F5F0F0'},
    {start: '#D9D4CE', end: '#F4F0EC'},
    {start: '#D9D7CE', end: '#F1F0EC'},
    {start: '#D3D9CE', end: '#F0F6ED'},
    {start: '#CED9D0', end: '#EAF3ED'},
    {start: '#CED9D9', end: '#EFF2F2'},
    {start: '#CED2D9', end: '#F0F3F8'},
    {start: '#CFCED9', end: '#F2F1F8'},
    {start: '#D9CED4', end: '#F7F0F4'},
    // 彩色系列
    {start: '#FFBFBF', end: '#FFF0F0'},
    {start: '#FFDFB2', end: '#FFF3E3'},
    {start: '#FFF2B2', end: '#FFF9DB'},
    {start: '#DFFFBF', end: '#E9FFD3'},
    {start: '#C1FFBF', end: '#E0FFDF'},
    {start: '#BFFFFF', end: '#DDFFFF'},
    {start: '#BFDAFF', end: '#EEF5FF'},
    {start: '#C6BFFF', end: '#EFEDFF'},
    {start: '#FFBFE2', end: '#FFEBF6'},
];

// 前端维护图片描述的快捷按钮
const INTRODUCTION_KEY_PREFIX = 'figure_fast_introduction_';
export const FAST_AI_FIGURE_INTRODUCTION_BUTTONS = [
    {
        key: INTRODUCTION_KEY_PREFIX + 'person',
        tag: '人物',
        value: '中青年女性、身材娇小、温柔亲和、微笑、烫卷长发、穿着温柔连衣裙、温馨的居家风格、背景为沙发，中国人',
    },
    {
        key: INTRODUCTION_KEY_PREFIX + 'animal',
        tag: '动物',
        value: '日落光，肥胖可爱柯基，侧着身体，海边度假，有沙滩、大海，蓝天白云',
    },
    {
        key: INTRODUCTION_KEY_PREFIX + 'scenery',
        tag: '风景',
        value: '写实唯美主义，蓝天白云，晴朗的天空，阳光明媚，蜿蜒曲折的山坡上，有一座城楼，城楼前有一条长长的古城墙',
    },
];

// 前端维护风格选择类型
const STYLE_KEY_PREFIX = 'figure_option_style_';
export const STYLE_OPTIONS = [
    {
        key: STYLE_KEY_PREFIX + 'simple',
        tag: '写实',
        value: 1,
        backgroundUrl: styleRealism,
    },
    {
        key: STYLE_KEY_PREFIX + 'anime',
        tag: '二次元',
        value: 2,
        backgroundUrl: styleAnime,
    },
    {
        key: STYLE_KEY_PREFIX + '3d',
        tag: '3D',
        value: 3,
        backgroundUrl: style3d,
    },
    {
        key: STYLE_KEY_PREFIX + 'illustration',
        tag: '插画',
        value: 4,
        backgroundUrl: styleIllustration,
    },
];
