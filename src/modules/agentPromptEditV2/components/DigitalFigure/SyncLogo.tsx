/* eslint-disable max-statements */
/**
 * @file 数字人创建-数字形象：数字形象同步头像hook，点击同步形象到头像时触发
 * <AUTHOR>
 * 2023/4/2
 */

import {Form, Spin, Switch, Tooltip, Checkbox} from 'antd';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import isEqual from 'lodash/isEqual';
import {CheckboxChangeEvent} from 'antd/es/checkbox';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentDigitalFigure, DigitalType} from '@/api/agentEdit/interface';
import {canvasToFile, getLoadedImage} from '@/utils/image';
import {COLORS_MAP} from '@/modules/agentPromptEditV2/components/DigitalFigure/constants';
import {uploadDigitalFigure} from '@/api/agentEdit';
import {getFigureUrl} from '@/modules/agentPromptEditV2/components/DigitalFigure/utils';
import {useIsMobileStore} from '@/store/home/<USER>';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {IsCharacterAgent, IsSelected} from '@/utils/loggerV2/interface';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {usePromptEditContext} from '../../utils';

// eslint-disable-next-line react-refresh/only-export-components
export const setLogo = async (digitalFigure: AgentDigitalFigure) => {
    const figureUrl = getFigureUrl(digitalFigure);
    const img = await getLoadedImage(`${figureUrl}?timestamp=${new Date().valueOf()}`);

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    // 人物形象多一步，绘制渐变背景色
    canvas.width = 246;
    canvas.height = 246;
    // 通过形象类型和是否抠图来判断是否用渐变色

    if (
        digitalFigure?.digitalType === DigitalType.CHARACTER &&
        (digitalFigure?.splitPicTag || digitalFigure.backgroundColor)
    ) {
        if (digitalFigure.bgUrl) {
            const bgImg = await getLoadedImage(digitalFigure.bgUrl);
            const sx = bgImg.width / 2 - bgImg.height / 2;
            const sWidth = bgImg.height;
            const sHeight = bgImg.height;
            ctx.drawImage(bgImg, sx, 0, sWidth, sHeight, 0, 0, canvas.width, canvas.height);
        } else if (digitalFigure.backgroundPicColor) {
            // 抠图的3:4
            const backgroundColor = digitalFigure.backgroundPicColor || digitalFigure.backgroundColor;
            const gradientCss = COLORS_MAP.get(backgroundColor!) || '';
            const stopColor = gradientCss?.substring(36, 43);
            // 创建渐变背景颜色
            const gradient = ctx.createLinearGradient(0, 0, 0, 246);
            gradient.addColorStop(0, backgroundColor!);
            gradient.addColorStop(1, stopColor);
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 246, 328);
        }

        // 绘制图像
        ctx.drawImage(img, 0, 0, 246, (246 * img.height) / img.width);
    } else if (digitalFigure?.digitalType === DigitalType.OTHERS) {
        // 存量其他类型4:3
        const clipX = ((img.width / img.height) * canvas.width - canvas.width) / 2;
        ctx.drawImage(img, clipX, 0, img.height, img.height, 0, 0, 246, 246);
    } else {
        // 新增不抠图9:16，裁顶部
        ctx.drawImage(img, 0, 0, img.width, img.width, 0, 0, 246, 246);
    }

    const imgFile = await canvasToFile(canvas);
    return imgFile;
};

interface SyncDeps {
    coverUrl: string;
    figureUrl: string;
    backgroundPicUrl: string;
    digitalType?: DigitalType;
    logoUrl: string;
}

/**
 * @description
 * 一个同步头像开关组件，可以切换是否使用自定义头像。
 * 如果开启了自定义头像，则会将原始头像替换为自定义头像。
 * 如果关闭了自定义头像，则会还原到原始头像。
 *
 * @param {object} props 组件属性对象，包含以下属性：
 * - value {boolean} 默认值为undefined，表示开关的初始状态，true表示开启自定义头像，false表示关闭自定义头像。
 * - onChange {(val: boolean) => void} 必选，当开关状态改变时调用，参数为当前开关状态（true/false）。
 * - onUrlChange {(val: string) => void} 可选，当更新头像地址时调用，参数为最新的头像地址。
 *
 * @returns {JSX.Element} 返回一个JSX元素，表示同步头像开关组件。
 */

function SyncSwitch(props: {value?: boolean; onChange?: (val: boolean) => void; onUrlChange?: (val: string) => void}) {
    const [lastLogoUrl, setLastLogoUrl] = useState('');
    const {agentInfo, digitalFigure} = usePromptEditStoreV2(store => ({
        agentInfo: store.agentConfig.agentInfo,
        digitalFigure: store.agentConfig.agentInfo.digitalFigure,
    }));
    const isMobile = useIsMobileStore(store => store.isMobile);
    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();
    const isRoleFramework = useUserInfoStore(store => store.userFeatureAccess.role_framework);

    // 同步按钮是否禁用
    const digitalSyncDisabled = useMemo(() => !!digitalFigure && !getFigureUrl(digitalFigure), [digitalFigure]);

    //  同步头像开关状态的自动复位
    const [loading, setLoading] = useState(false);

    // 当用户点击开关的时候，则强制更新开关状态
    const forceUpdateSwitch = useRef(false);
    // 存储上一次的头像和形象信息
    const lastDeps = useRef<SyncDeps | null>(null);

    // 这个副作用用来在头像或形象数据变更的时候，关闭同步头像开关
    useEffect(() => {
        const currentDeps: SyncDeps = {
            coverUrl: digitalFigure?.coverUrl || '',
            figureUrl: digitalFigure?.figureUrl || '',
            backgroundPicUrl: digitalFigure?.backgroundPicUrl || '',
            logoUrl: agentInfo.logoUrl,
        };
        // 首次加载或者没有appId，直接返回
        if (!lastDeps.current || !agentInfo.appId) {
            lastDeps.current = currentDeps;
            return;
        }

        // 用户点了开关，则直接返回
        if (forceUpdateSwitch.current) {
            return;
        }

        // 其他情况下，依赖的数据发生变更，则关闭开关
        if (!isEqual(currentDeps, lastDeps.current)) {
            lastDeps.current = currentDeps;
            props.onChange && props.onChange(false);
        }
    }, [
        digitalFigure?.coverUrl,
        digitalFigure?.figureUrl,
        digitalFigure?.backgroundPicUrl,
        digitalFigure?.digitalType,
        agentInfo.logoUrl,
        props,
        agentInfo.appId,
    ]);

    // 同步用作头像
    const handleSetAgentLogo = useCallback(
        async (e: CheckboxChangeEvent) => {
            const value = e.target.checked;
            try {
                setLoading(true);
                forceUpdateSwitch.current = true;
                if (value) {
                    const imgFile = await setLogo(digitalFigure!);
                    const canvasUrl = await uploadDigitalFigure(imgFile);
                    // 存储上一个头像，用于恢复头像
                    setLastLogoUrl(agentInfo.logoUrl);
                    // 更新基础配置头像
                    props.onUrlChange && props.onUrlChange(canvasUrl);
                } else {
                    // 更新基础配置头像
                    props.onUrlChange && props.onUrlChange(lastLogoUrl);
                }

                props.onChange && props.onChange(value);
            } finally {
                // 打点
                clickLog(EVENT_VALUE_CONST.CHARACTER_USE_AVATAR, EVENT_PAGE_CONST.CODELESS_CREATE, {
                    ...extLog,
                    [EVENT_EXT_KEY_CONST.IS_SELECTED]: value ? IsSelected.TRUE : IsSelected.FALSE,
                    [EVENT_EXT_KEY_CONST.IS_CHARACTER_AGENT]: isRoleFramework
                        ? IsCharacterAgent.YES
                        : IsCharacterAgent.NO,
                });

                setLoading(false);
                setTimeout(() => {
                    forceUpdateSwitch.current = false;
                }, 300);
            }
        },
        [agentInfo.logoUrl, digitalFigure, lastLogoUrl, props, clickLog, extLog, isRoleFramework]
    );

    // 同步用作头像
    const handleSwitchAgentLogo = useCallback(
        async (value: boolean) => {
            try {
                setLoading(true);
                forceUpdateSwitch.current = true;
                if (value) {
                    const imgFile = await setLogo(digitalFigure!);
                    const canvasUrl = await uploadDigitalFigure(imgFile);
                    // 存储上一个头像，用于恢复头像
                    setLastLogoUrl(agentInfo.logoUrl);
                    // 更新基础配置头像
                    props.onUrlChange && props.onUrlChange(canvasUrl);
                } else {
                    // 更新基础配置头像
                    props.onUrlChange && props.onUrlChange(lastLogoUrl);
                }

                props.onChange && props.onChange(value);
            } catch (error) {
                throw error;
            } finally {
                setLoading(false);
                setTimeout(() => {
                    forceUpdateSwitch.current = false;
                }, 300);
            }
        },
        [agentInfo.logoUrl, digitalFigure, lastLogoUrl, props]
    );

    return (
        <>
            {isMobile ? (
                <Switch
                    value={props.value}
                    size="small"
                    onChange={handleSwitchAgentLogo}
                    loading={loading}
                    disabled={digitalSyncDisabled}
                    className="ml-2"
                />
            ) : (
                <>
                    <Form.Item noStyle name={['agentInfo', 'digitalSyncEnabled']}>
                        <Checkbox
                            value={props.value}
                            disabled={loading || digitalSyncDisabled}
                            onChange={handleSetAgentLogo}
                        />
                    </Form.Item>

                    {loading ? (
                        <Spin className="ml-3" size="small" />
                    ) : (
                        <span className="ml-2 text-[14px]">同时用作头像</span>
                    )}
                </>
            )}
        </>
    );
}

export default function SyncLogo(props: {onChange?: (val: string) => void}) {
    const {digitalFigure} = usePromptEditStoreV2(store => ({
        digitalFigure: store.agentConfig.agentInfo.digitalFigure,
    }));

    const {readonly} = usePromptEditContext();

    // 同步按钮是否禁用
    const digitalSyncDisabled = useMemo(
        () => readonly || !getFigureUrl(digitalFigure || undefined),
        [digitalFigure, readonly]
    );

    const handleOnUrlChange = useCallback(
        (e: string) => {
            props.onChange && props.onChange(e);
        },
        [props]
    );

    return (
        <Tooltip title={digitalSyncDisabled ? '请先配置数字形象' : ''}>
            <div className="flex items-center">
                <Form.Item noStyle name={['agentInfo', 'digitalSyncEnabled']}>
                    <SyncSwitch onUrlChange={handleOnUrlChange} />
                </Form.Item>
            </div>
        </Tooltip>
    );
}
