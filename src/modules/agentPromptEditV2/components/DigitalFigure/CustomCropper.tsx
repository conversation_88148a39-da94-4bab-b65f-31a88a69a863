/**
 * @file 图片裁剪组件，基于react-easy-crop重写
 * https://www.npmjs.com/package/react-easy-crop
 * <AUTHOR>
 * 2023/3/28 数字形象 图片裁剪组件
 */

import {Slider} from 'antd';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import Cropper from 'react-easy-crop';

import type {Area, Point} from 'react-easy-crop/types';
import classNames from 'classnames';

import {DigitalType} from '@/api/agentEdit/interface';
import CropRegionMask from '@/modules/agentPromptEditV2/components/DigitalFigure/assets/crop-region-mask.png';
import type {EasyCropProps, EasyCropRef} from './types';

const PREFIX = 'img-crop';
const ZOOM_INITIAL = 1;
const ZOOM_STEP = 0.01;

const EasyCrop = forwardRef<EasyCropRef, EasyCropProps>((props, ref) => {
    const {
        cropperRef,
        zoomSlider,
        showDesc,
        fullHeight,

        image,
        aspect,
        minZoom,
        maxZoom,
        digitalType,
        disable,
    } = {showDesc: true, ...props};

    const [zoom, setZoom] = useState(ZOOM_INITIAL);

    const onZoomChange = useCallback((value: any) => {
        setZoom(value || ZOOM_INITIAL);
    }, []);

    const [crop, onCropChange] = useState<Point>({x: 0, y: 0});

    const handleZoomChange = useCallback(
        (zoom: number) => {
            if (!disable) {
                setZoom(zoom);
            }
        },
        [disable]
    );
    const handleCropChange = useCallback(
        (location: Point) => {
            if (!disable) {
                onCropChange(location);
            }
        },
        [disable]
    );

    useEffect(() => {
        setZoom(ZOOM_INITIAL);
    }, []);

    const cropPixelsRef = useRef<Area>({width: 0, height: 0, x: 0, y: 0});

    const onCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
        cropPixelsRef.current = croppedAreaPixels;
    }, []);

    useImperativeHandle(ref, () => ({
        cropPixelsRef,
    }));

    return (
        <>
            <Cropper
                ref={cropperRef}
                image={image}
                crop={crop}
                zoom={zoom}
                aspect={aspect}
                minZoom={minZoom}
                maxZoom={maxZoom}
                cropShape="rect"
                showGrid={false}
                onCropChange={handleCropChange}
                onZoomChange={handleZoomChange}
                onCropComplete={onCropComplete}
                style={
                    digitalType === DigitalType.CHARACTER
                        ? {
                              cropAreaStyle: {
                                  backgroundImage: `url(${CropRegionMask})`,
                                  backgroundSize: '100%',
                                  backgroundRepeat: 'no-repeat',
                              },
                          }
                        : {}
                }
                classes={{
                    containerClassName: classNames(
                        `${PREFIX}-container ![position:relative] [width:100%] [&~section:first-of-type]:[margin-top:16px] [&~section:last-of-type]:[margin-bottom:16px]`,
                        fullHeight ? 'h-full' : '[height:360px]'
                    ),
                    mediaClassName: `${PREFIX}-media`,
                    cropAreaClassName: `${digitalType === DigitalType.CHARACTER ? 'border-none' : ''}`,
                }}
            />

            {zoomSlider && (
                <section className={`${PREFIX}-control items-top mx-auto mt-[20.5px] flex w-[550px]`}>
                    {/* 滑动缩放比例 */}
                    <div>
                        <Slider
                            className="w-[500px]"
                            min={1}
                            max={2}
                            step={ZOOM_STEP}
                            onChange={onZoomChange}
                            value={zoom}
                            defaultValue={1}
                        />
                    </div>
                    {/* 3.29 交互变更，不需要这块，暂时注释 */}
                    {/* <div className="ml-[15px] flex h-[32px] w-[174px] justify-between rounded-[6px] border border-[#DEE0E7]">
                        <button
                            className={buttonClass}
                            onClick={() => setZoom(zoom - ZOOM_STEP)}
                            disabled={zoom - ZOOM_STEP < 1}
                        >
                            －
                        </button>
                        <ConfigProvider
                            theme={{
                                components: {
                                    InputNumber: {
                                        paddingInline: 36,
                                    },
                                },
                            }}
                        >
                            <InputNumber
                                min={1}
                                max={2}
                                value={inputNumber}
                                onChange={onZoomChange}
                                controls={false}
                                className="flex justify-center border-none"
                            />
                        </ConfigProvider>

                        <button
                            className={buttonClass}
                            onClick={() => setZoom(zoom + ZOOM_STEP)}
                            disabled={zoom + ZOOM_STEP > 2}
                        >
                            ＋
                        </button>
                    </div> */}
                </section>
            )}
            {showDesc && (
                <div className="mx-auto mb-6 mt-4 w-fit">
                    {digitalType === DigitalType.CHARACTER ? (
                        <span>调整图片大小，确保人像位于提示框内</span>
                    ) : (
                        <span>调整图片大小，确保主要元素位于提示框内</span>
                    )}
                </div>
            )}
        </>
    );
});

export default EasyCrop;
