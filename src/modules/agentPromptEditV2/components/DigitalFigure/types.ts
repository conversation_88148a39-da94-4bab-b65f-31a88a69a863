import type {ModalProps, UploadProps} from 'antd';
import type {ForwardedRef, MutableRefObject} from 'react';
import type {default as Cropper, CropperProps} from 'react-easy-crop';
import type {Area} from 'react-easy-crop/types';
import {FigureType} from '@/api/agentEditV2/interface';

export type BeforeUpload = Exclude<UploadProps['beforeUpload'], undefined>;
export type BeforeUploadReturnType = ReturnType<BeforeUpload>;

export interface ImgCropProps {
    quality?: number;
    fillColor?: string;

    zoomSlider?: boolean;
    rotationSlider?: boolean;
    aspectSlider?: boolean;
    showReset?: boolean;
    resetText?: string;

    aspect?: number;
    minZoom?: number;
    maxZoom?: number;
    cropShape?: 'rect' | 'round';
    showGrid?: boolean;
    cropperProps?: Omit<
        CropperProps,
        | 'image'
        | 'crop'
        | 'zoom'
        | 'rotation'
        | 'aspect'
        | 'minZoom'
        | 'maxZoom'
        | 'zoomWithScroll'
        | 'cropShape'
        | 'showGrid'
        | 'onCropChange'
        | 'onZoomChange'
        | 'onRotationChange'
        | 'onCropComplete'
        | 'classes'
    >;

    modalClassName?: string;
    modalTitle?: string;
    modalWidth?: number | string;
    modalOk?: string;
    modalCancel?: string;
    onModalOk?: (value: BeforeUploadReturnType) => void;
    onModalCancel?: (resolve: (value: BeforeUploadReturnType) => void) => void;
    modalProps?: Omit<
        ModalProps,
        | 'className'
        | 'title'
        | 'width'
        | 'okText'
        | 'cancelText'
        | 'onOk'
        | 'onCancel'
        | 'open'
        | 'visible'
        | 'wrapClassName'
        | 'maskClosable'
        | 'destroyOnClose'
    >;

    beforeCrop?: BeforeUpload;
    children: JSX.Element;
}

export interface EasyCropRef {
    cropPixelsRef: MutableRefObject<Area>;
}

export type EasyCropProps = {
    cropperRef: ForwardedRef<Cropper>;
    image: string;
    digitalType?: number;
    figureType?: FigureType;
    fullHeight?: boolean;
    showDesc?: boolean;
    disable?: boolean;
} & Required<Pick<ImgCropProps, 'zoomSlider' | 'aspectSlider' | 'showReset' | 'aspect' | 'minZoom' | 'maxZoom'>>;
