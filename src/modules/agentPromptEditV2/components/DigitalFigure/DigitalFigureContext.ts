/**
 * @file 数字人创建-数字形象：上传图片组件 > 裁剪组件 > 背景选择组件 上下文数据共享
 * <AUTHOR>
 * 2023/3/29
 */

import {createContext} from 'react';
import {DigitalCreateStep} from '@/api/agentEdit/interface';
import {FigureType} from '@/api/agentEditV2/interface';

export interface ContextType {
    /** 当前执行步骤 */
    currentSetp: number;
    /** 去除背景的图 */
    splitedImage: string;
    /** 原始上传的图 用于背景选择返回上一步 */
    originalImage: string;
    /** 静态、动态数字人标识 */
    figureType: FigureType;
}

export const DigitalFigureContext = createContext<ContextType>({
    currentSetp: DigitalCreateStep.OnceCrop,
    splitedImage: '',
    originalImage: '',
    figureType: FigureType.Static,
});
