export const isProd = () => {
    return ['agents.baidu.com', 'plugin.baidu.com', 'plugin-prod.now.baidu.com', 'plugin.now.baidu.com'].includes(
        window.location.hostname
    );
};

// 线上的 conversation 接口必须由前端直接请求，不能经过 FCNAP 转发，原因是浏览器对持久连接的请求数量有限制
const AGENT_PROXY_WS_SERVER_PREFIX = 'https://agent-proxy-ws.baidu.com';

// 其余接口经过 FCNAP 转发到 proxy-server
const AGENT_PROXY_SERVER_PREFIX = window.location.origin;

export const serviceUrl = {
    getAppInfo: `${AGENT_PROXY_SERVER_PREFIX}/get_app_info`,
    init: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/init`,
    conversation: `${isProd() ? AGENT_PROXY_WS_SERVER_PREFIX : AGENT_PROXY_SERVER_PREFIX}/agent/call/conversation`,
    history: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/history`,
    cleanContext: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/clean_context`,
    dangerCheck: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/danger_check`,
    feedBackLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/like/receive`,
    feedBackDisLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/dislike/receive`,
    getLike: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/like/get`,
    cancel: `${AGENT_PROXY_SERVER_PREFIX}/agent/call/cancel`,
};

/**
 * 展示插件返回信息弹窗的 tab item key
 */
export const enum FunctionTabKey {
    Response = 'function-response',
    OriginResponse = 'function-origin-response',
}

/**
 * 插件弹窗类型打点值
 */
export enum LogBoxType {
    input = 1,
    output = 2,
    originOutput = 3,
}

/**
 * 插件弹窗tab名称
 */
export const LogBoxTabName = {
    [FunctionTabKey.Response]: 1,
    [FunctionTabKey.OriginResponse]: 2,
};
