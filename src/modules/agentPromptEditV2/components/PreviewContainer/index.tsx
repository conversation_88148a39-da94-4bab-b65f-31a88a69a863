/**
 * @file 智能体开发预览容器
 *
 */
import {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import '@baidu/lingjing-fe-agent-adapter/dist/js/agent.js';
import '@baidu/lingjing-fe-agent-adapter/dist/css/agent.css';
import {useParams} from 'react-router-dom';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import guideEmitter from '@/store/agent/GuideEmitter';
import {
    VersionType,
    ChatViewEventType,
    DebugInfo,
} from '@/modules/agentPromptEditV2/components/PreviewContainer/interface';
import {serviceUrl} from '@/modules/agentPromptEditV2/components/PreviewContainer/constant';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {LuiSdkScene} from '@/dicts';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import workflowRunEmitter from '@/modules/workflow/context/RunContext/emitter';
import {isConfigView} from '../../utils';
import DebugInfoModal from './DebugInfoModal';
import previewContainerEmitter from './emitter';

interface PreviewContainerProps {
    platform: 'pc' | 'wise';
    chatViewConfig: ChatViewConfig;
    onClose?: () => void;
    luiSdkScene: LuiSdkScene;
    bodyHeight?: string;
    chatViewHeight?: string;
}
interface NavConfig {
    type: string;
    menu?: boolean;
    hide?: boolean;
    businessCard?: boolean;
    previewTitle?: string;
    share?: boolean;
    dropdown?: boolean;
}

interface DigitalFigureConfig {
    background: boolean;
}

interface AnswerConfig {
    interactiveArea?: boolean;
    thumb?: boolean;
    tuning?: boolean;
    feedback?: boolean;
}

interface InputBoxConfig {
    placeholder: string;
}

// todo 引用 sdk 里的类型
interface ChatViewConfig {
    nav: NavConfig;
    footer: boolean;
    watermark: boolean;
    digitalFigure?: DigitalFigureConfig;
    answer?: AnswerConfig;
    inputBox?: InputBoxConfig;
    showRecommendsInOneLine?: boolean;
    isHiddenBtnOnSubmit?: boolean;
    history?: boolean;
}

const Chat = window.LingJingAgentSDK.AgentPreview;

const PreviewContainer = forwardRef(
    (
        {
            platform = 'pc',
            chatViewConfig,
            onClose = () => {},
            luiSdkScene,
            chatViewHeight,
            bodyHeight,
        }: PreviewContainerProps,
        ref
    ) => {
        // eslint-disable-next-line prefer-const
        let {appId, lastSaveTime} = usePromptEditStoreV2(store => ({
            appId: store.agentConfig?.agentInfo?.appId,
            lastSaveTime: store.display?.lastSaveTime,
        }));
        const [hasEnhancedAccess] = useUserInfoStore(store => [store.userInfoData?.userInfo.hasEnhancedAccess]);

        const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
        const [modalOpen, setModalOpen] = useState(false);

        const configView = isConfigView();
        const {id} = useParams();
        appId = configView ? id || '' : appId;

        const previewContainerRef = useRef<HTMLDivElement>(null);
        const chatRef = useRef<typeof Chat | null>(null);

        const {clickLog, showLog, displayLog} = useUbcLogV2();
        const ext = useExtLog();

        useEffect(() => {
            if (appId && !chatRef.current && Chat) {
                // 初始化 ChatViewSDK
                chatRef.current = new Chat({
                    agentId: appId,
                    container: previewContainerRef.current!,
                    serviceUrl,
                    versionType: isConfigView() ? VersionType.Online : VersionType.Develop,
                    chatViewConfig: {
                        ...chatViewConfig,
                        sdkScene: luiSdkScene,
                        userInfo: {
                            hasEnhancedAccess,
                        },
                        sendPlatformLogHandler: {
                            clickLog,
                            showLog,
                            displayLog,
                            ext,
                        },
                        bodyHeight,
                    },
                    platform,
                });

                // 渲染对话容器
                chatRef.current?.render();
            }

            // 用户点击预览右上角关闭按钮，隐藏预览容器
            const topNavHideClickCallback = () => {
                onClose?.();
            };

            // 用户在预览容器内反馈提交后，通知调优 tab 展示 toast
            const submitClickCallback = () => {
                guideEmitter.emit('feedback');
            };

            const answerDebugInfoCallback = (event: {data: DebugInfo}) => {
                setModalOpen(true);
                setDebugInfo(event.data);
            };

            // 预览容器中对话返回超时后，通知模型选择按钮展示 popup
            const firstMessageTimeoutCallback = () => {
                guideEmitter.emit('firstMessageTimeout');
            };

            // 用户点击回答气泡中的推荐按钮，通知 RecommendDialogue 展示下拉列表
            const answerRecommendCallback = () => {
                guideEmitter.emit('recommend');
            };

            // 回答气泡推荐按钮展示展示
            const answerRecommendShowCallback = () => {
                guideEmitter.emit('answerRecommendShow');
            };

            // 对话中止
            const conversationAbortCallback = () => {
                workflowRunEmitter.emit('conversationAbort');
            };

            // 用户中止对话
            const conversationUserAbortCallback = () => {
                workflowRunEmitter.emit('conversationUserAbort');
            };

            // 发送第一条消息
            const sendFirstMessageCallback = (data: any) => {
                workflowRunEmitter.emit('sendFirstMessage', data);
            };

            // 对话错误
            const conversationErrorCallback = (e: any) => {
                workflowRunEmitter.emit('conversationError', e);
            };

            const EVENT_HANDLERS = [
                {event: ChatViewEventType.TopNavHideClick, handler: topNavHideClickCallback},
                {event: ChatViewEventType.AnswerTuningSubmitClick, handler: submitClickCallback},
                {event: ChatViewEventType.AnswerDebugInfo, handler: answerDebugInfoCallback},
                {event: ChatViewEventType.FirstMessageTimeout, handler: firstMessageTimeoutCallback},
                {event: ChatViewEventType.ConversationAbort, handler: conversationAbortCallback},
                {event: ChatViewEventType.SendFirstMessage, handler: sendFirstMessageCallback},
                {event: ChatViewEventType.ConversationUserAbort, handler: conversationUserAbortCallback},
                {event: ChatViewEventType.ConversationError, handler: conversationErrorCallback},
                {event: ChatViewEventType.AnswerRecommendClick, handler: answerRecommendCallback},
                {event: ChatViewEventType.AnswerRecommendShow, handler: answerRecommendShowCallback},
            ];

            EVENT_HANDLERS.forEach(({event, handler}) => {
                chatRef.current?.on(event, handler);
            });

            return () => {
                EVENT_HANDLERS.forEach(({event, handler}) => {
                    chatRef.current?.off(event, handler);
                });
            };
        }, [
            appId,
            platform,
            chatViewConfig,
            onClose,
            configView,
            luiSdkScene,
            hasEnhancedAccess,
            clickLog,
            showLog,
            displayLog,
            ext,
            bodyHeight,
        ]);

        // 保存成功后，触发预览容器刷新
        useEffect(() => {
            if (lastSaveTime === undefined) {
                return;
            }

            chatRef.current?.refreshNav?.();
            chatRef.current?.refreshChatContent?.();
        }, [lastSaveTime]);

        // 预览容器刷新事件处理
        useEffect(() => {
            const handleRefresh = () => {
                chatRef.current?.refreshNav?.();
            };

            previewContainerEmitter.on('refresh', handleRefresh);
            return () => {
                previewContainerEmitter.off('refresh', handleRefresh);
            };
        }, [lastSaveTime]);

        // 监听工作流提示事件
        useEffect(() => {
            const runTipsHandler = () => {
                chatRef.current?.showInputTips({message: '请发送问题完成工作流验证'});
            };

            workflowRunEmitter.on('runTips', runTipsHandler);

            return () => {
                workflowRunEmitter.off('runTips', runTipsHandler);
            };
        }, []);

        // 对外暴露 setBodyHeight 方法调用
        useImperativeHandle(ref, () => {
            return {
                setBodyHeight(height: string) {
                    chatRef.current?.setBodyHeight(height);
                },
                showActiveMsg: () => {
                    chatRef.current?.reloadChatContent?.({showLatestPair: true});
                },
                deleteRecommend: (msgId: string) => {
                    chatRef.current?.deleteRecommend(msgId);
                },
            };
        });

        return (
            <div className="flex w-full" style={{height: chatViewHeight ? chatViewHeight : '100%'}}>
                <div className="h-full w-full" ref={previewContainerRef} />

                {debugInfo && (
                    <DebugInfoModal debugInfo={debugInfo} modalOpen={modalOpen} setModalOpen={setModalOpen} />
                )}
            </div>
        );
    }
);

export default PreviewContainer;
