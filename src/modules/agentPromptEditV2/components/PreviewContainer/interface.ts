export enum VersionType {
    Online = 'online',
    Develop = 'develop',
    Audit = 'audit',
}

export enum ChatViewEventType {
    // 反馈调优气泡内的提交按钮监听
    AnswerTuningSubmitClick = 'answerTuningSubmitClick',
    // 导航栏关闭按钮点击监听
    TopNavHideClick = 'topNavHideClick',
    // 清除按钮点击监听
    ClearBtnClick = 'cleanBtnClick',
    // 回答气泡中的调试信息
    AnswerDebugInfo = 'answerDebugInfo',
    // 单轮对话端到端超时12秒
    FirstMessageTimeout = 'firstMessageTimeout',
    // 对话中止
    ConversationAbort = 'conversationAbort',
    // 发送第一条消息
    SendFirstMessage = 'sendFirstMessage',
    // 用户中止对话
    ConversationUserAbort = 'conversationUserAbort',
    // 对话错误
    ConversationError = 'conversationError',
    // 回答气泡中的推荐按钮监听
    AnswerRecommendClick = 'answerRecommendClick',
    // 推荐按钮展示
    AnswerRecommendShow = 'answerRecommendShow',
}

export enum DebugInfoType {
    Rag = 'rag',
    // 插件
    FunctionRequest = 'function-request',
    FunctionResponse = 'function-response',
    // 工作流工具
    WorkflowToolRequest = 'workflow-tool-request',
    WorkflowToolResponse = 'workflow-tool-response',
}

/**
 * 插件的请求信息
 * 协议文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MYFIkxNl2o/-UXFkEkRyR/RkTIYagoc0u5ET
 */
export interface FunctionRequestInfo {
    url: string;
    header: string;
    body: string;
    method: string;
    proto: string;
}

/** 知识库调试信息 */
export interface RagDebugInfo {
    type: DebugInfoType.Rag;
    score: number;
    url: string;
    text: string;
    urlPrefix: string;
    ragHeader?: string;
    rewriteQuery?: string;
}

/** 插件调试信息 */
export interface FunctionDebugInfo {
    end: {
        ext: string;
        function: {
            name: string;
            output: string;
        };
    };
    start: {
        ext: string;
        function: {
            name: string;
            arguments: string;
        };
    };
}

/** 工作流工具调试信息 */
export interface WorkflowToolDebugInfo {
    end: {
        ext?: string;
        workflow: {
            name: string;
            output: string;
        };
    };
    start: {
        ext?: string;
        workflow: {
            name: string;
            input: string;
        };
    };
}

/**
 * answerDebugInfo 事件返回的事件 data 类型
 */
export interface DebugInfo {
    type: DebugInfoType;
    content: RagDebugInfo | FunctionDebugInfo | WorkflowToolDebugInfo;
}
