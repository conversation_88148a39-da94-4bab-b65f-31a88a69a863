/**
 * @file 查看调试信息的弹窗
 * <AUTHOR>
 */
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Button, ConfigProvider, Modal, Row, Tabs, Tooltip, message} from 'antd';
import styled from '@emotion/styled';
import copy from 'copy-to-clipboard';
import {
    DebugInfoType,
    FunctionDebugInfo,
    DebugInfo,
    RagDebugInfo,
    FunctionRequestInfo,
    WorkflowToolDebugInfo,
} from '@/modules/agentPromptEditV2/components/PreviewContainer/interface';
import {ScrollContainer} from '@/components/ScrollContainer';
import {reportError} from '@/utils/monitor/useCommonError';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import <PERSON><PERSON><PERSON>ie<PERSON> from './JsonViewer';
import {FunctionTabKey, LogBoxType} from './constant';

const StyledModal = styled(Modal)`
    .ant-modal-content {
        padding-top: 24px !important;
        padding-bottom: 24px !important;

        .ant-modal-header {
            margin-bottom: 12px !important;
        }
    }
`;

const StyledScrollContainer = styled(ScrollContainer)`
    overflow: auto;
    padding: 16px;
    background-color: #eceef380;
    border-radius: 8px;
`;

const StyledTabs = styled(Tabs)`
    .ant-tabs-nav:before {
        border-bottom: none !important;
    }
`;

/** 复制的 json 类型 */
const enum JsonType {
    Input = 'input',
    Output = 'output',
    WorkflowNodeInput = 'workflowNodeInput',
    WorkflowNodeOutput = 'workflowNodeOutput',
}

enum RelateLevel {
    High = 'high',
    Middle = 'middle',
    Low = 'low',
}

export default function DebugInfoModal({
    debugInfo,
    modalOpen,
    setModalOpen,
}: {
    debugInfo: DebugInfo;
    modalOpen: boolean;
    setModalOpen: (open: boolean) => void;
}) {
    const [functionTabActiveKey, setFunctionTabActiveKey] = useState(FunctionTabKey.Response);
    const [cURL, setCURL] = useState('');

    /** 插件给大模型的输入 json */
    const [output, setOutput] = useState('');
    /** 插件的原始输出，可能是一段 sse string 也可能是 json string */
    const [originOutput, setOriginOutput] = useState('');
    /** 输入给插件的 json */
    const [input, setInput] = useState('');

    const {clickLog, showLog} = useUbcLogV2();

    const ext = useExtLog();

    const [workflowToolInput, setWorkflowToolInput] = useState<string | {input: string}>('');
    const [workflowToolOutput, setWorkflowToolOutput] = useState<string | {output: string}>('');

    const copyCURL = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PLUGIN_COPY_CURL, EVENT_PAGE_CONST.CODELESS_CREATE, {
            ...ext,
            [EVENT_EXT_KEY_CONST.PLUGIN_NAME]: (debugInfo?.content as FunctionDebugInfo).start.function.name,
            [EVENT_EXT_KEY_CONST.PLUGIN_BOX_TYPE]: LogBoxType.input,
        });

        if (!cURL) {
            message.error('复制CURL失败');
            return;
        }

        copy(cURL);
        message.success('复制CURL成功');
    }, [cURL, clickLog, debugInfo?.content, ext]);

    const handleCopy = useCallback(
        (type: string) => () => {
            const copyOriginOutput = functionTabActiveKey === FunctionTabKey.OriginResponse;

            let boxType = -1;

            switch (type) {
                case JsonType.Input:
                    copy(JSON.stringify(input));
                    message.success('复制JSON成功');

                    boxType = LogBoxType.input;
                    break;
                case JsonType.Output:
                    copy(JSON.stringify(copyOriginOutput ? originOutput : output));
                    message.success(copyOriginOutput ? '复制原始数据成功' : '复制成功');

                    boxType = copyOriginOutput ? LogBoxType.originOutput : LogBoxType.output;
                    break;
                case JsonType.WorkflowNodeInput:
                    copy(JSON.stringify(workflowToolInput));
                    message.success('复制成功');
                    break;
                case JsonType.WorkflowNodeOutput:
                    copy(JSON.stringify(workflowToolOutput));
                    message.success('复制成功');
                    break;
                default:
                    message.error('复制失败');
            }

            clickLog(EVENT_VALUE_CONST.PLUGIN_COPY_JSON, EVENT_PAGE_CONST.CODELESS_CREATE, {
                ...ext,
                [EVENT_EXT_KEY_CONST.PLUGIN_NAME]: (debugInfo?.content as FunctionDebugInfo).start.function.name,
                [EVENT_EXT_KEY_CONST.PLUGIN_BOX_TYPE]: boxType,
            });
        },
        [
            clickLog,
            debugInfo?.content,
            ext,
            functionTabActiveKey,
            input,
            originOutput,
            output,
            workflowToolInput,
            workflowToolOutput,
        ]
    );

    const closeModal = useCallback(() => {
        setModalOpen(false);
    }, [setModalOpen]);

    const getCurl = (requestInfo: FunctionRequestInfo): string => {
        try {
            const header = JSON.parse(requestInfo.header);

            let cURL = `curl ${requestInfo.url} -X ${requestInfo.method}`;
            Object.keys(header).forEach(key => {
                cURL = `${cURL} -H '${key}:${header[key as any][0]}'`;
            });
            if (requestInfo.body) {
                cURL = `${cURL} -d '${requestInfo.body}'`;
            }
            return cURL;
        } catch (e) {
            reportError(e);
            console.error('Parse request_info header fail', e);
            return '';
        }
    };

    /**
     * 展示插件返回信息弹窗的 tab item
     * @param output 插件的输出参数（输入给大模型），可能是 json 或者是纯文本字符串
     * @param originOutput 插件的原始输出，可能是 json 也可能是 sse 字符串（比如：event:message\ndata: {"message": "单词"}\n\nevent:message\ndata: {"message": "单词添加"}\n\nevent:message\ndata: {"message": "单词添加成功"}）
     * @param originOutputIsJSON 插件的原始输出是否是 json 形式
     */
    const functionResponseTabItem = (output: any, originOutput: any, originOutputIsJSON: boolean) => {
        return [
            {
                key: FunctionTabKey.Response,
                label: '插件返回',
                children: (
                    <StyledScrollContainer className="h-[383px]">
                        {typeof output === 'object' ? <JsonViewer value={output} /> : <pre>{output}</pre>}
                    </StyledScrollContainer>
                ),
            },
            originOutput && {
                key: FunctionTabKey.OriginResponse,
                label: '原始数据',
                children: (
                    <StyledScrollContainer className="h-[383px]">
                        {originOutputIsJSON ? <JsonViewer value={originOutput} /> : <pre>{originOutput}</pre>}
                    </StyledScrollContainer>
                ),
            },
        ];
    };

    const changeFunctionTab = useCallback(
        (activeKey: string) => {
            const key = activeKey as FunctionTabKey;

            setFunctionTabActiveKey(key);

            clickLog(EVENT_VALUE_CONST.PANEL_PLUGIN_BOX_TAB, EVENT_PAGE_CONST.CODELESS_CREATE, {
                ...ext,
                [EVENT_EXT_KEY_CONST.PLUGIN_BOX_TYPE]:
                    key === FunctionTabKey.OriginResponse ? LogBoxType.originOutput : LogBoxType.output,
                [EVENT_EXT_KEY_CONST.PLUGIN_NAME]: (debugInfo?.content as FunctionDebugInfo).start.function.name,
            });
        },
        [clickLog, debugInfo?.content, ext]
    );

    /** 插件调试信息组件 */
    // eslint-disable-next-line complexity, max-statements
    const functionInfo = useMemo(() => {
        if (debugInfo.type !== DebugInfoType.FunctionRequest && debugInfo.type !== DebugInfoType.FunctionResponse) {
            return <></>;
        }

        const functionDebugInfo = debugInfo.content as FunctionDebugInfo;

        let outputData = null;
        try {
            outputData = JSON.parse(functionDebugInfo.end.function.output || '{}');
        } catch (e) {
            outputData = functionDebugInfo.end.function.output;
        }

        setOutput(outputData);

        // 插件请求信息，用于拼接 CURL
        let requestInfo = null;
        try {
            requestInfo = JSON.parse(functionDebugInfo.start.ext || '{}').request_info || {};
            setCURL(getCurl(requestInfo));
        } catch (e) {
            requestInfo = '';
            reportError(e);
            console.error('Parse functionDebugInfo.start.ext request_info fail', e);
        }

        // 插件输入
        let inputJson = null;
        try {
            inputJson = JSON.parse(functionDebugInfo.start.function.arguments);
            setInput(inputJson);
        } catch (e) {
            inputJson = '';
            reportError(e);
            console.error('Parse functionDebugInfo.start.function.arguments fail', e);
        }

        // 插件的原始输出
        let originResponse = null;
        try {
            originResponse = JSON.parse(functionDebugInfo.end.ext || '{}').response_info;
        } catch (e) {
            originResponse = '';
        }

        let isJSON = false;
        let originOutput = '';
        if (originResponse && originResponse.header && originResponse.body) {
            let header = null;
            try {
                header = JSON.parse(originResponse.header);
            } catch (e) {
                header = {};
                reportError(e);
                console.error('Parse functionDebugInfo.end.ext response_info fail', e);
            }

            // 原始输出可能是 json 也可能是 SSE 形式
            isJSON = header['Content-Type'] === 'application/json';
            originOutput = isJSON ? JSON.parse(originResponse.body) : originResponse.body;
            setOriginOutput(originOutput);
        }

        if (debugInfo.type === DebugInfoType.FunctionResponse) {
            return (
                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {
                                horizontalItemGutter: 24,
                                horizontalItemPadding: '12px 0 7px 0',
                                horizontalMargin: '0 0 13px 0',
                            },
                        },
                    }}
                >
                    <StyledTabs
                        defaultActiveKey={FunctionTabKey.Response}
                        items={functionResponseTabItem(outputData, originOutput, isJSON)}
                        onChange={changeFunctionTab}
                    />
                </ConfigProvider>
            );
        }

        if (debugInfo.type === DebugInfoType.FunctionRequest) {
            return (
                <StyledScrollContainer className="mt-6 h-[425px]">
                    <JsonViewer value={inputJson} />
                </StyledScrollContainer>
            );
        }
        return <></>;
    }, [debugInfo, setOutput, changeFunctionTab]);

    const footer = useMemo(() => {
        switch (debugInfo.type) {
            case DebugInfoType.FunctionRequest:
                return (
                    <div className="mt-6 flex justify-end">
                        {cURL && (
                            <Button className="mr-2" onClick={copyCURL}>
                                复制CURL
                            </Button>
                        )}
                        <Button onClick={handleCopy(JsonType.Input)}>复制JSON</Button>
                    </div>
                );
            case DebugInfoType.FunctionResponse:
                return (
                    <div className="mt-6 flex justify-end">
                        <Button onClick={handleCopy(JsonType.Output)}>
                            {functionTabActiveKey === FunctionTabKey.Response ? '复制' : '复制原始数据'}
                        </Button>
                    </div>
                );
            case DebugInfoType.WorkflowToolRequest:
                return (
                    <div className="mt-6 flex justify-end">
                        <Button onClick={handleCopy(JsonType.WorkflowNodeInput)}>复制JSON</Button>
                    </div>
                );
            case DebugInfoType.WorkflowToolResponse:
                return (
                    <div className="mt-6 flex justify-end">
                        <Button onClick={handleCopy(JsonType.WorkflowNodeOutput)}>复制</Button>
                    </div>
                );
            default:
                return null;
        }
    }, [debugInfo.type, cURL, copyCURL, handleCopy, functionTabActiveKey]);

    /** 知识库调试信息组件 */
    const ragInfo = useMemo(() => {
        if (debugInfo.type !== DebugInfoType.Rag) {
            return <></>;
        }

        const ragInfo = debugInfo.content as RagDebugInfo;

        const ragLevel =
            ragInfo.score >= 0.8 ? RelateLevel.High : ragInfo.score >= 0.5 ? RelateLevel.Middle : RelateLevel.Low;
        const ragScoreText =
            (ragLevel === RelateLevel.Low ? '弱' : ragLevel === RelateLevel.High ? '强' : '') + '相关' + ragInfo.score;

        return (
            <StyledScrollContainer className="mb-1 mt-4 max-h-[485px]">
                {ragInfo.rewriteQuery && (
                    <div className="mb-3 flex items-center text-black">
                        <span className="leading-none">检索问题</span>

                        <Tooltip
                            overlayStyle={{
                                maxWidth: 'unset',
                                boxShadow: '0px 8px 10px -4px #1E1F244D',
                                borderRadius: 12,
                            }}
                            overlayInnerStyle={{
                                borderRadius: 12,
                            }}
                            title={'大模型根据对话上下文润色改写用户意图，生成检索问题'}
                        >
                            <span className="iconfont icon-questionCircle ml-[3px] mr-[3px] cursor-pointer leading-none text-gray-tertiary"></span>
                        </Tooltip>
                        <span className="leading-none">:</span>
                        <span className="ml-[3px] leading-none">{ragInfo.rewriteQuery}</span>
                    </div>
                )}
                <div className="mb-2 flex items-center">
                    <span
                        className={
                            'flex-shrink-0 rounded-[3px] px-[3px] py-[2.5px] text-[11px] font-medium leading-none ' +
                            (ragInfo.score >= 0.5 ? 'bg-[#5562F21A] text-primary' : 'bg-[#E3E6E9] text-black')
                        }
                    >
                        {ragScoreText}
                    </span>
                    {ragInfo.ragHeader && (
                        <span className="ml-[3px] min-w-0 flex-grow truncate leading-none text-gray-tertiary">
                            {ragInfo.ragHeader}
                        </span>
                    )}
                </div>
                {ragInfo.url && (
                    <div>
                        <span className="">{ragInfo.urlPrefix}链接:</span>
                        <a className="text-[#6E4BFA]" href={ragInfo.url} target="_blank" rel="noreferrer">
                            {ragInfo.url}
                        </a>
                    </div>
                )}
                <div>{ragInfo.text}</div>
            </StyledScrollContainer>
        );
    }, [debugInfo]);

    /** 工作流工具调试信息组件 */
    const workflowToolInfo = useMemo(() => {
        if (
            debugInfo.type === DebugInfoType.WorkflowToolRequest ||
            debugInfo.type === DebugInfoType.WorkflowToolResponse
        ) {
            return (
                <StyledScrollContainer className="mt-6 h-[425px]">
                    <JsonViewer
                        value={
                            debugInfo.type === DebugInfoType.WorkflowToolRequest
                                ? workflowToolInput
                                : workflowToolOutput
                        }
                    />
                </StyledScrollContainer>
            );
        }
        return <></>;
    }, [debugInfo.type, workflowToolInput, workflowToolOutput]);

    const debugInfoContent = useMemo(() => {
        switch (debugInfo?.type) {
            case DebugInfoType.FunctionRequest:
            case DebugInfoType.FunctionResponse:
                return functionInfo;
            case DebugInfoType.Rag:
                return ragInfo;
            case DebugInfoType.WorkflowToolRequest:
            case DebugInfoType.WorkflowToolResponse:
                return workflowToolInfo;
            default:
                return <></>;
        }
    }, [debugInfo?.type, functionInfo, ragInfo, workflowToolInfo]);

    // 添加 useEffect 处理状态更新
    useEffect(() => {
        if (
            debugInfo.type !== DebugInfoType.WorkflowToolRequest &&
            debugInfo.type !== DebugInfoType.WorkflowToolResponse
        ) {
            return;
        }

        const workflowToolDebugInfo = debugInfo.content as WorkflowToolDebugInfo;

        // 解析输入
        try {
            const inputJson = JSON.parse(workflowToolDebugInfo.start.workflow.input);
            setWorkflowToolInput(inputJson);
        } catch (error) {
            // 如果解析失败，则设置为原始输入
            const defaultValue = {
                input: workflowToolDebugInfo.start.workflow.input,
            };
            setWorkflowToolInput(defaultValue);
            console.error('Parse workflow tool info fail', error);
        }

        // 解析输出
        try {
            const outputJson = JSON.parse(workflowToolDebugInfo.end.workflow.output || '{}');
            setWorkflowToolOutput(outputJson);
        } catch (error) {
            // 如果解析失败，则设置为原始输出
            const defaultValue = {
                output: workflowToolDebugInfo.end.workflow.output,
            };
            setWorkflowToolOutput(defaultValue);
            console.error('Parse workflow tool info fail', error);
        }
    }, [debugInfo.content, debugInfo.type]);

    useEffect(() => {
        switch (debugInfo?.type) {
            case DebugInfoType.FunctionRequest:
                showLog(EVENT_VALUE_CONST.PLUGIN_INVOKE_BOX, EVENT_PAGE_CONST.CODELESS_CREATE, {
                    ...ext,
                    [EVENT_EXT_KEY_CONST.PLUGIN_BOX_TYPE]: LogBoxType.input,
                    [EVENT_EXT_KEY_CONST.PLUGIN_NAME]: (debugInfo?.content as FunctionDebugInfo).start.function.name,
                });
                break;
            case DebugInfoType.FunctionResponse:
                showLog(EVENT_VALUE_CONST.PLUGIN_INVOKE_BOX, EVENT_PAGE_CONST.CODELESS_CREATE, {
                    ...ext,
                    [EVENT_EXT_KEY_CONST.PLUGIN_BOX_TYPE]:
                        functionTabActiveKey === FunctionTabKey.Response ? LogBoxType.output : LogBoxType.originOutput,
                    [EVENT_EXT_KEY_CONST.PLUGIN_NAME]: (debugInfo?.content as FunctionDebugInfo).end.function.name,
                });
                break;
            case DebugInfoType.Rag:
                showLog(EVENT_VALUE_CONST.REPOSITORY_INVOKE_BOX, EVENT_PAGE_CONST.CODELESS_CREATE, ext);
                break;
        }
    }, [debugInfo, ext, functionTabActiveKey, showLog]);

    return (
        <StyledModal
            open={modalOpen}
            title={
                <Row align={'middle'}>
                    <span className="text-[20px]">查看调用信息</span>
                </Row>
            }
            footer={footer}
            width={865}
            onCancel={closeModal}
            maskClosable={false}
            centered
        >
            {debugInfoContent}
        </StyledModal>
    );
}
