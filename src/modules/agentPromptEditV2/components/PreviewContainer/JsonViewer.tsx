/**
 * @file Json 渲染组件, 文档：https://github.com/uiwjs/react-json-view
 * <AUTHOR>
 */
import '@baidu/lingjing-fe-agent-adapter/dist/js/agent.js';
import '@baidu/lingjing-fe-agent-adapter/dist/css/agent.css';
import styled from '@emotion/styled';
import JsonView from '@uiw/react-json-view';

const StyledJsonViewer = styled(JsonView)`
    .w-rjv-wrap {
        line-height: 26px;
        font-size: 14px;
        font-family: Menlo;
        font-weight: 400;
    }
`;

const customJsonTheme = {
    '--w-rjv-key-number': '#6E4BFA',
    '--w-rjv-key-string': '#6E4BFA',
    '--w-rjv-quotes-color': '#6E4BFA',
    '--w-rjv-value': '#1E1F24',
    '--w-rjv-type-boolean-color': '#1E1F24',
    '--w-rjv-type-int-color': '#1E1F24',
    '--w-rjv-type-string-color': '#1E1F24',
    '--w-rjv-border-left-width': '1px',
    '--w-rjv-curlybraces-color': '#848691',
    '--w-rjv-brackets-color': '#848691',
    '--w-rjv-quotes-string-color': '#1E1F24',
    '--w-rjv-ellipsis-color': '#848691',
    '--w-rjv-line-color': '#DBDCE0',
    '--w-rjv-colon-color': '#6E4BFA',
};

export default function JsonViewer({value}: {value: any}) {
    return (
        <StyledJsonViewer
            value={value}
            displayObjectSize={false}
            displayDataTypes={false}
            enableClipboard={false}
            indentWidth={27}
            shortenTextAfterLength={0}
            style={customJsonTheme as any}
        >
            <JsonView.Arrow>
                <div className="mb-[3px] mr-[10px] flex h-full w-full items-center justify-center">
                    <span className="iconfont icon-expand1 text-black-base  text-[10px]"></span>
                </div>
            </JsonView.Arrow>
            <JsonView.Ellipsis
                // eslint-disable-next-line react/jsx-no-bind
                render={({'data-expanded': isExpanded, className}, {value}) => {
                    if (isExpanded) {
                        if (Array.isArray(value)) {
                            return <span className={`${className} text-[#848691]`}>{value.length}</span>;
                        } else if (typeof value === 'object') {
                            return <span className={`${className} text-[#848691]`}>{Object.keys(value).length}</span>;
                        }
                    }
                    return <span />;
                }}
            />
        </StyledJsonViewer>
    );
}
