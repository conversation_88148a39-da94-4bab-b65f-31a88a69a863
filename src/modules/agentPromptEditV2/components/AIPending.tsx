/**
 * @file AI 生成等待中
 * <AUTHOR>
 * @date 2024/08/21
 */

import styled from '@emotion/styled';
import loadingAnimation from '@/assets/loading-animation.gif';

const AIPendingContainer = styled.div`
    position: relative;
    z-index: 50;
    display: flex;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    background-color: #f5f6f9b2;
    .loading-img {
        margin-left: auto;
        margin-right: auto;
        width: 3rem;
    }
    .loading-text {
        margin-top: 0.75rem;
    }
`;

export default function AIPending(props: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <AIPendingContainer {...props}>
            <div>
                <img src={loadingAnimation} className="loading-img" alt="加载中"></img>
                <div className="loading-text">正在生成中...</div>
            </div>
        </AIPendingContainer>
    );
}
