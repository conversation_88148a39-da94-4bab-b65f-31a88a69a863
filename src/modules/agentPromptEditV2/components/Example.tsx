import styled from '@emotion/styled';

const ExampleContainer = styled.div`
    font-family: PingFang SC;
    padding: 0 12px;
    li,
    ul,
    p {
        margin-top: 6px;
        margin-bottom: 6px;
    }
    h2 {
        font-weight: 500;
        margin-top: 12px;
        &:first-of-type {
            margin-top: 0px;
        }
    }
    h1,
    h2 {
        color: #000311;
    }
    p,
    li {
        color: #585858;
        line-height: 20px;
    }
`;

export default function Example({header, ...props}: {header?: React.ReactNode} & React.HTMLAttributes<HTMLDivElement>) {
    return (
        <ExampleContainer {...props}>
            {header}
            <h2>#角色规范</h2>
            <p>
                你是一个健身教练，你的任务是针对用户的问题，结合训练科学、生理学和营养学等专业知识，给用户锻炼指导和营养信息。
            </p>
            <h2>#思考规范</h2>
            <ul>
                <li>
                    1.当用户需要你输出饮食建议时，比如“帮我推荐减脂餐”，你应该优先调用knowledge_retrieval工具，并根据返回信息详细回答用户问题。
                </li>
                <li>
                    2.当用户需要你输出健身计划时，你应该先调用knowledge_retrieval工具然后再调用eChartsToolCall工具，比如“帮我制定一周健身计划”。
                </li>
                <li>
                    3.当knowledge_retrieval工具返回结果不能满足用户需求时，你可以根据用户意图修改参数再次调用该工具。
                </li>
                <li>4.当用户的需求不明确时，你应该主动优先明确用户需求。</li>
                <li>
                    5.对于超出健身教练服务范围的需求如电影推荐等，你需要按如下话术委婉拒答“我只是一个健身教练，不能回答这个问题噢”，并引导用户提出关于健身相关的问题。
                </li>
            </ul>
            <h2>#回复规范</h2>
            <ul>
                <li>1.你需要以专业、可靠的风格回复用户。</li>
                <li>2.在每次结束对话时你可以向用户进行提问并引导相关话题深入进行。</li>
                <li className="mb-3">3.请注意你的回复内容长度不要超过200字。</li>
            </ul>
        </ExampleContainer>
    );
}
