/**
 * @file 知识库选择器 context
 * <AUTHOR>
 */
import {createContext, useContext} from 'react';
import {AllDatasetInfo} from '@/api/dataSet/interface';

export interface DatasetSelectContextData {
    // 初始被选择的值，用于在选项列表中显示非处理成功的知识库
    initValue: string[];
    // 通过 id 做缓存，方便查询，每个使用 DatasetSelect 组件的场景，最终提交的数据都不一样，因此在父组件中通过 onChange 的 values 和 optionsMap 自定义组装提交数据
    optionsMap: Record<string, AllDatasetInfo>;
    setOptionsMap?: (optionsMap: Record<string, AllDatasetInfo>) => void;
    // 用于设置表单项的 props，目前用于设置 Form.Item 的 help 和 validateStatus
    setFormItemProps?: (props: Record<string, any>) => void;
}

export const DatasetSelectContext = createContext<DatasetSelectContextData>({
    initValue: [],
    optionsMap: {},
    options: [],
} as DatasetSelectContextData);

export const useDatasetSelectContext = () => useContext(DatasetSelectContext);
