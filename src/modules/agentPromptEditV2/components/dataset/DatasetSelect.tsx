/**
 * @file src/modules/agentPromptEdit/DatasetSelect.tsx
 * <AUTHOR>
 */
import {Checkbox, Input, Select, Spin, Tag} from 'antd';
import {LabeledValue, SelectValue} from 'antd/es/select';
import React, {Fragment, MouseEvent, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {css} from '@emotion/css';
import {CaretDownOutlined, CaretRightOutlined} from '@ant-design/icons';
import api from '@/api/dataSet';
import {AllDatasetInfo, FileSimpleInfo, DataSetStatus} from '@/api/dataSet/interface';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import urls from '@/links';
import themeConfig from '@/styles/lingjing-light-theme';
import {getPopupContainer} from '@/utils/getPopupContainer';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import DatasetAddFile from './DatasetAddFile';
import {useDatasetSelectContext} from './DatasetSelectContext';

export const SelectStyle = css`
    margin-left: 32px;
    &.ant-select-multiple {
        .ant-select-selection-overflow {
            row-gap: 4px;
            margin: 2px 0;
        }

        .ant-select-selection-item {
            background-color: #e8edff;
            color: ${themeConfig.token.colorPrimary};
            font-weight: 500;
            .ant-select-selection-item-remove {
                color: ${themeConfig.token.colorPrimary};
            }
        }
    }
`;

export const SelectDropdownStyle = css`
    &.ant-select-dropdown {
        border-radius: 6px;
        padding: 0;

        .ant-select-item {
            color: rgba(0, 0, 0, 0.85);
            border-radius: 0;
            padding-left: 0.08rem;

            .ant-select-item-option-content {
                font-weight: 400;
            }
        }
        .ant-select-item {
            .ant-select-item-option-content {
                .ant-checkbox-wrapper {
                    margin-right: 12px;
                    margin-left: 6.5px;
                    .ant-checkbox-inner {
                        border-radius: 3px;
                    }
                }
            }
        }
        .ant-select-item-option-selected {
            .ant-select-item-option-state {
                display: none;
            }
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
            background-color: #fff;
        }
        .ant-select-item-option-disabled {
            color: rgba(0, 0, 0, 0.85);
        }
        .ant-select-item:hover {
            background-color: #e8edff;
        }

        .filenames-container {
            color: ${themeConfig.token?.colorTextBase};
            background: ${themeConfig.token?.colorBgCard};

            .filenames {
                color: ${themeConfig.token?.colorTextTertiary};
            }
        }
    }
`;

export const SelectTagStyle = css`
    &.ant-tag {
        display: flex;
        border: none;
        font-size: 14px;
        margin-right: 4px;
        padding: 2px 6px;

        &.ant-tag-processing {
            background: rgba(244, 163, 0, 0.1);
            color: #ffaa00;

            .ant-tag-close-icon {
                color: #ffaa00;
            }
        }
        &.ant-tag-success {
            background: rgba(var(--main-color-base), 0.1);
            color: ${themeConfig.token.colorPrimary};

            .ant-tag-close-icon {
                color: ${themeConfig.token.colorPrimary};
            }
        }
        &.ant-tag-has-color {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;

            .ant-tag-close-icon {
                color: #ff4d4f;
            }
        }
        .iconfont {
            font-size: 14px;
        }
    }
`;

// Select 组件选中的标签颜色和知识库状态码的映射
const TAG_STYLE_MAP = {
    [DataSetStatus.waiting]: {
        color: 'fail',
        icon: <i className="iconfont icon-exclamationcircle mr-1" />,
    },
    [DataSetStatus.doing]: {
        color: 'processing',
        icon: <i className="iconfont icon-clock-circle mr-1" />,
    },
    [DataSetStatus.done]: {
        color: 'success',
        icon: null,
    },
    [DataSetStatus.failed]: {
        color: 'fail',
        icon: <i className="iconfont icon-exclamationcircle mr-1" />,
    },
    [DataSetStatus.auditing]: {
        color: 'processing',
        icon: <i className="iconfont icon-clock-circle mr-1" />,
    },
    [DataSetStatus.shielded]: {
        color: 'fail',
        icon: <i className="iconfont icon-offline mr-1" />,
    },
};

/**
 * 获取表单项属性: help 和 validateStatus
 *
 * @param processingCount 正在处理的知识库数量
 * @param invalidCount 无效的知识库数量
 */
function getFormItemProps(counter: Record<DataSetStatus, number>) {
    if (counter[DataSetStatus.failed])
        return {
            help: (
                <div>
                    {counter[DataSetStatus.failed]}个知识库处理异常，请
                    <a href={urls.datasetList.raw()} target="_blank" rel="noreferrer">
                        前往知识库
                    </a>
                    查看失败原因
                </div>
            ),
            validateStatus: 'validating',
        };
    if (counter[DataSetStatus.waiting])
        return {
            help: (
                <div>
                    {counter[DataSetStatus.waiting]}个知识库待处理，请
                    <a href={urls.datasetList.raw()} target="_blank" rel="noreferrer">
                        前往知识库
                    </a>
                    提交处理
                </div>
            ),
            validateStatus: 'validating',
        };

    if (counter[DataSetStatus.shielded])
        return {
            help: (
                <div>
                    {counter[DataSetStatus.shielded]}个知识库被屏蔽引用失败，请
                    <a href={urls.datasetList.raw()} target="_blank" rel="noreferrer">
                        前往知识库
                    </a>
                    查看屏蔽原因
                </div>
            ),
            validateStatus: 'validating',
        };
    if (counter[DataSetStatus.auditing])
        return {
            help: `${counter[DataSetStatus.auditing]}个知识库正在审核中，请稍等`,
            validateStatus: 'validating',
        };
    if (counter[DataSetStatus.doing])
        return {
            help: `${counter[DataSetStatus.doing]}个知识库正在处理中，请稍等`,
            validateStatus: 'validating',
        };

    return {};
}

function DatasetSelectOptionContent({
    option,
    fieldNames,
    selectIds,
}: {
    option: AllDatasetInfo & {disabled: boolean};
    fieldNames: {value: 'datasetTable' | 'datasetId'};
    selectIds: SelectValue[];
}) {
    const {datasetId, datasetName, disabled} = option;
    const [unfold, setUnfold] = useState(false);
    const [loading, setLoading] = useState(false);
    const [optionDetail, setOptionDetail] = useState({
        unfold: false,
        total: 0,
        fileList: [] as FileSimpleInfo[],
        loading: false,
        fileNames: '',
    });

    const toggleOptionFold = useCallback(
        (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();
            setUnfold(!unfold);

            // 文件列表为空时获取文件列表
            if (!optionDetail.fileList?.length && !loading) {
                const tmpOptionDetail = {
                    ...optionDetail,
                };
                setLoading(true);
                api.getAllFiles({datasetId})
                    .then(res => {
                        tmpOptionDetail.total = res.total;
                        tmpOptionDetail.fileList = res.fileList;
                        tmpOptionDetail.fileNames =
                            res.fileList
                                ?.map(file => {
                                    if (!file?.fileName) {
                                        return '';
                                    }

                                    const fileName = file.fileName;
                                    const dotIndex = fileName.lastIndexOf('.');
                                    // 文件扩展名长度（包含 '.' 的长度，dotIndex 小于 0 时，fileExtLength 也为 0）
                                    const fileExtLength = dotIndex < 0 ? 0 : fileName.length - dotIndex;
                                    // 判断文件名(包含扩展名)总长度大与 30 + 扩展名长度(包含 '.' 的长度)
                                    if (fileName.length > 30 + fileExtLength) {
                                        // 文件名过长，截取前 17 个字符，后 10 个字符，中间用 '...' 分隔
                                        return `${fileName.slice(0, 17)}...${fileName.slice(
                                            fileName.length - 10 - fileExtLength
                                        )}`;
                                    }

                                    return file.fileName;
                                })
                                .join('、') || '';
                    })
                    .finally(() => {
                        setLoading(false);
                        setOptionDetail(tmpOptionDetail);
                    });
            }
        },
        [optionDetail, setOptionDetail, datasetId, loading, unfold]
    );

    return (
        <Fragment>
            <div className="flex items-center">
                {unfold ? (
                    <CaretDownOutlined className="ml-[8px]" onClick={toggleOptionFold} />
                ) : (
                    <CaretRightOutlined className="ml-[8px]" onClick={toggleOptionFold} />
                )}
                <Checkbox checked={selectIds.includes(option[fieldNames.value])} disabled={disabled} />
                <div className="truncate">{datasetName}</div>
            </div>
            {unfold &&
                (loading ? (
                    <div className="flex justify-center">
                        <Spin size="small" />
                    </div>
                ) : (
                    <div className="mt-[8px] pl-[47px] pr-[9px]">
                        <div className="filenames-container whitespace-normal break-words rounded-[6px] px-[12px] py-[8px]">
                            共{optionDetail?.total || 0}个文件：
                            <span className="filenames">{optionDetail?.fileNames}</span>
                        </div>
                    </div>
                ))}
        </Fragment>
    );
}

enum DatasetFetchStatus {
    fetching,
    success,
    fail,
}

export default function DatasetSelect({
    selectIds,
    onChange,
    maxCount,
    fieldNames = {value: 'datasetTable'},
}: {
    selectIds: SelectValue[];
    onChange?: (args: SelectValue[]) => void;
    maxCount: number;
    fieldNames?: {value: 'datasetTable' | 'datasetId'};
}) {
    const [open, setOpen] = useState(false);
    const datasetSelectContextData = useDatasetSelectContext();
    const {initValue, setFormItemProps, setOptionsMap, optionsMap} = datasetSelectContextData;
    const {ubcClickLog, ubcShowLog} = useUbcLog();
    const {showLog, clickLog} = useUbcLogV3();
    const [rawOptions, setRawOptions] = useState<AllDatasetInfo[]>(Object.values(optionsMap || {})); // 知识库选项
    const [datasetFetchStatus, setDatasetFetchStatus] = useState<DatasetFetchStatus>(
        rawOptions.length ? DatasetFetchStatus.success : DatasetFetchStatus.fetching
    ); // 是否正在获取知识库列表
    // 搜索框输入值
    const [searchValue, setSearchValue] = useState('');
    // 知识库轮询定时器 id
    const datasetPollingIntervalId = useRef(0);
    // 通过弹窗修改的知识库 id 数组
    const [changedDatasetIds, setChangedDatasetIds] = useState<string[]>([]);
    // 是否首次请求完成知识库列表
    const isFirstRequestDataset = useRef(false);

    const onInputValueChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setSearchValue(e.target.value);
        },
        [setSearchValue]
    );

    // 用 id 做缓存，方便快速查询
    const viewDatasetOptionsMap = useMemo(() => {
        const viewDatasetOptionsMap: Record<string, AllDatasetInfo> = {};
        rawOptions.forEach(dataset => {
            viewDatasetOptionsMap[dataset[fieldNames.value]] = dataset;
        });
        // 更新知识库选项，用于在父组件中根据 onChange 自定义被选择的数据
        setOptionsMap && setOptionsMap(viewDatasetOptionsMap);
        // 更新 promptEditStore 中的全局知识库数据，用于在 canPublish 中做校验

        return viewDatasetOptionsMap;
    }, [rawOptions, fieldNames.value, setOptionsMap]);

    const viewDatasetOptions = useMemo(() => {
        // 筛选处理成果或已被选择的知识库列表
        const options = rawOptions.filter(
            dataset =>
                dataset.status === DataSetStatus.done ||
                initValue?.includes(dataset[fieldNames.value]) ||
                changedDatasetIds?.includes(dataset[fieldNames.value])
        );

        // 未超出最大选择数量时，非处理成果状态且未被选择的知识库不可点
        if (selectIds?.length < maxCount) {
            return options.map(dataset => ({
                ...dataset,
                disabled: dataset.status !== DataSetStatus.done && !selectIds.includes(dataset[fieldNames.value]),
            }));
        }

        // 已超出最大选择数量时，未被选择的知识库不可点
        return options.map(dataset => ({
            ...dataset,
            disabled: !selectIds?.includes(dataset[fieldNames.value]),
        }));
    }, [selectIds, rawOptions, initValue, changedDatasetIds, maxCount, fieldNames.value]);

    const datasetSelectChange = useCallback(
        (values: SelectValue[]) => {
            /**
             * 首次通过弹窗修改/新建后，本次修改/新建后被添加进 changedDatasetIds 数组的知识库为 "处理中" 状态
             * changedDatasetIds 数组中 "处理中" 状态的知识库只有首次通过弹窗修改后，自动选择时才在下拉列表中显示
             * 用户操作后选项值发生变更，将被取消选中的知识库 id 从 changedDatasetIds 数组中移除，此时还在 "处理中" 状态的知识库在下拉列表中不可见
             */
            const selectedChangedDatasetIds = changedDatasetIds.filter(id => values.includes(id));
            setChangedDatasetIds(selectedChangedDatasetIds);
            onChange && onChange(values);
        },
        [onChange, changedDatasetIds]
    );

    const getAgentDataset = useCallback(async () => {
        try {
            const res = await api.getAllStatusDataset();
            setRawOptions(res);
            setDatasetFetchStatus(DatasetFetchStatus.success);
        } catch {
            setDatasetFetchStatus(DatasetFetchStatus.fail);
        }
    }, [setDatasetFetchStatus, setRawOptions]);

    useEffect(() => {
        const counter: Record<DataSetStatus, number> = {
            [DataSetStatus.waiting]: 0,
            [DataSetStatus.doing]: 0,
            [DataSetStatus.done]: 0,
            [DataSetStatus.failed]: 0,
            [DataSetStatus.auditing]: 0,
            [DataSetStatus.shielded]: 0,
        };

        if (!selectIds) {
            return;
        }

        selectIds.forEach(datasetId => {
            const dataset = viewDatasetOptionsMap[datasetId as string];
            dataset && counter[dataset.status]++;
        });

        // 只有正在处理的知识库时轮询
        if (counter[DataSetStatus.doing] + counter[DataSetStatus.auditing]) {
            // 被选择的知识库中存在正在处理或异常的知识库且当前没有启动定时器，则启动获取知识库循环
            if (!datasetPollingIntervalId.current) {
                datasetPollingIntervalId.current = setInterval(getAgentDataset, 5000) as unknown as number;
            }
        } else {
            // 停止获取知识库循环
            datasetPollingIntervalId.current && clearInterval(datasetPollingIntervalId.current);
            datasetPollingIntervalId.current = 0;
        }

        if (selectIds.length > maxCount) {
            setFormItemProps && setFormItemProps({help: `最多选择${maxCount}个`, validateStatus: 'error'});
        } else {
            setFormItemProps && setFormItemProps(getFormItemProps(counter));
        }

        return () => {
            if (datasetPollingIntervalId.current) {
                clearInterval(datasetPollingIntervalId.current);
                datasetPollingIntervalId.current = 0;
            }
        };
    }, [
        selectIds,
        maxCount,
        viewDatasetOptionsMap,
        datasetPollingIntervalId,
        setFormItemProps,
        getAgentDataset,
        ubcClickLog,
    ]);

    // 刷新知识库列表
    const refreshAgentDataset = useCallback(async () => {
        if (datasetFetchStatus === DatasetFetchStatus.fetching) {
            return;
        }

        // 重置知识库选项面板
        setDatasetFetchStatus(DatasetFetchStatus.fetching);
        getAgentDataset();
    }, [datasetFetchStatus, getAgentDataset]);

    useEffect(() => {
        if (isFirstRequestDataset.current) {
            return;
        }

        // 首次获取知识库列表
        if (!rawOptions.length) {
            getAgentDataset();
        }

        isFirstRequestDataset.current = true;
    }, [getAgentDataset, rawOptions]);

    const openAddFile = useCallback(() => {
        if (selectIds.length < maxCount) {
            setOpen(true);
            ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateDatasetCreate);
            clickLog(EVENT_VALUE_CONST.REPOSITORY_SELECT_NEW);
        }
    }, [selectIds?.length, maxCount, ubcClickLog, clickLog]);

    const selectDropdownRender = useCallback(
        (menu: React.ReactNode) => (
            <div className="nowheel">
                <div className="m-[12px] mb-[8px]">
                    <Input className="rounded-[6px]" placeholder="请搜索知识库名称" onChange={onInputValueChange} />
                </div>
                {menu}
                {datasetFetchStatus === DatasetFetchStatus.success && (
                    <div className="mb-[10px] flex justify-between px-4 pt-[10px]">
                        <a
                            className={
                                selectIds?.length < maxCount ? 'cursor-pointer' : 'cursor-not-allowed opacity-40'
                            }
                            onClick={openAddFile}
                        >
                            <i className="iconfont icon-plusCircle mr-1 text-sm" />
                            新建知识库
                        </a>
                        <span
                            className="cursor-pointer text-primary"
                            onClick={() => {
                                ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateDatasetRefresh);
                                refreshAgentDataset();
                            }}
                        >
                            <i className="iconfont icon-update text-sm" /> 刷新
                        </span>
                    </div>
                )}
            </div>
        ),
        [refreshAgentDataset, ubcClickLog, onInputValueChange, datasetFetchStatus, selectIds, maxCount, openAddFile]
    );

    const handleDropDownVisibleChange = useCallback(
        (e: boolean) => {
            if (e) {
                ubcShowLog(EVENT_TRACKING_CONST.ZeroCreateDataset);
                ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateDataset);
                showLog(EVENT_VALUE_CONST.NODE_REPOSITORY_SELECT);
            }
        },
        [ubcClickLog, ubcShowLog, showLog]
    );

    const handleSelectItem = useCallback(
        (_: any, option: {value: string}) => {
            ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateDatasetItem, {
                zeroCreateDatasetItemId: option.value,
            });
        },
        [ubcClickLog]
    );

    // 添加文件后自动选择该知识库
    const onAddFile = useCallback(
        (dataset: AllDatasetInfo) => {
            // 更新知识库列表
            getAgentDataset();
            const addedDatasetId = dataset[fieldNames.value];
            // 新建知识库的场景 getAgentDataset 请求结束前 rawOptions 和 viewDatasetOptionsMap 中缺少该知识库数据，此时需要添加到 rawOptions
            if (!viewDatasetOptionsMap[addedDatasetId]) {
                setRawOptions([...rawOptions, dataset]);
            }

            !changedDatasetIds.includes(addedDatasetId) && setChangedDatasetIds([...changedDatasetIds, addedDatasetId]);
            const newSelectIds = [...selectIds];
            // 该知识库未被选择，则自动选择
            !selectIds.includes(addedDatasetId) && newSelectIds.push(addedDatasetId);
            onChange && onChange(newSelectIds);
        },
        [fieldNames.value, selectIds, onChange, changedDatasetIds, rawOptions, viewDatasetOptionsMap, getAgentDataset]
    );

    const tagRender = useCallback(
        (props: {value: SelectValue; closable: boolean; onClose: (e: any) => void}) => {
            const {value, closable, onClose} = props;
            const optionDetail = viewDatasetOptionsMap[value as string];
            const showLabel = optionDetail?.datasetName
                ? `${optionDetail.datasetName}${
                      optionDetail.status === DataSetStatus.doing ? ` (${Math.floor(+optionDetail.process)}%)` : ''
                  }`
                : (value as string);

            return (
                <Tag
                    icon={TAG_STYLE_MAP[optionDetail?.status || DataSetStatus.done]?.icon}
                    color={TAG_STYLE_MAP[optionDetail?.status || DataSetStatus.done]?.color}
                    className={SelectTagStyle}
                    closable={closable}
                    onClose={onClose}
                >
                    {showLabel}
                </Tag>
            );
        },
        [viewDatasetOptionsMap]
    );

    const filterOption = useCallback(
        (input: string, option?: any) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
        []
    );

    return (
        <Fragment>
            <Select
                className={SelectStyle}
                listHeight={180}
                popupClassName={SelectDropdownStyle}
                mode="multiple"
                defaultActiveFirstOption={false}
                optionFilterProp="datasetName"
                placeholder={`请选择知识库，最多可选${maxCount}个`}
                searchValue={searchValue}
                showSearch={false}
                value={selectIds as LabeledValue[]}
                notFoundContent={
                    <div className="relative min-h-[4rem]">
                        <div className="absolute left-1/2 top-1/2  -translate-x-1/2 -translate-y-1/2">
                            {datasetFetchStatus === DatasetFetchStatus.fetching ? (
                                <Spin size="small" />
                            ) : datasetFetchStatus === DatasetFetchStatus.success ? (
                                '暂无结果'
                            ) : (
                                <div>
                                    加载失败，请点击
                                    <span
                                        className="cursor-pointer text-primary"
                                        onClick={() => {
                                            ubcClickLog(EVENT_TRACKING_CONST.ZeroCreateDatasetRefresh);
                                            refreshAgentDataset();
                                        }}
                                    >
                                        重试
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                }
                filterOption={filterOption}
                onSelect={handleSelectItem}
                dropdownRender={selectDropdownRender}
                onChange={datasetSelectChange}
                onDropdownVisibleChange={handleDropDownVisibleChange}
                tagRender={tagRender}
                getPopupContainer={getPopupContainer}
            >
                {datasetFetchStatus === DatasetFetchStatus.success
                    ? viewDatasetOptions.map((option: AllDatasetInfo & {disabled: boolean}) => (
                          <Select.Option
                              key={option[fieldNames.value]}
                              value={option[fieldNames.value]}
                              label={option.datasetName}
                              disabled={option.disabled}
                          >
                              <DatasetSelectOptionContent
                                  option={option}
                                  fieldNames={fieldNames}
                                  selectIds={selectIds}
                              />
                          </Select.Option>
                      ))
                    : null}
            </Select>
            <DatasetAddFile open={open} datasetList={rawOptions} setOpen={setOpen} onAddFile={onAddFile} />
        </Fragment>
    );
}
