/**
 * @file 创建/编辑知识库的弹窗
 * <AUTHOR>
 *
 */
import {ConfigProvider, Modal} from 'antd';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {css} from '@emotion/css';
import {useLocation} from 'react-router-dom';
import api from '@/api/dataSet';
import baijiahaoApi from '@/api/dataSet/baijiahao';
import {AddFileType, DatasetInfo, FieldType, ImportMethod, StepOneRef} from '@/modules/dataset/interface';
import {AllDatasetInfo} from '@/api/dataSet/interface';
import DataSetInfoFrom from '@/modules/dataset/edit/DatasetInfoForm';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {getCurrentFile} from '@/modules/dataset/utils';
import DatasetEditContextProvider from '@/modules/dataset/edit/context';
import useBaijiahaoAuthInit from '@/modules/dataset/hooks/useBaijiahaoAuthInit';
import useBaijiahaoProcessInit from '@/modules/dataset/hooks/useBaijiahaoProcessInit';
import useWangpanInit from '@/modules/dataset/hooks/useWangpanInit';
import {FORM_CONFIG} from '@/modules/dataset/constant';
import {useToast} from '@/modules/activity/masterRecruitment/hooks/useToast';

const ModalStyle = css`
    position: reative;
    &.ant-modal {
        .ant-modal-header {
            .ant-modal-title {
                font-size: 18px;
                line-height: 24px;
                font-weight: 500;
            }
        }

        .ant-modal-close {
            top: 25px;
            right: 21px;
        }

        .ant-form-item {
            margin-bottom: 18px;
        }
    }
`;

export default function DatasetAddFile({
    open,
    datasetList,
    setOpen,
    onAddFile,
    datasetId,
}: {
    open: boolean;
    datasetList: AllDatasetInfo[] | undefined;
    setOpen: (open: boolean) => void;
    onAddFile: (dataset: AllDatasetInfo) => void;
    datasetId?: string;
}) {
    const [submitLoading, setSubmitLoading] = useState(false);

    // 存在异步任务时需要再不同的上下文创建知识库和提交文件向量化，故需要对共用信息（创建的知识库 id）单独维护
    const currentEditDatasetInfoRef = useRef<DatasetInfo & {detail?: AllDatasetInfo}>();
    const location = useLocation();
    const {toastConfig} = location?.state || {};
    const {showToast} = useToast();

    const handleAddFile = useCallback(
        (dataset: AllDatasetInfo) => {
            onAddFile(dataset);
            setOpen(false);
            // 重置表单
            formRef.current?.reset();
            currentEditDatasetInfoRef.current = {};
        },
        [onAddFile, setOpen]
    );
    const handleCreateFailed = useCallback(() => {
        setSubmitLoading(false);
    }, []);

    // 知识库创建表单提交处理
    const handleCreate = useCallback(
        async (props: FieldType) => {
            const {name, description, datasetId} = props;
            const currentFiles = await getCurrentFile(props);
            try {
                // 提交知识库文件并使用默认配置向量化
                const dataset = await api.generateAgentEmbedding({
                    datasetId: currentEditDatasetInfoRef.current?.datasetId || datasetId,
                    datasetName: name,
                    datasetDescription: description,
                    fileList: currentFiles,
                });
                if (props.importMethod === ImportMethod.baijiahao) {
                    await baijiahaoApi.changeBaijiahaoAutoImportConfig({
                        auto: !!props.autoImportBjh,
                        datasetId: dataset.datasetId,
                    });
                }

                handleAddFile(dataset);
                setSubmitLoading(false);
            } catch (error) {
                console.error(error);
                setSubmitLoading(false);
            }
        },
        [handleAddFile]
    );

    const formRef = useRef<StepOneRef>(null);

    const {showLog, clickLog} = useUbcLogV3();
    const handleCancel = useCallback(() => {
        clickLog('add_file_cancel');
        setOpen(false);
    }, [clickLog, setOpen]);

    const handleOK = useCallback(() => {
        clickLog('add_file_confirm');
        setSubmitLoading(true);
        try {
            formRef.current?.create();
        } catch (error) {
            setSubmitLoading(false);
        }
    }, [clickLog]);

    const [validReason, setValidReason] = useState<any>([]);

    useEffect(() => {
        if (open) {
            // 埋点 J-1 添加文件页面展示
            showLog('add_file_box');
            if (datasetId && formRef.current) {
                formRef.current.form.setFieldValue(FORM_CONFIG.dataset.name, datasetId);
                formRef.current.form.setFieldValue(FORM_CONFIG.addFileType.name, AddFileType.OldDataset);
            }
        }
    }, [datasetId, open, showLog]);

    const contextValue = useMemo(
        () => ({
            handleCreateDataset: async (props: {name: string; description?: string}) => {
                const {name, description} = props;
                const data = await api.createDataSet({
                    datasetName: name,
                    datasetDescription: description,
                    fileList: [],
                });
                const currentEditDatasetInfo = {
                    datasetId: data.datasetId,
                    datasetFiles: [],
                    detail: data,
                };
                currentEditDatasetInfoRef.current = currentEditDatasetInfo;
                onAddFile(data);
                return data.datasetId;
            },
            handleCreateFailed,
            handleAsyncTask: (datasetId: string) => {
                let detail: AllDatasetInfo | null = null;
                if (currentEditDatasetInfoRef.current?.detail) {
                    detail = currentEditDatasetInfoRef.current?.detail;
                } else {
                    detail = datasetList?.find(item => item.datasetId === datasetId) || null;
                }

                if (!detail) {
                    return;
                }

                handleAddFile(detail);
                setSubmitLoading(false);
            },
        }),
        [datasetList, handleAddFile, handleCreateFailed, onAddFile]
    );

    useBaijiahaoAuthInit();
    useBaijiahaoProcessInit(open);
    useWangpanInit();

    const ModalStyles = useMemo(() => {
        return {
            body: {
                padding: '0 24px',
            },
            header: {
                margin: '0 24px 12px 24px',
            },
            footer: {
                margin: '18px 24px 0 24px',
            },
            content: {
                padding: '24px 0',
            },
        };
    }, []);

    // 弹窗打开后判断是否需要弹出 toast
    const afterOpenChange = useCallback(
        (open: boolean) => {
            // 检查是否有待显示的 toast
            if (toastConfig && open) {
                const toastData = toastConfig;
                showToast({
                    msg: toastData.msg,
                    type: toastData.type,
                });
            }
        },
        [toastConfig, showToast]
    );

    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        controlHeight: 30,
                    },
                },
            }}
        >
            <Modal
                centered
                className={ModalStyle}
                open={open}
                title="创建知识库"
                width={865}
                onCancel={handleCancel}
                onOk={handleOK}
                okButtonProps={{disabled: validReason.length > 0 || submitLoading}}
                okText="确定"
                cancelText="取消"
                transitionName=""
                styles={ModalStyles}
                afterOpenChange={afterOpenChange}
            >
                <DatasetEditContextProvider value={contextValue}>
                    <DataSetInfoFrom
                        ref={formRef}
                        showAddTypeSelector
                        onCreate={handleCreate}
                        loading={submitLoading}
                        onFormValidateChange={setValidReason}
                        simple
                    />
                </DatasetEditContextProvider>
            </Modal>
        </ConfigProvider>
    );
}
