/**
 * @file 知识库模块通用函数
 * <AUTHOR>
 */
import {AllDatasetInfo, DataSetStatus} from '@/api/dataSet/interface';

/**
 * 根据 datasetTable 获取知识库的数据
 */
export const getDatasetInfo = (datasetTable: string, allStatusDatasets: AllDatasetInfo[]) =>
    allStatusDatasets?.find(dataset => dataset.datasetTable === datasetTable);

/**
 * 检查是否有无效知识库(非 '已可用' 状态的知识库)
 */
export function checkInvalidDatasetIds(datasetIds: string[], allStatusDatasets: AllDatasetInfo[] | null) {
    if (!datasetIds?.length) {
        return false;
    }

    // 数据还未加载完成，返回 false
    if (!allStatusDatasets) {
        return false;
    }

    // todo: 这里需要考虑：
    // 1. 如果选中的数据库被删除，引导用户删除已选中数据库
    // 2. 如果选中的数据库是自动挂载的问一问
    if (allStatusDatasets.length === 0) {
        return true;
    }

    // 遍历 datasetIds，如果有一个知识库状态不为 已可用，则返回 true
    const result = datasetIds.some((datasetId: string) => {
        const datasetInfo = getDatasetInfo(datasetId, allStatusDatasets);
        return datasetInfo?.status !== DataSetStatus.done;
    });

    return result;
}

/**
 * 用于知识库列表校验
 *
 */
export function validateDatasetIds(value: string[], allStatusDatasets: AllDatasetInfo[] | null) {
    // Error 不做展示，只标记表单项为 error 状态
    return checkInvalidDatasetIds(value, allStatusDatasets)
        ? Promise.reject(new Error('存在无效知识库'))
        : Promise.resolve();
}
