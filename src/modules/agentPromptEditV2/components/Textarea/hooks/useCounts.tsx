import {Editor} from '@tiptap/core';
import {useCallback, useEffect, useState} from 'react';
import {reverseJSON} from '../utils';
export const useCount = ({editor}: {editor: Editor | undefined}) => {
    const [count, setCount] = useState(0);

    // 获取编辑器内容字符长度
    const getCount = useCallback((editor?: Editor) => {
        if (editor) {
            const content = editor.getJSON();
            const text = reverseJSON(content);
            setCount(text.length);
            return text.length;
        }
        return 0;
    }, []);

    useEffect(() => {
        editor?.on('update', () => getCount(editor));
        return () => {
            editor?.off('update', () => getCount(editor));
        };
    }, [count, editor, getCount]);

    return {count, getCount};
};
