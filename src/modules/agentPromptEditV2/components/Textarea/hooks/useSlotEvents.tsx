import {useEffect} from 'react';
import {SLOT_EVENT} from '../utils';

const useSlotEvents = (
    spanRef: React.RefObject<HTMLSpanElement>,
    updateAttributes: (attrs: {content: string}) => void
) => {
    useEffect(() => {
        // 主动触发focus事件
        const handleFocus = (isRight = false) => {
            spanRef.current?.focus();
            if (isRight) {
                const range = document.createRange();
                range.selectNodeContents(spanRef.current as Node);
                range.collapse(false);
                window.getSelection()?.removeAllRanges();
                window.getSelection()?.addRange(range);
            }
        };

        // 处理slot的focus和backspace事件
        const handleSlotClick = (event: CustomEvent) => {
            const {direction} = event.detail;
            handleFocus(direction === 'left');
        };

        // 处理backspace事件
        const handleSlotBackspace = () => {
            const input = spanRef.current;
            if (input) {
                const currentValue = input.textContent || '';
                input.textContent = currentValue.slice(0, -1);
                // 触发 input 事件
                input.dispatchEvent(new Event('input', {bubbles: true}));
            }

            setTimeout(() => {
                handleFocus(true);
            }, 0);
        };

        const element = spanRef.current;
        element?.addEventListener(SLOT_EVENT.slotFocus, handleSlotClick as EventListener);
        element?.addEventListener(SLOT_EVENT.slotBackspace, handleSlotBackspace as EventListener);

        return () => {
            element?.removeEventListener(SLOT_EVENT.slotFocus, handleSlotClick as EventListener);
            element?.removeEventListener(SLOT_EVENT.slotBackspace, handleSlotBackspace as EventListener);
        };
    }, [spanRef, updateAttributes]);
};

export default useSlotEvents;
