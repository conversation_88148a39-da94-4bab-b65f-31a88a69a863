import styled from '@emotion/styled';

export const StyledTiptap = styled.div<{readonly?: boolean}>`
    .tiptap {
        height: 100%;
        color: ${props => (props.readonly ? '#848691' : '#000311')};
        cursor: ${props => (props.readonly ? 'not-allowed' : 'auto')};
        :focus-visible {
            outline: none !important;
        }

        *::selection {
            background-color: #cbd6ef !important;
        }

        p.is-editor-empty:first-child::before {
            color: #adb5bd;
            content: attr(data-placeholder);
            float: left;
            height: 0;
            pointer-events: none;
        }

        p {
            line-height: 23px;
            font-weight: 400;
            font-size: 14px;
            font-weight: 400;
        }

        &::-webkit-scrollbar {
            background: transparent;
            width: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #d9d9d9;
            border-radius: 5px;
        }
    }
`;
