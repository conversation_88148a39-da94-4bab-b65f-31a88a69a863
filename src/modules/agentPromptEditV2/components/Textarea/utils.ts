/**
 * <AUTHOR>
 * @file
 */
import {JSONContent} from '@tiptap/core';
import {Slice} from '@tiptap/pm/model';
export const SlotInputRegex = /\{#\s*placeholder="([^"]+)"\s*\}([\s\S]*?)\{\/#\}/g;

// SlotInput 扩展配置
export const EXTENSION_OPTIONS = {
    name: 'slotInput',
    tag: 'slot-input',
    className: 'slot-text-edit',
};

export const SLOT_EVENT = {
    slotFocus: 'slotFocus',
    slotBackspace: 'slotBackspace',
};

const breakTag = '@@SLOT_INPUT_LINE_BREAK@@';

// 将content{/#} 中的换行符替换为breakTag，防止SlotText中的换行符被解析为html标签
export function convertSlotInput(content: string, placeholder: string) {
    const protectedContent = content.replace(/"/g, '&quot;').replace(/\n/g, breakTag);
    return `<slot-input placeholder="${placeholder}" content="${protectedContent}">${protectedContent}</slot-input>`;
}

const escapeHTML = (text: string) => {
    return text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
};

// 查找 {# placeholder="xxx"}content{/#} 格式 并将匹配到的转换为<slot-input placeholder="xxx" content="content"></slot-input>
export const convertTiptap = (text?: string, readonly?: boolean, showHighlight?: boolean) => {
    if (!text) {
        return '';
    }

    // 交给编辑器内的经过转译
    const escapedContent = escapeHTML(text);

    // 先将slot-input转换并用特殊标记保护起来
    const htmlContent = escapedContent.replace(SlotInputRegex, (match, placeholder, content) => {
        if (readonly && !showHighlight) {
            return content;
        } else {
            return convertSlotInput(content, placeholder);
        }
    });

    // 处理普通文本的换行
    const withParagraphs = htmlContent
        .split('\n')
        .map(line => `<p>${line}</p>`)
        .join('');

    // 还原slot-input中的换行符
    return withParagraphs.replace(new RegExp(breakTag, 'g'), '\n');
};

// 将 JSON 转换为 正常文本格式
export const reverseJSON = (json: JSONContent, isWithoutComponents = true) => {
    let content = '';
    if (json.type === 'doc') {
        content += json?.content?.map(item => reverseJSON(item, isWithoutComponents)).join('\n');
    } else if (json.type === 'paragraph') {
        if (json.content) {
            json.content.forEach(item => {
                content += reverseJSON(item, isWithoutComponents);
            });
        }
    } else if (json.type === 'text') {
        content += json.text;
    } else if (json.type === 'slotInput') {
        if (isWithoutComponents) {
            content += `${json.attrs?.content}`;
        } else {
            content += `{# placeholder="${json.attrs?.placeholder}"}${json.attrs?.content}{/#}`;
        }
    }
    return content;
};

// 复制粘贴转化，将自定义组件中的文本添加到剪贴板中，由于数据格式不同于reverseJSON，所以需要单独处理
export const clipboardTextSerializer = (slice: Slice) => {
    let content = '';

    slice.content.content.forEach(node => {
        content += node.children
            .map(child => {
                if (child.type.name === 'text') {
                    return child.text;
                } else if (child.type.name === 'slotInput') {
                    return child.attrs.content;
                }
                return '';
            })
            .join('');
        content += '\n';
    });

    return content.replace(/\n$/, '');
};
