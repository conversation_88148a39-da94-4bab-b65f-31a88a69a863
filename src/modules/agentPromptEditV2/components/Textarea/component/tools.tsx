import {Editor} from '@tiptap/core';
import {InputProps, message} from 'antd';
import {useEffect} from 'react';
import Toast from 'antd-mobile/es/components/toast';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {useCount} from '../hooks/useCounts';

interface ToolsProps {
    count?: InputProps['count'];
    editor: Editor;
    value?: string;
}

export const Tools = ({count, editor}: ToolsProps) => {
    const {show, max = 0} = count || {};
    const {count: number} = useCount({editor});

    useEffect(() => {
        if (max && number >= max) {
            if (isMobileDevice()) {
                Toast.show('已达可输入字数上限');
            } else {
                message.error('已达可输入字数上限');
            }
        }
    }, [max, number]);
    return (
        count &&
        show && (
            <div className="absolute bottom-[9px] right-3">
                {typeof show === 'function'
                    ? show({value: '', count: number >= max ? max : number, maxLength: max})
                    : null}
            </div>
        )
    );
};
