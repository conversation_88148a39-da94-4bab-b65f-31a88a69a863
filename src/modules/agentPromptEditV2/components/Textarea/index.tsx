/**
 * <AUTHOR>
 * @file
 */
import {Editor, EditorContent, useEditor} from '@tiptap/react';
import {useEffect, useMemo, useRef} from 'react';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Document from '@tiptap/extension-document';
import History from '@tiptap/extension-history';
import debounce from 'lodash/debounce';
import {InputProps} from 'antd';
import Placeholder from '@tiptap/extension-placeholder';
import {StyledTiptap} from './StyledTiptap';
import SlotText from './Extension/SlotTextExtension';
import {convertTiptap, reverseJSON} from './utils';
import {Tools} from './component/tools';
import {clipboardTextParser} from './Extension/clipboardTextParser';
import SlotKeyboardExtension from './Extension/SlotKeyboardExtension';
import {CustomCharacterCount} from './Extension/CustomCharacterCount';

interface Props {
    // 编辑器内容
    value?: string;
    // 用于兼容Form.Item的onChange事件
    onChange?: (value?: string) => void;
    // 编辑器placeholder
    placeholder?: string;
    // 主动更新外部value的事件
    onValueChange?: (event: TextAreaEvent) => void;
    // 编辑器聚焦事件
    onFocus?: (event: TextAreaEvent) => void;
    // 编辑器失焦事件
    onBlur?: () => void;
    // 是否只读
    readonly?: boolean;
    // 是否隐藏高亮
    showHighlight?: boolean;
    // 用于兼容Form.Item的count事件
    count?: InputProps['count'];

    className?: string;
}

export interface TextAreaEvent {
    target: {
        value: string;
    };
}

export const SlotTextarea = (props: Props) => {
    const {value, onChange, onValueChange, count, placeholder, className, readonly, showHighlight} = props;
    const editorValue = useRef<string | null>(null); // 用于记录是否是编辑数值
    const isFirstRender = useRef(true);
    const {max} = count || {};
    // 主动编辑时，对外输出
    const debouncedOutput = useMemo(
        () =>
            debounce((editor: Editor) => {
                // 避免首次渲染时，触发防抖输出
                if (isFirstRender.current) {
                    isFirstRender.current = false;
                    return;
                }

                const json = editor?.getJSON();
                const text = reverseJSON(json, false);
                editorValue.current = text;
                onValueChange?.({target: {value: text}});
                onChange?.(text);
            }, 200),
        [onChange, onValueChange]
    );

    // 编辑器的基础配置
    const editor = useEditor({
        parseOptions: {
            preserveWhitespace: true,
        },
        editorProps: {
            clipboardTextParser: clipboardTextParser,
        },
        editable: !readonly,
        content: convertTiptap(value, readonly, showHighlight),
        extensions: [
            Document,
            Paragraph,
            Text,
            History,
            Placeholder.configure({
                placeholder: placeholder || '请输入内容',
                showOnlyWhenEditable: false,
            }),
            SlotText.configure({
                max,
            }),
            SlotKeyboardExtension,
            CustomCharacterCount.configure({
                limit: max,
            }),
        ],

        onUpdate: () => {
            // 防抖输出
            editor && debouncedOutput(editor);
        },

        onFocus: () => {
            const json = editor?.getJSON();
            const text = reverseJSON(json || {}, false);
            props.onFocus && props.onFocus({target: {value: text || ''}});
        },
        onBlur: () => {
            props.onBlur && props.onBlur();
        },
    });

    // 外部value更新时，更新编辑器内容
    useEffect(() => {
        if (editor && value !== editorValue.current) {
            try {
                const currentPos = editor.view.state.selection.from;
                editor.commands.setContent(convertTiptap(value, readonly, showHighlight), true, {
                    preserveWhitespace: true,
                });
                editor.commands.setTextSelection(currentPos);
            } catch (error) {
                console.error(error);
            }
        }
    }, [editor, value, readonly, showHighlight]);

    if (!editor) {
        return null;
    }

    return (
        <div className={`relative  w-full px-3 py-2 caret-[#556dea] ${className}`}>
            <StyledTiptap className="editor-container" readonly={readonly}>
                <EditorContent editor={editor} />
            </StyledTiptap>
            <Tools count={count} editor={editor} value={value} />
        </div>
    );
};
