import {J<PERSON><PERSON>ontent} from '@tiptap/core';
import {Slice, Fragment, Node} from '@tiptap/pm/model';

// 用于避免tiptap在粘贴文本时，自动跳过空换行符的行为
// https://github.com/ueberdosis/tiptap/issues/775
export function clipboardTextParser(text: string, context: any) {
    const blocks = text.split(/(?:\r\n?|\n)/);
    const nodes: Node[] = [];

    blocks.forEach(line => {
        const nodeJson: JSONContent = {type: 'paragraph'};
        if (line.length > 0) {
            nodeJson.content = [{type: 'text', text: line}];
        }

        const node = Node.fromJSON(context?.doc?.type?.schema, nodeJson);
        nodes.push(node);
    });

    const fragment = Fragment.fromArray(nodes);
    return Slice.maxOpen(fragment);
}
