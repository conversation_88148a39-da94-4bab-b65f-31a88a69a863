// 自定义字符限制

import {Extension} from '@tiptap/core';
import {Plugin, PluginKey} from '@tiptap/pm/state';
import {EXTENSION_OPTIONS} from '../utils';

export interface CharacterCountOptions {
    limit: number;
}

export interface CharacterCountStorage {
    characters: () => number;
    words: () => number;
    limit: number;
}

export const CustomCharacterCount = Extension.create<CharacterCountOptions>({
    name: 'customCharacterCount',

    addOptions() {
        return {
            limit: Infinity,
        };
    },
    /**
     * @description 添加 ProseMirror 插件，用于过滤 transaction，防止字符数超过限制
     */
    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('customCharacterCount'),
                // 过滤transaction，防止字符数超过限制
                filterTransaction: (tr, state) => {
                    const newLine = state.doc.content.content.length - tr.doc.content.content.length;
                    // 如果是删除操作,始终允许
                    if (tr.steps.some(step => step.toString().includes('delete'))) {
                        return true;
                    }

                    // 计算新文档的字符数,如果超过了，则截取文章后的内容
                    const doc = tr.doc;
                    let count = 0;
                    doc.descendants(node => {
                        if (node.type.name === 'paragraph') {
                            count += 1;
                        } else if (node.type.name === 'text') {
                            count += node.text?.length || 0;
                        } else if (node.type.name === EXTENSION_OPTIONS.name) {
                            count += node.attrs.content?.length || 0;
                        }
                    });

                    if (count > this.options.limit) {
                        // 创建一个新的 transaction 来截取内容
                        const excess = count - this.options.limit;
                        const position = doc.content.size - excess + newLine;
                        if (position > 0) {
                            const lastTextPos = doc.resolve(position);
                            tr.delete(lastTextPos.pos, doc.content.size);
                        }
                    }
                    return true;
                },
            }),
        ];
    },
});
