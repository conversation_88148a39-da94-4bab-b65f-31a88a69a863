// react不允许大写的组件被当作函数使用，但是由于tiptap官方提供的ReactNodeViewRenderer是大写开头的，所以这里禁用大写规则
/* eslint-disable new-cap */
/**
 * <AUTHOR>
 * @file SlotText的扩展配置
 */
import {mergeAttributes, InputRule} from '@tiptap/core';
import {ReactNodeViewRenderer} from '@tiptap/react';
import Text from '@tiptap/extension-text';
import {Slice} from '@tiptap/pm/model';
import {Plugin} from '@tiptap/pm/state';
import {clipboardTextSerializer, EXTENSION_OPTIONS, SlotInputRegex} from '../utils';
import {SlotText} from './SlotText';

// 创建自定义输入规则
const customInputRule = new InputRule({
    find: SlotInputRegex,
    handler: ({state, range, match}) => {
        const placeholder = match[1] || '';
        const content = match[2] || '';
        const {tr} = state;

        tr.replaceWith(
            range.from - 1,
            range.to,
            state.schema.nodes.slotInput.create({
                content: content,
                placeholder: placeholder,
            })
        );
    },
});

export default Text.extend({
    name: EXTENSION_OPTIONS.name,

    inline: true,

    group: 'inline',

    atom: true,

    selectable: false,
    addOptions() {
        return {
            max: 0,
        };
    },
    addAttributes() {
        return {
            content: {
                default: '',
            },
            placeholder: {
                default: '',
            },
        };
    },
    parseHTML() {
        return [
            {
                tag: EXTENSION_OPTIONS.tag,
            },
        ];
    },

    renderHTML({HTMLAttributes}) {
        return [EXTENSION_OPTIONS.tag, mergeAttributes(HTMLAttributes)];
    },
    addInputRules() {
        return [customInputRule];
    },
    // 组件就在这里挂载
    addNodeView() {
        return ReactNodeViewRenderer((props: any) => {
            return SlotText({
                ...props,
                readOnly: this.editor.isEditable,
                max: this.options.max,
                node: {
                    ...props.node,
                    attrs: {
                        content: props.node.attrs.content || '',
                        placeholder: props.node.attrs.placeholder || '',
                    },
                },
            });
        });
    },
    // ProseMirror插件，用于处理复制事件
    addProseMirrorPlugins() {
        return [
            new Plugin({
                props: {
                    // 处理复制事件，防止SlotText的样式被复制
                    clipboardTextSerializer: (slice: Slice) => {
                        const text = clipboardTextSerializer(slice);
                        // 往剪贴板中添加文本，在 firefox 浏览器或个别移动端浏览器可能会因为无权限而写入剪切板失败
                        navigator.clipboard.writeText(text).catch(e => console.error(e));
                        return text;
                    },
                },
            }),
        ];
    },
});
