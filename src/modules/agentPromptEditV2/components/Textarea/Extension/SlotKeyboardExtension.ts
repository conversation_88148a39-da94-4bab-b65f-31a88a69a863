/**
 * <AUTHOR>
 * @file SlotText的键盘事件扩展配置
 */
import {Extension} from '@tiptap/core';
import {Selection} from '@tiptap/pm/state';
import {EXTENSION_OPTIONS, SLOT_EVENT} from '../utils';

// 定义类型
interface EditorParams {
    editor: {
        state: any;
        view: any;
    };
}

interface CursorDirection {
    direction: 'left' | 'right';
}

const SlotKeyboardExtension = Extension.create({
    name: 'slotKeyboard',

    /**
     * @description
     * 添加键盘快捷键，实现光标在节点之间的移动和删除操作
     *
     * @returns {Record<string, Function>} 返回一个包含键盘快捷键名称及对应处理函数的记录对象，主要为editor层面
     * - ArrowRight：按下右箭头，光标移动到slotInput节点后面
     * - ArrowLeft：按下左箭头，光标移动到slotInput节点前面
     * - ArrowUp：按下向上箭头，光标移动到slotInput节点上方
     * - ArrowDown：按下向下箭头，光标移动到slotInput节点下方
     * - Backspace：按下退格键，如果光标在SlotText后，则触发slotBackspace事件
     *
     * @param {EditorParams} params 编辑器参数，包含editor（编辑器实例）、view（视图实例）等属性
     * @param {CursorDirection['direction']} direction 光标移动的方向，可选值为'left'或'right'，默认值为'right'
     *
     * @returns {boolean} 返回true表示已经处理了该快捷键，否则返回false
     */
    addKeyboardShortcuts() {
        // 自定义事件，在handleSlotNodeFocus中被调用
        const createSlotFocusEvent = (direction: CursorDirection['direction']) => {
            // 创建自定义事件
            return new CustomEvent(SLOT_EVENT.slotFocus, {
                detail: {direction},
            });
        };

        // 主动触发slot节点的focus事件
        const handleSlotNodeFocus = (dom: HTMLElement, direction: CursorDirection['direction']) => {
            // 获取slot节点
            const slotTextEditDom = dom?.querySelector<HTMLElement>(`.${EXTENSION_OPTIONS.className}`);
            // 触发自定义事件
            if (slotTextEditDom) {
                slotTextEditDom.dispatchEvent(createSlotFocusEvent(direction));
                return true;
            }
            return false;
        };

        // 处理外部editor的光标移动
        const handleArrowNavigation = (
            {editor}: EditorParams,
            offset: number,
            direction: CursorDirection['direction']
        ) => {
            const {state, view} = editor;
            const {selection} = state;
            const {$from} = selection;

            // 获取光标坐标
            const currentCoords = view.coordsAtPos($from.pos);
            // 获取光标坐标对应的节点
            const pos = view.posAtCoords({
                left: currentCoords.left,
                top: currentCoords.top + offset,
            });

            if (!pos) return false;

            // 获取光标坐标对应的节点
            const targetNode = state.doc.nodeAt(pos.pos);
            const beforeNode = state.doc.nodeAt(pos.pos - 1);
            const afterNode = state.doc.nodeAt(pos.pos + 1);
            // 光标优先聚焦到slotInput节点
            if (
                targetNode?.type.name === EXTENSION_OPTIONS.name ||
                beforeNode?.type.name === EXTENSION_OPTIONS.name ||
                afterNode?.type.name === EXTENSION_OPTIONS.name
            ) {
                let newPos = pos.pos;
                if (beforeNode?.type.name === EXTENSION_OPTIONS.name) {
                    newPos = pos.pos - 1;
                } else if (afterNode?.type.name === EXTENSION_OPTIONS.name) {
                    newPos = pos.pos + 1;
                }

                const dom = view.nodeDOM(newPos);
                return handleSlotNodeFocus(dom, direction);
            }

            // 设置光标位置
            view.dispatch(state.tr.setSelection(Selection.near(state.doc.resolve(pos.pos))));
            return true;
        };

        return {
            // 按下右箭头
            ArrowRight: ({editor: {state, view}}: EditorParams) => {
                const {$from} = state.selection;

                if ($from.nodeAfter?.type.name === EXTENSION_OPTIONS.name) {
                    const dom = view.nodeDOM($from.pos);
                    return handleSlotNodeFocus(dom, 'right');
                }
                return false;
            },

            // 按下左箭头
            ArrowLeft: ({editor: {state, view}}: EditorParams) => {
                const {$from} = state.selection;

                if ($from.nodeBefore?.type.name === EXTENSION_OPTIONS.name) {
                    const dom = view.nodeDOM($from.pos - 1);
                    return handleSlotNodeFocus(dom, 'left');
                }
                return false;
            },
            // 按下向上箭头
            ArrowUp: (params: EditorParams) => handleArrowNavigation(params, -10, 'left'),

            // 按下向下箭头
            ArrowDown: (params: EditorParams) => handleArrowNavigation(params, 30, 'right'),

            // 按下退格键
            Backspace: ({editor: {state, view}}: EditorParams) => {
                const {$from, $to} = state.selection;
                // 如果是在SlotText后按下退格键,且不是在有选中的情况下删除
                const isSelected = $from.pos !== $to.pos;
                if ($from.nodeBefore?.type.name === EXTENSION_OPTIONS.name && !isSelected) {
                    const dom = view.nodeDOM($from.pos - 1);
                    const slotTextEditDom = dom?.querySelector(`.${EXTENSION_OPTIONS.className}`);

                    if (slotTextEditDom && slotTextEditDom.innerText !== '') {
                        // 触发useSlotEvents中的slotBackspace事件
                        slotTextEditDom.dispatchEvent(new CustomEvent(SLOT_EVENT.slotBackspace));
                        return true;
                    }
                }
                return false;
            },
        };
    },
});

export default SlotKeyboardExtension;
