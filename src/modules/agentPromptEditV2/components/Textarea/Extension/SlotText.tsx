/**
 * <AUTHOR>
 * @file SlotText组件
 */

import {Editor, NodeViewWrapper} from '@tiptap/react';
import React, {useRef, useEffect, useCallback} from 'react';
import styled from '@emotion/styled';
import {EXTENSION_OPTIONS} from '../utils';
import useSlotEvents from '../hooks/useSlotEvents';
import {useCount} from '../hooks/useCounts';

const EditableSpan = styled.span`
    color: #5562f2;
    display: inline-block;
    min-width: 1px;
    caret-color: #5562f2;
    user-select: text;
    cursor: text;
    word-break: break-all;

    &:focus {
        outline: none;
    }
`;

const Placeholder = styled.span`
    color: #a9afec;
    cursor: text;
    user-select: none;
`;

const StyledEmpty = styled.span`
    color: #5562f2;
    display: inline-block;
    width: 3px;
`;
// 添加类型定义
interface SlotTextProps {
    node: {
        attrs: {
            content: string;
            placeholder?: string;
        };
    };
    updateAttributes: (attrs: {content: string}) => void;
    readOnly?: boolean;
    max: number;
    editor: Editor;
}

export const SlotText: React.FC<SlotTextProps> = ({node, updateAttributes, readOnly, editor, max}) => {
    const spanRef = useRef<HTMLSpanElement>(null);
    useSlotEvents(spanRef, updateAttributes);
    const {count} = useCount({editor});

    // 获取光标的相对位置
    const getCursorPosition = useCallback(() => {
        if (!spanRef.current) return 0;

        const selection = window.getSelection();
        const range = selection?.getRangeAt(0);

        if (!range) return 0;

        // 创建一个新范围，从元素开始到光标位置
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(spanRef.current);
        preCaretRange.setEnd(range.endContainer, range.endOffset);

        // 返回光标前的字符数量
        return preCaretRange.toString().length;
    }, []);

    useEffect(() => {
        if (spanRef.current && node.attrs.content !== spanRef.current.textContent) {
            spanRef.current.innerText = node.attrs.content || '';
        }
    }, [node.attrs.content]);
    // 由于粘贴事件会被编辑器捕获，所以需要手动处理
    const handlePaste = useCallback(
        (e: React.ClipboardEvent<HTMLSpanElement>) => {
            e.preventDefault();
            e.stopPropagation();

            // 获取剪贴板的文本
            let pastedText = e.clipboardData.getData('text/plain');

            // 计算允许粘贴的最大字符数
            const allowedChars = max - count;

            // 截取允许的字符数
            if (pastedText.length > allowedChars) {
                pastedText = pastedText.substring(0, allowedChars);
            }

            // 获取当前选区
            const selection = window.getSelection();
            const range = selection?.getRangeAt(0);

            if (range && selection) {
                // 插入文本
                range.deleteContents();
                const textNode = document.createTextNode(pastedText);
                range.insertNode(textNode);

                // 更新内容
                const newContent = spanRef.current?.textContent || '';
                updateAttributes({content: newContent});

                // 将光标定位到插入文本的末尾
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        },
        [updateAttributes, count, max]
    );

    // 聚焦事件
    const focusSlot = useCallback((isRight = false) => {
        spanRef.current?.focus();
        if (isRight) {
            // 将光标移动到文本最后
            const range = document.createRange();
            range.selectNodeContents(spanRef.current as Node);
            range.collapse(false);
            const selection = window.getSelection();
            selection?.removeAllRanges();
            selection?.addRange(range);
        }
    }, []);

    // 光标移动事件
    const changeCursorPosition = useCallback(
        (isUp: boolean) => {
            const position = getCursorPosition();
            setTimeout(() => {
                const newPosition = getCursorPosition();
                // 如果光标位置没有改变，说明已经在头/尾
                if (position === newPosition && spanRef.current) {
                    // 获取当前节点的位置
                    const pos = editor.view.posAtDOM(spanRef.current, 0);
                    // 将光标移动到当前节点之前
                    editor.commands.focus(pos + (isUp ? 0 : 1));
                }
            }, 0);
        },
        [editor.commands, editor.view, getCursorPosition]
    );
    // 输入事件
    const onInput = useCallback(
        (e: React.ChangeEvent<HTMLSpanElement>) => {
            const currentText = e.target.textContent || '';
            const oldText = node.attrs.content;

            // 如果字符数超过限制，阻止输入
            if (count >= max && currentText.length >= oldText.length) {
                if (spanRef.current) {
                    spanRef.current.innerText = node.attrs.content || '';
                    focusSlot(true);
                }
                return;
            }

            if (currentText.endsWith('\n')) {
                e.target.textContent = currentText.slice(0, -1);
                focusSlot(true);
            }

            const content = e.target.textContent || '';
            updateAttributes({content});
        },
        [count, focusSlot, max, node.attrs.content, updateAttributes]
    );
    // 删除事件
    const handleDelete = useCallback(
        (e: React.KeyboardEvent<HTMLSpanElement>) => {
            const selection = window.getSelection();
            const range = selection?.getRangeAt(0);
            // 如果光标在整个SlotText的开始位置,则删除前一个字符
            const position = getCursorPosition();

            // 如果光标在整个SlotText的开始位置,则删除前一个字符
            if ((position === 0 || node.attrs.content === '') && range?.startOffset === 0) {
                e.preventDefault(); // 阻止默认行为

                const pos = editor.view.posAtDOM(spanRef.current as Node, 0);
                if (node.attrs.content) {
                    // 如果内容不为空，删除前一个字符
                    editor
                        .chain()
                        .focus(pos - 1)
                        .deleteRange({from: pos - 1, to: pos})
                        .run();
                } else {
                    // 如果内容为空，删除当前节点
                    editor
                        .chain()
                        .focus(pos)
                        .deleteRange({from: pos, to: pos + 1})
                        .run();
                }
            }
        },
        [editor, getCursorPosition, node.attrs.content]
    );
    // 回车事件
    const handleEnter = useCallback(
        (e: React.KeyboardEvent<HTMLSpanElement>) => {
            e.preventDefault();
            // 手动插入换行符+空格
            const selection = window.getSelection();
            const range = selection?.getRangeAt(0);

            if (range) {
                const textNode = document.createTextNode('\n ');
                range.insertNode(textNode);
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection?.removeAllRanges();
                selection?.addRange(range);

                // 更新内容
                const content = spanRef.current?.textContent || '';
                updateAttributes({content});
            }
        },
        [updateAttributes]
    );

    // 键盘事件
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLSpanElement>) => {
            if (e.key === 'Backspace') {
                handleDelete(e);
            }

            if (e.key === 'Enter') {
                handleEnter(e);
            }

            if (e.key === 'ArrowUp') {
                changeCursorPosition(true);
            } else if (e.key === 'ArrowDown') {
                changeCursorPosition(false);
            }
        },
        [changeCursorPosition, handleDelete, handleEnter]
    );

    // 点击SlotText事件
    const onClick = useCallback(
        (e: React.MouseEvent<HTMLSpanElement>) => {
            if (!readOnly) {
                return;
            }

            e.preventDefault();
            e.stopPropagation();
            focusSlot();
        },
        [focusSlot, readOnly]
    );

    return (
        <NodeViewWrapper className="react-component mx-[3px] rounded-[6px] bg-[#5562f21a] py-[2px]" as="span">
            <StyledEmpty contentEditable={false} />
            <EditableSpan
                className={EXTENSION_OPTIONS.className}
                ref={spanRef}
                contentEditable={readOnly}
                onInput={onInput}
                onPaste={handlePaste}
                onKeyDown={handleKeyDown}
                onClick={onClick}
                style={{
                    display: node.attrs.content ? 'inline' : 'inline-block',
                }}
            />
            {!node.attrs.content && <Placeholder onMouseDown={onClick}>{node.attrs.placeholder}</Placeholder>}
            <StyledEmpty contentEditable={false} />
        </NodeViewWrapper>
    );
};
