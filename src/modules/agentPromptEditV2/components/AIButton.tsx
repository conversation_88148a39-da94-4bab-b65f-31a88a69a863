/* eslint-disable complexity */
/**
 * @file 双端 AI 生成按钮
 * <AUTHOR>
 * @date 2024/08/21
 */

import {ConfigProvider, Tooltip} from 'antd';
import omit from 'lodash/omit';
import {HtmlHTMLAttributes, useCallback, useState} from 'react';
import Toast from 'antd-mobile/es/components/toast';
import classNames from 'classnames';
import {css} from '@emotion/css';
import loadingBluePng from '@/assets/loading-blue.png';
import Icon from '@/components/Icon';
import {Z_INDEX_MAP} from '@/styles/z-index';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {usePromptEditContext} from '../utils';

const tooltipStyle = css`
    max-width: 100% !important;
`;

const iconAiImgStyle = css`
    :before {
        padding: 1px;
        border-radius: 50%;
        background: #fff;
    }
`;

interface AIBuildButtonProps extends HtmlHTMLAttributes<HTMLDivElement> {
    tooltip: string;
    label?: any;
    disabled?: boolean;
    iconType?: 'default' | 'solid';
    building?: boolean;
}

const isMobile = isMobileDevice();

export default function AIBuildButton({iconType = 'default', building = false, ...props}: AIBuildButtonProps) {
    const [isShowTooltip, setIsShowTooltip] = useState(false);

    const handleClick = useCallback(
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (props.disabled) {
                isMobile && props.tooltip !== '' && Toast.show(props.tooltip);
            } else {
                props.onClick?.(e);
            }
        },
        [props]
    );

    const handleMouseOver = useCallback(() => {
        setIsShowTooltip(isMobile ? false : props.tooltip !== '');
    }, [props.tooltip]);

    const handleMouseLeave = useCallback(() => {
        setIsShowTooltip(false);
    }, []);

    const {readonly} = usePromptEditContext();

    return (
        !readonly && (
            <ConfigProvider
                theme={{
                    components: {
                        Button: {
                            colorText: '#50525C',
                        },
                    },
                }}
            >
                <Tooltip
                    open={isShowTooltip && !building}
                    title={isMobile ? '' : props.tooltip}
                    placement="top"
                    overlayClassName={tooltipStyle}
                    zIndex={Z_INDEX_MAP.toolTipAI}
                >
                    <div
                        {...omit(props, 'tooltip', 'label', 'disabled')}
                        className={classNames('ml-[15px] inline-flex items-center font-normal', {
                            'cursor-not-allowed text-[rgba(85,98,242,0.4)]': props.disabled,
                            'cursor-pointer text-primary hover:text-[#3644D9]': !props.disabled,
                        })}
                        onClick={handleClick}
                        onMouseOver={handleMouseOver}
                        onMouseLeave={handleMouseLeave}
                    >
                        {iconType === 'default' &&
                            (building ? (
                                <img src={loadingBluePng} className="anticon-spin h-[14px] w-[14px] opacity-40" />
                            ) : (
                                <Icon name="generate" className={classNames(isMobile ? 'text-base' : 'text-sm')} />
                            ))}

                        {iconType === 'solid' && (
                            <Icon
                                name="ai-generateimg"
                                hoverStyle={false}
                                className={classNames('rounded-full text-2xl hover:text-primaryHover', iconAiImgStyle)}
                            />
                        )}

                        {!isMobile && props.label && (
                            <span className="ml-[3px] inline-block text-[14px]">{props.label}</span>
                        )}
                    </div>
                </Tooltip>
            </ConfigProvider>
        )
    );
}
