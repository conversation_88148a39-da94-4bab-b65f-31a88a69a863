import {PlusCircleOutlined} from '@ant-design/icons';
import {useCallback} from 'react';
import {SystemLabelType} from '@/api/agentEdit/interface';

export default function AssistantTag(props: {
    name: SystemLabelType;
    value?: boolean;
    onChange?: (val: boolean) => void;
}) {
    const handleAddTag = useCallback(() => {
        props.onChange?.(true);
    }, [props]);

    return (
        props.value === false && (
            <div
                onClick={handleAddTag}
                className="text-14px mr-[11px] inline-flex cursor-pointer items-center font-medium text-primary"
            >
                <PlusCircleOutlined className="mr-1" />
                {props.name === SystemLabelType.THOUGHT ? '思考路径' : '个性化'}
            </div>
        )
    );
}
