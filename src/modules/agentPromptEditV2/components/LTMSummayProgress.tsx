/**
 * @file 长期记忆手动总结状态栏组件
 * <AUTHOR>
 */
import {LTMSummaryProgress} from '@/api/agentEditV2/interface';
import loadingAnimation from '@/assets/loading-animation.gif';

export default function LTMSummaryProgressCmpt(progress: LTMSummaryProgress) {
    const {summaryText, summaryStatus} = progress;
    if (summaryText && summaryStatus === 'finished') {
        return <span className="whitespace-pre-line">{summaryText}</span>;
    } else if (summaryStatus === 'doing') {
        return (
            <span className="inline-flex items-center align-middle text-gray-tertiary">
                <img src={loadingAnimation} className="mr-1 h-4 w-4" alt="加载中"></img>
                <span>总结中，预计 20 秒内完成...</span>
            </span>
        );
    }
    return (
        <span className="inline-flex items-center align-middle text-gray-tertiary">
            <span className="iconfont icon-error mr-1"></span>
            <span>总结失败，请重试</span>
        </span>
    );
}
