/**
 * @file 参与活动记录- card
 * <AUTHOR>
 */
import {useCallback, useRef} from 'react';
import {Card, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useNavigate} from 'react-router-dom';
import dayjs from 'dayjs';
import urls from '@/links';
import {DateFormat} from '@/utils/date';
import {convertToAutoFormat} from '@/utils/processImage';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import useTextOverflow from '@/modules/center/hooks/useTextOverflow';
import {ActivitiesRecordInfo} from '@/api/activityRecord/interface';
import ActivityTags from '@/modules/activityList/components/ActivityTags';

const CardContainer = styled.div`
    .ant-card {
        box-shadow: none !important;
        padding: 16px !important;
    }
`;

export default function ActivityRecordCardPC({recordData}: {recordData: ActivitiesRecordInfo}) {
    const navigate = useNavigate();
    const cardRef = useRef<HTMLDivElement>(null);

    const [descriptionRef, isDescriptionFlow] = useTextOverflow();

    const {clickLog} = useUbcLogV3();

    const handleDetail = useCallback(() => {
        navigate(urls.activityDetail.fill({id: String(recordData.id)}));

        clickLog(EVENT_VALUE_CONST.ACTIVITY_HISTORY_CARD);
    }, [navigate, clickLog, recordData]);

    return (
        <CardContainer>
            <Card
                ref={cardRef}
                headStyle={{
                    justifyContent: 'start',
                    borderBottom: 0,
                    padding: 0,
                    paddingBottom: 0,
                    cursor: 'pointer',
                }}
                bodyStyle={{
                    padding: 0,
                    paddingBottom: 0,
                    height: 'auto',
                    overflow: 'hidden',
                }}
                bordered={false}
                onClick={handleDetail}
            >
                <div className="flex-row-center flex w-full cursor-pointer">
                    <div className="relative mr-3 box-border aspect-[16/9] h-[118px] w-[208px] flex-shrink-0 cursor-pointer rounded-[9px]">
                        <img
                            alt="example"
                            src={convertToAutoFormat(recordData.coverImg)}
                            className="h-full w-full rounded-[9px] object-cover object-center"
                        />
                    </div>
                    <div className="flex-column-start min-w-0 flex-1 py-[9px]">
                        <div className="mb-[6px] truncate break-all text-[21px] font-medium leading-6 text-colorTextDefault">
                            <span>{recordData.title}</span>
                        </div>
                        {recordData?.subTitle && (
                            <Tooltip
                                placement="top"
                                title={isDescriptionFlow ? recordData.subTitle : ''}
                                autoAdjustOverflow={false}
                                overlayStyle={{
                                    maxWidth: '404px',
                                }}
                            >
                                <div
                                    className="min-h-[20px] truncate text-sm font-normal text-[#272933]"
                                    ref={descriptionRef}
                                >
                                    {recordData.subTitle}
                                </div>
                            </Tooltip>
                        )}

                        <div className="my-[6px] flex h-4 leading-none">
                            <ActivityTags activityData={recordData} />
                        </div>
                        <div className="flex h-[22px] items-center justify-between gap-[11px] font-pingfang text-sm font-normal">
                            <div className="truncate text-gray-tertiary">
                                {recordData?.participateInTime &&
                                    recordData?.participateInTime !== 0 &&
                                    `参与时间: ${dayjs(recordData.participateInTime * 1000).format(
                                        DateFormat.YMD_CHINESE
                                    )}`}
                            </div>

                            <div className="whitespace-nowrap rounded-md  text-primary">
                                <span className="iconfont icon-visual mr-[3px] h-4 w-4 text-center text-sm text-primary" />
                                查看详情
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </CardContainer>
    );
}
