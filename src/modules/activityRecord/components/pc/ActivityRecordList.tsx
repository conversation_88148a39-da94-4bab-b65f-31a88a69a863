/**
 * 通用化活动中心-活动记录--列表页 - PC
 * <AUTHOR>
 */
import {useCallback, useEffect, useState} from 'react';
import {useNavigate, useLocation, useSearchParams} from 'react-router-dom';
import {<PERSON><PERSON>, Row, Col, Pagination} from 'antd';
import urls from '@/links';
import Loading from '@/components/Loading';
import emptyImgUrl from '@/assets/version_empty_3.png';
import ContentHeader from '@/modules/pluginCenter/components/ContentHeader';
import {GetRecordListResponse, DEFAULT_PAGINATION_SETTINGS} from '@/api/activityRecord/interface';
import ActivityRecordCard from '@/modules/activityRecord/components/pc/ActivityRecordCard';
import api from '@/api/activityRecord';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';

const RequestStatus = {
    /** 加载中 */
    Loading: 'loading',
    /** 成功 */
    Success: 'success',
    /** 失败 */
    Failure: 'failure',
};

export default function ActivityRecordList() {
    const navigate = useNavigate();
    const location = useLocation();
    const {displayLog} = useUbcLogV3();

    const [searchParams, setSearchParams] = useSearchParams();
    const pageNoParam = Number(searchParams.get('pageNo')) || DEFAULT_PAGINATION_SETTINGS.pageNo;

    const [requestStatus, setRequestStatus] = useState(RequestStatus.Loading);
    const [requestError, setRequestError] = useState<any>();

    // 列表数据
    const [recordLists, setRecordList] = useState<GetRecordListResponse>();

    const handleBackActivityList = useCallback(() => {
        if (location.key === 'default' || window.history.length === 1) {
            navigate(urls.activityList.raw());
        } else {
            navigate(-1);
        }
    }, [location.key, navigate]);

    const getRecordListData = useCallback(
        async (pageNo: number = pageNoParam, pageSize: number = DEFAULT_PAGINATION_SETTINGS.pageSize) => {
            try {
                setRequestStatus(RequestStatus.Loading);
                const res = await api.getActivityRecordList({
                    pageNo,
                    pageSize,
                });
                setRecordList(res);

                setRequestStatus(RequestStatus.Success);
            } catch (e) {
                setRequestStatus(RequestStatus.Failure);
                setRequestError(e);
                throw e;
            }
        },
        [pageNoParam]
    );

    const handlePageNoChange = useCallback(
        (newPageNo: number) => {
            setSearchParams(
                () => ({
                    pageNo: String(newPageNo),
                }),
                {replace: true}
            );
        },
        [setSearchParams]
    );

    useEffect(() => {
        getRecordListData();
    }, [getRecordListData]);

    useEffect(() => {
        displayLog(EVENT_PAGE_CONST.ACTIVITY_RECORD);
    }, [displayLog]);

    const centerStyle = 'my-auto flex h-[calc(100vh-120px)] flex-shrink justify-center';

    return (
        <div className="min-w-7xl relative h-full max-w-[1680px] overflow-hidden">
            {/* 页面标题 */}
            <div className="flex items-center font-pingfang font-medium">
                <span
                    className="iconfont icon-a-leftbar text-black-base mr-2 cursor-pointer text-[19px] hover:text-primary"
                    onClick={handleBackActivityList}
                ></span>
                <ContentHeader title={'参与记录'} />
            </div>
            {/* 页面内容 */}
            {requestStatus === RequestStatus.Loading ? (
                <div className={centerStyle}>
                    <Loading />
                </div>
            ) : requestStatus === RequestStatus.Failure ? (
                <RenderError className={centerStyle} error={requestError} />
            ) : recordLists && recordLists.activitys?.length > 0 ? (
                // 有数据
                <div className="mt-[21px]">
                    <Row gutter={[16, 16]} wrap>
                        {recordLists.activitys.map(item => (
                            <Col xs={12} lg={12} xl={12} key={item.id}>
                                <ActivityRecordCard key={item.id} recordData={item} />
                            </Col>
                        ))}
                    </Row>

                    {recordLists.total > DEFAULT_PAGINATION_SETTINGS.pageSize && (
                        <div className="mt-6 flex justify-end">
                            <Pagination
                                className="w-fit"
                                current={recordLists.pageNo || DEFAULT_PAGINATION_SETTINGS.pageNo}
                                pageSize={recordLists.pageSize || DEFAULT_PAGINATION_SETTINGS.pageSize}
                                total={recordLists.total}
                                onChange={handlePageNoChange}
                            />
                        </div>
                    )}
                </div>
            ) : (
                // 无数据
                <div className={centerStyle}>
                    <div className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
                        <div className="flex flex-col items-center">
                            <div className="mb-6 text-center">
                                <img src={emptyImgUrl} className="h-[99px] w-[121px]" style={{margin: 'auto'}} />
                                <p className="mt-2.5 text-sm leading-[22px] text-gray-secondary">
                                    您还没有参与过任何活动哦，快来报名参加吧！
                                </p>
                            </div>
                            <Button
                                className="h-[30px] w-[86px] rounded-full font-medium"
                                type="primary"
                                size="small"
                                onClick={handleBackActivityList}
                            >
                                参与活动
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
