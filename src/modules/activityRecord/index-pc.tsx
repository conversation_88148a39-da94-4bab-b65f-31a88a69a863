/**
 * @file 活动参与记录页 - PC 端
 * <AUTHOR>
 */

import {CacheProvider} from 'react-suspense-boundary';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import ActivityRecordList from '@/modules/activityRecord/components/pc/ActivityRecordList';

export default function RecordPc() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.ACTIVITY_CENTER}>
            <div>
                <CacheProvider>
                    <CommonErrorBoundary pendingFallback={<Loading />}>
                        <ActivityRecordList />
                    </CommonErrorBoundary>
                </CacheProvider>
            </div>
        </LogContextProvider>
    );
}
