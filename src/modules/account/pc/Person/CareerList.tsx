/**
 * 职业认证列表组件，用于展示用户的职业认证信息。
 * @param careerCards - 职业认证卡片配置数组
 * <AUTHOR>
 */

import React from 'react';
import PeronQualifyCard from '@/modules/account/pc/Person/components/QualifyCard';
import {AuthCardConfigProps} from '@/modules/account/pc/Person/interface';
import {AccountInfoPage} from '@/api/account/interface';

const CareerList = ({careerCards, account}: {careerCards: AuthCardConfigProps[]; account: AccountInfoPage}) => {
    return (
        <div>
            <div className="text-black-base mb-[18px] font-pingfang text-lg font-medium leading-6">职业认证</div>
            <div className="grid grid-cols-2 gap-[15px]">
                {careerCards?.map(item => (
                    <PeronQualifyCard account={account} key={item.title} qualificationConfig={item} />
                ))}
            </div>
        </div>
    );
};

export default CareerList;
