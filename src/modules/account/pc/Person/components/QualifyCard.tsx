/**
 * 职业认证卡片
 */
import {ConfigProvider, Tooltip, Button, Flex, Popover, QRCode} from 'antd';
import classNames from 'classnames';
import {convertToAutoFormat} from '@/utils/processImage';
import {AccountInfoPage, CareerAuthStatus} from '@/api/account/interface';
import {AuthCardConfigProps, CareerAuthType} from '@/modules/account/pc/Person/interface';
import {CAREER_CARD_TITLES, CAREER_AUTH_QRCODE} from '@/modules/account/pc/Org/components/config';

export default function PersonQualifyCard({
    account,
    qualificationConfig,
}: {
    qualificationConfig: AuthCardConfigProps;
    account: AccountInfoPage;
}) {
    const {title, description, statusTag, disabled, buttonText, icon, color, bgColor, onButtonClick} =
        qualificationConfig;

    const isCareerTitle = title === CAREER_CARD_TITLES[CareerAuthType.Career];

    // 判断状态是否为认证通过状态
    const isCareerAuthPass = account?.personInfo?.careerAuthStatus === CareerAuthStatus.Pass;

    /**
     * 渲染按钮组件
     *
     * @param onButtonClick 按钮点击事件处理函数，可选参数
     * @param disabled 按钮是否禁用，可选参数
     * @returns 返回渲染后的按钮组件
     */
    const renderButton = (onButtonClick?: () => void, disabled?: boolean) => (
        <Button
            type="default"
            className={classNames('w-[86px] rounded-full p-0 text-center text-primary shadow-none', {
                'hover:border-[#EDEDF0] hover:text-[#3644D966]': !disabled,
            })}
            onClick={onButtonClick}
            disabled={disabled}
            style={{opacity: disabled ? 0.4 : 1}}
        >
            {buttonText}
        </Button>
    );

    return (
        <Flex className="h-[82px] items-center justify-start rounded-[9px] border border-solid border-[#EDEEF0] bg-white px-[15px] py-[18px]">
            <Flex className="mr-3 h-10 w-10 flex-shrink-0 items-center justify-center">
                <img className="h-full w-full" src={convertToAutoFormat(icon)} alt="" />
            </Flex>
            <Flex className="mr-[5px] min-w-0 flex-1 flex-col overflow-auto">
                <div className="flex items-center gap-[6px]">
                    <span className="text-black-base text-sm font-medium">{title}</span>
                    <Tooltip title={isCareerAuthPass && statusTag ? `认证: ${account?.personInfo?.profession}` : ''}>
                        <span
                            className="inline-flex h-4 cursor-pointer items-center rounded-[3px] border-none px-[3px] py-[2.5px] text-[0.75rem] font-medium leading-[0.75rem]"
                            style={{color: color, backgroundColor: bgColor}}
                        >
                            {statusTag}
                        </span>
                    </Tooltip>
                </div>
                <div className="mt-[6px] truncate text-sm text-gray-tertiary">
                    <span>{description}</span>
                </div>
            </Flex>
            <Flex className="flex-shrink-0">
                <ConfigProvider
                    theme={{
                        components: {
                            Button: {
                                contentFontSize: 14,
                                lineWidth: 1,
                                controlHeight: 30,
                                fontWeight: 500,
                                defaultBorderColor: '#EDEDF0',
                            },
                            QRCode: {
                                paddingSM: 8,
                            },
                        },
                    }}
                    wave={{disabled: true}}
                >
                    {isCareerTitle && !disabled ? (
                        <Popover
                            placement="bottom"
                            overlayInnerStyle={{padding: 18}}
                            content={
                                <div className="flex flex-col items-center">
                                    <div className="flex justify-center">
                                        <QRCode value={CAREER_AUTH_QRCODE} bordered={false} size={200} />
                                    </div>
                                    <div className="mt-[15px] w-[196px] break-words text-center text-sm text-gray-tertiary">
                                        打开百度App,进入我的-右上角扫一扫，扫描上方二维码去认证
                                    </div>
                                </div>
                            }
                        >
                            {renderButton(onButtonClick, disabled)}
                        </Popover>
                    ) : (
                        renderButton(onButtonClick, disabled)
                    )}
                </ConfigProvider>
            </Flex>
        </Flex>
    );
}
