/**
 * 个人认证（实名认证&&真实性认证）卡片 && 企业/机构认证（主体认证&&真实性认证）卡片
 */
import {ConfigProvider, Button, Flex, Tooltip} from 'antd';
import classNames from 'classnames';
import {AuthCardConfigProps, BaseAuthCardTitles} from '@/modules/account/pc/Person/interface';
import {AccountQualifyType, CustomerAuthStatus, QualifyStatus} from '@/api/account/interface';
import {convertToAutoFormat} from '@/utils/processImage';

// eslint-disable-next-line complexity
export default function BaseAuthCard({
    account,
    title,
    description,
    statusTag,
    disabled,
    buttonText,
    icon,
    color,
    bgColor,
    onButtonClick,
}: AuthCardConfigProps) {
    if (!account) return null;

    const {accountType, customerInfo} = account;
    const {entityStatus, authStatus, entityAuditMsg} = customerInfo || {};

    const isAuthPass = entityStatus === QualifyStatus.PASS;
    const isAuthenticityTitle = title === BaseAuthCardTitles.Authenticity;
    const isEntity = accountType !== AccountQualifyType.Person;
    const isFailedAuth =
        isEntity &&
        (entityStatus === QualifyStatus.RETURN || (authStatus === CustomerAuthStatus.RETURN && isAuthenticityTitle));

    const showTip = isEntity && !isAuthPass && isAuthenticityTitle;

    // 判断是否为政府或部门账号，如果是则真实性认证卡片按钮置灰
    const isAboutGovCompany = [AccountQualifyType.Department, AccountQualifyType.Gov].includes(accountType);

    // 新增的逻辑：判断是否显示【真实性认证】卡片按钮
    const shouldShowButton = !(accountType !== 1 && isAuthenticityTitle && authStatus === 2);

    return (
        <Flex className="h-[82px] items-center justify-start rounded-[9px] border border-solid border-[#EDEEF0] bg-white px-[15px] py-[18px]">
            <Flex className="mr-3 h-10 w-10 flex-shrink-0 items-center justify-center">
                <img className="h-full w-full" src={convertToAutoFormat(icon)} alt="" />
            </Flex>
            <Flex className="mr-[5px] min-w-0 flex-1 flex-col overflow-auto">
                <Flex className="flex items-center gap-[6px]">
                    <span className="text-black-base text-sm font-medium">{title}</span>
                    <Tooltip title={isFailedAuth ? entityAuditMsg : ''}>
                        <span
                            className={classNames(
                                'inline-flex h-4 items-center rounded-[3px] border-none px-[3px] py-[2.5px] text-[0.75rem] font-medium leading-[0.75rem]',
                                {'cursor-pointer': isFailedAuth}
                            )}
                            style={{color: color, backgroundColor: bgColor}}
                        >
                            {statusTag}
                        </span>
                    </Tooltip>
                </Flex>
                <div className="mt-[6px] truncate text-sm text-gray-tertiary">{description}</div>
            </Flex>
            <Flex className="flex-shrink-0">
                <ConfigProvider
                    theme={{
                        components: {
                            Button: {
                                contentFontSize: 14,
                                lineWidth: 1,
                                controlHeight: 30,
                                fontWeight: 500,
                                defaultBorderColor: '#EDEDF0',
                            },
                        },
                    }}
                    wave={{disabled: true}}
                >
                    <Tooltip title={showTip && !isAboutGovCompany ? '请先完成主体认证' : ''}>
                        {shouldShowButton && (
                            <Button
                                type="default"
                                className={classNames(
                                    'w-[86px] rounded-full p-0 text-center text-primary shadow-none',
                                    {'hover:border-[#EDEDF0] hover:text-[#3644D966]': !disabled}
                                )}
                                onClick={onButtonClick}
                                disabled={disabled}
                                style={{opacity: disabled ? 0.4 : 1}}
                            >
                                {buttonText}
                            </Button>
                        )}
                    </Tooltip>
                </ConfigProvider>
            </Flex>
        </Flex>
    );
}
