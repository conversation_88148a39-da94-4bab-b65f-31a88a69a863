/**
 * 实名认证-同步信息弹窗组件
 * v_jinxiaolan
 */
import {Modal, Form, Input} from 'antd';
import {useEffect, useCallback} from 'react';
import styled from '@emotion/styled';
import ModalFooter from '@/modules/agentPromptEditV2/pc/components/Privacy/ModalFooter';
import api from '@/api/account/index';

const StyledFormItem = styled(Form.Item)`
    &.ant-form-item {
        margin-bottom: 0;
    }
`;

interface RealNameModalProps {
    modalVisible?: boolean;
    handleCancel: () => void;
    handleConfirm: () => void;
}

export default function RealNameModal({modalVisible, handleCancel, handleConfirm}: RealNameModalProps) {
    const [form] = Form.useForm();

    /** 获取实名信息数据 */
    const getRealNameInfo = useCallback(async () => {
        const res = await api.getPassPersonInfo();
        form.setFieldsValue({
            realName: res?.realName,
            certCode: res?.certCode,
        });
    }, [form]);

    useEffect(() => {
        modalVisible && getRealNameInfo();
    }, [modalVisible, getRealNameInfo]);

    return (
        <Modal
            open={modalVisible}
            title={<div className="font-pingfang text-lg font-medium leading-6">实名信息</div>}
            cancelText="取消"
            okText="确定"
            centered
            closable
            width={600}
            destroyOnClose
            footer={<ModalFooter handleCancel={handleCancel} handleConfirm={handleConfirm} />}
            onCancel={handleCancel}
        >
            <div className="mb-[18px]">
                <p className="-mt-[5px] text-sm leading-5 text-gray-tertiary">您的账号已完成实名信息填写</p>
                <div className="mt-[18px] max-h-[315px] w-[552px]">
                    <Form disabled form={form} layout="vertical" name="realNameModalForm">
                        <div className="flex h-[36px] w-full items-center">
                            <div className="min-w-[55px]">
                                <span className="font-medium">姓名</span>
                                <span className="ml-1.5 text-error">*</span>
                            </div>
                            <div className="ml-6 flex-1 flex-shrink-0 justify-between">
                                <StyledFormItem name="realName">
                                    <Input className="border-0 bg-colorBgFormList text-opacity-60" />
                                </StyledFormItem>
                            </div>
                        </div>
                        <div className="mt-[18px] flex h-[36px] w-full items-center">
                            <div className="label">
                                <span className="font-medium ">身份证</span>
                                <span className="ml-1.5 text-error">*</span>
                            </div>
                            <div className="ml-6 max-w-[474px] flex-1">
                                <StyledFormItem name="certCode">
                                    <Input className="border-0 bg-colorBgFormList text-opacity-60" />
                                </StyledFormItem>
                            </div>
                        </div>
                    </Form>
                </div>
            </div>
        </Modal>
    );
}
