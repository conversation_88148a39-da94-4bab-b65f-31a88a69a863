/**
 * 个人认证模块 interface
 */
import {AccountInfoPage, AccountQualifyType} from '@/api/account/interface';

/** 职业认证类型 0-默认未进行职业认证 1-职业 2-医生 3-律师 */
export enum CareerAuthType {
    // 默认未进行职业认证
    Default = 0,
    // 职业
    Career = 1,
    // 医生
    Doctor = 2,
    // 律师
    Lawyer = 3,
}

// 0-未认证 1-新增 2-通过 4-拒绝 6-待审 9-考察期 8-取消
export enum OriginalCareerAuthStatus {
    UnAuth = 0,
    New = 1,
    Passed = 2,
    Refused = 4,
    Pending = 6,
    Examine = 9,
    Cancel = 8,
}

export interface CardStatusTagConfig {
    statusTag: string;
    color: string;
    bgColor: string;
}

export interface AuthCardConfigProps {
    account?: AccountInfoPage;
    title: string;
    description: string;
    statusTag?: string;
    color?: string;
    bgColor?: string;
    icon: string;
    docUrl?: string;
    buttonText?: string;
    disabled?: boolean;
    onButtonClick?: () => void;
}

export enum BaseAuthCardTitles {
    RealName = '实名认证',
    Authenticity = '真实性认证',
    Subject = '主体认证',
    BjhQaulify = '百家号',
}

export const AccountQualifyTypeMap: Record<AccountQualifyType, string> = {
    [AccountQualifyType.Person]: '个人',
    [AccountQualifyType.Enterprise]: '企业',
    [AccountQualifyType.Gov]: '政府',
    [AccountQualifyType.Department]: '事业',
    [AccountQualifyType.Other]: '其他组织',
};
