/**
 * 个人认证-账号中心
 */

import {Button, Modal, Row, Space} from 'antd';
import {Fragment, useCallback, useState} from 'react';
import {css} from '@emotion/css';
import urls from '@/links';
import accountApi from '@/api/account';
import LoadError from '@/components/Loading/LoadError';
import {
    AccountInfoPage,
    AccountQualifyType,
    BjhQualifyStatus,
    BjhQualifySubType,
    BjhQualifyType,
    QualifyStatus,
} from '@/api/account/interface';
import AuthCard from '@/modules/account/pc/Person/components/BaseAuthCard';
import RealNameModal from '@/modules/account/pc/Person/components/RealNameModal';
import usePersonCardHook from '@/modules/account/hooks/useAuthCardConfigHook';
import {ORG_KEY_TO_TYPE} from '../Org/components/config';
import CareerList from './CareerList';

const syncModalStyled = css`
    &.ant-modal-confirm .ant-modal-confirm-paragraph {
        max-width: 100%;
    }
`;

interface PersonInfoProps {
    account: AccountInfoPage;
    loadAccountInfo: () => Promise<any>;
}

const openOrgTypeList = () => {
    setTimeout(() => window.open(urls.accountOrgList.raw(), '_blank'));
};

const getOrgLink = (accountInfo: AccountInfoPage) => {
    const {customerInfo} = accountInfo || {};
    if (customerInfo?.entityStatus === QualifyStatus.RETURN) {
        const orgType = ORG_KEY_TO_TYPE[customerInfo?.customerType] || ORG_KEY_TO_TYPE[AccountQualifyType.Enterprise];
        setTimeout(() => window.open(`${urls.accountOrgSubmit.raw()}?type=${orgType}`, '_blank'));
    } else {
        openOrgTypeList();
    }
};

const PersonInfo = (props: PersonInfoProps) => {
    const [syncLoading, setSyncLoading] = useState<boolean>();
    const [getBjhLoading, setBjhLoading] = useState<boolean>();
    const [modal, contextHolder] = Modal.useModal();
    const {account, loadAccountInfo} = props;

    const {careerCards, personCards, realNameModalVisible, handleRealNameModalCancel, handleRealNameModalConfirm} =
        usePersonCardHook(account);

    const syncBjh = useCallback(async () => {
        try {
            setSyncLoading(true);
            await accountApi.acceptBjhInfo();
            loadAccountInfo?.();
        } finally {
            setSyncLoading(false);
        }
    }, [loadAccountInfo]);

    const disagreeSync = useCallback(() => {
        openOrgTypeList();
    }, []);

    const checkBjhSync = useCallback(() => {
        try {
            setBjhLoading(true);
            accountApi
                .getBjhQualifyInfo()
                .then(res => {
                    const enableSync =
                        res?.status === BjhQualifyStatus.PASS &&
                        res?.type === BjhQualifyType.ORGANIZATION &&
                        res?.subType === BjhQualifySubType.COMPANY;
                    if (enableSync) {
                        modal.confirm({
                            className: syncModalStyled,
                            icon: null,
                            title: '信息同步',
                            okText: '同意使用',
                            cancelText: '自行填写',
                            width: 600,
                            closable: true,
                            centered: true,
                            content: (
                                <div>
                                    <p className="mb-[18px]">
                                        账号<span className="mx-0.5">{res.name}</span>
                                        在百家号已完成组织机构认证，文心智能体平台将同步使用相关信息。
                                    </p>
                                    <h2 className="mb-[6px] font-bold">百家号机构认证信息</h2>
                                    <div className="flex items-center justify-between rounded-lg border-[1px] border-solid border-gray-border-secondary px-4 py-3">
                                        <div>
                                            <p>账号：{res.name || '-'}</p>
                                            <p>类型：企业</p>
                                            <p>名称：{res.orgName || '-'}</p>
                                        </div>
                                        <div>
                                            <span className="rounded bg-[#3FC7461A] p-[3px] font-medium text-[#3FC746]">
                                                认证通过
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ),
                            footer: (
                                <Space className="mt-[18px] flex w-full justify-end" size={6} align="end">
                                    <Button
                                        key="back"
                                        shape="round"
                                        onClick={disagreeSync}
                                        className="mr-2 rounded-[100px] border-[#EDEEF0] text-sm hover:text-gray-tertiary"
                                    >
                                        自行填写
                                    </Button>
                                    <Button
                                        key="submit"
                                        shape="round"
                                        type="primary"
                                        className="rounded-[100px] text-sm hover:bg-primaryHover"
                                        loading={syncLoading}
                                        onClick={syncBjh}
                                    >
                                        同意使用
                                    </Button>
                                </Space>
                            ),
                        });
                    } else {
                        getOrgLink(account);
                    }
                })
                .catch(() => getOrgLink(account));
        } finally {
            setBjhLoading(false);
        }
    }, [account, syncLoading, disagreeSync, modal, syncBjh]);

    /** 切换为组织机构 - 按钮事件 */
    const handleChangeOrganization = useCallback(() => {
        // 用户没有在平台侧提交过机构信息，才去查询百家号同步信息
        const needCheckBjhSync = !account.customerInfo;
        if (needCheckBjhSync) {
            checkBjhSync();
        } else {
            getOrgLink(account);
        }
    }, [account, checkBjhSync]);

    const centerStyle = 'my-auto flex h-[calc(100vh-120px)] flex-shrink justify-center';

    return (
        <Fragment>
            {contextHolder}
            <Row align="middle" className="mb-[21px]">
                <span className="mr-2 text-2xl font-medium">账号信息</span>
                <span className="text-gray-tertiary">
                    当前账户为个人账户，可切换为
                    <Button
                        type="link"
                        loading={getBjhLoading}
                        className="cursor-pointer p-0 text-primary"
                        onClick={handleChangeOrganization}
                    >
                        组织机构
                    </Button>
                </span>
            </Row>

            {personCards && personCards.length > 0 ? (
                <Fragment>
                    <div className="text-black-base mb-[18px] font-pingfang text-lg font-medium leading-6">
                        我的认证
                    </div>
                    <div className="mb-6 grid grid-cols-2 gap-[15px]">
                        {personCards?.map(items => <AuthCard key={items.title} {...items} account={account} />)}
                    </div>
                    {/* 职业信息 */}
                    <CareerList careerCards={careerCards} account={account} />
                </Fragment>
            ) : (
                <LoadError className={centerStyle} />
            )}

            {/* 实名信息弹窗组件 */}
            <RealNameModal
                modalVisible={realNameModalVisible}
                handleCancel={handleRealNameModalCancel}
                handleConfirm={handleRealNameModalConfirm}
            />
        </Fragment>
    );
};

export default PersonInfo;
