import {Avatar, Button, Card, Image, message, Popover, Space} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import classNames from 'classnames';
import {CloseOutlined} from '@ant-design/icons';
import {CacheProvider} from 'react-suspense-boundary';
import accountApi from '@/api/account';
import {
    AccountQualifyStatus,
    AccountQualifyType,
    AuthType,
    CustomerAuthStatus,
    OrgLegalPersonQR,
} from '@/api/account/interface';
import Header from '@/components/Header';
import Loading from '@/components/Loading';
import LingJingSteps from '@/components/Steps';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {LogContextProvider} from '@/utils/loggerV2/context';
import LoadError from '@/components/Loading/LoadError';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {useAccountInfo} from '../../hooks/useAccountInfo';
import {ENTERPRISE_STEPS_ITEMS} from './components/config';

const renderError = () => <LoadError />;

const OrgAuth = () => {
    const {displayLog} = useUbcLogV3();
    const {loading, account} = useAccountInfo();
    const [messageApi, contextHolder] = message.useMessage();
    const [btnLoading, setBtnLoading] = useState<{
        legal: boolean;
        remit: boolean;
    }>({
        legal: false,
        remit: false,
    });
    const [refreshLoading, setRefreshLoading] = useState<boolean>();
    const [btnDisabled, setBtnDisabled] = useState<{
        legal: boolean;
        remit: boolean;
    }>({
        legal: false,
        remit: false,
    });
    const [open, setOpen] = useState<boolean>();
    const [qrCodeInfo, setQrCodeInfo] = useState<OrgLegalPersonQR>();
    const [qrCodeExpired, setQrCodeExpired] = useState<boolean>();
    // 非政府机构，认证审核通过才允许做真实性认证
    const enableSubmit = loading || account.qualifyStatus === AccountQualifyStatus.OrgPrimePass;

    /** 检测 是否为法人认证审核中 */
    const isLegalAudit =
        account?.customerInfo?.authType === AuthType.LEGAL &&
        account?.customerInfo?.authStatus === CustomerAuthStatus.AUDIT;
    /** 法人刷脸只支持企业 */
    const isEnterprise = account?.customerInfo?.customerType === AccountQualifyType.Enterprise;

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    useEffect(() => {
        setBtnDisabled({
            legal: !isEnterprise || (!enableSubmit && !isLegalAudit),
            remit: !enableSubmit,
        });
    }, [enableSubmit, isLegalAudit, isEnterprise]);

    const onSubmitLegal = useCallback(() => {
        setBtnLoading(val => ({...val, legal: true}));
        setBtnDisabled(val => ({...val, remit: true}));
        accountApi
            .getLegalQRCode()
            .then(res => {
                setQrCodeInfo(res);
                setOpen(true);
                // 超时之后自动刷新二维码状态
                setTimeout(
                    () => {
                        setQrCodeExpired(true);
                    },
                    res.timeout * 1000 - +new Date()
                );
            })
            .finally(() => {
                setBtnLoading(val => ({...val, legal: false}));
                setBtnDisabled(val => ({...val, remit: false}));
            });
    }, []);

    const onSubmitRmit = useCallback(() => {
        setBtnLoading(val => ({...val, remit: true}));
        setBtnDisabled(val => ({...val, legal: true}));
        accountApi
            .getAccPayLink()
            .then(res => {
                if (res?.pcUrl) {
                    setTimeout(() => {
                        window.open(res.pcUrl);
                    });
                } else {
                    message.error('对公打款异常，请重新发起！');
                }
            })
            .finally(() => {
                setBtnLoading(val => ({...val, remit: false}));
                setBtnDisabled(val => ({...val, legal: false}));
            });
    }, []);

    useEffect(() => {
        // 对公打款进行中状态，直接跳转页面
        if (
            account?.customerInfo?.authType === AuthType.REMIT &&
            account?.customerInfo?.authStatus === CustomerAuthStatus.AUDIT
        ) {
            accountApi.getAccPayLink().then(res => {
                if (res?.pcUrl) {
                    messageApi
                        .open({
                            type: 'info',
                            content: '对公打款流程中，即将跳转',
                            duration: 1,
                        })
                        .then(() => (window.location.href = res.pcUrl));
                } else {
                    message.error('对公打款异常，请重新发起！');
                }
            });
        } else if (isLegalAudit) {
            // 法人刷脸进行中
            onSubmitLegal();
        }
    }, [account, messageApi, onSubmitLegal, isLegalAudit]);

    const refresh = useCallback(() => {
        setRefreshLoading(true);
        accountApi
            .getLegalQRCode()
            .then(res => {
                setQrCodeInfo(res);
                setQrCodeExpired(false);
                // 超时之后自动刷新二维码状态
                setTimeout(
                    () => {
                        setQrCodeExpired(true);
                    },
                    res.timeout * 1000 - +new Date()
                );
            })
            .finally(() => setRefreshLoading(false));
    }, []);

    const closePop = useCallback(() => {
        setOpen(false);
    }, []);

    return (
        <div>
            {contextHolder}
            <Header />
            <div className="flex flex-col justify-center pt-[60px]">
                <LingJingSteps className="mx-auto mb-12 w-1/2" current={2} items={ENTERPRISE_STEPS_ITEMS} />
                <Space className="justify-center" size={42}>
                    <Card className="h-[446px] w-[380px] px-[56px] py-[36px] text-center">
                        <Avatar
                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/compRemit.png"
                            className="mb-[20px]"
                            size={120}
                        />
                        <div className="gap-2.5 rounded-[9px] bg-gray-bg-base px-[12px]  py-[3px] font-pingfang text-[14px] font-normal leading-[21px] text-gray-tertiary">
                            资质认证通过后可进行认证
                        </div>
                        <h1 className="lead-[18px] my-[15px] my-[15px] text-[18px] font-medium">对公打款</h1>
                        <p>适用于对公账户的大型企业</p>
                        <p>（预计1个工作日）</p>
                        <Button
                            className="mt-[35px] h-[30px] font-medium leading-[20px]"
                            loading={btnLoading.remit}
                            disabled={btnDisabled.remit}
                            type="primary"
                            shape="round"
                            onClick={onSubmitRmit}
                        >
                            立即认证
                        </Button>
                    </Card>
                    <Card className="h-[446px] w-[380px] px-[56px] py-[36px] text-center">
                        <Avatar
                            src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/legalFace.png"
                            className="mb-[20px]"
                            size={120}
                        />
                        <div className="gap-2.5 rounded-[9px] bg-gray-bg-base px-[12px]  py-[3px] font-pingfang text-[14px] font-normal leading-[21px] text-gray-tertiary">
                            资质认证通过后可进行认证
                        </div>
                        <h1 className="lead-[18px] my-[15px] text-[18px] font-medium">法定代表人认证</h1>
                        <p>适用于中小微企业，个体工商户</p>
                        <p>（预计5-10分钟）</p>
                        <Popover
                            destroyTooltipOnHide
                            placement="bottomRight"
                            open={open}
                            trigger="contextMenu"
                            content={
                                <div className="relative px-[18px] py-[6px] text-center">
                                    {refreshLoading ? (
                                        <Loading />
                                    ) : (
                                        <div>
                                            <div className="mb-3.5">
                                                <Image
                                                    height={160}
                                                    width={160}
                                                    className={classNames({
                                                        'opacity-40': qrCodeExpired,
                                                    })}
                                                    src={qrCodeInfo?.base64Qrcode}
                                                />
                                            </div>
                                            {qrCodeExpired ? (
                                                <div className="leading-4">
                                                    二维码已失效，请
                                                    <Button className="p-0" type="link" onClick={refresh}>
                                                        刷新
                                                    </Button>
                                                </div>
                                            ) : (
                                                <div>请使手机百度扫码认证</div>
                                            )}
                                        </div>
                                    )}
                                    <CloseOutlined
                                        className="absolute right-0 top-[-8px] text-gray-tertiary"
                                        onClick={closePop}
                                    />
                                </div>
                            }
                        >
                            <Button
                                className="mt-[35px] h-[30px] font-medium leading-[20px]"
                                loading={btnLoading.legal}
                                disabled={btnDisabled.legal}
                                type="primary"
                                shape="round"
                                onClick={onSubmitLegal}
                            >
                                立即认证
                            </Button>
                        </Popover>
                    </Card>
                </Space>
            </div>
        </div>
    );
};

export default function OrgAuthPc() {
    return (
        <CacheProvider>
            <CommonErrorBoundary renderError={renderError}>
                <LogContextProvider page={EVENT_PAGE_CONST.ACCOUNT_ENTERPRISE} ext={{eCertificationStep: 3}}>
                    <OrgAuth />
                </LogContextProvider>
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
