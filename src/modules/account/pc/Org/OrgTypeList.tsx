/**
 * 机构类型选择页
 * v_jinxiaolan
 */
import {useCallback, useEffect} from 'react';
import {NavigateFunction, useNavigate} from 'react-router-dom';
import {Button} from 'antd';
import {CacheProvider} from 'react-suspense-boundary';
import AccountStyleProvider from '@/modules/account/pc/Org/components/AccountStyleProvider';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import Header from '@/components/Header';
import StaticTips from '@/components/StaticTips';
import {convertToAutoFormat} from '@/utils/processImage';
import {AccountQualifyStatus, AccountQualifyType} from '@/api/account/interface';
import urls from '@/links';
import {
    ORG_TIP_TEXTS,
    organizationInfos,
    ENTERPRISE_STEPS_ITEMS,
    CUSTOMER_TYPE_MAP,
} from '@/modules/account/pc/Org/components/config';
import LingJingSteps from '@/components/Steps/index';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {useAccountInfo} from '../../hooks/useAccountInfo';

const OrgTypeList = ({navigate}: {navigate: NavigateFunction}) => {
    const {displayLog} = useUbcLogV3();
    // 点击-选择机构类型，跳转到下一步骤
    const handleChangeOrg = useCallback(
        (index: number) => {
            const customerTypeIndex = (index + 2) as AccountQualifyType;
            const query = CUSTOMER_TYPE_MAP[customerTypeIndex];

            navigate(`${urls.accountOrgSubmit.raw()}?type=${query}`);
        },
        [navigate]
    );

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    return (
        <AccountStyleProvider>
            <Header />
            <div className="h-[calc(100vh-70px)] w-full overflow-y-scroll">
                {/* 信息填写提示 */}
                <div className="mx-auto mb-[35px] mt-8 w-[1025px]">
                    <StaticTips
                        className="rounded-[9px] px-[9px] py-[7px] text-colorTextDefault"
                        bgColor="bg-[#5562F20A]"
                        iconfont="icon-tip-fill"
                    >
                        {ORG_TIP_TEXTS}
                    </StaticTips>
                </div>
                <div className="flex flex-col items-center justify-center">
                    {/* 机构选择步骤条 */}
                    <LingJingSteps items={ENTERPRISE_STEPS_ITEMS} current={0} className="mb-[35px] w-[716px]" />

                    <div className="flex min-w-[1200px] rounded-lg bg-white pb-[60px] pt-[40px]">
                        {organizationInfos.map((items, index) => (
                            <div
                                key={items.title}
                                className="w-[300px] border-r border-dashed border-gray-300 px-[40px] text-center last:border-r-0"
                            >
                                <div className="mx-auto mb-[15px] h-[120px] w-[120px] overflow-hidden rounded-full">
                                    <img
                                        className="h-full w-full object-cover"
                                        src={convertToAutoFormat(items.imgUrl)}
                                    />
                                </div>
                                <div className="mb-[35px] font-pingfang">
                                    <div className="mb-[15px] text-lg font-medium leading-[18px] text-colorTextDefault">
                                        {items.title}
                                    </div>
                                    <p className="text-left text-sm leading-[21px] text-[#272933]">
                                        {items.description}
                                    </p>
                                </div>

                                <div className="text-left">
                                    <div className="mb-3 text-sm font-normal text-colorTextDefault">
                                        需要提供以下材料
                                    </div>
                                    <ul className="list-inside list-disc text-sm font-normal leading-[24px] text-[#868491]">
                                        {items.materials.map(material => (
                                            <li key={material}>{material}</li>
                                        ))}
                                    </ul>
                                    <div className="mt-[18px] flex justify-end">
                                        <Button
                                            type="primary"
                                            className="rounded-full text-sm"
                                            // eslint-disable-next-line react/jsx-no-bind
                                            onClick={() => handleChangeOrg(index)}
                                        >
                                            选择
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </AccountStyleProvider>
    );
};

export default function OrgTypeListPC() {
    const {account} = useAccountInfo();
    const navigate = useNavigate();

    useEffect(() => {
        // 企业审核中时，自动重定向到组织机构提交页
        if (account.qualifyStatus === AccountQualifyStatus.OrgPrimeAudit) {
            navigate(urls.accountOrgSubmit.raw());
        }
    }, [account, navigate]);

    return (
        <CacheProvider>
            <CommonErrorBoundary pendingFallback={<Loading />}>
                <LogContextProvider page={EVENT_PAGE_CONST.ACCOUNT_ENTERPRISE} ext={{eCertificationStep: 1}}>
                    <OrgTypeList navigate={navigate} />
                </LogContextProvider>
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
