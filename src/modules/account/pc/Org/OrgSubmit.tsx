import {useNavigate, useSearchParams} from 'react-router-dom';
import {CacheProvider} from 'react-suspense-boundary';
import {Form, Card, Modal, Input, message, Alert} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {css} from '@emotion/css';
import {useForm} from 'antd/lib/form/Form';
import get from 'lodash/get';
import classNames from 'classnames';
import {ExclamationCircleFilled} from '@ant-design/icons';
import urls from '@/links';
import {
    AccountQualifyStatus,
    AccountQualifyType,
    OrgSubmitParams,
    OrgUpdateParams,
    QualifyStatus,
} from '@/api/account/interface';
import accountApi from '@/api/account';
import smsApi from '@/api/leadsData';
import {RequiredMark} from '@/components/RequiredMark';
import Header from '@/components/Header';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import CountdownText from '@/components/CountdownText';
import LingJingSteps from '@/components/Steps';
import DICTS from '@/dicts/home';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useOrgSubmitInfo} from '../../hooks/useOrgSubmitInfo';
import {useAccountInfo} from '../../hooks/useAccountInfo';
import EnterpriseForm from './components/EnterpriseForm';
import GovForm from './components/GovForm';
import {ENTERPRISE_STEPS_ITEMS, GOV_STEPS_ITEMS, ORG_TYPE_MAP} from './components/config';
import Sample from './components/Sample';
import AccountStyleProvider from './components/AccountStyleProvider';
import {
    checkOrgSubmitFormValue,
    checkPhoneVerify,
    getEditParams,
    getSubmitParams,
    hideAuth,
} from './components/validator';
import OperatorForm from './components/OperatorForm';
import OrgSubmitFooter from './components/OrgSubmitFooter';

const styledForm = css`
    .ant-form-item-explain-error {
        margin-top: 4px;
        margin-bottom: 18px;
    }
`;

const styledAgreementFormItem = css`
    .ant-form-item-explain-error {
        margin-top: 0;
        margin-bottom: -18px;
    }
`;

const {Item} = Form;

// eslint-disable-next-line complexity
const OrgSubmit = () => {
    const {displayLog} = useUbcLogV3();
    const [searchParams] = useSearchParams() || {};
    const type = searchParams.get('type');
    const [form] = useForm<any>();
    const [submitLoading, setLoading] = useState<boolean>();
    const [messageApi, contextHolder] = message.useMessage();
    const [modal, modalContext] = Modal.useModal();
    const navigate = useNavigate();
    const {account, loadAccountInfo, loading} = useAccountInfo();
    const {orgSubmitInfo, loadOrgSubmitInfo} = useOrgSubmitInfo(!!account?.customerInfo);
    const [subDisabled, setSubDisabled] = useState<boolean>();
    const [isVerify, setIsVerify] = useState<boolean>();
    // 审核通过后允许修改运营者信息
    const [canOperatorEdit, setCanOperatorEdit] = useState<boolean>(false);
    const canEdit =
        loading === false &&
        ![
            AccountQualifyStatus.OrgPrimeAudit,
            AccountQualifyStatus.OrgPrimePass,
            AccountQualifyStatus.OrgSeniorPass,
            AccountQualifyStatus.OrgSeniorAudit,
        ].includes(account.qualifyStatus);
    // 是否展示开发者信息修改按钮
    const showEditOperator = useMemo(() => {
        return (
            loading === false &&
            [AccountQualifyStatus.OrgPrimePass, AccountQualifyStatus.OrgSeniorPass].includes(account.qualifyStatus)
        );
    }, [loading, account.qualifyStatus]);
    const orgType = useMemo(() => {
        // 认证信息不允许修改的时候或者未传type的时候，默认使用接口数据的type
        if (
            (orgSubmitInfo?.entityStatus &&
                [QualifyStatus.AUDIT, QualifyStatus.PASS].includes(orgSubmitInfo?.entityStatus)) ||
            !type
        ) {
            return orgSubmitInfo?.customerType || AccountQualifyType.Enterprise;
        } else {
            // 其他可修改认证信息的状态，以用户指定的type为准
            return type && ORG_TYPE_MAP[type.toLocaleLowerCase()]
                ? ORG_TYPE_MAP[type.toLocaleLowerCase()]
                : AccountQualifyType.Enterprise;
        }
    }, [type, orgSubmitInfo]);
    const isSyncFromBjh = useMemo(() => {
        const isOrgPass = account?.customerInfo?.entityStatus === QualifyStatus.PASS;
        if (isOrgPass) {
            const orgPicPath = ['enterpriseInfo', 'enterpriseLicense'];
            const orgNamePath = ['enterpriseInfo', 'enterpriseName'];
            const orgCodePath = ['enterpriseInfo', 'enterpriseCode'];
            const license = get(orgSubmitInfo || {}, orgPicPath);
            const name = get(orgSubmitInfo || {}, orgNamePath);
            const code = get(orgSubmitInfo || {}, orgCodePath);
            // 认证通过且从百家号同步过来的数据只有机构名称和证件号
            return !license && name && code;
        }
    }, [account, orgSubmitInfo]);
    const phoneValue = Form.useWatch(['submitterPhone'], form);

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    const onFieldsChange = useCallback(async () => {
        // 判断是否有字段被修改，并且是否存在校验错误
        const values = form.getFieldsValue();
        setSubDisabled(!checkOrgSubmitFormValue(values, canOperatorEdit));
    }, [form, canOperatorEdit]);

    const showReject = useMemo(() => {
        return orgSubmitInfo?.entityStatus === QualifyStatus.RETURN && orgSubmitInfo.customerType === orgType;
    }, [orgType, orgSubmitInfo]);

    useEffect(() => {
        // 审核驳回之后，机构类型和上次驳回类型不同的不回填
        const noRefill = orgSubmitInfo?.customerType && orgSubmitInfo?.customerType !== orgType;
        const update = noRefill
            ? {
                  customerId: orgSubmitInfo?.customerId,
                  customerType: orgType || orgSubmitInfo?.customerType || AccountQualifyType.Enterprise,
              }
            : {
                  enterpriseInfo: orgSubmitInfo?.enterpriseInfo,
                  governmentInfo: orgSubmitInfo?.governmentInfo,
                  publicInstitutionInfo: orgSubmitInfo?.publicInstitutionInfo,
                  otherOrgInfo: orgSubmitInfo?.otherOrgInfo,
                  submitterName: orgSubmitInfo?.submitterName,
                  // 可编辑态不回写脱敏的数据
                  submitterPhone: canEdit ? undefined : orgSubmitInfo?.submitterPhone,
                  submitterIdCardNumber: canEdit ? undefined : orgSubmitInfo?.submitterIdCardNumber,
                  customerId: orgSubmitInfo?.customerId,
                  customerType: orgType || orgSubmitInfo?.customerType || AccountQualifyType.Enterprise,
              };
        form.setFieldsValue(update);
        // 可编辑态不回写脱敏的数据 所以提交按钮disabled默认为true
        setSubDisabled(true);
    }, [canEdit, canOperatorEdit, orgType, account?.customerInfo?.customerId, orgSubmitInfo, form]);

    const onBack = useCallback(
        (action: string) => {
            return () => {
                modal.confirm({
                    title: '确认离开当前页面？',
                    content: '退出后未提交的信息将不会保存',
                    width: 400,
                    icon: null,
                    centered: true,
                    onOk: () => navigate(action),
                });
            };
        },
        [modal, navigate]
    );

    const onCompanyCheck = useCallback(
        (params: OrgSubmitParams | OrgUpdateParams) => {
            if (params.customerType === AccountQualifyType.Enterprise && params.enterpriseInfo) {
                const {enterpriseInfo} = params;
                return accountApi
                    .checkOrgAvailable({
                        companyCode: enterpriseInfo.enterpriseCode,
                        companyName: enterpriseInfo.enterpriseName,
                    })
                    .then(check => {
                        if (!check.pass) {
                            messageApi.open({
                                type: 'error',
                                content: check.errMsg,
                                duration: 3,
                            });
                        }
                        return check.pass;
                    })
                    .catch(() => true);
            } else {
                return Promise.resolve(true);
            }
        },
        [messageApi]
    );

    const onEditSubmit = useCallback(
        async (res: any) => {
            if (res.phoneCode) {
                await smsApi.openLeadsPlaintext({
                    phone: res.submitterPhone,
                    verifyCode: res.phoneCode,
                });
            }

            try {
                const params = getEditParams(res);
                await accountApi.editOrgInfo(params as OrgSubmitParams);
                messageApi.open({
                    type: 'success',
                    content: '已保存',
                });
                await loadOrgSubmitInfo?.();
                setCanOperatorEdit(false);
            } catch (error) {
                messageApi.open({
                    type: 'error',
                    content: '保存失败',
                });
            }
        },
        [messageApi, loadOrgSubmitInfo]
    );

    const onSubmit = useCallback(() => {
        return form.validateFields().then(
            async res => {
                setLoading(true);
                try {
                    // 修改开发者信息时，直接提交
                    if (canOperatorEdit) {
                        await onEditSubmit(res);
                        return;
                    }

                    const isVerify = await checkPhoneVerify(res.submitterPhone)();
                    setIsVerify(isVerify);
                    if (isVerify) {
                        if (!res.phoneCode) {
                            messageApi.open({
                                type: 'error',
                                content: '请发送验证码',
                            });
                            return;
                        }

                        await smsApi.openLeadsPlaintext({
                            phone: res.submitterPhone,
                            verifyCode: res.phoneCode,
                        });
                    }

                    const params = getSubmitParams(res);
                    const check = await onCompanyCheck(params);
                    if (!check) {
                        return;
                    }

                    if (res.customerId) {
                        await accountApi.updateOrgInfo(params as OrgUpdateParams);
                    } else {
                        await accountApi.submitOrgInfo(params);
                    }

                    loadAccountInfo?.().then(res => {
                        if (res.qualifyStatus === AccountQualifyStatus.OrgPrimeAudit) {
                            messageApi
                                .open({
                                    type: 'success',
                                    content: (
                                        <CountdownText
                                            countdownLimit={1}
                                            invoke
                                            // eslint-disable-next-line react/jsx-no-bind
                                            countdownLabel={(time: number) =>
                                                `提交成功，请耐心等待审核，${time}s后自动跳转账号中心`
                                            }
                                            label="即将跳转"
                                        />
                                    ),
                                    duration: 1,
                                })
                                .then(() => navigate(urls.account.raw()));
                        }
                    });
                } finally {
                    setLoading(false);
                }
            },
            error => {
                const name = error?.errorFields?.[0]?.name?.[0];
                const labels = document.getElementsByClassName('ant-form-item-required');
                const item = Array.from(labels)?.find((item: any) => item.htmlFor === name);
                item?.scrollIntoView({behavior: 'smooth', block: 'center', inline: 'nearest'});
            }
        );
    }, [canOperatorEdit, loadAccountInfo, onCompanyCheck, navigate, messageApi, form, onEditSubmit]);

    const onCodeSend = useCallback(() => {
        setTimeout(() => onFieldsChange());
    }, [onFieldsChange]);

    const onOperatorEditChange = useCallback((val: boolean) => {
        setCanOperatorEdit(val);
    }, []);

    return (
        <AccountStyleProvider>
            {contextHolder}
            {modalContext}
            <div>
                <div className="fixed z-10 w-full">
                    <Header />
                </div>
                <div className="flex w-full flex-col justify-center overflow-y-scroll px-[55px] pb-[36px] pt-[106px]">
                    <LingJingSteps
                        className={classNames('mx-auto mb-[36px]', hideAuth(orgType) ? 'w-[408px]' : 'w-[720px]')}
                        current={1}
                        items={hideAuth(orgType) ? GOV_STEPS_ITEMS : ENTERPRISE_STEPS_ITEMS}
                    />
                    <div className="flex justify-center">
                        <Card className="w-[906px]">
                            <div className="overflow-x-hidden">
                                <Form
                                    className={styledForm}
                                    colon={false}
                                    form={form}
                                    labelWrap
                                    labelAlign="left"
                                    labelCol={{span: 4}}
                                    wrapperCol={{span: 11}}
                                    requiredMark={RequiredMark}
                                    scrollToFirstError
                                    onFieldsChange={onFieldsChange}
                                    onValuesChange={onFieldsChange}
                                >
                                    {showReject && (
                                        <Alert
                                            className="mb-8"
                                            type="error"
                                            icon={<ExclamationCircleFilled />}
                                            showIcon
                                            message="审核未通过"
                                            description={
                                                <span className="whitespace-pre-wrap">
                                                    {orgSubmitInfo?.entityAuditMsg || '请重新修改后提交'}
                                                </span>
                                            }
                                        />
                                    )}
                                    {AccountQualifyType.Gov === orgType ? (
                                        <GovForm disabled={!canEdit} />
                                    ) : (
                                        <EnterpriseForm
                                            type={orgType}
                                            form={form}
                                            disabled={!canEdit}
                                            isSyncFromBjh={isSyncFromBjh}
                                        />
                                    )}
                                    <OperatorForm
                                        showEditOperator={showEditOperator}
                                        canModifyToday={orgSubmitInfo?.canModifyToday}
                                        disabled={!canEdit && !canOperatorEdit}
                                        form={form}
                                        isVerify={isVerify}
                                        onCodeSend={onCodeSend}
                                        checkPhoneVerify={checkPhoneVerify}
                                        onEditChange={onOperatorEditChange}
                                    />
                                    <Item label="机构类型" name={['customerType']} hidden preserve>
                                        <Input />
                                    </Item>
                                    <Item label="认证记录" name={['customerId']} hidden preserve>
                                        <Input />
                                    </Item>
                                    {(canEdit || canOperatorEdit) && (
                                        <OrgSubmitFooter
                                            styledAgreementFormItem={styledAgreementFormItem}
                                            submitLoading={submitLoading}
                                            onBack={onBack}
                                            urls={urls}
                                            subDisabled={subDisabled}
                                            onSubmit={onSubmit}
                                            DICTS={DICTS}
                                            showPhoneChangeConfirm={
                                                canOperatorEdit && phoneValue !== orgSubmitInfo?.submitterPhone
                                            }
                                        />
                                    )}
                                </Form>
                            </div>
                        </Card>
                        {orgType !== AccountQualifyType.Gov && <Sample className="ml-[24px]" type={orgType} />}
                    </div>
                </div>
            </div>
        </AccountStyleProvider>
    );
};

export default function OrgSubmitPc() {
    return (
        <CacheProvider>
            <CommonErrorBoundary>
                <LogContextProvider page={EVENT_PAGE_CONST.ACCOUNT_ENTERPRISE} ext={{eCertificationStep: 2}}>
                    <OrgSubmit />
                </LogContextProvider>
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
