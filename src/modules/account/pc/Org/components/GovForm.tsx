import {Form, Input} from 'antd';
import {FileType} from '@/api/auditManagement/interface';
import ImageTypeUpload from './ImageUpload';

const {Item} = Form;

interface GovFormProps {
    disabled: boolean;
}

const GovForm = ({disabled}: GovFormProps) => {
    return (
        <div>
            <h1 className="mb-5 text-base font-medium">政府信息</h1>
            <Item
                required
                label="机构名称"
                name={['governmentInfo', 'governmentName']}
                rules={[
                    {required: true, message: '请输入机构名称名称'},
                    {message: '不超过40个字符', max: 40},
                ]}
            >
                <Input placeholder="请输入机构名称" disabled={disabled} />
            </Item>
            <Item
                required
                label="确认公函"
                name={['governmentInfo', 'applicationLetter']}
                rules={[{required: true, message: '请上传确认公函'}]}
            >
                <ImageTypeUpload
                    deletable={!disabled}
                    preTip={
                        <div className="mb-3 text-gray-tertiary">
                            为确保入驻的主体真实有效、组织知情、合法经营，请下载并填写
                            <a href="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/applicationLetter3.0.docx">
                                《入驻申请函》
                            </a>
                            （加盖公章）
                        </div>
                    }
                    fileType={FileType.applicationLetter}
                />
            </Item>
            <Item label="补充证明材料" name={['governmentInfo', 'evidenceUrl']}>
                <ImageTypeUpload maxCount={5} fileType={FileType.evidence} deletable={!disabled} />
            </Item>
        </div>
    );
};

export default GovForm;
