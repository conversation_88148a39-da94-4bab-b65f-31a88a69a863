import {FC, PropsWith<PERSON><PERSON>dren, ReactN<PERSON>, useCallback, useEffect, useState} from 'react';
import {Image, Upload, message} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {UploadListType} from 'antd/es/upload/interface';
import {UploadFile, UploadChangeParam, RcFile} from 'antd/lib/upload/interface';
import api from '@/api/auditManagement';
import {FileType, UploadFileRes} from '@/api/auditManagement/interface';
import {getBase64} from '@/utils/image';

const DEFAULT_MAX_SIZE = 20 * 1024;

const StyledUpload = styled(Upload)`
    &.ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-container {
        height: 80px;
        width: 80px;
        margin-block: ${(props: any) => {
            return !props.value || props.value?.length <= 1 ? 0 : '8px';
        }};
    }
    &.ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-thumbnail
        img {
        object-fit: cover;
        border-radius: 8px;
    }

    &.ant-upload-wrapper.ant-upload-picture-card-wrapper
        .ant-upload-list.ant-upload-list-picture-card
        .ant-upload-list-item-progress {
        bottom: 24px;
    }

    &.ant-upload-wrapper.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select {
        margin-bottom: 0;
    }
`;
interface ImageUploadProp {
    fileType: FileType;
    accept?: string;
    value?: string | string[];
    deletable?: boolean;
    className?: any;
    // 图片大小限制，以K为单位
    size?: number;
    preTip?: ReactNode;
    maxCount?: number;
    onChange?: (value: string | string[]) => void;
    onAfterUploadSuccess?: (params: {url: string; file: File}) => void;
    // 单位K
    minSize?: number;
    minHeight?: number;
    minWidth?: number;
    listType?: UploadListType;
    id?: any;
}

async function checkFileLimit(params: {
    file: RcFile;
    // 单位K
    size?: number;
    minSize?: number;
    minHeight?: number;
    minWidth?: number;
}) {
    const {file, size, minSize, minHeight, minWidth} = params;
    if (size && file.size > (size || DEFAULT_MAX_SIZE) * 1024) {
        const sizeMsg = size < 1024 ? `请上传小于${size}K的图片` : `请上传小于${size / 1024}M的图片`;
        message.open({
            type: 'error',
            content: sizeMsg,
        });
        return Upload.LIST_IGNORE;
    }

    if (minSize && file.size < 1024 * minSize) {
        message.open({
            type: 'error',
            content: `请上传大于${minSize}K的图片`,
        });
        return Upload.LIST_IGNORE;
    }

    if (minHeight && minWidth) {
        try {
            const base64Url = await getBase64(file);
            return new Promise<boolean | typeof Upload.LIST_IGNORE>(resolve => {
                const img = new HTMLImageElement();
                if (img) {
                    img.src = base64Url;
                    img.onload = () => {
                        if (img.width * img.height < minHeight * minWidth) {
                            message.open({
                                type: 'error',
                                content: `请上传分辨率大于${minHeight}*${minWidth}的文件`,
                            });
                            resolve(Upload.LIST_IGNORE);
                        } else {
                            resolve(true);
                        }
                    };

                    img.onerror = () => resolve(Upload.LIST_IGNORE);
                } else {
                    resolve(Upload.LIST_IGNORE);
                }
            });
        } catch {
            return Upload.LIST_IGNORE;
        }
    }
    return true;
}

const ImageTypeUpload: FC<PropsWithChildren<ImageUploadProp>> = props => {
    const [display, setDisplay] = useState<UploadFile[]>([]);
    const [showPreview, setShowPreview] = useState<boolean>(false);
    const [previewFile, setPreviewFile] = useState<any>();
    const {
        fileType,
        deletable,
        size = DEFAULT_MAX_SIZE,
        value,
        accept = 'image/jpeg,image/jpg,image/png,image/bmp',
        listType = 'picture-card',
        onAfterUploadSuccess,
        minSize,
        minHeight,
        minWidth,
        maxCount,
        preTip = null,
        id,
    } = props;

    const beforeUpload = useCallback(
        async (file: RcFile) => {
            const check = size ? await checkFileLimit({file, size, minSize, minHeight, minWidth}) : true;
            return check;
        },
        [size, minSize, minHeight, minWidth]
    );

    const uploadFile = useCallback(
        (event: any) => {
            const {onSuccess, onError, file, onProgress} = event;
            onProgress({
                percent: 30,
            });
            return api
                .uploadFileToBos({type: fileType, file}, undefined, {forbiddenToast: true})
                .then(({url}: UploadFileRes) => {
                    onProgress({
                        percent: 100,
                    });
                    onSuccess(url);
                    onAfterUploadSuccess && onAfterUploadSuccess({url, file});
                    return url;
                })
                .catch(error => {
                    message.open({
                        type: 'error',
                        content: error.msg || '上传失败，请重试！',
                    });
                    onError(error);
                    return Upload.LIST_IGNORE;
                });
        },
        [fileType, onAfterUploadSuccess]
    );

    const onChange = useCallback(
        (e: UploadChangeParam<UploadFile<any>>) => {
            setDisplay(e.fileList);
            if (e.fileList.every(cur => cur.status?.toLowerCase() === 'done')) {
                const value = e ? e.fileList.map(cur => cur?.response || cur.uid) : [];
                const limitOne = !maxCount || maxCount === 1;
                props.onChange && props.onChange(limitOne ? value?.[0] || '' : value);
            }
        },
        [maxCount, props]
    );

    const handlePreview = useCallback(async (file: any) => {
        if (file?.url) {
            setPreviewFile(file.url);
            setShowPreview(true);
        }
    }, []);

    useEffect(() => {
        const display: UploadFile[] =
            (Array.isArray(value) ? value : value ? [value] : [])?.map(cur => {
                return {
                    url: cur,
                    status: 'done',
                    name: cur,
                    uid: cur,
                } as UploadFile;
            }) || [];
        setDisplay(display);
    }, [value]);

    return (
        <div id={id}>
            {preTip}
            <StyledUpload
                accept={accept}
                maxCount={props.maxCount || 1}
                fileList={display}
                disabled={!deletable}
                openFileDialogOnClick={deletable === undefined ? true : deletable}
                listType={listType}
                onChange={onChange}
                customRequest={uploadFile}
                onPreview={handlePreview}
                beforeUpload={beforeUpload as any}
            >
                {display.length >= (props.maxCount || 1) ? null : props.children ? (
                    props.children
                ) : (
                    <div>
                        <PlusOutlined />
                    </div>
                )}
            </StyledUpload>
            {showPreview && (
                <Image
                    className="object-cover"
                    wrapperStyle={{display: 'none'}}
                    preview={{
                        visible: showPreview,
                        onVisibleChange: visible => setShowPreview(visible),
                        afterOpenChange: visible => !visible && setShowPreview(false),
                    }}
                    src={previewFile}
                />
            )}
        </div>
    );
};

export default ImageTypeUpload;
