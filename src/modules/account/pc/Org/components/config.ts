import {NamePath} from 'antd/es/form/interface';
import {
    AccountQualifyType,
    PersonQualifyStatus,
    QualifyStatus,
    EnterpriseType,
    CustomerAuthStatus,
} from '@/api/account/interface';
import {
    CareerAuthType,
    CardStatusTagConfig,
    OriginalCareerAuthStatus,
    AuthCardConfigProps,
    BaseAuthCardTitles,
} from '@/modules/account/pc/Person/interface';

export const ENTERPRISE_STEPS_ITEMS = [{title: '选择类型'}, {title: '主体信息'}, {title: '真实性认证'}];

export const GOV_STEPS_ITEMS = [{title: '选择类型'}, {title: '主体信息'}];

/** 主体类型映射 主要用于 机构/企业类型选择时携带在query中 */
export const CUSTOMER_TYPE_MAP = {
    [AccountQualifyType.Person]: 'person',
    [AccountQualifyType.Enterprise]: 'company',
    [AccountQualifyType.Gov]: 'gov',
    [AccountQualifyType.Department]: 'department',
    [AccountQualifyType.Other]: 'other',
};

export const ORG_TIP_TEXTS =
    '进行组织机构认证前请务必确认本账号为机构公用账号，请不要使用私人账号进行认证。一旦认证成机构账号，本账号及账号下所有历史及未来资产均属于机构。 如使用私人账号进行认证，若产生账号及资产归属纠纷可能使您的私人账号和/或机构账号无法正常使用，后续造成的任何损失由您及机构各自进行承担。';

interface OrganizationType {
    title: string;
    description: string;
    imgUrl: string;
    materials: string[];
}

export const COMPANY_IMG = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/company.png';

export const ORG_IMG = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/government.png';

export const TEAM_IMG = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/department.png';

export const ORG_TEAM_IMG =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/other_organization.png';

export const organizationInfos: OrganizationType[] = [
    {
        title: '企业',
        description: '适合企业、公司、分支机构、个体工商户等机构类型',
        materials: ['企业营业执照', '开发者身份信息'],
        imgUrl: COMPANY_IMG,
    },
    {
        title: '政府',
        description: '适用于国家党、政机关及直属单位等机构类型',
        materials: ['政府确认公函', '开发者身份信息'],
        imgUrl: ORG_IMG,
    },
    {
        title: '事业单位',
        description: '适用于公立学校、科研院所等机构类型',
        materials: ['事业单位法人证书', '开发者身份信息'],
        imgUrl: TEAM_IMG,
    },
    {
        title: '其他组织',
        description: '适合各类公益机构、社团、民间组织、公共场馆等非公立机构团体',
        materials: ['组织机构代码证', '开发者身份信息'],
        imgUrl: ORG_TEAM_IMG,
    },
];

export const PERSON_AUTH_STATUS_TAG_CONFIG: Record<number, CardStatusTagConfig> = {
    [PersonQualifyStatus.NONE]: {
        statusTag: '未认证',
        color: '#848691',
        bgColor: '#8486911A',
    },
    [PersonQualifyStatus.PASS]: {
        statusTag: '已认证',
        color: '#3FC746',
        bgColor: '#3FC7461A',
    },
};

export const CAREER_CARD_TITLES: {[key in CareerAuthType]: string} = {
    [CareerAuthType.Default]: '',
    [CareerAuthType.Career]: '身份职业认证',
    [CareerAuthType.Doctor]: '医生认证',
    [CareerAuthType.Lawyer]: '律师认证',
};

/** 认证状态标签配置 */
export const CERTIFICATION_STATUS_TAG_CONFIG: Record<number | string, CardStatusTagConfig> = {
    [QualifyStatus.NONE]: {
        statusTag: '未认证',
        color: '#848691',
        bgColor: '#8486911A',
    },
    [QualifyStatus.AUDIT]: {
        statusTag: '认证中',
        color: '#5562F2',
        bgColor: '#5562F21A',
    },
    [QualifyStatus.PASS]: {
        statusTag: '已认证',
        color: '#3FC746',
        bgColor: '#3FC7461A',
    },
    [QualifyStatus.RETURN]: {
        statusTag: '认证失败',
        color: '#FF3333',
        bgColor: '#FEEDF1',
    },
    [CustomerAuthStatus.ACCESS]: {
        statusTag: '未认证',
        color: '#848691',
        bgColor: '#8486911A',
    },
    // 新增状态
    [`${QualifyStatus.PASS}-${OriginalCareerAuthStatus.Examine}`]: {
        statusTag: '已认证：资料即将过期，请尽快更新',
        color: '#3FC746',
        bgColor: '#3FC7461A',
    },
};

export const BUTTON_TEXT_MAP: Record<QualifyStatus | CustomerAuthStatus, string> = {
    [QualifyStatus.NONE]: '立即认证',
    [QualifyStatus.AUDIT]: '继续认证',
    [QualifyStatus.PASS]: '去查看',
    [QualifyStatus.RETURN]: '修改',
    [CustomerAuthStatus.ACCESS]: '立即认证',
};

// 认证卡片的icon地址
export const SUBJECT_IMG =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/subject.png';

export const LAWYER_IMG = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/lawyer.png';

export const DOCTOR_IMG = 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/doctor.png';

export const REALNAME_IMG =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/realName.png';

export const AUTH_IMG =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/authenticity.png';

export const IDENTITY_IMG =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/account/card-icon/identity-occupation.png';

/**
 * 个人--实名认证&&真实性认证卡片配置信息
 */

export const PERSON_CARD_CONFIGS: Record<string, AuthCardConfigProps> = {
    [BaseAuthCardTitles.RealName]: {
        title: BaseAuthCardTitles.RealName,
        description: '完成身份信息校验享受更多开发者的权益。',
        icon: REALNAME_IMG,
    },
    [BaseAuthCardTitles.Authenticity]: {
        title: BaseAuthCardTitles.Authenticity,
        description: '完成真实身份信息确认享受更多开发者的权益。',
        icon: AUTH_IMG,
    },
    [BaseAuthCardTitles.Subject]: {
        title: BaseAuthCardTitles.Subject,
        description: '完成企业主身份信息校验，享受更多权益。',
        icon: SUBJECT_IMG,
    },
    [`${EnterpriseType.personBusiness}-${BaseAuthCardTitles.Authenticity}`]: {
        title: BaseAuthCardTitles.Authenticity,
        description: '完成企业主真实身份信息确认，享受更多权益。',
        icon: AUTH_IMG,
    },
};

/**
 * 个人--职业认证卡片配置信息
 */
export const CAREER_CARD_CONFIGS: Record<number, AuthCardConfigProps> = {
    [CareerAuthType.Career]: {
        title: CAREER_CARD_TITLES[CareerAuthType.Career],
        description: '适用于具有正当职业或荣誉身份的开发者，如医生等。',
        buttonText: '扫码认证',
        icon: IDENTITY_IMG,
    },
    [CareerAuthType.Doctor]: {
        title: CAREER_CARD_TITLES[CareerAuthType.Doctor],
        description: '发布医疗类智能体，需先完成医生认证审核。',
        buttonText: '立即认证',
        icon: DOCTOR_IMG,
    },
    [CareerAuthType.Lawyer]: {
        title: CAREER_CARD_TITLES[CareerAuthType.Lawyer],
        description: '发布法律类智能体，需先完成律师认证审核。',
        buttonText: '立即认证',
        icon: LAWYER_IMG,
    },
};

// 身份职业认证地址，用于二维码生成，type: 0 原流程，1修改流程(已经审核通过，重新修改)
export const CAREER_AUTH_QRCODE = 'https://auth.baidu.com/authplus/career?from=wenxinagent&type=0';

// 医生认证页面
export const DOCTOR_AUTH_URL = 'https://auth.baidu.com/vmagic-mobile#/hmedical/auth-trade';

// 律师认证页面
export const LAWYER_AUTH_URL = 'https://lvlin.baidu.com/fp/account/index/filldata?fr=pingtai';

export const PASS_AUTH_HOST = `${process.env.PASS_HOST}/v6/realName?tpl=wise&u=${encodeURIComponent(
    window.location.href
)}`;

export const ORG_LABEL_MAP: Partial<
    Record<
        AccountQualifyType,
        {
            enterpriseName: string;
            enterpriseLicense: string;
            enterpriseCode: string;
            namePathKey: string;
            namePathFields: {
                enterpriseName: NamePath;
                enterpriseCode: NamePath;
                enterpriseLicense: NamePath;
            };
        }
    >
> = {
    [AccountQualifyType.Enterprise]: {
        enterpriseName: '企业',
        enterpriseLicense: '营业执照影印件',
        enterpriseCode: '统一社会信用代码',
        namePathKey: 'enterpriseInfo',
        namePathFields: {
            enterpriseName: ['enterpriseInfo', 'enterpriseName'],
            enterpriseCode: ['enterpriseInfo', 'enterpriseCode'],
            enterpriseLicense: ['enterpriseInfo', 'enterpriseLicense'],
        },
    },
    [AccountQualifyType.Department]: {
        enterpriseName: '事业单位',
        enterpriseLicense: '事业单位法人证书',
        enterpriseCode: '统一社会信用代码',
        namePathKey: 'publicInstitutionInfo',
        namePathFields: {
            enterpriseName: ['publicInstitutionInfo', 'institutionName'],
            enterpriseCode: ['publicInstitutionInfo', 'institutionCode'],
            enterpriseLicense: ['publicInstitutionInfo', 'institutionLicense'],
        },
    },
    [AccountQualifyType.Other]: {
        enterpriseName: '组织',
        enterpriseLicense: '组织机构代码证',
        enterpriseCode: '统一社会信用代码',
        namePathKey: 'otherOrgInfo',
        namePathFields: {
            enterpriseName: ['otherOrgInfo', 'enterpriseName'],
            enterpriseCode: ['otherOrgInfo', 'enterpriseCode'],
            enterpriseLicense: ['otherOrgInfo', 'enterpriseLicense'],
        },
    },
};

export const ORG_TYPE_MAP: {
    [key: string]: AccountQualifyType;
} = {
    company: AccountQualifyType.Enterprise,
    gov: AccountQualifyType.Gov,
    department: AccountQualifyType.Department,
    other: AccountQualifyType.Other,
};

export const ORG_KEY_TO_TYPE: Partial<Record<AccountQualifyType, string>> = {
    [AccountQualifyType.Enterprise]: 'company',
    [AccountQualifyType.Gov]: 'gov',
    [AccountQualifyType.Department]: 'department',
    [AccountQualifyType.Other]: 'other',
};
