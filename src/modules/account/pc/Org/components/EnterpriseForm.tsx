import {Form, Input, FormInstance, Row, Col, message} from 'antd';
import get from 'lodash/get';
import dayjs from 'dayjs';
import {Fragment, useCallback, useEffect, useMemo, useState} from 'react';
import {NamePath} from 'antd/es/form/interface';
import {AccountQualifyType} from '@/api/account/interface';
import auditApi from '@/api/auditManagement';
import {FileType, ImageType} from '@/api/auditManagement/interface';
import {validateCompNumber, validateDepartName} from '@/utils/formValidator';
import ImageTypeUpload from './ImageUpload';
import CertificateStart from './CertificateStart';
import CertificateExpire from './CertificateExpire';
import {ORG_LABEL_MAP} from './config';
const {Item} = Form;

interface EnterpriseFormProps {
    type: AccountQualifyType;
    form: FormInstance;
    disabled?: boolean;
    isSyncFromBjh?: boolean;
}

// 避免 dynamic namePath for Form Error https://github.com/ant-design/ant-design/discussions/45041
const useDynamicWatch = (namePath: NamePath[], form: FormInstance) => {
    const root = Form.useWatch([], form);
    return get(root, namePath);
};

const EnterpriseForm = (props: EnterpriseFormProps) => {
    const [showOcrFields, setShowOcrFields] = useState<boolean>();
    const {form, type, disabled, isSyncFromBjh} = props;
    const typeParams = useMemo(() => {
        return {
            fileType: type === AccountQualifyType.Department ? FileType.institutionLicense : FileType.enterpriseLicense,
            label: ORG_LABEL_MAP[type] || ORG_LABEL_MAP[AccountQualifyType.Enterprise],
            showMainType: [AccountQualifyType.Enterprise].includes(type),
            ocrUpdateKey: ORG_LABEL_MAP[type]?.namePathKey || 'enterpriseInfo',
        };
    }, [type]);
    const {fileType, label, showMainType, ocrUpdateKey} = typeParams;
    const enterpriseLicense = useDynamicWatch(label!.namePathFields.enterpriseLicense, form);

    useEffect(() => {
        enterpriseLicense && setShowOcrFields(true);
    }, [enterpriseLicense]);

    const handleImageOcr = useCallback(
        async ({url}: {url: string}) => {
            const ocrType =
                fileType === FileType.institutionLicense ? ImageType.institutionLicense : ImageType.enterpriseLicense;
            message.open({
                type: 'loading',
                content: '自动识别证件信息中...',
            });
            auditApi
                .recognizeImage(
                    {
                        type: ocrType,
                        url,
                    },
                    {timeout: 30 * 1000}
                )
                .then(result => {
                    if (!result) {
                        return;
                    }

                    let updateValue: any = {};
                    const updateKey = ocrUpdateKey;
                    switch (fileType) {
                        case FileType.enterpriseLicense:
                            updateValue = {
                                enterpriseName: result.ocrInfo?.enterpriseName,
                                enterpriseCode: result.ocrInfo?.enterpriseCode,
                                licenseStartTime: result.ocrInfo?.startDate,
                                licenseEndTime: result.ocrInfo?.endDate,
                                orgType: result.ocrInfo?.orgType,
                            };
                            break;
                        case FileType.institutionLicense:
                            updateValue = {
                                enterpriseName: result.ocrInfo?.institutionName,
                                enterpriseCode: result.ocrInfo?.institutionCode,
                                licenseStartTime: result.ocrInfo?.startDate,
                                licenseEndTime: result.ocrInfo?.endDate,
                            };
                            break;
                        default:
                            break;
                    }

                    updateValue.enterpriseName &&
                        form.setFieldValue(label!.namePathFields.enterpriseName, updateValue.enterpriseName);
                    updateValue.enterpriseCode &&
                        form.setFieldValue(label!.namePathFields.enterpriseCode, updateValue.enterpriseCode);
                    updateValue.licenseStartTime &&
                        form.setFieldValue([updateKey, 'licenseStartTime'], updateValue.licenseStartTime);
                    updateValue.licenseEndTime &&
                        form.setFieldValue([updateKey, 'licenseEndTime'], updateValue.licenseEndTime);
                    updateValue.orgType && form.setFieldValue([updateKey, 'orgType'], updateValue.orgType);
                })
                .finally(() => {
                    message.destroy();
                    setShowOcrFields(true);
                });
        },
        [fileType, ocrUpdateKey, label, form]
    );
    // 百家号同步的企业信息只展示企业名称和证件号
    if (isSyncFromBjh) {
        <div>
            <h1 className="mb-5 text-base font-medium">{label!.enterpriseName || '企业'}信息</h1>
            <Item
                required
                label={`${label!.enterpriseName}名称`}
                name={label!.namePathFields.enterpriseName}
                rules={[
                    {required: true, message: `请输入${label!.enterpriseName}名称`},
                    {
                        validateTrigger: ['onBlur'],
                        validator: validateDepartName(`请输入有效的${label!.enterpriseName}`),
                    },
                ]}
            >
                <Input placeholder={`请输入${label!.enterpriseName || ''}名称`} disabled={disabled} />
            </Item>
            <Item
                required
                label={label!.enterpriseCode}
                name={label!.namePathFields.enterpriseCode}
                rules={[
                    {required: true, message: `请输入${label!.enterpriseCode}`},
                    {
                        validateTrigger: ['onBlur'],
                        validator: validateCompNumber(type),
                    },
                ]}
            >
                <Input placeholder={`请输入${label!.enterpriseCode || ''}`} disabled={disabled} />
            </Item>
        </div>;
    }

    return (
        <div>
            <h1 className="mb-5 text-base font-medium">{label!.enterpriseName || '企业'}信息</h1>
            <Item
                required
                label={label!.enterpriseLicense}
                name={label!.namePathFields.enterpriseLicense}
                rules={[{required: true, message: `请上传${label!.enterpriseLicense}`}]}
            >
                <ImageTypeUpload
                    fileType={fileType}
                    onAfterUploadSuccess={handleImageOcr}
                    size={1 * 1024 * 1024}
                    deletable={!disabled}
                />
            </Item>
            {showOcrFields && (
                <Fragment>
                    <Item
                        required
                        label={`${label!.enterpriseName}名称`}
                        name={label!.namePathFields.enterpriseName}
                        rules={[
                            {required: true, message: `请输入${label!.enterpriseName}名称`},
                            {message: '不超过40个字符', max: 40},
                            {
                                validateTrigger: ['onBlur'],
                                validator: validateDepartName(`请输入有效的${label!.enterpriseName}`),
                            },
                        ]}
                    >
                        <Input
                            className="w-[400px]"
                            placeholder={`请输入${label!.enterpriseName || ''}名称`}
                            disabled={disabled}
                        />
                    </Item>
                    <Item
                        required
                        label={label!.enterpriseCode}
                        name={label!.namePathFields.enterpriseCode}
                        rules={[
                            {required: true, message: `请输入${label!.enterpriseCode}`},
                            {
                                validateTrigger: ['onBlur'],
                                validator: validateCompNumber(type),
                            },
                        ]}
                    >
                        <Input
                            className="w-[400px]"
                            placeholder={`请输入${label!.enterpriseCode || ''}`}
                            disabled={disabled}
                        />
                    </Item>
                    <Item
                        wrapperCol={{span: 24}}
                        required
                        label="证件有效期"
                        rules={[{required: true, message: '请输入证件有效期起止时间'}]}
                    >
                        <Row gutter={8} align="middle">
                            <Col key="org-licenseStartTime" span={7}>
                                <Item
                                    name={[label!.namePathKey, 'licenseStartTime']}
                                    rules={[{required: true, message: '请输入证件有效期开始时间'}]}
                                    noStyle
                                >
                                    <CertificateStart disabled={disabled} />
                                </Item>
                            </Col>
                            <Col key="org-licenseTime-range" span={1}>
                                至
                            </Col>
                            <Col key="org-licenseEndTime">
                                <Item
                                    name={[label!.namePathKey, 'licenseEndTime']}
                                    rules={[
                                        {required: true, message: '请输入证件有效期截止时间'},
                                        {
                                            validator: (rule: any, value: string) => {
                                                const oneMonthLater = dayjs().add(1, 'month');
                                                if (value && dayjs(value).isAfter(oneMonthLater)) {
                                                    return Promise.resolve();
                                                }
                                                return Promise.reject(new Error('证件有效期需要一个月以上'));
                                            },
                                        },
                                    ]}
                                    noStyle
                                >
                                    <CertificateExpire disabled={disabled} />
                                </Item>
                            </Col>
                        </Row>
                    </Item>
                    {showMainType && (
                        <Item
                            required
                            label="主体类型"
                            name={[label!.namePathKey, 'orgType']}
                            rules={[{required: true, message: '请输入主体类型，与营业执照类型一致'}]}
                        >
                            <Input
                                className="w-[400px]"
                                placeholder="请输入主体类型，与营业执照类型一致"
                                disabled={disabled}
                            />
                        </Item>
                    )}
                </Fragment>
            )}
            <Item label="补充证明材料" name={[label!.namePathKey, 'evidenceUrl']}>
                <ImageTypeUpload
                    fileType={FileType.evidence}
                    maxCount={5}
                    deletable={!disabled}
                    preTip={
                        <div className="mb-3 text-gray-tertiary">
                            {label!.enterpriseName}
                            授权书、商标证、特殊行业等辅助材料证明文件，支持png、bmp、jpeg、jpg格式，单个文件大小不超过20M
                        </div>
                    }
                />
            </Item>
        </div>
    );
};

export default EnterpriseForm;
