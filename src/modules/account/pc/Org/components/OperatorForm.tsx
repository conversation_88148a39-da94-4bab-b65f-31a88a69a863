import {Button, Form, FormInstance, Input, Tooltip} from 'antd';
import {useCallback, useMemo} from 'react';
import PhoneCodeFormGroup from '@/components/PhoneCodeFormGroup';
import api from '@/api/leadsData';
import {validateChineseIdNumber, validatePersonName} from '@/utils/formValidator';
import {validateIdNumberMasking, validatePhoneMasking} from './validator';

const {Item} = Form;

interface OperatorFormProps {
    disabled: boolean;
    form: FormInstance;
    isVerify: boolean | undefined;
    onCodeSend?: () => void;
    checkPhoneVerify: (phone: string) => () => Promise<boolean>;
    onEditChange: (val: boolean) => void;
    // 在认证审核通过之后展示编辑按钮
    showEditOperator: boolean;
    // 每天限制修改一次，当前是否允许修改认证信息
    canModifyToday?: boolean;
}

const sendAuthCode = (phone: string) => {
    return () => {
        return api.sendPhoneVerifyCode({
            phone,
        });
    };
};

const OperatorForm = ({
    disabled,
    form,
    isVerify,
    onCodeSend,
    checkPhoneVerify,
    onEditChange,
    showEditOperator,
    canModifyToday,
}: OperatorFormProps) => {
    const validateIdNumber = useMemo(() => {
        return showEditOperator ? validateIdNumberMasking() : validateChineseIdNumber();
    }, [showEditOperator]);

    const onClickEdit = useCallback(() => {
        onEditChange(true);
    }, [onEditChange]);

    return (
        <div className="mt-[32px]">
            <div className="mb-5 flex items-center text-base font-medium">
                <h1>开发者信息</h1>
                {!!showEditOperator && !!canModifyToday && (
                    <Button type="link" className="text-sm" onClick={onClickEdit}>
                        修改
                    </Button>
                )}
                {!!showEditOperator && !canModifyToday && (
                    <Tooltip title="每日只能修改1次，请明天再来修改">
                        <Button type="link" className="text-sm" disabled>
                            修改
                        </Button>
                    </Tooltip>
                )}
            </div>
            <Item
                required
                label="姓名"
                name={['submitterName']}
                validateTrigger={['onBlur']}
                rules={[
                    {required: true, message: '请输入开发者姓名'},
                    {message: '姓名不超过30个字符', max: 30},
                    {
                        validateTrigger: ['onBlur'],
                        validator: validatePersonName('请输入正确的开发者姓名'),
                    },
                ]}
            >
                <Input className="w-[320px]" placeholder="请输入开发者姓名" disabled={disabled} />
            </Item>
            <Item
                label="身份证"
                name={['submitterIdCardNumber']}
                validateTrigger={['onBlur']}
                rules={[
                    {required: true, message: '请输入开发者身份证件号码'},
                    {
                        validateTrigger: ['onBlur'],
                        validator: validateIdNumber,
                    },
                ]}
            >
                <Input className="w-[320px]" placeholder="请输入开发者身份证件号码" disabled={disabled} />
            </Item>
            <PhoneCodeFormGroup
                phoneNamePath={['submitterPhone']}
                codeNamePath={['phoneCode']}
                checkPhoneVerify={checkPhoneVerify}
                sendAuthCode={sendAuthCode}
                hideVerifyCodeBtn={disabled}
                showCodeInputDefault={false}
                isVerify={isVerify}
                onCodeSendCB={onCodeSend}
                form={form}
                disabled={disabled}
                phoneValidator={showEditOperator ? validatePhoneMasking() : undefined}
            />
        </div>
    );
};

export default OperatorForm;
