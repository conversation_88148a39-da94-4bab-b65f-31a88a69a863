import {ConfigProvider} from 'antd';
import theme from '@/styles/lingjing-light-theme';

const AccountStyleProvider = (props: any) => {
    return (
        <ConfigProvider
            theme={{
                ...theme,
                components: {
                    ...theme.components,
                    Input: {
                        ...theme.components.Input,
                        colorBgContainer: theme.token.colorBgInput,
                        colorBorder: theme.token.colorBgInput,
                        paddingBlock: 8,
                        paddingInline: 12,
                        lineWidth: 0,
                        borderRadius: 9,
                        borderRadiusSM: 9,
                        borderRadiusOuter: 9,
                        colorBgContainerDisabled: theme.token.colorBgInput,
                        lineHeight: 1,
                    },
                    Upload: {
                        ...theme.components.Upload,
                        paddingXS: 0,
                        // 修改Picture Card大小
                        controlHeightLG: 31.5,
                        lineWidth: 0.5,
                        lineType: 'dashed',
                        colorBorder: theme.token.colorBorderDefaultHover,
                        // 背景色
                        colorFillAlter: theme.token.colorBgInput,
                    },
                    Form: {
                        ...theme.components.Form,
                        marginLG: 18,
                        lineHeight: 1.5,
                        colorTextBase: theme.token.colorTextBase,
                    },
                    DatePicker: {
                        ...theme.components.DatePicker,
                        colorBgContainer: theme.token.colorBgInput,
                        colorBgContainerDisabled: theme.token.colorBgInput,
                        colorSplit: theme.token.colorBgInput,
                        colorBorder: theme.token.colorBgInput,
                        paddingInline: 8,
                    },
                    Alert: {
                        ...theme.components.Alert,
                        paddingMD: 8,
                        paddingContentHorizontalLG: 12,
                        fontSizeHeading3: 16,
                        fontSizeLG: 14,
                        colorText: theme.token.colorTextTertiary,
                        marginXS: 6,
                        colorTextHeading: theme.token.colorTextDefault,
                    },
                    Modal: {
                        ...theme.components.Modal,
                        colorText: theme.token.colorTextBase,
                    },
                    Button: {
                        ...theme.components.Button,
                        borderRadius: 100,
                        defaultBorderColor: '#EDEEF0',
                        defaultShadow: 'none',
                        defaultColor: '#000311',
                        paddingBlock: 5,
                        paddingInline: 15,
                        contentFontSize: 14,
                        contentLineHeight: 1.****************,
                        fontWeight: 500,
                        colorPrimary: theme.token.colorPrimary,
                        colorPrimaryHover: '#3644D9',
                    },
                },
                token: {
                    ...theme.token,
                    lineWidthBold: 1,
                    fontFamily: 'PingFang SC',
                },
            }}
        >
            {props.children}
        </ConfigProvider>
    );
};

export default AccountStyleProvider;
