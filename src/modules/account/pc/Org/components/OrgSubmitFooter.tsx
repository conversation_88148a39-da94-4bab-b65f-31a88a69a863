import {ExclamationCircleFilled} from '@ant-design/icons';
import {Form, Checkbox, Flex, Space, Button, Popconfirm} from 'antd';
import classNames from 'classnames';
import React from 'react';

const {Item} = Form;

interface OrgSubmitFooterProps {
    styledAgreementFormItem: string;
    submitLoading?: boolean;
    onBack: (action: string) => () => void;
    urls: any;
    subDisabled?: boolean;
    onSubmit: () => void;
    DICTS: any;
    showPhoneChangeConfirm?: boolean;
}

const OrgSubmitFooter: React.FC<OrgSubmitFooterProps> = ({
    styledAgreementFormItem,
    submitLoading,
    onBack,
    urls,
    subDisabled,
    onSubmit,
    DICTS,
    showPhoneChangeConfirm,
}) => {
    return (
        <div>
            <Flex justify="end">
                <Item
                    className={classNames('justify-self-end', styledAgreementFormItem)}
                    labelCol={{span: 0}}
                    wrapperCol={{span: 24}}
                    name={['agreement']}
                    valuePropName="checked"
                    rules={[
                        {
                            validator: (rule: any, value: boolean) => {
                                if (!value) {
                                    return Promise.reject(Error('请同意协议'));
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    <Checkbox>
                        我已阅读并同意
                        <a target="_blank" className="mx-[4px]" href={DICTS.SERVICE_AGREEMENT} rel="noreferrer">
                            文心智能体平台协议
                        </a>
                    </Checkbox>
                </Item>
            </Flex>
            <Flex justify="end">
                <Space size={6}>
                    <Button shape="round" disabled={submitLoading} onClick={onBack(urls.accountOrgList.raw())}>
                        上一步
                    </Button>
                    <Button shape="round" disabled={submitLoading} onClick={onBack(urls.account.raw())}>
                        取消
                    </Button>
                    {showPhoneChangeConfirm ? (
                        <Popconfirm
                            title={
                                <div className="w-[300px]">
                                    确认更改信息么？手机号会影响线索组件等功能，请确认信息的修改
                                </div>
                            }
                            icon={<ExclamationCircleFilled className="text-orange-500" />}
                            placement="topRight"
                            trigger="click"
                            onConfirm={onSubmit}
                            okText="确认"
                            autoAdjustOverflow
                            cancelText="取消"
                            okButtonProps={{
                                shape: 'round',
                                size: 'middle',
                            }}
                            cancelButtonProps={{
                                shape: 'round',
                                size: 'middle',
                            }}
                        >
                            <Button shape="round" loading={submitLoading} disabled={subDisabled} type="primary">
                                提交
                            </Button>
                        </Popconfirm>
                    ) : (
                        <Button
                            shape="round"
                            loading={submitLoading}
                            disabled={subDisabled}
                            type="primary"
                            onClick={onSubmit}
                        >
                            提交
                        </Button>
                    )}
                </Space>
            </Flex>
        </div>
    );
};

export default OrgSubmitFooter;
