import {FC, useCallback, useEffect, useState} from 'react';
import {Checkbox, Flex, FormInstance, message} from 'antd';
import dayjs, {Dayjs} from 'dayjs';
import classNames from 'classnames';
import {StyledDatePicker} from './CertificateStart';

export interface CertificateExpireProps {
    onChange?: (val: string) => void;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    form?: FormInstance;
}

const modal: any = null;
const CertificateExpire: FC<CertificateExpireProps> = props => {
    const [display, setDisplay] = useState<Dayjs | null>(null);
    const [isLongTerm, setIsLongTerm] = useState<boolean>();
    const {value, onChange, disabled} = props;

    useEffect(() => {
        if (value === '9999-01-01') {
            setIsLongTerm(true);
            setDisplay(dayjs('9999-01-01'));
        } else {
            const display = value ? dayjs(value) : null;
            setDisplay(display);
            setIsLongTerm(false);
        }
    }, [value]);

    const selectDate = useCallback(
        (val: any) => {
            if (val && dayjs(val).isBefore(dayjs())) {
                message.open({
                    type: 'error',
                    content: '证件过期时间必须在今天之后',
                });
                return;
            }

            const value = val ? dayjs(val).format('YYYY-MM-DD') : '';
            onChange?.(value);
            modal?.close();
        },
        [onChange]
    );

    const selectLongTerm = useCallback(
        (val: any) => {
            const value = val.target.checked ? '9999-01-01' : '';
            onChange && onChange(value);
        },
        [onChange]
    );

    const disabledDate = useCallback((currentDate: any) => {
        return dayjs(currentDate).isBefore(dayjs());
    }, []);

    return (
        <div className="flex items-center justify-center">
            <Flex className="rounded-[9px] bg-colorBgFormList pl-[12px]" align="center">
                <div
                    className={classNames('mr-[4px] text-gray-tertiary', {
                        'opacity-60': isLongTerm ? true : disabled,
                    })}
                >
                    结束
                </div>
                <StyledDatePicker
                    style={{
                        padding: '6px 8px 6px',
                    }}
                    disabled={isLongTerm ? true : disabled}
                    value={display as any}
                    onChange={selectDate}
                    disabledDate={disabledDate}
                    placeholder="输入日期筛选"
                />
            </Flex>
            <Checkbox
                disabled={disabled}
                className="ml-2.5 w-fit text-center"
                checked={isLongTerm}
                onChange={selectLongTerm}
            >
                长期
            </Checkbox>
        </div>
    );
};

export default CertificateExpire;
