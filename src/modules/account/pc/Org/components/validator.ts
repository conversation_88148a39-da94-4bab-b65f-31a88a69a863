import has from 'lodash/has';
import smsApi from '@/api/leadsData';
import {AccountQualifyType, OrgSubmitParams, OrgUpdateParams} from '@/api/account/interface';
import {
    IdCardRegex,
    PERSON_NAME_CN_EN_REGEX,
    PHONE_REG,
    validateChineseIdNumber,
    validatePhone,
} from '@/utils/formValidator';

const checkSubmitterInfo = (formValue: any) => {
    const {submitterName, submitterPhone, submitterIdCardNumber, agreement} = formValue;
    const phoneCodeCheck = has(formValue, ['phoneCode']) && (!formValue.phoneCode || formValue.phoneCode.length !== 6);
    const checkName = submitterName && PERSON_NAME_CN_EN_REGEX.test(submitterName);
    const checkMobileValue = checkPhone(submitterPhone);
    const checkIdValue = checkIdCard(submitterIdCardNumber);
    if (!checkName || !checkMobileValue || !checkIdValue || agreement !== true || phoneCodeCheck) {
        return false;
    }
    return true;
};

const checkEnterpriseValue = (formValue: any) => {
    const {enterpriseName, enterpriseCode, licenseStartTime, licenseEndTime, orgType, enterpriseLicense} =
        formValue?.enterpriseInfo || {};
    if (!enterpriseName || !enterpriseCode || !licenseStartTime || !licenseEndTime || !orgType || !enterpriseLicense) {
        return false;
    }
    return true;
};

const checkOtherValue = (formValue: any) => {
    const {enterpriseName, enterpriseCode, licenseStartTime, licenseEndTime, enterpriseLicense} =
        formValue?.otherOrgInfo || {};
    if (!enterpriseName || !enterpriseCode || !licenseStartTime || !licenseEndTime || !enterpriseLicense) {
        return false;
    }
    return true;
};

const checkDepartmentValue = (formValue: any) => {
    const {institutionName, institutionCode, licenseStartTime, licenseEndTime, institutionLicense} =
        formValue?.publicInstitutionInfo || {};
    if (!institutionName || !institutionCode || !licenseStartTime || !licenseEndTime || !institutionLicense) {
        return false;
    }
    return true;
};

const checkGovValue = (formValue: any) => {
    const {governmentName, applicationLetter} = formValue?.governmentInfo || {};
    if (!governmentName || !applicationLetter) {
        return false;
    }
    return true;
};

export const checkOrgSubmitFormValue = (formValue: any, canOperatorEdit?: boolean) => {
    const checkSub = checkSubmitterInfo(formValue);
    if (!checkSub) {
        return false;
    }

    if (canOperatorEdit) {
        return checkSub;
    }

    switch (formValue.customerType) {
        case AccountQualifyType.Enterprise:
            return checkEnterpriseValue(formValue);
        case AccountQualifyType.Gov:
            return checkGovValue(formValue);
        case AccountQualifyType.Other:
            return checkOtherValue(formValue);
        case AccountQualifyType.Department:
            return checkDepartmentValue(formValue);
        default:
            return true;
    }
};

export const getSubmitParams = (formValue: any) => {
    const {
        customerId,
        customerType,
        submitterName,
        submitterPhone,
        submitterIdCardNumber,
        enterpriseInfo,
        governmentInfo,
        publicInstitutionInfo,
        otherOrgInfo,
    } = formValue;
    const updateEnterprise = enterpriseInfo
        ? {
              ...enterpriseInfo,
              enterpriseName: enterpriseInfo.enterpriseName?.trim(),
              enterpriseCode: enterpriseInfo.enterpriseCode?.trim()?.toLocaleUpperCase(),
          }
        : undefined;
    const updateGov = governmentInfo
        ? {
              ...governmentInfo,
              governmentName: governmentInfo.governmentName?.trim(),
          }
        : undefined;

    const updateDepartment = publicInstitutionInfo
        ? {
              ...publicInstitutionInfo,
              institutionName: publicInstitutionInfo?.institutionName,
              institutionCode: publicInstitutionInfo?.institutionCode?.trim()?.toLocaleUpperCase(),
          }
        : undefined;

    const updateOther = otherOrgInfo
        ? {
              ...otherOrgInfo,
              enterpriseName: otherOrgInfo.enterpriseName?.trim(),
              enterpriseCode: otherOrgInfo.enterpriseCode?.trim()?.toLocaleUpperCase(),
          }
        : undefined;

    const update = {
        customerId,
        customerType,
        submitterName: submitterName?.trim(),
        submitterPhone: submitterPhone?.trim(),
        submitterIdCardNumber: submitterIdCardNumber?.trim()?.toLocaleUpperCase(),
        enterpriseInfo: updateEnterprise,
        governmentInfo: updateGov,
        publicInstitutionInfo: updateDepartment,
        otherOrgInfo: updateOther,
    } as OrgUpdateParams | OrgSubmitParams;
    return update;
};

export const getEditParams = (formValue: any) => {
    const {customerId, submitterName, submitterPhone, submitterIdCardNumber} = formValue;

    const update = {
        customerId,
        submitterName: submitterName?.trim(),
        submitterPhone: submitterPhone?.trim(),
        submitterIdCardNumber: submitterIdCardNumber?.trim()?.toLocaleUpperCase(),
    } as OrgUpdateParams | OrgSubmitParams;
    return update;
};

export const hideAuth = (orgType?: AccountQualifyType) =>
    orgType && [AccountQualifyType.Gov, AccountQualifyType.Department].includes(orgType);

export const checkPhoneVerify = (phone: string) => {
    return () => {
        return smsApi
            .checkPhoneVerify({phone})
            .then(res => {
                return res.isVerify;
            })
            .catch(() => true);
    };
};

const checkPhone = (value: string) => {
    if (!value) {
        return false;
    }

    if (/^\*{7}\d{4}$/.test(value)) {
        return true;
    }
    return PHONE_REG.test(value);
};

const checkIdCard = (value: string) => {
    if (!value) {
        return false;
    }

    if (/^\*{14}\d{4}$/.test(value)) {
        return true;
    }
    return IdCardRegex.test(value);
};

// 更新场景豁免脱敏后的身份证号
export const validateIdNumberMasking = (msg?: string) => {
    return (rule: any, value: string) => {
        if (/^\*{14}\d{4}$/.test(value)) {
            return Promise.resolve();
        }
        return validateChineseIdNumber(msg)(rule, value);
    };
};

// 更新场景豁免脱敏后的手机号
export const validatePhoneMasking = (msg?: string) => {
    return (rule: any, value: string) => {
        if (/^\*{7}\d{4}$/.test(value)) {
            return Promise.resolve();
        }
        return validatePhone(msg || '请输入正确的手机号码')(rule, value);
    };
};
