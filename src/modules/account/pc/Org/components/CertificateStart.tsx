import {FC, useCallback, useEffect, useState} from 'react';
import {DatePicker, Flex, message} from 'antd';
import dayjs, {Dayjs} from 'dayjs';
import styled from '@emotion/styled';

export const StyledDatePicker = styled(DatePicker)`
    &.ant-picker .ant-picker-suffix {
        color: ${(props: any) => {
            return props.disabled ? 'rgba(0, 3, 17, 0.4)' : '#000311';
        }};
    }
`;

export interface CertificateStartProps {
    onChange?: (val: string) => void;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
}

const modal: any = null;
const CertificateStart: FC<CertificateStartProps> = props => {
    const [display, setDisplay] = useState<Dayjs | null>(null);
    const {value, onChange, disabled} = props;

    useEffect(() => {
        const display = value ? dayjs(value) : null;
        setDisplay(display);
    }, [value]);

    const selectDate = useCallback(
        (val: any) => {
            if (val && dayjs(val).isAfter(dayjs())) {
                message.open({
                    type: 'error',
                    content: '证件起始时间必须在今天之前',
                });
                return;
            }

            const value = val ? dayjs(val).format('YYYY-MM-DD') : '';
            onChange?.(value);
            modal?.close();
        },
        [onChange]
    );

    const disabledDate = useCallback((currentDate: any) => {
        return dayjs(currentDate).isAfter(dayjs());
    }, []);

    return (
        <Flex className="rounded-[9px] bg-colorBgFormList pl-[12px]" align="center">
            <div className="w-[40px] text-gray-tertiary">开始</div>
            <StyledDatePicker
                style={{
                    padding: '6px 8px 6px',
                }}
                disabled={disabled}
                className="w-full"
                placeholder="输入日期筛选"
                value={display as any}
                onChange={selectDate}
                disabledDate={disabledDate}
            />
        </Flex>
    );
};

export default CertificateStart;
