import {Card, Image, Space} from 'antd';
import {HTMLAttributes, ReactNode} from 'react';
import {CardInterface} from 'antd/es/card';
import {CheckCircleFilled, CloseCircleFilled} from '@ant-design/icons';
import classNames from 'classnames';
import {AccountQualifyType} from '@/api/account/interface';

type SampleProps = HTMLAttributes<CardInterface> & {
    type: AccountQualifyType;
};

interface SampleConfig {
    title: string;
    description: string[];
    picture: ReactNode | null;
}

const BusinessLicenseConfig: SampleConfig = {
    title: '营业执照影印件',
    description: [
        '1.请提供证件的原件拍照或彩色电子扫描件，若提供黑白复印件则需要加盖红色公章',
        '2.证件完整清晰可辨，关键信息内容不得遮挡缺失',
        '3.证件需在有效期内且为最新版',
        '4.支持png、bmp、jpeg、jpg格式，图片大小不超过20M',
    ],
    picture: (
        <div>
            <div className="mb-[16px] w-fit text-center">
                <Image
                    className="border border-primary"
                    width={110}
                    src="https://pic.rmb.bdstatic.com/81f442e5bba504e81cc4edd08912e490.png"
                />
                <p className="text-xs">
                    <CheckCircleFilled className="mr-[2px] text-primary" />
                    <span className="font-medium text-black">四角完整、清晰</span>
                </p>
            </div>
            <Space size={8} align="center" wrap>
                <div className="text-center">
                    <Image
                        className="border border-error"
                        width={110}
                        src="https://pic.rmb.bdstatic.com/d9ee5b7be61a2130aa895758b226ae6c.png"
                    />
                    <p className="text-xs">
                        <CloseCircleFilled className="mr-[2px] text-error" />
                        <span className="font-medium text-black">页面文字不清晰</span>
                    </p>
                </div>
                <div className="text-center">
                    <Image
                        className="border border-error"
                        width={110}
                        src="https://pic.rmb.bdstatic.com/bd81603929fdd0d0a27b07e0613e1691.png"
                    />
                    <p className="text-xs">
                        <CloseCircleFilled className="mr-[2px] text-error" />
                        <span className="font-medium text-black">未加盖公章</span>
                    </p>
                </div>
            </Space>
        </div>
    ),
};

const DepartmentConfig = {
    title: '事业单位法人证书',
    description: [
        '1.为确保入驻的主体真实有效、组织知情、合法经营，请上传《事业单位法人证书》',
        '2.支持png、bmp、jpeg、jpg格式，图片大小不超过20M',
    ],
    picture: (
        <Image
            width={336}
            src="https://now.bdstatic.com/store/v2/43bbe5a/lingjing-fe/97ece00/assets/public-institution-example.4b260cf1.jpg"
        />
    ),
};

const OtherConfig = {
    title: '组织机构代码证',
    description: [
        '1.请提供证件的原件拍照或彩色电子扫描件，若提供黑白复印件则需要加盖红色公章',
        '2.支持png、bmp、jpeg、jpg格式，图片大小不超过20M',
    ],
    picture: null,
};

const SAMPLE_CONFIG: Partial<Record<AccountQualifyType, SampleConfig>> = {
    [AccountQualifyType.Enterprise]: BusinessLicenseConfig,
    [AccountQualifyType.Other]: OtherConfig,
    [AccountQualifyType.Department]: DepartmentConfig,
};

const Sample = (props: SampleProps) => {
    const {type, className} = props;
    const config = SAMPLE_CONFIG[type];
    const {description, picture, title} = config || {};
    return (
        <Card className={classNames('w-[400px]', className)}>
            <h1 className="mb-5 text-base font-medium">{title}</h1>
            <h3 className="mb-5 text-sm text-black">图片要求</h3>
            {description?.map(des => (
                <p key={type + des} className="mb-3.5 text-gray-tertiary">
                    {des}
                </p>
            ))}
            {picture && (
                <div>
                    <h3 className="mb-5 text-sm text-black">示例</h3>
                    {picture}
                </div>
            )}
        </Card>
    );
};

export default Sample;
