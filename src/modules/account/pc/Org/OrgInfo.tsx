/**
 * 企业账号中心
 */
import {Row} from 'antd';
import {Fragment} from 'react';
import {AccountInfoPage} from '@/api/account/interface';
import usePersonCardHook from '@/modules/account/hooks/useAuthCardConfigHook';
import LoadError from '@/components/Loading/LoadError';
import AuthCard from '@/modules/account/pc/Person/components/BaseAuthCard';

interface OrgInfoProps {
    account: AccountInfoPage;
    loadAccountInfo: () => Promise<any>;
}

const OrgInfo = (props: OrgInfoProps) => {
    const {account} = props;
    const {orgCardConfig} = usePersonCardHook(account);

    const centerStyle = 'my-auto flex h-[calc(100vh-120px)] flex-shrink justify-center';

    return (
        <div>
            <Row align="middle" className="mb-5">
                <span className="mr-2 text-2xl font-medium">账号信息</span>
            </Row>

            {/* TODO 个人认证内容 */}
            {orgCardConfig && orgCardConfig.length > 0 ? (
                <Fragment>
                    <div className="text-black-base mb-[18px] font-pingfang text-lg font-medium leading-6">
                        我的认证
                    </div>
                    <div className="mb-6 grid grid-cols-2 gap-[15px]">
                        {orgCardConfig?.map(items => <AuthCard key={items.title} {...items} account={account} />)}
                    </div>
                </Fragment>
            ) : (
                <LoadError className={centerStyle} />
            )}
        </div>
    );
};

export default OrgInfo;
