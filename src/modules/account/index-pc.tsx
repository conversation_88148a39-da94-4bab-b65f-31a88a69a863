import {useEffect, useMemo} from 'react';
import {AccountQualifyType, QualifyStatus} from '@/api/account/interface';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useAccountInfo} from './hooks/useAccountInfo';
import PersonInfo from './pc/Person/PersonInfo';
import OrgInfo from './pc/Org/OrgInfo';
import AccountStyleProvider from './pc/Org/components/AccountStyleProvider';

const AccountCenter = () => {
    const {displayLog} = useUbcLogV3();
    const {account, loading, loadAccountInfo} = useAccountInfo();
    // 判断是否是企业或者政府机构账号
    const isOrg = useMemo(
        () =>
            account?.accountType === AccountQualifyType.Person
                ? account?.customerInfo?.entityStatus === QualifyStatus.AUDIT
                : [
                      AccountQualifyType.Department,
                      AccountQualifyType.Enterprise,
                      AccountQualifyType.Gov,
                      AccountQualifyType.Other,
                  ].includes(account?.accountType),
        [account?.accountType, account?.customerInfo?.entityStatus]
    );

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    return (
        <AccountStyleProvider>
            {loading ? (
                <div className="my-auto flex h-[calc(100vh-120px)] flex-shrink justify-center">
                    <Loading />
                </div>
            ) : isOrg ? (
                <OrgInfo account={account} loadAccountInfo={loadAccountInfo} />
            ) : (
                <PersonInfo account={account} loadAccountInfo={loadAccountInfo} />
            )}
        </AccountStyleProvider>
    );
};

export default function AccountPc() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.ACCOUNT_CENTER}>
            <CommonErrorBoundary pendingFallback={<Loading />}>
                <AccountCenter />
            </CommonErrorBoundary>
        </LogContextProvider>
    );
}
