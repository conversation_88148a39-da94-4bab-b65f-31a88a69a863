import {useCallback, useMemo, useState} from 'react';
import urls from '@/links';
import {
    AccountInfo,
    PersonQualifyStatus,
    QualifyStatus,
    EnterpriseType,
    AccountQualifyType,
    CareerType,
    CareerAuthStatus,
    CustomerAuthStatus,
} from '@/api/account/interface';
import {
    CUSTOMER_TYPE_MAP,
    PERSON_AUTH_STATUS_TAG_CONFIG,
    CAREER_CARD_TITLES,
    CERTIFICATION_STATUS_TAG_CONFIG,
    BUTTON_TEXT_MAP,
    CAREER_CARD_CONFIGS,
    LAWYER_AUTH_URL,
    PERSON_CARD_CONFIGS,
    PASS_AUTH_HOST,
} from '@/modules/account/pc/Org/components/config';
import {
    AuthCardConfigProps,
    BaseAuthCardTitles,
    CareerAuthType,
    OriginalCareerAuthStatus,
} from '@/modules/account/pc/Person/interface';

function openUrl(url: string) {
    setTimeout(() => {
        window.open(url, '_blank');
    });
}

export default function usePersonCardHook(account: AccountInfo) {
    const accountType = account.accountType;

    // 设置实名信息弹窗的可见性状态，默认为false（不显示）
    const [realNameModalVisible, setRealNameModalVisible] = useState(false);

    /** * 个人实名信息弹窗 -- 关闭按钮 */
    const handleRealNameModalCancel = useCallback(() => {
        setRealNameModalVisible(false);
    }, []);

    /** * 个人实名信息弹窗 -- 确认按钮 */
    const handleRealNameModalConfirm = useCallback(() => {
        setRealNameModalVisible(false);
    }, []);

    const getOrgCardConfig = useCallback(
        (account: AccountInfo) => {
            if (!account?.customerInfo) return [];

            const {authStatus, entityStatus} = account?.customerInfo;

            // 获取配置项
            const authenticityConfig =
                PERSON_CARD_CONFIGS[`${EnterpriseType.personBusiness}-${BaseAuthCardTitles.Authenticity}`];
            const subjectConfig = PERSON_CARD_CONFIGS[BaseAuthCardTitles.Subject];

            // 判断是否为政府或部门账号，如果是则真实性认证卡片按钮置灰
            const isAboutGovCompany = [AccountQualifyType.Department, AccountQualifyType.Gov].includes(accountType);

            return [
                {
                    ...subjectConfig,
                    buttonText: BUTTON_TEXT_MAP[entityStatus] || '立即认证',
                    disabled: entityStatus === QualifyStatus.AUDIT,
                    ...(entityStatus !== QualifyStatus.NONE && {...CERTIFICATION_STATUS_TAG_CONFIG[entityStatus]}),
                    onButtonClick: () =>
                        openUrl(`${urls.accountOrgSubmit.raw()}?type=${CUSTOMER_TYPE_MAP[accountType]}`),
                },
                {
                    ...authenticityConfig,
                    buttonText: BUTTON_TEXT_MAP[authStatus] || '立即认证',
                    ...(authStatus !== CustomerAuthStatus.NONE &&
                        !isAboutGovCompany && {...CERTIFICATION_STATUS_TAG_CONFIG[authStatus]}),
                    disabled: entityStatus !== QualifyStatus.PASS || isAboutGovCompany,
                    onButtonClick: () => openUrl(urls.accountOrgAuth.raw()),
                },
            ];
        },
        [accountType]
    );

    // eslint-disable-next-line complexity
    const getPersonAuthCardsConfig = useCallback((account: AccountInfo) => {
        if (!account?.personInfo) return {careerCards: [], personCards: []};

        const {authStatus, realStatus, careerAuthStatus, careerType, originalCareerAuthStatus} = account.personInfo;

        // 判断是否有任何职业认证处于 "认证中" 或 "已认证"
        const isAnyCareerAuthedOrAuthing = [CareerAuthStatus.Auditing, CareerAuthStatus.Pass].includes(
            careerAuthStatus
        );
        // 检查是否需要设置特殊的已认证但即将过期状态
        const isAboutToExpire =
            careerAuthStatus === CareerAuthStatus.Pass && originalCareerAuthStatus === OriginalCareerAuthStatus.Examine;

        // 初始化卡片列表 - 只包括 CAREER_CARD_CONFIGS 中定义的卡片(排除医生卡片)
        let careerCards: AuthCardConfigProps[] = Object.values(CAREER_CARD_CONFIGS)
            .filter(config => config.title !== CAREER_CARD_TITLES[CareerAuthType.Doctor])
            .map(config => {
                return {
                    ...config,
                    disabled: realStatus === PersonQualifyStatus.NONE || authStatus === PersonQualifyStatus.NONE,
                    onButtonClick: (() => {
                        switch (config.title) {
                            case CAREER_CARD_TITLES[CareerAuthType.Lawyer]:
                                return () => openUrl(LAWYER_AUTH_URL);
                            default:
                                return undefined;
                        }
                    })(),
                };
            });

        // 更新对应的职业认证卡片的状态
        if (isAnyCareerAuthedOrAuthing && careerType in CAREER_CARD_CONFIGS) {
            const certifiedCardIndex = careerCards.findIndex(card => card.title === CAREER_CARD_TITLES[careerType]);
            if (certifiedCardIndex !== -1) {
                let cardToUpdate = {
                    ...careerCards[certifiedCardIndex],
                    disabled: false,
                    buttonText: '去查看',
                };

                // 新增条件：当careerType为医生(2)或律师(3)且 carrerAuthStatus 为"认证中"(1)时不显示"认证中"tag标签样式
                const shouldHideAuthingTag =
                    (careerType === CareerType.Doctor || careerType === CareerType.Lawyer) &&
                    careerAuthStatus === CareerAuthStatus.Auditing;
                // 如果不是特殊状态，则应用当前状态的配置
                if (!shouldHideAuthingTag) {
                    cardToUpdate = {
                        ...cardToUpdate,
                        ...CERTIFICATION_STATUS_TAG_CONFIG[careerAuthStatus],
                    };
                }

                // 如果是特殊状态，则应用该状态的配置
                if (isAboutToExpire) {
                    cardToUpdate = {
                        ...cardToUpdate,
                        ...CERTIFICATION_STATUS_TAG_CONFIG[`${QualifyStatus.PASS}-${OriginalCareerAuthStatus.Examine}`],
                    };
                }

                careerCards[certifiedCardIndex] = cardToUpdate;

                // 更新其他卡片的状态
                careerCards = careerCards.map((card, index) => {
                    if (index !== certifiedCardIndex) {
                        return {
                            ...card,
                            disabled: true,
                        };
                    }
                    return card;
                });
            }
        }

        const getStatusTagConfig = (status: PersonQualifyStatus) => {
            if (status === PersonQualifyStatus.NONE) {
                return {};
            }
            return PERSON_AUTH_STATUS_TAG_CONFIG[status];
        };

        // 初始化个人卡片列表
        const personCards: AuthCardConfigProps[] = Object.values(PERSON_CARD_CONFIGS)
            .slice(0, 2)
            .map(config => {
                // 根据卡片类型选择对应的状态进行判断
                const status = config.title === BaseAuthCardTitles.RealName ? realStatus : authStatus;

                return {
                    ...config,
                    buttonText: status === PersonQualifyStatus.PASS ? '去查看' : '立即认证',
                    disabled: false,
                    ...getStatusTagConfig(status),
                    onButtonClick: () => {
                        if (status === PersonQualifyStatus.PASS) {
                            if (config.title === BaseAuthCardTitles.RealName) {
                                setRealNameModalVisible(true);
                            } else {
                                openUrl(PASS_AUTH_HOST);
                            }
                        } else {
                            openUrl(PASS_AUTH_HOST);
                        }
                    },
                };
            });

        return {careerCards, personCards};
    }, []);

    const orgCardConfig = useMemo(() => getOrgCardConfig(account), [account, getOrgCardConfig]);
    const {careerCards, personCards} = useMemo(
        () => getPersonAuthCardsConfig(account),
        [account, getPersonAuthCardsConfig]
    );

    return {
        realNameModalVisible,
        handleRealNameModalCancel,
        handleRealNameModalConfirm,
        orgCardConfig,
        careerCards,
        personCards,
    };
}
