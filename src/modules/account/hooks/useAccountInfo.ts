import {useCallback, useState} from 'react';
import {
    AccountInfo,
    AccountInfoPage,
    AccountQualifyStatus,
    AccountQualifyType,
    CustomerAuthStatus,
    PersonInfo,
    PersonQualifyStatus,
    QualifyStatus,
} from '@/api/account/interface';
import api from '@/api/account/index';
import {useOnMountUnsafe} from '@/hooks/useOnMountUnsafe';

const getOrgQualifyStatus = (info: AccountInfo) => {
    const {customerInfo, accountType, personInfo} = info || {};
    if (!customerInfo) {
        return AccountQualifyStatus.Init;
    }

    // 政府和事业单位无需做真实性认证，直接默认是高级通过
    if ([AccountQualifyType.Gov, AccountQualifyType.Department].includes(accountType)) {
        return customerInfo?.entityStatus === QualifyStatus.PASS
            ? AccountQualifyStatus.OrgSeniorPass
            : customerInfo?.entityStatus === QualifyStatus.AUDIT
            ? AccountQualifyStatus.OrgSeniorAudit
            : AccountQualifyStatus.Init;
    }

    if (customerInfo?.entityStatus === QualifyStatus.PASS) {
        return customerInfo?.authStatus === CustomerAuthStatus.PASS
            ? AccountQualifyStatus.OrgSeniorPass
            : customerInfo?.authStatus === CustomerAuthStatus.AUDIT
            ? AccountQualifyStatus.OrgSeniorAudit
            : AccountQualifyStatus.OrgPrimePass;
    } else {
        return customerInfo.entityStatus === QualifyStatus.AUDIT
            ? AccountQualifyStatus.OrgPrimeAudit
            : // 企业提交失败，仍然保留个人认证状态
              getPersonQualifyStatus(personInfo);
    }
};

const getPersonQualifyStatus = (personInfo?: PersonInfo) => {
    if (personInfo?.realStatus === PersonQualifyStatus.PASS) {
        return personInfo?.authStatus === PersonQualifyStatus.PASS
            ? AccountQualifyStatus.PersonSeniorPass
            : AccountQualifyStatus.PersonPrimePass;
    }
    return AccountQualifyStatus.Init;
};

const getAccountQualifyStatus = (info?: AccountInfo) => {
    return info?.customerInfo ? getOrgQualifyStatus(info) : getPersonQualifyStatus(info?.personInfo);
};

export function useAccountInfo() {
    const [account, setAccount] = useState<AccountInfoPage>({
        accountType: AccountQualifyType.Person,
        qualifyStatus: AccountQualifyStatus.Init,
    });
    const [loading, setLoading] = useState<boolean>();

    const loadAccountInfo = useCallback(() => {
        setLoading(true);
        return api
            .getAccountInfo()
            .then(res => {
                const update = {
                    ...res,
                    qualifyStatus: getAccountQualifyStatus(res),
                };
                setAccount(update);
                return update;
            })
            .finally(() => setLoading(false));
    }, []);

    useOnMountUnsafe(() => {
        loadAccountInfo();
    });

    return {
        account,
        loading,
        loadAccountInfo,
    };
}
