import {useCallback, useEffect, useState} from 'react';
import {OrgSubmitInfo} from '@/api/account/interface';
import api from '@/api/account/index';

export function useOrgSubmitInfo(loadData: boolean) {
    const [orgSubmitInfo, setOrgSubmitInfo] = useState<OrgSubmitInfo>();
    const [loading, setLoading] = useState<boolean>();

    const loadOrgSubmitInfo = useCallback(() => {
        setLoading(true);
        return api
            .getOrgInfo()
            .then(res => {
                setOrgSubmitInfo(res);
            })
            .finally(() => setLoading(false));
    }, []);

    useEffect(() => {
        loadData && loadOrgSubmitInfo();
    }, [loadData, loadOrgSubmitInfo]);

    return {
        orgSubmitInfo,
        loading,
        loadOrgSubmitInfo,
    };
}
