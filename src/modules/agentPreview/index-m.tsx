import {CacheProvider} from 'react-suspense-boundary';
import React, {useEffect} from 'react';
import {useParams} from 'react-router-dom';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {getAgentPreview} from '@/api/myAgentPreview';
import {getTargetLink} from '../center/components/mobile/getTargetLink';

/**
 * 预览页
 */
export default function PreviewComponents() {
    const {id} = useParams();

    useEffect(() => {
        (async () => {
            if (id) {
                // 由于新增审核态预览，必须判断是否部署成功
                const previewInfo = await getAgentPreview({appId: id});
                const url = getTargetLink(previewInfo);
                window.location.href = url;
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <CacheProvider>
            <CommonErrorBoundary pendingFallback={<Loading />}>
                <Loading />
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
