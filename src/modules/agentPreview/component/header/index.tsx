/**
 * @file 预览页顶部栏
 * <AUTHOR>
 */

import {ConfigProvider} from 'antd';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import PopoverContainer from '@/components/Popover/Container';
import {ConfigTab} from '../../interface';
import {PreviewHeaderLeft} from './Left';
import {PreviewSegmented} from './PreviewSegmented';
import {PreviewHeaderRight} from './Right';

export const PreviewHeader = () => {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {shareTag, gray} = useAgentInfoStore(store => ({
        shareTag: store.shareTag,
        gray: store.gray,
    }));

    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        borderRadius: 99,
                        controlHeight: 30,
                        colorBorder: '#ededf0',
                        paddingInline: 12,
                        fontWeight: 500,
                    },
                },
            }}
        >
            <PopoverContainer className="relative flex h-full w-full flex-row items-center justify-between px-[22px]">
                <PreviewHeaderLeft />

                {!gray && (
                    <>
                        {isLogin && shareTag === ConfigTab.copy && <PreviewSegmented />}
                        {/* 数据还未返回的时候不展示 */}
                        {shareTag !== undefined && <PreviewHeaderRight />}
                    </>
                )}
            </PopoverContainer>
        </ConfigProvider>
    );
};
