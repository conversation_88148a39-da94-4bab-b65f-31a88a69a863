/**
 * @file 从 components/SideBar/v2-px组件中抽离出来的侧边栏菜单，用于预览页顶部返回按钮hover展示
 * <AUTHOR>
 */

import classNames from 'classnames';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {Badge} from 'antd';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useAiBotStore} from '@/store/agent/aiBot';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {LoginSource} from '@/utils/loggerV2/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import homeConstant from '@/dicts/home';
import urls from '@/links';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {getRedDot} from '@/api/redDot';
import {RedDotType} from '@/api/redDot/interface';
import {isTpProxyMode} from '@/utils/tp/tpProxyMode';
import useAiBotSdk from '@/components/Sidebar/hooks/useAiBotSdk';
import OneLineTooltip from '@/components/Sidebar/OneLineTooltip';
import {getCounselingAccess} from '@/api/counseling';

interface MenuDetail {
    label?: React.ReactNode;
    children?: React.ReactNode;
    key: string;
    onClick: React.MouseEventHandler<HTMLDivElement>;
}
const menuClickEventMap: Record<string, EVENT_VALUE_CONST> = {
    [urls.agentList.raw()]: EVENT_VALUE_CONST.MY_AGENT,
    [urls.plugin.raw()]: EVENT_VALUE_CONST.MY_PLUGIN,
    [urls.datasetList.raw()]: EVENT_VALUE_CONST.MY_REPOSITORY,
};
const MenuItem = ({className, url, ...props}: {url: string} & React.HTMLAttributes<HTMLDivElement>) => {
    const active = useLocation().pathname.startsWith(url);
    return (
        <div
            className={classNames(
                className,
                'h-10 cursor-pointer text-sm font-medium transition-colors hover:bg-[#f7f8fa]',
                {
                    'bg-[#ECEEF3] bg-opacity-40 text-[#1e1f24]': active,
                }
            )}
            {...props}
        />
    );
};

const MenuCard = ({
    title,
    menus,
    className,
}: {title: string; menus: MenuDetail[]} & React.HTMLAttributes<HTMLDivElement>) => {
    return (
        <div className={classNames(className, 'mx-auto w-full border-t-[1px] border-t-gray-border-secondary')}>
            <div className="px-3 pb-3 pt-[18px] text-sm font-medium text-gray-tertiary">{title}</div>
            <div className="flex flex-col gap-1">
                {menus.map(
                    menu =>
                        menu.children || (
                            <MenuItem
                                className="rounded-lg px-1 py-2"
                                key={menu.key}
                                url={menu.key}
                                onClick={menu.onClick}
                            >
                                {menu.label}
                            </MenuItem>
                        )
                )}
            </div>
        </div>
    );
};

export const SideMenus = () => {
    const {setAiBotState} = useAiBotStore(state => ({
        setAiBotState: state.setAiBotState,
    }));
    const active = useLocation().pathname;
    const uniformLoginMyAgent = useUniformLogin(LoginSource.MY_AGENT);
    const uniformLoginCounseling = useUniformLogin(LoginSource.MY_COUNSELING);
    const uniformLoginMyPlugin = useUniformLogin(LoginSource.MY_PLUGIN);
    const uniformLoginMyKnowledge = useUniformLogin(LoginSource.MY_KNOWLEDGE);
    const uniformLoginMyWorkflow = useUniformLogin(LoginSource.MY_WORKFLOW);
    const {clickLog} = useUbcLogV2();
    const [isLogin] = useUserInfoStore(store => [store.isLogin]);
    const navigate = useNavigate();

    const [showMyAgentRedDot, setShowMyAgentRedDot] = useState(false);
    const [showCounselingRedDot, setShowCounselingRedDot] = useState(false);

    const [userInfoData] = useUserInfoStore(store => [store.userInfoData]);
    const allowCreate = !userInfoData?.hasTpProxy;
    const showServeMenuCard = !isTpProxyMode();

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            const {redShow} = await getRedDot({
                type: RedDotType.AgentDiagnosis,
            });
            redShow && setShowMyAgentRedDot(redShow);
        })();
    }, [isLogin]);

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            const {redShow} = await getRedDot({
                type: RedDotType.Counseling,
            });
            redShow && setShowCounselingRedDot(redShow);
        })();
    }, [isLogin]);

    const [showCounseling, setShowCounseling] = useState(false);

    useEffect(() => {
        (async () => {
            if (!isLogin) return;
            try {
                // 获取是否开放付费咨询入口
                const isShow = await getCounselingAccess();
                setShowCounseling(isShow);
            } catch (error) {
                setShowCounseling(false);
            }
        })();
    }, [isLogin]);

    const checkAllowCreate = useCallback(
        (task: any): any => {
            if (!allowCreate) {
                return () => {};
            }

            task();
        },
        [allowCreate]
    );

    const handleLinkClick: (linkUrl: string) => () => void = useCallback(
        linkUrl => {
            return () => {
                const valueName = menuClickEventMap[linkUrl];
                valueName && clickLog(valueName);
                navigate(linkUrl);
            };
        },
        [navigate, clickLog]
    );

    const openAiBot = useCallback(async () => {
        // 智能体客服点击
        clickLog(EVENT_VALUE_CONST.SIDE_SERVICE);
        setAiBotState(true);
    }, [clickLog, setAiBotState]);

    useAiBotSdk();

    const handleDocsClick = useCallback(() => {
        // 文档中心点击
        clickLog(EVENT_VALUE_CONST.SIDE_DOCUMENT);
        window.open(homeConstant.DOCS_URL);
    }, [clickLog]);

    const handleCommunityClick = useCallback(() => {
        // 社区中心点击
        clickLog(EVENT_VALUE_CONST.SIDE_COMMUNITY);
        window.open(homeConstant.COMMUNITY_URL);
    }, [clickLog]);

    const userMenuItem: MenuDetail[] = useMemo(() => {
        const menuList = [
            {
                key: urls.agent.raw(),
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span
                            className={classNames('iconfont text-base', {
                                'icon-myagent': !active.startsWith(urls.agent.raw()),
                                'icon-myagent1': active.startsWith(urls.agent.raw()),
                            })}
                        />
                        <span>我的智能体</span>
                        {showMyAgentRedDot && <Badge status="error" className="-ml-2 -mt-4" />}
                    </span>
                ),
                onClick: isLogin ? handleLinkClick(urls.agentList.raw()) : uniformLoginMyAgent,
            },
            {
                key: urls.plugin.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont', {
                                    'icon-a-myplugin': !active.startsWith(urls.plugin.raw()),
                                    'icon-myplugin': active.startsWith(urls.plugin.raw()),
                                })}
                            ></span>
                            <span>我的插件</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () => checkAllowCreate(isLogin ? handleLinkClick(urls.plugin.raw()) : uniformLoginMyPlugin),
            },
            {
                key: urls.dataset.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont', {
                                    'icon-DataSet': !active.startsWith(urls.dataset.raw()),
                                    'icon-dataset1': active.startsWith(urls.dataset.raw()),
                                })}
                            ></span>
                            <span>我的知识库</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.datasetList.raw()) : uniformLoginMyKnowledge),
            },
            {
                key: urls.workflowList.raw(),
                label: (
                    <OneLineTooltip flag={allowCreate} content={DISALLOW_OPERATE_HINT}>
                        <span
                            className={`flex items-center gap-2 px-2  ${
                                allowCreate ? 'text-black-base' : 'text-[#1E1F244D]'
                            }`}
                        >
                            <span
                                className={classNames('iconfont text-base', {
                                    'icon-workflow': !active.startsWith(urls.workflowList.raw()),
                                    'icon-workflows': active.startsWith(urls.workflowList.raw()),
                                })}
                            ></span>
                            <span>我的工作流</span>
                        </span>
                    </OneLineTooltip>
                ),
                onClick: () =>
                    checkAllowCreate(isLogin ? handleLinkClick(urls.workflowList.raw()) : uniformLoginMyWorkflow),
            },
        ];
        const counseling = {
            key: urls.counseling.raw(),
            label: (
                <span className="flex items-center gap-2 px-2">
                    <span
                        className={classNames('iconfont text-base', {
                            'icon-pay': !active.startsWith(urls.counseling.raw()),
                            'icon-pay-select': active.startsWith(urls.counseling.raw()),
                        })}
                    />
                    <span>付费咨询</span>
                    {showCounselingRedDot && <Badge status="error" className="-ml-2 -mt-4" />}
                </span>
            ),
            onClick: isLogin ? handleLinkClick(urls.counseling.raw()) : uniformLoginCounseling,
        };

        /** 是否展示付费咨询入口 */
        return showCounseling
            ? [
                  menuList[0],
                  counseling, // 插入付费咨询
                  ...menuList.slice(1), // 插入剩余菜单项
              ]
            : menuList;
    }, [
        active,
        showMyAgentRedDot,
        isLogin,
        handleLinkClick,
        uniformLoginMyAgent,
        allowCreate,
        showCounselingRedDot,
        uniformLoginCounseling,
        showCounseling,
        checkAllowCreate,
        uniformLoginMyPlugin,
        uniformLoginMyKnowledge,
        uniformLoginMyWorkflow,
    ]);

    const serveMenuItem: MenuDetail[] = useMemo(
        () => [
            {
                key: homeConstant.DOCS_URL,
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span className="iconfont icon-a-platformdoc text-base"></span>
                        <span>文档中心</span>
                    </span>
                ),
                onClick: handleDocsClick,
            },
            {
                key: homeConstant.COMMUNITY_URL,
                label: (
                    <span className="flex items-center gap-2 px-2">
                        <span className="iconfont icon-a-dialogueballoons text-base"></span>
                        <span>社区中心</span>
                    </span>
                ),
                onClick: handleCommunityClick,
            },
            {
                key: 'smart-service',
                children: (
                    <MenuItem
                        className="rounded-lg px-1 py-2"
                        key="smart-service"
                        url="smart-service"
                        onClick={openAiBot}
                    >
                        <span className="relative flex items-center gap-2 px-2">
                            <span className="iconfont icon-services text-base"></span>
                            <span>智能客服</span>
                        </span>
                    </MenuItem>
                ),
                onClick: openAiBot,
            },
        ],
        [handleCommunityClick, handleDocsClick, openAiBot]
    );
    return (
        <>
            <MenuItem
                url={urls.center.raw()}
                className="flex items-center gap-2 rounded-lg px-3 py-2"
                onClick={handleLinkClick(urls.center.raw())}
            >
                <span
                    className={classNames('iconfont', {
                        'icon-a-discoverycenter': !active.startsWith(urls.center.raw()),
                        'icon-a-discoverycenter1': active.startsWith(urls.center.raw()),
                    })}
                ></span>
                <span>智能体商店</span>
            </MenuItem>

            <div className="no-scrollbar mb-auto overflow-y-auto">
                <MenuCard title="个人空间" menus={userMenuItem} className="mb-4 mt-[18px]" />
                {showServeMenuCard && <MenuCard title="服务空间" menus={serveMenuItem} />}
            </div>
        </>
    );
};
