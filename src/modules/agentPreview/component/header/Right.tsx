/**
 * @file 预览页顶部栏右侧按钮功能区
 * <AUTHOR>
 */
import {Button} from 'antd';
import {useCallback} from 'react';
import {useNavigate, useParams, useSearchParams} from 'react-router-dom';
import urls from '@/links';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import DuplicateButton from '@/modules/agentPromptEditV2/pc/components/DuplicateButton';
import {useAgentNumModal} from '@/components/Sidebar/hooks/useAgentNum';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import PopoverContainer from '@/components/Popover/Container';
import useServerOnceTip from '@/components/Popover/useServerOnceTip';
import {PopupName} from '@/api/beginnerGuide/interface';
import {PreviewTab} from '../../interface';
import {DelayPopoVer} from '../Popover';
import {ShareButton} from './ShareButton';
import {CollectionButton} from './CollectionButton';

const COPY_AGENT = '喜欢这个智能体？尝试做个同款吧～';
const MAKE_AGENT = '喜欢这个智能体？参考它自己创建一个吧~';
export const PreviewHeaderRight = () => {
    const {name, shareTag} = useAgentInfoStore(store => ({
        name: store.name,
        shareTag: store.shareTag,
    }));
    const {id} = useParams();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const uniformLogin = useUniformLogin();
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const allowCreate = !userInfoData?.hasTpProxy;
    const previewActiveTab = searchParams.get('previewActiveTab') || PreviewTab.Preview;
    const {setOpenAgentWarningModal, appNum, appLimit, AgentNumModal, Modalprops} = useAgentNumModal();
    const {clickLog} = useUbcLogV2();

    const {open: openShareTip, handleClose: handleShareTip} = useServerOnceTip({
        name: PopupName.SharePoster,
        appId: id,
        autoView: true,
    });

    // 创建智能体按钮点击
    const createBtnClick = useCallback(() => {
        // 登录态校验
        if (!isLogin) {
            uniformLogin();
            return;
        }

        // 超过上限弹窗提示
        if (appNum && appLimit && appLimit <= appNum) {
            setOpenAgentWarningModal(true);
            return;
        }

        navigate(urls.agentPromptQuickStart.raw());
    }, [appLimit, appNum, isLogin, navigate, setOpenAgentWarningModal, uniformLogin]);

    // 做同款点击
    const copyAgent = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.AGENT_COPY, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
            [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
        });
    }, [clickLog, id, name]);

    return (
        <PopoverContainer className="relative flex h-full items-center gap-[10px]">
            {shareTag ? (
                <DelayPopoVer title={COPY_AGENT} align={{offset: [0, 40]}} openShareTip={openShareTip}>
                    <DuplicateButton onClick={copyAgent} agentName={name || ''} />
                </DelayPopoVer>
            ) : (
                <DelayPopoVer title={MAKE_AGENT} align={{offset: [0, 40]}} openShareTip={openShareTip}>
                    <Button
                        className="primary flex items-center gap-[3px] leading-5"
                        onClick={createBtnClick}
                        disabled={!allowCreate}
                    >
                        <span className="iconfont icon-imagine" />
                        创建智能体
                    </Button>
                </DelayPopoVer>
            )}

            {previewActiveTab === PreviewTab.Preview && (
                <>
                    <CollectionButton />
                    <ShareButton openShareTip={openShareTip} handleShareTip={handleShareTip} />
                </>
            )}
            <AgentNumModal {...Modalprops} />
        </PopoverContainer>
    );
};
