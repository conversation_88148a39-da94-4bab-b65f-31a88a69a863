/**
 * @file 预览页顶部栏切换组件
 * <AUTHOR>
 */
import styled from '@emotion/styled';
import {Segmented} from 'antd';
import {useCallback} from 'react';
import {SegmentedValue} from 'antd/es/segmented';
import {useParams, useSearchParams} from 'react-router-dom';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {PreviewTab} from '../../interface';

const StyledSegmented = styled(Segmented)`
    border-radius: 12px !important;
    .ant-segmented-group {
        .ant-segmented-item {
            border-radius: 12px !important;
            .ant-segmented-item-label {
                overflow: visible;
                min-height: 32px;
                line-height: 32px;
                padding: 0;
                font-size: 16px;
            }
        }
        .ant-segmented-item-selected {
            color: ${ThemeConfig.token.colorPrimary};
            font-weight: 500;
        }
        .ant-segmented-thumb-motion {
            border-radius: 12px !important;
        }
    }
`;

export const PreviewSegmented = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const previewActiveTab = searchParams.get('previewActiveTab') || PreviewTab.Preview;
    const name = useAgentInfoStore(store => store.name);
    const {clickLog} = useUbcLogV2();
    const {id} = useParams();

    //  点击tab切换 打点
    const handleTabClick = useCallback(
        (previewActiveTab: string) => {
            clickLog(EVENT_VALUE_CONST.AGENT_EXP_TAB, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
                [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
                [EVENT_EXT_KEY_CONST.E_AGENTEXP_TAB_NAME]: previewActiveTab === PreviewTab.Preview ? 1 : 2,
            });
        },
        [clickLog, id, name]
    );

    const handleTabChange = useCallback(
        (value: SegmentedValue) => {
            setSearchParams(
                () => {
                    const res: Record<string, string> = {};
                    const prev = new URLSearchParams(window.location.search);
                    res.previewActiveTab = value as AgentTab;
                    const appId = prev?.get('appId');
                    appId && (res.appId = appId);
                    return res;
                },
                {replace: true}
            );
        },
        [setSearchParams]
    );

    //  tab选项
    const tabOptions = [
        {
            label: (
                <div className="px-8" onClick={() => handleTabClick(PreviewTab.Preview)}>
                    对话
                </div>
            ),
            value: PreviewTab.Preview,
        },
        {
            label: (
                <div className="px-8" onClick={() => handleTabClick(PreviewTab.Config)}>
                    配置
                </div>
            ),
            value: PreviewTab.Config,
        },
    ];

    return (
        <StyledSegmented
            className="absolute right-1/2 translate-x-1/2"
            defaultValue={PreviewTab.Preview}
            value={previewActiveTab}
            options={tabOptions}
            block
            onChange={handleTabChange}
        />
    );
};
