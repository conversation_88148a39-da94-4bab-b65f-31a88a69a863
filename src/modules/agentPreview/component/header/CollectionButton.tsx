import {useCallback} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {NoticeType} from 'antd/es/message/interface';
import {Button, message} from 'antd';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {TagId} from '@/modules/center/utils';
import {FavoriteStatus} from '@/api/center/interface';
import centerApi from '@/api/center/index';
import urls from '@/links';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';

export const CollectionButton = () => {
    const {name, isFavorited, setIsFavorited} = useAgentInfoStore(store => ({
        name: store.name,
        shareTag: store.shareTag,
        setFavoriteCount: store.setFavoriteCount,
        isFavorited: store.isFavorited,
        setIsFavorited: store.setIsFavorited,
    }));

    const {id} = useParams();
    const navigate = useNavigate();
    const [isLogin] = useUserInfoStore(store => [store.isLogin]);
    const uniformLogin = useUniformLogin();
    const {clickLog} = useUbcLogV3();

    const redirectsToFavorite = useCallback(() => {
        navigate(`${urls.center.raw()}?tagId=${TagId.followed}`);
    }, [navigate]);

    // 关注按钮点击
    const handleFollowClick = useCallback(
        async (event: React.MouseEvent<HTMLDivElement>) => {
            event.stopPropagation();
            // 登录态校验
            if (!isLogin) {
                uniformLogin();
                return;
            }

            // 关注、取消关注 打点

            clickLog(
                EVENT_VALUE_CONST.AGENT_FOLLOW,
                {
                    agentName: name,
                    agentId: id,
                    isOpen: isFavorited ? 0 : 1,
                },
                EVENT_PAGE_CONST.AGENT_EXPERIENCE
            );

            // 乐观更新
            setIsFavorited(!isFavorited);

            await centerApi.updateFavoriteStatus({
                appId: id || '',
                status: isFavorited ? FavoriteStatus.UnFavorited : FavoriteStatus.Favorited,
            });

            const msgType: NoticeType = isFavorited ? 'error' : 'success';

            const msgContent = isFavorited ? (
                '已取消关注'
            ) : (
                <span>
                    关注成功
                    <Button className="ml-2 p-0 text-primary" type="link" onClick={redirectsToFavorite}>
                        去查看
                    </Button>
                </span>
            );

            message.open({type: msgType, content: msgContent, icon: isFavorited && <></>});
        },
        [clickLog, id, isFavorited, isLogin, name, redirectsToFavorite, setIsFavorited, uniformLogin]
    );

    return (
        <Button onClick={handleFollowClick} className="flex items-center gap-[3px]">
            {isFavorited ? (
                <span>已关注</span>
            ) : (
                <>
                    <span className="iconfont icon-plus pointer-events-auto mr-[3px] text-base leading-4" />
                    <span>关注</span>
                </>
            )}
        </Button>
    );
};
