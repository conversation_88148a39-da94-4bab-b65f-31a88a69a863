import {Tooltip} from 'antd';
import {useCallback} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {css} from '@emotion/css';
import urls from '@/links';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {SideMenus} from '@/modules/agentPreview/component/header/SideMenus';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {removeParameter} from '@/components/Login/hooks/useAccountNavigation';

const customStyle = css`
    .ant-tooltip-inner {
        border-radius: 12px !important;
        padding: 12px !important;
        width: 180px;
    }
`;

const tooltipStyle = css`
    max-width: 100% !important;
    .ant-tooltip-inner {
        border-radius: 12px !important;
    }
`;

export const PreviewHeaderLeft = () => {
    const {name, logoUrl, userName, publishTime, gray, userMedalInfo} = useAgentInfoStore(store => ({
        name: store.name,
        logoUrl: store.logoUrl,
        userName: store.userName,
        publishTime: store.publishTime,
        gray: store.gray,
        userMedalInfo: store.userMedalInfo,
    }));
    const {id} = useParams();
    const {clickLog} = useUbcLogV2();
    const navigate = useNavigate();

    const clickBack = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.AGENT_EXP_RETURN, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
            [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
        });

        // 根据当前路径判断返回哪个页面
        if (location.pathname.includes(removeParameter(urls.centerAgentPreview.raw()))) {
            navigate(urls.center.raw());
        } else if (location.pathname.includes(removeParameter(urls.agentPreview.raw()))) {
            navigate(urls.agentList.raw());
        } else {
            // 兜底，返回体验中心
            navigate(urls.center.raw());
        }
    }, [clickLog, id, name, navigate]);

    return (
        <div className="flex h-full flex-row  items-center">
            <Tooltip
                arrow={false}
                overlayClassName={customStyle}
                title={
                    <div className="w-full text-black">
                        <SideMenus />
                    </div>
                }
                placement="bottomLeft"
                color="#FFFFFF"
            >
                <div
                    className="relative h-10 w-10 cursor-pointer rounded-[9px] hover:bg-colorBgInput hover:text-primary"
                    onClick={clickBack}
                >
                    <span className="icon iconfont icon-left absolute left-2/4 top-2/4 -translate-x-2/4 -translate-y-2/4  text-xl" />
                </div>
            </Tooltip>
            <img className="mx-[9px] h-10 w-10 rounded-full" src={logoUrl}></img>

            <div className="flex flex-col justify-center">
                <div className="flex items-center text-sm font-medium">{name}</div>
                <div className="flex text-gray-tertiary">
                    <div className="flex items-center text-xs">
                        <span>@{userName}</span>
                        {userMedalInfo && userMedalInfo.length > 0 && (
                            <span className="flex">
                                {userMedalInfo.map(item => (
                                    <Tooltip
                                        key={item.medalId}
                                        title={item.name}
                                        placement="bottom"
                                        overlayClassName={tooltipStyle}
                                    >
                                        <img
                                            src={item.level.img}
                                            className="ml-[3px] h-3 w-3 cursor-pointer bg-contain first:ml-[3px]"
                                        />
                                    </Tooltip>
                                ))}
                            </span>
                        )}
                    </div>
                    <div className="mx-[6px] my-auto h-[11px] border-l border-[#DCDDE0]"></div>
                    <div className="text-xs">{gray ? '审核中' : `发布于${publishTime}`}</div>
                </div>
            </div>
        </div>
    );
};
