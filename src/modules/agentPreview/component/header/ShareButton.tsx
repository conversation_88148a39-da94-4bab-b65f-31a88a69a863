/**
 * @file 顶部分享按钮
 * <AUTHOR>
 */

import {Tooltip, Button, TooltipProps} from 'antd';
import {useCallback, useState} from 'react';
import {useParams} from 'react-router-dom';
import {getPopupContainer} from '@/utils/getPopupContainer';
import api from '@/api/appVersion';
import {AgentUrlChannel} from '@/modules/agentOutput/config';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {SharePopover} from '@/modules/agentPromptEditV2/pc/components/ShareAgent/SharePopover';
import CustomPopover from '@/components/Popover';
import {LogContextProvider} from '@/utils/loggerV2/context';

export const ButtonTip = ({
    children,
    title,
    ...props
}: {children: React.ReactNode; title: string | React.ReactNode} & TooltipProps) => {
    return (
        <Tooltip
            title={title}
            align={{
                offset: [0, 16],
            }}
            placement="bottom"
            getPopupContainer={getPopupContainer}
            {...props}
        >
            {children}
        </Tooltip>
    );
};

export const ShareButton = ({openShareTip, handleShareTip}: {openShareTip: boolean; handleShareTip: () => void}) => {
    const {id} = useParams();
    const [url, setUrl] = useState('');
    const {name, isOwner} = useAgentInfoStore(store => ({name: store.name, isOwner: store.isOwner}));
    const {clickLog} = useUbcLogV2();
    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin();
    const [isLoading, setLoading] = useState(true);

    // 获取分享链接
    const getUrl = useCallback(() => {
        if (!isLogin) {
            uniformLogin();
            return;
        }

        if (!url) {
            api.agentShareLinkInfo({
                appId: id || '',
                channel: AgentUrlChannel.agentURL,
            }).then(data => {
                setUrl(data.shareUrl);
                setLoading(false);
            });
        }
    }, [id, isLogin, uniformLogin, url]);

    // 分享按钮点击
    const shareAgent = useCallback(() => {
        getUrl();
        handleShareTip();
        clickLog(EVENT_VALUE_CONST.AGENT_SHARE, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
            [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
            [EVENT_EXT_KEY_CONST.E_IS_OWNER]: isOwner ? 1 : 0,
        });
    }, [clickLog, getUrl, handleShareTip, id, isOwner, name]);

    return (
        <CustomPopover
            type="primary"
            placement="bottomLeft"
            title={<span className="block w-[226px] font-normal">领取智能体专属海报，快去分享吧</span>}
            exitTime
            open={openShareTip}
            onClose={handleShareTip}
            overlayClassName="z-[51]"
            align={{offset: [0, -4]}}
            footer={
                <div className="mr-6 cursor-pointer font-medium" onClick={handleShareTip}>
                    知道了
                </div>
            }
        >
            <LogContextProvider
                ext={{agentId: id, agentName: name, eIsOwner: isOwner ? 1 : 0}}
                page={EVENT_PAGE_CONST.AGENT_EXPERIENCE}
            >
                <SharePopover id={id} name={name} isLoading={isLoading} shareUrl={url} textContent="手机扫码体验">
                    <Button onClick={shareAgent}>分享</Button>
                </SharePopover>
            </LogContextProvider>
        </CustomPopover>
    );
};
