/**
 * @file  智能体详细配置
 * <AUTHOR>
 */
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import lingjingIcon from '@/assets/lingjing-logo.png';
import {WorkflowNodeType} from '@/api/workflow/interface';
import deepSeekIcon from '@/assets/deepseek-logo.png';
import {getDigitalFigure, getWorkflowNodeName} from '../../utils';

// eslint-disable-next-line complexity
export const AgentConfig = () => {
    const {model, recommendedPlugins, recommendedWorkflows, digitalFigure, privateAssets, workflowInfo} =
        useAgentInfoStore(store => ({
            model: store.model,
            recommendedPlugins: store.recommendedPlugins,
            recommendedWorkflows: store.recommendedWorkflows,
            digitalFigure: store.digitalFigure,
            privateAssets: store.privateAssets,
            workflowInfo: store.workflowInfo,
        }));

    // 检查是否有任何数据存在
    const hasAnyData = Boolean(
        model ||
            (recommendedPlugins && recommendedPlugins.length > 0) ||
            (recommendedWorkflows && recommendedWorkflows.length > 0) ||
            (digitalFigure && digitalFigure.length > 0) ||
            (privateAssets && privateAssets.length > 0) ||
            (workflowInfo && Object.keys(workflowInfo).length > 0)
    );

    // 如果没有数据，则不渲染组件
    if (!hasAnyData) {
        return null;
    }

    const isDeepSeekModel = model && model.startsWith('DeepSeek');

    return (
        <div className="mt-6 font-medium">
            <div className="text-base">配置项</div>
            {model && (
                <div className="mt-[15px]">
                    <div className="text-sm text-gray-tertiary">模型</div>
                    <span className="mt-[6px] flex flex-wrap text-xs">
                        <img src={isDeepSeekModel ? deepSeekIcon : lingjingIcon} className="mr-[3px] h-4 w-4"></img>
                        {model}
                    </span>
                </div>
            )}

            {/* 工作流模式节点信息展现 */}
            {workflowInfo && Object.keys(workflowInfo).length > 0 && (
                <div className="mt-[18px]">
                    <div className="text-sm text-gray-tertiary">工作流模式</div>
                    <div className="flex flex-wrap text-xs">
                        {Object.entries(workflowInfo).map(([key, value]) => (
                            <div
                                key={key}
                                className="radius mr-[18px] mt-[9px] flex h-5 items-center rounded-[3px] bg-colorBgFormList px-[3px]"
                            >
                                <span className="text-[11px] leading-[17px]">
                                    {getWorkflowNodeName(key as WorkflowNodeType)}*{value}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* 推荐插件 */}
            {recommendedPlugins && recommendedPlugins.length > 0 && (
                <div className="mt-[18px]">
                    <div className="text-sm text-gray-tertiary">推荐插件</div>
                    <div className="flex flex-wrap text-xs">
                        {recommendedPlugins?.map(item => (
                            <span key={item.name} className="mr-4 mt-[9px] flex">
                                <img src={item.logoUrl} className="mr-[3px] h-4 w-4 rounded-[3px]"></img>
                                {item.name}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {/* 推荐工作流 */}
            {recommendedWorkflows && recommendedWorkflows.length > 0 && (
                <div className="mt-[18px]">
                    <div className="text-sm text-gray-tertiary ">推荐工作流</div>
                    <div className="flex flex-wrap text-xs">
                        {recommendedWorkflows?.map(item => (
                            <span key={item.name} className="mr-4 mt-[9px] flex">
                                <img src={item.logoUrl} className="mr-[3px] h-4 w-4 rounded-[3px]"></img>
                                {item.name}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {/* 角色形象 */}

            {digitalFigure && digitalFigure.length > 0 && (
                <div className="mt-[18px]">
                    <div className="text-sm text-gray-tertiary">角色形象</div>
                    <div className="flex flex-wrap text-xs">
                        {digitalFigure?.map(item => (
                            <span key={item} className="mr-4 mt-[9px] flex">
                                <img src={getDigitalFigure(item)} className="mr-[3px] h-4 w-4"></img>
                                {item}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {/* 私有资产 */}
            {privateAssets && privateAssets?.length > 0 && (
                <div className="mt-[18px]">
                    <div className="text-sm text-gray-tertiary">私有资产</div>
                    <div className="mt-[6px] flex flex-wrap text-xs">
                        {privateAssets?.map(item => (
                            <span
                                key={item.type}
                                className="mr-4 h-[19px] rounded-[3px] bg-colorBgInput px-[3px] py-[1px]"
                            >
                                {item.type}*{item.count}
                            </span>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};
