/**
 * @file  推荐对话组件
 * <AUTHOR>
 */
import {useCallback, useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import {RecommendedConversation} from '@/api/preview/interface';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {getRandomItems} from '../../utils';
interface Props {
    sendMessage: (message: string) => void;
}
export const RecommendDialog = ({sendMessage}: Props) => {
    const {recommendedConversations, name} = useAgentInfoStore(store => ({
        recommendedConversations: store.recommendedConversations,
        name: store.name,
    }));
    const {id} = useParams();
    const {clickLog} = useUbcLogV2();
    const [randomRecommend, setRandomRecommend] = useState<RecommendedConversation[]>([]);

    // 获取推荐对话
    const getRecommanded = useCallback(() => {
        // 随机选取三个推荐对话
        if (recommendedConversations && recommendedConversations.length > 0) {
            setRandomRecommend(getRandomItems(recommendedConversations, 3));
        }
    }, [recommendedConversations]);

    const getRecommandedClick = useCallback(() => {
        getRecommanded();
        // 点击换一换按钮，发送日志
        clickLog(EVENT_VALUE_CONST.REC_CARD_CHANGE, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
            [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
        });
    }, [clickLog, getRecommanded, id, name]);
    // 发送推荐对话
    const recommendedClick = useCallback(
        (item: string) => {
            sendMessage(item);
            // 点击推荐对话按钮，发送日志
            clickLog(EVENT_VALUE_CONST.REC_CONV_CARD, EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
                [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
                [EVENT_EXT_KEY_CONST.E_MESSAGE_ID]: item,
            });
        },
        [clickLog, id, name, sendMessage]
    );

    useEffect(() => {
        // 初始化时获取推荐对话
        getRecommanded();
    }, [clickLog, getRecommanded, id, name]);

    return randomRecommend.length ? (
        <div className="mb-[6px] mt-[24px]">
            <div className="flex items-center justify-between">
                <div className="text-base font-medium">推荐对话</div>
                {recommendedConversations && recommendedConversations.length > 3 && (
                    <div
                        className="flex cursor-pointer items-center text-sm text-primary"
                        onClick={getRecommandedClick}
                    >
                        <span className="iconfont icon-update mr-1"></span>
                        换一换
                    </div>
                )}
            </div>
            {/* 推荐对话列表 */}
            {randomRecommend.map(item => (
                <div
                    key={item.msgId}
                    onClick={() => recommendedClick(item.msgId)}
                    className="mt-[9px] cursor-pointer rounded-md border bg-colorBgInput p-[15px] hover:border-primary"
                >
                    <div className="line-clamp-2 w-full  whitespace-normal text-sm font-normal">{item.content}</div>
                </div>
            ))}
        </div>
    ) : null;
};
