/* eslint-disable complexity */
/**
 * @file  预览页右侧容器
 * <AUTHOR>
 */
import {useMemo} from 'react';
import classNames from 'classnames';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {CardTagName} from '@/modules/center/interface';
import {numFormat} from '@/utils/text';
import {isVertical} from '../../utils';
import {RecommendDialog} from './Recommend';
import {AgentConfig} from './AgentConfig';
interface Props {
    sendMessage: (message: string) => void;
}
export const PreviewInfoContainer = ({sendMessage}: Props) => {
    const {showUseNum, useNum, description, agentMedalInfo, tags, verticalCertificationInfo} = useAgentInfoStore(
        store => ({
            useNum: store.useNum,
            showUseNum: store.showUseNum,
            description: store.description,
            recommendedConversations: store.recommendedConversations,
            agentMedalInfo: store.agentMedalInfo,
            tags: store.tags,
            verticalCertificationInfo: store.verticalCertificationInfo,
        })
    );

    const showDescriptionPadding = useMemo(() => {
        return agentMedalInfo && agentMedalInfo.length > 0 && tags && tags.length > 0;
    }, [agentMedalInfo, tags]);

    return (
        <div className="p-6">
            {showUseNum && (
                <div className="flex items-center">
                    <span className="text-[21px] font-medium">{numFormat(+(useNum || 0))}</span>
                    <span className="ml-[5px] text-sm font-normal text-gray-tertiary">
                        {isVertical(verticalCertificationInfo) ? '人咨询过' : '人聊过'}
                    </span>
                </div>
            )}

            {((agentMedalInfo && agentMedalInfo.length > 0) || (tags && tags.length > 0)) && (
                <div className="mb-[15px] mt-[6px] flex flex-wrap items-center">
                    {agentMedalInfo &&
                        agentMedalInfo.length > 0 &&
                        agentMedalInfo.map(item => (
                            <span
                                className="mr-[18px] mt-[9px] flex h-[22px] items-center rounded-[200px] bg-colorBgFormList pl-[6px] pr-[6px]"
                                key={item.medalId}
                            >
                                <img src={item.level.img} className="h-4 w-4 bg-contain" />
                                <span className="h-[22px] pl-[3px] text-[12px] font-semibold leading-[22px] text-[#000]">
                                    {item.name}
                                </span>
                            </span>
                        ))}
                    {tags &&
                        tags.length > 0 &&
                        tags.map(tag =>
                            tag.name === CardTagName.tuningStar ? (
                                <span
                                    className="mr-[18px] mt-[9px] flex h-[22px] items-center rounded-[200px] bg-colorBgFormList pl-[6px] pr-[6px]"
                                    key={tag.name}
                                >
                                    <img
                                        src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/tuning_star.png"
                                        className="h-4 w-4 bg-contain"
                                    />
                                    <span className="h-[22px] pl-[3px] text-[12px] font-semibold leading-[22px] text-[#000]">
                                        调优之星
                                    </span>
                                </span>
                            ) : (
                                <span
                                    key={tag.name}
                                    className="ml-1 h-[22px] rounded-[3px] bg-[#FF82001A] p-[3px] text-xs font-medium leading-none text-[#E87400]"
                                >
                                    {tag.name}
                                </span>
                            )
                        )}
                </div>
            )}

            <div className={classNames(showDescriptionPadding ? 'mt-0' : 'mt-[15px]', 'text-sm')}>{description}</div>

            <RecommendDialog sendMessage={sendMessage} />
            <AgentConfig />
        </div>
    );
};
