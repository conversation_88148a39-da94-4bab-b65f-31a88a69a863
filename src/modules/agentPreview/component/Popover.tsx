import {useCallback, useEffect, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import CustomPopover, {type CustomPopoverProps} from '@/components/Popover';

export const DelayPopoVer = ({
    openShareTip,
    ...props
}: Exclude<CustomPopoverProps, 'open' | 'onClose'> & {openShareTip: boolean}) => {
    const [showHint, setShowHint] = useState(false);
    const [searchParams] = useSearchParams();
    const previewActiveTab = searchParams.get('previewActiveTab');

    const handleRemoveTip = useCallback(() => {
        setShowHint(false);
    }, []);

    // 5秒后显示提示
    useEffect(() => {
        const timer = setTimeout(() => {
            setShowHint(true);
        }, 5000);
        return () => {
            clearTimeout(timer);
        };
    }, []);

    useEffect(() => {
        // 路由切换后关闭提示
        handleRemoveTip();
    }, [handleRemoveTip, previewActiveTab]);

    return (
        <CustomPopover
            placement="bottom"
            // 显示分享引导时，不展示该引导，避免气泡重叠
            open={!openShareTip && showHint}
            type="primary"
            onClose={handleRemoveTip}
            zIndex={100}
            {...props}
        />
    );
};
