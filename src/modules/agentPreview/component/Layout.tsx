/**
 * @file 预览页面布局
 * <AUTHOR>
 */

import {Button, Layout, message} from 'antd';
import {useCallback, useEffect, useRef} from 'react';
import {useParams, useSearchParams, useNavigate} from 'react-router-dom';
import AgentPromptEditV2 from '@/modules/agentPromptEditV2/pc';
import api from '@/api/preview';
import {useAgentInfoStore} from '@/store/preview/agentInfo';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import Loading from '@/components/Loading';
import loginBackgroundUrl from '@/assets/login/preview-login-back.png';
import {resolveAgentError} from '@/utils/agentNotExistError';
import {ConfigTab, PreviewTab} from '../interface';
import {PreviewComponent} from '../PreviewComponent';
import {PreviewHeader} from './header';

import {PreviewInfoContainer} from './AgentInfo/PreviewInfoContainer';

const {Header, Sider, Content} = Layout;

export const PreviewLayout = () => {
    const previewComponentRef = useRef<HTMLIFrameElement | null>(null);
    const [searchParams] = useSearchParams();

    const {setAgentInfo, resetAgentInfo, shareTag, name} = useAgentInfoStore(store => ({
        setAgentInfo: store.setAgentInfo,
        resetAgentInfo: store.resetAgentInfo,
        shareTag: store.shareTag,
        name: store.name,
    }));

    const previewActiveTab = searchParams.get('previewActiveTab') || PreviewTab.Preview;

    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin();
    const navigate = useNavigate();

    const {id} = useParams();
    useEffect(() => {
        (async () => {
            if (id) {
                try {
                    // 获取agent信息
                    const agentInfo = await api.getAgentInfo({appid: id});
                    setAgentInfo(agentInfo);
                } catch (err: any) {
                    resolveAgentError(err, navigate);
                }
            }
        })();
        return () => {
            resetAgentInfo();
        };
    }, [id, setAgentInfo, navigate, resetAgentInfo]);

    // 发送消息给iframe
    const sendMessage = useCallback((msgId: string) => {
        if (!previewComponentRef.current) {
            message.info('对话加载中，请稍等');
            return;
        }

        // 嵌套了多层的iframe，需要指定targetOrigin
        const innerIframe = previewComponentRef.current.contentWindow?.frames['webswan-slave' as any];
        if (!innerIframe) {
            message.info('对话加载中，请稍等');
            return;
        }

        previewComponentRef.current.contentWindow?.frames['webswan-slave' as any]?.postMessage(
            {
                type: 'recommendDialog',
                data: {
                    msgId,
                },
            },
            '*'
        );
    }, []);

    const {displayLog} = useUbcLogV2();
    useEffect(() => {
        // 体验页展现埋点
        if (id && name) {
            displayLog(EVENT_PAGE_CONST.AGENT_EXPERIENCE, {
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: name,
                [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
            });
        }
    }, [displayLog, id, name]);

    return (
        <div className="relative h-full w-full min-w-[1280px] overflow-hidden">
            <Header className="h-[3.5rem] border-b border-colorBorderFormList bg-white   p-0">
                <PreviewHeader />
            </Header>
            {shareTag === undefined ? (
                <Loading />
            ) : shareTag === ConfigTab.copy && previewActiveTab === PreviewTab.Config ? (
                <AgentPromptEditV2 />
            ) : (
                <Layout
                    style={{
                        height: 'calc(100% - 3.5rem)',
                    }}
                >
                    <Content className="bg-gray-layout">
                        <PreviewComponent ref={previewComponentRef} />
                    </Content>
                    <Sider className=" min-w-[400px] overflow-auto border-l  border-colorBorderFormList bg-white">
                        <PreviewInfoContainer sendMessage={sendMessage} />
                    </Sider>
                </Layout>
            )}
            {!isLogin && (
                <div className="absolute bottom-0 left-0 z-20 flex h-[120px] w-full items-center justify-center bg-white">
                    <span className="text-xl font-medium text-[#272933]">
                        登录文心智能体平台，免费体验与智能体聊天！
                    </span>
                    <Button
                        className="z-10 ml-[35px] h-[47px] w-[164px] rounded-[221px] text-xl font-medium text-white"
                        type="primary"
                        onClick={uniformLogin}
                    >
                        登录体验
                    </Button>
                    <img src={loginBackgroundUrl} className="absolute bottom-0 left-0  w-[100%]"></img>
                </div>
            )}
        </div>
    );
};
