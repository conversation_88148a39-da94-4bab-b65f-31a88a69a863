import React, {
    Fragment,
    useCallback,
    useEffect,
    useMemo,
    useState,
    useRef,
    useLayoutEffect,
    forwardRef,
    LegacyRef,
} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {message, Modal} from 'antd';
import classNames from 'classnames';
import {css} from '@emotion/css';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import api from '@/api/myAgentPreview';
import urls from '@/links';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {ErrorResponseCode} from '@/api/error';
import {BuildType, GetAgentPreviewResponse} from '@/api/myAgentPreview/interface';
import {useThrowAsyncError} from '@/utils/monitor/useCommonError';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {ChatBoxPreviewIframe, PreviewContainer, SmartAppPreviewIframe} from './index-pc';
import InfoCard, {InfoCardProps} from './InfoCard';
import {handlePreviewUrl} from './getPreviewUrl';

const ConfigShareHint = ({
    className,
    agent,
    onClose,
    ...props
}: React.HTMLAttributes<HTMLDivElement> & {agent: GetAgentPreviewResponse; onClose: () => void}) => {
    const navigate = useNavigate();
    const {id} = useParams();
    const logExt = useMemo(() => {
        return {
            [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: agent.name,
        };
    }, [agent.name, id]);
    const {ubcClickLog, ubcShowLog} = useUbcLog();

    const handleViewConfig = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.previewConfigCheck, logExt);
        navigate(urls.agentPromptConfig.fill({id: id!}));
    }, [ubcClickLog, logExt, navigate, id]);

    // 点击【不再提示】，下次进入该智能体不会展示提示条，进入其他智能体会展示；
    const notRemindConfigShareTip = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.previewConfigMute, logExt);
        onClose();
    }, [onClose, ubcClickLog, logExt]);

    const closeConfigShareTip = useCallback(() => {
        ubcClickLog(EVENT_TRACKING_CONST.previewConfigClose, logExt);
        onClose();
    }, [onClose, ubcClickLog, logExt]);

    useEffect(() => {
        if (!id) {
            return;
        }

        ubcShowLog(EVENT_TRACKING_CONST.previewConfigNote, logExt);
    }, [id, logExt, ubcShowLog]);

    return (
        <div className={classNames('absolute top-6 flex justify-center text-center', className)} {...props}>
            <div className="inline-flex items-center rounded-[9px] border-[1px] border-gray-border-secondary bg-white px-3 py-[9px] text-sm leading-[22px] shadow-[0px_4px_10px_0px_#1E1F240F]">
                <span className="iconfont icon-tip-fill mr-2 cursor-pointer text-base text-primary"></span>
                该智能体已公开配置详情，
                <span className="cursor-pointer text-primary" onClick={handleViewConfig}>
                    点击查看
                </span>
                <div className="ml-8 cursor-pointer text-gray-tertiary">
                    <span onClick={notRemindConfigShareTip}>不再提示</span>
                    <span
                        className="iconfont icon-close ml-2 cursor-pointer text-sm text-gray-secondary"
                        onClick={closeConfigShareTip}
                    ></span>
                </div>
            </div>
        </div>
    );
};

interface PreviewComponentProps {
    url?: string;
}

const customInfoCardStyle = css`
    .ant-modal {
        width: 414px !important;
        .ant-modal-content {
            height: 100%;
            padding: 0 !important;
            box-shadow: none;
            .ant-modal-close {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background-color: #f5f6f9;
                top: 24px;
                inset-inline-end: 24px;
                .ant-modal-close-icon {
                    font-size: 16px;
                }
            }

            .ant-modal-body {
                height: 100%;
            }
        }
    }
`;

enum PreviewContainerEventType {
    ShowInfoCard = 'showInfoCard',
    NavigateToAgentEdit = 'navigateToAgentEdit',
    NavigateToAgentCenter = 'navigateToAgentCenter',
    ShowToast = 'showToast',
    ShowLoginModal = 'showLoginModal',
}

const BaiduSmartAppsDomain = '.smartapps.baidu.com';
const BaiduChatbotDomain = 'chat.baidu.com';

enum LoadingStatus {
    Loading,
    Error,
    Normal,
}

export const PreviewComponent = forwardRef(
    (Prop: PreviewComponentProps, ref: LegacyRef<HTMLIFrameElement> | undefined) => {
        const {url} = Prop;
        const navigate = useNavigate();
        const {id} = useParams();
        const [agent, setAgent] = useState<GetAgentPreviewResponse | null>(null);
        const previewUrl = useMemo(() => agent && handlePreviewUrl(agent), [agent]);
        const [loadingStatus, setLoadingStatus] = useState<LoadingStatus>(LoadingStatus.Loading);
        const [requestError, setRequestError] = useState<any>();
        const [isLogin] = useUserInfoStore(store => [store.isLogin]);
        const [infoCard, setInfoCard] = useState<InfoCardProps>({
            appDesc: '',
            appName: '',
            digitalFigure: {
                digitalType: 'normal',
                backgroundPrimaryColor: '',
            },
            creator: '',
            iconUrl: '',
            iconText: '',
            profilePageTag: [],
            analytics: {
                viewsCount: '',
                helpedUsersCount: '',
            },
        });
        const [isInfoCardOpen, setIsInfoCardOpen] = useState(false);
        const [containerWidth, setContainerWidth] = useState(0);
        const uniformLogin = useUniformLogin();

        const containerRef = useRef<HTMLDivElement>(null);

        const [showConfigShareHint, setShowConfigShareHint] = useState(false);

        const PreviewIframe = useMemo(() => {
            // previewUrl agent法律智能助手、金融智能助手预览使用子bot，其余使用c端web化
            if (!previewUrl) {
                return;
            }

            // 解析 URL 并获取域名和路径
            const urlObj = new URL(previewUrl);
            const hostname = urlObj.hostname;

            return hostname === BaiduChatbotDomain ? ChatBoxPreviewIframe : SmartAppPreviewIframe;
        }, [previewUrl]);

        // 跳转到编辑页面
        const navigateToEditPage = useCallback(() => {
            if (agent?.buildType === BuildType.Codeless) {
                navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Create}`);
            } else if (agent?.buildType === BuildType.LowCode) {
                navigate(urls.agentFlowEdit.fill({appId: agent.appId}));
            }
        }, [agent?.appId, agent?.buildType, navigate]);

        // 更新名片信息 展示名片
        const showInfoCard = (infoCard: InfoCardProps) => {
            setInfoCard(infoCard);
            setIsInfoCardOpen(true);
        };

        const throwAsyncError = useThrowAsyncError();

        const handleCloseHint = useCallback(() => setShowConfigShareHint(false), []);

        useLayoutEffect(() => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.offsetWidth);
            }
        }, []);

        useEffect(() => {
            (async () => {
                setShowConfigShareHint(false);
                setLoadingStatus(LoadingStatus.Loading);

                try {
                    const agentPreviewInfo = await api.getAgentPreview({appId: id!});
                    agentPreviewInfo && setAgent(agentPreviewInfo);
                } catch (error: any) {
                    setLoadingStatus(LoadingStatus.Error);
                    setRequestError(error);
                    // 智能体不存在不需要上报
                    if (error && +error?.errno === ErrorResponseCode.PREVIEWAGENTNOTEXIST) {
                        return message.open({
                            type: 'error',
                            content: `${error.errno}${error?.msg}`,
                        });
                    }

                    // 会让页面渲染boundary中的错误组件&会上报错误-谨慎使用
                    throwAsyncError(error);
                }
            })();
        }, [id, throwAsyncError, url]);

        useEffect(() => {
            const handleMessage = (event: MessageEvent) => {
                // 检查 origin
                if (!event.origin.endsWith(BaiduSmartAppsDomain)) {
                    return;
                }

                const {type, data} = event.data;

                switch (type) {
                    case PreviewContainerEventType.ShowInfoCard:
                        showInfoCard(data);
                        break;
                    case PreviewContainerEventType.NavigateToAgentEdit:
                        navigateToEditPage();
                        break;
                    case PreviewContainerEventType.NavigateToAgentCenter:
                        navigate(urls.center.raw());
                        break;
                    case PreviewContainerEventType.ShowToast:
                        message.open({
                            type: 'success',
                            content: data.content,
                        });
                        break;
                    case PreviewContainerEventType.ShowLoginModal:
                        uniformLogin();
                        break;
                    default:
                        break;
                }
            };

            window.addEventListener('message', handleMessage);

            return () => {
                window.removeEventListener('message', handleMessage);
            };
        }, [navigate, navigateToEditPage, uniformLogin]);

        const handleLoad = useCallback(() => {
            setLoadingStatus(LoadingStatus.Normal);
        }, []);

        const handleError = useCallback(() => {
            setLoadingStatus(LoadingStatus.Error);
        }, []);

        const previewPlaceholder = useMemo(() => {
            if (!previewUrl || !agent) {
                return <Loading />;
            }

            if (loadingStatus === LoadingStatus.Error) {
                return <RenderError error={requestError} />;
            }

            return (
                <Fragment>
                    {loadingStatus === LoadingStatus.Loading && <Loading />}
                    {PreviewIframe && [
                        <PreviewIframe
                            ref={ref}
                            key="preview-iframe"
                            src={previewUrl}
                            onLoad={handleLoad}
                            onError={handleError}
                            allow="microphone"
                            className={classNames({hidden: loadingStatus === LoadingStatus.Loading})}
                            isLogin={isLogin}
                        />,
                    ]}
                    {showConfigShareHint && (
                        <ConfigShareHint
                            // 191 是提示条宽度的一半
                            style={{left: containerWidth > 0 ? containerWidth / 2 - 191 : -999}}
                            onClose={handleCloseHint}
                            agent={agent}
                        />
                    )}
                </Fragment>
            );
        }, [
            PreviewIframe,
            agent,
            containerWidth,
            handleCloseHint,
            handleError,
            handleLoad,
            isLogin,
            loadingStatus,
            requestError,
            previewUrl,
            ref,
            showConfigShareHint,
        ]);

        const closeInfoCard = useCallback(() => {
            setIsInfoCardOpen(false);
        }, []);

        return (
            <PreviewContainer ref={containerRef} className="relative">
                {previewPlaceholder}
                {/* 卡片 */}
                <Modal
                    wrapClassName={customInfoCardStyle}
                    style={{height: infoCard.profilePageTag && infoCard.profilePageTag.length > 0 ? '368px' : '336px'}}
                    open={isInfoCardOpen}
                    centered
                    footer={null}
                    onCancel={closeInfoCard}
                >
                    <InfoCard {...infoCard} />
                </Modal>
            </PreviewContainer>
        );
    }
);
