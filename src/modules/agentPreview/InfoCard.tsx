import styled from '@emotion/styled';
import classNames from 'classnames';
import React, {memo, useState} from 'react';
import type {CSSProperties, FC} from 'react';
import {Tag} from 'antd';
import {Link, LinkProps} from 'react-router-dom';

interface CustomDivProps {
    color: CSSProperties['color'];
    digitalType: 'normal' | 'human';
}

// 十六进制转 rgb
const hexToRgb = (hex: string) => {
    // 移除开头的#符号(如果存在)
    const newHex = hex.replace(/^#/, '');

    // 将三个颜色值分别转换为十进制
    const r = parseInt(newHex.substring(0, 2), 16);
    const g = parseInt(newHex.substring(2, 4), 16);
    const b = parseInt(newHex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
};

const CustomDiv = styled.div<CustomDivProps>`
    background: ${({color, digitalType}) => {
        const backOpacity = digitalType === 'normal' ? '0.5' : '0.25';
        // 生成渐变字符串，透明度从0开始，到指定的结束透明度
        return `linear-gradient(to bottom,  rgba(${hexToRgb(color as string)}, 0), rgba(${hexToRgb(
            color as string
        )}, ${backOpacity})); border-radius: 18px`;
    }};
    .desc {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
    }
    .ai-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(99.63deg, #a64dff 18%, #3366ff 99.05%);
        border-radius: 3px;
    }
    .ant-tag {
        padding: 4px 6px;
        line-height: 11px;
        background: transparent;
        font-weight: 500;
        color: #1e1f24;
        border: 0.5px solid rgba(17, 0, 0, 0.2);
        border-radius: 3px;
        a {
            display: flex;
            align-content: center;
            color: #1e1f24;
        }
    }
`;

interface ProfileTag {
    title: string;
    url: string;
}

type DigitalType = 'normal' | 'human';

interface Analytics {
    viewsCount: string;
    helpedUsersCount: string;
}

interface DigitalFigure {
    digitalType: DigitalType;
    backgroundPrimaryColor: CSSProperties['color'];
}

export interface InfoCardProps {
    appDesc: string;
    appName: string;
    creator: string;
    iconUrl: string;
    iconText: string;
    digitalFigure: DigitalFigure;
    profilePageTag: ProfileTag[];
    analytics: Analytics;
}

const extractNumberAndUnit = (str: string) => {
    // 匹配数字部分
    const numberMatch = /[\d|.?]+/.exec(str);
    // 匹配"万"字
    const unitMatch = /万/.exec(str);

    const number = numberMatch ? numberMatch[0] : '';
    const unit = unitMatch ? unitMatch[0] : '';

    return [number, unit];
};

const InfoCard: FC<InfoCardProps> = memo(
    ({appName, appDesc, digitalFigure, analytics, iconUrl, iconText, profilePageTag}) => {
        const digitalType = digitalFigure ? 'human' : 'normal';
        const backgroundPrimaryColor = digitalFigure?.backgroundPrimaryColor || '#BFDAFF';
        const [viewsCount, viewUnit] = extractNumberAndUnit(analytics.viewsCount);
        const [helpedUsersCount, helpedUnit] = extractNumberAndUnit(analytics.helpedUsersCount);
        const [hasError, setHasError] = useState(false);

        const handleError = () => {
            setHasError(true);
        };

        return (
            <CustomDiv
                color={backgroundPrimaryColor}
                digitalType={digitalType}
                className="relative flex h-full w-full flex-col items-center"
            >
                <div className="absolute left-[50%] top-[5px] h-[186px] w-[186px] translate-x-[-50%] translate-y-[-50%] overflow-hidden rounded-full">
                    <div className="relative">
                        {hasError || iconUrl === '' ? (
                            <span
                                className={classNames(
                                    'rounded-full',
                                    'flex',
                                    'justify-center',
                                    'items-center',
                                    'font-medium',
                                    'leading-snug',
                                    'font-pingfang',
                                    'group-hover:scale-[1.117]',
                                    'duration-300',
                                    'bg-white',
                                    'h-[186px]',
                                    'w-[186px]'
                                )}
                            >
                                {iconText}
                            </span>
                        ) : (
                            <img
                                className="h-[186px] w-[186px] rounded-full bg-white"
                                src={iconUrl}
                                alt={iconText}
                                onError={handleError}
                            />
                        )}
                        <div className="absolute left-[0] top-[0] box-border h-[186px] w-[186px] rounded-full border-[0.5px] border-solid border-black opacity-5"></div>
                    </div>
                </div>
                <div className="font-['PingFang SC'] mt-[136px] flex w-[334px] flex-col items-center">
                    <div className="text-black-base mb-2 flex h-[34px] w-[320px] items-center justify-center  font-semibold leading-[1.25]">
                        <div className="truncate text-[30px] ">{appName}</div>
                        <div className="ml-[6px] h-[16px] w-[39px] shrink-0 bg-agent-tag bg-cover"></div>
                    </div>
                    <div className="text-black-base flex h-[27px] w-full justify-center truncate text-base font-normal leading-[1.25]">
                        {/* 对话次数 */}
                        <div className="flex items-center">
                            <div className="flex items-center">
                                <span className="font-['baidunumber_Medium'] text-[21px] font-medium">
                                    {viewsCount}
                                </span>
                                <span className="font-['PingFang SC'] mt-[-1px] text-[18px] font-medium">
                                    {viewUnit}
                                </span>
                            </div>
                            <span className="ml-1 text-[14px] font-normal opacity-60">对话次数</span>
                        </div>
                        <div className="w-[18px]"></div>
                        {/* 使用人数 */}
                        <div className="flex items-center">
                            <div className="flex items-center">
                                <span className="font-['baidunumber_Medium'] text-[21px] font-medium">
                                    {helpedUsersCount}
                                </span>
                                <span className="font-['PingFang SC'] mt-[-1px] text-[18px] font-medium">
                                    {helpedUnit}
                                </span>
                            </div>
                            <span className="ml-1 text-[14px] font-normal opacity-60">使用人数</span>
                        </div>
                    </div>
                    {profilePageTag && profilePageTag.length > 0 && (
                        <div className="mt-[8px] flex  w-full justify-center text-neutral-800">
                            {profilePageTag.map((pageTag, index) => (
                                <Tag
                                    key={pageTag.title}
                                    className={classNames({'pr-[3px]': index === profilePageTag.length - 1})}
                                >
                                    <Link to={pageTag.url as LinkProps['to']} target="_blank">
                                        {pageTag.title}
                                        <span
                                            className={classNames('text-[12px]', {
                                                'iconfont icon-right': index === profilePageTag.length - 1,
                                            })}
                                        ></span>
                                    </Link>
                                </Tag>
                            ))}
                        </div>
                    )}
                    <div className="desc mt-[30px] line-clamp-2 w-[99%] whitespace-normal text-center text-base font-normal leading-[25px] text-neutral-800">
                        {appDesc}
                    </div>
                </div>
            </CustomDiv>
        );
    }
);
export default InfoCard;
