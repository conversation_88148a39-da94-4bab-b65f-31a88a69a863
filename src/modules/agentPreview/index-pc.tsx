import styled from '@emotion/styled';
import {CacheProvider} from 'react-suspense-boundary';
import React from 'react';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Header from '@/components/Header';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {PreviewLayout} from './component/Layout';

export const PreviewContainer = styled.div`
    overflow: hidden;
    display: flex;
    position: relative;
    width: 100%;
    min-width: 656px;
    height: 100%;
`;

export const ChatBoxPreviewIframe = styled.iframe`
    width: calc(100% + 300px);
    height: calc(100% + 42px);
    position: absolute;
    top: -42px;
    left: -300px;
`;

export const SmartAppPreviewIframe = styled.iframe<{isLogin: boolean}>`
    width: 100%;
    // 未登录时，需要将整体上移，已保证人物智能体能正常显示推荐对话
    height: ${props => (props.isLogin ? 'calc(100% + 71px)' : 'calc(100% + 31px)')};
    position: absolute;
    top: -71px;
`;

const renderError = (error: any) => {
    return (
        <div>
            <Header />
            <RenderError error={error} />
        </div>
    );
};

/**
 * 预览页
 */
export default function PreviewComponents() {
    return (
        <CacheProvider>
            <CommonErrorBoundary pendingFallback={<Loading />} renderError={renderError}>
                <PreviewLayout />
            </CommonErrorBoundary>
        </CacheProvider>
    );
}
