/**
 * 处理 preview url 的公共方法
 * <AUTHOR>
 */

import type {GetAgentPreviewResponse} from '@/api/myAgentPreview/interface';
import {useAgentPreviewSceneStore} from '@/store/agentPreviewScene/useAgentPreviewSceneStore';

export const handlePreviewUrl = (agentPreviewInfo: GetAgentPreviewResponse) => {
    const {agentWebScene} = useAgentPreviewSceneStore.getState();
    // 创建一个URL对象
    const urlObject = new URL(agentPreviewInfo.previewUrl);
    // 预览者是否为开发者
    urlObject.searchParams.set('_chatIsOwner', agentPreviewInfo.isOwner ? '1' : '0');
    // web化-PC-灵境平台
    urlObject.searchParams.set('_chatSource', 'lingjing');
    // 预览路口区分
    urlObject.searchParams.set('_swebScene', agentWebScene);
    // 兼容旧版web化-web化新版上线后删除
    urlObject.searchParams.set('source', 'lingjing');
    urlObject.searchParams.set('isOwner', agentPreviewInfo.isOwner ? '1' : '0');

    // 返回更新后的URL字符串
    return urlObject.toString();
};
