import activeUrl from '@/assets/face.png';
import audioUrl from '@/assets/mic.png';
import {WorkflowNodeType} from '@/api/workflow/interface';
import {VerticalCertificationInfo, VerticalCertificationTagCateCode} from '@/api/preview/interface';

// 获取随机数组
export const getRandomItems = (arr: any, index: number) => {
    if (!arr || arr.length === 0) {
        return [];
    }

    const arrCopy = [...arr];
    // 打乱数组
    const shuffled = arrCopy.sort(() => 0.5 - Math.random());
    // 取前面三个
    return shuffled.slice(0, index);
};

export const DIGITALFIGURE = {
    ACTIVE: '动态形象',
    STATIC: '静态形象',
    AUDIO: '语音形象',
};

// 工作流节点名称
export const WORKFLOW_NODE_NAME: Record<WorkflowNodeType, string> = {
    [WorkflowNodeType.START]: '开始',
    [WorkflowNodeType.END]: '结束',
    [WorkflowNodeType.BRANCH]: '选择器',
    [WorkflowNodeType.LLM]: '文心大模型',
    [WorkflowNodeType.HTTP]: 'HTTP',
    [WorkflowNodeType.KNOWLEDGE]: '知识库',
    [WorkflowNodeType.TOOL]: '插件',
    [WorkflowNodeType.TEXT]: '文本处理',
    [WorkflowNodeType.CODE]: '代码',
    [WorkflowNodeType.VARIABLE]: '变量',
    [WorkflowNodeType.MESSAGE]: '消息',
    [WorkflowNodeType.WORKFLOW]: '工作流',
    [WorkflowNodeType.SUGGESTION]: '追问',
    [WorkflowNodeType.INTENTION]: '意图识别',
    [WorkflowNodeType.PARAMS_EXTRACT]: '参数提取',
};

// 获取角色形象icon链接
export const getDigitalFigure = (name: string) => {
    if (name === DIGITALFIGURE.ACTIVE) {
        return activeUrl;
    } else if (name === DIGITALFIGURE.AUDIO) {
        return audioUrl;
    } else {
        return activeUrl;
    }
};

// 获取工作流节点名称
export const getWorkflowNodeName = (key: WorkflowNodeType) => {
    return WORKFLOW_NODE_NAME[key];
};

// 判断是否是垂类认证
export const isVertical = (info?: VerticalCertificationInfo[]) => {
    if (!info || info.length === 0) {
        return false;
    }

    return info.some(
        item =>
            item.tagCateCode === VerticalCertificationTagCateCode.Q2C_LAWYER_CREDIT ||
            item.tagCateCode === VerticalCertificationTagCateCode.DOCTOR_CREDIT_CATE
    );
};
