/**
 * @file 表单相关的 hooks
 * <AUTHOR>
 */
import {Form} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useLocation, useNavigate, useSearchParams} from 'react-router-dom';
import dayjs from 'dayjs';
import {css} from '@emotion/css';
import api from '@/api/activityForm';
import {
    ActivityFormDetail,
    ActivityFormItem,
    ActivityFormItemConditionOperatorType,
    ActivityFormItemTimeConditionItem,
    ActivityFormStatus,
} from '@/api/activityForm/interface';
import {formatFormData} from '@/modules/activityForm/utils';
import urls from '@/links';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import useLogin from '@/components/Login/hooks/useLogin';

export enum FormRequestStatus {
    Loading = 'loading',
    Success = 'success',
    Failed = 'failed',
}

export const FormStyle = css`
    .ant-form-item-explain-error {
        margin-top: 9px;
    }
`;

/**
 * 表单页 hook
 */
export function useFormHook() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const location = useLocation();
    // 表单 id
    const formId = searchParams.get('formId');
    // 活动 id
    const activityId = searchParams.get('activityId');
    // 渠道
    const source = searchParams.get('source');
    const sourceParamsString = source ? `?source=${source}` : '';
    // 表单实例
    const [form] = Form.useForm();
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {loginCheck} = useLogin();

    // 请求状态
    const [requestStatus, setRequestStatus] = useState(FormRequestStatus.Loading);
    const [requestError, setRequestError] = useState<any>();
    // 表单数据
    const [formData, setFormData] = useState<ActivityFormDetail>();
    // 默认禁用表单，状态为 “进行中” 时才可以操作表单
    const [isFormDisabled, setFormDisabled] = useState(true);

    // 表单项的 name 和对应配置的 map，便于通过 name 快速找到对应的配置数据
    const formContentsMap = useMemo(() => {
        const formContents: Record<string, ActivityFormItem> = {};
        return formData?.content?.reduce((pre, cur) => {
            pre[cur.name] = cur;
            if (cur.body?.length) {
                cur.body.forEach(item => {
                    pre[item.name] = item;
                });
            }
            return pre;
        }, formContents);
    }, [formData?.content]);

    // 请求表单数据
    const requestFormDetail = useCallback(async () => {
        if (!formId) {
            setRequestStatus(FormRequestStatus.Failed);
            setRequestError(new Error('参数异常，缺少formId'));
            return;
        }

        try {
            setRequestStatus(FormRequestStatus.Loading);
            const res = await api.getFormDetail({formId});
            setFormData(res);
            setRequestStatus(FormRequestStatus.Success);
            res.status === ActivityFormStatus.InProgress && setFormDisabled(false);
        } catch (error) {
            setRequestStatus(FormRequestStatus.Failed);
            setRequestError(error);
        }
    }, [formId]);

    // 表单变更后，校验是否需要登录
    const onFormChange = useCallback(() => {
        !isLogin && loginCheck();
    }, [isLogin, loginCheck]);

    // 表单提交
    const submit = useCallback(async () => {
        if (!isLogin) {
            loginCheck();
            return Promise.reject(new Error('未登录'));
        }

        // 滚动到第一个校验错误的表单项
        form.submit();
        const fields = await form.validateFields({recursive: true});
        const submitData = formatFormData(formContentsMap!, fields);
        return await api.submitForm({formId: formId!, formData: submitData, activityId, channel: source});
    }, [isLogin, loginCheck, activityId, form, formContentsMap, formId, source]);

    // 跳转活动详情页
    const navigateToActivityDetail = useCallback(() => {
        navigate(`${urls.activityDetail.fill({id: activityId!})}${sourceParamsString}`, {replace: true});
    }, [activityId, sourceParamsString, navigate]);

    const navigateBack = useCallback(() => {
        if (location.key === 'default' || window.history.length === 1) {
            // 有活动id时返回活动详情页，否则返回列表页
            if (activityId) {
                navigateToActivityDetail();
            } else {
                navigate(urls.activityList.raw());
            }
        } else {
            navigate(-1);
        }
    }, [location.key, activityId, navigateToActivityDetail, navigate]);

    useEffect(() => {
        // 请求表单数据
        requestFormDetail();
    }, [requestFormDetail]);

    return {
        formId,
        activityId,
        form,
        submit,
        requestStatus,
        formData,
        formContentsMap,
        navigateBack,
        isFormDisabled,
        setFormDisabled,
        onFormChange,
        navigateToActivityDetail,
        requestError,
    };
}

/**
 * 获取时间条件描述
 *
 * @param condition 时间条件
 * @param prefix 描述前缀，"首次创建时间" 或 "首次发布时间"
 */
function getConditionDesc(condition: ActivityFormItemTimeConditionItem[], prefix: string) {
    const formatTemplate = 'YYYY年MM月DD日HH时mm分ss秒';
    const afterTime = condition.find(item => item.opera === ActivityFormItemConditionOperatorType.gt);
    const beforeTime = condition.find(item => item.opera === ActivityFormItemConditionOperatorType.lt);

    if (afterTime && beforeTime) {
        return `${prefix}在${dayjs(beforeTime.time * 1000).format(formatTemplate)}至${dayjs(
            afterTime.time * 1000
        ).format(formatTemplate)}`;
    }

    if (afterTime) {
        return `${prefix}晚于${dayjs(afterTime.time * 1000).format(formatTemplate)}`;
    }

    if (beforeTime) {
        return `${prefix}早于${dayjs(beforeTime.time * 1000).format(formatTemplate)}`;
    }
}

/**
 * 获取表单项组件 hook
 *
 * @param option 表单项配置
 */
export function useFormItemHook(option: ActivityFormItem) {
    // 表单实例
    const form = Form.useFormInstance();
    // 表单项的值
    const value = Form.useWatch([option.name], form);
    // 表单项标题结尾展示的文本
    const suffix = useMemo(() => {
        if (option.requireMin && option.requireMax) {
            return `请选择${option.requireMin}-${option.requireMax}项`;
        } else if (option.requireMin) {
            return `最少选${option.requireMin}项`;
        } else if (option.requireMax) {
            return `最多选${option.requireMax}项`;
        }

        return '';
    }, [option.requireMin, option.requireMax]);

    // 智能体选择器用到的可选条件
    const question = useMemo(() => {
        if (!option.condition?.firstCreateTime?.length && !option.condition?.firstPublishTime?.length) {
            return;
        }

        const descArr = [];
        if (option.condition?.firstCreateTime?.length) {
            descArr.push(getConditionDesc(option.condition.firstCreateTime, '首次创建时间'));
        }

        if (option.condition?.firstPublishTime?.length) {
            descArr.push(getConditionDesc(option.condition.firstPublishTime, '首次发布时间'));
        }

        return `按活动要求，已筛选出${descArr.join('、')}的智能体`;
    }, [option.condition]);

    return {value, suffix, question};
}
