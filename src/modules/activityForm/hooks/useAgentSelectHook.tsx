/**
 * @file PC 端和移动端公用的智能体选择器逻辑
 * <AUTHOR>
 */
import {useCallback, useEffect, useMemo, useState} from 'react';
import {Checkbox, Radio} from 'antd';
import {useSearchParams} from 'react-router-dom';
import nth from 'lodash/nth';
import api from '@/api/activityForm';
import {ActivityFormItem, ActivityFormItemAgentListItem} from '@/api/activityForm/interface';
import urls from '@/links';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';

export function useAgentSelectHook({
    value,
    option,
    disabled,
    isMobile,
}: {
    // 表单项的值
    value: string[];
    // 是否禁用
    disabled?: boolean;
    // 表单项的配置
    option: ActivityFormItem;
    // 是否是移动端
    isMobile?: boolean;
}) {
    const [searchParams] = useSearchParams();
    const formId = searchParams.get('formId');
    const isLogin = useUserInfoStore(store => store.isLogin);
    // 存储请求到的智能体列表
    const [agentList, setAgentList] = useState<ActivityFormItemAgentListItem[]>([]);

    // 下拉选择器的 options 数据
    const options = useMemo(() => {
        return agentList.map(item => ({
            label: item.name,
            value: item.appId,
            // 已选择的选项数量达到 requireMax 上限值后，将未被选择的选项置灰不可点
            disabled:
                disabled ||
                (!!option.requireMax &&
                    option.multiple &&
                    value?.length >= option.requireMax &&
                    !value?.includes(item.appId)),
        }));
    }, [agentList, disabled, option.multiple, option.requireMax, value]);

    // 挑战智能体编辑页
    const toAgentEdit = useCallback((appId: string) => {
        // setTimeout 是为了避免 safari 拦截打开新标签页
        const timer = setTimeout(() => {
            window.open(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}`, '_blank');
            clearTimeout(timer);
        }, 10);
    }, []);

    // 自定义渲染下拉选择的选项
    const optionRender = useCallback(
        (item: any) => {
            const isSelected = option.multiple ? value?.includes(item.value) : item.value === value;

            return (
                <div className="flex w-full items-center justify-between px-0.5">
                    <div className="flex items-center">
                        {option.multiple ? (
                            <Checkbox className="mr-[6px]" checked={isSelected} />
                        ) : (
                            <Radio className="mr-[6px]" checked={isSelected} />
                        )}
                        {item.label}
                    </div>
                    {isMobile ? (
                        <span
                            className="-mr-3 text-primary"
                            onClick={e => {
                                e.stopPropagation();
                                toAgentEdit(item.value);
                            }}
                        >
                            查看
                        </span>
                    ) : (
                        <span
                            className="iconfont icon-eye cursor-pointer text-[#1F1F1F]"
                            onClick={e => {
                                e.stopPropagation();
                                toAgentEdit(item.value);
                            }}
                        />
                    )}
                </div>
            );
        },
        [option.multiple, value, isMobile, toAgentEdit]
    );

    // 渲染下拉选择器中被选择的标签，用'，'分割被选择的智能体名称
    const tagRender = useCallback(
        (item: any) => {
            const isLast = nth(value, -1) === item.value;
            return (
                <span>
                    {item.label}
                    {isLast ? '' : '，'}
                </span>
            );
        },
        [value]
    );

    // 请求当前用户的智能体列表
    useEffect(() => {
        if (!formId || !isLogin) {
            setAgentList([]);
            return;
        }

        api.getFormAgentList({formId, name: option.name}).then(res => {
            setAgentList(res);
        });
    }, [formId, isLogin, option.name]);

    return {
        agentList,
        options,
        tagRender,
        optionRender,
    };
}
