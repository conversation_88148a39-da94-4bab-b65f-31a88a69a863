/**
 * @file 活动详情页 - PC 端
 * <AUTHOR>
 */
import {CacheProvider} from 'react-suspense-boundary';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import ActivityForm from '@/modules/activityForm/components/pc/ActivityForm';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';

export default function IndexPc() {
    return (
        <div>
            <CacheProvider>
                <CommonErrorBoundary pendingFallback={<Loading />}>
                    <LogContextProvider page={EVENT_PAGE_CONST.ACTIVITY_FORM}>
                        <ActivityForm />
                    </LogContextProvider>
                </CommonErrorBoundary>
            </CacheProvider>
        </div>
    );
}
