/**
 * @file 多行输入题
 * <AUTHOR>
 */
import {Form} from 'antd';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import Textarea from '@/components/Input/Textarea';
import {getInputValidatorRule} from '@/modules/activityForm/utils';

const DefaultMaxLength = 50;

export default function ActivityFormTextarea({index, option}: ActivityFormItemProps) {
    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            description={option.placeholder}
        >
            <Form.Item name={[option.name, 'value']} rules={[getInputValidatorRule(option, DefaultMaxLength)]}>
                <Textarea
                    placeholder="请输入"
                    showCount
                    maxLength={option.max || DefaultMaxLength}
                    autoSize={{minRows: 1, maxRows: 3}}
                />
            </Form.Item>
        </FormItemContainer>
    );
}
