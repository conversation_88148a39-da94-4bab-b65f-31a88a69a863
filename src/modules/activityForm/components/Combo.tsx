/**
 * @file 组合组件
 * <AUTHOR>
 */
import {Form} from 'antd';
import {Fragment} from 'react';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import Input from '@/components/Input';

export default function ActivityFormCombo({index, option}: ActivityFormItemProps) {
    return (
        <FormItemContainer index={index} title={option.label}>
            {option?.body?.map(item => (
                <Fragment key={item.name}>
                    <div className="mb-[6px]">
                        {item.required && (
                            <span className="mr-[6px] text-error" style={{fontFamily: 'SimSong'}}>
                                *
                            </span>
                        )}
                        {item.label}
                    </div>
                    <Form.Item
                        className="mb-3"
                        name={[item.name, 'value']}
                        rules={[{required: item.required, message: '请输入'}]}
                    >
                        <Input placeholder={item.placeholder || '请输入'} showCount maxLength={item.max || 20} />
                    </Form.Item>
                </Fragment>
            ))}
        </FormItemContainer>
    );
}
