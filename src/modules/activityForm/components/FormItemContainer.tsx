/**
 * @file 表单项容器组件
 * <AUTHOR>
 */
import {ReactNode, useCallback, useState} from 'react';
import {Tooltip} from 'antd';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import PopupM from '@/components/mobile/Popup';

interface Props {
    // 下标
    index: number;
    // 标题
    title: string | ReactNode;
    // 标题后面 【】括号内的文本
    suffix?: string;
    // 标题后面的移动端点击或 PC 端 hover "问号" 时出现的描述文本
    question?: string;
    // 标题下方的描述文本
    description?: string;
    // 是否必填
    required?: boolean;
    children: ReactNode;
}

export default function FormItemContainer({index, title, suffix, question, description, required, children}: Props) {
    const [showPopup, setShowPopup] = useState(false);
    // "问号" 的交互区分移动端和 PC 端，PC 端 hover 出 Tooltip、移动端点击弹出 PopupM
    const isMobile = isMobileDevice();

    // 移动端弹出 PopupM
    const openQuestionContent = useCallback(() => {
        setShowPopup(true);
    }, []);

    // 移动端关闭 PopupM
    const closeQuestionContent = useCallback(() => {
        setShowPopup(false);
    }, []);

    return (
        <div className="mb-6">
            <div>
                {required && (
                    <span className="mr-[6px] text-error" style={{fontFamily: 'SimSong'}}>
                        *
                    </span>
                )}
                <span className="text-base font-medium text-colorTextDefault">
                    {index + 1}. {title}
                </span>
                {!!suffix?.length && <span className="ml-[6px] text-gray-tertiary">【{suffix}】</span>}
                {!!question?.length &&
                    (isMobile ? (
                        <span
                            className="iconfont icon-questionCircle ml-[6px] text-sm text-[#A7B4C5]"
                            onClick={openQuestionContent}
                        />
                    ) : (
                        <Tooltip title={question} overlayStyle={{maxWidth: 404}}>
                            <span className="iconfont icon-questionCircle ml-[6px] cursor-pointer text-sm text-[#A7B4C5]" />
                        </Tooltip>
                    ))}
            </div>
            {!!description?.length && <div className="mt-[6px] text-sm text-gray-secondary">{description}</div>}
            <div className="mt-[13px] max-w-[552px]">{children}</div>
            {isMobile && (
                <PopupM
                    visible={showPopup}
                    title="提示说明"
                    headerClassName="leading-[18px] -mb-[6px]"
                    showCloseButton
                    onClose={closeQuestionContent}
                    closeOnMaskClick
                >
                    <div className="text-sm text-black">{question}</div>
                </PopupM>
            )}
        </div>
    );
}
