/**
 * @file 活动结束状态的组件
 * <AUTHOR>
 */
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import urls from '@/links';
import LoadError from '@/components/Loading/LoadError';

export default function ActivityCompleted({
    className,
    hideButton,
    children,
}: {
    className?: string;
    hideButton?: boolean;
    children?: React.ReactNode;
}) {
    const navigate = useNavigate();

    const toActivityList = useCallback(() => {
        navigate(urls.activityList.raw());
    }, [navigate]);

    return (
        <div className={classNames('flex h-[100vh] w-full flex-col bg-gray-bg-base', className)}>
            {children}
            <LoadError
                tips="活动已关闭"
                extra="活动已结束，无法填写"
                btnText="参与更多活动"
                hideBtn={hideButton}
                onBtnClick={toActivityList}
            />
        </div>
    );
}
