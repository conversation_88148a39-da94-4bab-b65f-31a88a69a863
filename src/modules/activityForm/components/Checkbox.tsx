/**
 * @file 多选题
 * <AUTHOR>
 */
import {Form, Checkbox} from 'antd';
import {useMemo} from 'react';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Input from '@/components/Input';
import {getCheckboxValidatorRule} from '@/modules/activityForm/utils';

export default function ActivityFormCheckbox({index, disabled, option}: ActivityFormItemProps) {
    const {value, suffix} = useFormItemHook(option);
    const defaultValue = useMemo(() => {
        return option.options?.filter(item => item.isDefault)?.map(item => item.value) || [];
    }, [option.options]);

    return (
        <FormItemContainer index={index} title={option.label} required={option.required} suffix={suffix || '多选题'}>
            <Form.Item
                name={[option.name, 'value']}
                initialValue={defaultValue}
                rules={[getCheckboxValidatorRule(option)]}
            >
                <Checkbox.Group className="block">
                    {option.options?.map(item => (
                        <div className="mb-3 last:mb-0" key={item.value}>
                            <Checkbox
                                className="flex leading-5"
                                value={item.value}
                                disabled={
                                    disabled ||
                                    (!!option.requireMax &&
                                        value?.value?.length >= option.requireMax &&
                                        !value?.value?.includes(item.value))
                                }
                            >
                                {item.value}
                            </Checkbox>
                            {!!item.needInput && value?.value?.includes(item.value) && (
                                <Form.Item
                                    className="mt-[9px]"
                                    name={[option.name, 'inputValues', item.value]}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder={item.placeholder || '请输入'}
                                        showCount
                                        maxLength={20}
                                        prefix={
                                            <span className="mr-0.5 text-error" style={{fontFamily: 'SimSong'}}>
                                                *
                                            </span>
                                        }
                                    />
                                </Form.Item>
                            )}
                        </div>
                    ))}
                </Checkbox.Group>
            </Form.Item>
        </FormItemContainer>
    );
}
