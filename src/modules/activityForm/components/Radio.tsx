/**
 * @file 单选题
 * <AUTHOR>
 */
import {Form, Radio} from 'antd';
import {useMemo} from 'react';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Input from '@/components/Input';

export default function ActivityFormRadio({index, option}: ActivityFormItemProps) {
    const {value} = useFormItemHook(option);

    const defaultValue = useMemo(() => {
        return option.options?.find(item => item.isDefault)?.value;
    }, [option.options]);

    return (
        <FormItemContainer index={index} title={option.label} required={option.required}>
            <Form.Item
                name={[option.name, 'value']}
                initialValue={defaultValue}
                rules={[{required: option.required, message: '请选择'}]}
            >
                <Radio.Group className="block">
                    {option.options?.map(item => (
                        <div className="mb-3 last:mb-0" key={item.value}>
                            <Radio className="w-full leading-5" value={item.value}>
                                {item.value}
                            </Radio>
                            {!!item.needInput && value?.value === item.value && (
                                <Form.Item
                                    className="mt-[9px]"
                                    name={[option.name, 'inputValues', item.value]}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder={item.placeholder || '请输入'}
                                        showCount
                                        maxLength={20}
                                        prefix={
                                            <span className="mr-0.5 text-error" style={{fontFamily: 'SimSong'}}>
                                                *
                                            </span>
                                        }
                                    />
                                </Form.Item>
                            )}
                        </div>
                    ))}
                </Radio.Group>
            </Form.Item>
        </FormItemContainer>
    );
}
