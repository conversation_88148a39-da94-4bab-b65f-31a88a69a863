/**
 * @file
 * <AUTHOR>
 */
import {Avatar, Button, Modal, Space} from 'antd';
import React from 'react';
import styled from '@emotion/styled';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import useLogin from '@/components/Login/hooks/useLogin';
import useLogoutModal from '@/components/Login/hooks/useLogoutModal';
import Popover from '@/components/Popover';

const StyleModal = styled(Modal)`
    .ant-modal-content {
        border-radius: 21px !important;
        padding: 21px;
    }
    .ant-modal-header {
        margin: 0 !important;
    }
    .ant-modal-title {
        color: #1e1f24 !important;
        font-weight: 500 !important;
        font-size: 18px !important;
        line-height: 24px !important;
        margin-bottom: 3px !important;
    }
    .ant-modal-footer {
        margin-top: 18px !important;
    }
`;

const Header = ({navigateBack}: {navigateBack: () => void}) => {
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {loginCheck} = useLogin();
    const {logoutClickHandler, modalOpen, logoutHandler, cancelLogoutHandler} = useLogoutModal();

    return (
        <div className="flex w-full items-center justify-between bg-white px-6 py-[14px]">
            <span className="flex items-center text-black">
                <span className="iconfont icon-left mr-6 cursor-pointer text-xl" onClick={navigateBack} />
                <span className="text-sm font-medium">参与活动</span>
            </span>
            {isLogin ? (
                <Popover
                    placement="bottom"
                    arrow={false}
                    overlayInnerStyle={{padding: '6px'}}
                    overlayStyle={{
                        borderRadius: '9px',
                        boxShadow: '0px 6px 21px 0px rgba(0, 0, 0, 0.10)',
                    }}
                    content={
                        <div
                            className="flex h-[30px] w-[80px] cursor-pointer items-center justify-center rounded-[6px] bg-colorBgFormList text-sm text-black"
                            style={{margin: '0 auto'}}
                            onClick={logoutClickHandler}
                        >
                            退出登录
                        </div>
                    }
                >
                    <Avatar src={userInfoData?.userInfo.portrait} style={{width: 28, height: 28}} />
                    <span className="ml-[9px] text-sm font-medium">{userInfoData?.userInfo.name}</span>
                </Popover>
            ) : (
                <Button type="primary" className="h-[30px] px-[11px] leading-none" onClick={loginCheck}>
                    登录
                </Button>
            )}
            <StyleModal
                centered
                title="你确定要退出吗？"
                open={modalOpen}
                onCancel={cancelLogoutHandler}
                width={400}
                closable={false}
                footer={
                    <Space size={[6, 0]}>
                        <Button
                            onClick={logoutHandler}
                            type="default"
                            className="h-[30px] px-[14px] font-medium leading-none"
                        >
                            退出
                        </Button>
                        <Button
                            onClick={cancelLogoutHandler}
                            type="primary"
                            className="h-[30px] px-[14px] font-medium leading-none"
                        >
                            保持登录
                        </Button>
                    </Space>
                }
            >
                <p className="leading-[22px] text-colorTextDefault">退出后，将无法使用完整功能</p>
            </StyleModal>
        </div>
    );
};

export default Header;
