/**
 * @file PC 端日期区间选择器
 * <AUTHOR>
 */
import {ConfigProvider, DatePicker, Form} from 'antd';
import {CalendarOutlined} from '@ant-design/icons';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {DateFormat} from '@/utils/date';
import {datePickerToken} from '@/styles/component-token';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);

export default function ActivityFormDateRange({index, option}: ActivityFormItemProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    DatePicker: datePickerToken,
                },
            }}
        >
            <FormItemContainer index={index} title={option.label} required={option.required}>
                <Form.Item
                    name={[option.name, 'value']}
                    rules={[{required: option.required, message: '请选择日期区间'}]}
                >
                    <DatePicker.RangePicker
                        className="w-[270px] text-black"
                        format={DateFormat.YMD_HYPHEN}
                        suffixIcon={<CalendarOutlined className="text-black" />}
                    />
                </Form.Item>
            </FormItemContainer>
        </ConfigProvider>
    );
}
