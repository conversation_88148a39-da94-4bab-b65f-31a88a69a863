/**
 * @file PC 端日期选择器
 * <AUTHOR>
 */
import {ConfigProvider, DatePicker, Form} from 'antd';
import {CalendarOutlined} from '@ant-design/icons';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {DateFormat} from '@/utils/date';
import {datePickerToken} from '@/styles/component-token';

export default function ActivityFormDatetime({index, option}: ActivityFormItemProps) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    DatePicker: datePickerToken,
                },
            }}
        >
            <FormItemContainer index={index} title={option.label} required={option.required}>
                <Form.Item name={[option.name, 'value']} rules={[{required: option.required, message: '请选择日期'}]}>
                    <DatePicker
                        className="w-[270px] text-black"
                        placeholder="选择日期"
                        format={DateFormat.YMD_HYPHEN}
                        suffixIcon={<CalendarOutlined className="text-black" />}
                    />
                </Form.Item>
            </FormItemContainer>
        </ConfigProvider>
    );
}
