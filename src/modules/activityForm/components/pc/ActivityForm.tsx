/**
 * @file PC 端表单页
 * <AUTHOR>
 */
import {<PERSON><PERSON>, But<PERSON>, ConfigProvider, Form, Modal} from 'antd';
import React, {Fragment, useCallback, useEffect, useMemo} from 'react';
import {css} from '@emotion/css';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import ActivityFormRadio from '@/modules/activityForm/components/Radio';
import ActivityFormCheckbox from '@/modules/activityForm/components/Checkbox';
import ActivityFormCheckboxOrder from '@/modules/activityForm/components/CheckboxOrder';
import ActivityFormTextarea from '@/modules/activityForm/components/Textarea';
import ActivityFormText from '@/modules/activityForm/components/Text';
import ActivityFormSelect from '@/modules/activityForm/components/pc/Select';
import ActivityFormDatetime from '@/modules/activityForm/components/pc/Datetime';
import ActivityFormDateRange from '@/modules/activityForm/components/pc/DateRange';
import ActivityFormInputFile from '@/modules/activityForm/components/InputFile';
import ActivityFormAgentSelect from '@/modules/activityForm/components/pc/AgentSelect';
import ActivityFormCombo from '@/modules/activityForm/components/Combo';
import Header from '@/modules/activityForm/components/pc/Header';
import {ActivityFormItemType, ActivityFormStatus} from '@/api/activityForm/interface';
import {FormRequestStatus, FormStyle, useFormHook} from '@/modules/activityForm/hooks/useFormHook';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import ActivityCompleted from '@/modules/activityForm/components/ActivityCompleted';
import urls from '@/links';
import {buttonToken} from '@/styles/component-token';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';

const FormItemComponentMap = {
    [ActivityFormItemType.Radio]: ActivityFormRadio,
    [ActivityFormItemType.Checkbox]: ActivityFormCheckbox,
    [ActivityFormItemType.Text]: ActivityFormText,
    [ActivityFormItemType.Textarea]: ActivityFormTextarea,
    [ActivityFormItemType.Select]: ActivityFormSelect,
    [ActivityFormItemType.CheckboxOrder]: ActivityFormCheckboxOrder,
    [ActivityFormItemType.Datetime]: ActivityFormDatetime,
    [ActivityFormItemType.DateRange]: ActivityFormDateRange,
    [ActivityFormItemType.InputFile]: ActivityFormInputFile,
    [ActivityFormItemType.AgentSelect]: ActivityFormAgentSelect,
    [ActivityFormItemType.Combo]: ActivityFormCombo,
};

export default function ActivityForm() {
    const navigate = useNavigate();
    const {displayLog} = useUbcLogV3();

    const {
        formId,
        form,
        submit,
        requestStatus,
        formData,
        navigateBack,
        activityId,
        isFormDisabled,
        setFormDisabled,
        onFormChange,
        navigateToActivityDetail,
        requestError,
    } = useFormHook();
    const [modal, modalContextHolder] = Modal.useModal();

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    const imgList = useMemo(() => {
        return formData?.img?.filter(item => item?.url?.length || item?.desc?.length);
    }, [formData?.img]);

    const submitAction = useCallback(async () => {
        await submit();
        // 提交完成后提示 “提交成功”
        const isConfirm = await modal.confirm({
            icon: <Fragment />,
            content: (
                <div className="flex items-center">
                    <span className="iconfont icon-check-circle-fill mr-[6px] font-normal text-[#39B362]" />
                    <span className="text-lg font-medium text-black">提交成功</span>
                </div>
            ),
            cancelText: '取消',
            okText: '确定',
            centered: true,
            autoFocusButton: null,
            className: css`
                .ant-modal-content {
                    padding: 1.5rem !important;

                    .ant-modal-confirm-btns {
                        margin-top: 24px;
                    }
                }
            `,
        });
        // 如果是活动相关的表单页（页面参数中存在 activityId 表示活动相关的表单页），点击 “确认” 后跳转到活动页
        if (isConfirm && activityId) {
            navigateToActivityDetail();
        }

        // 如果不是活动相关的表单页（页面参数中不存在 activityId 表示非活动相关的表单页），点击 “确认” 后跳转到活动列表页
        if (isConfirm && !activityId) {
            navigate(urls.activityList.raw(), {replace: true});
        }

        // 表单置灰
        setFormDisabled(true);
    }, [submit, modal, activityId, navigateToActivityDetail, navigate, setFormDisabled]);

    if (requestStatus === FormRequestStatus.Loading) {
        return <Loading />;
    }

    if (requestStatus === FormRequestStatus.Failed) {
        return (
            <div>
                <Header navigateBack={navigateBack} />
                <RenderError error={requestError} />
            </div>
        );
    }

    if (formData?.status === ActivityFormStatus.Completed) {
        return (
            <ActivityCompleted>
                <Header navigateBack={navigateBack} />
            </ActivityCompleted>
        );
    }

    return (
        <ConfigProvider theme={{components: {Button: buttonToken, Form: {itemMarginBottom: 0}}}}>
            <Header navigateBack={navigateBack} />
            <div className="h-[calc(100vh-56px)] overflow-y-scroll">
                <div className="flex w-full items-center justify-center bg-gray-bg-base pb-[118px] pt-8">
                    <div className="flex min-w-[852px] max-w-[852px] gap-6">
                        <Form
                            form={form}
                            layout="vertical"
                            className={classNames('h-auto flex-1 rounded-[18px] bg-white p-6', FormStyle)}
                            disabled={isFormDisabled}
                            onChange={onFormChange}
                            scrollToFirstError={{behavior: 'instant', block: 'center'}}
                        >
                            <div className="mb-8">
                                {formData?.status === ActivityFormStatus.NotStarted && (
                                    <Alert
                                        className="mb-6 py-[5px]"
                                        message="当前活动暂未开始"
                                        type="error"
                                        icon={<span className="iconfont icon-info-circle-fill mr-[6px]" />}
                                        showIcon
                                    />
                                )}
                                <div className="mb-[9px] text-[21px] font-medium leading-[27px] text-black">
                                    {formData?.title}
                                </div>
                                {!!formData?.subTitle?.length && (
                                    <div className="line-clamp-2 text-sm text-gray-secondary">{formData?.subTitle}</div>
                                )}
                            </div>
                            {formData?.content?.map((item, index) => {
                                const Component = FormItemComponentMap[item.type];
                                if (Component) {
                                    return (
                                        <Component
                                            key={item.name}
                                            index={index}
                                            formId={formId!}
                                            disabled={isFormDisabled}
                                            option={item}
                                        />
                                    );
                                }

                                return null;
                            })}
                            <div className="pt-2 text-right">
                                <Button
                                    className="h-[30px] text-sm font-medium leading-none"
                                    type="primary"
                                    onClick={submitAction}
                                >
                                    提交
                                </Button>
                            </div>
                        </Form>
                        {!!imgList?.length && (
                            <div className="sticky top-8 flex h-max w-[228px] flex-col gap-6">
                                {imgList.map(item => (
                                    <div key={item?.url} className="w-full rounded-[18px] bg-white px-6 py-4">
                                        {!!item?.desc?.length && (
                                            <span className="mb-[15px] inline-block leading-[22px] text-[#272933]">
                                                {item.desc}
                                            </span>
                                        )}
                                        {!!item.url?.length && (
                                            <img className="h-[180px] w-[180px] rounded-[9px]" src={item.url} />
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {modalContextHolder}
        </ConfigProvider>
    );
}
