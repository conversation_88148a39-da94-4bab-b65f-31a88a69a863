/**
 * @file PC 端下拉选择器
 * <AUTHOR>
 */
import {Form} from 'antd';
import {useMemo} from 'react';
import {css} from '@emotion/css';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Select from '@/components/Select';
import {getSelectValidatorRule} from '@/modules/activityForm/utils';
import themeConfig from '@/styles/lingjing-light-theme';

const selectStyle = css`
    &.ant-select .ant-select-selection-placeholder {
        color: ${themeConfig.token?.colorTextTertiary} !important;
    }
`;

export default function ActivityFormSelect({index, option}: ActivityFormItemProps) {
    const {suffix} = useFormItemHook(option);

    const defaultValue = useMemo(() => {
        if (option.multiple) {
            return option.options?.filter(item => item.isDefault)?.map(item => item.value) || [];
        }
        return option.options?.find(item => item.isDefault)?.value;
    }, [option.multiple, option.options]);

    const options = useMemo(() => {
        return option.options?.map(item => ({
            label: item.value,
            value: item.value,
        }));
    }, [option.options]);

    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            suffix={option.multiple ? suffix : undefined}
        >
            <Form.Item
                name={[option.name, 'value']}
                initialValue={defaultValue}
                rules={[getSelectValidatorRule(option)]}
            >
                <Select
                    className={selectStyle}
                    options={options}
                    mode={option.multiple ? 'multiple' : undefined}
                    placeholder="请选择"
                />
            </Form.Item>
        </FormItemContainer>
    );
}
