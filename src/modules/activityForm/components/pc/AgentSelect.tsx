/**
 * @file PC 端智能体选择器
 * <AUTHOR>
 */
import {Empty, Form} from 'antd';
import {css} from '@emotion/css';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Select from '@/components/Select';
import {getSelectValidatorRule} from '@/modules/activityForm/utils';
import {useAgentSelectHook} from '@/modules/activityForm/hooks/useAgentSelectHook';
import themeConfig from '@/styles/lingjing-light-theme';

const selectStyle = css`
    &.ant-select {
        .ant-select-selector {
            padding-left: 12px;
        }
        .ant-select-selection-placeholder {
            color: ${themeConfig.token?.colorTextTertiary} !important;
        }
    }
`;

export default function ActivityFormAgentSelect({index, disabled, option}: ActivityFormItemProps) {
    const {value, suffix, question} = useFormItemHook(option);
    const {options, optionRender, tagRender} = useAgentSelectHook({
        value: value?.value,
        disabled,
        option,
    });

    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            suffix={option.multiple ? suffix : undefined}
            question={question}
        >
            <Form.Item name={[option.name, 'value']} rules={[getSelectValidatorRule(option)]}>
                <Select
                    className={selectStyle}
                    options={options}
                    mode={option.multiple ? 'multiple' : undefined}
                    placeholder="请选择"
                    removeIcon={false}
                    menuItemSelectedIcon={<span />}
                    optionRender={optionRender}
                    tagRender={tagRender}
                    notFoundContent={
                        <Empty
                            className="p-3"
                            imageStyle={{height: 44}}
                            description={<span className="p-3 text-gray-tertiary">暂无符合条件的智能体</span>}
                        />
                    }
                />
            </Form.Item>
        </FormItemContainer>
    );
}
