/**
 * @file 单行输入题
 * <AUTHOR>
 */
import {Form} from 'antd';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import Input from '@/components/Input';
import {getInputValidatorRule} from '@/modules/activityForm/utils';

const DefaultMaxLength = 20;

export default function ActivityFormText({index, option}: ActivityFormItemProps) {
    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            description={option.placeholder}
        >
            <Form.Item name={[option.name, 'value']} rules={[getInputValidatorRule(option, DefaultMaxLength)]}>
                <Input placeholder="请输入" showCount maxLength={option.max || DefaultMaxLength} />
            </Form.Item>
        </FormItemContainer>
    );
}
