/**
 * @file 移动端 Header
 * <AUTHOR>
 */
import {Avatar} from 'antd';
import classNames from 'classnames';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import ModalM from '@/components/mobile/Modal';
import AvatarPopover from '@/modules/home/<USER>/AvatarPopover';
import useLogin from '@/components/Login/hooks/useLogin';
import useLogoutModal from '@/components/Login/hooks/useLogoutModal';

const Header = ({className, title}: {className?: string; title: string}) => {
    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {loginCheck} = useLogin();
    const {logoutClickHandler, modalOpen, logoutHandler, cancelLogoutHandler} = useLogoutModal();
    const LogoutModal = (
        <ModalM open={modalOpen} okText="退出" cancelText="取消" onOk={logoutHandler} onCancel={cancelLogoutHandler}>
            <p className="px-[38px] text-center text-black opacity-90">退出登录将无法继续使用智能体，确认退出吗？</p>
        </ModalM>
    );

    return (
        <div
            className={classNames(
                'z-[999] flex w-full items-center justify-between bg-gray-bg-base py-[11px]',
                className
            )}
        >
            <span className="w-[13px]" />
            <span className="flex-1">{title}</span>
            {isLogin ? (
                <AvatarPopover
                    height="104"
                    placement="bottomRight"
                    overlayStyle={{
                        width: '172px',
                        height: '96px',
                    }}
                    overlayInnerStyle={{
                        padding: '0',
                        boxShadow: '0px 2px 30px 0px #1D22520F',
                        borderRadius: '9px',
                    }}
                    arrow={false}
                    logoutBtn={
                        <div
                            className="mb-[9px] flex h-9 w-[132px] items-center justify-center rounded-[9px] bg-[#EBECFD] text-sm font-medium text-primary"
                            style={{margin: '0 auto'}}
                            onClick={logoutClickHandler}
                        >
                            退出登录
                        </div>
                    }
                >
                    <Avatar src={userInfoData?.userInfo.portrait} style={{width: 28, height: 28}} />
                </AvatarPopover>
            ) : (
                <div className="h-[28px] font-medium leading-[28px] text-[#4E6EF2]" onClick={loginCheck}>
                    登录
                </div>
            )}
            {LogoutModal}
        </div>
    );
};

export default Header;
