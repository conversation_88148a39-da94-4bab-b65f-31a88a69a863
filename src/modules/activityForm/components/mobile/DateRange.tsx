/**
 * @file 移动端日期区间选择器
 * <AUTHOR>
 */
import {Button, ConfigProvider, Form} from 'antd';
import {CalendarPickerView} from 'antd-mobile';
import {Fragment, useCallback, useEffect, useState} from 'react';
import dayjs from 'dayjs';
import {CalendarOutlined, SwapRightOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import classNames from 'classnames';
import {css} from '@emotion/css';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import PopupM from '@/components/mobile/Popup';
import {buttonToken, datePickerToken} from '@/styles/component-token';
import {DateFormat} from '@/utils/date';

const StyledCalendarPickerView = styled(CalendarPickerView)`
    &.adm-calendar-picker-view {
        overflow: hidden;
        .adm-calendar-picker-view-header {
            display: none;
        }
    }
`;

const DateRangeContainerStyle = css`
    & {
        .placeholder {
            color: #b7b9c1;
        }
        &.disabled {
            background: ${datePickerToken.colorBgContainerDisabled};

            .placeholder {
                color: ${datePickerToken.colorTextDisabled};
            }
        }
    }
`;

// 区间选择器容器组件，用于触发 Form.item 组件的 onChange
function DateRangeWrapper({
    value,
    disabled,
    onChange,
}: {
    // 表单项数据
    value?: [dayjs.Dayjs, dayjs.Dayjs];
    // 是否禁用
    disabled?: boolean;
    // 表单项数据变更回调
    onChange?: (value: [dayjs.Dayjs, dayjs.Dayjs] | []) => void;
}) {
    // 是否显示 popup
    const [visible, setVisible] = useState(false);
    // 当前日期区间
    const [currentDateRange, setCurrentDateRange] = useState<[Date, Date] | []>([]);

    // 打开 popup
    const openPopup = useCallback(() => {
        !disabled && setVisible(true);
    }, [disabled]);

    // 关闭 popup
    const closePopup = useCallback(() => {
        setVisible(false);
    }, []);

    // 日期区间变更回调，变更后设置 currentDateRange state，点击 “确认” 时将 currentDateRange 通过 onChange 传递给 Form.Item
    const onCalendarPickerViewChange = useCallback((value: [Date, Date] | null) => {
        setCurrentDateRange(value || []);
    }, []);

    // 点击 “确认” 时触发
    const onConfirm = useCallback(() => {
        onChange?.([dayjs(currentDateRange?.[0]), dayjs(currentDateRange?.[1])]);
        closePopup();
    }, [onChange, currentDateRange, closePopup]);

    useEffect(() => {
        if (value?.length) {
            setCurrentDateRange([value[0]?.toDate(), value[1]?.toDate()]);
        } else {
            setCurrentDateRange([new Date(), new Date()]);
        }
    }, [value]);

    return (
        <ConfigProvider theme={{components: {Button: buttonToken}}}>
            <div
                className={classNames(
                    'flex w-[270px] items-center justify-between rounded-[6px] border-[1px] px-[11px] py-[5px] text-sm text-black',
                    {disabled},
                    DateRangeContainerStyle
                )}
                onClick={openPopup}
            >
                {value?.length ? (
                    <Fragment>
                        <span>{value[0]?.format(DateFormat.YMD_HYPHEN)}</span>
                        <SwapRightOutlined />
                        <span>{value[1]?.format(DateFormat.YMD_HYPHEN)}</span>
                    </Fragment>
                ) : (
                    <Fragment>
                        <span className="placeholder">开始日期</span>
                        <SwapRightOutlined className="placeholder" />
                        <span className="placeholder">结束日期</span>
                    </Fragment>
                )}
                <CalendarOutlined className="text-black" />
            </div>
            <PopupM
                title="年月日选择器"
                visible={visible}
                onClose={closePopup}
                showCloseButton
                closeOnMaskClick
                bodyStyle={{
                    paddingLeft: 0,
                    paddingRight: 0,
                }}
            >
                <StyledCalendarPickerView
                    className="h-[400px]"
                    selectionMode="range"
                    onChange={onCalendarPickerViewChange}
                    min={dayjs().subtract(1, 'year').toDate()}
                    max={dayjs().add(1, 'year').toDate()}
                />
                <footer className="mb-6 flex w-full border-t-[1px] border-colorBorderFormList px-[18px] pt-[15px]">
                    <Button type="primary" className="h-10 w-full font-medium" onClick={onConfirm}>
                        确认
                    </Button>
                </footer>
            </PopupM>
        </ConfigProvider>
    );
}

export default function ActivityFormDateRange({index, disabled, option}: ActivityFormItemProps) {
    return (
        <FormItemContainer index={index} title={option.label} required={option.required}>
            <Form.Item name={[option.name, 'value']} rules={[{required: option.required, message: '请选择日期区间'}]}>
                <DateRangeWrapper disabled={disabled} />
            </Form.Item>
        </FormItemContainer>
    );
}
