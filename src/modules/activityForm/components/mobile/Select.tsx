/**
 * @file 移动端下拉选择器（选项以 Popup 形式弹出）
 * <AUTHOR>
 */
import {Checkbox, Form, Radio} from 'antd';
import {useCallback, useMemo} from 'react';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Select from '@/components/Select/index-m';
import {getSelectValidatorRule} from '@/modules/activityForm/utils';

export default function ActivityFormSelect({index, disabled, option}: ActivityFormItemProps) {
    const {value, suffix} = useFormItemHook(option);

    const defaultValue = useMemo(() => {
        if (option.multiple) {
            return option.options?.filter(item => item.isDefault)?.map(item => item.value) || [];
        }
        return option.options?.find(item => item.isDefault)?.value;
    }, [option.multiple, option.options]);

    const options = useMemo(() => {
        return option.options?.map(item => ({
            label: item.value,
            value: item.value,
        }));
    }, [option.options]);

    // 自定义渲染下拉选择的选项
    const optionRender = useCallback(
        (item: any) => {
            const isSelected = option.multiple ? value?.value?.includes(item.value) : item.value === value?.value;

            return (
                <div className="flex w-full items-center px-0.5">
                    {option.multiple ? (
                        <Checkbox className="mr-[6px]" checked={isSelected} />
                    ) : (
                        <Radio className="mr-[6px]" checked={isSelected} />
                    )}
                    <div>{item.label}</div>
                </div>
            );
        },
        [option.multiple, value]
    );

    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            suffix={option.multiple ? suffix : undefined}
        >
            <Form.Item
                name={[option.name, 'value']}
                initialValue={defaultValue}
                rules={[getSelectValidatorRule(option)]}
            >
                <Select
                    disabled={disabled}
                    options={options}
                    mode={option.multiple ? 'multiple' : undefined}
                    placeholder="请选择"
                    menuItemSelectedIcon={<span />}
                    optionRender={optionRender}
                />
            </Form.Item>
        </FormItemContainer>
    );
}
