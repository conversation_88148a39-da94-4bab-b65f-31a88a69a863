/**
 * @file 移动端智能体选择器
 * <AUTHOR>
 */
import {Empty, Form} from 'antd';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import Select from '@/components/Select/index-m';
import {getSelectValidatorRule} from '@/modules/activityForm/utils';
import {useAgentSelectHook} from '@/modules/activityForm/hooks/useAgentSelectHook';

export default function ActivityFormAgentSelect({index, disabled, option}: ActivityFormItemProps) {
    const {value, suffix, question} = useFormItemHook(option);
    const {options, optionRender, tagRender} = useAgentSelectHook({
        disabled,
        value: value?.value,
        option,
        isMobile: true,
    });

    return (
        <FormItemContainer
            index={index}
            title={option.label}
            required={option.required}
            suffix={option.multiple ? suffix : undefined}
            question={question}
        >
            <Form.Item name={[option.name, 'value']} rules={[getSelectValidatorRule(option)]}>
                <Select
                    disabled={disabled}
                    options={options}
                    mode={option.multiple ? 'multiple' : undefined}
                    placeholder="请选择"
                    menuItemSelectedIcon={<span />}
                    optionRender={optionRender}
                    tagRender={tagRender}
                    notFoundContent={
                        <Empty
                            className="p-3"
                            imageStyle={{height: 44}}
                            description={<span className="p-3 text-gray-tertiary">暂无符合条件的智能体</span>}
                        />
                    }
                />
            </Form.Item>
        </FormItemContainer>
    );
}
