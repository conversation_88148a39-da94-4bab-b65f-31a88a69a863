/**
 * @file 移动端表单页
 * <AUTHOR>
 */
import {<PERSON><PERSON>, Button, ConfigProvider, Form} from 'antd';
import React, {useCallback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {ActivityFormItemType, ActivityFormStatus} from '@/api/activityForm/interface';
import ActivityFormRadio from '@/modules/activityForm/components/Radio';
import ActivityFormCheckbox from '@/modules/activityForm/components/Checkbox';
import ActivityFormCheckboxOrder from '@/modules/activityForm/components/CheckboxOrder';
import ActivityFormTextarea from '@/modules/activityForm/components/Textarea';
import ActivityFormText from '@/modules/activityForm/components/Text';
import ActivityFormSelect from '@/modules/activityForm/components/mobile/Select';
import ActivityFormDatetime from '@/modules/activityForm/components/mobile/Datetime';
import ActivityFormDateRange from '@/modules/activityForm/components/mobile/DateRange';
import ActivityFormInputFile from '@/modules/activityForm/components/InputFile';
import ActivityFormAgentSelect from '@/modules/activityForm/components/mobile/AgentSelect';
import ActivityFormCombo from '@/modules/activityForm/components/Combo';
import Header from '@/modules/activityForm/components/mobile/Header';
import {FormRequestStatus, useFormHook} from '@/modules/activityForm/hooks/useFormHook';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import ActivityCompleted from '@/modules/activityForm/components/ActivityCompleted';
import ModalM from '@/components/mobile/Modal';
import urls from '@/links';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';

const FormItemComponentMap = {
    [ActivityFormItemType.Radio]: ActivityFormRadio,
    [ActivityFormItemType.Checkbox]: ActivityFormCheckbox,
    [ActivityFormItemType.Text]: ActivityFormText,
    [ActivityFormItemType.Textarea]: ActivityFormTextarea,
    [ActivityFormItemType.Select]: ActivityFormSelect,
    [ActivityFormItemType.CheckboxOrder]: ActivityFormCheckboxOrder,
    [ActivityFormItemType.Datetime]: ActivityFormDatetime,
    [ActivityFormItemType.DateRange]: ActivityFormDateRange,
    [ActivityFormItemType.InputFile]: ActivityFormInputFile,
    [ActivityFormItemType.AgentSelect]: ActivityFormAgentSelect,
    [ActivityFormItemType.Combo]: ActivityFormCombo,
};

export default function ActivityForm() {
    const navigate = useNavigate();
    const {
        formId,
        activityId,
        form,
        submit,
        requestStatus,
        formData,
        isFormDisabled,
        setFormDisabled,
        onFormChange,
        navigateToActivityDetail,
        requestError,
    } = useFormHook();
    const [showModal, setShowModal] = useState(false);

    const {displayLog} = useUbcLogV3();

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    // 提交表单
    const submitAction = useCallback(async () => {
        await submit();
        setShowModal(true);
    }, [submit]);

    // 提交完成后点击 "确认" 后跳转页面
    const onModalOk = useCallback(() => {
        // 如果是活动相关的表单页（页面参数中存在 activityId 表示活动相关的表单页），点击 “确认” 后跳转到活动页
        if (activityId) {
            navigateToActivityDetail();
        }
        // 如果不是活动相关的表单页（页面参数中不存在 activityId 表示非活动相关的表单页），点击 “确认” 后跳转到体验中心
        else {
            navigate(urls.center.raw(), {replace: true});
        }

        setFormDisabled(true);
    }, [activityId, navigate, navigateToActivityDetail, setFormDisabled]);

    // 提交完成后点击 "取消"，则关闭 “提交成功” 弹窗，并将表单置灰
    const onModalCancel = useCallback(() => {
        setShowModal(false);
        setFormDisabled(true);
    }, [setFormDisabled]);

    if (requestStatus === FormRequestStatus.Loading) {
        return <Loading />;
    }

    if (requestStatus === FormRequestStatus.Failed) {
        return (
            <div>
                <Header className="text-left text-sm text-gray-tertiary" title="欢迎填写活动表单" />
                <RenderError error={requestError} />
            </div>
        );
    }

    if (formData?.status === ActivityFormStatus.Completed) {
        return (
            <ActivityCompleted className="px-[13px]" hideButton>
                <Header className="text-left text-sm text-gray-tertiary" title="欢迎填写活动表单" />
            </ActivityCompleted>
        );
    }

    return (
        <ConfigProvider theme={{components: {Form: {itemMarginBottom: 0}}}}>
            <div className="w-full bg-gray-bg-base px-[13px]">
                <Header className="text-left text-sm text-gray-tertiary" title="欢迎填写活动表单" />
                <div className="h-[calc(100vh-50px)] overflow-y-scroll">
                    <Form
                        form={form}
                        layout="vertical"
                        className="mb-[37px] rounded-[18px] bg-white p-[13px]"
                        disabled={isFormDisabled}
                        onChange={onFormChange}
                        scrollToFirstError={{behavior: 'instant', block: 'center'}}
                    >
                        <div className="mb-6">
                            {formData?.status === ActivityFormStatus.NotStarted && (
                                <Alert
                                    className="mb-[15px] py-1"
                                    message="当前活动暂未开始"
                                    type="error"
                                    icon={<span className="iconfont icon-info-circle-fill mr-[6px]" />}
                                    showIcon
                                />
                            )}
                            <div className="mb-[6px] text-[21px] font-medium leading-[27px] text-black">
                                {formData?.title}
                            </div>
                            {!!formData?.subTitle?.length && (
                                <div className="line-clamp-2 text-sm leading-[22px] text-gray-secondary">
                                    {formData?.subTitle}
                                </div>
                            )}
                        </div>
                        {formData?.content?.map((item, index) => {
                            const Component = FormItemComponentMap[item.type];
                            if (Component) {
                                return (
                                    <Component
                                        key={item.name}
                                        index={index}
                                        formId={formId!}
                                        disabled={isFormDisabled}
                                        option={item}
                                    />
                                );
                            }

                            return null;
                        })}
                        <div className="pt-2">
                            <Button
                                className="h-[50px] w-full rounded-xl text-base font-medium"
                                type="primary"
                                onClick={submitAction}
                            >
                                提交
                            </Button>
                        </div>
                    </Form>
                </div>
            </div>
            <ModalM okText="确定" cancelText="取消" open={showModal} onOk={onModalOk} onCancel={onModalCancel}>
                <div className="flex items-center justify-center">
                    <span className="iconfont icon-check-circle-fill mr-[6px] font-normal text-[#39B362]" />
                    <span className="text-base text-black">提交成功</span>
                </div>
            </ModalM>
        </ConfigProvider>
    );
}
