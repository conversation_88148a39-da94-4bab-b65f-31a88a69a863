/**
 * @file 移动端日期选择器
 * <AUTHOR>
 */
import {Button, ConfigProvider, Form} from 'antd';
import {DatePickerView} from 'antd-mobile';
import {useCallback, useEffect, useState} from 'react';
import dayjs from 'dayjs';
import {PickerDate} from 'antd-mobile/es/components/date-picker/util';
import {CalendarOutlined} from '@ant-design/icons';
import {css} from '@emotion/css';
import classNames from 'classnames';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import PopupM from '@/components/mobile/Popup';
import {buttonToken, datePickerToken} from '@/styles/component-token';
import {DateFormat} from '@/utils/date';

const DateTimeContainerStyle = css`
    & {
        .placeholder {
            color: #b7b9c1;
        }
        &.disabled {
            background: ${datePickerToken.colorBgContainerDisabled};

            .placeholder {
                color: ${datePickerToken.colorTextDisabled};
            }
        }
    }
`;

function DatePickerWrapper({
    placeholder,
    value,
    disabled,
    onChange,
}: {
    // 占位文字
    placeholder?: string;
    // 表单项数据
    value?: dayjs.Dayjs;
    // 是否禁用
    disabled?: boolean;
    // 表单项数据变更回调
    onChange?: (value: dayjs.Dayjs) => void;
}) {
    // 弹窗显示控制
    const [visible, setVisible] = useState(false);
    // 当前选中的日期
    const [currentDate, setCurrentDate] = useState<PickerDate | undefined>();

    // 打开弹窗
    const openPopup = useCallback(() => {
        !disabled && setVisible(true);
    }, [disabled]);

    // 关闭弹窗
    const closePopup = useCallback(() => {
        setVisible(false);
    }, []);

    // 弹窗日期选择器值变更回调，变更后设置 currentDate state，点击 “确认” 时将 currentDate 通过 onChange 传递给 Form.Item
    const onPickerViewChange = useCallback((value: PickerDate) => {
        setCurrentDate(value);
    }, []);

    // 点击 “确认” 时触发
    const onConfirm = useCallback(() => {
        onChange?.(dayjs(currentDate));
        closePopup();
    }, [onChange, currentDate, closePopup]);

    useEffect(() => {
        if (value) {
            setCurrentDate(value?.toDate());
        } else {
            setCurrentDate(new Date());
        }
    }, [value]);

    return (
        <ConfigProvider theme={{components: {Button: buttonToken}}}>
            <div
                className={classNames(
                    'flex w-[270px] items-center justify-between rounded-[6px] border-[1px] px-[11px] py-[5px] text-sm text-black',
                    {disabled},
                    DateTimeContainerStyle
                )}
                onClick={openPopup}
            >
                {value?.format ? (
                    <span>{value.format(DateFormat.YMD_HYPHEN)}</span>
                ) : (
                    <span className="placeholder">{placeholder || '选择日期'}</span>
                )}
                <CalendarOutlined className="text-black" />
            </div>
            <PopupM
                title="年月日选择器"
                visible={visible}
                onClose={closePopup}
                showCloseButton
                closeOnMaskClick
                bodyStyle={{
                    paddingLeft: 0,
                    paddingRight: 0,
                }}
            >
                <DatePickerView
                    className="h-[193px]"
                    value={currentDate}
                    onChange={onPickerViewChange}
                    min={dayjs().subtract(100, 'year').toDate()}
                    max={dayjs().add(100, 'year').toDate()}
                />
                <footer className="mb-6 flex w-full border-t-[1px] border-colorBorderFormList px-[18px] pt-[15px]">
                    <Button type="primary" className="h-10 w-full font-medium" onClick={onConfirm}>
                        确认
                    </Button>
                </footer>
            </PopupM>
        </ConfigProvider>
    );
}

export default function ActivityFormDatetime({index, disabled, option}: ActivityFormItemProps) {
    return (
        <FormItemContainer index={index} title={option.label} required={option.required}>
            <Form.Item name={[option.name, 'value']} rules={[{required: option.required, message: '请选择日期'}]}>
                <DatePickerWrapper disabled={disabled} placeholder="选择日期" />
            </Form.Item>
        </FormItemContainer>
    );
}
