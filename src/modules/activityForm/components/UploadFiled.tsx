/**
 * @file 文件上传组件
 * <AUTHOR>
 */
import React, {useMemo, useState, useCallback} from 'react';
import styled from '@emotion/styled';
import {message, Upload} from 'antd';
import classNames from 'classnames';
import type {UploadFile} from 'antd/es/upload/interface';
import type {AxiosProgressEvent} from 'axios';
import isNumber from 'lodash/isNumber';
import {FileIcon} from '@/modules/dataset/components/Common';
import api from '@/api/activityForm';
import uploadIcon from '@/assets/dataset/dataset-local-upload.png';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';

// 如果有样式文件或图片资源，可以在这里引入
const Dragger = styled(Upload.Dragger)`
    .ant-upload-drag {
        border-width: 0 !important;
    }
    .ant-upload-list-item-container {
        height: auto !important;
        margin: 0 !important;
    }
`;

const StyledDiv = styled.div`
    #wraper {
        #mask {
            visibility: hidden;
        }
        &:hover {
            #mask {
                visibility: visible;
            }
        }
    }
`;

const SUBMIT_ATTR_RANGE = {
    // 文档的大小 50M
    fileSize: 50,
    // 一次上传文档的个数
    onceUploadFileNumber: 10,
    // 支持的类型
    acceptType: '.zip,.rar',
};

interface FileCardProps {
    file: UploadFile;
    disabled?: boolean;
    onRemove?: () => void;
}

const formatFileSize = (sizeBit: number) => {
    let size = sizeBit;
    const units = ['B', 'KB', 'MB', 'GB'];
    for (const unit of units) {
        if (size < 1024) {
            return `${size.toFixed(1)}${unit}`;
        }

        size = size / 1024;
    }
    return `${size}GB`;
};

// 展示已上传文件的文件信息
// eslint-disable-next-line complexity
function UploadFileCard({file, disabled, onRemove}: FileCardProps) {
    const hasError = file.status === 'error';
    const status = file.status;
    const suffix = file.name.split('.').reverse()[0].toUpperCase();
    const isPicture = ['PNG', 'JPG', 'JPEG'].includes(suffix);
    // 文件上传后，后端需要进行处理，耗时较长，最后 10% 待后端返回后完成，防止出现进度条满了却迟迟上传不成功
    const percent = file.percent! > 90 ? (status === 'done' ? 100 : 90) : file.percent;
    const isMobile = isMobileDevice();

    const imageSrc = useMemo(
        () => (isPicture ? URL.createObjectURL(file.originFileObj as File) : ''),
        [file.originFileObj, isPicture]
    );

    return (
        <>
            <StyledDiv
                className={classNames('relative w-[253px] overflow-hidden rounded-lg bg-white', {
                    'border border-[#FFA7AA] border-opacity-40 bg-[#F8F0F3]': hasError,
                    'border border-gray-border-secondary': !hasError,
                    'cursor-not-allowed': disabled,
                })}
            >
                <div id="wraper" className="flex content-center items-center overflow-hidden">
                    <FileIcon name={file.name} className="m-[6px] h-12 w-12" src={imageSrc} />
                    <div className="ml-[.375rem] flex w-[calc(100%-66px)] flex-col items-start">
                        <span className="inline-block w-full overflow-hidden overflow-ellipsis whitespace-nowrap pr-3 text-sm leading-[22px]">
                            {file.name}
                        </span>
                        <span className="text-xs leading-[18px] text-flow-hover">
                            {suffix && suffix + ' · '}
                            {formatFileSize(file.size || 0)}
                        </span>
                    </div>
                    {!isMobile && !disabled && (
                        <div
                            id="mask"
                            className="absolute flex h-full w-full cursor-pointer rounded-lg bg-black bg-opacity-60 text-lg text-white"
                            onClick={onRemove}
                        >
                            <span className={classNames('iconfont icon-delete m-auto text-lg')} />
                        </div>
                    )}
                    {status === 'uploading' && !disabled && (
                        <div
                            className="absolute flex h-full overflow-hidden bg-[#F0F2F8] mix-blend-multiply"
                            style={{width: `${percent}%`, transition: 'width 0.1s ease'}}
                            onClick={onRemove}
                        />
                    )}
                </div>
            </StyledDiv>
            {isMobile && !disabled && (
                <span
                    className="iconfont icon-close-circle-fill absolute -top-[9px] left-[244px] text-lg text-gray-tertiary"
                    onClick={onRemove}
                />
            )}
        </>
    );
}

// eslint-disable-next-line react-refresh/only-export-components
export const handleRenderFileItem = (
    originalNode: React.ReactNode,
    file: UploadFile,
    fileList: UploadFile[],
    {remove}: {remove: () => void},
    disabled?: boolean
) => {
    return <UploadFileCard file={file} disabled={disabled} onRemove={remove} />;
};

interface UploadFiledProps {
    formId: string;
    name: string;
    disabled?: boolean;
    limit?: number;
    maxSize?: number;
    setUploadFileLoading: (value: boolean) => void;
    setUploadFileFail: (value: boolean) => void;
    onChange?: (fileList: UploadFile[]) => void;
}

// 定义 UploadFiled 组件
const UploadFiled: React.FC<UploadFiledProps> = ({
    formId,
    name,
    disabled,
    maxSize = 10,
    limit = 1,
    setUploadFileLoading,
    setUploadFileFail,
    onChange,
}) => {
    // 组件逻辑（如果有）
    const [fileList, setFileList] = useState<UploadFile[] | undefined>([]);
    const isMobile = isMobileDevice();

    const handleCustomUpload = useCallback(
        async (option: any) => {
            setUploadFileLoading(true);
            const file = option.file as File;
            const handleUploadProgress = (e: AxiosProgressEvent) => {
                isNumber(e.progress) && option.onProgress({percent: (e.progress * 100).toFixed(1)});
            };

            try {
                const result = await api.formUpload({formId, name, file, onUploadProgress: handleUploadProgress});
                option.onSuccess(result);
                setUploadFileLoading(false);
                setUploadFileFail(false);
            } catch (error: any) {
                console.error('上传文件出错:', error);
                option.onError(error && error?.msg ? error?.msg : '网络错误');
                setUploadFileLoading(false);
                setUploadFileFail(true);
            }
        },
        [formId, name, setUploadFileFail, setUploadFileLoading]
    );

    const handleBeforeUpload = useCallback(
        (file: UploadFile, currentFileList: UploadFile[]) => {
            if (!file.size || file.size > maxSize * 1024 * 1024) {
                message.error(`文件大小限制为${maxSize}M以下`);
                return Upload.LIST_IGNORE;
            }

            const fileLength = fileList?.length ?? 0;
            // 第一个不合法的下标（超出单次或总共文件数量的限制）
            const firstIllegalIndex = Math.min(limit - fileLength, SUBMIT_ATTR_RANGE.onceUploadFileNumber);

            // 当前文件在文件列表中的位置
            const fileIndex = currentFileList.findIndex(currentFile => currentFile.uid === file.uid);

            // 根据下标决定该文件是否保留（超出单次或总共文件数量的限制）
            if (firstIllegalIndex <= fileIndex) {
                return Upload.LIST_IGNORE;
            }

            return true;
        },
        [maxSize, fileList, limit]
    );

    const handleUploadChange = useCallback(
        (info: {file: UploadFile; fileList: UploadFile[]}) => {
            const newFileList = [...info.fileList];
            setFileList(newFileList);
            onChange?.(newFileList);
        },
        [onChange]
    );

    const fileUploaded = !!fileList?.length;

    return (
        <Dragger
            accept={SUBMIT_ATTR_RANGE.acceptType}
            listType="picture-card"
            // eslint-disable-next-line react/jsx-no-bind
            itemRender={(...args) => handleRenderFileItem(...args, disabled)}
            fileList={fileList}
            beforeUpload={handleBeforeUpload}
            customRequest={handleCustomUpload}
            onChange={handleUploadChange}
            disabled={disabled}
        >
            <div
                className={classNames(
                    'flex flex-col items-center justify-center rounded-[9px] border border-[#ECEEF3] py-[22px]',
                    {hidden: fileUploaded}
                )}
            >
                <img src={uploadIcon} className="h-[81px]" />
                <div className="mt-[15px] flex items-center justify-center leading-[22px] text-black">
                    {isMobile ? '点击' : '点击或将文件拖拽到这里'}
                    <span className="text-primary">上传文件</span>
                </div>
                <div className="mt-[3px] text-[10px] leading-[15px] text-[#00000040]">
                    支持上传1份zip、rar格式文件，文件大小{maxSize}M以下
                </div>
            </div>
        </Dragger>
    );
};

export default UploadFiled;
