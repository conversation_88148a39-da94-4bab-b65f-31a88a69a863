/**
 * @file 文件上传组件
 * <AUTHOR>
 */
import {Form} from 'antd';
import {useState} from 'react';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import UploadFiled from '@/modules/activityForm/components/UploadFiled';

export default function ActivityFormInputFile({index, formId, disabled, option}: ActivityFormItemProps) {
    // todo: 文件上传相关逻辑，待定
    const [, setUploadFileLoading] = useState(false);
    const [, setUploadFileFail] = useState(false);

    return (
        <FormItemContainer index={index} title={option.label} required={option.required}>
            <Form.Item
                name={[option.name, 'value']}
                rules={[
                    {
                        validator: (_, value) => {
                            if (option.required && !value?.length) {
                                return Promise.reject(new Error('请上传文件'));
                            }

                            if (value?.[0]?.status === 'error') {
                                return Promise.reject(new Error(value?.[0]?.error));
                            }
                            return Promise.resolve();
                        },
                    },
                ]}
            >
                <UploadFiled
                    disabled={disabled}
                    formId={formId}
                    name={option.name}
                    maxSize={option.maxSize}
                    setUploadFileFail={setUploadFileFail}
                    setUploadFileLoading={setUploadFileLoading}
                />
            </Form.Item>
        </FormItemContainer>
    );
}
