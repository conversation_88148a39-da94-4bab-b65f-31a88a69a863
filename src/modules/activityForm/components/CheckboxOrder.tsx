/**
 * @file 排序题
 * <AUTHOR>
 */
import {Checkbox, Form} from 'antd';
import {useMemo} from 'react';
import styled from '@emotion/styled';
import {ActivityFormItemProps} from '@/modules/activityForm/components/interface';
import {useFormItemHook} from '@/modules/activityForm/hooks/useFormHook';
import {ActivityFormItemOption} from '@/api/activityForm/interface';
import FormItemContainer from '@/modules/activityForm/components/FormItemContainer';

const StyledCheckbox = styled(Checkbox)`
    & .ant-checkbox {
        display: none;

        & + span {
            padding-inline-start: 0 !important;
        }
    }
    &:after {
        content: '' !important;
    }
`;

export default function CheckboxOrder({index, option}: ActivityFormItemProps) {
    const {value} = useFormItemHook(option);

    const optionsMap = useMemo(() => {
        const initValue: Record<any, ActivityFormItemOption> = {};
        return option.options?.reduce((prev, item) => {
            prev[item.value] = item;
            return prev;
        }, initValue);
    }, [option]);

    const optionsInfo = useMemo(() => {
        const selectedOptions: ActivityFormItemOption[] =
            value?.value?.map((item: any) => optionsMap?.[item]).filter(Boolean) || [];
        const unselectedOptions = option.options?.filter(item => !value?.value?.includes(item.value)) || [];

        return {
            selectedOptions,
            unselectedOptions,
        };
    }, [optionsMap, option.options, value?.value]);

    return (
        <FormItemContainer index={index} title={option.label} required={option.required} suffix="排序题">
            <Form.Item
                name={[option.name, 'value']}
                rules={[
                    {
                        validator: (_, values) => {
                            if (!values?.length && option.required) {
                                return Promise.reject(new Error('请选择'));
                            }

                            if (values?.length && option.options?.length && values.length < option.options?.length) {
                                return Promise.reject(new Error('请选择所有选项'));
                            }

                            return Promise.resolve();
                        },
                    },
                ]}
            >
                <Checkbox.Group className="-mt-3 block">
                    {optionsInfo.selectedOptions?.map((item, index) => (
                        <div className="mt-3" key={item.value}>
                            <StyledCheckbox className="flex leading-5" value={item.value}>
                                <div className="flex items-center gap-2 px-0">
                                    <span className="border-box h-[16px] w-[16px] rounded-[3px] border-[1px] border-primary bg-primary text-center text-[10px] font-bold leading-[14px] text-white">
                                        {index + 1}
                                    </span>
                                    <div className="flex-1">{item.value}</div>
                                </div>
                            </StyledCheckbox>
                        </div>
                    ))}
                    {optionsInfo.unselectedOptions?.map(item => (
                        <div className="mt-3" key={item.value}>
                            <StyledCheckbox className="flex leading-5" value={item.value}>
                                <div className="flex items-center gap-2 px-0">
                                    <span className="border-box h-[16px] w-[16px] rounded-[3px] border-[1px] leading-[14px]" />
                                    <div className="flex-1">{item.value}</div>
                                </div>
                            </StyledCheckbox>
                        </div>
                    ))}
                </Checkbox.Group>
            </Form.Item>
        </FormItemContainer>
    );
}
