/**
 * @file 工具函数
 * <AUTHOR>
 */
import {Rule} from 'antd/es/form';
import dayjs from 'dayjs';
import type {UploadFile} from 'antd/es/upload/interface';
import {ActivityFormItem, ActivityFormItemType, ActivityFormItemValue} from '@/api/activityForm/interface';
import {DateFormat} from '@/utils/date';

/**
 * 获取输入框校验规则
 * @param option 表单项配置
 * @param defaultMaxLength 默认最大长度
 */
export function getInputValidatorRule(option: ActivityFormItem, defaultMaxLength: number): Rule {
    return {
        validator: (_, value) => {
            if (option.required && !value?.length) {
                return Promise.reject(new Error('请输入'));
            }

            if (value?.length > (option.max || defaultMaxLength)) {
                return Promise.reject(new Error(`长度不能超过${option.max || defaultMaxLength}个字符`));
            }

            if (option.min && value?.length < option.min) {
                return Promise.reject(new Error(`长度最少${option.min}个字符`));
            }

            if (option.regular && !new RegExp(option.regular).test(value)) {
                return Promise.reject(new Error('输入格式错误'));
            }

            return Promise.resolve();
        },
    };
}

/**
 * 获取复选框校验规则
 * @param option 表单项配置
 */
export function getCheckboxValidatorRule(option: ActivityFormItem): Rule {
    return {
        validator: (_, values) => {
            if (!values?.length && option.required) {
                return Promise.reject(new Error('请选择'));
            }

            if (option.requireMax && values?.length > option.requireMax) {
                return Promise.reject(new Error(`最多选择${option.requireMax}项`));
            }

            // 必填或者已经选择了值，且配置了最少选择项，则判断是否达到最少选择项
            if ((option.required || values?.length) && option.requireMin && values?.length < option.requireMin) {
                return Promise.reject(new Error(`最少选择${option.requireMin}项`));
            }

            return Promise.resolve();
        },
    };
}

/**
 * 获取选择框校验规则
 * @param option 表单项配置
 */
export function getSelectValidatorRule(option: ActivityFormItem): Rule {
    return option.multiple
        ? getCheckboxValidatorRule(option)
        : {
              validator: (_, values) => {
                  if (!values?.length && option.required) {
                      return Promise.reject(new Error('请选择'));
                  }

                  return Promise.resolve();
              },
          };
}

/**
 * 将表单的 Object 类型的数据转化为服务端需要的数组结构
 * @param formContentsMap 表单项配置的 map
 * @param formData 表单数据
 */
export function formatFormData(
    formContentsMap: Record<string, ActivityFormItem>,
    formData: Record<string, ActivityFormItemValue>
): ActivityFormItemValue[] {
    return Object.entries(formData).map(([key, value]) => {
        // 当前表单项配置数据
        const option = formContentsMap[key];
        // Checkbox 类型的数据需要将 value 和 inputValue 分别用 ',' 拼接
        if (option.type === ActivityFormItemType.Checkbox) {
            const inputValue = value.value?.map((item: string) => value.inputValues?.[item]).join(',');

            return {
                value: value.value?.join(','),
                inputValue,
                name: key,
            };
        }

        // Radio 类型的数据需要inputValues 进行处理
        if (option.type === ActivityFormItemType.Radio) {
            const inputValue = value.inputValues?.[value.value];

            return {
                value: value.value,
                name: key,
                inputValue,
            };
        }

        // 排序题、多选的智能体选择器、多选下拉选择器类型的数据需要将 value 拼接为 ',' 分隔的字符串
        if (
            option.type === ActivityFormItemType.CheckboxOrder ||
            ((option.type === ActivityFormItemType.Select || option.type === ActivityFormItemType.AgentSelect) &&
                option.multiple)
        ) {
            return {
                value: value.value?.join(','),
                name: key,
            };
        }

        // 日期范围类型的数据需要将 value 中的 dayjs 转为 YYYY-MM-DD 格式的字符串并且拼接为 ',' 分隔的字符串
        if (option.type === ActivityFormItemType.DateRange) {
            return {
                value: value.value?.map((item: dayjs.Dayjs) => item.format(DateFormat.YMD_HYPHEN)).join(','),
                name: key,
            };
        }

        // 日期类型的数据需要将 value 中的 dayjs 转为 YYYY-MM-DD 格式的字符串
        if (option.type === ActivityFormItemType.Datetime) {
            return {
                value: value.value?.format(DateFormat.YMD_HYPHEN),
                name: key,
            };
        }

        // 文件上传类型的数据需要将文件的 url 拼接为 ',' 分隔的字符串
        if (option.type === ActivityFormItemType.InputFile) {
            return {
                value: value.value?.map((item: UploadFile) => item.response?.fileUrl).join(','),
                name: key,
            };
        }

        return {
            ...value,
            name: key,
        };
    });
}
