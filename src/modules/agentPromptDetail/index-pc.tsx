/**
 * @file prompt零代码智能体详情页面-pc（当前只有对外部署模块）
 * @update <EMAIL>
 */
import React, {useCallback, useEffect, useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {CacheProvider} from 'react-suspense-boundary';
import classNames from 'classnames';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import urls from '@/links';
import agentListApi from '@/api/agentList';
import {AgentInfo, ScopeStatus} from '@/api/agentList/interface';
import {AgentTabType, AuditStatus, ServerAuditStatus} from '@/modules/agentList/interface';
import Loading from '@/components/Loading';
import PluginTabBar from '@/modules/pluginDetail/components/PluginTabBar';
import {ScrollContainer} from '@/components/ScrollContainer';
import {useLayoutStore} from '@/store/home/<USER>';
import {initWeChatAuthState, useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';
import RightPanel from '@/components/RightPanel';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {resolveAgentError} from '@/utils/agentNotExistError';
import {XmiChannelType} from '@/api/agentDeploy/interface';
import AgentOutput from '../agentOutput';

import {OutputType} from '../agentOutput/config';
import OutputDetail from '../agentOutput/detail';

function DetailContent() {
    // 打点
    const {displayLog} = useUbcLogV2();
    const {showRightPanel, setShowRightPanel} = useLayoutStore(store => ({
        showRightPanel: store.showRightPanel,
        setShowRightPanel: store.setShowRightPanel,
    }));
    const [loading, setLoading] = useState(false);
    const [outputAvailable, setOutputAvailable] = useState(false);
    const [outputType, setOutputType] = useState<OutputType | null>(null);
    const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null);
    const [agentLogExt, setAgentLogExt] = useState<AgentLogExt>();

    const navigate = useNavigate();

    const {id} = useParams();
    if (!id) {
        // 返回我的智能体列表
        navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
    }

    useEffect(() => {
        // 公开已上线零代码Agent对外部署可用
        if (
            agentInfo &&
            agentInfo.permission !== undefined &&
            agentInfo.status !== undefined &&
            agentInfo.originalStatus !== undefined &&
            [ScopeStatus.Link, ScopeStatus.Public].includes(agentInfo.permission) &&
            [
                AuditStatus.Online,
                AuditStatus.Editing,
                ServerAuditStatus.SecondAuditing,
                ServerAuditStatus.SecondAuditSuccess,
                ServerAuditStatus.SecondAuditFailed,
            ].includes(agentInfo.originalStatus)
        ) {
            setOutputAvailable(true);
        }
    }, [agentInfo]);

    useEffect(() => {
        if (!showRightPanel) {
            setOutputType(null);
        }
    }, [showRightPanel]);

    // 返回我的智能体列表
    const onReturn = useCallback(() => {
        navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
    }, [navigate]);

    const {setWxAuthMap, setWxDeploy} = useWeChatAuthStore(store => ({
        setWxAuthMap: store.setWxAuthMap,
        setWxDeploy: store.setWxDeploy,
    }));

    // 获取带审核的应用信息
    const getAgentInfo = useCallback(async () => {
        const agentInfo = await agentListApi.getAgentDetail(id!);
        setAgentInfo(agentInfo);
        setAgentLogExt({
            [EVENT_EXT_KEY_CONST.AGENT_ID]: agentInfo.appId! || id!,
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentInfo.name,
        });

        // 存储微信授权相关信息
        const wxAuthList = agentInfo?.wxAuthList || [];
        const wxAuthMap = {...initWeChatAuthState.wxAuthMap};
        wxAuthList?.forEach(item => {
            wxAuthMap[item.type] = item;
        });

        // 存储小米应用商店授权相关信息
        if (agentInfo?.xmiDeployInfo) {
            wxAuthMap[XmiChannelType.XmiAppStore] = agentInfo.xmiDeployInfo;
        }

        setWxAuthMap(wxAuthMap);

        // 存储部署渠道
        const deployChannels = agentInfo?.deployChannels || [];
        const wxDeployMap = {...initWeChatAuthState.wxDeployMap};

        deployChannels.forEach(type => {
            wxDeployMap[type] = wxAuthMap[type]?.auth;
        });

        setWxDeploy(wxDeployMap);
    }, [id, setWxAuthMap, setWxDeploy]);

    useEffect(() => {
        (async () => {
            setLoading(true);
            try {
                await getAgentInfo();
            } catch (err: any) {
                resolveAgentError(err, navigate);
            }

            setLoading(false);
        })();
    }, [getAgentInfo, navigate]);

    const handleOutputTypeChange = useCallback(
        (outputType: OutputType) => {
            setOutputType(outputType);
            setShowRightPanel(true);
        },
        [setShowRightPanel]
    );

    useEffect(() => {
        id &&
            displayLog(EVENT_PAGE_CONST.AGENT_DEPLOY, {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: id,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentInfo?.name,
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div className="flex">
            <div className={classNames('m-auto flex-1', {'max-w-[83.5rem] px-8 py-[1.875rem]': showRightPanel})}>
                <PluginTabBar title="部署" hideRightTab onReturnList={onReturn} />
                {/* 内容滚动区 */}
                <ScrollContainer className="h-[calc(100vh-104px)] overflow-y-auto overflow-x-hidden pt-10">
                    {/* 在内容区loading */}
                    {loading ? (
                        <Loading />
                    ) : (
                        <AgentOutput
                            available={outputAvailable}
                            curOutputType={outputType}
                            outputTypeChange={handleOutputTypeChange}
                            agentLogExt={agentLogExt}
                        />
                    )}
                </ScrollContainer>
            </div>
            {/** 右侧对外部署面板区 */}
            <RightPanel>
                {(outputType && (
                    <OutputDetail
                        outputType={outputType}
                        previewUrl={agentInfo?.previewUrl}
                        appId={id!}
                        agentLogExt={agentLogExt}
                    />
                )) ||
                    null}
            </RightPanel>
        </div>
    );
}

/**
 * 零代码智能体详情页
 */
export default function AgentPromptDetailPC() {
    return (
        <div>
            <CacheProvider>
                <CommonErrorBoundary pendingFallback={<Loading />}>
                    <DetailContent />
                </CommonErrorBoundary>
            </CacheProvider>
        </div>
    );
}
