import {useState} from 'react';
import {message} from 'antd';
import {WxAccountType, WxAppId} from '@/api/agentDeploy/interface';
import api from '@/api/agentDeploy/index';
import {useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';

// 定义useWxAccountAuth的返回类型（根据需要调整）
interface wxAccountAuthProps {
    appId: string;
    wxAccountType: WxAccountType;
    wxAppId?: WxAppId;
    openAuthQrCodeModal?: boolean;
    setQrCodeUrl: (url: string) => void;
    setOpenAuthQrCodeModal: (open: boolean) => void;
    bindFail: (err: any) => void;
    bindSuccess?: () => void;
}

let isBinding = false; // 当前是否正在绑定中

// 定义useWxAccountAuth hook
export default function useBindWxAccountAuth() {
    const [expireTime, setExpireTime] = useState(0);
    const {setAuthStatus, setCheckedStatus, setBindWxAppId} = useWeChatAuthStore(store => ({
        setAuthStatus: store.setAuthStatus,
        setCheckedStatus: store.setCheckedStatus,
        setBindWxAppId: store.setBindWxAppId,
    }));

    const intervalTime: number = 1500; // 每隔多久查一次,单位毫秒
    // const maxPollNum: number = 50; // 最大轮询次数
    // let reqNum = 0; // 查询次数
    let timer: any = null;

    // 取消轮询
    const cancelPoll = () => {
        timer && clearTimeout(timer);
    };

    const bindAuth = async ({
        appId,
        wxAccountType,
        wxAppId,
        setQrCodeUrl,
        setOpenAuthQrCodeModal,
        bindFail,
        bindSuccess,
    }: wxAccountAuthProps) => {
        try {
            // 未授权且有wxAppId，点击进行授权绑定
            if (wxAppId) {
                const startTime = Date.now();
                // 定义：轮询获取授权信息函数
                const pollGetWxAuthResult = () => {
                    // 先取消
                    timer && clearTimeout(timer);
                    // // 弹窗关闭
                    // if (!openAuthQrCodeModal) {
                    //     return;
                    // }
                    // 二维码超时提示失效 或 超过最大查询次数，取消查询
                    if (expireTime > 0 && Math.round((Date.now() - startTime) / 1000) > expireTime) {
                        message.error('当前授权二维码已失效，请关闭弹窗重新授权');
                        timer && clearTimeout(timer);
                        return;
                    }
                    // 定时请求
                    timer = setTimeout(async () => {
                        try {
                            // 记录一次请求
                            // reqNum++;
                            const res = await api.getWxAuthState({
                                appId,
                                type: wxAccountType,
                            });

                            // 授权成功后，关闭弹窗
                            if (res.auth) {
                                // 更新授权状态
                                setAuthStatus(wxAccountType, true);
                                setOpenAuthQrCodeModal && setOpenAuthQrCodeModal(false);
                                // 更新部署选中
                                setCheckedStatus(wxAccountType, true);
                                // 提示授权成功
                                message.success('授权成功');
                                timer && clearTimeout(timer);
                                // 回调授权成功
                                bindSuccess && bindSuccess();
                            } else {
                                pollGetWxAuthResult();
                            }
                        } catch (e: any) {
                            // 接口返回报错
                            if (+e.errno) {
                                // 关闭
                                setOpenAuthQrCodeModal && setOpenAuthQrCodeModal(false);
                                // 延迟toast
                                setTimeout(() => {
                                    message.error(e?.msg || '获取授权状态失败');
                                }, 500);
                                // 授权绑定失败操作
                                bindFail(e);
                            } else {
                                message.error(e?.msg || '获取授权状态失败');
                            }
                        }
                    }, intervalTime);
                };
                if (isBinding) {
                    return;
                }
                isBinding = true;
                // 进行授权绑定
                const res = await api.bindWxAccount({
                    appId,
                    type: wxAccountType,
                    wxAppId,
                });
                isBinding = false;
                // 设置已经绑定的微信号
                setBindWxAppId(wxAccountType, wxAppId);
                // 已经授权直接授权成功
                if (res.auth) {
                    // 更新授权状态
                    setAuthStatus(wxAccountType, true);
                    // 更新部署选中
                    setCheckedStatus(wxAccountType, true);
                    // 提示授权成功
                    message.success('授权成功');
                    // 回调授权成功
                    bindSuccess && bindSuccess();
                } else if (res.jumpUrl) {
                    // 打开授权二维码弹窗
                    setQrCodeUrl(res.jumpUrl);
                    setExpireTime(res.expireTime);
                    setOpenAuthQrCodeModal(true);
                    // 轮询获取授权结果
                    pollGetWxAuthResult();
                } else {
                    message.error('获取授权二维码链接失败，请重新授权');
                }
            } else {
                message.error('授权失败，请输入微信appId');
            }
        } catch (e: any) {
            isBinding = false;
            message.error(e?.msg || '绑定授权失败，请重新输入微信appId进行授权');
        }
    };

    return {
        bindAuth,
        cancelPoll,
    };
}
