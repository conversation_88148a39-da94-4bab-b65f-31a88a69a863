/**
 * @file 微信授权解绑逻辑
 * <AUTHOR>
 */
import {Modal, message} from 'antd';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {WxAccountType} from '@/api/agentDeploy/interface';
import api from '@/api/agentDeploy/index';
import {useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';

interface UnBindWxAccountProps {
    appId: string;
    wxAccountType: WxAccountType;
    updateAuthStatus: (wxAccountType: WxAccountType) => void;
}

let isUnBinding = false; // 当前是否正在解绑中

export default function useUnbindWxAccount() {
    const [modal, modalContextHolder] = Modal.useModal();

    const {setAuthStatus, setCheckedStatus, setBindWxAppId} = useWeChatAuthStore(store => ({
        setAuthStatus: store.setAuthStatus,
        setCheckedStatus: store.setCheckedStatus,
        setBindWxAppId: store.setBindWxAppId,
    }));

    const confirm = ({appId, wxAccountType, updateAuthStatus}: UnBindWxAccountProps) => {
        if (isUnBinding) {
            return;
        }

        isUnBinding = true;
        // 弹窗确认解绑？
        const content =
            wxAccountType === WxAccountType.WxMiniApp
                ? '解绑后，微信小程序将无法使用文心智能体回复消息，如需完全取消授权，请前往“微信公众平台 > 设置 > 第三方设置 > 第三方平台授权管理”中取消授权'
                : `解绑后，微信公众号（${
                      wxAccountType === WxAccountType.WxSubscribe ? '订阅号' : '服务号'
                  }）将无法使用文心智能体回复消息，如需完全取消授权，请前往“微信公众平台 > 设置与开发 > 公众号设置 > 授权管理 > 查看平台详情”中取消授权。`;
        modal.confirm({
            title: '确定要解绑吗？',
            icon: <ExclamationCircleOutlined />,
            content,
            cancelText: '取消',
            okText: '确定',
            centered: true,
            autoFocusButton: null,
            onOk() {
                // 确认解绑弹窗，解绑，更新状态
                return api
                    .unbindWxAccount({
                        appId,
                        type: wxAccountType,
                    })
                    .then(() => {
                        // 设置删除绑定的微信号
                        setBindWxAppId(wxAccountType, '');
                        // 解绑成功会变成未授权
                        setAuthStatus(wxAccountType, false);
                        // 取消选择态
                        setCheckedStatus(wxAccountType, false);
                        updateAuthStatus(wxAccountType);
                        message.success('解绑成功');
                    })
                    .catch(e => {
                        if (+e.errno) {
                            message.error(e.msg || '解绑失败');
                        } else {
                            console.info('e', e);
                            throw e;
                        }
                    })
                    .finally(() => {
                        isUnBinding = false;
                    });
            },
            onCancel() {
                isUnBinding = false;
            },
        });
    };

    return {
        modalContextHolder,
        confirm,
    };
}
