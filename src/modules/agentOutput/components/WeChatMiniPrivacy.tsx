import React, {useCallback, useState, ChangeEvent, useEffect, useMemo} from 'react';
import {Button, Form, Input, message} from 'antd';
import dayjs from 'dayjs';
import styled from '@emotion/styled';
import api from '@/api/agentDeploy/index';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {WxPrivacyParam, WxPrivacyInfoResponse} from '@/api/agentDeploy/interface';

export const StyledFormItem = styled(Form.Item)`
    &.ant-form-item {
        margin-bottom: 0;
    }
`;

const emailReg = /^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$/;
const phoneReg = /^1[3-9]\d{9}$/;

const emailPhoneValidator = async (_: any, value: string) => {
    if (phoneReg.test(value) || emailReg.test(value)) {
        return Promise.resolve();
    }
    return Promise.reject(new Error('请填写有效的联系电话/邮箱'));
};

interface Props {
    appId: string;
    wxAppId: string;
    getAgentInfo?: () => void;
    setWxSetPrivacy?: (status: boolean) => void;
}
const WeChatMiniPrivacy = ({appId, wxAppId, getAgentInfo, setWxSetPrivacy}: Props) => {
    const [form] = Form.useForm();
    const [inputFocused, setInputFocused] = useState(false);
    const [currentContact, setCurrentContact] = useState(form.getFieldValue('contact'));
    const [wxPrivacyData, setWxPrivacyData] = useState<WxPrivacyInfoResponse | null>(null);
    const {customerInfo} = useUserInfoStore(store => store.userInfoData) || {};

    // 此处跳转的协议页面，暂时部署在C端环境
    const privacyAgreementLink = `https://aiplugin.baidu.com/wx/page/privacyAgreement${
        appId ? `?agentid=${appId}` : ''
    }`;

    const developerName = useMemo(
        () => wxPrivacyData?.wxPrivacyInfo?.user?.developerName || customerInfo?.developerName || '',
        [customerInfo?.developerName, wxPrivacyData?.wxPrivacyInfo?.user?.developerName]
    );

    useEffect(() => {
        (async () => {
            if (!appId) {
                setWxPrivacyData(null);
                return;
            }
            const res = await api.getWxPrivacy({appId});
            setWxPrivacyData(res);
            form.setFieldsValue({
                developerName: res.wxPrivacyInfo?.user?.developerName || customerInfo?.developerName || '',
                contact: res.wxPrivacyInfo?.user?.contact,
            });
        })();
    }, [appId, customerInfo?.developerName, form]);

    const handleInputChange = useCallback(
        async (e: ChangeEvent<HTMLInputElement>) => {
            setCurrentContact(e.target.value);
            form.setFieldsValue({
                contact: e.target.value,
            });
        },
        [form]
    );

    const handleInputFocus = useCallback(() => {
        setInputFocused(true);
    }, []);

    /** 确定 */
    const handleConfirm = useCallback(async () => {
        if (!wxPrivacyData || !appId) {
            return;
        }

        await form.validateFields({validateOnly: false}).then(async () => {
            const developerName =
                wxPrivacyData?.wxPrivacyInfo?.user?.developerName || customerInfo?.developerName || '';
            const contact = form.getFieldValue('contact') || wxPrivacyData?.wxPrivacyInfo?.user?.contact || '';

            // 隐私设置里的配置、联系方式等信息，会有server主从同步问题，因此前端需要自己保存配置信息。而不能载提交保存后，通过拉取接口数据来更新数据。
            setWxPrivacyData({
                wxPrivacyInfo: {
                    user: {
                        developerName,
                        contact,
                    },
                    updateDate: wxPrivacyData?.wxPrivacyInfo?.updateDate || dayjs().format('YYYY-MM-DD'),
                    effectiveDate: wxPrivacyData?.wxPrivacyInfo?.effectiveDate || dayjs().format('YYYY-MM-DD'),
                },
                // 这个值，只在隐私协议里使用，这里并不赋值。等打开隐私协议时，通过接口获取
                developerName: wxPrivacyData?.developerName,
            });

            const setWxPrivacyParam: WxPrivacyParam = {
                appId,
                user: {
                    developerName,
                    contact,
                },
            };
            try {
                await api.setWxPrivacy(setWxPrivacyParam);
                setWxSetPrivacy?.(true);
                getAgentInfo?.();
                message.warning('提交成功');
            } catch (e) {
                message.warning('提交隐私设置失败');
                console.error('提交隐私设置失败', e);
            }
        });
    }, [appId, customerInfo?.developerName, form, getAgentInfo, setWxSetPrivacy, wxPrivacyData]);

    return (
        <Form form={form}>
            <div className="flex h-[30px] w-full items-center">
                <div className="w-[85px] ">
                    <span className="font-medium">开发者名称</span>
                    <span className="ml-1.5 text-error">*</span>
                </div>
                <div className="ml-1.5 w-[463px]">
                    <StyledFormItem name="developerName" rules={[{required: true, message: ''}]}>
                        <Input
                            className="border-0 bg-colorBgFormList text-opacity-60"
                            defaultValue={developerName}
                            disabled
                        />
                    </StyledFormItem>
                </div>
            </div>
            <div className="mt-[9px] flex h-11">
                <div className="mt-1.5 w-[83px]">
                    <span className="font-medium ">联系方式</span>
                    <span className="ml-1.5 text-error">*</span>
                </div>
                <div className="ml-1.5 w-[463px]">
                    <StyledFormItem
                        name="contact"
                        rules={[{required: true, message: ''}, {validator: emailPhoneValidator}]}
                    >
                        <Input
                            className={`border-nonebg-colorBgFormList] ${inputFocused ? '' : 'text-gray-tertiary'}`}
                            placeholder="请输入联系方式"
                            defaultValue={currentContact}
                            onChange={handleInputChange}
                            onFocus={handleInputFocus}
                        />
                    </StyledFormItem>
                </div>
            </div>
            <div className="flex items-center">
                <Button
                    type="primary"
                    className={`mr-3 text-sm ${wxAppId && currentContact ? 'hover:bg-primaryHover ' : ''}`}
                    onClick={handleConfirm}
                    disabled={!(wxAppId && currentContact)}
                >
                    确认
                </Button>
                <div>
                    信息填写完成确认后，可以点击预览{' '}
                    <a href={privacyAgreementLink} target="_blank" rel="noreferrer" className="text-primary">
                        隐私政策
                    </a>{' '}
                    内容
                </div>
            </div>
        </Form>
    );
};

export default WeChatMiniPrivacy;
