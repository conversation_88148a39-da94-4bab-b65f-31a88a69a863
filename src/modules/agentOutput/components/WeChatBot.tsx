/**
 * @file 微信机器人
 * <AUTHOR>
 */

import {Button} from 'antd';
import {AgentUrlChannel, OutputTypeConfig, addChannelToAgentWebUrl} from '../config';
import LinkCopyUrl from './LinkCopyUrl';
import SinglePreviewImage from './SinglePreviewImage';

interface Props {
    /** web化 agent预览 url */
    previewUrl: string;
}

export default function WeChatBot({previewUrl = ''}: Props) {
    return (
        <div className="pb-8">
            <h1 className="mb-6 text-xl font-medium">{OutputTypeConfig.WeChatBot.name}</h1>
            <section>
                <h3 className="mb-4 mt-6 text-lg font-medium">配置应用到聊天工具栏</h3>
                <h3 className="mb-4 mt-6 text-lg font-medium">步骤一：创建自建应用</h3>
                <div className="text-sm text-gray-secondary">
                    <p>
                        1. PC端登录访问
                        <Button
                            type="link"
                            target="_blank"
                            href="https://work.weixin.qq.com/wework_admin/frame#apps"
                            className="h-auto border-0 p-0"
                        >
                            企业微信管理后台
                        </Button>
                        进入企业微信管理后
                    </p>
                    <p className="mt-4">2. 点击应用管理—创建自建应用</p>
                    <div className="mt-2">
                        <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-创建自建应用-步骤1.png" />
                    </div>
                    <p className="mt-4">
                        3. 输入应用名称、应用头像、介绍，点击应用可见范围，选择全部成员，点击创建应用
                    </p>
                    <div className="mt-2">
                        <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-创建自建应用-步骤3.png" />
                    </div>
                </div>
            </section>
            <section>
                <h3 className="mb-4 mt-6 text-lg font-medium">步骤二：配置应用到聊天工具栏</h3>
                <div className="text-sm text-gray-secondary">
                    <p>1. 点击客户与上下游—聊天工具—进入聊天工具栏管理</p>
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-1.png" />
                    <p className="mt-4">2. 点击配置应用页面</p>
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-2.png" />
                    <p className="mt-4">3. 选择已经创建好的自建应用，点击下一步</p>
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-3.png" />
                    <p className="mt-4">
                        4. 输入页面名称和将想嵌入到企业微信的智能体页面链接
                        <LinkCopyUrl
                            url={addChannelToAgentWebUrl(previewUrl, AgentUrlChannel.weChatBot)}
                            copyText="点此复制链接"
                        />
                        粘贴到页面内容自定义输入框，点击确定
                    </p>
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-4.png" />
                    <p className="mt-4">
                        5.
                        如果希望工作台点击应用进入智能体，可以分别为手机电脑端配置应用主页链接为智能体对话链接，并勾选下面选项。
                    </p>
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-5-1.png" />
                    <SinglePreviewImage src="https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/发布到企业微信-配置应用到聊天工作栏-步骤2-5-2.png" />
                </div>
            </section>
        </div>
    );
}
