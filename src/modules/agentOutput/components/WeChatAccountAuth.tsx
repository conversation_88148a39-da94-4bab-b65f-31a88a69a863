import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {Button, Form, Input, Tooltip} from 'antd';
import classNames from 'classnames';
import {WxAccountType, WxAccountLogMap} from '@/api/agentDeploy/interface';
import {useWeChatAuthStore} from '@/store/agent/WeChatAuthStore';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {getLogPage} from '../utils';
import useBindWxAccountAuth from '../hooks/useBindWxAccountAuth';
import useUnbindWxAccount from '../hooks/useUnbindWxAccount';
import {OutputTypeConfig} from '../config';
import SinglePreviewImage from './SinglePreviewImage';
import WxAuthQrCodeModel from './WxAuthQrCodeModel';
import WeChatMiniPrivacy from './WeChatMiniPrivacy';

const WeChatSubscribeQA = () => {
    return (
        <div>
            <div className="pb-4 pt-6 text-lg font-medium">常见问题</div>
            <div className="pb-2 text-sm font-medium">
                1.和订阅号聊天时，为什么会收到“智能体处理中，发送『继续』以获取回答”的提示？
            </div>
            <div className="pb-4 text-sm text-gray-secondary">
                当发送消息到智能体给出完整答案的时延超过15秒时，用户会收到请回复“继续”
                的提示，用户回复“继续”后将收到完整答案。为避免该问题出现，建议您修改智能体提示词，控制智能体回复长度或减少智能体挂载的非必要插件，尽量保证智能体在15秒内完成回复。
            </div>
            <div className="pb-2 text-sm font-medium">2.已经扫码完成了授权操作，但依然显示“未授权”，如何解决？</div>
            <div className="pb-4 text-sm text-gray-secondary">
                在平台处理授权操作过程中出现报错或网络问题时，可能会出现完成授权操作但状态未更新的情况。此时建议您点击解绑按钮解除账号绑定后再次点击授权并操作授权。
            </div>
            <div className="pb-2 text-sm font-medium">
                3.授权成功后，为什么会收到“请确认您绑定的公众号类型与选择的部署渠道一致”的提示？
            </div>
            <div className="text-sm text-gray-secondary">
                授权平台与公众号类型需要对应，请确认您是否在订阅号渠道绑定了服务号，或者在服务号渠道绑定了订阅号。
            </div>
        </div>
    );
};

const WeChatServerQA = () => {
    return (
        <div>
            <div className="pb-4 pt-6 text-lg font-medium">常见问题</div>
            <div className="pb-2 text-sm font-medium">1.已经扫码完成了授权操作，但依然显示“未授权”，如何解决？</div>
            <div className="pb-4 text-sm text-gray-secondary">
                在平台处理授权操作过程中出现报错或网络问题时，可能会出现完成授权操作但状态未更新的情况。此时建议您点击解绑按钮解除账号绑定后再次点击授权并扫码完成授权操作。
            </div>
            <div className="pb-2 text-sm font-medium">
                2.授权成功后，为什么会收到“请确认您绑定的公众号类型与选择的部署渠道一致”的提示？
            </div>
            <div className="pb-4 text-sm text-gray-secondary">
                授权平台与公众号类型需要对应，请确认您是否在订阅号渠道绑定了服务号，或者在服务号渠道绑定了订阅号。
            </div>
            <div className="pb-2 text-sm font-medium">3.智能体可以部署到未完成认证的服务号吗？</div>
            <div className="text-sm text-gray-secondary">
                服务号必须完成认证，部署到服务号的智能体才可接受消息，未完成认证或认证中的智能体无法接受消息。您可前往微信公众平台
                设置与开发 &gt; 微信认证 进行认证操作。
            </div>
        </div>
    );
};

const WeChatMiniAppQA = () => {
    return <></>;
};

const imgSrcMap = {
    [WxAccountType.WxServer]:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/wxserver.png',
    [WxAccountType.WxSubscribe]:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/wxsubscribe.png',
    [WxAccountType.WxMiniApp]:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/wxMinApp.png',
};
interface WeChatAccountAuthProps {
    // 在这里定义你的props类型
    wxAccountType: WxAccountType;
    appId: string;
    className?: string;
    showTitle?: boolean; // 是否展示标题
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt;
    getAgentInfo?: () => void;
    setWxSetPrivacy?: (status: boolean) => void;
}

// eslint-disable-next-line complexity
const WeChatAccountAuth = ({
    appId,
    wxAccountType,
    showTitle,
    className,
    agentLogExt,
    getAgentInfo,
    setWxSetPrivacy,
}: WeChatAccountAuthProps) => {
    const {clickLog} = useUbcLogV2();

    const {wxAppId} = useWeChatAuthStore(store => ({
        wxAppId: store.wxAuthMap?.[wxAccountType].wxAppId,
    }));

    const bindStatus = !!wxAppId;

    const {modalContextHolder, confirm} = useUnbindWxAccount();
    const {bindAuth, cancelPoll} = useBindWxAccountAuth();
    const [openAuthQrCodeModal, setOpenAuthQrCodeModal] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState('');

    const imgSrc = imgSrcMap[wxAccountType];

    const title =
        wxAccountType === WxAccountType.WxServer
            ? OutputTypeConfig.WeChatServerAccount.name
            : wxAccountType === WxAccountType.WxSubscribe
            ? OutputTypeConfig.WeChatSubscribeAccount.name
            : OutputTypeConfig.WeChatMiniProgram.name;

    const [form] = Form.useForm();
    const inputWxId = Form.useWatch('inputWxId', form);
    const [errors, setErrors] = React.useState({inputWxId: ''});

    const logExt = useMemo(() => {
        return {
            ...agentLogExt,
            [EVENT_EXT_KEY_CONST.WECHAT_OFFICIAL_TYPE]: WxAccountLogMap[wxAccountType],
        };
    }, [wxAccountType, agentLogExt]);

    // 校验
    const validateWxAppId = useCallback(
        (rule: any, value: any) => {
            if (wxAccountType === WxAccountType.WxMiniApp) {
                return Promise.resolve();
            }

            if (value.startsWith('wx') && value.length === 18) {
                setErrors({inputWxId: ''});
                return Promise.resolve();
            } else {
                setErrors({inputWxId: '开发者ID格式错误，需是以wx开头的18位字符串'});
                Promise.reject(new Error('开发者ID格式错误，需是以wx开头的18位字符串'));
            }
        },
        [setErrors, wxAccountType]
    );

    // 授权表单提交
    const onFinish = useCallback(
        (values: any) => {
            const page = getLogPage();

            const {inputWxId} = values;
            bindAuth({
                wxAccountType,
                appId,
                wxAppId: inputWxId || wxAppId,
                setQrCodeUrl,
                openAuthQrCodeModal,
                setOpenAuthQrCodeModal,
                bindFail: e => {
                    setErrors({inputWxId: e?.msg});
                },
                bindSuccess: () => {
                    clickLog(EVENT_VALUE_CONST.AUTHORISE_SUCCESS_TOAST, page, {
                        ...logExt,
                    });
                },
            });

            clickLog(EVENT_VALUE_CONST.WECHAT_OFFICIAL_CONFIRM, page, {
                ...logExt,
            });
        },
        [bindAuth, appId, openAuthQrCodeModal, wxAccountType, wxAppId, setErrors, clickLog, logExt]
    );

    // 解绑
    const onUnbind = useCallback(() => {
        setErrors({inputWxId: ''});
        // 解绑确认
        confirm({
            appId,
            wxAccountType,
            updateAuthStatus: () => {},
        });
    }, [confirm, appId, wxAccountType, setErrors]);

    useEffect(() => {
        // 关闭弹窗，取消
        if (!openAuthQrCodeModal) {
            cancelPoll();
        }
    }, [openAuthQrCodeModal, cancelPoll]);
    return (
        <div className={classNames('pb-8', className)}>
            {showTitle && <h1 className="text-xl font-medium">{title}</h1>}
            {wxAccountType === WxAccountType.WxMiniApp ? (
                <>
                    <div className="mt-6 text-sm text-gray-secondary">
                        按照如下步骤配置智能体，智能体即可作为独立的微信小程序，在微信端提供服务。
                    </div>
                    <div className="flex flex-col gap-4 text-sm text-gray-secondary">
                        <div className="text-black-base pt-4 text-[18px] font-medium leading-[26px]">温馨提示：</div>
                        <div>1. 智能体在小程序部署后将覆盖原有小程序的所有页面和功能。</div>
                        <div>2. 您的微信小程序账号主体类型需为个人以外的其他类型，并完整填写基本信息和服务类目。</div>
                        <div>
                            3. 您的微信小程序需完成备案，备案流程可参考{' '}
                            <a
                                href="https://developers.weixin.qq.com/miniprogram/product/record/record_guidelines.html"
                                target="_blank"
                                rel="noreferrer"
                                className="text-primary"
                            >
                                微信开放平台文档
                            </a>
                            。
                        </div>
                        <div>4. 您的微信小程序将根据微信公众平台要求，对用户展示服务协议。</div>
                    </div>
                </>
            ) : (
                <div className="mt-6 text-sm text-gray-secondary">
                    前往
                    <a href="https://mp.weixin.qq.com/" target="_blank" rel="noreferrer" className="text-primary">
                        微信公众平台
                    </a>
                    ，按照以下步骤配置智能体，公众号即可利用文心智能体自动回复用户信息，助力微信运营，提供更优服务。
                </div>
            )}

            <div className="text-black-base pb-4 pt-6 text-[18px] font-medium leading-[26px]">
                第一步：复制开发者ID（AppID）
            </div>
            <div className="text-sm text-gray-secondary">
                前往
                <a href="https://mp.weixin.qq.com/" target="_blank" rel="noreferrer" className="text-primary">
                    微信公众平台
                </a>{' '}
                {wxAccountType === WxAccountType.WxMiniApp ? (
                    <>管理 &gt; 开发管理 &gt; 开发设置，复制AppID（小程序ID）信息。</>
                ) : (
                    <>设置与开发 &gt; 开发接口管理 &gt; 基本配置 &gt; 账号开发信息，复制开发者ID（AppID）信息。</>
                )}
            </div>

            <div className="text-black-base pb-4 pt-6 text-[18px] font-medium leading-[26px]">
                第二步：填入开发者ID（AppID），点击确认
            </div>
            <Form name="wxAuthForm" onFinish={onFinish} layout="vertical" className="w-full" form={form}>
                <Form.Item
                    noStyle
                    name="inputWxId"
                    label="开发者ID（AppID）："
                    rules={[{validator: validateWxAppId}]}
                    validateStatus={errors.inputWxId && 'error'}
                    help={errors.inputWxId}
                >
                    <div className="flex">
                        <Tooltip title={bindStatus ? '智能体已绑定该开发者ID，若需绑定其他ID，请先解绑' : ''}>
                            <Input
                                placeholder="请输入开发者ID（AppID）"
                                defaultValue={wxAppId}
                                styles={{input: {borderColor: '#dee0e7'}}}
                                disabled={bindStatus}
                            />
                        </Tooltip>
                        {bindStatus ? (
                            <Button type="primary" onClick={onUnbind} className="ml-3">
                                解绑
                            </Button>
                        ) : (
                            <Button type="primary" htmlType="submit" className="ml-3" disabled={!inputWxId}>
                                确认
                            </Button>
                        )}
                    </div>
                </Form.Item>
            </Form>

            {wxAccountType === WxAccountType.WxMiniApp && (
                <>
                    <div className="text-black-base pb-4 pt-6 text-[18px] font-medium leading-[26px]">
                        第三步：填写联系信息，完善服务协议内容
                    </div>
                    <WeChatMiniPrivacy
                        appId={appId}
                        wxAppId={wxAppId}
                        getAgentInfo={getAgentInfo}
                        setWxSetPrivacy={setWxSetPrivacy}
                    />
                </>
            )}

            <div className="text-black-base pb-4 pt-6 text-[18px] font-medium leading-[26px]">
                第{wxAccountType === WxAccountType.WxMiniApp ? '四' : '三'}步：使用微信扫描二维码，完成授权
            </div>
            <div className="text-sm text-gray-secondary">
                确认开发者ID（AppID）后，会展示授权二维码，请使用公众平台绑定的管理员个人微信号扫描二维码，完成授权。
            </div>
            <SinglePreviewImage src={imgSrc} className="pt-2" />
            {/* 问题 */}
            {wxAccountType === WxAccountType.WxServer ? (
                <WeChatServerQA />
            ) : wxAccountType === WxAccountType.WxSubscribe ? (
                <WeChatSubscribeQA />
            ) : (
                <WeChatMiniAppQA />
            )}
            <WxAuthQrCodeModel
                open={openAuthQrCodeModal}
                wxAccountType={wxAccountType}
                qrCodeUrl={qrCodeUrl}
                setOpenAuthQrCodeModal={setOpenAuthQrCodeModal}
                agentLogExt={agentLogExt}
            />
            {modalContextHolder}
        </div>
    );
};

export default WeChatAccountAuth;
