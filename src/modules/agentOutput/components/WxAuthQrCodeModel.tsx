/**
 * @file 微信授权二维码弹窗
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useRef} from 'react';
import {Modal} from 'antd';
import {css} from '@emotion/css';
import {WxAccountType, WxAccountLogMap} from '@/api/agentDeploy/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {getLogPage} from '../utils';

const customModalStyle = css`
    .ant-modal-content {
        padding: 0 !important;
        height: 480px;
    }
    .ant-btn-text {
        font-weight: 400;
        border-radius: 0;
        border-right: 0.5px solid #e8e8e8;
    }
`;

interface WxAuthQrCodeModelProps {
    open: boolean;
    qrCodeUrl: string;
    wxAccountType?: WxAccountType;
    setOpenAuthQrCodeModal: (open: boolean) => void;
    agentLogExt?: AgentLogExt;
}

const WxAuthQrCodeModel: React.FC<WxAuthQrCodeModelProps> = ({
    open,
    qrCodeUrl,
    wxAccountType,
    setOpenAuthQrCodeModal,
    agentLogExt,
}: WxAuthQrCodeModelProps) => {
    const {clickLog} = useUbcLogV2();
    const page = getLogPage();

    const onCancel = useCallback(() => {
        setOpenAuthQrCodeModal(false);

        clickLog(EVENT_VALUE_CONST.WECHAT_AUTHORISE_CLOSE, page, {
            ...agentLogExt,
            [EVENT_EXT_KEY_CONST.WECHAT_OFFICIAL_TYPE]: WxAccountLogMap[wxAccountType!],
        });
    }, [setOpenAuthQrCodeModal, clickLog, agentLogExt, page, wxAccountType]);

    const wxQrCode = useRef<HTMLIFrameElement>(null);

    useEffect(() => {
        if (open && wxQrCode?.current) {
            wxQrCode.current.style.transformOrigin = 'top left';
            wxQrCode.current.style.transform = 'scale(0.6, 0.6)';
        }
    }, [wxQrCode, open]);

    return (
        <div>
            <Modal className={customModalStyle} open={open} footer={null} width={865} centered onCancel={onCancel}>
                <iframe
                    ref={wxQrCode}
                    className="h-full min-h-[800px] rounded-[18px] border"
                    id="iframe"
                    width={1442}
                    frameBorder="0"
                    scrolling="no"
                    src={qrCodeUrl}
                ></iframe>
            </Modal>
        </div>
    );
};

export default WxAuthQrCodeModel;
