/**
 * @file 单张图片支持大图预览
 * <AUTHOR>
 */

import {Space, Image} from 'antd';

interface Props {
    width?: number;
    src: string;
    className?: string;
}
export default function SinglePreviewImage({width = 452, src, className}: Props) {
    return (
        <div className={`mt-2 flex ${className}`}>
            <Image
                width={width}
                className="min-h-[250px]"
                src={src}
                placeholder
                preview={
                    (src && {
                        minScale: 1,
                        maxScale: 3,
                        mask: (
                            <Space align="center">
                                <span className="iconfont icon-visual text-2xl" />
                                <span className="text-sm">查看图片</span>
                            </Space>
                        ),
                    }) ||
                    false
                }
            />
        </div>
    );
}
