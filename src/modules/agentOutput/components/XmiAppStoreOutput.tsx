import {OutputTypeConfig} from '../config';
import SinglePreviewImage from './SinglePreviewImage';

const imgSrc =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/agent-output/xiaomi-appstore-simple.jpg';
export default function XmiAppStoreOutput() {
    return (
        <div>
            <h1 className="text-xl font-medium">{OutputTypeConfig.XmiAppStore.name}</h1>
            <div className="mt-5 text-sm text-gray-secondary">
                部署后智能体可作为独立的小程序，在小米应用商店的以下场景中分发获得流量曝光
            </div>
            <div className="text-black-base pb-4 pt-5 text-[18px] font-medium leading-[26px]">小米应用商店搜索推荐</div>
            <div className="text-sm text-gray-secondary">用户在小米应用商店中搜索，会推荐相关的智能体</div>
            <SinglePreviewImage src={imgSrc} className="pt-2" />
        </div>
    );
}
