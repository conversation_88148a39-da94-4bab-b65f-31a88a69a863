/**
 * @file 网页链接
 * <AUTHOR>
 */

import {Button, Form, Input, message, Image, ConfigProvider} from 'antd';
import {useCallback} from 'react';
import {useParams} from 'react-router-dom';
import copy from 'copy-to-clipboard';
import {useResource} from 'react-suspense-boundary';
import api from '@/api/appVersion';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {getLogPage} from '../utils';
import {AgentUrlChannel, OutputTypeConfig} from '../config';

interface DetailContentProps {
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt;
}

function DetailContent({agentLogExt}: DetailContentProps) {
    const {id, appId} = useParams();

    const newAppId = id || appId || new URLSearchParams(location.search).get('appId') || '';

    const {clickLog} = useUbcLogV2();
    const page = getLogPage();

    // 获取短链和二维码
    const [{shareUrl, qrCode}] = useResource(api.agentShareLinkInfo, {
        appId: newAppId || '',
        channel: AgentUrlChannel.agentURL,
        qrCode: true,
    });

    // 复制链接
    const copyShareUrl = useCallback(() => {
        try {
            copy(shareUrl);
            message.success('链接已复制');
        } catch (e) {
            message.error('复制失败，请重试');
        }

        clickLog(EVENT_VALUE_CONST.WEB_LINK_COPY, page, {
            ...agentLogExt,
        });
    }, [shareUrl, clickLog, page, agentLogExt]);

    return (
        <div>
            <h1 className="mb-6 text-xl font-medium">{OutputTypeConfig.agentURL.name}</h1>
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            verticalLabelPadding: '0 0 6px',
                        },
                    },
                }}
            >
                <Form layout="vertical" className="w-full">
                    <Form.Item label="URL链接">
                        <div className="flex">
                            <Input
                                name="shareUrl"
                                readOnly
                                value={shareUrl}
                                styles={{input: {borderColor: '#dee0e7'}}}
                            />
                            <Button className="ml-4" type="primary" onClick={copyShareUrl}>
                                复制
                            </Button>
                        </div>
                    </Form.Item>
                    <Form.Item label="二维码">
                        <div className="inline-flex rounded-[0.5625rem] border border-gray-border-secondary p-[0.69rem]">
                            <Image className="h-32 w-32" src={qrCode} preview={false} />
                        </div>
                    </Form.Item>
                </Form>
            </ConfigProvider>
        </div>
    );
}

export default function AgentUrl({agentLogExt}: DetailContentProps) {
    return (
        <div>
            <CommonErrorBoundary pendingFallback={<Loading />}>
                <DetailContent agentLogExt={agentLogExt} />
            </CommonErrorBoundary>
        </div>
    );
}
