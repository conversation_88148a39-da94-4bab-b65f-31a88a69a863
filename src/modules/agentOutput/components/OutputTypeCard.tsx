/**
 * @file Agent对外部署列表卡片
 * <AUTHOR>
 */

import {ConfigProvider} from 'antd';
import classNames from 'classnames';
import React from 'react';
import CustomPopover from '@/components/Popover';
import {OutputType} from '../config';
import {OutputTypeInfo} from '../config';
import {CardType} from '../interface';

interface Props {
    /** 是否可用，不可用全部卡片置灰 */
    available: boolean;
    /** 部署类型卡片信息 */
    outputTypeInfo: OutputTypeInfo;
    /** 当前选中的output类型 */
    curOutputType: OutputType | null;
    /** output类型change事件 */
    outputTypeChange: (outputType: OutputType) => void;
    className?: string;
    // 处理关闭提示
    handleRemoveConfigTip?: () => void;
    cardType?: CardType;
}

export default function OutputTypeCard({
    available,
    outputTypeInfo,
    curOutputType,
    outputTypeChange,
    className,
    handleRemoveConfigTip,
    cardType = CardType.Deploy,
}: Props) {
    // available代表整体可用，outputTypeInfo.enabled代表单卡可用
    const enabled = available && outputTypeInfo.enabled;

    return (
        <ConfigProvider
            theme={{
                components: {
                    Popover: {
                        // 取消动效
                        motionDurationMid: '',
                        motionEaseInOutCirc: '',
                        motionEaseOutCirc: '',
                    },
                },
            }}
        >
            <CustomPopover
                align={{
                    offset: [0, 16],
                }}
                placement="bottom"
                open={outputTypeInfo.showConfigHint}
                content={outputTypeInfo.tipsContent}
                type="primary"
                onClose={handleRemoveConfigTip}
            >
                <div
                    className={classNames('flex rounded-[0.75rem] bg-white px-4', {
                        'hover:cursor-pointer hover:shadow-card-selected': enabled,
                        'hover:cursor-not-allowed': !enabled,
                        className,
                        'h-[7.4375rem] py-6': cardType === CardType.Deploy,
                        'h-[76px] py-4': cardType === CardType.Publish,
                    })}
                    key={outputTypeInfo.key}
                    onClick={() => enabled && outputTypeChange(outputTypeInfo.key)}
                >
                    <div
                        className={classNames('mr-4 flex items-center justify-center rounded-full', {
                            'bg-[#eceef3cc] text-black': outputTypeInfo.key !== curOutputType,
                            'bg-primary text-white': outputTypeInfo.key === curOutputType,
                            'text-black/40': !enabled,
                            'h-10 w-10': cardType === CardType.Deploy,
                            'h-[30px] w-[30px]': cardType === CardType.Publish,
                        })}
                    >
                        <span
                            className={classNames(`iconfont ${outputTypeInfo.icon} `, {
                                'text-2xl': cardType === CardType.Deploy,
                                'text-lg': cardType === CardType.Publish,
                            })}
                        ></span>
                    </div>
                    <div className="-mb-[10px] flex-1 overflow-hidden">
                        <div
                            className={classNames('font-medium', {
                                'text-primary': outputTypeInfo.key === curOutputType,
                                'text-black/40': !enabled,
                                'text-base': cardType === CardType.Deploy,
                                'text-sm leading-[22px]': cardType === CardType.Publish,
                            })}
                        >
                            {outputTypeInfo.name}
                        </div>
                        <div
                            className={classNames('mt-[0.38rem] text-gray-tertiary', {
                                'text-black/40': !enabled,
                                'text-sm leading-[1.3125rem]': cardType === CardType.Deploy,
                                'line-clamp-2': cardType === CardType.Deploy && !outputTypeInfo.deployStatusContent,
                                'line-clamp-1': cardType === CardType.Deploy && outputTypeInfo.deployStatusContent,
                                'line-clamp-1 text-xs leading-[20px]': cardType === CardType.Publish,
                            })}
                        >
                            {outputTypeInfo.desc}
                        </div>
                        {/* 部署状态 */}
                        {cardType === CardType.Deploy && outputTypeInfo.deployStatusContent && (
                            <div className="mt-2">{outputTypeInfo.deployStatusContent}</div>
                        )}
                    </div>
                </div>
            </CustomPopover>
        </ConfigProvider>
    );
}
