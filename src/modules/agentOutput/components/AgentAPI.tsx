/**
 * @file API调用
 * <AUTHOR>
 */

import {Button, ConfigProvider, Form, Input, Modal, Space, Tooltip, message} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import copy from 'copy-to-clipboard';
import classNames from 'classnames';
import {CacheProvider} from 'react-suspense-boundary';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import api from '@/api/appVersion';
import Loading from '@/components/Loading';
import DICTS from '@/dicts/home';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {getLogPage} from '../utils';
import {OutputTypeConfig} from '../config';

interface DetailContentProps {
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt;
}

function DetailContent({agentLogExt}: DetailContentProps) {
    const {id, appId} = useParams();

    const newAppId = id || appId || new URLSearchParams(location.search).get('appId') || '';

    const [loading, setLoading] = useState(true);
    // API ID 即appId
    const [apiId, setApiId] = useState('');
    // API 密钥
    const [secret, setSecret] = useState('');
    const [secretMask, setSecretMask] = useState(true);

    const [modal, modalContextHolder] = Modal.useModal();

    const {clickLog} = useUbcLogV2();
    const page = getLogPage();

    // 获取密钥
    useEffect(() => {
        (async () => {
            setLoading(true);

            try {
                const {appId: apiId, secret} = await api.getAgentSecretInfo({
                    appId: newAppId,
                });

                setApiId(apiId);
                setSecret(secret);
                setLoading(false);
            } catch (error) {
                setLoading(false);
            }
        })();
    }, [newAppId]);

    // 复制ID
    const copyAssistantId = useCallback(() => {
        try {
            copy(apiId);
            message.success('ID已复制');
        } catch (e) {
            message.error('复制失败，请重试');
        }

        clickLog(EVENT_VALUE_CONST.API_COPY_ID, page, {
            ...agentLogExt,
        });
    }, [apiId, clickLog, agentLogExt, page]);

    // 复制密钥
    const copyAssistantKey = useCallback(() => {
        try {
            copy(secret);
            message.success('密钥已复制');
        } catch (e) {
            message.error('复制失败，请重试');
        }

        clickLog(EVENT_VALUE_CONST.API_COPY_KEY, page, {
            ...agentLogExt,
        });
    }, [secret, clickLog, agentLogExt, page]);

    // 显示或者隐藏密钥
    const onSecretMask = useCallback(() => {
        setSecretMask(!secretMask);
    }, [secretMask]);

    // 重置更新密钥
    const updateSecretKey = useCallback(async () => {
        try {
            const {appId: apiId, secret} = await api.resetAgentSecretInfo(newAppId);

            setApiId(apiId);
            setSecret(secret);
            message.success('密钥更新成功');
        } catch (error: any) {
            // 接口失败
            if (error.response) {
                throw new Error('密钥更新失败');
            }

            // 其他类型错误直接抛出
            throw error;
        }
    }, [newAppId]);

    // 重置更新密钥确认弹窗
    const updateSecretKeyConfirm = useCallback(async () => {
        await modal.confirm({
            icon: <ExclamationCircleOutlined />,
            title: '确认要更新密钥？',
            content: '密钥更新后，已有引用也需重新输入密钥才可继续使用',
            cancelText: '取消',
            okText: '确认',
            centered: true,
            onOk() {
                return updateSecretKey();
            },
        });
    }, [modal, updateSecretKey]);

    const viewApiInfo = useCallback(() => {
        setTimeout(() => {
            window.open(DICTS.URL_DOC_OUTPUT_AGENT_API, '_blank');
        }, 300);
        clickLog(EVENT_VALUE_CONST.INVOKE_API_SPEC, page, {
            ...agentLogExt,
        });
    }, [clickLog, page, agentLogExt]);

    return (
        <div>
            <h1 className="mb-6 text-xl font-medium">{OutputTypeConfig.agentAPI.name}</h1>
            {loading ? (
                <Loading />
            ) : (
                <ConfigProvider
                    theme={{
                        components: {
                            Form: {
                                verticalLabelPadding: '0 0 6px',
                            },
                        },
                    }}
                >
                    <Form layout="vertical" className="w-full" disabled={!secret}>
                        <Form.Item label="ID">
                            <div className="flex">
                                <Input name="apiId" readOnly value={apiId} styles={{input: {borderColor: '#dee0e7'}}} />
                                <Button className="ml-4" type="primary" onClick={copyAssistantId}>
                                    复制
                                </Button>
                            </div>
                        </Form.Item>
                        <Form.Item label="密钥">
                            <div className="flex">
                                {/* TODO: 可以用Input.Password吗 */}
                                <Input
                                    name="secret"
                                    value={secretMask ? (secret ? '*'.repeat(secret.length) : '') : secret}
                                    readOnly
                                    styles={{
                                        affixWrapper: {padding: '3px 11px', lineHeight: 1.5, borderColor: '#dee0e7'},
                                        input: {lineHeight: 1.5},
                                    }}
                                    suffix={
                                        <Space size={[12, 0]} align="center">
                                            <Tooltip arrow title={'更新密钥'}>
                                                <span
                                                    className="iconfont icon-update inline-block h-6 cursor-pointer text-[1.1125rem] leading-6"
                                                    onClick={updateSecretKeyConfirm}
                                                />
                                            </Tooltip>
                                            <Tooltip arrow title={secretMask ? '显示密钥' : '隐藏密钥'}>
                                                <span
                                                    className={classNames(
                                                        'inline-block h-6 cursor-pointer text-[1.1125rem] leading-6',
                                                        {
                                                            'iconfont icon-hide': secretMask,
                                                            'iconfont icon-visual': !secretMask,
                                                        }
                                                    )}
                                                    onClick={onSecretMask}
                                                />
                                            </Tooltip>
                                        </Space>
                                    }
                                />
                                <Button className="ml-4" type="primary" onClick={copyAssistantKey}>
                                    复制
                                </Button>
                            </div>
                        </Form.Item>
                        <Form.Item className="-mt-[13px]">
                            <Space align="center">
                                <span className="text-gray-secondary">
                                    内测阶段每天可调用<span className="font-medium text-black">500</span>次
                                </span>
                                <a target="_blank" onClick={viewApiInfo} rel="noreferrer">
                                    点击查看API调用说明
                                </a>
                            </Space>
                        </Form.Item>
                        {modalContextHolder}
                    </Form>
                </ConfigProvider>
            )}
        </div>
    );
}

export default function AgentAPI({agentLogExt}: DetailContentProps) {
    return (
        <div>
            <CacheProvider>
                <CommonErrorBoundary>
                    <DetailContent agentLogExt={agentLogExt} />
                </CommonErrorBoundary>
            </CacheProvider>
        </div>
    );
}
