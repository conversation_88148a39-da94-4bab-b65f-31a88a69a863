/**
 * @file 微信公众号
 * <AUTHOR>
 */

import {Button, Radio} from 'antd';
import {useCallback, useState} from 'react';
import classNames from 'classnames';
import DownloadQR from '@/components/GenerateQRCode/downloadQR';
import TabRadioGroup from '@/components/TabRadioGroup';
import {WxAccountType} from '@/api/agentDeploy/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import {getLogPage} from '../utils';
import {
    AgentUrlChannel,
    OutputTypeConfig,
    WeChatOfficialAccountType,
    WeChatOfficialAccountTypeNames,
    addChannelToAgentWebUrl,
    WechatOfficialTabLogMap,
} from '../config';
import LinkCopyUrl from './LinkCopyUrl';
import SinglePreviewImage from './SinglePreviewImage';
import WeChatAccountAuth from './WeChatAccountAuth';

interface Props {
    /** web化 agent预览 url */
    previewUrl: string;
    appId: string;
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt;
}

export default function WeChatOfficialAccount({previewUrl = '', appId, agentLogExt}: Props) {
    const [curType, setCurType] = useState<WeChatOfficialAccountType>(WeChatOfficialAccountType.Menu);
    const {clickLog} = useUbcLogV2();

    const page = getLogPage();

    const onChangeType = useCallback(
        (e: any) => {
            setCurType(e.target.value);
            const value = e.target.value as WeChatOfficialAccountType;
            clickLog(EVENT_VALUE_CONST.WECHAT_OFFICIAL_TAB, page, {
                ...agentLogExt,
                [EVENT_EXT_KEY_CONST.WECHAT_OFFICIAL_TAB_NAME]: WechatOfficialTabLogMap[value],
            });
        },
        [setCurType, clickLog, agentLogExt, page]
    );

    return (
        <div className="mb-8">
            <h1 className="mb-6 text-xl font-medium">{OutputTypeConfig.WeChatOfficialAccount.name}</h1>
            <TabRadioGroup value={curType} onChange={onChangeType}>
                <Radio.Button className="w-32" value={WeChatOfficialAccountType.ServerMessage}>
                    {WeChatOfficialAccountTypeNames[WeChatOfficialAccountType.ServerMessage]}
                </Radio.Button>
                <Radio.Button className="w-32" value={WeChatOfficialAccountType.SubscribeMessage}>
                    {WeChatOfficialAccountTypeNames[WeChatOfficialAccountType.SubscribeMessage]}
                </Radio.Button>
                <Radio.Button className="w-32" value={WeChatOfficialAccountType.Menu}>
                    {WeChatOfficialAccountTypeNames[WeChatOfficialAccountType.Menu]}
                </Radio.Button>
                <Radio.Button className="w-32" value={WeChatOfficialAccountType.AutoReply}>
                    {WeChatOfficialAccountTypeNames[WeChatOfficialAccountType.AutoReply]}
                </Radio.Button>
            </TabRadioGroup>
            <div className={classNames({hidden: curType !== WeChatOfficialAccountType.Menu})}>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">配置公众号菜单</h3>
                    <div className="text-sm text-gray-secondary">
                        <p>通过公众号自定义菜单配置智能体入口，用户点击菜单会跳转到智能体聊天页面。</p>
                        <p>备注：必须是服务号或者已认证订阅号才能配置菜单。</p>
                    </div>
                </section>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">步骤</h3>
                    <div className="text-sm text-gray-secondary">
                        <p>
                            1. 访问
                            <Button
                                type="link"
                                target="_blank"
                                href="https://mp.weixin.qq.com/"
                                className="h-auto border-0 p-0"
                            >
                                微信公众平台
                            </Button>
                            并登录你的服务号。
                        </p>
                        <p className="mt-4">2. 依次选择: 内容与互动 &gt; 自定义菜单</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E9%85%8D%E7%BD%AE%E5%85%AC%E4%BC%97%E5%8F%B7%E8%8F%9C%E5%8D%95-%E6%AD%A5%E9%AA%A42.png" />
                        <p className="mt-4">3. 配置菜单名称以及消息类型，这里选择跳转网页</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E9%85%8D%E7%BD%AE%E5%85%AC%E4%BC%97%E5%8F%B7%E8%8F%9C%E5%8D%95-%E6%AD%A5%E9%AA%A43.png" />
                        <p className="mt-4">
                            4. 使用智能体h5 聊天对话链接{' '}
                            <LinkCopyUrl
                                url={addChannelToAgentWebUrl(previewUrl, AgentUrlChannel.weChatOfficialAccountMenu)}
                                copyText="点此复制链接"
                            />
                            ，填写跳转网页链接，并保存发布。
                        </p>
                    </div>
                </section>
            </div>
            <div className={classNames({hidden: curType !== WeChatOfficialAccountType.AutoReply})}>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">配置自动回复</h3>
                    <div className="text-sm text-gray-secondary">
                        通过公众号配置自动回复，帮助用户获取智能体聊天链接。
                    </div>
                </section>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">步骤</h3>
                    <div className="text-sm text-gray-secondary">
                        <p>
                            1. 访问
                            <Button
                                type="link"
                                target="_blank"
                                href="https://mp.weixin.qq.com/"
                                className="h-auto border-0 p-0"
                            >
                                微信公众平台
                            </Button>
                            并登录你的服务号。
                        </p>
                        <p className="mt-4">2. 依次选择: 内容与互动 &gt; 自动回复</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E6%AD%A5%E9%AA%A42.png" />
                        <p className="mt-2">
                            <p>这里可以设置三种类型回复</p>
                            <ul className="ml-6 mt-2 list-disc leading-6">
                                <li>
                                    <span className="font-medium text-black">关注回复：</span>
                                    粉丝在关注您的公众号时，会自动发送您设置的文字/音频/图片/视频给粉丝;
                                </li>
                                <li>
                                    <span className="font-medium text-black">收到消息回复：</span>
                                    在粉丝给您平台发送微信消息时，自动回复您设置的文字/音频/图片/视频给粉丝;
                                </li>
                                <li>
                                    <span className="font-medium text-black">关键词自动回复：</span>
                                    当粉丝发送的消息内容里面包含了你设置的关键字时，自动回复相关的内容给粉丝。回复内容可以选择智能体文本链接或者二维码图片。
                                </li>
                            </ul>
                        </p>
                    </div>
                </section>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">自动回复文本链接</h3>
                    <div className="text-sm text-gray-secondary">
                        <p>1. 在关键词回复里添加回复，依次输入规则名称，选择关键字，回复内容选择文字</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8Da%E6%A0%87%E7%AD%BE%E8%AE%BE%E7%BD%AE-%E6%AD%A5%E9%AA%A41.png" />
                        <p className="mt-4">
                            2. 将这段代码复制并粘贴到输入框内，&lt;a
                            href=&quot;你要放入的链接&quot;&gt;起个名字&lt;/a&gt;；
                        </p>
                        <p className="mt-4">
                            3. 将要跳转的链接地址
                            <LinkCopyUrl
                                url={addChannelToAgentWebUrl(
                                    previewUrl,
                                    AgentUrlChannel.weChatOfficialAccountAutoReply
                                )}
                                copyText="链接地址复制"
                            />
                            以及需要显示的文字插入到代码里，位置不可以出错；然后点击确定并保存；
                        </p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8Da%E6%A0%87%E7%AD%BE%E8%AE%BE%E7%BD%AE-%E6%AD%A5%E9%AA%A43.png" />
                    </div>
                </section>
                <section>
                    <h3 className="mb-4 mt-6 text-lg font-medium">自动回复二维码图片</h3>
                    <div className="text-sm text-gray-secondary">
                        <p>1. 在关键词回复里添加回复，依次输入规则名称、选择关键字、回复内容点击图片</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D%E4%BA%8C%E7%BB%B4%E7%A0%81-%E6%AD%A5%E9%AA%A41.png" />
                        <p className="mt-4">
                            2. 点击
                            <DownloadQR
                                url={addChannelToAgentWebUrl(
                                    previewUrl,
                                    AgentUrlChannel.weChatOfficialAccountAutoReply
                                )}
                                downloadText="下载智能体二维码"
                            />
                        </p>
                        <p className="mt-4">3. 上传平台下载的智能体二维码图片，并选择确定保存</p>
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D%E4%BA%8C%E7%BB%B4%E7%A0%81-%E6%AD%A5%E9%AA%A43-1.png" />
                        <SinglePreviewImage src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/wechat_account/%E5%8F%91%E5%B8%83%E5%88%B0%E5%BE%AE%E4%BF%A1%E5%85%AC%E4%BC%97%E5%8F%B7-%E6%B7%BB%E5%8A%A0%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D-%E8%87%AA%E5%8A%A8%E5%9B%9E%E5%A4%8D%E4%BA%8C%E7%BB%B4%E7%A0%81-%E6%AD%A5%E9%AA%A43-2.png" />
                    </div>
                </section>
            </div>
            {/* 微信服务号 */}
            <WeChatAccountAuth
                className={classNames({hidden: curType !== WeChatOfficialAccountType.ServerMessage})}
                wxAccountType={WxAccountType.WxServer}
                appId={appId}
            />

            {/* 微信订阅号 */}
            <WeChatAccountAuth
                className={classNames({hidden: curType !== WeChatOfficialAccountType.SubscribeMessage})}
                wxAccountType={WxAccountType.WxSubscribe}
                appId={appId}
            />
        </div>
    );
}
