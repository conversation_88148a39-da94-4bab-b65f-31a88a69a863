/**
 * @file js代码高亮
 * <AUTHOR>
 */
import {Space, Tooltip, message} from 'antd';
import copy from 'copy-to-clipboard';
import {useCallback} from 'react';
import {PrismLight as SyntaxHighlighter} from 'react-syntax-highlighter';
import Javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript.js';
import OneLight from 'react-syntax-highlighter/dist/esm/styles/prism/one-light.min.js';

SyntaxHighlighter.registerLanguage('js', Javascript);

interface Props {
    /** 高量的代码 */
    codeStr: string;
    /** 高量代码容器高度，不是外层block的高度 */
    height?: number;
    /** language 标记性语言类型 可以是HTML、CSS、JS */
    languageText?: string;
}

export default function CodeHighLight({codeStr = '', height = 275, languageText = 'JS'}: Props) {
    // 复制代码
    const copyCode = useCallback(() => {
        try {
            copy(codeStr);
            message.success('代码已复制');
        } catch (e) {
            message.error('复制失败，请重试');
        }
    }, [codeStr]);

    return (
        <div className="rounded-md border bg-[#fbfcfd]">
            <div className="flex h-[34px] items-center justify-between rounded-t-md bg-gray-bg-base px-[0.62rem]">
                <span className="text-xs">{languageText}</span>
                <Tooltip arrow placement="bottom" title={'复制代码'} className="cursor-pointer">
                    <Space align="center" onClick={copyCode}>
                        <span className="iconfont icon-copy" />
                        <div className="text-xs">Copy Code</div>
                    </Space>
                </Tooltip>
            </div>
            <div className="overflow-y-auto rounded-md px-4 pb-[1.38rem] pt-[0.81rem]" style={{height: height + 'px'}}>
                <SyntaxHighlighter
                    language="js"
                    style={OneLight}
                    customStyle={{backgroundColor: '#fbfcfd'}}
                    wrapLongLines
                >
                    {codeStr}
                </SyntaxHighlighter>
            </div>
        </div>
    );
}
