/**
 * @file 复制链接（link样式）
 * <AUTHOR>
 */

import {Button, message} from 'antd';
import copy from 'copy-to-clipboard';
import {useCallback} from 'react';

export default function LinkCopyUrl({url, copyText = '点击复制智能体链接'}: {url: string; copyText?: string}) {
    // 复制链接
    const copyUrl = useCallback(() => {
        try {
            copy(url);
            message.success('链接已复制');
        } catch (e) {
            message.error('复制失败，请重试');
        }
    }, [url]);

    return (
        <Button disabled={!url} type="link" className="h-auto border-0 p-0" onClick={copyUrl}>
            {copyText}
        </Button>
    );
}
