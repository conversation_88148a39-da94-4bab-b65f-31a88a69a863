/**
 * @file JS代码嵌入
 * <AUTHOR>
 */

import {Button} from 'antd';
import {useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import DICTS from '@/dicts/home';
import {JsSDKType, OutputTypeConfig, JsSDKTypeNames} from '../config';
import CodeHighlight from './JSCodeHighlight';
import SinglePreviewImage from './SinglePreviewImage';

/**
 * JS SDK 部署代码模版
 * TODO：待C端jsSDK实现用户调用会话次数限制和百度passport登录需求，最终cdn模版代码可能还需要调整
 */
const codeTemplate = `const scriptEl = document.createElement("script");
scriptEl.src = "https://unpkg.com/lingjing-agent@latest/dist/js/index.global.js";
document.body.appendChild(scriptEl);
scriptEl.onload = () => {
    const chat = new LingJingAgent.Chat({
        agentId: "{{appId}}",
        uid: 1234567890,
        container: undefined,
        entry: {
            container: undefined,
            image: undefined,
        },
    });
    chat.showEntry();
}`;

export default function JsSDKOutput() {
    const {id: appId = ''} = useParams();
    const [curJsSDKType] = useState<JsSDKType>(JsSDKType.FloatingBall);
    const [codeStr, setCodeStr] = useState(codeTemplate);

    useEffect(() => {
        setCodeStr(codeTemplate.replace(/{{appId}}/g, appId).replace(/{{type}}/g, curJsSDKType));
    }, [appId, curJsSDKType]);

    return (
        <div>
            <h1 className="mb-6 text-xl font-medium">{OutputTypeConfig.jsSDK.name}</h1>
            <h3 className="mb-4 text-base font-medium">{JsSDKTypeNames[curJsSDKType]}</h3>
            <SinglePreviewImage
                className="mt-4"
                src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/jssdk/JS%E4%BB%A3%E7%A0%81%E5%B5%8C%E5%85%A5-%E6%82%AC%E6%B5%AE%E7%90%83.png"
            />
            <SinglePreviewImage
                className="mb-6"
                src="https://lingjing-online.bj.bcebos.com/lingjing-online/agent_output/jssdk/JS%E4%BB%A3%E7%A0%81%E5%B5%8C%E5%85%A5-%E5%AF%B9%E8%AF%9D%E9%9D%A2%E6%9D%BF.png"
            />
            <h3 className="mb-4 text-base font-medium">部署方式</h3>
            <div className="mb-4 mt-4">
                <CodeHighlight codeStr={codeStr} />
            </div>
            <Button
                type="link"
                target="_blank"
                href={DICTS.URL_DOC_OUTPUT_AGENT_JS_SDK}
                className="mb-6 h-auto border-0 p-0"
            >
                点击查看代码包部署说明
            </Button>
        </div>
    );
}
