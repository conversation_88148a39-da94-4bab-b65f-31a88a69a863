/**
 * @file Agent对外部署页面配置
 * <AUTHOR>
 */
import {WechatOfficialTabType} from '@/utils/loggerV2/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';

/**
 * 对外输出类型枚举
 */
export enum OutputType {
    /** 网页链接 */
    AgentURL = 'agentURL',
    /** API调用 */
    AgentAPI = 'agentAPI',
    /** JS代码嵌入 */
    JsSDK = 'jsSDK',
    /** 发布到微信公众号 */
    WeChatOfficialAccount = 'WeChatOfficialAccount',
    /** 微信小程序嵌入 */
    WeChatMiniProgram = 'WeChatMiniProgram',
    /** 微信机器人嵌入 */
    WeChatBot = 'WeChatBot',
    /** 微信服务号账号接入 */
    WeChatServerAccount = 'WeChatServerAccount',
    /** 微信订阅号接入 */
    WeChatSubscribeAccount = 'WeChatSubscribeAccount',
    /** 小米应用商店 */
    XmiAppStore = 'XmiAppStore',
}

/**
 * Agent对外部署类型卡片信息
 */
export interface OutputTypeInfo {
    /**
     * 对外部署类型
     */
    key: OutputType;
    /**
     * 类型名称
     */
    name: string;
    /**
     * 类型介绍
     */
    desc: string;
    /**
     * icon图片
     */
    icon: string;
    /**
     * 是否可用
     */
    enabled: boolean;
    /**
     * 是否展示提示
     */
    showConfigHint: boolean;
    /**
     * 提示内容
     */
    tipsContent?: React.ReactNode;
    /**
     * 部署状态提示
     */
    deployStatusContent: React.ReactNode;
}

export const OutputTypeConfig: Record<OutputType, OutputTypeInfo> = {
    [OutputType.AgentURL]: {
        key: OutputType.AgentURL,
        name: '网页链接',
        desc: '支持以链接或者二维码的形式对外分享',
        icon: 'icon-web',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.AgentAPI]: {
        key: OutputType.AgentAPI,
        name: 'API调用',
        desc: '支持以 API的形式灵活调用',
        icon: 'icon-API',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.JsSDK]: {
        key: OutputType.JsSDK,
        name: 'JS代码嵌入',
        desc: '支持以代码包或代码块的形式快速部署到网页',
        icon: 'icon-code',
        // MVP版本JSSDK部署类型卡片入口先置灰，后续迭代
        enabled: false,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.WeChatOfficialAccount]: {
        key: OutputType.WeChatOfficialAccount,
        name: '发布到微信公众号',
        desc: '支持在微信公众号与智能体对话',
        icon: 'icon-a-WeChatPublic',
        enabled: true,
        showConfigHint: false,
        tipsContent: '发布到微信公众号支持服务号和订阅号啦，快来试试吧~',
        deployStatusContent: '',
    },
    [OutputType.WeChatMiniProgram]: {
        key: OutputType.WeChatMiniProgram,
        name: '发布到微信小程序',
        desc: '支持智能体发布为独立小程序',
        icon: 'icon-a-WechatMini',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.WeChatBot]: {
        key: OutputType.WeChatBot,
        name: '发布到企业微信',
        desc: '支持快速部署到企业微信',
        icon: 'icon-bot',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.WeChatServerAccount]: {
        key: OutputType.WeChatServerAccount,
        name: '微信公众号-服务号',
        desc: '',
        icon: 'icon-bot',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.WeChatSubscribeAccount]: {
        key: OutputType.WeChatSubscribeAccount,
        name: '微信公众号-订阅号',
        desc: '',
        icon: 'icon-bot',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
    [OutputType.XmiAppStore]: {
        key: OutputType.XmiAppStore,
        name: '发布到小米应用商店',
        desc: '支持智能体在小米应用商店内进行分发推荐',
        icon: 'icon-xiaomi',
        enabled: true,
        showConfigHint: false,
        tipsContent: '',
        deployStatusContent: '',
    },
};

/**
 * JS SDK tab类型
 */
export enum JsSDKType {
    /** 悬浮球 */
    FloatingBall = 'floatingBall',
    /** 机器人头像 */
    RobotAvatar = 'robotAvatar',
    /** mini对话框 */
    MiniDialogBox = 'miniDialogBox',
}

export const JsSDKTypeNames = {
    [JsSDKType.FloatingBall]: '悬浮球',
    [JsSDKType.RobotAvatar]: '机器人头像',
    [JsSDKType.MiniDialogBox]: 'mini对话框',
};

/**
 * JS SDK tab类型
 */
export enum WeChatOfficialAccountType {
    /** 订阅号消息 */
    SubscribeMessage = 'subscribeMessage',
    /** 服务号消息 */
    ServerMessage = 'serverMessage',
    /** 配置公众号菜单 */
    Menu = 'menu',
    /** 配置自动回复 */
    AutoReply = 'autoReply',
}

export const WeChatOfficialAccountTypeNames = {
    [WeChatOfficialAccountType.SubscribeMessage]: '接管订阅号消息',
    [WeChatOfficialAccountType.ServerMessage]: '接管服务号消息',
    [WeChatOfficialAccountType.Menu]: '配置公众号菜单',
    [WeChatOfficialAccountType.AutoReply]: '配置自动回复',
};

/**
 * Agent 对外部署提供的Url的渠道值
 * 是一串数字，遵循手百渠道定义规则：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/iaBNihC3Vx/_tcWkrs_7n/IvG42KBo0TwFAX
 */
export const AgentUrlChannel = {
    /** 网页链接 */
    agentURL: '****************',
    /** 微信公众号菜单 */
    weChatOfficialAccountMenu: '****************',
    /** 微信公众号自动回复 */
    weChatOfficialAccountAutoReply: '****************',
    /** 微信小程序 MVP不上，上线了再申请渠道值补上 */
    weChatMiniProgram: '',
    /** 微信机器人 */
    weChatBot: '****************',
};

/**
 * 拼接Agent WebUrl渠道值
 * @param url Agent web化url
 * @param channel 渠道值
 * @returns 拼接渠道值后的Agent web化url
 */
export const addChannelToAgentWebUrl = (url: string, channel: string) => {
    if (!url) return '';

    if (!channel) return url;

    const urlObject = new URL(url);
    // 渠道值
    urlObject.searchParams.set('_swebScene', channel);

    return urlObject.toString();
};

export const WechatOfficialTabLogMap = {
    [WeChatOfficialAccountType.ServerMessage]: WechatOfficialTabType.SERVER,
    [WeChatOfficialAccountType.SubscribeMessage]: WechatOfficialTabType.SUBSCRIBE,
    [WeChatOfficialAccountType.Menu]: WechatOfficialTabType.OFFICIAL,
    [WeChatOfficialAccountType.AutoReply]: WechatOfficialTabType.AUTO_REPLY,
};

export const CardTypeLogMap = {
    [OutputType.AgentURL]: EVENT_VALUE_CONST.WEB_LINK,
    [OutputType.AgentAPI]: EVENT_VALUE_CONST.INVOKE_API,
    [OutputType.JsSDK]: EVENT_VALUE_CONST.JS_CODE,
    [OutputType.WeChatOfficialAccount]: EVENT_VALUE_CONST.WECHAT_OFFICIAL,
    [OutputType.WeChatMiniProgram]: EVENT_VALUE_CONST.WECHAT_MINI_APP,
    [OutputType.WeChatBot]: EVENT_VALUE_CONST.WECHAT_BUSINESS,
    [OutputType.WeChatServerAccount]: EVENT_VALUE_CONST.WECHAT_SERVER,
    [OutputType.WeChatSubscribeAccount]: EVENT_VALUE_CONST.WECHAT_SUBSCRIBE,
    [OutputType.XmiAppStore]: EVENT_VALUE_CONST.XIAOMI_APP_STORE,
};
