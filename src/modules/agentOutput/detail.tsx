/**
 * @file Agent对外部署详情
 * <AUTHOR>
 */
import {WxAccountType} from '@/api/agentDeploy/interface';
import {AgentLogExt} from '@/utils/loggerV2/interface';
import AgentUrl from './components/AgentUrl';
import JsSDKOutput from './components/JsSDKOutput';
import WeChatBot from './components/WeChatBot';
import AgentAPI from './components/AgentAPI';
import WeChatOfficialAccount from './components/WeChatOfficialAccount';
import WeChatAccountAuth from './components/WeChatAccountAuth';
import {OutputType} from './config';
import XmiAppStoreOutput from './components/XmiAppStoreOutput';

interface Props {
    /** 当前部署类型 */
    outputType: OutputType;
    /** web化 agent预览 url */
    previewUrl?: string;
    /** 智能体Id */
    appId: string;
    /** 智能体打点信息 */
    agentLogExt?: AgentLogExt;
    getAgentInfo?: () => void;
    setWxSetPrivacy?: (status: boolean) => void;
}

// eslint-disable-next-line complexity
export default function AgentOutputDetail({
    outputType,
    previewUrl,
    appId,
    agentLogExt,
    getAgentInfo,
    setWxSetPrivacy,
}: Props) {
    return (
        <>
            {outputType === OutputType.AgentURL && <AgentUrl agentLogExt={agentLogExt} />}
            {outputType === OutputType.AgentAPI && <AgentAPI agentLogExt={agentLogExt} />}
            {outputType === OutputType.JsSDK && <JsSDKOutput />}
            {outputType === OutputType.WeChatOfficialAccount && (
                <WeChatOfficialAccount previewUrl={previewUrl || ''} appId={appId} agentLogExt={agentLogExt} />
            )}
            {outputType === OutputType.WeChatMiniProgram && (
                <WeChatAccountAuth
                    wxAccountType={WxAccountType.WxMiniApp}
                    appId={appId}
                    agentLogExt={agentLogExt}
                    showTitle
                    getAgentInfo={getAgentInfo}
                    setWxSetPrivacy={setWxSetPrivacy}
                />
            )}
            {outputType === OutputType.WeChatBot && <WeChatBot previewUrl={previewUrl || ''} />}
            {outputType === OutputType.WeChatServerAccount && (
                <WeChatAccountAuth wxAccountType={WxAccountType.WxServer} appId={appId} showTitle />
            )}
            {outputType === OutputType.WeChatSubscribeAccount && (
                <WeChatAccountAuth wxAccountType={WxAccountType.WxSubscribe} appId={appId} showTitle />
            )}
            {outputType === OutputType.XmiAppStore && <XmiAppStoreOutput />}
        </>
    );
}
