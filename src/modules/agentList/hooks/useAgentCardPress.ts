/**
 * @file 获取 AgentCard 点击状态
 * <AUTHOR>
 * @date 2024/09/20
 * @description 卡片点击状态逻辑抽离
 */

import {useCallback, useState} from 'react';

export const useAgentCardPress = () => {
    const [isPress, setIsPress] = useState(false);
    const handleMouseDown = useCallback(() => {
        setIsPress(true);
    }, []);
    const handleMouseUp = useCallback(() => {
        setIsPress(false);
    }, []);
    const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
    }, []);
    const handleMouseLeave = useCallback((event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
        setIsPress(false);
    }, []);
    return {
        handleMouseDown,
        handleMouseUp,
        handleMouseEnter,
        handleMouseLeave,
        isPress,
    };
};
