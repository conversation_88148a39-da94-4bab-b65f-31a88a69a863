/**
 * @file Agent列表-动态Tab hooks
 * <AUTHOR>
 */

import {useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import urls from '@/links';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/index';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {AgentTabType, AGENT_TAB_TYPE_NAME, AGENT_TAB_TYPE_NAME_M, RefreshInfo, AGENT_TAB_TYPE_LOG} from '../interface';

export interface TabItemData {
    id: AgentTabType;
    description: string;
    route: string;
}

const getTabItems = (
    isMobile?: boolean
): Record<
    AgentTabType,
    {
        id: AgentTabType;
        description: string;
        route: string;
    }
> => {
    const tabNameMaps = isMobile ? AGENT_TAB_TYPE_NAME_M : AGENT_TAB_TYPE_NAME;
    return {
        [AgentTabType.Codeless]: {
            id: AgentTabType.Codeless,
            description: tabNameMaps[AgentTabType.Codeless],
            route: urls.agentList.fill({tab: AgentTabType.Codeless}),
        },
        [AgentTabType.Workflow]: {
            id: AgentTabType.Workflow,
            description: tabNameMaps[AgentTabType.Workflow],
            route: urls.agentList.fill({tab: AgentTabType.Workflow}),
        },
        [AgentTabType.LowCode]: {
            id: AgentTabType.LowCode,
            description: tabNameMaps[AgentTabType.LowCode],
            route: urls.agentList.fill({tab: AgentTabType.LowCode}),
        },
        [AgentTabType.QianFan]: {
            id: AgentTabType.QianFan,
            description: tabNameMaps[AgentTabType.QianFan],
            route: urls.agentList.fill({tab: AgentTabType.QianFan}),
        },
    };
};

const isMobile = isMobileDevice();

export default function useAgentTypeTabs(
    pageLoading: boolean,
    hasCodelessAgent: boolean,
    hasLowCodeAgent: boolean,
    hasWorkflowAgent: boolean,
    qianFanRefreshInfo: RefreshInfo
) {
    const navigate = useNavigate();
    // 获取动态路由tab参数
    const {tab} = useParams();
    const tabItemMaps = getTabItems(isMobile);

    // tabs，默认有零代码&低代码
    const tabItems = useMemo(
        () => [
            tabItemMaps[AgentTabType.Codeless],
            tabItemMaps[AgentTabType.Workflow],
            tabItemMaps[AgentTabType.LowCode],
        ],
        [tabItemMaps]
    );

    // 选中的tab key
    const [activeTabKey, setActiveTabKey] = useState<AgentTabType | null>(AgentTabType.Codeless);
    const prevActiveTabKeyRef = useRef<AgentTabType | null>(null);
    const {displayLog} = useUbcLogV2();

    // 获取选中第一个有agent的分类Tab key，选中顺序 【零代码】>【工作流模式】>【低代码】>【千帆（&isFromQianFan=true）】
    const getDefActiveKey = useCallback(() => {
        let key = AgentTabType.Codeless;

        if (hasCodelessAgent) {
            key = AgentTabType.Codeless;
        } else if (hasWorkflowAgent) {
            key = AgentTabType.Workflow;
        } else if (hasLowCodeAgent) {
            key = AgentTabType.LowCode;
        } else if (qianFanRefreshInfo?.agentListRes?.agentList?.length > 0) {
            key = AgentTabType.QianFan;
        } else {
            key = AgentTabType.Codeless;
        }
        return key;
    }, [hasCodelessAgent, hasLowCodeAgent, hasWorkflowAgent, qianFanRefreshInfo?.agentListRes?.agentList?.length]);

    // 设置当前选中 Tab
    useEffect(() => {
        const urlTabKey = tab as AgentTabType;

        // 如果动态tab路由无效
        // 则 按顺序选中第一个有agent的分类Tab， 顺序优先级 【零代码】>【低代码】
        // 并重定向选中tab路由
        if (pageLoading) {
            return;
        }

        if (!tabItemMaps[urlTabKey] || urlTabKey === AgentTabType.QianFan) {
            const key = getDefActiveKey();

            setActiveTabKey(key);
            navigate(tabItemMaps[key].route, {replace: true});

            return;
        }

        // 动态路由tab!=千帆，但动态路由tab 有效，选中对应tab
        if (tabItemMaps[urlTabKey]) {
            setActiveTabKey(urlTabKey);
            return;
        }

        setActiveTabKey(getDefActiveKey());
    }, [getDefActiveKey, pageLoading, navigate, tab, tabItemMaps]);

    /** Tab 切换展示对应列表 */
    const tabChange = useCallback(
        (key: AgentTabType) => {
            setActiveTabKey(key);

            navigate(tabItemMaps[key].route);
            return Promise.resolve();
        },
        [navigate, tabItemMaps]
    );

    // 页面展现打点，用于点展比统计，每次Tab改变都打
    useEffect(() => {
        // 这次Tab与上次不同才打点，不然初始加载时，会打多次
        if (!pageLoading && activeTabKey && prevActiveTabKeyRef.current !== activeTabKey) {
            displayLog(EVENT_PAGE_CONST.MY_AGENT, {
                [EVENT_EXT_KEY_CONST.MY_AGENT_TYPE]: AGENT_TAB_TYPE_LOG[activeTabKey],
            });
            prevActiveTabKeyRef.current = activeTabKey;
        }
    }, [activeTabKey, displayLog, pageLoading]);

    return {
        tabItems,
        activeTabKey,
        tabChange,
    };
}
