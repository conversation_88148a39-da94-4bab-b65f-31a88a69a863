/**
 * @file Agent列表-刷新hooks
 * <AUTHOR>
 */

import {useCallback, useEffect, useState} from 'react';
import api from '@/api/agentList';
import {
    AgentListExtInfo,
    AgentSource,
    AgentType,
    ExtDataType,
    GetAgentListExtResponse,
} from '@/api/agentList/interface';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {GetAgentListParams} from '@/api/agentList/interface';
import {AgentTabType, DEFAULT_PAGINATION_SETTINGS, RefreshInfo} from '../interface';

const getInitRefreshInfo = () => {
    return {
        isLoading: true,
        isError: false,
        agentListRes: {
            total: 0,
            pageNo: DEFAULT_PAGINATION_SETTINGS.pageNo,
            pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
            isFromQianFan: false,
            agentList: [],
        },
    };
};

export const agentExtArrToMap = (res: GetAgentListExtResponse) => {
    const map: Record<string, AgentListExtInfo> = {};
    res.forEach(item => {
        if (item.appId) {
            map[item.appId] = item;
        }
    });
    return map;
};

const refreshList = async (agentTabType: AgentTabType, info: RefreshInfo): Promise<RefreshInfo> => {
    try {
        const params: GetAgentListParams = {
            agentSource: agentTabType === AgentTabType.QianFan ? AgentSource.QianFan : AgentSource.LingJing,
            agentType: agentTabType === AgentTabType.LowCode ? AgentType.LowCode : AgentType.Codeless,
            pageNo: DEFAULT_PAGINATION_SETTINGS.pageNo,
            pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
        };

        // 工作流模式 agent,需要传入 modeType
        if (agentTabType === AgentTabType.Workflow) {
            params.modeType = AgentModeType.Workflow;
        }

        const res = await api.getAgentList(params);

        return {
            ...info,
            agentListRes: res,
            isLoading: false,
            isError: false,
        };
    } catch (error) {
        return {
            ...info,
            isLoading: false,
            isError: true,
            error,
        };
    }
};

const refreshExt = async (agentType: AgentType): Promise<Record<string, AgentListExtInfo>> => {
    try {
        const res = await api.getAgentListExt({
            agentSource: AgentSource.LingJing,
            agentType,
            ...DEFAULT_PAGINATION_SETTINGS,
            type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution},${ExtDataType.tags},${ExtDataType.Dynamic},${ExtDataType.XmiAuditInfo}`,
        });

        return agentExtArrToMap(res);
    } catch (error) {
        return {};
    }
};

export default function useRefreshAgentList() {
    const [codelessRefreshInfo, setCodelessRefreshInfo] = useState<RefreshInfo>(getInitRefreshInfo());
    const [workflowRefreshInfo, setWorkflowRefreshInfo] = useState<RefreshInfo>(getInitRefreshInfo());
    const [lowCodeRefreshInfo, setLowCodeRefreshInfo] = useState<RefreshInfo>(getInitRefreshInfo());
    const [qianFanRefreshInfo, setQianFanRefreshInfo] = useState<RefreshInfo>(getInitRefreshInfo());
    const [isUserFromQianFan, setIsUserFromQianFan] = useState(false);
    const [hasCodelessAgent, setHasCodelessAgent] = useState(false);
    const [hasLowCodeAgent, setHasLowCodeAgent] = useState(false);
    const [hasWorkflowAgent, setHasWorkflowAgent] = useState(false);

    const [pageLoading, setPageLoading] = useState(true);
    const [loadError, setLoadError] = useState(false);
    const [requestError, setRequestError] = useState<any>();

    const [codelessExtInfo, setCodelessExtInfo] = useState<Record<string, AgentListExtInfo>>({});
    const [lowCodeExtInfo, setLowCodeExtInfo] = useState<Record<string, AgentListExtInfo>>({});
    const [workflowExtInfo, setWorkflowExtInfo] = useState<Record<string, AgentListExtInfo>>({});

    useEffect(() => {
        (async () => {
            try {
                setPageLoading(true);
                /** list 和 ext 接口为并发，不能互相阻塞请求 */
                (async () => {
                    const {codelessExt, lowCodeExt, workflowExt} = await api.getLingjingFirstPageAgentExt();
                    setCodelessExtInfo(agentExtArrToMap(codelessExt));
                    setLowCodeExtInfo(agentExtArrToMap(lowCodeExt));
                    setWorkflowExtInfo(agentExtArrToMap(workflowExt));
                })();

                const {
                    codelessListRes: codelessFirstPageRes,
                    workflowListRes: workflowFirstPageRes,
                    lowCodeListRes: lowCodeFirstPageRes,
                    isUserFromQianFan,
                } = await api.getLingjingFirstPageAgentList({
                    pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
                });

                setHasCodelessAgent(codelessFirstPageRes.agentList.length > 0);
                setHasLowCodeAgent(lowCodeFirstPageRes.agentList.length > 0);
                setHasWorkflowAgent(workflowFirstPageRes.agentList.length > 0);
                setCodelessRefreshInfo({
                    ...getInitRefreshInfo(),
                    agentListRes: codelessFirstPageRes,
                    isLoading: false,
                });
                setWorkflowRefreshInfo({
                    ...getInitRefreshInfo(),
                    agentListRes: workflowFirstPageRes,
                    isLoading: false,
                });

                setLowCodeRefreshInfo({
                    ...getInitRefreshInfo(),
                    agentListRes: lowCodeFirstPageRes,
                    isLoading: false,
                });

                setIsUserFromQianFan(isUserFromQianFan);
                setPageLoading(false);
            } catch (error) {
                setPageLoading(false);
                setLoadError(true);
                setRequestError(error);
                console.error(error);
            }
        })();
    }, []);

    const refreshCodelessList = useCallback(async () => {
        const info: RefreshInfo = getInitRefreshInfo();
        setCodelessRefreshInfo({...info, isLoading: true});
        setCodelessRefreshInfo(await refreshList(AgentTabType.Codeless, info));
        setLowCodeExtInfo(await refreshExt(AgentType.Codeless));
    }, []);

    const refreshWorkflowList = useCallback(async () => {
        const info: RefreshInfo = getInitRefreshInfo();
        setWorkflowRefreshInfo({...info, isLoading: true});
        setWorkflowRefreshInfo(await refreshList(AgentTabType.Workflow, info));
    }, []);

    const refreshLowCodeList = useCallback(async () => {
        const info: RefreshInfo = getInitRefreshInfo();
        setLowCodeRefreshInfo({...info, isLoading: true});
        setLowCodeRefreshInfo(await refreshList(AgentTabType.LowCode, info));
        setLowCodeExtInfo(await refreshExt(AgentType.LowCode));
    }, []);

    const refreshQianFanList = useCallback(async () => {
        const info: RefreshInfo = getInitRefreshInfo();
        setQianFanRefreshInfo({...info, isLoading: true});
        setQianFanRefreshInfo(await refreshList(AgentTabType.QianFan, info));
    }, []);

    return {
        pageLoading,
        loadError,
        requestError,
        isUserFromQianFan,
        hasCodelessAgent,
        hasLowCodeAgent,
        hasWorkflowAgent,
        codelessRefreshInfo,
        lowCodeRefreshInfo,
        workflowRefreshInfo,
        qianFanRefreshInfo,
        lowCodeExtInfo,
        codelessExtInfo,
        workflowExtInfo,
        refreshCodelessList,
        refreshLowCodeList,
        refreshQianFanList,
        refreshWorkflowList,
    };
}
