/**
 * @file Agent列表页入口页-Mobile
 * <AUTHOR>
 */

import {CacheProvider} from 'react-suspense-boundary';
import Loading from '@/components/Loading';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import AllAgentListM from './components/mobile/AllAgentList';

export default function EntryM() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.MY_AGENT}>
            <div>
                <CacheProvider>
                    <CommonErrorBoundary pendingFallback={<Loading />}>
                        <AllAgentListM />
                    </CommonErrorBoundary>
                </CacheProvider>
            </div>
        </LogContextProvider>
    );
}
