/**
 * <AUTHOR>
 * @date 2024/09/20
 * @description 公共常量和方法
 */

import {AgentStatusLabel, AgentType} from '@/api/agentList/interface';
import {AgentData} from '@/modules/agentList/interface';
import {EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {MyAgentType} from '@/utils/loggerV2/interface';
import {AgentStatusConfigs} from './interface';

export const STATUS_ICON: AgentStatusConfigs = {
    [AgentStatusLabel.DRAFTING]: {
        icon: 'icon-text',
        text: '草稿',
        color: '#1E1F24',
    },
    [AgentStatusLabel.PUBLISHED]: {
        icon: 'icon-a-Property1peizhi',
        text: '已发布',
        color: '#1E1F24',
    },
    [AgentStatusLabel.ONLINE]: {
        icon: 'icon-a-Property1yipeizhi',
        text: '已上线',
        color: '#1E1F24',
    },
    [AgentStatusLabel.OFFLINE]: {
        icon: 'icon-offline',
        text: '已下线',
        color: '#1E1F24',
    },
};

const MIN_BOTTOM = 200;
export const reachBottom = (container: HTMLDivElement): boolean =>
    Math.abs(container.scrollHeight - container.clientHeight - container.scrollTop) < MIN_BOTTOM;

export const mAgentLogoSize = {
    width: '68px',
    height: '68px',
    fontSize: '19px',
};

export const PCAgentLogoSize = {
    width: '56px',
    height: '56px',
    fontSize: '19px',
};

export const CommonAuditingNotice =
    '您选择的发布渠道需人工审核通过后才能生效，预计1小时内反馈审核结果，请您注意查收审核结果的短信通知。';

export const getCardExt = (agent: AgentData) => {
    return {
        [EVENT_EXT_KEY_CONST.MY_AGENT_TYPE]:
            agent.agentType === AgentType.Codeless ? MyAgentType.CODELESS : MyAgentType.LOW_CODE,
        [EVENT_EXT_KEY_CONST.AGENT_ID]: agent.appId,
        [EVENT_EXT_KEY_CONST.AGENT_NAME]: agent.name,
    };
};

/** 记录调优按钮上的小红点是否点击过的 localStorage 的 key */
export const TuningRedDot = 'isTuningRedDot';
