/**
 * @file Agent列表页-pc 区分零代码、低代码、千帆AppBuilder 3个Tab
 * <AUTHOR>
 */

import {ConfigProvider, Alert} from 'antd';
import {CategoryTabs} from '@/modules/center/components/Tags';
import ContentHeader from '@/modules/pluginCenter/components/ContentHeader';
import Loading from '@/components/Loading';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {LOW_CODE_AGENT_DEPRECATED_DATE} from '@/modules/agentPromptEditV2/utils';
import {AgentTabType} from '../../interface';
import useRefreshAgentList from '../../hooks/useRefreshAgentList';
import useAgentTypeTabs from '../../hooks/useAgentTypeTabs';
import AgentList from './AgentList';
import {AgentWarning} from './AgentWarning';
import AgentDiagnosisUpdateAlert from './AgentDiagnosisUpdateAlert';
/**
 * 智能体列表页主要内容
 */
// eslint-disable-next-line complexity
export default function AllAgentList() {
    const {
        pageLoading,
        loadError,
        requestError,
        hasCodelessAgent,
        hasWorkflowAgent,
        hasLowCodeAgent, // 确保这个变量正确反映低代码agent列表是否为空
        codelessRefreshInfo,
        workflowRefreshInfo,
        lowCodeRefreshInfo,
        qianFanRefreshInfo,
        refreshCodelessList,
        refreshWorkflowList,
        refreshLowCodeList,
        codelessExtInfo,
        lowCodeExtInfo,
        workflowExtInfo,
    } = useRefreshAgentList();

    const {tabItems, activeTabKey, tabChange} = useAgentTypeTabs(
        pageLoading,
        hasCodelessAgent,
        hasWorkflowAgent,
        hasLowCodeAgent, // 传递这个变量到useAgentTypeTabs hook
        qianFanRefreshInfo
    );

    return (
        <div className="relative flex h-screen flex-col overflow-hidden pt-[1.875rem] font-pingfang">
            {/* 页面标题 */}
            <ContentHeader title={'我的智能体'} />
            {pageLoading ? (
                <Loading />
            ) : loadError ? (
                <RenderError error={requestError} />
            ) : (
                <>
                    {/* 智能体列表tab */}
                    <div className="flex items-center justify-between pb-[18px] pt-[21px] text-gray-tertiary">
                        <CategoryTabs
                            tabs={tabItems.filter(tab => tab.id !== AgentTabType.LowCode || hasLowCodeAgent)}
                            activeTab={activeTabKey}
                            switchTab={tabChange}
                        />
                    </div>
                    {/* 警告信息 */}
                    {activeTabKey === AgentTabType.Codeless && <AgentDiagnosisUpdateAlert />}
                    {activeTabKey === AgentTabType.LowCode && hasLowCodeAgent && (
                        <div className="mb-4">
                            <div className="inline-block">
                                <Alert message={LOW_CODE_AGENT_DEPRECATED_DATE} type="error" showIcon />
                            </div>
                        </div>
                    )}
                    <AgentWarning />
                    {/* 智能体列表区域 */}
                    <ConfigProvider
                        theme={{
                            token: {
                                screenLG: 1280,
                                screenLGMin: 1280,
                                screenLGMax: 1280,
                                screenXL: 1446,
                                screenXLMin: 1446,
                                screenXXL: 1680,
                                screenXXLMin: 1680,
                            },
                        }}
                    >
                        {/* 零代码 */}
                        <AgentList
                            className={activeTabKey === AgentTabType.Codeless ? '' : 'hidden'}
                            agentTabType={AgentTabType.Codeless}
                            refreshInfo={codelessRefreshInfo}
                            onRefresh={refreshCodelessList}
                            initExtInfo={codelessExtInfo}
                        />
                        {/* 工作流模式 */}
                        <AgentList
                            className={activeTabKey === AgentTabType.Workflow ? '' : 'hidden'}
                            agentTabType={AgentTabType.Workflow}
                            refreshInfo={workflowRefreshInfo}
                            onRefresh={refreshWorkflowList}
                            initExtInfo={workflowExtInfo}
                        />
                        {/* 低代码 */}
                        <AgentList
                            className={activeTabKey === AgentTabType.LowCode && hasLowCodeAgent ? '' : 'hidden'}
                            agentTabType={AgentTabType.LowCode}
                            refreshInfo={lowCodeRefreshInfo}
                            onRefresh={refreshLowCodeList}
                            initExtInfo={lowCodeExtInfo}
                        />
                    </ConfigProvider>
                </>
            )}
        </div>
    );
}
