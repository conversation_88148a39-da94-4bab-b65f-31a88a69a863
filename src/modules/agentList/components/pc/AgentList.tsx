/**
 * @file 我的智能体列表页
 * <AUTHOR>
 * @update <EMAIL> 升级列表-区分零代码、低代码、千帆AppBuilder
 */
import {Button, Col, Row, Tooltip} from 'antd';
import {UIEvent, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import {useCreateWorkflowAgent} from '@/modules/agentPromptEditV2/hooks/useCreateWorkflowAgent';
import AgentCard from '@/modules/agentList/components/pc/AgentCard';
import {
    AGENT_EMPTY_CREATE,
    AgentData,
    AgentListProps,
    AgentTabType,
    DEFAULT_PAGINATION_SETTINGS,
} from '@/modules/agentList/interface';
import urls from '@/links';
import {ScrollContainer} from '@/components/ScrollContainer';
import {AgentListExtInfo, AgentSource, AgentType, ExtDataType} from '@/api/agentList/interface';
import api from '@/api/agentList';
import Empty from '@/components/Empty';
import {AppBuildType} from '@/api/appList/interface';
import Loading from '@/components/Loading';
import {RedMarkScene, useUserInfoStore} from '@/store/login/userInfoStore';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {useAgentNumModal} from '@/components/Sidebar/hooks/useAgentNum';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {postRedDotMarkRead} from '@/api/redDot';
import {MyAgentsSubType, RedDotType} from '@/api/redDot/interface';
import FlowAgentCreateModal from '../../../agentCreate/components/FlowAgentCreateModal';
import {reachBottom} from '../../utils';
import {agentExtArrToMap} from '../../hooks/useRefreshAgentList';

export default function AgentsList({
    className = '',
    agentTabType,
    refreshInfo,
    onRefresh,
    initExtInfo,
}: AgentListProps) {
    const [myAgentListRedMarkShow, resetRedMark] = useUserInfoStore(store => [
        store.redMarks[RedMarkScene.MyAgentList]?.redShow,
        store.resetRedMark,
    ]);

    // 列表滚动时的当前页码
    const pageNoRef = useRef(DEFAULT_PAGINATION_SETTINGS.pageNo);
    // 是否请求到了最后一页
    const couldRequestMore = useRef(true);
    // 列表数据
    const [agents, setAgents] = useState<AgentData[]>(refreshInfo.agentListRes.agentList);

    // 扩充数据
    const [extInfo, setExtInfo] = useState<Record<string, AgentListExtInfo>>({});
    const onRefreshHandler = useCallback(async () => {
        setExtInfo({});
        onRefresh?.();
    }, [onRefresh]);
    const exts = useMemo(() => ({...initExtInfo, ...extInfo}), [initExtInfo, extInfo]);

    const navigate = useNavigate();
    const {setOpenAgentWarningModal, appNum, appLimit, AgentNumModal, Modalprops} = useAgentNumModal();

    // 根据初始请求返回数据，设置列表首页
    useEffect(() => {
        pageNoRef.current = DEFAULT_PAGINATION_SETTINGS.pageNo;
        couldRequestMore.current =
            (refreshInfo.agentListRes.agentList.length || 0) < (refreshInfo.agentListRes.total || 0);
        setAgents(refreshInfo.agentListRes.agentList);

        // 如果低代码列表为空，则跳转到零代码列表
        if (agentTabType === AgentTabType.LowCode && refreshInfo?.agentListRes?.agentList?.length === 0) {
            const currentPath = window.location.pathname;
            // 如果当前路径是低代码列表，切数据为空，则跳转到零代码列表
            if (currentPath.endsWith(`/${AgentTabType.LowCode}`)) {
                navigate(urls.agentList.fill({tab: AgentTabType.Codeless}));
            }
        }
    }, [refreshInfo.agentListRes.agentList, refreshInfo.agentListRes.total, navigate, agentTabType]);

    // 滚动到列表底部时展现的 loading
    const [isShowBottomLoading, setIsShowBottomLoading] = useState(false);

    // 是否处于滚动分页请求中状态
    const inRequestingRef = useRef(false);

    const getAgentList = useCallback(async () => {
        inRequestingRef.current = true;
        return api
            .getAgentList({
                agentSource: agentTabType === AgentTabType.QianFan ? AgentSource.QianFan : AgentSource.LingJing,
                agentType: agentTabType === AgentTabType.LowCode ? AgentType.LowCode : AgentType.Codeless,
                pageNo: pageNoRef.current,
                pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
            })
            .finally(() => {
                inRequestingRef.current = false;
            });
    }, [agentTabType]);

    const updateAgentList = useCallback(
        async (currentAgentList?: AgentData[]) => {
            const {agentList, total} = await getAgentList();

            setIsShowBottomLoading(false);

            const list = (currentAgentList || []).concat(agentList || []);
            setAgents(list);

            couldRequestMore.current = list.length < total;
        },
        [getAgentList, setAgents]
    );

    const updateAgentExt = useCallback(async () => {
        const extRes = await api.getAgentListExt({
            agentSource: AgentSource.LingJing,
            agentType: agentTabType === AgentTabType.LowCode ? AgentType.LowCode : AgentType.Codeless,
            ...DEFAULT_PAGINATION_SETTINGS,
            type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution},${ExtDataType.tags},${ExtDataType.Dynamic},${ExtDataType.XmiAuditInfo}`,
            pageNo: pageNoRef.current,
            modeType: agentTabType === AgentTabType.Workflow ? AgentModeType.Workflow : undefined,
        });

        setExtInfo(prev => ({...prev, ...agentExtArrToMap(extRes)}));
    }, [agentTabType]);

    const handleScroll = useCallback(
        async (event: UIEvent<HTMLDivElement>) => {
            if (inRequestingRef.current || !couldRequestMore.current) {
                return;
            }

            if (!reachBottom(event.currentTarget)) {
                return;
            }

            pageNoRef.current += 1;
            try {
                setIsShowBottomLoading(true);
                updateAgentList(agents);
                updateAgentExt();
            } catch (e) {
                // 请求失败时还原
                pageNoRef.current -= 1;
                setIsShowBottomLoading(false);
            }
        },
        [updateAgentList, agents, updateAgentExt]
    );

    const {clickLog} = useUbcLogV2();
    // 打开低代码创建弹窗
    const [appCreateModalOpen, setAppCreateModalOpen] = useState(false);

    const userInfoData = useUserInfoStore(store => store.userInfoData);
    const allowCreate = !userInfoData?.hasTpProxy;

    const {navigateToWorkflowAgent, isRequesting} = useCreateWorkflowAgent();

    // agent list 为空，跳转编辑页面
    const clickEmptyCreateBtn = useCallback(() => {
        // 检查应用数限制
        if (appNum && appLimit && appLimit <= appNum) {
            setOpenAgentWarningModal(true);
            return;
        }

        // 不同 agentTabType 对应的创建入口
        const agentTypeAction = {
            [AgentTabType.Codeless]: {
                // 跳转零代码创建页面
                action: () => navigate(urls.agentPromptQuickStart.raw()),
            },
            [AgentTabType.Workflow]: {
                // 跳转工作流创建页面
                action: navigateToWorkflowAgent,
            },
            [AgentTabType.LowCode]: {
                // 跳转低代码创建页面（应用）
                action: () => setAppCreateModalOpen(true),
            },
            [AgentTabType.QianFan]: null,
        };

        // 获取当前 agentTabType 对应的配置
        const typeConfig = agentTypeAction[agentTabType];
        if (!typeConfig) {
            return;
        }

        // 记录点击日志
        clickLog(AGENT_EMPTY_CREATE[agentTabType], EVENT_PAGE_CONST.MY_AGENT);

        // 执行跳转
        typeConfig.action();
    }, [agentTabType, appLimit, appNum, clickLog, navigate, navigateToWorkflowAgent, setOpenAgentWarningModal]);

    const scrollContainerRef = useRef<HTMLDivElement>(null);

    // 我的智能体菜单飘红已读
    useEffect(() => {
        (async () => {
            if (!myAgentListRedMarkShow) {
                return;
            }

            try {
                await postRedDotMarkRead({
                    type: RedDotType.MyAgents,
                    subType: MyAgentsSubType.Sidebar,
                });

                resetRedMark(RedMarkScene.MyAgentList);
            } catch (error) {
                console.error(error);
            }
        })();
    }, [myAgentListRedMarkShow, resetRedMark]);

    return (
        <ScrollContainer
            onScroll={handleScroll}
            className={classNames('flex-grow overflow-y-auto overflow-x-hidden pb-6 pr-2', className)}
            ref={scrollContainerRef}
        >
            {refreshInfo.isLoading ? (
                <Loading />
            ) : refreshInfo.isError ? (
                <RenderError error={refreshInfo.error} />
            ) : refreshInfo.agentListRes.agentList.length > 0 ? (
                // 如果有智能体数据，渲染卡片列表
                <div>
                    <Row gutter={[16, 16]} wrap>
                        {agents.map(agent => (
                            <Col xs={12} lg={12} xl={12} key={agent.appId}>
                                <AgentCard
                                    agent={agent}
                                    onRefresh={onRefreshHandler}
                                    extData={exts?.[agent.appId]}
                                    scrollContainerRef={scrollContainerRef}
                                />
                            </Col>
                        ))}
                    </Row>

                    {/* 滚动到底部时展现加载态 */}
                    {isShowBottomLoading && (
                        <div className="flex items-center justify-center pb-6 pt-2">
                            <span className="text-xs leading-3 text-flow-hover">加载中...</span>
                        </div>
                    )}
                </div>
            ) : (
                <>
                    {/* 智能体列表为空显示空态 */}
                    <Empty desc="暂无智能体">
                        {agentTabType === AgentTabType.QianFan ? null : (
                            <Tooltip
                                title={allowCreate ? undefined : DISALLOW_OPERATE_HINT}
                                placement="bottom"
                                overlayInnerStyle={{width: 'max-content', whiteSpace: 'nowrap'}}
                            >
                                <Button
                                    type="primary"
                                    className="mt-[1.125rem] rounded-[100px]"
                                    onClick={clickEmptyCreateBtn}
                                    disabled={!allowCreate}
                                    loading={isRequesting}
                                >
                                    {agentTabType === AgentTabType.Workflow ? '创建工作流智能体' : '去创建'}
                                </Button>
                            </Tooltip>
                        )}
                    </Empty>
                    {agentTabType === AgentTabType.LowCode && (
                        <FlowAgentCreateModal
                            open={appCreateModalOpen}
                            setModalOpen={setAppCreateModalOpen}
                            buildType={AppBuildType.LowCode}
                        />
                    )}
                    <AgentNumModal {...Modalprops} />
                </>
            )}
        </ScrollContainer>
    );
}
