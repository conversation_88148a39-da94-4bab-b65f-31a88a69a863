import {Alert} from 'antd';
import {useEffect, useRef, useState} from 'react';
import {useAgentCountStore} from '@/store/agent/agentNum';

export const AgentWarning = () => {
    const {appNum, appLimit} = useAgentCountStore(store => ({
        setAgentNumber: store.setAgentNumber,
        appNum: store.appNum,
        appLimit: store.appLimit,
    }));

    const [alertShow, setAlertShow] = useState(false);

    useEffect(() => {
        setAlertShow(appNum !== undefined && appLimit !== undefined && appNum >= appLimit);
    }, [appLimit, appNum]);

    const domContainer = useRef<HTMLDivElement>(null);

    return (
        alertShow && (
            <div className="mb-4 inline-block" ref={domContainer}>
                <Alert
                    message={`1个账号最多创建${appLimit}个智能体，如需继续创建，请先清理闲置智能体。`}
                    type="warning"
                    showIcon
                />
            </div>
        )
    );
};
