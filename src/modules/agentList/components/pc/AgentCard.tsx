/* eslint-disable complexity */
/**
 * @file Agent 单卡片组件
 * <AUTHOR>
 * @date 2024/09/20
 * @description 功能全部迁移至 card 组件
 */
import {MouseEvent, useCallback, useEffect, useMemo, useRef} from 'react';
import {Card, Tooltip} from 'antd';
import {useNavigate} from 'react-router-dom';
import dayjs from 'dayjs';
import {css} from '@emotion/css';
import {AgentData, AuditSign, CardHoverText, Logo, AgentTagType} from '@/modules/agentList/interface';
import {AgentType, AgentListExtInfo, AgentStatusLabel, AllAuditStatus} from '@/api/agentList/interface';
import urls from '@/links';
import {SpeechStatus} from '@/api/agentEditV2';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import AgentLogoComponent from '@/components/AgentLogo';
import useTextOverflow from '@/modules/center/hooks/useTextOverflow';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {defaultAvatar, defaultName} from '@/store/agent/promptEditStoreV2';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentPermission} from '@/api/agentEdit';
import {AgentTitleTags} from '@/components/CardTemplate/AgentTitleTags';
import {TagsUsedBy} from '@/modules/center/interface';
import {DynamicFigureStatus} from '@/api/agentEdit/interface';
import {PCAgentLogoSize, getCardExt, STATUS_ICON} from '../../utils';
import AgentTags from '../AgentTags';
import {useAgentCardPress} from '../../hooks/useAgentCardPress';
import AgentActions from './AgentActions';
import LowCodeAgentAction from './LowCodeAgentAction';

const tooltipStyle = css`
    max-width: 100% !important;
    .ant-tooltip-inner {
        border-radius: 12px !important;
    }
`;

export interface AgentStatusConfig {
    icon: string;
    text: string;
    color: string;
    hoverText?: string;
}

export interface CardData {
    logo: Logo;
    agentId: string;
    agentType: AgentType;
    showPluginError: boolean;
    cardDisable: boolean;
    cardHoverText: CardHoverText;
    title: string;
    extraTags: string[];
    description: string;
    status: AgentStatusConfig;
    speechStatus: SpeechStatus;
    figureTaskStatus: FigureTaskStatus;
    cardClickHandle: () => void;
    extData?: AgentListExtInfo;
    labelStatus: AgentStatusLabel;
    auditSign: AuditSign;
    latestTime: number | null;
}

export default function AgentCardPC({
    agent,
    onRefresh,
    extData,
    scrollContainerRef,
}: {
    agent: AgentData;
    onRefresh?: () => Promise<void>;
    extData?: AgentListExtInfo;
    scrollContainerRef?: React.RefObject<HTMLDivElement>;
}) {
    const {clickLog, showLog} = useUbcLogV2();
    const navigate = useNavigate();

    const [titleRef, isTitleFlow] = useTextOverflow();
    const [descriptionRef, isDescriptionFlow] = useTextOverflow();

    const prohibitEdit = useMemo(() => {
        return (
            agent?.tags?.includes(AgentTagType.TEACHER_RECRUIT_AGENT) &&
            agent.originStatus === AllAuditStatus.Developing
        );
    }, [agent.originStatus, agent?.tags]);

    const handleTitleClick = useCallback(() => {
        if (prohibitEdit) {
            return;
        }

        clickLog(EVENT_TRACKING_CONST.AgentListAgentCard, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        if (agent.agentType === AgentType.Codeless) {
            navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Create}`);
        } else if (agent.agentType === AgentType.LowCode) {
            navigate(urls.agentFlowEdit.fill({appId: agent.appId}));
        }
    }, [agent, clickLog, navigate, prohibitEdit]);

    const {isPress, handleMouseDown, handleMouseUp, handleMouseEnter, handleMouseLeave} = useAgentCardPress();

    /* 体验按钮展现打点 */
    useEffect(() => {
        if ([AgentStatusLabel.PUBLISHED, AgentStatusLabel.ONLINE].includes(agent.labelStatus)) {
            showLog(EVENT_VALUE_CONST.AGENT_EXP, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        }

        if (agent.labelStatus !== AgentStatusLabel.DRAFTING) {
            showLog(EVENT_VALUE_CONST.AGENT_ANALYSIS, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const labelHoverText = useMemo(() => {
        if (agent.labelStatus === AgentStatusLabel.PUBLISHED) {
            /** 私有状态十秒钟之后就会流转到已上线，所以不显示审核态 tooltip */
            return agent.permission === AgentPermission.PRIVATE
                ? ''
                : '智能体已发布，正在审核中，当前阶段可以点击【体验】按钮，可以提前【体验】智能体效果～';
        } else if (agent.labelStatus === AgentStatusLabel.ONLINE) {
            return agent.onlineTime === 0
                ? ''
                : `线上版本于${dayjs(agent.onlineTime).format('YYYY-MM-DD HH:mm:ss')}发布`;
        } else if (agent.labelStatus === AgentStatusLabel.OFFLINE) {
            return agent.forceOfflineMessage || '';
        } else {
            return '';
        }
    }, [agent.forceOfflineMessage, agent.labelStatus, agent.onlineTime, agent.permission]);

    const cardRef = useRef<HTMLDivElement>(null);

    const handlePreviewDynamic = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            clickLog(EVENT_TRACKING_CONST.AgentListAgentCard, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
            navigate(
                `${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Create}&previewDynamic=true`
            );
        },
        [agent, clickLog, navigate]
    );

    const showEditTime = extData?.isShowTime && extData.latestTime !== 0;

    /* 卡片主体 */
    return (
        <Card
            ref={cardRef}
            className="hover:bg-[rgba(255, 255, 255, 0.8)] group relative rounded-xl font-pingfang shadow-none transition duration-200"
            // 卡片标题和描述
            title={
                <div className="flex px-3 pt-3" onClick={handleTitleClick}>
                    <div className="relative flex-shrink-0">
                        <div className="rounded-full border-black transition duration-200 group-hover:shadow-card-selected">
                            <AgentLogoComponent
                                width={PCAgentLogoSize.width}
                                height={PCAgentLogoSize.height}
                                fontSize={PCAgentLogoSize.fontSize}
                                logoUrl={agent.logoUrl || defaultAvatar}
                                figureTaskStatus={agent.figureTaskStatus}
                                speechStatus={agent.speechStatus}
                                isPress={isPress}
                            />
                        </div>
                    </div>

                    <div className="ml-3 min-w-0">
                        <div className="flex h-[16px] items-end leading-[16px]">
                            <Tooltip
                                placement="top"
                                title={isTitleFlow ? agent.name : ''}
                                arrow={false}
                                autoAdjustOverflow
                                overlayStyle={{
                                    maxWidth: '268px',
                                }}
                            >
                                <div
                                    className="-mt-[2px] h-[16px] truncate text-base font-semibold leading-[16px] text-black"
                                    ref={titleRef}
                                >
                                    {agent.name || defaultName}
                                </div>
                            </Tooltip>

                            {((agent.agentMedalList && agent.agentMedalList.length > 0) ||
                                (extData?.tags && extData?.tags.length > 0)) && (
                                <div className="ml-[3px] flex h-[16px] items-center leading-[16px]">
                                    {agent.agentMedalList &&
                                        agent.agentMedalList.length > 0 &&
                                        agent.agentMedalList.map(item => (
                                            <Tooltip
                                                overlayClassName={tooltipStyle}
                                                key={item.medalId}
                                                title={item.name}
                                                placement="top"
                                            >
                                                <img
                                                    src={item.level.img}
                                                    className="mr-[3px] h-[14px] w-[14px] bg-contain last:mr-0"
                                                />
                                            </Tooltip>
                                        ))}
                                    {/* 智能体获得的头衔标签 */}
                                    {extData?.tags && extData?.tags.length > 0 && (
                                        <AgentTitleTags
                                            tags={extData?.tags}
                                            cardContainerRef={cardRef}
                                            scrollContainerRef={scrollContainerRef}
                                            usedBy={TagsUsedBy.AgentCard}
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                        <Tooltip
                            placement="top"
                            title={isDescriptionFlow ? agent.description : ''}
                            arrow={false}
                            autoAdjustOverflow
                            overlayStyle={{
                                maxWidth: '268px',
                            }}
                        >
                            <div
                                className="mt-1 min-h-[18px] truncate text-sm font-normal leading-[18px] text-gray-secondary"
                                ref={descriptionRef}
                            >
                                {agent.description}
                            </div>
                        </Tooltip>

                        <div className="mt-[6px] flex leading-none">
                            <div className="h-5 w-0 flex-grow-0"></div>
                            <AgentTags agent={agent} extData={extData} />
                        </div>
                        <div className="mt-2 flex h-3 items-center text-[12px] font-normal leading-none text-gray-quaternary">
                            {showEditTime ? `最新草稿 ${dayjs(extData.latestTime).format('YYYY-MM-DD HH:mm:ss')}` : ''}
                            {extData?.dynamicDigital?.status === DynamicFigureStatus.VIDEO_MAKING_DONE && (
                                <>
                                    {showEditTime && (
                                        <span className="mx-[9px] inline-block h-[10px] w-[1px] bg-gray-quaternary"></span>
                                    )}
                                    <span className="text-[12px] text-gray-quaternary">数字人已生成</span>
                                    <span className="pl-3 text-[12px] text-primary" onClick={handlePreviewDynamic}>
                                        预览
                                    </span>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            }
            headStyle={{
                justifyContent: 'start',
                borderBottom: 0,
                padding: 0,
                paddingBottom: 12,
                cursor: 'pointer',
            }}
            bodyStyle={{
                padding: 0,
            }}
            bordered={false}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onTouchStart={handleMouseDown}
            onTouchEnd={handleMouseUp}
        >
            <div className="flex h-[54px] cursor-default items-center justify-between border-t border-[#edeef0] px-3">
                <Tooltip title={labelHoverText} arrow={false} placement="topLeft">
                    <div
                        className="flex cursor-pointer items-center space-x-2"
                        style={{
                            color: STATUS_ICON[agent.labelStatus].color,
                        }}
                    >
                        {/* 状态区图标 */}
                        <span
                            className={`iconfont ${STATUS_ICON[agent.labelStatus].icon} text-[18px] leading-[18px]`}
                        ></span>
                        {/* 状态区文本 */}
                        <span className="wrap line-clamp-2 text-sm leading-none">
                            {STATUS_ICON[agent.labelStatus].text}
                        </span>
                    </div>
                </Tooltip>

                {extData?.dynamicDigital?.estimateGenDuration &&
                    [
                        DynamicFigureStatus.NEW,
                        DynamicFigureStatus.VIDEO_QUALITING,
                        DynamicFigureStatus.VIDEO_QUALITING_DONE,
                        DynamicFigureStatus.VIDEO_MAKING,
                    ].includes(extData.dynamicDigital?.status) && (
                        <span className="pl-3 text-[12px] text-[#8C1AFF]">
                            数字人{Math.ceil(extData.dynamicDigital.estimateGenDuration / 60)}小时后生成
                        </span>
                    )}

                <div className="flex-grow"></div>
                {/* 操作区 */}
                {agent.agentType === AgentType.Codeless ? (
                    <AgentActions agent={agent} onRefresh={onRefresh} extData={extData} />
                ) : (
                    <LowCodeAgentAction agentId={agent.appId} />
                )}
            </div>
        </Card>
    );
}
