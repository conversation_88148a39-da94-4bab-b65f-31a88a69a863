/**
 * @file Agent 卡片操作集组件
 * <AUTHOR>
 * @update <EMAIL> 升级卡片操作Actions
 * @update <EMAIL> 抽离组件
 */
/* eslint-disable complexity */
import {useCallback, useMemo, useState} from 'react';
import {Popover, Tooltip, Badge, message} from 'antd';
import {css} from '@emotion/css';
import {useNavigate} from 'react-router-dom';
import {AgentListExtInfo, AgentStatusLabel, AllAuditStatus} from '@/api/agentList/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {LogExtIsUnread} from '@/utils/loggerV2/interface';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentPermission} from '@/api/agentEdit';
import urls from '@/links';
import {postRedDotMarkRead} from '@/api/redDot';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {AgentDiagnosisSubType, DatasetRecallTestSubType, RedDotType} from '@/api/redDot/interface';
import {getAgentPreview} from '@/api/myAgentPreview';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {DynamicFigureStatus} from '@/api/agentEdit/interface';
import {XmiDeployAuditStatus} from '@/api/agentDeploy/interface';
import {AgentData, AuditStatus, AgentTagType} from '../../interface';
import {TuningRedDot, getCardExt} from '../../utils';
import PopoverMenu from './PopoverMenu';

const getPopupContainer = () => document.getElementById('layout-content-container') || document.body;

const PopoverOverlayCSS = css`
    .ant-popover-content .ant-popover-inner {
        margin-top: 11px;
        border-radius: 9px !important;
        padding: 5px 4px;
        box-shadow: 0px 0px 40px 0px #1d225233 !important;
    }
`;

const BtnClassName =
    'mr-2 cursor-pointer rounded-[8px] bg-[#5562F214] px-3 py-2 leading-none hover:text-primary hover:bg-primary/10';

// 零代码Agent卡片操作按钮
export default function AgentActionsPC({
    agent,
    onRefresh,
    extData,
}: {
    agent: AgentData;
    onRefresh?: () => Promise<void>;
    extData?: AgentListExtInfo;
}) {
    const [open, setOpen] = useState(false);
    const popoverClose = useCallback(() => {
        setOpen(false);
    }, []);

    const tuningRedDotKey = `${TuningRedDot}-${agent.appId}`;

    const [allowShowTuningRedDot, setAllowShowTuningRedDot] = useState(
        !parseInt(window.localStorage.getItem(tuningRedDotKey) || '0', 10)
    );

    const {showLog, clickLog} = useUbcLogV2();
    const handleOpenChange = useCallback(
        (visible: boolean) => {
            if (visible) {
                showLog(EVENT_VALUE_CONST.AGENT_MORE_PANEL, EVENT_PAGE_CONST.MY_AGENT, {
                    ...getCardExt(agent),
                    [EVENT_EXT_KEY_CONST.E_AGENT_STATUS]: agent.labelStatus,
                });
            }

            setOpen(visible);
        },
        [agent, showLog]
    );

    // 显示的错误信息
    const errTip = useMemo(() => {
        const {editXmiAuditRedDot, editPlagiarizeRedDot, editSimilarRedDot} = extData?.redDotInfo || {};

        // 发布小米商店审核不通过
        if (editXmiAuditRedDot) {
            const {auditStatus, message} = extData?.xmiDeployInfo || {};

            if (auditStatus === XmiDeployAuditStatus.AUDIT_FAIL && message) return message;
        }

        if (editPlagiarizeRedDot) {
            return '与其他智能体相同影响流量效果及使用，建议修改';
        }

        if (editSimilarRedDot) {
            return '与被复制的智能体相似度过高，影响流量效果及使用，建议修改';
        }

        const showPluginError =
            agent.offlinePluginNum > 0 &&
            (agent.permission === AgentPermission.PUBLIC || agent.permission === AgentPermission.LINK) &&
            agent.status !== AuditStatus.Developing;

        return showPluginError ? '引用插件状态异常，请关注处理！' : '';
    }, [agent.offlinePluginNum, agent.permission, agent.status, extData?.redDotInfo, extData?.xmiDeployInfo]);

    const navigate = useNavigate();
    const prohibitEdit = useMemo(() => {
        return (
            agent?.tags?.includes(AgentTagType.TEACHER_RECRUIT_AGENT) &&
            agent.originStatus === AllAuditStatus.Developing
        );
    }, [agent.originStatus, agent?.tags]);
    const handleToEdit = useCallback(() => {
        if (prohibitEdit) {
            return;
        }

        clickLog(EVENT_VALUE_CONST.AGENT_EDIT, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Create}`);
    }, [agent, clickLog, navigate, prohibitEdit]);

    const handleToPreview = useCallback(async () => {
        clickLog(EVENT_VALUE_CONST.AGENT_EXP, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        // 体验前需判断是否部署成功，否则提示用户
        try {
            await getAgentPreview({appId: agent.appId});
            navigate(urls.agentPreview.fill({id: agent.appId}));
        } catch (error: any) {
            message.error('正在部署中，请稍后重试~');
        }
    }, [agent, clickLog, navigate]);

    const handleToAnalyze = useCallback(() => {
        const analysisRedDot = extData?.redDotInfo?.analysisRedDot && agent.modeType !== AgentModeType.Workflow;
        const datasetAnalysisRedDot =
            extData?.redDotInfo?.datasetAnalysisRedDot && agent.modeType !== AgentModeType.Workflow;
        if (extData?.redDotInfo?.analysisRedDot) {
            // 点击后，标记该智能体分析报告已读
            postRedDotMarkRead({
                appId: agent.appId,
                type: RedDotType.AgentDiagnosis,
                subType: AgentDiagnosisSubType.Agent,
            });
        }

        if (extData?.redDotInfo?.datasetAnalysisRedDot) {
            // 点击后，标记该智能体知识库检索分析报告已读
            postRedDotMarkRead({
                appId: agent.appId,
                type: RedDotType.AgentDiagnosis,
                subType: DatasetRecallTestSubType.Agent,
            });
        }

        clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS, EVENT_PAGE_CONST.MY_AGENT, {
            ...getCardExt(agent),
            // eslint-disable-next-line camelcase
            is_unread: analysisRedDot || datasetAnalysisRedDot ? LogExtIsUnread.HasUnread : LogExtIsUnread.NoUnread,
        });
        navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Analysis}`);
    }, [agent, clickLog, extData, navigate]);

    const handleAnalysisHover = useCallback(() => {
        showLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_HOVER, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
    }, [agent, showLog]);

    const handleTooltipOpenChange = useCallback(
        (visible: boolean) => {
            if (visible) {
                handleAnalysisHover();
            }
        },
        [handleAnalysisHover]
    );

    const showPreviewButton = useMemo(() => {
        if (
            ![
                AllAuditStatus.Auditing,
                AllAuditStatus.Online,
                AllAuditStatus.Editing,
                AllAuditStatus.SecondAuditing,
                AllAuditStatus.SecondAuditFailed,
                AllAuditStatus.SecondAuditSuccess,
            ].includes(agent.originStatus)
        ) {
            return false;
        } else if (agent.labelStatus === AgentStatusLabel.PUBLISHED && agent.permission !== AgentPermission.PRIVATE) {
            return true;
        } else {
            return agent.labelStatus !== AgentStatusLabel.DRAFTING;
        }
    }, [agent.labelStatus, agent.originStatus, agent.permission]);

    /** 调优 btn 的悬浮提示文案 */
    const tuningHint = useMemo(() => {
        const content = [];

        extData?.qaCount && content.push(<span className="whitespace-nowrap">{extData?.qaCount}个新的问答待调优</span>);
        extData?.dynamicDigital?.status === DynamicFigureStatus.VIDEO_MAKING_DONE &&
            extData?.qaCount &&
            content.push(content.length ? '，' : '', <span className="whitespace-nowrap">可添加数字人视频</span>);
        extData?.conversationCount &&
            content.push(
                content.length ? '，' : '',
                <span className="whitespace-nowrap">已调优问答为您带来{extData?.conversationCount}次对话</span>
            );

        return content.length ? content : undefined;
    }, [extData?.conversationCount, extData?.dynamicDigital?.status, extData?.qaCount]);

    const handleToTuning = useCallback(async () => {
        window.localStorage.setItem(tuningRedDotKey, '1');
        setAllowShowTuningRedDot(false);

        clickLog(EVENT_EXT_KEY_CONST.AGENT_TUNING, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Tuning}`);
    }, [agent, clickLog, navigate, tuningRedDotKey]);

    const analysisRedDot = extData?.redDotInfo?.analysisRedDot && agent.modeType !== AgentModeType.Workflow;
    const datasetAnalysisRedDot =
        extData?.redDotInfo?.datasetAnalysisRedDot && agent.modeType !== AgentModeType.Workflow;
    const analysisTooltipTitle = useMemo(() => {
        if (analysisRedDot && datasetAnalysisRedDot) {
            return '有新的智能体质量分析报告和知识库检索分析报告，请查看';
        }

        if (analysisRedDot) {
            return '有新的智能体质量分析报告，请查看';
        }

        if (datasetAnalysisRedDot) {
            return '有新的知识库检索分析报告，请查看';
        }
        return '';
    }, [analysisRedDot, datasetAnalysisRedDot]);

    return (
        <>
            {prohibitEdit ? (
                <Tooltip title={'请先完成招募活动智能体创建'}>
                    <div
                        style={{
                            // pointerEvents: prohibitEdit ? 'none' : 'auto',
                            opacity: prohibitEdit ? 0.5 : 1,
                            cursor: prohibitEdit ? 'not-allowed' : '',
                        }}
                    >
                        <Badge status="error" dot={!!errTip} offset={[-11, 3]}>
                            <div className={BtnClassName} onClick={handleToEdit}>
                                编辑
                            </div>
                        </Badge>
                        <Badge status="error" dot={extData?.wxInfo?.auth} offset={[-3, 3]}>
                            <span className="iconfont icon-feedback inline-block cursor-pointer rounded-[5px] bg-[#5562F214] px-2 py-1 text-[14px] text-gray-secondary hover:bg-primary/10 hover:text-primary" />
                        </Badge>
                    </div>
                </Tooltip>
            ) : (
                <>
                    {/* 私有状态十秒钟之后就会流转到已上线，所以不显示审核态 tooltip */}
                    {showPreviewButton && (
                        <div className={BtnClassName} onClick={handleToPreview}>
                            体验
                        </div>
                    )}
                    <Tooltip title={errTip} rootClassName="max-w-[295px]">
                        <Badge status="error" dot={!!errTip} offset={[-11, 3]}>
                            <div className={BtnClassName} onClick={handleToEdit}>
                                编辑
                            </div>
                        </Badge>
                    </Tooltip>
                    {agent.labelStatus !== AgentStatusLabel.DRAFTING && (
                        /* 分析报告和调优功能相关，工作流模式不显示分析报告提示 */
                        <Tooltip title={analysisTooltipTitle} placement="top" onOpenChange={handleTooltipOpenChange}>
                            <Badge status="error" dot={!!(analysisRedDot || datasetAnalysisRedDot)} offset={[-11, 3]}>
                                <div className={BtnClassName} onClick={handleToAnalyze}>
                                    分析
                                </div>
                            </Badge>
                        </Tooltip>
                    )}
                    {agent.modeType !== AgentModeType.Workflow && (
                        <Tooltip
                            title={tuningHint}
                            placement="top"
                            color="#1E1F24F2"
                            overlayInnerStyle={{
                                borderRadius: '12px',
                                padding: '10px 12px',
                            }}
                            rootClassName="max-w-[285px] z-[999]"
                            getPopupContainer={getPopupContainer}
                        >
                            <Badge
                                status="error"
                                dot={extData && extData?.qaCount > 0 && allowShowTuningRedDot}
                                offset={[-11, 3]}
                            >
                                <div className={BtnClassName} onClick={handleToTuning}>
                                    调优
                                </div>
                            </Badge>
                        </Tooltip>
                    )}
                    <Popover
                        trigger="hover"
                        placement="bottomRight"
                        align={{
                            offset: [12, -5],
                        }}
                        fresh
                        open={open}
                        onOpenChange={handleOpenChange}
                        content={
                            <PopoverMenu
                                agent={agent}
                                open={open}
                                onRefresh={onRefresh}
                                parentClose={popoverClose}
                                setOpen={setOpen}
                                extData={extData}
                            />
                        }
                        arrow={false}
                        overlayClassName={PopoverOverlayCSS}
                    >
                        <Badge status="error" dot={extData?.wxInfo?.auth} offset={[-3, 3]}>
                            <span className="iconfont icon-feedback inline-block cursor-pointer rounded-[5px] bg-[#5562F214] px-2 py-1 text-[14px] text-gray-secondary hover:bg-primary/10 hover:text-primary" />
                        </Badge>
                    </Popover>
                </>
            )}
        </>
    );
}
