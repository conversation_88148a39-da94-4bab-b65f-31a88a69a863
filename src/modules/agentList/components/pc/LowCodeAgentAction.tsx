import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {Space} from 'antd';
import {AgentType} from '@/api/agentList/interface';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import urls from '@/links';
import {PluginTabType} from '@/api/pluginVersion/interface';

// 低代码Agent卡片操作按钮-进入Agent详情概览页
export default function LowCodeAgentAction({agentId}: {agentId: string}) {
    const navigate = useNavigate();
    const {ubcClickLog} = useUbcLog();

    const actionClick = useCallback(() => {
        // 工作台 icon 点击打点
        ubcClickLog(EVENT_TRACKING_CONST.AgentListAgentCardDetailIcon, {
            activatedAgentId: agentId,
            activatedAgentType: AgentType.LowCode,
        });

        // 跳转到低代码Agent详情页面
        navigate(urls.agentFlowDetail.fill({id: agentId, tab: PluginTabType.Overview}));
    }, [agentId, navigate, ubcClickLog]);

    return (
        <Space
            align="center"
            className="h-6 cursor-pointer rounded-[5px] bg-primary/[0.07] px-2 text-sm text-gray-secondary hover:bg-primary/10 hover:text-primary"
            onClick={actionClick}
        >
            <span className="iconfont icon-app block"></span>
            <span>工作台</span>
        </Space>
    );
}
