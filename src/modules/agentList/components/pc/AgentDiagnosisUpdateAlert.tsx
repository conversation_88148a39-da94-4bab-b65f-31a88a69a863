import {Alert} from 'antd';
import {useCallback, useEffect, useRef, useState} from 'react';
import {AgentDiagnosisSubType, RedDotType} from '@/api/redDot/interface';
import {getRedDot, postRedDotMarkRead} from '@/api/redDot';

export default function AgentDiagnosisUpdateAlert() {
    const [diagnosisUpdateNotice, setDiagnosisUpdateNotice] = useState('');
    const [alertShow, setAlertShow] = useState(false);
    const domContainer = useRef<HTMLDivElement>(null);

    useEffect(() => {
        (async () => {
            try {
                // 获取哪些Agent诊断报告有更新的提示
                const {content} = await getRedDot({
                    type: RedDotType.AgentDiagnosis,
                });

                setDiagnosisUpdateNotice(content || '');
                setAlertShow(!!content);

                // 有更新提示，发请求标记提示已读
                if (content) {
                    await postRedDotMarkRead({
                        type: RedDotType.AgentDiagnosis,
                        subType: AgentDiagnosisSubType.SideBarAndBanner,
                    });
                }
            } catch (error) {
                console.error(error);
            }
        })();
    }, []);

    const closeAlert = useCallback(() => {
        setAlertShow(false);
    }, []);

    return (
        alertShow && (
            <div ref={domContainer}>
                <div className="inline-block">
                    <Alert
                        message={diagnosisUpdateNotice}
                        type="info"
                        showIcon
                        closable
                        className="mb-4"
                        onClose={closeAlert}
                    />
                </div>
            </div>
        )
    );
}
