/**
 * @file Agent 卡片菜单
 * <AUTHOR>
 * @date 2024/09/20
 * @description 从 AgentAction 组件中抽离的通用菜单
 */

import {useNavigate} from 'react-router-dom';
import {Button, message, Modal, Tooltip} from 'antd';
import {ExclamationCircleFilled} from '@ant-design/icons';
import copy from 'copy-to-clipboard';
import {useCallback, useEffect, useMemo, useState} from 'react';
import classNames from 'classnames';
import {AgentListExtInfo, AgentStatusLabel} from '@/api/agentList/interface';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useAgentNum} from '@/components/Sidebar/hooks/useAgentNum';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {MyAgentType} from '@/utils/loggerV2/interface';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {generateOperationModule, generateSessionId, OperationModuleType} from '@/utils/monitor/utils';
import {EVENT_EXT_KEY_CONST as EVENT_EXT_KEY_CONST_V1} from '@/utils/logger/constants/extkey';
import urls from '@/links';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {DuplicateAgentSource} from '@/api/agentDuplicate/interface';
import duplicateApi from '@/api/agentDuplicate';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import agentListApi from '@/api/agentList';
import {ErrorRequestCode, ErrorResponseCode} from '@/api/error';
import {isTpProxyMode} from '@/utils/tp/tpProxyMode';
import {DISALLOW_OPERATE_HINT} from '@/utils/tp/constant';
import {defaultName} from '@/store/agent/promptEditStoreV2';
import {ConfirmModalStyle, ConfirmModalStyleConfig} from '@/styles/components/ModalStyle';
import {SharePopover} from '@/modules/agentPromptEditV2/pc/components/ShareAgent/SharePopover';
import api from '@/api/appVersion';
import {AgentUrlChannel} from '@/modules/agentOutput/config';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {AgentPermission} from '@/api/agentEdit/interface';
import {
    AGENT_TAB_TYPE_NAME,
    AgentActionKey,
    AgentActionRecord,
    AgentData,
    AgentTabType,
    DISALLOW_KEYS,
} from '../../interface';
import {getCardExt} from '../../utils';

export default function PopoverMenu({
    agent,
    onRefresh,
    parentClose,
    setOpen,
    extData,
    open,
}: {
    agent: AgentData;
    parentClose: () => void;
    setOpen: (open: boolean) => void;
    onRefresh?: () => Promise<void>;
    extData?: AgentListExtInfo;
    open: boolean;
}) {
    const agentId = agent.appId;
    const title = agent.name || defaultName;
    const navigate = useNavigate();
    const {ubcClickLog} = useUbcLog();
    const {clickLog} = useUbcLogV2();

    const [modal, modalContextHolder] = Modal.useModal();
    // 超级权限下屏蔽删除智能体入口（特例：服务商代运营场景下不屏蔽）
    const [hasEnhancedAccess, userInfoData] = useUserInfoStore(store => [
        store.userInfoData?.userInfo.hasEnhancedAccess,
        store.userInfoData,
    ]);

    const allowCreate = !userInfoData?.hasTpProxy;

    const {updateAgentListNum, appLimit} = useAgentNum();
    // 上一版的打点函数，用于后面取消删除弹窗、删除agent操作的打点
    const clickLogV1 = useCallback(
        (value?: string) => {
            if (!value) return;

            ubcClickLog(value, {
                [EVENT_EXT_KEY_CONST_V1.AGENT_TYPE]: AGENT_TAB_TYPE_NAME[AgentTabType.Codeless],
            });
        },
        [ubcClickLog]
    );
    // 打点重构的打点函数，用于AgentAction，例如删除、分析的点击打点
    const clickLogAgentAction = useCallback(
        (value?: string) => {
            if (!value) return;

            clickLog(value, EVENT_PAGE_CONST.MY_AGENT, {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: title,
                [EVENT_EXT_KEY_CONST.MY_AGENT_TYPE]: MyAgentType.CODELESS,
            });
        },
        [agentId, clickLog, title]
    );

    // 复制ID到剪贴板
    const copyID = useCallback((id: string) => {
        try {
            copy(id);
        } catch (e) {
            message.error('ID复制失败');
        }

        message.success('ID已复制到剪贴板');
    }, []);

    // 删除Agent
    const deleteAgent = useCallback(
        async (id: string) => {
            try {
                // 已经删除agent打点
                clickLogV1(EVENT_TRACKING_CONST.AgentListAgentCardDeleteConfirm);

                await agentListApi.deleteAgent(id);

                message.success('删除成功');
                // 删除Agent后刷新Agent列表
                onRefresh && onRefresh();
                // 删除Agent后更新Agent列表数量
                updateAgentListNum();
            } catch (err: any) {
                message.error(err?.msg || '删除失败');
            }
        },
        [clickLogV1, onRefresh, updateAgentListNum]
    );

    // 删除智能体确认弹窗
    const deleteAgentConfirm = useCallback(
        async (id: string) => {
            await modal.confirm({
                icon: <ExclamationCircleFilled className="text-[#FF8200]" />,
                title: '删除后无法恢复，确认删除吗？',
                cancelText: '取消',
                styles: ConfirmModalStyleConfig,
                className: classNames(ConfirmModalStyle),
                okText: '确定',
                centered: true,
                autoFocusButton: null,
                afterClose: parentClose,
                onOk() {
                    // 删除弹窗确认点击打点
                    clickLogAgentAction(EVENT_VALUE_CONST.AGENT_DELETE_CONFIRM);
                    return deleteAgent(id);
                },
                // 取消删除弹窗打点
                onCancel() {
                    clickLogV1(EVENT_TRACKING_CONST.AgentListAgentCardDeleteCancel);
                },
            });
        },
        [modal, parentClose, clickLogAgentAction, deleteAgent, clickLogV1]
    );

    const closeModal = useCallback(() => {
        Modal.destroyAll();
    }, []);

    const duplicateAgent = useCallback(
        // 创建副本点击打点
        async (agentId: string) => {
            // 标记进入复制智能体功能
            generateSessionId();
            generateOperationModule(OperationModuleType.CopyAgent);
            try {
                const res = await duplicateApi.duplicate({
                    appId: agentId,
                    from: DuplicateAgentSource.MyAgent,
                    name: `${title}的副本`,
                });

                if (res.appId) {
                    message.success('智能体副本已创建');
                    navigate(`${urls.agentPromptEdit.raw()}?appId=${res.appId}&createBy=dup`);
                }
            } catch (error: any) {
                if (error.errno === ErrorResponseCode.AgentReachLimit) {
                    await modal.confirm({
                        icon: <ExclamationCircleFilled className="text-[#FF8200]" />,
                        styles: ConfirmModalStyleConfig,
                        className: classNames(ConfirmModalStyle),
                        content: `已达到智能体创建上限（${appLimit}）`,
                        footer: (
                            <div className="flex justify-end" onClick={closeModal}>
                                <Button type="primary">我知道了</Button>
                            </div>
                        ),
                        centered: true,
                        autoFocusButton: null,
                        bodyStyle: {
                            padding: '0 1.5rem',
                        },
                        afterClose: parentClose,
                    });
                }
                // 随着插件、商业化能力越来复杂，比如线索转化涉及营销通机审，复制智能体越来越久
                // 当前交互已不满足用户体验，需要优化，交互已知，后续和产品提优化需求
                // 临时方案：6s 超时，toast提示用户
                else if (error.errno === ErrorRequestCode.NetworkTimeoutError) {
                    message.info('智能体复制可能耗时较长，请稍候刷新');
                }
            }
        },
        [title, navigate, appLimit, closeModal, parentClose, modal]
    );
    const [shareUrl, setShareUrl] = useState('');
    const [isLoading, setLoading] = useState(false);

    // 获取分享链接
    const getShareUrl = useCallback(
        (id: string) => {
            const extLog = getCardExt(agent);
            clickLog(EVENT_EXT_KEY_CONST.AGENT_SHARE, EVENT_PAGE_CONST.MY_AGENT, {
                ...extLog,
                [EVENT_EXT_KEY_CONST.E_IS_OWNER]: 1,
            });

            if (!shareUrl) {
                setLoading(true);
                api.agentShareLinkInfo({
                    appId: id || '',
                    channel: AgentUrlChannel.agentURL,
                })
                    .then(data => {
                        setShareUrl(data.shareUrl);
                    })
                    .finally(() => setLoading(false));
            }
        },
        [shareUrl, agent, clickLog]
    );

    const actionClick = useCallback(
        (actionKey: AgentActionKey) => {
            if (!allowCreate && DISALLOW_KEYS.includes(actionKey)) return;
            if (actionKey !== AgentActionKey.Share) {
                setOpen(false);
            }

            const extLog = getCardExt(agent);
            switch (actionKey) {
                case AgentActionKey.Tuning:
                    clickLog(EVENT_EXT_KEY_CONST.AGENT_TUNING, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    navigate(`${urls.agentPromptEdit.raw()}?appId=${agentId}&activeTab=${AgentTab.Tuning}`);
                    break;
                case AgentActionKey.Output:
                    clickLog(EVENT_EXT_KEY_CONST.AGENT_DEPLOY, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    navigate(urls.agentPromptDetail.fill({id: agentId, tab: PluginTabType.Output}));
                    break;
                case AgentActionKey.CopyAgentId:
                    clickLog(EVENT_EXT_KEY_CONST.AGENT_COPYID, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    copyID(agentId);
                    break;
                case AgentActionKey.DelAgent:
                    clickLog(EVENT_EXT_KEY_CONST.AGENT_DELETE, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    deleteAgentConfirm(agentId);
                    break;
                case AgentActionKey.Duplicate:
                    clickLog(EVENT_EXT_KEY_CONST.AGENT_DUPLICATE, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    duplicateAgent(agentId);
                    break;
            }
        },
        [allowCreate, setOpen, navigate, agentId, clickLog, agent, copyID, deleteAgentConfirm, duplicateAgent]
    );

    const allowOperate = useCallback(
        (key: AgentActionKey) => {
            if (allowCreate) {
                return true;
            }
            return !DISALLOW_KEYS.includes(key);
        },
        [allowCreate]
    );

    const actionKeys = useMemo(() => {
        let keys = [];
        if (agent.labelStatus === AgentStatusLabel.DRAFTING) {
            keys = [AgentActionKey.Duplicate, AgentActionKey.CopyAgentId];
        } else if (agent.labelStatus === AgentStatusLabel.ONLINE && agent.permission !== AgentPermission.PRIVATE) {
            // 有预览链接，增加分享功能
            keys = [AgentActionKey.Output, AgentActionKey.Share, AgentActionKey.Duplicate, AgentActionKey.CopyAgentId];
        } else {
            keys = [AgentActionKey.Output, AgentActionKey.Duplicate, AgentActionKey.CopyAgentId];
        }

        if (!(hasEnhancedAccess && !isTpProxyMode())) {
            keys.push(AgentActionKey.DelAgent);
        }

        return keys;
    }, [agent.labelStatus, agent.permission, hasEnhancedAccess]);

    const [outputOpen, setOutputOpen] = useState(false);
    const onOpenChange = useCallback((visiable: boolean) => {
        setOutputOpen(visiable);
    }, []);

    useEffect(() => {
        setOutputOpen(open);
    }, [open]);

    return (
        <ul className="text-sm font-medium">
            {actionKeys.map(key =>
                key === AgentActionKey.Output ? (
                    <Tooltip
                        key={key}
                        title={extData?.wxInfo?.auth ? '微信小程序部署中，即将生效~' : ''}
                        placement="left"
                        overlayInnerStyle={{width: 'max-content', whiteSpace: 'nowrap'}}
                        open={outputOpen && extData?.wxInfo?.auth}
                        onOpenChange={onOpenChange}
                        align={{
                            offset: [-18, 0],
                        }}
                    >
                        <li
                            className="flex h-9 w-[102px] cursor-pointer items-center rounded-md pl-[15px] hover:bg-primary/10"
                            onClick={() => actionClick(key)}
                        >
                            {AgentActionRecord[key].name}
                        </li>
                    </Tooltip>
                ) : key === AgentActionKey.Share ? (
                    <LogContextProvider
                        ext={{agentId: agentId, agentName: agent.name, eIsOwner: 1}}
                        page={EVENT_PAGE_CONST.AGENT_EXPERIENCE}
                    >
                        <SharePopover
                            shareUrl={shareUrl}
                            id={agentId}
                            isLoading={isLoading}
                            textContent="手机扫码体验"
                            placement="right"
                            align={{
                                offset: [0, 0],
                            }}
                            trigger={['hover']}
                        >
                            <li
                                className="flex h-9 w-[102px] cursor-pointer items-center rounded-md pl-[15px] hover:bg-primary/10"
                                key={key}
                                onMouseEnter={() => getShareUrl(agentId)}
                            >
                                {AgentActionRecord[key].name}
                            </li>
                        </SharePopover>
                    </LogContextProvider>
                ) : (
                    <Tooltip
                        key={key}
                        title={allowOperate(key) ? undefined : DISALLOW_OPERATE_HINT}
                        overlayInnerStyle={{width: 'max-content', whiteSpace: 'nowrap'}}
                    >
                        <li
                            className={`flex h-9 w-[102px] cursor-pointer items-center rounded-md pl-[15px] ${
                                allowOperate(key) ? 'hover:bg-primary/10' : 'text-[#3A3A3E4D]'
                            }`}
                            key={key}
                            onClick={() => actionClick(key)}
                        >
                            {AgentActionRecord[key].name}
                        </li>
                    </Tooltip>
                )
            )}
            {modalContextHolder}
        </ul>
    );
}
