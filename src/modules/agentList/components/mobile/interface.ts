/**
 * @file 我的智能体列表页卡片数据类型
 * <AUTHOR>
 */

import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {AgentType, AgentListExtInfo, AgentStatusLabel} from '@/api/agentList/interface';
import {FigureTaskStatus} from '@/api/agentEditV2/interface';
import {SpeechStatus} from '@/api/agentEditV2';
import {AuditSign, AuditStatus, Logo} from '../../interface';

export enum TagColor {
    Red = 'error',
    Gray = 'default',
}

export interface ExtraTagsInfo {
    text: string;
    color: TagColor;
}

interface AgentStatusConfigs {
    [key: string]: ExtraTagsInfo;
}

export const STATUS_TAG_MAPS: AgentStatusConfigs = {
    [AuditStatus.Developing]: {
        text: '开发中',
        color: TagColor.Gray,
    },
    [AuditStatus.Auditing]: {
        text: '已发布',
        color: TagColor.Gray,
    },
    [AuditStatus.AuditFailed]: {
        text: '审核不通过',
        color: TagColor.Red,
    },
    [AuditStatus.Online]: {
        text: '已上线',
        color: TagColor.Gray,
    },
    [AuditStatus.Offline]: {
        text: '已下线',
        color: TagColor.Gray,
    },
    [AuditStatus.ForcedOffline]: {
        text: '已下线',
        color: TagColor.Gray,
    },
    [AuditStatus.Editing]: {
        text: '修改中',
        color: TagColor.Gray,
    },
};

export interface CardData {
    logo: Logo;
    agentId: string;
    agentType: AgentType;
    modeType: AgentModeType;
    showPluginError: boolean;
    title: string;
    extraTags: string[];
    description: string;
    /** 审核不通过原因 */
    auditFailedMsg: string;
    extData?: AgentListExtInfo;
    speechStatus: SpeechStatus;
    figureTaskStatus: FigureTaskStatus;
    cardClickHandle: () => void;
    labelStatus: AgentStatusLabel;
    /** 最新修改时间  */
    latestTime: number | null;
    /** 审核状态  */
    auditSign: AuditSign;
}
