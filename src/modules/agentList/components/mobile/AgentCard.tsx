/* eslint-disable complexity */
/**
 * @file 我的智能体Agent卡片组件-mobile
 * <AUTHOR>
 * @date 2024/09/20
 * @description 功能全部迁移至 card 组件
 */
import {useCallback, useMemo, useRef} from 'react';
import {Card} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import classNames from 'classnames';
import dayjs from 'dayjs';
import {useNavigate} from 'react-router-dom';
import styled from '@emotion/styled';
import {AgentData, AuditStatus, AgentTagType} from '@/modules/agentList/interface';
import useTextOverflow from '@/modules/center/hooks/useTextOverflow';
import AgentLogoComponent from '@/components/AgentLogo';
import {AgentPermission} from '@/api/agentEdit';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {AgentListExtInfo, AgentType, AllAuditStatus} from '@/api/agentList/interface';
import urls from '@/links';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {defaultName} from '@/store/agent/promptEditStoreV2';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {AgentTitleTags} from '@/components/CardTemplate/AgentTitleTags';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {TagsUsedBy} from '@/modules/center/interface';
import AgentTags from '../AgentTags';
import {useAgentCardPress} from '../../hooks/useAgentCardPress';
import {mAgentLogoSize, getCardExt} from '../../utils';
import AgentActions from './AgentActions';

const StyledCard = styled(Card)`
    & .ant-card-head-title {
        overflow: auto !important;
    }
`;
export default function AgentCardM({
    agent,
    onRefresh,
    extData,
}: {
    agent: AgentData;
    onRefresh?: () => Promise<void>;
    extData?: AgentListExtInfo;
}) {
    const [titleRef] = useTextOverflow();
    const [descriptionRef] = useTextOverflow();

    const errTip = useMemo(() => {
        const showPluginError =
            agent.offlinePluginNum > 0 &&
            (agent.permission === AgentPermission.PUBLIC || agent.permission === AgentPermission.LINK) &&
            agent.status !== AuditStatus.Developing;
        return showPluginError ? '引用插件异常，请使用电脑修改' : '';
    }, [agent.offlinePluginNum, agent.permission, agent.status]);

    const clickErrTip = useCallback(
        (event: React.MouseEvent) => {
            if (errTip) {
                Toast.show({content: errTip});
            }

            event.stopPropagation();
            event.preventDefault();
        },
        [errTip]
    );

    const {clickLog} = useUbcLogV2();
    const navigate = useNavigate();

    const prohibitEdit = useMemo(() => {
        return (
            agent?.tags?.includes(AgentTagType.TEACHER_RECRUIT_AGENT) &&
            agent.originStatus === AllAuditStatus.Developing
        );
    }, [agent.originStatus, agent?.tags]);

    const handleTitleClick = useCallback(() => {
        if (prohibitEdit) {
            Toast.show('请先完成招募活动智能体创建');
            return;
        }

        clickLog(EVENT_TRACKING_CONST.AgentListAgentCard, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        if (agent.agentType === AgentType.Codeless && agent.modeType !== AgentModeType.Workflow) {
            navigate(`${urls.agentPromptEdit.raw()}?appId=${agent.appId}&activeTab=${AgentTab.Create}`);
        } else {
            // 非已上线低代码、千帆提示前往PC编辑
            Toast.show('请使用电脑端进行编辑');
        }
    }, [agent, clickLog, navigate, prohibitEdit]);

    const {isPress, handleMouseDown, handleMouseUp, handleMouseEnter, handleMouseLeave} = useAgentCardPress();

    const cardRef = useRef<HTMLDivElement>(null);

    return (
        <StyledCard
            ref={cardRef}
            className={'group relative rounded-xl font-pingfang shadow-none transition duration-200'}
            // 卡片标题和描述
            title={
                <div className="flex min-h-[85px]" onClick={handleTitleClick}>
                    <div className="ml-[2px] flex-shrink-0">
                        <div className="mt-[1px] rounded-full border-black transition duration-200 group-hover:shadow-card-selected">
                            <AgentLogoComponent
                                width={mAgentLogoSize.width}
                                height={mAgentLogoSize.height}
                                fontSize={mAgentLogoSize.fontSize}
                                logoUrl={agent?.logoUrl}
                                isPress={isPress}
                                figureTaskStatus={agent.figureTaskStatus}
                            />
                        </div>
                    </div>

                    <div className="ml-3 flex min-w-0 flex-col">
                        <div className="flex">
                            <div
                                className={classNames(
                                    'mr-1 h-[16px] truncate text-base font-semibold leading-none text-black',
                                    {}
                                )}
                                ref={titleRef}
                            >
                                {agent.name || defaultName}
                            </div>
                            {((agent.agentMedalList && agent.agentMedalList.length > 0) ||
                                (extData?.tags && extData?.tags.length > 0)) && (
                                <div className="ml-[3px] flex h-[16px] items-center leading-[16px]">
                                    {agent.agentMedalList &&
                                        agent.agentMedalList.length > 0 &&
                                        agent.agentMedalList.map(item => (
                                            <img
                                                src={item.level.img}
                                                key={item.medalId}
                                                className="mr-[3px] h-[14px] w-[14px] bg-contain last:mr-0"
                                            />
                                        ))}
                                    {/* 智能体获得的头衔标签 */}
                                    {extData?.tags && extData?.tags.length > 0 && (
                                        <AgentTitleTags
                                            tags={extData?.tags}
                                            cardContainerRef={cardRef}
                                            usedBy={TagsUsedBy.AgentCard}
                                            tooltipPlacement="bottom"
                                        />
                                    )}
                                </div>
                            )}
                            <div className="flex-grow"></div>
                            {errTip && (
                                <span
                                    className="iconfont icon-tip ml-2 leading-none text-error"
                                    ref={titleRef}
                                    onClick={clickErrTip}
                                ></span>
                            )}
                        </div>

                        <div
                            className="mt-[6px] line-clamp-2 w-full truncate whitespace-normal text-sm font-normal text-gray-secondary"
                            ref={descriptionRef}
                        >
                            {agent.description}
                        </div>

                        <div className="flex flex-grow">
                            <AgentTags agent={agent} extData={extData} />
                        </div>

                        {extData?.isShowTime && extData.latestTime !== 0 && (
                            <div className="mt-1 text-[14px] text-gray-quaternary">
                                最新草稿 {dayjs(extData.latestTime).format('YYYY-MM-DD HH:mm:ss')}
                            </div>
                        )}
                    </div>
                </div>
            }
            headStyle={{
                borderBottom: 0,
                paddingLeft: 13,
                paddingTop: 15,
                paddingRight: 13,
                paddingBottom: 10,
            }}
            bodyStyle={{
                paddingTop: 0,
                paddingBottom: 15,
                paddingLeft: 13,
                paddingRight: 13,
            }}
            bordered={false}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onTouchStart={handleMouseDown}
            onTouchEnd={handleMouseUp}
        >
            {/* 卡片内容 / 操作区 */}
            <div className="border-t border-[#edeef0] pt-[15px] text-right">
                <AgentActions agent={agent} onRefresh={onRefresh} extData={extData} prohibitEdit={prohibitEdit} />
            </div>
        </StyledCard>
    );
}
