/**
 * @file 我的智能体列表页
 * <AUTHOR>
 * @update <EMAIL> 升级列表-区分零代码、低代码、千帆AppBuilder
 */
import {Row, Col, Button} from 'antd';
import {UIEvent, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import AgentCard from '@/modules/agentList/components/mobile/AgentCard';
import {
    AgentData,
    AgentTabType,
    AGENT_TAB_TYPE_NAME,
    DEFAULT_PAGINATION_SETTINGS,
    AgentListProps,
} from '@/modules/agentList/interface';
import urls from '@/links';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/logger/constants/extkey';
import {AgentType, AgentSource, AgentListExtInfo, ExtDataType} from '@/api/agentList/interface';
import api from '@/api/agentList';
import Loading from '@/components/Loading';
import {AgentWebScene, useAgentPreviewSceneStore} from '@/store/agentPreviewScene/useAgentPreviewSceneStore';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {ErrorState} from '@/components/ErrorState';
import {reachBottom} from '../../utils';
import {agentExtArrToMap} from '../../hooks/useRefreshAgentList';

export default function AgentsList({agentTabType, refreshInfo, onRefresh, initExtInfo}: AgentListProps) {
    // 列表滚动时的当前页码
    const pageNoRef = useRef(DEFAULT_PAGINATION_SETTINGS.pageNo);
    // 是否请求到了最后一页
    const couldRequestMore = useRef(true);
    // 列表数据
    const [agents, setAgents] = useState<AgentData[]>(refreshInfo.agentListRes.agentList);

    const {setAgentWebScene} = useAgentPreviewSceneStore(store => ({
        setAgentWebScene: store.setAgentWebScene,
    }));

    // 扩充数据
    const [extInfo, setExtInfo] = useState<Record<string, AgentListExtInfo>>({});
    const onRefreshHandler = useCallback(async () => {
        setExtInfo({});
        onRefresh?.();
    }, [onRefresh]);

    const exts = useMemo(() => ({...initExtInfo, ...extInfo}), [initExtInfo, extInfo]);

    useEffect(() => {
        // 根据初始请求返回数据，设置列表首页
        pageNoRef.current = DEFAULT_PAGINATION_SETTINGS.pageNo;
        couldRequestMore.current =
            (refreshInfo.agentListRes.agentList.length || 0) < (refreshInfo.agentListRes.total || 0);
        setAgents(refreshInfo.agentListRes.agentList);
        // 设置智能体预览渠道值
        setAgentWebScene(AgentWebScene.CenterList);
    }, [refreshInfo.agentListRes.agentList, refreshInfo.agentListRes.total, setAgentWebScene]);

    // 滚动到列表底部时展现的 loading
    const [isShowBottomLoading, setIsShowBottomLoading] = useState(false);

    // 是否处于滚动分页请求中状态
    const inRequestingRef = useRef(false);

    const getAgentList = useCallback(async () => {
        inRequestingRef.current = true;
        return api
            .getAgentList({
                agentSource: agentTabType === AgentTabType.QianFan ? AgentSource.QianFan : AgentSource.LingJing,
                agentType: agentTabType === AgentTabType.LowCode ? AgentType.LowCode : AgentType.Codeless,
                pageNo: pageNoRef.current,
                pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
            })
            .finally(() => {
                inRequestingRef.current = false;
            });
    }, [agentTabType]);

    const updateAgentList = useCallback(
        async (currentAgentList?: AgentData[]) => {
            const {agentList, total} = await getAgentList();

            setIsShowBottomLoading(false);

            const list = (currentAgentList || []).concat(agentList || []);
            setAgents(list);

            couldRequestMore.current = list.length < total;
        },
        [getAgentList, setAgents]
    );

    const updateAgentExt = useCallback(async () => {
        const extRes = await api.getAgentListExt({
            agentSource: AgentSource.LingJing,
            agentType: agentTabType === AgentTabType.LowCode ? AgentType.LowCode : AgentType.Codeless,
            ...DEFAULT_PAGINATION_SETTINGS,
            type: `${ExtDataType.Wx},${ExtDataType.TuningReportAndDistribution}`,
            pageNo: pageNoRef.current,
        });
        setExtInfo(prev => ({...prev, ...agentExtArrToMap(extRes)}));
    }, [agentTabType]);

    const handleScroll = useCallback(
        async (event: UIEvent<HTMLDivElement>) => {
            // 页面滚动时隐藏所有popover
            // 使用 antd-mobile popover组件也是各种问题，先只能这样处理了
            const domList = document.getElementsByClassName('ant-popover');
            Array.prototype.forEach.call(domList, dom => {
                if (dom) {
                    dom.classList.add('ant-popover-hidden');
                }
            });

            if (inRequestingRef.current || !couldRequestMore.current) {
                return;
            }

            if (!reachBottom(event.currentTarget)) {
                return;
            }

            pageNoRef.current += 1;

            try {
                setIsShowBottomLoading(true);
                updateAgentList(agents);
                updateAgentExt();
            } catch (e) {
                // 请求失败时还原
                pageNoRef.current -= 1;
                setIsShowBottomLoading(false);
            }
        },
        [updateAgentList, agents, updateAgentExt]
    );

    const navigate = useNavigate();
    const {ubcClickLog} = useUbcLog();

    const clickEmptyCreateBtn = useCallback(() => {
        // 空态-创建 agent 入口打点
        ubcClickLog(EVENT_TRACKING_CONST.AgentListPageCreateAgentBtn, {
            [EVENT_EXT_KEY_CONST.AGENT_TYPE]: AGENT_TAB_TYPE_NAME[agentTabType],
        });
        // 跳转零代码创建页面
        navigate(urls.agentPromptQuickStart.raw());
    }, [agentTabType, navigate, ubcClickLog]);

    return (
        <div onScroll={handleScroll} className="h-[calc(100vh-114px)] overflow-y-auto overflow-x-hidden px-3">
            {refreshInfo.isLoading ? (
                <Loading />
            ) : refreshInfo.isError ? (
                <RenderError error={refreshInfo.error} />
            ) : refreshInfo.agentListRes.agentList.length > 0 ? (
                // 如果有智能体数据，渲染卡片列表
                <div>
                    <Row gutter={[16, 8]} wrap>
                        {agents.map(agent => {
                            return (
                                // 移动端每行展示一个卡片
                                <Col span={24} key={agent.appId}>
                                    <AgentCard
                                        agent={agent}
                                        onRefresh={onRefreshHandler}
                                        extData={exts?.[agent.appId]}
                                    />
                                </Col>
                            );
                        })}
                    </Row>

                    {/* 滚动到底部时展现加载态 */}
                    {isShowBottomLoading && (
                        <div className="flex items-center justify-center pb-6 pt-[3px]">
                            <span className="text-xs leading-3 text-flow-hover">加载中...</span>
                        </div>
                    )}
                </div>
            ) : (
                <ErrorState msg="暂无数据" smallSize>
                    {/* 智能体列表为空显示空态 */}
                    {agentTabType === AgentTabType.Codeless ? (
                        <Button
                            type="primary"
                            className="mt-[12px] h-auto rounded-[100px] px-[14px] py-[4px] font-medium active:opacity-20"
                            onClick={clickEmptyCreateBtn}
                        >
                            立即创建
                        </Button>
                    ) : null}
                </ErrorState>
            )}
        </div>
    );
}
