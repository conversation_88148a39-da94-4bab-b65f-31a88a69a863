/**
 * @file Agent列表页-mobile 区分零代码、低代码、千帆AppBuilder 3个Tab
 * <AUTHOR>
 */

import {Alert} from 'antd';
import TopBar from '@/components/mobile/TopBar';
import CreateBtn from '@/components/mobile/TopBar/createBtn';
import Left from '@/components/Sidebar/v2-m';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import Loading from '@/components/Loading';
import {LOW_CODE_AGENT_DEPRECATED_DATE} from '@/modules/agentPromptEditV2/utils';
import {AgentTabType} from '../../interface';
import useRefreshAgentList from '../../hooks/useRefreshAgentList';
import useAgentTypeTabs from '../../hooks/useAgentTypeTabs';
import AgentList from './AgentList';
import AgentTypeTabs from './AgentTypeTabs';
import {AgentWarning} from './AgentWarning';

/**
 * 智能体列表页主要内容
 */
export default function AllAgentList() {
    const {
        pageLoading,
        loadError,
        requestError,
        isUserFromQianFan,
        hasCodelessAgent,
        hasLowCodeAgent,
        hasWorkflowAgent,
        codelessRefreshInfo,
        lowCodeRefreshInfo,
        qianFanRefreshInfo,
        refreshCodelessList,
        refreshLowCodeList,
        workflowRefreshInfo,
        refreshWorkflowList,
        codelessExtInfo,
        lowCodeExtInfo,
        workflowExtInfo,
    } = useRefreshAgentList();

    const {tabItems, activeTabKey, tabChange} = useAgentTypeTabs(
        pageLoading,
        hasCodelessAgent,
        hasLowCodeAgent,
        hasWorkflowAgent,
        qianFanRefreshInfo
    );

    return (
        <div className="relative min-h-[100vh] bg-gray-bg-base font-pingfang">
            <TopBar title="我的智能体" leftNode={<Left />} rightNode={<CreateBtn />} />
            {pageLoading ? (
                <Loading />
            ) : loadError ? (
                <RenderError error={requestError} />
            ) : (
                <>
                    {/* 智能体列表tab */}
                    <div className="p-3">
                        <AgentTypeTabs
                            tabItems={tabItems.filter(tab => tab.id !== AgentTabType.LowCode || hasLowCodeAgent)}
                            activeTab={activeTabKey}
                            switchTab={tabChange}
                        />
                    </div>
                    {/* 警告信息 */}
                    {activeTabKey === AgentTabType.LowCode && (
                        <div className="mb-4 inline-block">
                            <Alert message={LOW_CODE_AGENT_DEPRECATED_DATE} type="error" showIcon />
                        </div>
                    )}
                    <AgentWarning />
                    {/* 智能体列表区域 */}
                    {/* 零代码 */}
                    <div className={activeTabKey === AgentTabType.Codeless ? '' : 'hidden'}>
                        <AgentList
                            agentTabType={AgentTabType.Codeless}
                            refreshInfo={codelessRefreshInfo}
                            onRefresh={refreshCodelessList}
                            initExtInfo={codelessExtInfo}
                        />
                    </div>
                    {/* 工作流 */}
                    <div className={activeTabKey === AgentTabType.Workflow ? '' : 'hidden'}>
                        <AgentList
                            agentTabType={AgentTabType.Workflow}
                            refreshInfo={workflowRefreshInfo}
                            onRefresh={refreshWorkflowList}
                            initExtInfo={workflowExtInfo}
                        />
                    </div>
                    {/* 低代码 */}
                    <div className={activeTabKey === AgentTabType.LowCode ? '' : 'hidden'}>
                        <AgentList
                            agentTabType={AgentTabType.LowCode}
                            refreshInfo={lowCodeRefreshInfo}
                            onRefresh={refreshLowCodeList}
                            initExtInfo={lowCodeExtInfo}
                        />
                    </div>
                    {/* 千帆 */}
                    {isUserFromQianFan && (
                        <div className={activeTabKey === AgentTabType.QianFan ? '' : 'hidden'}>
                            <AgentList agentTabType={AgentTabType.QianFan} refreshInfo={qianFanRefreshInfo} />
                        </div>
                    )}
                </>
            )}
        </div>
    );
}
