import {Alert} from 'antd';
import {useAgentCountStore} from '@/store/agent/agentNum';

export const AgentWarning = () => {
    const {appNum, appLimit} = useAgentCountStore(store => ({
        setAgentNumber: store.setAgentNumber,
        appNum: store.appNum,
        appLimit: store.appLimit,
    }));

    return (
        appNum !== undefined &&
        appLimit !== undefined &&
        appNum >= appLimit && (
            <div className="inline-block">
                <Alert
                    message={`1个账号最多创建${appLimit}个智能体，如需继续创建，请先清理闲置智能体。`}
                    type="warning"
                    showIcon
                    className="mb-3 ml-3"
                />
            </div>
        )
    );
};
