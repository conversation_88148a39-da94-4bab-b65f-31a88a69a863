/**
 * @file Agent 卡片操作集组件-mobile
 * <AUTHOR>
 */

import {useCallback, useEffect, useMemo, useState} from 'react';
import {Badge, Popover, Space, message} from 'antd';
import classNames from 'classnames';
import {css} from '@emotion/css';
import Toast from 'antd-mobile/es/components/toast';
import {useNavigate} from 'react-router-dom';
import {
    AgentActionKey,
    AgentTabType,
    AGENT_TAB_TYPE_NAME,
    AgentActionRecord,
    AgentActionInfo,
    AgentData,
    AuditSign,
} from '@/modules/agentList/interface';
import {ErrorRequestCode, ErrorResponseCode} from '@/api/error';
import duplicateApi from '@/api/agentDuplicate';
import {EVENT_TRACKING_CONST} from '@/utils/logger/logConfig';
import {EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants/ext';
import {useUbcLog} from '@/utils/logger/useUbcLogger';
import urls from '@/links';
import agentListApi from '@/api/agentList';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {PluginTabType} from '@/api/pluginVersion/interface';
import {AgentType, AgentStatusLabel, AllAuditStatus, AgentListExtInfo} from '@/api/agentList/interface';
import ModalM from '@/components/mobile/Modal';
import PopupM from '@/components/mobile/Popup';
import {DuplicateAgentSource} from '@/api/agentDuplicate/interface';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useAgentNum} from '@/components/Sidebar/hooks/useAgentNum';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import {isTpProxyMode} from '@/utils/tp/tpProxyMode';
import {AgentModeType} from '@/modules/agentPromptEditV2/interface';
import {getTargetLink} from '@/modules/center/components/mobile/getTargetLink';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {defaultName} from '@/store/agent/promptEditStoreV2';
import {AgentPermission} from '@/api/agentEdit';
import {getAgentPreview} from '@/api/myAgentPreview';
import {getCardExt} from '../../utils';

const PopoverOverlayCSS = css`
    z-index: 100 !important;
    .ant-popover-content .ant-popover-inner {
        transform: translateX(13px);
        border-radius: 9px !important;
        padding: 0px;
        box-shadow: 0px 2px 30px 0px #1d22520f;
    }
`;

/** 外露的操作按钮 */
function ExposedAction({
    actionInfo,
    actionClick,
    modeType,
    redDot,
}: {
    actionInfo: AgentActionInfo;
    actionClick: (key: AgentActionKey) => void;
    modeType?: AgentModeType;
    redDot?: boolean;
}) {
    const onClick = useCallback(() => {
        actionClick(actionInfo.actionKey);
    }, [actionClick, actionInfo.actionKey]);

    return (
        <Space
            size={[4, 0]}
            align="center"
            className={classNames(
                'h-9 cursor-pointer rounded-[9px] bg-[#5562F214] px-[18px] text-sm font-medium text-black active:opacity-20',
                {
                    'opacity-40': modeType === AgentModeType.Workflow && actionInfo.actionKey === AgentActionKey.Edit,
                }
            )}
            onClick={onClick}
        >
            {actionInfo.icon && <span className={`iconfont block ${actionInfo.icon}`}></span>}
            <Badge dot={redDot} offset={[4, -2.5]}>
                <span>{actionInfo.mobileName || actionInfo.name}</span>
            </Badge>
        </Space>
    );
}

/** 更多按钮Popover的操作按钮集合 */
function PopoverContent({
    actionKeys,
    actionClick,
}: {
    actionKeys: AgentActionKey[];
    actionClick: (key: AgentActionKey) => void;
}) {
    // 超级权限下屏蔽删除智能体入口（特例：服务商代运营场景下不屏蔽）
    const [hasEnhancedAccess] = useUserInfoStore(store => [store.userInfoData?.userInfo.hasEnhancedAccess]);

    if (actionKeys.length === 0) {
        return null;
    }

    return (
        <ul className="text-sm font-medium">
            {actionKeys.map(
                key =>
                    !(hasEnhancedAccess && !isTpProxyMode() && key === AgentActionKey.DelAgent) && (
                        <li
                            className="mx-[21px] cursor-pointer border-t border-[#edeef0] py-[15px] first:border-0"
                            key={key}
                            onClick={() => actionClick(key)}
                        >
                            <Space size={[4, 0]}>
                                {AgentActionRecord[key].icon && (
                                    <span className={`iconfont block ${AgentActionRecord[key].icon}`}></span>
                                )}
                                <span>{AgentActionRecord[key].mobileName || AgentActionRecord[key].name}</span>
                            </Space>
                        </li>
                    )
            )}
        </ul>
    );
}

// 零代码Agent卡片操作按钮
// eslint-disable-next-line complexity
export default function AgentActionsM({
    agent,
    onRefresh,
    extData,
    prohibitEdit,
}: {
    agent: AgentData;
    onRefresh?: () => Promise<void>;
    extData?: AgentListExtInfo;
    prohibitEdit?: boolean;
}) {
    const {clickLog: clickLogV2, showLog} = useUbcLogV2();

    const [popoverOpen, setPopoverOpen] = useState(false);

    const auditFailedMsg = (agent.auditSign === AuditSign.FAIL && agent.message) || '';
    const handleOpenChange = useCallback(
        (visible: boolean) => {
            if (visible) {
                showLog(EVENT_VALUE_CONST.AGENT_MORE_PANEL, EVENT_PAGE_CONST.MY_AGENT, {
                    ...getCardExt(agent),
                    [EVENT_EXT_KEY_CONST.E_AGENT_STATUS]: agent.labelStatus,
                });
            }

            setPopoverOpen(visible);
        },
        [agent, showLog]
    );

    const navigate = useNavigate();
    const {ubcClickLog} = useUbcLog();
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

    const [reachLimitModalOpen, setReachLimitModalOpen] = useState(false);
    const [auditFailedReasonOpen, setAuditFailedReasonOpen] = useState(false);

    const userInfoData = useUserInfoStore(store => store.userInfoData);
    // 用户被服务商代理账号后，是否具有开发/创建权限
    const allowCreate = !userInfoData?.hasTpProxy;

    const {updateAgentListNum, appLimit} = useAgentNum();

    const agentId = agent.appId;
    const agentType = agent.agentType;
    const modeType = agent.modeType;

    const actionKeys = useMemo<AgentActionKey[]>(() => {
        // 工作流模式下，Agent卡片无复制操作
        const moreActionKeys =
            agent.agentType === AgentType.Codeless
                ? modeType === AgentModeType.Workflow
                    ? [AgentActionKey.DataAnalysis, AgentActionKey.DelAgent]
                    : [AgentActionKey.DataAnalysis, AgentActionKey.Duplicate, AgentActionKey.DelAgent]
                : [AgentActionKey.DelAgent];
        auditFailedMsg && moreActionKeys.unshift(AgentActionKey.AuditFailedReason);
        return moreActionKeys;
    }, [agent.agentType, modeType, auditFailedMsg]);

    const clickLog = useCallback(
        (value?: string) => {
            if (!value) return;
            ubcClickLog(value, {
                [EVENT_EXT_KEY_CONST.AGENT_TYPE]: AGENT_TAB_TYPE_NAME[AgentTabType.Codeless],
            });
        },
        [ubcClickLog]
    );

    const openDeleteConfirm = useCallback(() => {
        setConfirmOpen(true);
    }, []);
    const closeDeleteConfirm = useCallback(() => {
        setConfirmOpen(false);
        clickLog(EVENT_TRACKING_CONST.AgentListAgentCardDeleteCancel);
    }, [clickLog]);

    const closeReachLimitModal = useCallback(() => {
        setReachLimitModalOpen(false);
    }, []);

    // 删除Agent
    const deleteAgent = useCallback(async () => {
        try {
            setConfirmLoading(true);

            clickLog(EVENT_TRACKING_CONST.AgentListAgentCardDeleteConfirm);

            await agentListApi.deleteAgent(agentId);

            message.success('删除成功');
            // 删除Agent后刷新Agent列表
            onRefresh && onRefresh();

            // 删除Agent后刷新Agent列表数量
            updateAgentListNum();
        } catch (err: any) {
            message.error(err?.msg || '删除失败');
        } finally {
            setConfirmLoading(false);
        }
    }, [agentId, clickLog, onRefresh, updateAgentListNum]);

    const closeAuditFailedReason = useCallback(() => {
        setAuditFailedReasonOpen(false);
    }, []);
    const duplicateAgent = useCallback(
        async (agentId: string) => {
            ubcClickLog(EVENT_TRACKING_CONST.AgentListAgentCardDuplicate, {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: agent.name || defaultName,
            });
            try {
                const res = await duplicateApi.duplicate({
                    appId: agentId,
                    from: DuplicateAgentSource.MyAgent,
                    name: `${agent.name || defaultName}的副本`,
                });

                if (res.appId) {
                    Toast.show({content: '智能体副本已创建'});
                    navigate(`${urls.agentPromptEdit.raw()}?appId=${res.appId}&createBy=dup`);
                }
            } catch (error: any) {
                if (error.errno === ErrorResponseCode.AgentReachLimit) {
                    setReachLimitModalOpen(true);
                }
                // 随着插件、商业化能力越来复杂，比如线索转化涉及营销通机审，复制智能体越来越久
                // 当前交互已不满足用户体验，需要优化，交互已知，后续和产品提优化需求
                // 临时方案：6s 超时，toast提示用户
                else if (error.errno === ErrorRequestCode.NetworkTimeoutError) {
                    Toast.show('智能体复制可能耗时较长，请稍候刷新');
                }
            }
        },
        [ubcClickLog, agent.name, navigate]
    );

    const actionClick = useCallback(
        // eslint-disable-next-line complexity
        async (actionKey: AgentActionKey) => {
            if (prohibitEdit) {
                Toast.show('请先完成招募活动智能体创建');
                return;
            }

            setPopoverOpen(false);
            const extLog = getCardExt(agent);
            switch (actionKey) {
                // Agent 编辑
                case AgentActionKey.Edit:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_EDIT, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    if (modeType === AgentModeType.Workflow) {
                        Toast.show('请使用电脑端进行编辑');
                    } else {
                        navigate(`${urls.agentPromptEdit.raw()}?appId=${agentId}&activeTab=${AgentTab.Create}`);
                    }

                    break;
                // Agent 数据分析
                case AgentActionKey.DataAnalysis:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_ANALYSIS, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    if (agentType === AgentType.Codeless) {
                        navigate(urls.agentPromptDetail.fill({id: agentId, tab: PluginTabType.Overview}));
                    } else if (agentType === AgentType.LowCode) {
                        navigate(urls.agentFlowDetail.fill({id: agentId, tab: PluginTabType.Overview}));
                    }

                    break;
                // Agent 删除
                case AgentActionKey.DelAgent:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_DELETE, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    openDeleteConfirm();
                    break;
                // Agent 审核不通过原因
                case AgentActionKey.AuditFailedReason:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_REVIEWFAIL_REASON, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    setAuditFailedReasonOpen(true);
                    break;
                // Agent 创建副本
                case AgentActionKey.Duplicate:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_DUPLICATE, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    duplicateAgent(agentId);
                    break;
                // Agent 体验
                case AgentActionKey.Preview:
                    clickLogV2(EVENT_VALUE_CONST.AGENT_EXP, EVENT_PAGE_CONST.MY_AGENT, extLog);
                    // 体验前需判断是否部署成功，否则提示用户
                    try {
                        const previewInfo = await getAgentPreview({appId: agent.appId});
                        window.location.href = getTargetLink(previewInfo);
                    } catch (error: any) {
                        Toast.show('正在部署中，请稍后重试~');
                    }

                    break;
            }
        },
        [agent, clickLogV2, modeType, agentType, openDeleteConfirm, duplicateAgent, agentId, navigate, prohibitEdit]
    );

    /* 体验按钮展现打点 */
    useEffect(() => {
        if ([AgentStatusLabel.PUBLISHED, AgentStatusLabel.ONLINE].includes(agent.labelStatus)) {
            showLog(EVENT_VALUE_CONST.AGENT_EXP, EVENT_PAGE_CONST.MY_AGENT, getCardExt(agent));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const showPreviewButton = useMemo(() => {
        if (
            ![
                AllAuditStatus.Auditing,
                AllAuditStatus.Online,
                AllAuditStatus.Editing,
                AllAuditStatus.SecondAuditing,
                AllAuditStatus.SecondAuditFailed,
                AllAuditStatus.SecondAuditSuccess,
            ].includes(agent.originStatus)
        ) {
            return false;
        } else if (agent.labelStatus === AgentStatusLabel.PUBLISHED && agent.permission !== AgentPermission.PRIVATE) {
            return true;
        } else {
            return agent.labelStatus !== AgentStatusLabel.DRAFTING;
        }
    }, [agent.labelStatus, agent.originStatus, agent.permission]);

    return (
        <Space
            style={{
                opacity: prohibitEdit ? 0.5 : 1,
            }}
        >
            {/* action-体验 */}
            {showPreviewButton && <ExposedAction actionInfo={AgentActionRecord.preview} actionClick={actionClick} />}
            {/* action-编辑 */}
            {agentType === AgentType.Codeless && (
                <ExposedAction
                    actionInfo={AgentActionRecord.edit}
                    actionClick={actionClick}
                    modeType={modeType}
                    redDot={extData?.redDotInfo?.editPlagiarizeRedDot || extData?.redDotInfo?.editSimilarRedDot}
                />
            )}
            {/* action-更多：删除、审核不通过原因 */}
            <Popover
                trigger="hover"
                placement="bottomRight"
                open={popoverOpen}
                onOpenChange={allowCreate ? handleOpenChange : showDisallowOperateToast}
                content={<PopoverContent actionKeys={actionKeys} actionClick={actionClick} />}
                overlayClassName={PopoverOverlayCSS}
            >
                <span
                    className={classNames([
                        'iconfont icon-more flex h-9 w-9 cursor-pointer items-center justify-center rounded-[9px] bg-[#5562F214] text-sm font-medium text-black active:opacity-20',
                        allowCreate ? '' : 'text-opacity-40',
                    ])}
                ></span>
            </Popover>
            <ModalM
                open={confirmOpen}
                okText="删除"
                cancelText="取消"
                confirmLoading={confirmLoading}
                onCancel={closeDeleteConfirm}
                onOk={deleteAgent}
            >
                <p className="text-center text-black opacity-90">删除后无法恢复，确认删除吗？</p>
            </ModalM>
            {agentType === AgentType.Codeless && (
                <ModalM
                    open={reachLimitModalOpen}
                    footer={[
                        <div
                            className="flex w-full items-center justify-center text-lg text-primary"
                            key="close"
                            onClick={closeReachLimitModal}
                        >
                            我知道了
                        </div>,
                    ]}
                >
                    <p className="text-center text-base text-black opacity-90 ">已达到智能体创建上限（{appLimit}）</p>
                </ModalM>
            )}
            {auditFailedMsg && (
                <PopupM
                    visible={auditFailedReasonOpen}
                    showCloseButton
                    title="审核不通过原因"
                    bodyStyle={{minHeight: '25vh', paddingLeft: '17px', paddingRight: '17px'}}
                    onClose={closeAuditFailedReason}
                >
                    <div className="text-base text-gray-secondary">{auditFailedMsg}</div>
                </PopupM>
            )}
        </Space>
    );
}
