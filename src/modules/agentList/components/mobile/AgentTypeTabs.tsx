import {useCallback} from 'react';
import {Flex} from 'antd';
import classNames from 'classnames';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {showDisallowOperateToast} from '@/utils/tp/mobileToast';
import {AgentTabType} from '../../interface';
import {TabItemData} from '../../hooks/useAgentTypeTabs';

export default function AnalysisTypeTabs({
    tabItems,
    activeTab,
    switchTab,
}: {
    tabItems: TabItemData[];
    activeTab: AgentTabType | null;
    switchTab: (activeTab: AgentTabType) => void;
}) {
    const userInfoData = useUserInfoStore(state => state.userInfoData);
    const allowCreate = !userInfoData?.hasTpProxy;
    const checkAllowCreate = useCallback(
        (id: AgentTabType) => {
            return allowCreate || id !== AgentTabType.LowCode;
        },
        [allowCreate]
    );
    return (
        <Flex gap={4}>
            {tabItems.map(item => (
                <div
                    key={item.id}
                    className={classNames(
                        'flex h-[33px] items-center justify-center rounded-[9px] bg-white px-3 text-sm',
                        {
                            'font-medium text-primary': item.id === activeTab,
                            'active:bg-tagsHover active:text-primary':
                                checkAllowCreate(item.id) && item.id !== activeTab,
                            'text-black text-opacity-40': !checkAllowCreate(item.id),
                        }
                    )}
                    onClick={() => (checkAllowCreate(item.id) ? switchTab(item.id) : showDisallowOperateToast())}
                >
                    {item.description}
                </div>
            ))}
        </Flex>
    );
}
