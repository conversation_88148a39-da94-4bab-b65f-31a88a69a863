import {<PERSON>lex, Toolt<PERSON>} from 'antd';
import {ReactNode, useCallback} from 'react';
import {AgentType, AgentStatusLabel, SCOPE_STATUS_NAME, AgentListExtInfo} from '@/api/agentList/interface';
import {isMobileDevice} from '@/components/Login/utils/isMobileDevice';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {AgentData, AuditSign, AgentTagType} from '../interface';
import {CommonAuditingNotice, getCardExt, STATUS_ICON} from '../utils';

const isMobile = isMobileDevice();

const generateExtraTags = (agent: AgentData, extData?: AgentListExtInfo) => {
    // 移动端发布状态在标签中
    const extraTags: Array<string | ReactNode> = isMobile ? [STATUS_ICON[agent.labelStatus].text] : [];
    // 草稿状态不显示 '私有'/'公开'/'仅链接' 标签
    if (agent.agentType === AgentType.Codeless && agent.labelStatus !== AgentStatusLabel.DRAFTING && agent.permission) {
        extraTags.push(SCOPE_STATUS_NAME[agent.permission]);
    }

    if (agent.agentType === AgentType.LowCode) {
        extraTags.push('低代码');
    }

    if (extData?.conversationCount && extData?.conversationCount > 0) {
        extraTags.push(
            <Tooltip
                title={`${extData.qaCount || 0}个搜索问答待调优`}
                placement="top"
                overlayInnerStyle={{borderRadius: '12px', padding: '10px 12px'}}
                color="#1E1F24F2"
            >
                <span className="iconfont icon-star1 mr-0.5 text-[11px] font-normal leading-none text-primary"></span>
                问答已获流
            </Tooltip>
        );
    }

    // 教师招募标签
    if (agent.tags?.includes(AgentTagType.TEACHER_RECRUIT_AGENT)) {
        extraTags.push('教师招募活动');
    }
    return extraTags;
};

// agent 标签 className 属性
const AgentTagClassName = isMobile
    ? 'mr-[6px] mt-[6px] rounded-[3px] border-[1px] box-content h-[11px] border-solid px-[3px] py-[2px] text-[11px] leading-none bg-[#ffffff]'
    : 'mr-1 rounded-[6px] px-[6px] py-1 text-[12px] font-normal';

export default function AgentTags({agent, extData}: {agent: AgentData; extData?: AgentListExtInfo}) {
    const {showLog} = useUbcLogV2();
    const handleOpenChange = useCallback(
        (open: boolean) => {
            if (open) {
                showLog(EVENT_VALUE_CONST.AGENT_REVIEW_TAG, EVENT_PAGE_CONST.MY_AGENT, {
                    ...getCardExt(agent),
                    [EVENT_EXT_KEY_CONST.E_AGENT_REVIEW_STATUS]: agent.auditSign === AuditSign.FAIL ? 1 : 2,
                });
            }
        },
        [agent, showLog]
    );

    return (
        <>
            {agent.auditSign === AuditSign.AUDITING && (
                <Tooltip
                    arrow={false}
                    title={isMobile ? '' : CommonAuditingNotice}
                    placement="topLeft"
                    onOpenChange={handleOpenChange}
                >
                    <div className={'border-primary bg-[#ECEDFD] text-primary ' + AgentTagClassName}>
                        最新发布版本审核中
                    </div>
                </Tooltip>
            )}
            {agent.auditSign === AuditSign.FAIL && (
                <Tooltip
                    title={(agent.auditSign === AuditSign.FAIL && !isMobile && agent.message) || ''}
                    arrow={false}
                    placement="topLeft"
                    onOpenChange={handleOpenChange}
                >
                    <div className={'border-error bg-[#FDE9EA] text-error ' + AgentTagClassName}>
                        最新发布版本审核失败
                    </div>
                </Tooltip>
            )}
            {generateExtraTags(agent, extData).map((extraTag, index) => (
                <Flex
                    justify="center"
                    align="center"
                    className={'bg-gray-layout text-[#848691] ' + AgentTagClassName}
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                >
                    {extraTag}
                </Flex>
            ))}
        </>
    );
}
