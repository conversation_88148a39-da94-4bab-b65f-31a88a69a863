// 倒计时组件

import {useCallback, useEffect, useRef, useState} from 'react';

interface CountdownTime {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
}

interface CountdownProps {
    taskStartTime: string;
    taskEndTime: string;
}

const useCountdown = ({taskStartTime, taskEndTime}: CountdownProps): [CountdownTime, boolean] => {
    const [timeLeft, setTimeLeft] = useState<CountdownTime>({days: 0, hours: 0, minutes: 0, seconds: 0});
    const [isExpired, setIsExpired] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout>();

    const getTimeLeft = useCallback(() => {
        const now = Date.now();
        const startTime = Number(taskStartTime) * 1000;
        const endTime = Number(taskEndTime) * 1000;

        if (Number.isNaN(startTime) || Number.isNaN(endTime)) {
            return;
        }

        // 如果当前时间小于开始时间，计算距离开始的时间
        if (now < startTime) {
            const remaining = startTime - now;
            const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
            const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
            setTimeLeft({days, hours, minutes, seconds});
            setIsExpired(false);
        }
        // 如果当前时间在开始时间和结束时间之间，计算距离结束的时间
        else if (now >= startTime && now < endTime) {
            const remaining = endTime - now;
            const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
            const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
            setTimeLeft({days, hours, minutes, seconds});
            setIsExpired(false);
        }
        // 如果当前时间大于结束时间，设置过期状态
        else {
            setTimeLeft({days: 0, hours: 0, minutes: 0, seconds: 0});
            setIsExpired(true);
            clearInterval(intervalRef.current);
        }
    }, [taskStartTime, taskEndTime]);

    useEffect(() => {
        getTimeLeft();
        intervalRef.current = setInterval(() => {
            getTimeLeft();
        }, 1000);
        return () => clearInterval(intervalRef.current);
    }, [getTimeLeft]);

    return [timeLeft, isExpired];
};

export default useCountdown;
