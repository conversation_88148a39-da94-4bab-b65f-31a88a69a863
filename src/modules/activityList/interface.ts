/**
 * @file activityList 模块 interface
 * <AUTHOR>
 */
import {ActivityType, ActivityStatus} from '@/api/activityList/interface';

export const ACTIVITIES_STATUS_TAG_CONFIG = {
    [ActivityStatus.Draft]: {
        text: '',
        color: '',
        bgColor: '',
    },
    [ActivityStatus.NotStarted]: {
        text: '未开始',
        color: '#E87400',
        bgColor: '#FF82001A',
    },
    [ActivityStatus.InProgress]: {
        text: '进行中',
        color: '#3FC746',
        bgColor: '#3FC7461A',
    },
    [ActivityStatus.Ended]: {
        text: '评奖中',
        color: '#848691',
        bgColor: '#8486911A',
    },
    [ActivityStatus.Offline]: {
        text: '已结束',
        color: '#848691',
        bgColor: '#8486911A',
    },
};

// 活动类型名称映射
export const ACTIVITIES_TYPE_NAME = {
    [ActivityType.Training]: '训练营',
    [ActivityType.Theme]: '主题活动',
    [ActivityType.Inspiration]: '灵感中心',
};
