import {SubTaskInfo, eventCodeValue} from '@/api/task/interface';
import urls from '@/links';
import {makeSameQuery} from '@/modules/center/hooks/useAgentList';

// 跳转规则详情见 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MFBUKo27FA/b4k7WSkhjM/D70PtnVu9Nox4J#anchor-2c43236e-ea77-11ef-b584-e9b4be317b78
// eslint-disable-next-line complexity
export const getUrl = (data: SubTaskInfo) => {
    const {condition, eventCode} = data;
    switch (eventCode) {
        // 复制智能体且审核通过
        case eventCodeValue.COPY_AGENT_AND_AUDIT_SUCCESS:
            if (condition.appIdCondition.value) {
                return urls.agentPreview.fill({id: condition.appIdCondition.value});
            } else {
                return urls.center.raw() + `?${makeSameQuery}=true`;
            }

        // 新增创建且审核通过智能体
        // 新增创建且审核通过智能体数量
        // 新增创建智能体数量
        case eventCodeValue.CREATE_AGENT_AND_AUDIT_SUCCESS:
        case eventCodeValue.CREATE_AGENT_AND_AUDIT_SUCCESS_COUNT:
        case eventCodeValue.CREATE_AGENT_COUNT:
            return urls.agentPromptQuickStart.raw();

        //  复制智能体且审核通过数量
        case eventCodeValue.COPY_AGENT_AND_AUDIT_SUCCESS_COUNT:
            return urls.center.raw();

        // 挂载知识库并发布
        // 挂载链接并发布
        // 挂载插件并发布
        // 挂载商品并发布
        // 挂载线索转化功能并发布
        // 调优智能体并发布
        // 提交搜索问答并发布
        // 使用数字人分身
        case eventCodeValue.MOUNT_DATASET_AND_PUBLISH:
        case eventCodeValue.MOUNT_LINK_AND_PUBLISH:
        case eventCodeValue.MOUNT_PLUGIN_AND_PUBLISH:
        case eventCodeValue.MOUNT_PRODUCT_AND_PUBLISH:
        case eventCodeValue.MOUNT_XIANSUO_AND_PUBLISH:
        case eventCodeValue.TUNING_AGENT_AND_PUBLISH:
        case eventCodeValue.TUNING_Q2C_AND_PUBLISH:
        case eventCodeValue.USE_DYNAMIC_FIGURE_PUBLISH:
            return urls.agentList.raw();

        // 报名指定活动并成功提交
        case eventCodeValue.SUBMIT_ACTIVITY_SUCCESS:
            return condition.urlCondition.value;
    }
};
