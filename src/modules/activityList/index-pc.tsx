/**
 * @file 活动列表页入口组件 - PC 端
 * <AUTHOR>
 */
import {CacheProvider} from 'react-suspense-boundary';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import Loading from '@/components/Loading';
import ActivityAndTaskLayout from '@/modules/activityList/layout';
import {LotteryProvider} from '@/components/Prize';
export default function IndexPc() {
    return (
        <LogContextProvider page={EVENT_PAGE_CONST.ACTIVITY_CENTER}>
            <LotteryProvider>
                <CacheProvider>
                    <CommonErrorBoundary pendingFallback={<Loading />}>
                        <ActivityAndTaskLayout />
                    </CommonErrorBoundary>
                </CacheProvider>
            </LotteryProvider>
        </LogContextProvider>
    );
}
