/* eslint-disable camelcase */
import {ImgInfo} from '@/api/task/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
interface VideoListProps {
    data?: ImgInfo;
    taskPageId?: string;
    isLoading?: boolean;
}

export const VideoList = ({data, taskPageId}: VideoListProps) => {
    const {clickLog} = useUbcLogV3();

    const handleClick = (link: string) => {
        if (taskPageId) {
            clickLog(
                EVENT_VALUE_CONST.INNER_VIDEO_STRATEGY,
                {
                    eSourceUrl: link,
                    taskInfo: [
                        {
                            p_task_page_id: taskPageId,
                        },
                    ],
                },
                EVENT_PAGE_CONST.GROWTH_CENTER
            );
        }

        window.open(link, '_blank');
    };

    if (!data?.imgInfos?.length) {
        return null;
    }

    return (
        <div className="hide-scrollbar flex flex-col overflow-auto rounded-[18px] bg-white p-[15px]">
            <div className="text-lg font-semibold">{data?.title}</div>
            <div className="hide-scrollbar mt-4 flex-1 overflow-y-auto">
                {data?.imgInfos?.map((item, index, array) => (
                    <div key={item.title} className={index === array.length - 1 ? '' : 'mb-4'}>
                        <div
                            className="relative aspect-video w-[244px] cursor-pointer overflow-hidden rounded-[10px] bg-gray-100"
                            onClick={() => item.link && handleClick(item.link)}
                        >
                            <img src={item.imgUrl} className="h-full w-full object-cover" />
                        </div>
                        {item.title && <div className="mt-2 text-sm text-gray-700">{item.title}</div>}
                    </div>
                ))}
            </div>
        </div>
    );
};
