/**
 * @file 活动中心 - 用户信息
 * <AUTHOR>
 */
import classNames from 'classnames';
import {useNavigate} from 'react-router-dom';
import {Fragment, useCallback, useEffect, useRef, useState} from 'react';
import {Button, ConfigProvider} from 'antd';
import urls from '@/links';
import {buttonToken} from '@/styles/component-token';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import defaultAvatar from '@/assets/default-usert-avatar.png';
import api from '@/api/activityList';
import {ActivityStatisticsData} from '@/api/activityList/interface';

/**
 * 获取欢迎语和下一次切换欢迎语的时间
 */
function getWelcomeMessage() {
    const hour = new Date().getHours();
    // 创建一个新的日期对象，用于设置 setTimeout 时长，到达下一个时间后修改欢迎语
    const nextChangeTime = new Date();

    if (hour < 5) {
        nextChangeTime.setHours(5, 0, 0, 0);
        return {message: '深夜好！', nextChangeTime};
    } else if (hour < 8) {
        nextChangeTime.setHours(8, 0, 0, 0);
        return {message: '早晨好！', nextChangeTime};
    } else if (hour < 11) {
        nextChangeTime.setHours(11, 0, 0, 0);
        return {message: '上午好！', nextChangeTime};
    } else if (hour < 14) {
        nextChangeTime.setHours(14, 0, 0, 0);
        return {message: '中午好！', nextChangeTime};
    } else if (hour < 17) {
        nextChangeTime.setHours(17, 0, 0, 0);
        return {message: '下午好！', nextChangeTime};
    } else if (hour < 21) {
        nextChangeTime.setHours(21, 0, 0, 0);
        return {message: '傍晚好！', nextChangeTime};
    }

    nextChangeTime.setDate(nextChangeTime.getDate() + 1);
    nextChangeTime.setHours(0, 0, 0, 0);

    return {message: '晚上好！', nextChangeTime};
}

export function UserInfo({className, totalRecord = 0}: {className?: string; totalRecord?: number}) {
    const navigate = useNavigate();
    const [activityStatisticsData, setActivityStatisticsData] = useState<ActivityStatisticsData>();
    const {isLogin, userInfoData, setShowLoginModal} = useUserInfoStore(store => ({
        isLogin: store.isLogin,
        userInfoData: store.userInfoData,
        setShowLoginModal: store.setShowLoginModal,
    }));
    // 未登录状态的欢迎语
    const [welcomeMessage, setWelcomeMessage] = useState(getWelcomeMessage().message);
    // 每个时间段不同的欢迎语，该 timeoutRef 用于存储到下一个时间后切换欢迎语的 timeout
    const timeoutRef = useRef<NodeJS.Timeout>();

    // 登录状态跳转 “参与记录” 页
    const toActivityRecord = useCallback(() => {
        isLogin && navigate(urls.activityRecord.raw());
    }, [isLogin, navigate]);

    // 登录状态跳转 “任务记录” 页
    const toActivityTaskRecord = useCallback(() => {
        isLogin && navigate(urls.activityTaskRecord.raw());
    }, [isLogin, navigate]);

    // “登录” 点击
    const login = useCallback(() => {
        setShowLoginModal(true);
    }, [setShowLoginModal]);

    useEffect(() => {
        if (!isLogin) {
            return;
        }

        api.getActivityStatistics().then(res => {
            setActivityStatisticsData(res);
        });
    }, [isLogin]);

    useEffect(() => {
        // 只有未登录时启动 setTimeout
        if (!isLogin) {
            const {message, nextChangeTime} = getWelcomeMessage();
            timeoutRef.current = setTimeout(() => {
                setWelcomeMessage(message);
            }, nextChangeTime.getTime() - Date.now());
        }

        return () => {
            clearInterval(timeoutRef.current);
        };
    }, [isLogin, welcomeMessage]);

    return (
        <ConfigProvider theme={{components: {Button: buttonToken}}}>
            <div
                className={classNames(
                    'absolute right-8 top-[100px] flex h-[210px] w-[272px] flex-col items-center justify-center rounded-[21px] border-[1px] border-solid border-[#FFFFFF4D] bg-white/40 p-4 backdrop-blur-[40px]',
                    className
                )}
            >
                {isLogin ? (
                    <Fragment>
                        <img
                            className="mb-[12px] h-[72px] w-[72px] rounded-full"
                            src={userInfoData?.userInfo?.portrait || defaultAvatar}
                        />
                        <span className="mb-[24px] text-base font-medium leading-[22px] text-black">
                            {userInfoData?.userInfo?.name}
                        </span>
                        <div className="flex w-full flex-row justify-center gap-[42px]">
                            <div
                                className="flex h-[35px] cursor-pointer flex-col items-center justify-center rounded-full"
                                onClick={toActivityTaskRecord}
                            >
                                <span className="mr-[9px] text-lg font-medium text-primary">{totalRecord}</span>
                                <span className="mr-[3px] text-xs font-medium text-colorTextDefault">任务记录</span>
                            </div>
                            <div
                                className="flex h-[35px]  cursor-pointer flex-col items-center justify-center rounded-full"
                                onClick={toActivityRecord}
                            >
                                <span className="mr-[9px] text-lg font-medium text-primary">
                                    {activityStatisticsData?.participateInNum || 0}
                                </span>
                                <span className="mr-[3px] text-xs font-medium text-colorTextDefault">活动记录</span>
                            </div>
                        </div>
                    </Fragment>
                ) : (
                    <Fragment>
                        <span className="iconfont icon-prompt1 absolute right-3 top-[10px] text-lg text-white" />
                        <div className="mb-[18px] mt-[33px] text-2xl font-medium leading-[30px] text-black">
                            {welcomeMessage}
                        </div>
                        <div className="mb-9 text-base leading-[22px] text-[#272933]">欢迎来到活动中心</div>
                        <Button type="primary" className="h-[35px] w-full font-medium leading-none" onClick={login}>
                            登录
                        </Button>
                    </Fragment>
                )}
            </div>
        </ConfigProvider>
    );
}
