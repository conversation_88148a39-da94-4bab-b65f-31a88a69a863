/**
 * @file 活动列表页组件
 * <AUTHOR>
 */
import {useCallback, useEffect, useState, useRef} from 'react';
import {useSearchParams} from 'react-router-dom';
import {ConfigProvider, Pagination, Row, Col} from 'antd';
import {LogContextProvider} from '@/utils/loggerV2/context';
import Loading from '@/components/Loading';
import {LoadingSize} from '@/components/Loading/interface';
import {CategoryTabs} from '@/modules/center/components/Tags';
import Empty from '@/components/Empty';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import api from '@/api/activityList';
import {ActivityListData} from '@/api/activityList/interface';
import ActivityCard from '@/modules/activityList/components/pc/ActivityCard';
import {buttonToken} from '@/styles/component-token';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';

const DEFAULT_PAGINATION_SETTINGS = {
    pageNo: 1,
    pageSize: 24,
};

enum RequestStatus {
    Loading = 'loading',
    Success = 'success',
    Failed = 'failed',
}

// “全部” tab 的 id
const TabIdAll = '0';

/** 头部高度 = banner 高度 + tab 高度 */
const headerHeight = 420;

// eslint-disable-next-line complexity
export default function ActivityList() {
    const containerRef = useRef<HTMLDivElement | null>(null);

    const [searchParams, setSearchParams] = useSearchParams();
    // query 中的 tab 值，默认为 0（"全部"）
    const queryTab = searchParams.get('tab') || TabIdAll;
    // query 中的 pageNo 值，默认为 1
    const queryPageNo = searchParams.get('pageNo')
        ? Number(searchParams.get('pageNo'))
        : DEFAULT_PAGINATION_SETTINGS.pageNo;
    // tab 列表，从后端动态获取
    const [tabs, setTabs] = useState<Array<{id: string; description: string}>>([]);

    const [requestStatus, setRequestStatus] = useState(RequestStatus.Loading);
    const [requestError, setRequestError] = useState<any>();
    const [activityListData, setActivityListData] = useState<ActivityListData>();

    const {displayLog} = useUbcLogV3();

    /** Tab 切换后设置当前 url 中的 searchParams，pageNo 重置为 1 */
    const tabChange = useCallback(
        async (tab: string) => {
            setSearchParams({tab, pageNo: `${DEFAULT_PAGINATION_SETTINGS.pageNo}`}, {replace: true});
        },
        [setSearchParams]
    );

    /** 切换分页后设置当前 url 中的 searchParams，tab 保持当前值 */
    const handlePageNoChange = useCallback(
        (pageNo: number) => {
            setSearchParams(
                prev => {
                    const tab = prev.get('tab') || TabIdAll;
                    return {tab, pageNo: `${pageNo}`};
                },
                {replace: true}
            );
        },
        [setSearchParams]
    );

    // 请求列表
    const requestActivityList = useCallback(async () => {
        try {
            setRequestStatus(RequestStatus.Loading);

            const res = await api.getActivityList({
                // fixme: 等后端兼容后移除该表达式，直接传 Number(queryTab) 即可
                type: queryTab === '0' ? undefined : Number(queryTab),
                pageNo: queryPageNo,
                pageSize: DEFAULT_PAGINATION_SETTINGS.pageSize,
            });

            // 设置活动列表数据
            setActivityListData(res);

            // 动态设置 tabs 列表（“全部” tab 需要前端放在最前面）
            const tabs = [{id: TabIdAll, name: '全部'}, ...res.tabs].map(item => ({
                id: `${item.id}`,
                description: item.name,
            }));
            // 设置当前有数据的 tab 列表
            setTabs(tabs);

            const tab = tabs.find(item => item.id === queryTab);
            if (!tab) {
                tabChange(TabIdAll);
            }

            setRequestStatus(RequestStatus.Success);
        } catch (error: any) {
            // 错误码返回 30001 表示 tab 错误，需要切换到 “全部” tab
            if (error?.errno === 30001) {
                tabChange(TabIdAll);
                return;
            }

            setRequestStatus(RequestStatus.Failed);
            setRequestError(error);
            console.error(error);
        }
    }, [queryPageNo, queryTab, tabChange]);

    // 切换 tab、切换 page 后请求数据
    useEffect(() => {
        requestActivityList();
    }, [requestActivityList]);

    // displayLog 打点单独 useEffect 执行
    useEffect(() => {
        displayLog();
    }, [displayLog]);

    return (
        <div className="relative  font-pingfang">
            <div
                id="center-scroll-activity-container"
                ref={containerRef}
                className="w-full overflow-y-auto overflow-x-hidden"
            >
                {requestStatus === RequestStatus.Failed ? (
                    <RenderError error={requestError} />
                ) : (
                    <div className="[&>div:not(.banner,.title-container)]:max-w-[1384px] [&>div]:mx-auto [&_.title]:mx-auto [&_.title]:max-w-[1384px]">
                        {/* 列表tab */}
                        <div className="text-base font-semibold">有奖活动</div>

                        <div className="my-[17px] flex items-center justify-between  text-gray-tertiary">
                            <CategoryTabs tabs={tabs} activeTab={queryTab} switchTab={tabChange} />
                        </div>

                        {requestStatus === RequestStatus.Loading ? (
                            <div className="relative w-full" style={{height: `calc(100vh - ${headerHeight}px)`}}>
                                <Loading size={LoadingSize.centerSearch} />
                            </div>
                        ) : (
                            <>
                                {/* 列表区域 */}
                                <ConfigProvider
                                    theme={{
                                        token: {
                                            screenLG: 1280,
                                            screenLGMin: 1280,
                                            screenLGMax: 1280,
                                            screenXL: 1440,
                                            screenXLMin: 1440,
                                            screenXXL: 1680,
                                            screenXXLMin: 1680,
                                        },
                                        components: {Button: buttonToken},
                                    }}
                                >
                                    {activityListData && activityListData.activities?.length > 0 ? (
                                        <div className="w-full pb-8">
                                            {/*  列表 */}
                                            <Row gutter={[16, 16]} wrap>
                                                {activityListData.activities.map(item => (
                                                    <Col span={8} key={item.id}>
                                                        <LogContextProvider
                                                            ext={{
                                                                activityId: String(item.id),
                                                            }}
                                                        >
                                                            <ActivityCard key={item.id} activityListItem={item} />
                                                        </LogContextProvider>
                                                    </Col>
                                                ))}
                                            </Row>

                                            {/* 分页 */}
                                            {activityListData.total > DEFAULT_PAGINATION_SETTINGS.pageSize && (
                                                <div className="mt-6 flex justify-end">
                                                    <Pagination
                                                        className="w-fit"
                                                        current={
                                                            activityListData.pageNo ||
                                                            DEFAULT_PAGINATION_SETTINGS.pageNo
                                                        }
                                                        pageSize={
                                                            activityListData.pageSize ||
                                                            DEFAULT_PAGINATION_SETTINGS.pageSize
                                                        }
                                                        total={activityListData.total}
                                                        onChange={handlePageNoChange}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        // 空态
                                        <div
                                            className="relative w-full"
                                            style={{height: `calc(100vh - ${headerHeight}px)`}}
                                        >
                                            <Empty />
                                        </div>
                                    )}
                                </ConfigProvider>
                            </>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
