/* eslint-disable react/jsx-no-bind */
/* eslint-disable complexity */
/**
 * @file 任务列表滚动组件 - PC 端
 * <AUTHOR>
 */

import debounce from 'lodash/debounce';
import {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import classNames from 'classnames';
import {PkgInfo} from '@/api/task/interface';
import TaskItem from './taskItem';
import {TimeCountLeft, TimeCountRight} from './TimeCount';
interface SingleTaskScrollProps {
    isLoading?: boolean;
    direction?: 'row' | 'column';
    data?: PkgInfo;
    taskPageId?: string;
    index: number;
}

// 抽离滚动按钮组件
function ScrollButton({isLeft, onClick, index}: {isLeft: boolean; onClick: () => void; index: number}) {
    const background = useMemo(() => {
        if (index === 0) {
            return `linear-gradient(to ${isLeft ? 'right' : 'left'}, #F0F3FF, #F5EFFF00)`;
        }
        return `linear-gradient(to ${isLeft ? 'right' : 'left'}, #FFF9F1, #FFF9F100)`;
    }, [index, isLeft]);

    return (
        <>
            <div
                className={classNames(
                    'absolute top-1/2 h-full w-[26px] -translate-y-1/2',
                    isLeft ? 'left-0' : 'right-0'
                )}
                style={{
                    background,
                }}
            />
            <div
                className={classNames(
                    'iconfont',
                    isLeft ? 'icon-left' : 'icon-right',
                    'absolute top-1/2 -translate-y-1/2',
                    'h-[26px] w-[26px] rounded-full bg-white',
                    'flex items-center justify-center',
                    'text-[#333333]',
                    isLeft ? 'left-0' : 'right-0'
                )}
                style={{
                    filter: 'drop-shadow(0px 1.262px 7.574px rgba(0, 0, 0, 0.10))',
                }}
                onClick={onClick}
            />
        </>
    );
}

// 抽离任务标题组件
function TaskTitle({data, isColumn}: {data?: PkgInfo; isColumn: boolean}) {
    return (
        <div className="mb-1 flex justify-between">
            <div className={classNames('text-base font-medium', isColumn ? 'text-lg' : '')}>{data?.title}</div>
            {data?.showRemainTimeFlag === 1 &&
                data?.taskEndTime &&
                data?.taskStartTime &&
                (isColumn ? (
                    <TimeCountRight taskStartTime={data.taskStartTime} taskEndTime={data.taskEndTime} />
                ) : (
                    <TimeCountLeft taskStartTime={data.taskStartTime} taskEndTime={data.taskEndTime} />
                ))}
        </div>
    );
}

// 抽离计算高度函数
function calculateTaskHeight(isColumn: boolean, taskCount: number) {
    if (!isColumn) return 'auto';

    // 每个任务块高度约为100px，加上间距12px
    const taskHeight = 100;
    const gap = 12;

    // 如果任务块数量小于等于3，则显示所有任务块
    if (taskCount <= 3) {
        return `${taskCount * taskHeight + (taskCount - 1) * gap}px`;
    }

    // 如果任务块数量大于3，则显示3个任务块的高度
    return `${3 * taskHeight + 2 * gap}px`;
}

// 抽离任务内容组件
function TaskContent({
    scrollRef,
    isColumn,
    data,
    taskPageId,
    showCompleteFlag,
    showRewardFlag,
    isLeftButtonShow,
    isRightButtonShow,
    handleButtonClick,
    taskIndex,
}: {
    scrollRef: React.RefObject<HTMLDivElement>;
    isColumn: boolean;
    data?: PkgInfo;
    taskPageId?: string;
    showCompleteFlag?: 0 | 1;
    showRewardFlag?: 0 | 1;
    isLeftButtonShow: boolean;
    isRightButtonShow: boolean;
    handleButtonClick: (isLeft: boolean) => void;
    taskIndex: number;
}) {
    // 计算任务块数量
    const taskCount = data?.subTaskInfos?.length || 0;

    return (
        <div className="relative w-full">
            <div
                ref={scrollRef}
                className={classNames(
                    'hide-scrollbar w-full',
                    isColumn ? 'overflow-y-auto' : 'overflow-x-auto whitespace-nowrap'
                )}
                style={{
                    flexDirection: isColumn ? 'column' : 'row',
                    height: calculateTaskHeight(isColumn, taskCount),
                    display: 'flex',
                    minHeight: isColumn ? 'auto' : 'auto',
                    gap: isColumn ? '12px' : '1.5%',
                }}
            >
                {data?.subTaskInfos?.map((item, index) => (
                    <TaskItem
                        key={item.id}
                        isColumn={isColumn}
                        data={item}
                        taskPageId={taskPageId}
                        taskPkgId={data.id}
                        index={index}
                        showCompleteFlag={showCompleteFlag}
                        showRewardFlag={showRewardFlag}
                    />
                ))}
            </div>
            {!isColumn && (
                <>
                    {isLeftButtonShow && (
                        <ScrollButton isLeft onClick={() => handleButtonClick(true)} index={taskIndex} />
                    )}
                    {isRightButtonShow && (
                        <ScrollButton isLeft={false} onClick={() => handleButtonClick(false)} index={taskIndex} />
                    )}
                </>
            )}
        </div>
    );
}

// 单个任务组件
export function SingleTaskScroll({data, direction, taskPageId, index}: SingleTaskScrollProps) {
    const scrollRef = useRef<HTMLDivElement>(null);
    const [isLeftButtonShow, setIsLeftButtonShow] = useState(false);
    const [isRightButtonShow, setIsRightButtonShow] = useState(false);
    const isColumn = direction === 'column';

    // 滚动事件
    const handleScroll = debounce(() => {
        if (scrollRef.current) {
            setIsLeftButtonShow(scrollRef.current.scrollLeft > 0);
            setIsRightButtonShow(
                scrollRef.current.scrollLeft < scrollRef.current.scrollWidth - scrollRef.current.clientWidth - 1
            );
        }
    }, 100);

    // 滚动按钮点击事件
    const handleButtonClick = useCallback(
        (isLeft: boolean) => {
            if (scrollRef.current) {
                scrollRef.current.scrollTo({
                    left: scrollRef.current.scrollLeft + (isLeft ? -500 : 500),
                    behavior: 'smooth',
                });
            }
        },
        [scrollRef]
    );

    // 滚动事件监听
    useEffect(() => {
        const element = scrollRef.current;
        if (element && !isColumn) {
            handleScroll();
            element.addEventListener('scroll', handleScroll);
            return () => {
                element.removeEventListener('scroll', handleScroll);
                handleScroll.cancel();
            };
        }
    }, [handleScroll, isColumn, scrollRef]);

    // 背景颜色
    const backgroundColor = useMemo(() => {
        if (isColumn) {
            return 'white';
        }

        if (index === 0) {
            return 'linear-gradient(to bottom, #F2F3FF, #EFF3FF)';
        }

        return 'linear-gradient(to right, #FFF9F1, #FFF5EB)';
    }, [isColumn, index]);

    return (
        <div
            className="relative w-full overflow-hidden rounded-xl px-[15px] py-[15px]"
            style={{background: backgroundColor}}
        >
            <TaskTitle data={data} isColumn={isColumn} />
            <div className="mb-[9px] text-sm text-gray-tertiary">{data?.subTitle}</div>
            <TaskContent
                scrollRef={scrollRef}
                isColumn={isColumn}
                data={data}
                taskPageId={taskPageId}
                showCompleteFlag={data?.showCompleteFlag}
                showRewardFlag={data?.showRewardFlag}
                isLeftButtonShow={isLeftButtonShow}
                isRightButtonShow={isRightButtonShow}
                handleButtonClick={handleButtonClick}
                taskIndex={index}
            />
        </div>
    );
}

interface TaskScrollProps {
    data?: PkgInfo[];
    taskPageId?: string;
    isLoading?: boolean;
}
// 顶部任务列表滚动组件
export const TaskScroll = ({data, taskPageId, isLoading}: TaskScrollProps) => {
    return (
        data &&
        data.length > 0 && (
            <div className="mx-auto mb-[18px] max-w-[1384x] rounded-xl bg-white p-[24px]">
                <div className="mb-[18px]">
                    <span className="mb-1 text-lg font-semibold">成长激励</span>
                </div>
                <div className="flex flex-col gap-[19px]">
                    {data?.map((item, index) => (
                        <SingleTaskScroll
                            key={item.id}
                            data={item}
                            taskPageId={taskPageId}
                            isLoading={isLoading}
                            index={index}
                        />
                    ))}
                </div>
            </div>
        )
    );
};
