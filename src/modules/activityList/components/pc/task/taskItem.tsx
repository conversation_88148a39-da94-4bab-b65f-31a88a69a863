/* eslint-disable camelcase */
import {Tooltip} from 'antd';
import {useCallback, useMemo, useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {SubTaskInfo, SubTaskStatus} from '@/api/task/interface';
import api from '@/api/task';
import {useLottery} from '@/components/Prize/useLottery';
import urls from '@/links';
import {getUrl} from '@/modules/activityList/utils/getUrl';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {TaskCopy} from '@/utils/loggerV2/interface';

interface TaskItemProps {
    isColumn?: boolean;
    data?: SubTaskInfo;
    taskPageId?: string;
    taskPkgId?: string;
    index?: number;
    showCompleteFlag?: 1 | 0;
    showRewardFlag?: 1 | 0;
}

interface ProgressBarProps {
    progress: number;
    total: number;
    className?: string;
    showRewardFlag?: 1 | 0;
}

const TooltipContent = ({className}: {className?: string}) => {
    return (
        <Tooltip
            placement="top"
            title={`完成后抽奖一次`}
            rootClassName="[&_.ant-tooltip-content]:rounded-[14px] [&_.ant-tooltip-content]:overflow-hidden"
            overlayInnerStyle={{padding: '10px 12px'}}
        >
            <span className={`icon-gift iconfont mt-[1px] cursor-pointer ${className} text-sm leading-[14px]`}></span>
        </Tooltip>
    );
};

const ProgressBar = ({progress, total, showRewardFlag}: ProgressBarProps) => {
    const percent = useMemo(() => (progress / total) * 100, [progress, total]);

    return (
        <div className="mr-[18px] flex flex-1 items-center gap-2">
            {progress === 0 ? (
                '完成'
            ) : (
                <div className={`relative mr-[5px] max-w-[110px] flex-1 rounded-full bg-[#EDF2F7]`} style={{height: 8}}>
                    <div
                        className="absolute left-0 top-0 h-full rounded-full transition-all duration-300"
                        style={{
                            width: `${percent}%`,
                            background: 'linear-gradient(to right, #d5a2ff, #6acbff)',
                        }}
                    />
                </div>
            )}

            <div className=" whitespace-nowrap text-sm text-gray-tertiary">
                {progress}/{total}
            </div>
            {showRewardFlag === 1 && <TooltipContent />}
        </div>
    );
};

// eslint-disable-next-line complexity
export default function TaskItem({
    isColumn = false,
    data,
    taskPageId,
    taskPkgId,
    showCompleteFlag,
    showRewardFlag,
}: TaskItemProps) {
    const {openLottery} = useLottery();
    const navigate = useNavigate();
    const {clickLog, showLog} = useUbcLogV3();

    useEffect(() => {
        if (data && taskPageId) {
            showLog(
                EVENT_VALUE_CONST.TASK,
                {
                    taskInfo: [
                        {
                            p_task_page_id: taskPageId,
                            e_task_bag_id: taskPkgId,
                            e_task_progress: `${data.finishNum}/${data.totalNum}`,
                            e_task_id: data.id,
                        },
                    ],
                },
                EVENT_PAGE_CONST.GROWTH_CENTER
            );
        }
    }, [data, taskPageId, taskPkgId, showLog]);

    const goComplete = useCallback(() => {
        if (!taskPageId || !taskPkgId || !data?.id) {
            console.error('缺少必要参数', {taskPageId, taskPkgId, subTaskId: data?.id});
            return;
        }

        // 添加打点
        clickLog(
            EVENT_VALUE_CONST.TASK,
            {
                taskInfo: [
                    {
                        p_task_page_id: taskPageId,
                        e_task_bag_id: taskPkgId,
                        e_task_id: data.id,
                        e_task_copy: TaskCopy.complete,
                        e_task_progress: `${data.finishNum}/${data.totalNum}`,
                    },
                ],
            },
            EVENT_PAGE_CONST.GROWTH_CENTER
        );

        // 先获取跳转链接
        const url = getUrl(data);
        // 不关心接口是否返回，直接打开链接
        window.open(url, '_blank');

        if (!url) {
            console.error('无法获取任务跳转链接', {
                eventCode: data.eventCode,
                condition: data.condition,
                taskId: data.id,
            });
            return;
        }

        // 先调用任务开始 API
        api.taskStart({
            taskPageId,
            taskPkgId,
            subTaskId: data.id,
        });
    }, [data, taskPageId, taskPkgId, clickLog]);

    const goLottery = useCallback(() => {
        if (!taskPageId || !taskPkgId || !data?.id) {
            return;
        }

        // 添加打点
        clickLog(
            EVENT_VALUE_CONST.TASK,
            {
                taskInfo: [
                    {
                        p_task_page_id: taskPageId,
                        e_task_bag_id: taskPkgId,
                        e_task_id: data.id,
                        e_task_copy: TaskCopy.lottery,
                        e_task_progress: `${data.finishNum}/${data.totalNum}`,
                    },
                ],
            },
            EVENT_PAGE_CONST.GROWTH_CENTER
        );

        openLottery({taskPageId, taskPkgId, subTaskId: data?.id});
    }, [data?.id, data?.finishNum, data?.totalNum, openLottery, taskPageId, taskPkgId, clickLog]);

    // 任务记录点击
    const handleTaskRecord = useCallback(() => {
        if (!data) {
            return;
        }

        // 添加打点
        clickLog(
            EVENT_VALUE_CONST.TASK,
            {
                taskInfo: [
                    {
                        p_task_page_id: taskPageId,
                        e_task_bag_id: taskPkgId,
                        e_task_id: data.id,
                        e_task_copy: TaskCopy.task_record,
                        e_task_progress: `${data.finishNum}/${data.totalNum}`,
                    },
                ],
            },
            EVENT_PAGE_CONST.GROWTH_CENTER
        );
        navigate(urls.activityTaskRecord.raw());
    }, [clickLog, data, navigate, taskPageId, taskPkgId]);
    return (
        <div
            className="inline-block h-[100px] flex-col rounded-[10px] border-[1px] border-[#EDEEF0] bg-[#FFFFFF] px-[15px] py-3"
            style={{
                width: isColumn ? '100%' : '32%',
                flexShrink: 0,
            }}
        >
            <div className="flex h-full flex-col">
                <div className="flex-none">
                    <div className="text-black-base pb-[6px] text-sm font-medium">{data?.title}</div>
                    <div className="truncate pb-[10px] text-sm text-gray-tertiary">{data?.subTitle}</div>
                </div>

                <div className="mt-auto flex w-full items-center justify-between">
                    {/* 未完成时的左边区域 */}
                    {(data?.status === SubTaskStatus.IN_PROGRESS || data?.status === SubTaskStatus.NOT_BEGIN) && (
                        <div className="flex flex-1 text-sm text-gray-tertiary">
                            {showCompleteFlag === 1 &&
                                (isColumn ? (
                                    <>
                                        完成: {data?.finishNum}/{data?.totalNum}
                                        {showRewardFlag === 1 && data?.rewardFlag === 1 && (
                                            <TooltipContent className="ml-[5px] mt-[3px]" />
                                        )}
                                    </>
                                ) : (
                                    <ProgressBar
                                        progress={data?.finishNum ?? 0}
                                        total={data?.totalNum ?? 0}
                                        showRewardFlag={showRewardFlag}
                                    />
                                ))}
                            {showRewardFlag === 1 && data?.rewardFlag === 1 && showCompleteFlag === 0 && (
                                <span className="text-sm font-normal">
                                    完成后抽奖<span className="text-[#F62F00]">1</span>次
                                </span>
                            )}
                        </div>
                    )}
                    {data?.status === SubTaskStatus.WAIT_DRAW && (
                        <div className="flex flex-1 items-center">
                            <div className="inline-flex w-[39px] items-center rounded-[3px] bg-[#3FC7461A] p-[3px] text-[11px] font-medium text-[#3FC746]">
                                已完成
                            </div>
                        </div>
                    )}
                    {data?.status === SubTaskStatus.DRAWN && (
                        <div className="flex flex-1 items-center">
                            <div className="w-[39px] rounded-[3px] bg-colorBgFormList p-[3px] text-[11px] font-medium text-[#272933]">
                                {data?.rewardFlag === 1 ? '已抽奖' : '已完成'}
                            </div>
                        </div>
                    )}

                    {/* 操作区 */}
                    <div className="flex cursor-pointer items-center text-sm text-primary ">
                        {data?.status === SubTaskStatus.WAIT_DRAW && data?.rewardFlag === 1 && (
                            <div className="text-primary hover:text-primaryHover" onClick={goLottery}>
                                去抽奖
                            </div>
                        )}
                        {(data?.status === SubTaskStatus.IN_PROGRESS || data?.status === SubTaskStatus.NOT_BEGIN) && (
                            <div className="text-primary hover:text-primaryHover" onClick={goComplete}>
                                去完成
                            </div>
                        )}
                        {(data?.status === SubTaskStatus.DRAWN ||
                            (data?.status === SubTaskStatus.WAIT_DRAW && data?.rewardFlag === 0)) && (
                            <div className="text-primary hover:text-primaryHover" onClick={handleTaskRecord}>
                                任务记录
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
