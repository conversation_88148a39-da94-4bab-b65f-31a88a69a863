import {PkgInfo} from '@/api/task/interface';
import {SingleTaskScroll} from './TaskScroll';

interface RightTaskContainerProps {
    data?: PkgInfo[] | null;
    taskPageId?: string;
    isLoading?: boolean;
}

export const RightTaskContainer = ({data = [], taskPageId}: RightTaskContainerProps) => {
    return (
        data &&
        data.length > 0 && (
            <div className="flex flex-col gap-[19px]">
                {data?.map((item, index) => (
                    <SingleTaskScroll
                        key={item.id}
                        data={item}
                        taskPageId={taskPageId}
                        direction="column"
                        index={index}
                    />
                ))}
            </div>
        )
    );
};
