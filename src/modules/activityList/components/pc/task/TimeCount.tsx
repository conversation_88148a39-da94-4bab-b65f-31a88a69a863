import useCountDown from '@/modules/activityList/hooks/useCountdown';

interface TimeCountProps {
    taskStartTime: string;
    taskEndTime: string;
}

const TextItem = ({value}: {text: string; value: number}) => {
    return (
        <span className="mx-1 inline-block h-[18px] min-w-[18px] rounded bg-[#FF5A4F] px-[1px] text-center leading-[18px] text-white">
            {String(value).padStart(2, '0')}
        </span>
    );
};

// 左侧任务广场倒计时
export const TimeCountLeft = ({taskStartTime, taskEndTime}: TimeCountProps) => {
    const [timeLeft] = useCountDown({taskStartTime, taskEndTime});
    // 将天数转换为小时
    const totalHours = timeLeft.days * 24 + timeLeft.hours;

    return (
        <div className="text-xs">
            距结束还剩：
            <TextItem text="小时" value={totalHours} />
            :
            <TextItem text="分钟" value={timeLeft.minutes} />
            :
            <TextItem text="秒" value={timeLeft.seconds} />
        </div>
    );
};

// 右侧任务列表倒计时
export const TimeCountRight = ({taskStartTime, taskEndTime}: TimeCountProps) => {
    const [timeLeft] = useCountDown({taskStartTime, taskEndTime});
    // 将天数转换为小时
    const totalHours = timeLeft.days * 24 + timeLeft.hours;
    return (
        <div className="text-xs leading-7">
            任务仅剩：
            <span className="text-[#e24424]">{String(totalHours).padStart(2, '0')}</span>:
            <span className="text-[#e24424]">{String(timeLeft.minutes).padStart(2, '0')}</span>:
            <span className="text-[#e24424]">{String(timeLeft.seconds).padStart(2, '0')}</span>
        </div>
    );
};
