/**
 * @file 活动card组件
 * <AUTHOR>
 */

import {useCallback, useMemo, useRef} from 'react';
import {Card, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useNavigate} from 'react-router-dom';
import dayjs from 'dayjs';
import urls from '@/links';
import {DateFormat} from '@/utils/date';
import {convertToAutoFormat} from '@/utils/processImage';
import useTextOverflow from '@/modules/center/hooks/useTextOverflow';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {ActivityListItem, ActivityBadgeShow} from '@/api/activityList/interface';
import ActivityTags from '@/modules/activityList/components/ActivityTags';

const CardContainer = styled.div`
    .ant-card {
        box-shadow: none !important;
    }
`;

export default function ActivityCardPC({activityListItem}: {activityListItem: ActivityListItem}) {
    const navigate = useNavigate();

    const cardRef = useRef<HTMLDivElement>(null);
    const [descriptionRef, isDescriptionFlow] = useTextOverflow();

    const {clickLog} = useUbcLogV3();

    const topTags = useMemo(() => {
        return (
            <div className="font-['PingFang SC'] absolute left-[9px] top-[9px] text-[11px] font-medium text-white">
                {activityListItem?.showTop && activityListItem?.showTop === ActivityBadgeShow.Yes ? (
                    <span className="rounded-[3px] bg-primary py-[1.5px] pl-[2.5px] pr-[2.5px]">置顶</span>
                ) : null}

                {activityListItem?.showNew && activityListItem?.showNew === ActivityBadgeShow.Yes ? (
                    <span className="rounded-[3px] bg-[#FF301C] py-[1.5px] pl-[2.5px] pr-[2.5px]">新</span>
                ) : null}
            </div>
        );
    }, [activityListItem]);

    const handleToDetail = useCallback(() => {
        navigate(urls.activityDetail.fill({id: String(activityListItem.id)}));
        clickLog(EVENT_VALUE_CONST.ACTIVITY_CARD);
    }, [navigate, clickLog, activityListItem]);

    return (
        <CardContainer>
            <Card
                ref={cardRef}
                title={
                    <div className="relative aspect-[16/9] h-full w-full overflow-hidden">
                        <img
                            alt="example"
                            src={convertToAutoFormat(activityListItem.coverImg)}
                            className="object-fit h-full w-full rounded-[9px] object-cover"
                        />
                        {topTags}
                    </div>
                }
                headStyle={{
                    justifyContent: 'start',
                    borderBottom: 0,
                    padding: 16,
                    cursor: 'pointer',
                }}
                bodyStyle={{
                    paddingBottom: 16,
                    paddingTop: 0,
                    paddingLeft: 16,
                    paddingRight: 16,
                    height: 'auto',
                    overflow: 'hidden',
                }}
                bordered={false}
                onClick={handleToDetail}
            >
                <div className="font-['PingFang SC'] box-border cursor-pointer">
                    <div className="truncate text-base font-medium leading-[22px] text-black">
                        {activityListItem.title}
                    </div>

                    <Tooltip
                        placement="top"
                        title={isDescriptionFlow ? activityListItem.subTitle : ''}
                        autoAdjustOverflow={false}
                        overlayStyle={{
                            maxWidth: '250px',
                        }}
                    >
                        <div
                            className="my-1 truncate text-sm font-normal leading-[24px] text-gray-secondary"
                            ref={descriptionRef}
                        >
                            {activityListItem.subTitle}
                        </div>
                    </Tooltip>

                    <div className="flex items-center justify-between">
                        <div className="flex flex-1">
                            <ActivityTags activityData={activityListItem} />
                        </div>
                        <div className="flex items-center text-xs font-normal leading-5 text-gray-tertiary">
                            {activityListItem?.endTime &&
                                activityListItem?.endTime !== 0 &&
                                `截止时间：${dayjs(activityListItem.endTime * 1000).format(DateFormat.YMD_DOT)}`}
                        </div>
                    </div>
                </div>
            </Card>
        </CardContainer>
    );
}
