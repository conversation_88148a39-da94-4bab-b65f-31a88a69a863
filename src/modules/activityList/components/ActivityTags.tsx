/**
 * @file 活动中心模块标签
 * <AUTHOR>
 */
import {Tag} from 'antd';
import {Fragment, ReactNode} from 'react';
import classNames from 'classnames';
import {ActivityStatus, ActivityType} from '@/api/activityList/interface';
import {ACTIVITIES_STATUS_TAG_CONFIG, ACTIVITIES_TYPE_NAME} from '@/modules/activityList/interface';

function ActivityTag({
    className,
    color,
    bgColor,
    children,
}: {
    className?: string;
    // 文字颜色
    color?: string;
    // 背景色
    bgColor?: string;
    // 内容
    children?: ReactNode;
}) {
    return (
        <Tag
            color={bgColor}
            className={classNames(
                'inline-flex items-center rounded-[3px] border-none px-[3px] py-[2px] text-[0.75rem] font-medium leading-[0.75rem]',
                `text-[${color}]`,
                className
            )}
            bordered={false}
        >
            {children}
        </Tag>
    );
}

export default function ActivityTags({activityData}: {activityData?: {status: ActivityStatus; type: ActivityType}}) {
    if (!activityData) {
        return null;
    }
    return (
        <Fragment>
            {!!ACTIVITIES_STATUS_TAG_CONFIG[activityData.status]?.text && (
                <ActivityTag
                    color={ACTIVITIES_STATUS_TAG_CONFIG[activityData.status].color}
                    bgColor={ACTIVITIES_STATUS_TAG_CONFIG[activityData.status].bgColor}
                >
                    {ACTIVITIES_STATUS_TAG_CONFIG[activityData.status].text}
                </ActivityTag>
            )}
            {!!ACTIVITIES_TYPE_NAME[activityData.type] && (
                <ActivityTag color="#272933" bgColor="#F5F6FA">
                    {ACTIVITIES_TYPE_NAME[activityData.type]}
                </ActivityTag>
            )}
        </Fragment>
    );
}
