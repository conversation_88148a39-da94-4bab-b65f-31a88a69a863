/* eslint-disable camelcase */

import useSWR from 'swr';
import {useEffect} from 'react';
import {GET_TASK_LIST_API, getUserTaskPageInfo} from '@/api/task';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {useLottery} from '@/components/Prize/useLottery';
import ActivityList from './components/pc/ActivityList';
import {UserInfo} from './components/pc/UserInfo';
import {TaskScroll} from './components/pc/task/TaskScroll';
import {VideoList} from './components/pc/VideoList';
import ActivityBanner from './components/pc/Banner';
import {RightTaskContainer} from './components/pc/task/RightTaskContainer';
import {LoginContainer} from './components/pc/task/LoginContainer';

// eslint-disable-next-line complexity
export default function ActivityAndTaskLayout() {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const {data, isLoading, mutate} = useSWR(isLogin ? GET_TASK_LIST_API : null, getUserTaskPageInfo);
    const {topPkgInfos, rightPkgInfos, rightImgInfo} = data || {};

    const {displayLog} = useUbcLogV3();
    const {setRefreshTaskCenter} = useLottery();

    useEffect(() => {
        if (data) {
            displayLog(EVENT_PAGE_CONST.GROWTH_CENTER, {
                taskInfo: [
                    {
                        p_task_page_id: data.id,
                    },
                ],
            });
        }

        if (setRefreshTaskCenter) {
            setRefreshTaskCenter(() => {
                mutate();
            });
        }
    }, [data, displayLog, mutate, setRefreshTaskCenter]);
    return (
        <div className="relative flex w-full min-w-[1040px] flex-col gap-[15px]">
            <div className="w-full">
                <ActivityBanner />
                <UserInfo totalRecord={data?.totalRecord} />
            </div>

            <div className="flex w-full flex-row gap-[18px] px-8">
                <div className="min-w-0 flex-1 overflow-hidden">
                    {topPkgInfos && topPkgInfos.length > 0 && (
                        <TaskScroll data={topPkgInfos} taskPageId={data?.id} isLoading={isLoading} />
                    )}
                    <ActivityList />
                </div>

                {isLogin &&
                    ((rightPkgInfos && rightPkgInfos.length > 0) ||
                        (rightImgInfo && rightImgInfo?.imgInfos && rightImgInfo?.imgInfos?.length > 0)) && (
                        <div className="flex w-[272px] shrink-0 flex-col gap-[15px]">
                            <RightTaskContainer data={rightPkgInfos} taskPageId={data?.id} isLoading={isLoading} />
                            <VideoList data={data?.rightImgInfo} taskPageId={data?.id} isLoading={isLoading} />
                        </div>
                    )}

                {!isLogin && (
                    <div className="flex w-[272px] shrink-0 flex-col gap-[15px]">
                        <LoginContainer />
                    </div>
                )}
            </div>
        </div>
    );
}
