/**
 * @file 数据详情页
 * <AUTHOR>
 */
import {useNavigate, useParams} from 'react-router-dom';
import {useCallback, useEffect, useRef} from 'react';
import throttle from 'lodash/throttle';
import TopBar from '@/components/mobile/TopBar';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import LoadError from '@/components/Loading/LoadError';
import AnalysisPanel from './components/mobile/AnalysisPanel';

function AgentAnalysis() {
    // 获取动态路由参数
    const {id: appId} = useParams();
    const navigate = useNavigate();
    const {displayLog} = useUbcLogV3();

    useEffect(() => {
        displayLog();
    }, [displayLog]);

    const handleBack = useCallback(() => {
        navigate(-1);
    }, [navigate]);

    const containerRef = useRef<HTMLDivElement>(null);

    const handleScroll = throttle(() => {
        // 页面滚动时隐藏所有popover
        // 使用 antd-mobile popover组件也是各种问题，先只能这样处理了
        const domList = document.getElementsByClassName('ant-popover');
        Array.prototype.forEach.call(domList, dom => {
            if (dom) {
                dom.classList.add('ant-popover-hidden');
            }
        });
    }, 500);

    return (
        <div className="h-full overflow-y-auto bg-gray-bg-base" onScroll={handleScroll}>
            <TopBar
                title="数据分析"
                leftNode={
                    <span
                        className="iconfont icon-left cursor-pointer text-xl hover:text-primary"
                        onClick={handleBack}
                    />
                }
            />
            <div ref={containerRef} className="px-3 pb-2 pt-1.5">
                <AnalysisPanel appId={appId!} containerRef={containerRef} />
            </div>
        </div>
    );
}

export default function AgentAnalysisM() {
    const loadError = useCallback(() => <LoadError />, []);
    // 获取动态路由参数
    const {id: agentId} = useParams();

    return (
        <CommonErrorBoundary renderError={loadError}>
            <LogContextProvider page={EVENT_PAGE_CONST.AGENT_ANALYSIS} ext={{agentId}}>
                <AgentAnalysis />
            </LogContextProvider>
        </CommonErrorBoundary>
    );
}
