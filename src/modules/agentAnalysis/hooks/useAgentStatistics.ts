/**
 * @file 数据分析PC & Wise 获取图表、表格数据等 hooks
 * <AUTHOR>
 */

import {useCallback, useEffect, useRef, useState} from 'react';
import {DefaultOptionType} from 'antd/es/cascader';
import {RangePickerProps} from 'antd/es/date-picker';
import dayjs from 'dayjs';
import isString from 'lodash/isString';
import throttle from 'lodash/throttle';
import cloneDeep from 'lodash/cloneDeep';
import {LineSeriesOption, XAXisOption, YAXisOption} from 'echarts/types/dist/shared';
import api from '@/api/agentAnalysis';
import {
    AgentLineChartRenderDataItem,
    AgentLineChartRenderData,
    AllIndicatorsData,
    AnalysisTabType,
    GoodsLineChartData,
    IndicatorCnNameMap,
    IndicatorKeyEnum,
    StatisticsDetailData,
} from '@/api/agentAnalysis/interface';
import {convertedDate, DateFormat, getDateList} from '@/utils/date';
import echarts, {ECOption} from '@/utils/echarts';
import {
    IndicatorRadioOptions,
    SourceOptionAll,
    SourceSelectValue,
    AgentLineChartLineStyle,
} from '../components/pc/DataPanel/interface';
import {formatSourceOption, splitUnitAndNumber} from '../utils';
import {PAGE_SIZE, lineOption, tooltipFormatter} from '../components/pc/interface';
import {AgentTableColumns} from '../components/pc/DataPanel/config';
import {
    lineOption as WiseLineOption,
    AgentLineChartLineStyle as WiseLineChartLineStyle,
} from '../components/mobile/interface';

// 容器的水平方向的 padding 值，用于计算图表宽度
const containerPaddingX = 24;

export default function useAgentStatistics({
    appId,
    dateRangeValue,
    activeTab,
    sourceValue,
    containerRef,
    indicatorRadioKey,
    setIndicatorRadioKey,
    hasTable = true,
    isMobile,
}: {
    appId: string;
    dateRangeValue: RangePickerProps['value'];
    activeTab: AnalysisTabType;
    sourceValue: SourceSelectValue;
    containerRef: any;
    indicatorRadioKey: IndicatorKeyEnum;
    setIndicatorRadioKey: (indicatorKey: IndicatorKeyEnum) => void;
    hasTable?: boolean;
    isMobile?: boolean;
}) {
    const [lineChartLoading, setLineChartLoading] = useState(false);
    const [tableLoading, setTableLoading] = useState(false);
    // 图表数据
    const [lineChartData, setLineChartData] = useState<AgentLineChartRenderData>({dateList: []});
    // 线图实列
    const lineChartRef = useRef<HTMLDivElement>(null);
    // 线图实列
    const lineChartInstance = useRef<echarts.ECharts>();
    // 表格数据
    const [tableData, setTableData] = useState<StatisticsDetailData>({
        dataList: [],
        total: 0,
        pageNo: 1,
        pageSize: PAGE_SIZE,
    });

    /** 缓存数据来源渠道列表数据 */
    const [generalSourceOptions, setGeneralSourceOption] = useState<DefaultOptionType[]>([SourceOptionAll]);

    // 初始化lineChart option
    const initLineChartOption = useCallback(
        (dateList: any[], data: AgentLineChartRenderDataItem, name: string) => {
            const lineChartOption = cloneDeep(isMobile ? WiseLineOption : lineOption);
            // 设置 xAxis 数据
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
            let xAxisData: XAXisOption = (lineChartOption.xAxis as XAXisOption[])?.[0]!;
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
            let yAxisData: YAXisOption = (lineChartOption.yAxis as YAXisOption[])?.[0]!;
            const tooltipData = lineChartOption.tooltip;
            xAxisData = {
                ...xAxisData,
                // 移动端横坐标展示日期为MM/DD，PC为YYYY/MM/DD
                data: isMobile ? dateList.map(item => convertedDate(item, DateFormat.MD)) : dateList,
            } as XAXisOption;
            // y 坐标刻度值上拼接 label
            yAxisData = {
                ...yAxisData,
                axisLabel: {...yAxisData.axisLabel, formatter: `{value}${data.unit}`},
            } as YAXisOption;
            lineChartOption.xAxis = [xAxisData];
            lineChartOption.yAxis = [yAxisData];
            lineChartOption.tooltip = {
                ...tooltipData,
                formatter: (params: any) => {
                    params[0].unit = data.unit;

                    return tooltipFormatter(params);
                },
            };

            // 根据当前指标类型，设置 series 数据
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
            let seriesData: LineSeriesOption = (lineChartOption.series as LineSeriesOption[])?.[0]!;
            seriesData = {
                ...seriesData,
                data: data.data,
                name,
                ...(isMobile ? WiseLineChartLineStyle : AgentLineChartLineStyle),
            } as LineSeriesOption;
            lineChartOption.series = [seriesData];

            return lineChartOption;
        },
        [isMobile]
    );

    // 初始化lineChart
    const initChart = useCallback((chartWrap: HTMLElement | null, option: ECOption) => {
        const chart = chartWrap && echarts.init(chartWrap);
        chart?.setOption(option);
        return chart!;
    }, []);

    // 查询图表数据(用户分析、流量分析、对话分析、行为分析)
    // 查询图表数据(商品分析)
    // TODO: 该函数内容太多，不好理解，拆分函数
    const getLineChartData = useCallback(
        (
            activeTab: AnalysisTabType,
            source: SourceSelectValue,
            currIndicatorRadioKey: IndicatorKeyEnum,
            dateValue?: RangePickerProps['value']
        ) => {
            if (!appId) {
                return;
            }

            setLineChartLoading(true);

            const startTime = dateValue?.[0]?.unix()! || dateRangeValue?.[0]?.unix()!;
            const endTime = dateValue?.[1]?.unix()! || dateRangeValue?.[1]?.unix()!;

            const fetchLineChartData = () => {
                if (activeTab === AnalysisTabType.Goods) {
                    return api.getGoodsChartData({
                        appId,
                        startTime,
                        endTime,
                        source: source?.[0] ? `${source[0]}` : undefined,
                    });
                }

                return api.getStatisticsChartData({
                    appId,
                    startTime,
                    endTime,
                    firstSource: source?.[0] ? `${source[0]}` : undefined,
                    secondSource: source?.[1] ? `${source[1]}` : undefined,
                });
            };

            fetchLineChartData()
                .then((res: Array<AllIndicatorsData | GoodsLineChartData>) => {
                    if (!lineChartRef.current) {
                        return;
                    }

                    // 初始化图表
                    const dateList: any = [];
                    const chartData: Record<IndicatorKeyEnum, AgentLineChartRenderDataItem> = {} as Record<
                        IndicatorKeyEnum,
                        AgentLineChartRenderDataItem
                    >;

                    // 返回空数据时，图标区域显示 "暂无数据"
                    if (res?.length) {
                        // 获取当前选择的区间内的日期列表，用于设置 echarts x 坐标，由于后端返回的数据中可能缺少没有数据的日期，会导致图表 x 坐标日期不连续
                        const fullDateList = getDateList(
                            dayjs(startTime * 1000)
                                .subtract(1, 'day')
                                .format(DateFormat.YMD),
                            dayjs(endTime * 1000).format(DateFormat.YMD)
                        );

                        const tabsList =
                            activeTab === AnalysisTabType.Goods
                                ? [AnalysisTabType.Goods]
                                : [
                                      AnalysisTabType.User,
                                      AnalysisTabType.Conversation,
                                      AnalysisTabType.Traffic,
                                      AnalysisTabType.Action,
                                  ];

                        fullDateList.forEach((item: string) => {
                            const data = res.find(data => data.date === item);

                            tabsList.forEach(tabKey => {
                                IndicatorRadioOptions[tabKey].forEach(radioOption => {
                                    // 数据指标key
                                    const radioOptionValue = radioOption.value;

                                    // 数据指标对应数据value
                                    const value =
                                        data?.[
                                            radioOptionValue as unknown as keyof (
                                                | AllIndicatorsData
                                                | GoodsLineChartData
                                            )
                                        ] || 0;

                                    if (!chartData[radioOptionValue]) {
                                        chartData[radioOptionValue] = {
                                            data: [],
                                            unit: '',
                                        };
                                    }

                                    // 如果数据value是string，拆分数据值和单位（比如百分比）
                                    if (isString(value)) {
                                        const {value: numValue, unit} = splitUnitAndNumber(value);

                                        chartData[radioOptionValue].data.push(numValue);
                                        // 防止出现空数据导致单位被覆盖
                                        chartData[radioOptionValue].unit = chartData[radioOptionValue].unit || unit;
                                    } else {
                                        chartData[radioOptionValue].data.push(value);
                                    }
                                });
                            });
                            dateList.push(item);
                        });
                    }

                    // 缓存数据
                    setLineChartData({
                        dateList,
                        ...chartData,
                    });

                    // 如果有当前选择的 数据指标，保留不动，否则默认选中第一个
                    const curIndicatorRadioKey = currIndicatorRadioKey || IndicatorRadioOptions[activeTab][0].value;

                    // 初始化图表
                    const name = IndicatorCnNameMap[curIndicatorRadioKey];
                    const chartOption = initLineChartOption(
                        dateList,
                        chartData[curIndicatorRadioKey] || ({} as AgentLineChartRenderDataItem),
                        name
                    );
                    const lineChartDom = lineChartRef.current;
                    const chart = lineChartInstance.current || initChart(lineChartDom, chartOption);
                    chart.clear();
                    chart.setOption(chartOption);
                    const lineChartWidth = containerRef.current!.clientWidth - containerPaddingX * 2;
                    chart.resize({
                        width: lineChartWidth,
                    });

                    lineChartInstance.current = chart;
                })
                .catch(e => {
                    setLineChartData({dateList: []});
                    throw e;
                })
                .finally(() => {
                    setLineChartLoading(false);
                });
        },
        [appId, dateRangeValue, initLineChartOption, initChart, containerRef]
    );

    // 查询表格数据(用户分析、流量分析、对话分析、行为分析)
    const getTableData = useCallback(
        (
            activeTab: AnalysisTabType,
            source: SourceSelectValue,
            page: {pageNo: number; pageSize: number},
            dateValue?: RangePickerProps['value']
        ) => {
            if (!appId) {
                return;
            }

            if (!AgentTableColumns[activeTab] || AgentTableColumns[activeTab].length === 0) {
                return;
            }

            setTableData({
                dataList: [],
                total: 0,
                pageNo: 1,
                pageSize: PAGE_SIZE,
            });

            setTableLoading(true);

            api.getStatisticsDetailData({
                ...page,
                appId,
                startTime: dateValue?.[0]?.unix()! || dateRangeValue?.[0]?.unix()!,
                endTime: dateValue?.[1]?.unix()! || dateRangeValue?.[1]?.unix()!,
                firstSource: source?.[0] ? `${source[0]}` : undefined,
                secondSource: source?.[1] ? `${source[1]}` : undefined,
            })
                .then(res => {
                    // 接口失败情况下不改其初始值
                    setTableData(
                        res || {
                            dataList: [],
                            total: 0,
                            pageNo: 1,
                            pageSize: PAGE_SIZE,
                        }
                    );
                })
                .finally(() => {
                    setTableLoading(false);
                });
        },
        [appId, dateRangeValue]
    );

    const getAnalysisData = useCallback(
        (dateValue?: RangePickerProps['value']) => {
            const currIndicatorRadioKey = indicatorRadioKey || IndicatorKeyEnum.Rounds;

            // 更新指标选择器状态
            setIndicatorRadioKey(currIndicatorRadioKey);

            // 查询图表数据
            getLineChartData(activeTab, sourceValue, currIndicatorRadioKey, dateValue);

            if (hasTable) {
                // 查询表格数据
                getTableData(
                    activeTab,
                    sourceValue,
                    {
                        pageNo: 1,
                        pageSize: PAGE_SIZE,
                    },
                    dateValue
                );
            }
        },
        [indicatorRadioKey, setIndicatorRadioKey, getLineChartData, activeTab, sourceValue, hasTable, getTableData]
    );

    // 获取渠道列表数据
    const getGeneralStatisticsSource = useCallback(() => {
        if (!appId) {
            return;
        }

        api.getStatisticsSource({appId}).then(res => {
            setGeneralSourceOption(formatSourceOption(res));
        });
    }, [appId]);

    // 根据数据指标刷新lineChart
    const renderChartByIndicatorKey = useCallback(
        (indicatorKey: IndicatorKeyEnum) => {
            const name = IndicatorCnNameMap[indicatorKey] || '';

            const chartOption = initLineChartOption(
                lineChartData.dateList || [],
                lineChartData[indicatorKey] || ({} as AgentLineChartRenderDataItem),
                name
            );

            lineChartInstance.current?.clear();
            lineChartInstance.current?.setOption(chartOption);
        },
        [initLineChartOption, lineChartData]
    );

    // 初始化:获取整体数据
    useEffect(() => {
        // 根据智能体 id 获取图表和表格数据
        getAnalysisData();
        // 获取来源渠道数据
        getGeneralStatisticsSource();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 处理图表样式 resize
    useEffect(() => {
        const handleResize = throttle(() => {
            const lineChartWidth = containerRef.current!.clientWidth - containerPaddingX * 2;

            lineChartInstance.current?.resize({
                width: lineChartWidth,
            });
        }, 500);
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [containerRef, lineChartInstance]);

    return {
        /** 数据来源渠道列表数据 */
        generalSourceOptions,
        /** 折线图图表容器Ref */
        lineChartRef,
        /** 折线图图表loading */
        lineChartLoading,
        /** 折线图图表render数据 */
        lineChartData,
        /** 获取折线图图表render数据 */
        getLineChartData,
        /** 表格详情loading */
        tableLoading,
        /** 表格详情数据 */
        tableData,
        /** 获取表格详情数据 */
        getTableData,
        /** 获取图表和表格详情数据（初始化、时间范围change、来源渠道change时调用） */
        getAnalysisData,
        /** 根据数据指标刷线lineChart（不请求数据） */
        renderChartByIndicatorKey,
    };
}
