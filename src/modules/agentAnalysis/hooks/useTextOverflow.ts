/**
 * @file 检测文本是否溢出的 Hook
 */

import {useRef, useEffect, useState} from 'react';

/**
 * 检测文本是否溢出的 Hook
 * @param text 文本内容
 * @param isMultiLine 是否为多行文本，默认为 false
 * @returns {ref, isOverflow} ref: 元素引用，isOverflow: 是否溢出
 */
export const useTextOverflow = (text: string, isMultiLine = false) => {
    const ref = useRef<HTMLSpanElement>(null);
    const [isOverflow, setIsOverflow] = useState(false);

    useEffect(() => {
        const element = ref.current;
        if (element) {
            if (isMultiLine) {
                // 多行文本检测：scrollHeight > clientHeight 表示内容超出了容器高度
                setIsOverflow(element.scrollHeight > element.clientHeight);
            } else {
                // 单行文本检测：scrollWidth > clientWidth 表示内容超出了容器宽度
                setIsOverflow(element.scrollWidth > element.clientWidth);
            }
        }
    }, [text, isMultiLine]);

    return {ref, isOverflow};
};
