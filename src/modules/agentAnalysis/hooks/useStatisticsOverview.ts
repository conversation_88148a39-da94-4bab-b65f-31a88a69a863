/**
 * @file 获取概览数据hooks
 * <AUTHOR>
 */

import {useCallback, useEffect, useState} from 'react';
import api from '@/api/agentAnalysis';
import {numFormatForDataAnalysis} from '@/utils/text';
import {StatisticsOverviewData} from '@/api/agentAnalysis/interface';
import {DefaultOverviewCards, OverviewCardInfo, TrendType} from '../interface';

/**
 * 按规则format智能体排名文案
 * @param value 累计值，如累计对话数、累计曝光次数、对话满意度等
 * @param isValuePercent 是否累计值是百分比值，如对话满意度
 * @param rankValue 排名值名次，如累计对话数排名名次
 * @param totalAgent 参与排名总智能体数
 * @returns {string} 文案描述
 */

type NumType = string | number | undefined | null;

// eslint-disable-next-line complexity
const getRankText = (value: NumType, isValuePercent: boolean, rankValue: NumType, totalAgent: number) => {
    let res = '暂未参与智能体排名';

    // 没有累计值 ｜没有排名值 ｜ 没有总智能体数，展示未参与智能体排名
    if (!value || !rankValue || !totalAgent || isNaN(+value) || isNaN(+rankValue)) {
        return res;
    }

    // 累计值(除了百分比类型)小于100，展示未参与智能体排名文案
    if (!isValuePercent && +value < 100) {
        return res;
    }

    const numRankValue = +rankValue;

    // 排名值小于总智能体数，小于100，展示排名第rankValue名，否则展示已超过百分比智能体
    if (numRankValue <= totalAgent && numRankValue < 100) {
        res = `🎉智能体排名第${numRankValue}名`;
    } else {
        const rankPercent = Math.round((numRankValue / totalAgent) * 100);

        // 排名前20%，展示已超过百分比，否则展示已超过20%的智能体
        if (rankPercent <= 20) {
            res = `🎉已超过${Math.min(99, 100 - rankPercent)}%的智能体`;
        } else {
            res = `🎉已超过20%的智能体`;
        }
    }

    return res;
};

export default function useGetOverviewData(appId: string) {
    // 概览卡片
    const [overviewCardData, setOverviewCardData] = useState<OverviewCardInfo[]>(DefaultOverviewCards);

    // 获取概览数据
    const getOverviewData = useCallback(async () => {
        if (!appId) {
            return;
        }

        try {
            const res = await api.getStatisticsOverviewData({appId});

            if (res) {
                const overviewData = DefaultOverviewCards.map(item => {
                    const value = res[item.valueKey as keyof StatisticsOverviewData];
                    let formatValue = value;

                    if (typeof value === 'string' && !/^\d+$/.test(value) && isNaN(parseFloat(value)) === false) {
                        // 兼容后端返回百分比字符串，如'10.23%'，转为数字显示, 单位由前端配置
                        formatValue = `${parseFloat(value)}`;
                    } else if (typeof value === 'number') {
                        formatValue = numFormatForDataAnalysis(value);
                    } else {
                        formatValue = `${value}`;
                    }

                    let trendValue = `${res[item.trendValueKey as keyof StatisticsOverviewData]}`;
                    const trendValueNum = +trendValue.replace('%', '');
                    let trend = TrendType.Same;

                    if (trendValueNum > 0) {
                        trend = TrendType.Up;
                        trendValue = `+${trendValue}`;
                    } else if (trendValueNum < 0) {
                        trend = TrendType.Down;
                    }

                    return {
                        ...item,
                        value: formatValue,
                        trendValue,
                        trend,
                        rankValue: getRankText(
                            typeof value === 'string' ? parseFloat(value) : value,
                            item.valueKey === 'userSatisfactionRatio',
                            res[item.rankValueKey as keyof StatisticsOverviewData],
                            res.ta
                        ),
                    };
                });

                setOverviewCardData(overviewData);
            }
        } catch (e) {
            console.error(e);
        }
    }, [appId]);

    useEffect(() => {
        getOverviewData();
    }, [getOverviewData]);

    return {
        /** 渲染概览卡片UI数据 */
        overviewCardData,
    };
}
