/**
 * @file 数据分析模块的工具方法
 * <AUTHOR>
 */

import {AgentStatisticsSource, StatisticsSource} from '@/api/agentAnalysis/interface';
import {SourceOptionAll} from './components/pc/DataPanel/interface';

/**
 * 将数字和单位从字符串中分离出来(如：输入 10.1%, 输出 {value: 10.1, unit: '%'})
 *
 * @param str 待处理的字符串
 * @returns 返回一个对象，包含数字和单位
 */
export function splitUnitAndNumber(str: string): {value: number; unit: string} {
    // 匹配数字和可选的小数点后跟零个或多个数字的正则表达式
    const regex = /([\d]+(\.[\d]*)?)/;
    const matches = regex.exec(str);
    if (!matches) {
        return {
            value: 0,
            unit: '',
        };
    }

    const value = parseFloat(matches[1]);
    const unit = str.replace(matches[1], '');

    return {value, unit};
}

/**
 * 将渠道源数据格式化为Select下拉选择框选项数据
 * @param res 渠道源数据数组
 * @returns 返回一个Select下拉选择框选项数组
 */
export const formatSourceSelectOption = (res: AgentStatisticsSource[]) => {
    return [
        SourceOptionAll,
        ...res.map((item: AgentStatisticsSource) => ({
            label: item.label,
            value: item.source,
        })),
    ];
};

/**
 * 将渠道源数据格式化为级联下拉选择框选项数据
 * @param res 二级渠道源数据数组
 * @returns 返回一个级联下拉选择框选项数组
 */
export const formatSourceOption = (res: StatisticsSource[]) => {
    return [
        SourceOptionAll,
        ...res.map((item: StatisticsSource) => {
            const formatItem = {
                label: item.firstSourceName,
                value: item.firstSource,
                children: item.subSources
                    ? item.subSources.map(subItem => ({
                          label: subItem.secondSourceName,
                          value: subItem.secondSource,
                      }))
                    : [],
            };

            return formatItem;
        }),
    ];
};

export const enum DistributionDetailInfo {
    noDistribute = '智能体已上架智能体商店，在“最新发布”频道中即可看见',
    distribute = '质量较高的智能体可进入百度搜索公域分发，手机百度APP搜索智能体名称即有机会找到。',
}
