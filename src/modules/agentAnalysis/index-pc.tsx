/**
 * @file 智能体数据分析面板
 * <AUTHOR>
 */
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {ConfigProvider, Tabs} from 'antd';
import styled from '@emotion/styled';
import {useSearchParams} from 'react-router-dom';

import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {usePromptEditContext} from '@/modules/agentPromptEditV2/utils';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {AgentModeType, AgentTab} from '@/modules/agentPromptEditV2/interface';

import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import LoadError from '@/components/Loading/LoadError';

import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import CustomPopover from '@/components/Popover';
import useServerOnceTip from '@/components/Popover/useServerOnceTip';
import {PopupName} from '@/api/beginnerGuide/interface';
import DataPanel from './components/pc/DataPanel';
import ConversationPanel from './components/pc/ConversationPanel';

import BusinessLeadAnalysis from './components/pc/DataPanel/BusinessLeadAnalysis';
import {TabKey} from './components/pc/interface';
import EvaluateResult from './components/pc/EvaluateResult';
import DatasetRecallResult from './components/pc/DatasetRecallResult';

const StyledTabs = styled(Tabs)`
    .ant-tabs-tab-btn {
        font-weight: 500;
    }

    &.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #5562F2; !important;
    }

    .ant-tabs-nav {
        .ant-tabs-ink-bar {
            width: 30px !important;
        }
    }

    .ant-tabs-nav:before {
        border-bottom: none !important;
    }
`;

/**
 * 虽然被 appDetail 和 qianfanDetail 引用，但这两个页面目前并不会被访问到。
 */
function AgentAnalysis({appId}: {appId: string}) {
    const [searchParams] = useSearchParams();
    const pageActiveTab = searchParams.get('activeTab');

    const containerRef = useRef<HTMLDivElement>(null);

    const [tabKey, setTabKey] = useState<TabKey>(TabKey.data);
    const {open: openConversationLogTip, handleClose: handleCloseConversationLogTip} = useServerOnceTip({
        name: PopupName.ConversationFullOpenForAnalyze,
    });

    const contextData = usePromptEditContext();
    contextData.containerRef.analysisTab = containerRef;

    const {displayLog, clickLog} = useUbcLogV3();

    // 智能体类型
    const {modeType} = usePromptEditStoreV2(store => ({
        modeType: store.agentConfig.agentInfo.modeType,
    }));

    const handleClosePopover = useCallback(
        (e?: React.MouseEvent) => {
            // 阻止冒泡，避免关闭气泡会切换tab
            e?.stopPropagation();
            handleCloseConversationLogTip();
        },
        [handleCloseConversationLogTip]
    );

    const tabItems = [
        {
            key: TabKey.data,
            label: '数据看板',
            children: <DataPanel appId={appId} containerRef={containerRef} />,
        },
        {
            key: TabKey.conversation,
            label: (
                <CustomPopover
                    type="primary"
                    placement="right"
                    title={<span className="mr-2 font-normal">已上线的智能体可在此查看对话记录哦～</span>}
                    exitTime
                    open={openConversationLogTip}
                    onClose={handleClosePopover}
                    overlayClassName="z-[51]"
                >
                    对话记录
                </CustomPopover>
            ),
            children: <ConversationPanel appId={appId} />,
        },
    ];

    useEffect(() => {
        if (pageActiveTab === AgentTab.Analysis) {
            displayLog(EVENT_VALUE_CONST.AGENT_ANALYSIS, {analysisTabName: +tabKey});
        }
    }, [tabKey, displayLog, pageActiveTab]);

    const handlePanelChange = useCallback(
        (activeKey: string) => {
            setTabKey(activeKey as TabKey);
            handleCloseConversationLogTip();
            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_TAB, {analysisTabName: +activeKey});
        },
        [clickLog, handleCloseConversationLogTip]
    );

    return (
        <div ref={containerRef} className="pb-6">
            {/* 智能分析模块 */}
            {modeType !== AgentModeType.Workflow && (
                <div className="-mt-2 mb-4 flex gap-3">
                    <div className="w-1/2">
                        <EvaluateResult />
                    </div>
                    <div className="w-1/2">
                        <DatasetRecallResult />
                    </div>
                </div>
            )}
            <div className="rounded-[18px] bg-white p-6" id="summary">
                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {
                                titleFontSizeLG: 18,
                                horizontalItemPaddingLG: '0 0 5px 0',
                                horizontalItemGutter: 39,
                                horizontalItemPadding: '5px 0',
                            },
                        },
                    }}
                >
                    <StyledTabs
                        defaultActiveKey={TabKey.data}
                        items={tabItems}
                        size="large"
                        onChange={handlePanelChange}
                    />
                </ConfigProvider>
            </div>
            {tabKey === TabKey.data && (
                <div className="mt-4">
                    {/* 线索数据趋势模块 */}
                    <BusinessLeadAnalysis />
                </div>
            )}
        </div>
    );
}

export default function AgentAnalysisPC({appId}: {appId: string}) {
    const loadError = useCallback(() => <LoadError />, []);

    return (
        <CommonErrorBoundary renderError={loadError}>
            <LogContextProvider page={EVENT_PAGE_CONST.AGENT_ANALYSIS}>
                <AgentAnalysis appId={appId} />
            </LogContextProvider>
        </CommonErrorBoundary>
    );
}
