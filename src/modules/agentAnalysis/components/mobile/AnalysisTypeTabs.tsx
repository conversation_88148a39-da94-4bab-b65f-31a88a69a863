import {ConfigProvider, Tabs} from 'antd';
import {css} from '@emotion/css';
import {useCallback} from 'react';
import {colorPrimary, colorTextBase} from '@/styles/lingjing-light-theme';
import {AnalysisTabType} from '@/api/agentAnalysis/interface';
import {TabItem} from '../pc/DataPanel/interface';

export default function AnalysisTypeTabs({
    tabs,
    activeTab,
    onChange,
}: {
    tabs: TabItem[];
    activeTab: AnalysisTabType;
    onChange: (activeTab: AnalysisTabType) => void;
}) {
    const onTabsChange = useCallback(
        (tabKey: string) => {
            onChange(tabKey as AnalysisTabType);
        },
        [onChange]
    );

    return (
        <ConfigProvider
            theme={{
                components: {
                    Tabs: {
                        titleFontSize: 14,
                        itemColor: colorTextBase,
                        itemActiveColor: colorPrimary,
                        itemSelectedColor: colorPrimary,
                        itemHoverColor: colorPrimary,
                        inkBarColor: colorPrimary,
                        horizontalItemPadding: '5px 0 7px 0',
                        lineHeight: 20 / 14,
                        horizontalItemGutter: 18,
                        lineWidth: 0,
                        horizontalMargin: '18px 0 18px 0',
                    },
                },
            }}
        >
            <Tabs
                size="middle"
                indicator={{size: 30}}
                rootClassName={css`
                    .ant-tabs-tab-active .ant-tabs-tab-btn {
                        font-weight: 500;
                    }
                `}
                defaultActiveKey={AnalysisTabType.Conversation}
                activeKey={activeTab}
                items={tabs.map(item => ({label: item.description, key: item.id}))}
                onChange={onTabsChange}
            />
        </ConfigProvider>
    );
}
