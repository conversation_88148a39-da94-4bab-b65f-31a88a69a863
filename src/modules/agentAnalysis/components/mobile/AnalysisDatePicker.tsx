/**
 * @file 数据分析时间选择器
 * <AUTHOR>
 */

import React, {useCallback, useState} from 'react';
import type {RangePickerProps} from 'antd/es/date-picker';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import DropDown from '@/components/mobile/DropDown';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);

const defaultOptions = [
    {value: '1', label: '昨天'},
    {value: '7', label: '近7天'},
    {value: '14', label: '近14天'},
    {value: '30', label: '近30天'},
    {value: '60', label: '近60天'},
];

export default function AnalysisDatePicker({
    recentDays,
    setDateRangeValue,
    setRecentDays,
    getData,
}: {
    recentDays: string;
    dateRangeValue: RangePickerProps['value'];
    setDateRangeValue: (value: RangePickerProps['value']) => void;
    setRecentDays: (value: string) => void;
    getData?: (value?: RangePickerProps['value']) => void;
}) {
    const [dateOptions] = useState(defaultOptions);
    const {clickLog} = useUbcLogV3();

    // 下拉选择器change事件
    const handleChange = useCallback(
        (value: string | number) => {
            setRecentDays(value as string);

            // 设置时间选择器时间
            const dateRange: RangePickerProps['value'] = [
                dayjs().subtract(Number(value), 'day'),
                dayjs().subtract(1, 'day'),
            ];

            setDateRangeValue(dateRange);

            getData && getData(dateRange);

            // 1: 最近7天 2: 最近14天 3: 最近30天 7: 昨日 10: 最近60天
            const extEDateFilterType = {'1': 7, '7': 1, '14': 2, '30': 3, '60': 10} as const;
            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_STATIC_DATE_FILTER, {
                eDateFilterType: extEDateFilterType[value as keyof typeof extEDateFilterType],
            });
        },
        [setRecentDays, setDateRangeValue, getData, clickLog]
    );

    return (
        <div className="z-0 flex flex-shrink-0 items-center">
            <DropDown
                value={recentDays}
                options={dateOptions}
                onChange={handleChange}
                dropDownText={dateOptions.find(item => item.value === recentDays)?.label || '选择日期'}
            />
        </div>
    );
}
