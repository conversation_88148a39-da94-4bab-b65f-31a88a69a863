/**
 * @file 智能体数据分析面板-mobile
 * <AUTHOR>
 * @update <EMAIL> 2025.1 数据统计口径修改&交互样式升级
 */
import React, {useCallback, useEffect, useState} from 'react';
import {RangePickerProps} from 'antd/es/date-picker';
import dayjs from 'dayjs';
import classNames from 'classnames';
import {DefaultOptionType} from 'antd/es/cascader';
import Loading from '@/components/Loading';
import {LoadingSize} from '@/components/Loading/interface';
import Empty from '@/components/Empty';
import api from '@/api/agentAnalysis';
import {AnalysisTabType, IndicatorKeyEnum} from '@/api/agentAnalysis/interface';
import {formatSourceSelectOption} from '@/modules/agentAnalysis/utils';
import {getFeatureAccess} from '@/api/agentEditV2';
import {FeatureName} from '@/api/agentEditV2/interface';
import useAgentStatistics from '@/modules/agentAnalysis/hooks/useAgentStatistics';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {
    DefAnalysisDataTabs,
    IndicatorRadioOptions,
    AnalysisDataTabs,
    SourceOptionAll,
    SourceSelectValue,
} from '../pc/DataPanel/interface';
import {LogAnalysisTabTypeMap} from '../../interface';
import OverviewCards from './OverviewCards';
import AnalysisTypeTabs from './AnalysisTypeTabs';
import AnalysisChartCard from './AnalysisChartCard';

// 数据分析时间范围选择默认近7日
const DefaultRecentDays = 7;

export default function DataPanel({appId, containerRef}: {appId: string; containerRef: any}) {
    // 范围时间选择器值
    const [dateRangeValue, setDateRangeValue] = useState<RangePickerProps['value']>([
        dayjs().subtract(DefaultRecentDays, 'day'),
        dayjs().subtract(1, 'day'),
    ]);
    // 下拉时间选择器值
    const [recentDays, setRecentDays] = useState(`${DefaultRecentDays}`);

    const [analysisDataTabs, setAnalysisDataTabs] = useState(DefAnalysisDataTabs);
    const [activeTab, setActiveTab] = useState<AnalysisTabType>(AnalysisTabType.Conversation);

    const [sourceValue, setSourceValue] = useState<SourceSelectValue>([SourceOptionAll.value]);
    const [sourceSelectOption, setSourceSelectOption] = useState<DefaultOptionType[]>([SourceOptionAll]);
    /** 缓存数据来源渠道列表数据（商品的来源渠道） */
    const [goodsSourceOption, setGoodsSourceOption] = useState<DefaultOptionType[]>([SourceOptionAll]);

    // 数据指标(默认第一个选项)
    const [indicatorRadioKey, setIndicatorRadioKey] = useState<IndicatorKeyEnum>(
        IndicatorRadioOptions[activeTab][0].value
    );

    const {clickLog} = useUbcLogV3();

    const {
        generalSourceOptions,
        lineChartRef,
        lineChartLoading,
        lineChartData,
        getLineChartData,
        getAnalysisData,
        renderChartByIndicatorKey,
    } = useAgentStatistics({
        appId,
        dateRangeValue,
        activeTab,
        sourceValue,
        containerRef,
        indicatorRadioKey,
        setIndicatorRadioKey,
        // 移动端仅展示图表，不展示表格详情
        hasTable: false,
        isMobile: true,
    });

    // 数据指标Radio change，重新绘制选中指标 lineChart
    const onIndicatorRadioKeyChange = useCallback(
        (value: IndicatorKeyEnum) => {
            if (value === indicatorRadioKey) {
                return;
            }

            setIndicatorRadioKey(value);

            renderChartByIndicatorKey(value);

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_METRIC);
        },
        [clickLog, indicatorRadioKey, renderChartByIndicatorKey]
    );

    // 获取商品分析渠道列表数据
    const getGoodsStatisticsSource = useCallback(() => {
        api.getAgentGoodsStatisticsSource().then(res => {
            setGoodsSourceOption(formatSourceSelectOption(res));
        });
    }, []);

    // 是否展示商品分析
    useEffect(() => {
        (async () => {
            try {
                // 是有商品挂载白名单权益
                const {goods_mount: hasGoodsMount} = await getFeatureAccess({
                    featureName: FeatureName.GoodsMount,
                });

                if (hasGoodsMount) {
                    // 有则展示商品分析Tab
                    setAnalysisDataTabs(AnalysisDataTabs);
                    // 获取商品数据来源渠道列表
                    getGoodsStatisticsSource();
                }
            } catch (err) {
                console.error(err);
            }
        })();
    }, [getGoodsStatisticsSource]);

    // 设置来源渠道值
    useEffect(() => {
        setSourceSelectOption(activeTab === AnalysisTabType.Goods ? goodsSourceOption : generalSourceOptions);
    }, [activeTab, generalSourceOptions, goodsSourceOption]);

    // 用户分析、流量分析 等 tab 切换事件
    const onTabChange = useCallback(
        async (tabType: AnalysisTabType) => {
            const preActiveTab = activeTab;

            setActiveTab(tabType);
            setSourceValue([SourceOptionAll.value]);

            const currIndicatorRadioKey = IndicatorRadioOptions[tabType][0].value;
            setIndicatorRadioKey(currIndicatorRadioKey);

            // 从其他Tab切换到商品分析时，或者从商品分析切换到其他分析时，重新请求图表接口
            if (tabType === AnalysisTabType.Goods || preActiveTab === AnalysisTabType.Goods) {
                getLineChartData(tabType, [SourceOptionAll.value], currIndicatorRadioKey);
            }
            // 非商品分析Tab之间的切换不用请求图表接口，直接从缓存数据绘制图表即可
            else {
                renderChartByIndicatorKey(currIndicatorRadioKey);
            }

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_OPTION, {
                eAnalysisOption: LogAnalysisTabTypeMap[tabType],
            });
        },
        [activeTab, clickLog, getLineChartData, renderChartByIndicatorKey]
    );

    // 渠道选择器切换事件
    const onSourceChange = useCallback(
        (value: Array<string | number>) => {
            // 在智能体中为数字，在插件中为 string
            setSourceValue(value);

            getLineChartData(activeTab, value, indicatorRadioKey);

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_CHANNEL);
        },
        [getLineChartData, activeTab, indicatorRadioKey, clickLog]
    );

    return (
        <div>
            {/* 数据概览 */}
            <OverviewCards appId={appId} />
            {/* 统计类型Tabs */}
            <AnalysisTypeTabs tabs={analysisDataTabs} activeTab={activeTab} onChange={onTabChange} />
            {/* 不同数据指标图表集 */}
            <AnalysisChartCard
                activeTab={activeTab}
                dateRangeValue={dateRangeValue}
                recentDays={recentDays}
                setRecentDays={setRecentDays}
                setDateRangeValue={setDateRangeValue}
                sourceSelectOption={sourceSelectOption}
                source={sourceValue}
                onSourceChange={onSourceChange}
                getAnalysisData={getAnalysisData}
                indicatorKey={indicatorRadioKey}
                indicatorKeyOptions={IndicatorRadioOptions[activeTab]}
                onIndicatorKeyChange={onIndicatorRadioKeyChange}
                chartChildren={
                    <>
                        <div
                            className={classNames(
                                {
                                    hidden: !lineChartLoading,
                                },
                                'relative h-[186px] w-full'
                            )}
                        >
                            <Loading size={LoadingSize.small} />
                        </div>
                        <div
                            className={classNames('break-all pt-[14px] xlg:items-center xlg:justify-between', {
                                hidden: !lineChartData?.dateList?.length || lineChartLoading,
                                'xlg:flex': lineChartData?.dateList?.length && !lineChartLoading,
                            })}
                        >
                            <div ref={lineChartRef} className="inline-block h-[186px]"></div>
                        </div>
                        <div
                            className={classNames('relative h-[290px] w-full', {
                                hidden: lineChartData?.dateList?.length || lineChartLoading,
                            })}
                        >
                            <Empty />
                        </div>
                    </>
                }
            />
        </div>
    );
}
