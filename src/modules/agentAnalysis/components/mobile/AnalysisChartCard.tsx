/**
 * @file 数据分析图表卡片-mobile
 * <AUTHOR>
 */

import React, {useCallback, useState} from 'react';
import {css} from '@emotion/css';
import {Flex} from 'antd';
import {RangePickerProps} from 'antd/es/date-picker';
import {DefaultOptionType} from 'antd/es/cascader';
import classNames from 'classnames';
import {TreeSelectOption} from 'antd-mobile/es/components/tree-select';
import DropDown from '@/components/mobile/DropDown';
import {AnalysisTabType, IndicatorKeyEnum} from '@/api/agentAnalysis/interface';
import Cascader from '@/components/Cascader/index-m';
import {ScrollContainerM} from '@/components/ScrollContainer/index-m';
import {SourceOptionAll, SourceSelectValue} from '../pc/DataPanel/interface';
import AnalysisDatePicker from './AnalysisDatePicker';

export default function AnalysisChartCard({
    activeTab,
    dateRangeValue,
    recentDays,
    setDateRangeValue,
    setRecentDays,
    source,
    sourceSelectOption,
    onSourceChange,
    chartChildren,
    getAnalysisData,
    indicatorKey,
    indicatorKeyOptions,
    onIndicatorKeyChange,
}: {
    activeTab: AnalysisTabType;
    dateRangeValue: RangePickerProps['value'];
    recentDays: string;
    setDateRangeValue: (value: RangePickerProps['value']) => void;
    setRecentDays: (value: string) => void;
    source: SourceSelectValue;
    sourceSelectOption: DefaultOptionType[];
    onSourceChange: (value: SourceSelectValue) => void;
    chartChildren: React.ReactNode;
    getAnalysisData: (value?: RangePickerProps['value']) => void;
    indicatorKey: IndicatorKeyEnum;
    indicatorKeyOptions: Array<{value: IndicatorKeyEnum; label: string}>;
    onIndicatorKeyChange: (value: IndicatorKeyEnum) => void;
}) {
    const [sourcePopUpVisible, setSourcePopUpVisible] = useState(false);
    const [selectSourceName, setSelectSourceName] = useState(SourceOptionAll.label);

    const showPopup = useCallback(() => {
        setSourcePopUpVisible(true);
    }, []);

    const closePopup = useCallback(() => {
        setSourcePopUpVisible(false);
    }, []);

    const sourceChange = useCallback(
        (value: string | number) => {
            onSourceChange && onSourceChange([value]);
        },
        [onSourceChange]
    );

    const cascaderSourceChange = useCallback(
        (value: string[], options: TreeSelectOption[]) => {
            setSelectSourceName(options.map(item => item.label).join('/'));
            onSourceChange && onSourceChange(value);
            closePopup();
        },
        [closePopup, onSourceChange]
    );

    return (
        <div className="rounded-[1rem] border border-solid border-gray-100 bg-white px-[13px] pb-[18px] pt-[18px]">
            {/* 1.图表筛选区 */}
            <div className="mb-4 flex justify-between">
                <span className="mr-[2.625rem] flex-shrink-0 text-base font-semibold leading-5">数据详情</span>
                <Flex gap={21} className="max-w-[calc(100%-6.625rem)]">
                    {/* 商品分析还是一级渠道下拉框 */}
                    {activeTab === AnalysisTabType.Goods ? (
                        <DropDown
                            value={source[0]}
                            dropDownText={
                                `${sourceSelectOption.find(item => item.value === source[0])?.label}` || '渠道来源'
                            }
                            popoverWith={132}
                            overlayClassName={css`
                                .ant-popover-content .ant-popover-inner {
                                    transform: translatPeX(13px);
                                }
                            `}
                            onChange={sourceChange}
                            options={sourceSelectOption.map(item => ({
                                label: item.label as string,
                                value: item.value as string,
                            }))}
                        />
                    ) : (
                        <div className="flex items-center truncate" onClick={() => showPopup()}>
                            <span className="truncate text-sm">{selectSourceName}</span>
                            <span className="iconfont icon-expand1 ml-1 text-[8px] text-gray-quaternary"></span>
                        </div>
                    )}
                    <AnalysisDatePicker
                        dateRangeValue={dateRangeValue}
                        recentDays={recentDays}
                        setRecentDays={setRecentDays}
                        setDateRangeValue={setDateRangeValue}
                        getData={getAnalysisData}
                    />
                </Flex>
                {/* 一二级渠道下拉框 */}
                <Cascader
                    title="渠道筛选"
                    visible={sourcePopUpVisible}
                    setVisible={setSourcePopUpVisible}
                    value={source.map(item => item as string)}
                    options={sourceSelectOption as TreeSelectOption[]}
                    onChange={cascaderSourceChange}
                />
            </div>
            <div className="relative">
                <ScrollContainerM className="overflow-x-auto">
                    <Flex gap={6}>
                        {indicatorKeyOptions.map(radio => (
                            <div
                                key={radio.value}
                                onClick={() => onIndicatorKeyChange(radio.value)}
                                className={classNames(
                                    'h-[30px] whitespace-nowrap rounded-lg border-0 px-3 text-sm leading-[30px]',
                                    {
                                        'bg-[#5562F21A] font-medium text-primary': radio.value === indicatorKey,
                                        'bg-[#F5F6FA] text-black': radio.value !== indicatorKey,
                                    }
                                )}
                            >
                                {radio.label}
                            </div>
                        ))}
                    </Flex>
                </ScrollContainerM>
                <div className="absolute right-0 top-0 h-full w-[30px] bg-gradient-to-r from-white/0 to-white"></div>
            </div>
            {/* 图表容器 */}
            {chartChildren}
        </div>
    );
}
