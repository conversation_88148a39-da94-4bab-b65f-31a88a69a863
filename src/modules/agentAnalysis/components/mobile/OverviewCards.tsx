/**
 * @file 数据分析-wise-累计数据概览
 * <AUTHOR>
 */

import React from 'react';
import {Col, Flex, Row} from 'antd';
import useStatisticsOverview from '@/modules/agentAnalysis/hooks/useStatisticsOverview';
import {OverviewCardInfo, TrendType} from '@/modules/agentAnalysis/interface';

const DATA_STATUS_COLOR_MAP = {
    [TrendType.Up]: 'text-success',
    [TrendType.Down]: 'text-error',
    [TrendType.Same]: 'text-gray-secondary',
};

/**
 * 数据分析-概览卡片
 *
 * @param cardInfo 卡片数据
 * @constructor
 */
function OverviewCard({cardInfo}: {cardInfo: OverviewCardInfo}) {
    return (
        <div className="h-auto flex-1 self-stretch rounded-[0.75rem] border border-gray-border-secondary p-4 text-gray-secondary [&:nth-child(4n)]:mr-[0px]">
            <Flex>
                <span className="text-sm leading-none text-gray-tertiary">{cardInfo.name}</span>
            </Flex>
            <Flex className="my-3 whitespace-nowrap leading-none">
                <span className="mr-1 text-[21px] font-medium leading-[21px] text-black">{cardInfo.value || '--'}</span>
                <span className="text-sm">{cardInfo.unit}</span>
            </Flex>
            <Flex className="text-sm leading-none">
                <span className="text-gray-tertiary">周环比：</span>
                {/* 状态，cardInfo.value 有数据、但环比没数据时显示 "暂无环比" */}
                {cardInfo.trendValue || cardInfo.value ? (
                    <span className={`${DATA_STATUS_COLOR_MAP[cardInfo.trend] || 'text-gray-secondary'} font-medium`}>
                        {cardInfo.trendValue || '暂无环比'}
                    </span>
                ) : null}
            </Flex>
        </div>
    );
}

export default function OverviewCards({appId}: {appId: string}) {
    const {overviewCardData} = useStatisticsOverview(appId);

    return (
        <div className="rounded-2xl bg-white px-3 py-[1.125rem]">
            <Flex justify="space-between" align={'middle'} className="mb-[1.125rem]">
                <div className="text-base font-semibold leading-none">数据概览</div>
                <div className="text-sm text-gray-quaternary">中午12点前更新昨日数据</div>
            </Flex>
            <Row gutter={[8, 8]} wrap>
                {overviewCardData.map(item => (
                    <Col span={12} key={item.name}>
                        <OverviewCard cardInfo={item} />
                    </Col>
                ))}
            </Row>
        </div>
    );
}
