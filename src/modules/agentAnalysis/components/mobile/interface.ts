/**
 * @file 插件概览视图类型定义
 * <AUTHOR>
 */
import echarts, {ECOption} from '@/utils/echarts';
import themeConfig from '@/styles/lingjing-light-theme';
import {tooltipFormatter} from '../pc/interface';

export enum ChartTableType {
    Table = 'table',
    Chart = 'chart',
}

export const lineOption: ECOption = {
    color: ['#5AC2EA'],
    title: {
        text: '',
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'line',
            label: {
                show: false,
                backgroundColor: '#6a7985',
            },
            lineStyle: {
                color: '#edeef0', // 设置坐标轴线的颜色
            },
        },
        backgroundColor: '#fff',
        borderColor: '#fff',
        extraCssText: 'box-shadow: 0px 20px 80px rgba(29, 34, 82, 0.20); border: 1px solid #ECEEF3; padding: 12px;',
        className: 'rounded-[9px] p-3 leading-3 bg-white',
        // padding: 12,
        formatter: tooltipFormatter,
    },
    // legend: {
    //     // data: ['Line 1', 'Line 2', 'Line 3', 'Line 4', 'Line 5']
    // },
    toolbox: {
        feature: {
            // saveAsImage: {}
        },
    },
    grid: {
        // 确保曲线图底部 x 坐标的最左侧日期完整显示，设置左侧边距为 10
        left: 10,
        right: 15,
        top: 10,
        bottom: 0,
        containLabel: true, // 是否包含坐标轴的刻度标签
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            // nameGap: 15,
            axisLine: {
                lineStyle: {
                    color: '#edeef0', // 设置坐标轴线的颜色
                },
            },
            axisLabel: {
                margin: 12,
                color: '#848691',
                // echarts 未导出该参数类型，因此使用 any 类型
                formatter: (value: any) => {
                    // 在这里进行数据格式化处理，例如添加单位、小数点位数等
                    return value.replaceAll('-', '/');
                },
            },
            data: [], // todo赋值
        },
    ],
    yAxis: [
        {
            type: 'value',
            splitLine: {
                // 刻度线
                show: true, // 是否显示刻度线
                lineStyle: {
                    color: '#edeef0', // 刻度线的颜色
                    type: 'dashed', // 刻度线的类型
                },
            },
            axisLabel: {
                margin: 7,
                color: '#848691',
            },
        },
    ],
    series: [
        {
            name: '', // 需要传入
            type: 'line',
            // stack: 'Total',
            smooth: true,
            lineStyle: {
                color: 'blue',
                width: 1,
                type: 'solid',
            },
            showSymbol: true,
            symbol: 'circle', // 设置点的形状
            symbolSize: 4, // 设置点的大小
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgb(90, 194, 234, 0.12)',
                    },
                    {
                        offset: 1,
                        color: 'rgb(90, 194, 234, 0)',
                    },
                ]),
            },
            emphasis: {
                focus: 'series',
            },
            data: [], // todo赋值
        },
    ],
};

/**
 * 智能体曲线图样式
 */
export const AgentLineChartLineStyle = {
    lineStyle: {
        color: themeConfig.token.colorPrimary,
        width: 1.5,
        type: 'solid',
    },
    itemStyle: {
        color: themeConfig.token.colorPrimary, // 设置点的颜色，与线的颜色一致
        borderColor: '#fff',
        borderWidth: 2,
        emphasis: {
            borderColor: themeConfig.token.colorPrimary,
            color: '#fff',
        },
    },
    symbol: 'circle',
    symbolSize: 8,
    areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: 'rgba(26, 70, 255, 0.1)',
            },
            {
                offset: 1,
                color: 'rgba(26, 70, 255, 0)',
            },
        ]),
    },
    showAllSymbol: false,
};

// 默认每页查询数量
export const PAGE_SIZE = 10;

// 内容区滚动展示吸顶bar高度
export const BAR_SCROLL_CEILING_HEIGHT = 78;
