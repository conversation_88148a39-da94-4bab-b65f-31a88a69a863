/**
 * @file 插件概览视图类型定义
 * <AUTHOR>
 */
import echarts, {ECOption} from '@/utils/echarts';

/**
 * 趋势类型 1为上升趋势，-1为下降趋势，0为持平
 */
export enum TrendType {
    /** 1为上升趋势 */
    Up = 1,
    /** -1为下降趋势 */
    Down = -1,
    /** 0为持平 */
    Same = 0,
}

export enum ChartTableType {
    Table = 'table',
    Chart = 'chart',
}

/**
 * echart 鼠标悬浮时的弹层渲染函数，返回 DOM 字符串，在智能体中增加单位的处理逻辑
 *
 * @param params params[0].unit 为智能体模块手动调用时赋值，非 echarts 官方参数，echarts 中 params 后面有其他参数，因此直接在 params 中扩展
 */
export const tooltipFormatter = (params: any) => {
    // 获取当前数据项的名称和数值
    const value = params[0].value;
    const name = params[0].name;
    const color = params[0].color;
    const seriesName = params[0].seriesName;
    const unit = params[0].unit || '';

    // 返回包含 HTML 内容的字符串
    return `<div style="background-color: #fff; font-size: 14px;">
                <div style="margin-bottom: 6px; line-height: 18px;">${name}</div>
                <span style="font-weight: bold; color: ${color}; line-height: 18px;">${seriesName}</span>
                <span style="margin-left: 24px; color: #1E1F24;">${value}${unit}</span>
            </div>`;
};

export const lineOption: ECOption = {
    color: ['#5AC2EA'],
    title: {
        text: '',
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'line',
            label: {
                show: false,
                backgroundColor: '#6a7985',
            },
        },
        backgroundColor: '#fff',
        borderColor: '#fff',
        extraCssText: 'box-shadow: 0px 20px 80px rgba(29, 34, 82, 0.20); border: 1px solid #ECEEF3; padding: 12px;',
        className: 'rounded-[9px] p-3 leading-3 bg-white',
        // padding: 12,
        formatter: tooltipFormatter,
    },
    // legend: {
    //     // data: ['Line 1', 'Line 2', 'Line 3', 'Line 4', 'Line 5']
    // },
    toolbox: {
        feature: {
            // saveAsImage: {}
        },
    },
    grid: {
        // 确保曲线图底部 x 坐标的最左侧日期完整显示，设置左侧边距为 10
        left: 10,
        right: 36,
        top: 10,
        bottom: 0,
        containLabel: true, // 是否包含坐标轴的刻度标签
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            // nameGap: 15,
            axisLine: {
                lineStyle: {
                    color: '#ECEEF3', // 设置坐标轴线的颜色
                },
            },
            axisLabel: {
                margin: 24,
                color: '#50525C',
                // echarts 未导出该参数类型，因此使用 any 类型
                formatter: (value: any) => {
                    // 在这里进行数据格式化处理，例如添加单位、小数点位数等
                    return value.replaceAll('-', '/');
                },
            },
            data: [], // todo赋值
        },
    ],
    yAxis: [
        {
            type: 'value',
            splitLine: {
                // 刻度线
                show: true, // 是否显示刻度线
                lineStyle: {
                    color: '#ECEEF3', // 刻度线的颜色
                    type: 'dashed', // 刻度线的类型
                },
            },
            axisLabel: {
                margin: 24,
                color: '#50525C',
            },
        },
    ],
    series: [
        {
            name: '', // 需要传入
            type: 'line',
            // stack: 'Total',
            smooth: true,
            lineStyle: {
                color: 'blue',
                width: 1,
                type: 'solid',
            },
            showSymbol: true,
            symbol: 'circle', // 设置点的形状
            symbolSize: 4, // 设置点的大小
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgb(90, 194, 234, 0.12)',
                    },
                    {
                        offset: 1,
                        color: 'rgb(90, 194, 234, 0)',
                    },
                ]),
            },
            emphasis: {
                focus: 'series',
            },
            data: [], // todo赋值
        },
    ],
};

/*
 * 饼图配置,没用到暂时先注释
 */
/*
export const pieOption: ECOption = {
    grid: {
        left: 100,
        right: 0,
        top: 0,
        bottom: 0,
        containLabel: true, // 是否包含坐标轴的刻度标签
    },
    tooltip: {
        trigger: 'item',
        borderColor: '#fff',
        backgroundColor: '#fff',
        extraCssText: 'box-shadow: 0px 20px 80px rgba(29, 34, 82, 0.20); border: 1px solid #ECEEF3; padding: 12px;',
        className: 'rounded-[9px] p-3 leading-3',
        padding: 12,
        formatter: (params: any) => {
            // 获取当前数据项的名称和数值
            const name: string = `${params.name}`;
            const value: string = `${params.value}`;
            const color: string = `${params.color}`;
            // 返回包含 HTML 内容的字符串
            return `<div style="text-align: center; line-height: 18px; font-size: 14px;"><span style="font-weight: bold; color: ${color};">${name}</span><span style="margin-left: 24px; color: #1E1F24;">${value}</span></div>`;
        },
    },
    legend: {
        // 默认不展示
        show: false,
        top: '5%',
        left: 'center',
    },
    series: [
        {
            name: 'Access From',
            type: 'pie',
            left: -40,
            top: 0,
            right: 0,
            bottom: 0,
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 0,
                borderColor: '#fff',
                borderWidth: 2,
            },
            label: {
                show: true,
                color: '#50525C',
                fontSize: 14,
            },
            emphasis: {
                label: {
                    show: true,
                    fontWeight: 'bold',
                },
            },
            // labelLine: {
            //     show: false
            // },
            data: [
                {
                    value: 1048,
                    name: 'Search Engine',
                    itemStyle: {
                        // stop the chart from rendering this piece
                        color: '#78DBE2',
                        decal: {
                            symbol: 'none',
                        },
                    },
                },
                {
                    value: 735,
                    name: 'Direct',
                    itemStyle: {
                        // stop the chart from rendering this piece
                        color: '#7E9CFB',
                        decal: {
                            symbol: 'none',
                        },
                    },
                },
                {
                    value: 580,
                    name: 'Email',
                    itemStyle: {
                        // stop the chart from rendering this piece
                        color: '#FF9569',
                        decal: {
                            symbol: 'none',
                        },
                    },
                },
            ],
        },
    ],
};
*/

// 默认每页查询数量
export const PAGE_SIZE = 10;

// 内容区滚动展示吸顶bar高度
export const BAR_SCROLL_CEILING_HEIGHT = 78;

export const enum TabKey {
    /** 数据看板 */
    data = '1',
    /** 对话看板 */
    conversation = '2',
}

// Agent评估报告生成时间 为3天
export const EvaluateReportAnalysisTime = '3天';

export const DatasetCallRatioThreshold = 70;

export const DatasetCoverageThreshold = 30;

export const DatasetScoreThreshold = 0.7;

/** 是否达标类型 */
export enum PassType {
    /** 未达标 */
    Failed = 0,
    /** 达标 */
    Passed = 1,
}

export interface ImageUploadProps {
    enabled: boolean;
    title?: string;
    maxCount?: number;
}
