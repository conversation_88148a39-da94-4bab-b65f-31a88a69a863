/**
 * @file 知识库检索分析-报告概览
 * <AUTHOR>
 */

import {Divider, Flex} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {DatasetRecallOverview} from '@/api/datasetRecallTest/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import DatasetReportPanel from '../DatasetReportPanel';
import {RecallOverviewItem} from './RecallOverviewItem';

export default function RecallOverview({datasetRecallOverview}: {datasetRecallOverview: DatasetRecallOverview}) {
    const [reportOpen, setReportOpen] = useState(false);
    const {clickLog} = useUbcLogV3();

    const openDatasetReport = useCallback(() => {
        // 点击查看报告埋点
        clickLog(EVENT_VALUE_CONST.REPOSITORY_ANALYSIS_CHECK);
        setReportOpen(true);
    }, [clickLog]);

    const overviewList = useMemo(
        () => [
            {
                title: '知识库调用率',
                value:
                    datasetRecallOverview.reportReady && datasetRecallOverview.datasetCallRatio
                        ? [Math.floor(datasetRecallOverview.datasetCallRatio).toString(), '%']
                        : ['--', '%'],
                tips: '智能体有调用知识库的次数/智能体对话次数*100%',
            },
            {
                title: '知识库覆盖率',
                value:
                    datasetRecallOverview.reportReady && datasetRecallOverview.datasetCoverage
                        ? [Math.floor(datasetRecallOverview.datasetCoverage).toString(), '%']
                        : ['--', '%'],
                tips: '智能体有召回知识库的次数/智能体调用知识库次数*100%',
            },
            {
                title: '召回分段数',
                value: datasetRecallOverview.reportReady
                    ? [datasetRecallOverview.textHits.toString(), '段']
                    : ['--', '段'],
                tips: '当前智能体关联的知识库中，被召回的段落数，重复段落已去重',
            },
        ],
        [
            datasetRecallOverview.datasetCallRatio,
            datasetRecallOverview.datasetCoverage,
            datasetRecallOverview.reportReady,
            datasetRecallOverview.textHits,
        ]
    );

    return (
        <div>
            <Flex align="center" className="px-6 text-sm">
                <div>
                    {datasetRecallOverview.reportReady
                        ? '👏 已生成上一周的知识库检索报告，快来看看吧～'
                        : `💪 知识库检索报告待解锁，本周还差${datasetRecallOverview.chatLeft}次对话即可解锁哦！`}
                </div>
                {datasetRecallOverview.reportReady && (
                    <div
                        className="ml-[22px] cursor-pointer whitespace-nowrap leading-[20px] text-primary hover:opacity-80"
                        onClick={openDatasetReport}
                    >
                        查看报告
                    </div>
                )}
            </Flex>
            <Divider className="my-[22px]" />
            <Flex className="px-6" gap="92px">
                {overviewList.map(item => (
                    <RecallOverviewItem key={item.title} {...item} />
                ))}
            </Flex>

            <DatasetReportPanel
                open={reportOpen}
                setOpen={setReportOpen}
                datasetRecallOverview={datasetRecallOverview}
                overviewList={overviewList}
            />
        </div>
    );
}
