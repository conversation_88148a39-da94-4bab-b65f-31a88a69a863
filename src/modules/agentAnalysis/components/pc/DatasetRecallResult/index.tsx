/**
 * @file 知识库检索分析卡片
 * <AUTHOR>
 */

import {Space} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import isEmpty from 'lodash/isEmpty';
import Loading from '@/components/Loading';
import {LoadingSize} from '@/components/Loading/interface';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {defaultRenderError} from '@/components/CommonErrorBoundary/util';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import datasetRecallTest from '@/api/datasetRecallTest';
import {DatasetRecallOverview} from '@/api/datasetRecallTest/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {formatReportUpdateTime} from '../ReportPanel/utils';
import RecallOverviewEmpty from './RecallOverviewEmpty';
import RecallOverview from './RecallOverview';

const backgroundCircleStyle =
    'absolute top-[-338px] z-0 h-[400px] w-[400px] rounded-full bg-[#6685FF] opacity-10 blur-[50px]';

export default function DatasetRecallResult() {
    const [pageLoading, setPageLoading] = useState(true);
    const [loadError, setLoadError] = useState(false);
    const [requestError, setRequestError] = useState<any>();
    const [datasetRecallOverview, setDatasetRecallOverview] = useState<DatasetRecallOverview>();
    const {appId, datasetIds} = usePromptEditStoreV2(store => ({
        appId: store.agentConfig?.agentInfo?.appId,
        datasetIds: store.agentConfig?.agentJson?.datasetIds || [],
    }));

    const {showLog} = useUbcLogV3();

    const fetchDatasetRecallReport = useCallback(async () => {
        if (!appId) {
            return;
        }

        try {
            setPageLoading(true);
            const res = await datasetRecallTest.getDatasetRecallOverview({appId});
            setDatasetRecallOverview(res);
            setPageLoading(false);
        } catch (error) {
            setPageLoading(false);
            setLoadError(true);
            setRequestError(error);
            console.error(error);
        }
    }, [appId]);

    useEffect(() => {
        (async () => {
            if (!appId) {
                return;
            }

            try {
                await fetchDatasetRecallReport();
            } catch (err) {
                console.error(err);
            }
        })();
    }, [appId, fetchDatasetRecallReport]);

    // 组件展现埋点
    useEffect(() => {
        if (!pageLoading && !loadError && datasetRecallOverview) {
            showLog(EVENT_VALUE_CONST.REPOSITORY_ANALYSIS);
        }
    }, [pageLoading, loadError, datasetRecallOverview, showLog]);

    return (
        <div className="relative h-full min-h-[180px] rounded-[0.875rem] border border-solid border-gray-100 bg-white p-6 text-colorTextDefault">
            <Space className="flex flex-row gap-[18px]">
                <span className="text-gray-base text-lg font-medium leading-[26px]">知识库检索分析</span>
                {datasetRecallOverview?.updateTime && (
                    <span className="text-xs leading-[26px] text-gray-tertiary">
                        更新时间：{formatReportUpdateTime(datasetRecallOverview.updateTime)}
                    </span>
                )}
            </Space>

            {pageLoading ? (
                <Loading size={LoadingSize.small} />
            ) : loadError || !datasetRecallOverview ? (
                <RenderError
                    size="small"
                    className="h-auto py-0"
                    onBtnClick={fetchDatasetRecallReport}
                    error={requestError}
                />
            ) : (
                <CommonErrorBoundary renderError={defaultRenderError({size: 'small', className: 'h-full'})}>
                    <div className="relative mt-[18px] h-[172px] w-full overflow-hidden rounded-xl border border-solid border-gray-border-secondary py-5">
                        <div className={`${backgroundCircleStyle} left-[-50px]`} />
                        <div className={`${backgroundCircleStyle} right-[-50px]`} />
                        <div className="z-1 relative">
                            {isEmpty(datasetIds) ? (
                                <RecallOverviewEmpty />
                            ) : (
                                <RecallOverview datasetRecallOverview={datasetRecallOverview} />
                            )}
                        </div>
                    </div>
                </CommonErrorBoundary>
            )}
        </div>
    );
}
