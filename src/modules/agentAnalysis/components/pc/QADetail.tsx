/**
 * @file 问答对渲染组件（可渲染多个问答对）
 * <AUTHOR>
 */

import {QAItem} from '@/api/agentAnalysis/interface';
import Markdown from '@/modules/agentPromptEditV2/pc/TuningTab/components/Markdown';

export default function QAList({qaList}: {qaList: Array<Omit<QAItem, 'time'>>}) {
    return (
        <div className="rounded-xl bg-gray-bg-base p-4">
            {qaList.map((item, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={`qa-${index}`} className={`${index === 0 ? '' : 'mt-4'}`}>
                    <div className="mt-4 text-right first:mt-0">
                        <div className="inline-block rounded-[15px] rounded-br-[3px] bg-[#7365ff] px-[13px] py-[9px] text-left leading-[22px] text-white">
                            <Markdown>{item.query}</Markdown>
                        </div>
                    </div>
                    <div className="mt-4 rounded-[15px] rounded-tl-[3px] bg-white px-[13px] py-[9px] leading-[22px]">
                        <Markdown>{item.response}</Markdown>
                    </div>
                </div>
            ))}
        </div>
    );
}
