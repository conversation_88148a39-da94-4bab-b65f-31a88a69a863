/* eslint-disable complexity */
/**
 * @file 智能分析-有报告情况
 * <AUTHOR>
 */

import {Divider, Flex} from 'antd';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import {AgentDiagnosisInfo, AnalysisResultType} from '@/api/agentDiagnosis/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import {CardTagName} from '@/modules/center/interface';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {repeatTypeTips} from '@/api/agentAnalysis/interface';
import {RepeatTypeStatus, RepeatType} from '@/api/agentEdit/interface';
import TextWithTooltip from '../DatasetReportPanel/TextWithTooltip';
import DiagnosisReport from '../ReportPanel/DiagnosisReport';
import {ResultSummary} from './ResultSummary';

export default function DiagnosisEntry({
    analyzeInfo,
    repeatType,
    duplicate,
}: {
    analyzeInfo: AgentDiagnosisInfo;
    repeatType?: RepeatType;
    duplicate?: boolean;
}) {
    const [reportOpen, setReportOpen] = useState(false);
    const {clickLog, showLog} = useUbcLogV3();
    const {tags} = usePromptEditStoreV2(store => ({
        tags: store.tags,
    }));

    // 上次评估的优化项个数
    const lastIssueNum = analyzeInfo.diagnosisResult?.lastIssueKeys?.length || 0;
    // 当前评估的优化项个数
    const issueNum = analyzeInfo.diagnosisResult?.issueKeys?.length || 0;
    // 是否有调优，待优化项个数减少或者有调优之星
    const improved = useMemo(
        () => !!tags?.find(tag => tag.name === CardTagName.tuningStar) || issueNum < lastIssueNum,
        [tags, issueNum, lastIssueNum]
    );

    // 智能体质量分析展示的四种状态
    const analysisResultType = useMemo(() => {
        // 未产出评估报告
        if (!analyzeInfo.diagnosisResult) {
            return;
        }

        // 有分发，则是优秀智能体
        if (analyzeInfo.diagnosisResult?.agentDistributeScene) {
            return AnalysisResultType.CanDistribute;
        }

        // 无分发-优化项减少
        if (improved) {
            return AnalysisResultType.OptimizationReduce;
        }

        // 不可分发-有优化项
        if (issueNum > 0) {
            return AnalysisResultType.HasOptimization;
        }

        // 不可分发-无优化项
        return AnalysisResultType.NoOptimization;
    }, [analyzeInfo.diagnosisResult, improved, issueNum]);

    // 分析模块四种状态展现打点
    useEffect(() => {
        if (analysisResultType) {
            showLog(EVENT_VALUE_CONST.AI_ANALYSIS_BOX, {cAiAnalysisType: analysisResultType});
        }
    }, [analysisResultType, showLog]);

    // 评估结果 详细信息文案
    const evaluationInfo = useMemo(() => {
        // 未产出评估报告
        if (!analyzeInfo.diagnosisResult) {
            return;
        }

        // 有分发，则是优秀智能体
        if (analyzeInfo.diagnosisResult?.agentDistributeScene) {
            return improved
                ? '恭喜！智能体质量已提升至优秀，获得百度搜索分发权益，继续保持哟！'
                : '恭喜！您的智能体配置完整，运行正常，对话效果优秀，已获得百度搜索分发权益，继续保持哟！';
        }

        // 有改进，提示鼓励文案
        if (improved) {
            return issueNum === 0 ? (
                '恭喜！您的智能体质量已提升，已在平台官方上架，可以被所有人体验啦！'
            ) : (
                <>
                    恭喜！您的智能体质量已提升，再接再厉继续优化
                    <span className="mx-1 font-medium text-[#FF8200]">
                        {analyzeInfo.diagnosisResult.issueKeys.length}项
                    </span>
                    ，有机会获得更多流量哦～
                </>
            );
        }

        // 首次分析，但没有分发，鼓励进行调优
        return issueNum === 0 ? (
            '恭喜！您的智能体配置完整，运行正常，已在平台官方上架，可以被所有人体验啦！'
        ) : (
            <>
                智能体已在平台官方上架，已发现
                <span className="mx-1 font-medium text-[#FF8200]">
                    {analyzeInfo.diagnosisResult.issueKeys.length}个
                </span>
                待优化项，继续优化可获得更多流量
            </>
        );
    }, [analyzeInfo.diagnosisResult, improved, issueNum]);

    const repeatTips = useMemo(() => {
        return !!repeatType && Object.values(RepeatTypeStatus).includes(repeatType)
            ? `由于该智能体${repeatTypeTips[repeatType]}与其他智能体相同，影响了流量效果及使用，建议修改`
            : duplicate
            ? '由于该智能体与被复制的智能体相似度过高，影响了流量效果及使用，建议修改智能体头像/名称/人设与回复逻辑'
            : '';
    }, [repeatType, duplicate]);

    /** 打开诊断报告 */
    const openDiagnosisReport = useCallback(() => {
        setReportOpen(true);

        clickLog(EVENT_VALUE_CONST.CHECK_REPORT);
    }, [clickLog]);

    /** 不同质量维度按钮打开诊断报告 */
    const openDiagnosisReportOfType = useCallback(() => {
        setReportOpen(true);
    }, []);

    const [, setSearchParams] = useSearchParams();

    const changeTab = useCallback(() => {
        setSearchParams(
            () => {
                const res: Record<string, string> = {};
                const prev = new URLSearchParams(window.location.search);
                res.activeTab = AgentTab.Create;
                const appId = prev.get('appId');
                appId && (res.appId = appId);
                return res;
            },
            {replace: true}
        );
    }, [setSearchParams]);

    return (
        <div className="h-full w-full">
            <Flex align="center" className="px-6 text-sm">
                {repeatType || duplicate ? (
                    <>
                        <TextWithTooltip className="truncate">🔔 {repeatTips}</TextWithTooltip>
                        <div
                            className="ml-[22px] cursor-pointer whitespace-nowrap leading-[20px] text-primary hover:opacity-80"
                            onClick={changeTab}
                        >
                            去修改
                        </div>
                    </>
                ) : (
                    <>
                        <TextWithTooltip className="truncate">
                            {(analyzeInfo.diagnosisResult?.issueKeys || []).length > 0 ? '🔔 ' : '🎉 '}
                            {evaluationInfo}
                        </TextWithTooltip>
                        <div
                            className="ml-[22px] cursor-pointer whitespace-nowrap leading-[20px] text-primary hover:opacity-80"
                            onClick={openDiagnosisReport}
                        >
                            查看详情
                        </div>
                    </>
                )}
            </Flex>

            <Divider className="my-[22px]" />

            <ResultSummary report={analyzeInfo.diagnosisResult!} openReport={openDiagnosisReportOfType} />

            <DiagnosisReport
                open={reportOpen}
                setOpen={setReportOpen}
                reportTime={analyzeInfo.reportTime}
                report={analyzeInfo.diagnosisResult!}
            />
        </div>
    );
}
