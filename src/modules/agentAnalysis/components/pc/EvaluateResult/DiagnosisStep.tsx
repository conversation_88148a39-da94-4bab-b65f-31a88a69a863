/**
 * @file 智能分析-无诊断报告场景-分析进度组件
 * <AUTHOR>
 */

import {Divider, Flex, Steps, Tooltip} from 'antd';
import {useCallback, useMemo} from 'react';
import {StepProps} from 'antd/lib/steps';
import {css} from '@emotion/css';
import {useSearchParams} from 'react-router-dom';
import {AgentEvaluateStatus, AgentStatus} from '@/api/agentDiagnosis/interface';
import {AgentPermission} from '@/api/agentEdit/interface';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {repeatTypeTips} from '@/api/agentAnalysis/interface';
import {RepeatTypeStatus, RepeatType} from '@/api/agentEdit/interface';
import {EvaluateReportAnalysisTime} from '../interface';
import TextWithTooltip from '../DatasetReportPanel/TextWithTooltip';

// 无报告的空状态
enum EmptyStatus {
    /** 未上线-开发中 */
    Developing = 1,
    /** 未上线-审核中 */
    Auditing = 2,
    /** 未上线-审核失败 */
    AuditFail = 3,
    /** 已上线-未公开 */
    OnlineNoPublic = 4,
    /** 已上线-未评估 */
    OnlineNoEvaluated = 5,
    /** 已上线-评估中 */
    OnlineEvaluating = 6,
    /** 已下线 */
    Offline = 7,
}

interface EmptyShowData {
    /** 状态说明 */
    statusDesc: string;
    /** 报告分析前智能体状态进度 */
    progress: StepProps[];
}

/** 进度条-已完成icon */
function StepIconSuccess() {
    return <div className="iconfont icon-success text-2xl leading-none text-success"></div>;
}

/** 进度条-进行中icon */
function StepIconClock() {
    return <div className="iconfont icon-clock-circle text-2xl leading-none text-warning"></div>;
}

/** 进度条-失败icon */
function StepIconFail() {
    return <div className="iconfont icon-error text-2xl leading-none text-gray-tertiary"></div>;
}

/** 进度条-数字步骤icon */
function StepIconNumber({num}: {num: number}) {
    return (
        <div className="flex h-[22px] w-[22px] items-center justify-center rounded-full border border-gray-tertiary bg-gray-bg-base text-sm text-gray-tertiary">
            {num}
        </div>
    );
}

/** 质量分析ToolTip */
function DiagnosisToolTip({
    agentPermission = AgentPermission.PUBLIC,
    showViewPathTips,
}: {
    /** Agent 访问权限 */
    agentPermission?: AgentPermission;
    /** 是否展示查看路径的提示 */
    showViewPathTips?: boolean;
}) {
    const tips = useMemo(() => {
        if (agentPermission === AgentPermission.PUBLIC) {
            return `智能体上线后，预计${EvaluateReportAnalysisTime}内完成质量分析${
                showViewPathTips ? '，可在“我的智能体-分析”查看结果' : ''
            }`;
        }
        return `您的智能体还未公开发布，不可进行质量分析。公开发布的智能体预计${EvaluateReportAnalysisTime}内完成质量分析${
            showViewPathTips ? '，可在“我的智能体-分析”查看结果' : ''
        }`;
    }, [agentPermission, showViewPathTips]);

    if (agentPermission === undefined) {
        return '质量分析';
    }

    return (
        <Tooltip
            title={tips}
            placement="bottom"
            rootClassName={css`
                max-width: 380px !important;
            `}
            align={{
                offset: [0, 8],
                targetOffset: [-30, 0],
            }}
            className="cursor-pointer"
        >
            质量分析
            <span className="iconfont icon-questionCircle ml-1 text-sm text-gray-quaternary"></span>
        </Tooltip>
    );
}

const EmptyShowDataMaps: Record<EmptyStatus, EmptyShowData> = {
    [EmptyStatus.Developing]: {
        statusDesc: '公开发布智能体将解锁专属分析报告哦～',
        progress: [
            {
                title: '开发中',
                status: 'process',
                icon: <StepIconClock />,
            },
            {
                title: '已上线',
                icon: <StepIconNumber num={2} />,
            },
            {
                title: '质量分析',
                icon: <StepIconNumber num={3} />,
            },
        ],
    },
    [EmptyStatus.Auditing]: {
        statusDesc: '智能体上线中，上线后将解锁专属分析报告哦～',
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '上线中',
                status: 'process',
                icon: <StepIconClock />,
            },
            {
                title: '质量分析',
                icon: <StepIconNumber num={3} />,
            },
        ],
    },
    [EmptyStatus.AuditFail]: {
        statusDesc: '智能体未通过审核，无法进行质量分析',
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '审核不通过',
                status: 'process',
                icon: <StepIconFail />,
            },
            {
                title: '质量分析',
                icon: <StepIconNumber num={3} />,
            },
        ],
    },
    [EmptyStatus.OnlineNoPublic]: {
        statusDesc: '智能体暂未公开发布，不能进行质量分析哦～',
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '已上线',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '质量分析',
                status: 'process',
                icon: <StepIconFail />,
            },
        ],
    },
    [EmptyStatus.Offline]: {
        statusDesc: '智能体已下线，无法进行质量分析',
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '已下线',
                status: 'process',
                icon: <StepIconFail />,
            },
            {
                title: '质量分析',
                icon: <StepIconNumber num={3} />,
            },
        ],
    },
    [EmptyStatus.OnlineNoEvaluated]: {
        statusDesc: '智能体分析功能已上线，重新公开发布后可收获专属分析报告',
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '已上线',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '质量分析',
                icon: <StepIconNumber num={3} />,
            },
        ],
    },
    [EmptyStatus.OnlineEvaluating]: {
        statusDesc: `质量分析进行中，预计${EvaluateReportAnalysisTime}内可查看分析报告`,
        progress: [
            {
                title: '已发布',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '已上线',
                status: 'finish',
                icon: <StepIconSuccess />,
            },
            {
                title: '质量分析中',
                status: 'process',
                icon: <StepIconClock />,
            },
        ],
    },
};

export default function DiagnosisStep({
    agentStatus,
    agentPermission,
    evaluateStatus = AgentEvaluateStatus.NoEvaluated,
    showAlert = true,
    showViewPathTips,
    repeatType,
    duplicate,
}: {
    agentStatus: AgentStatus;
    agentPermission: AgentPermission;
    evaluateStatus?: AgentEvaluateStatus;
    showAlert?: boolean;
    /** 是否展示查看路径的提示 */
    showViewPathTips?: boolean;
    /** */
    repeatType?: RepeatType;
    /** 是否为复制 */
    duplicate?: boolean;
}) {
    // eslint-disable-next-line complexity
    const emptyData = useMemo(() => {
        // 默认已上线评估中
        let info: EmptyShowData = EmptyShowDataMaps[EmptyStatus.OnlineEvaluating];

        if (agentStatus === AgentStatus.Offline) {
            info = EmptyShowDataMaps[EmptyStatus.Offline];
        }

        if (agentStatus === AgentStatus.Developing) {
            info = EmptyShowDataMaps[EmptyStatus.Developing];
        }

        if (agentStatus === AgentStatus.Auditing) {
            info = EmptyShowDataMaps[EmptyStatus.Auditing];
        }

        if (agentStatus === AgentStatus.AuditFail) {
            info = EmptyShowDataMaps[EmptyStatus.AuditFail];
        }

        if (
            agentStatus === AgentStatus.Online &&
            (evaluateStatus === AgentEvaluateStatus.NoEvaluated || evaluateStatus === null)
        ) {
            // 公开发布已上线，未评估代表历史Agent，引导去发布后触发评估
            if ((agentPermission || AgentPermission.PUBLIC) === AgentPermission.PUBLIC) {
                info = EmptyShowDataMaps[EmptyStatus.OnlineNoEvaluated];
            }
            // 非公开发布已上线，未评估，提示未公开不能进行质量分析
            else {
                info = EmptyShowDataMaps[EmptyStatus.OnlineNoPublic];
            }
        }

        // 更新step第3步-【质量分析】
        const diagnosisStepIndex = 2;
        if (info.progress[diagnosisStepIndex]) {
            if (agentStatus === AgentStatus.Online && evaluateStatus === AgentEvaluateStatus.Evaluating) {
                info.progress[diagnosisStepIndex].title = (
                    <DiagnosisToolTip agentPermission={agentPermission} showViewPathTips={showViewPathTips} />
                );
            } else {
                info.progress[diagnosisStepIndex].title = (
                    <DiagnosisToolTip agentPermission={agentPermission} showViewPathTips={showViewPathTips} />
                );
            }
        }

        return info;
    }, [agentPermission, agentStatus, evaluateStatus, showViewPathTips]);

    const repeatTips = useMemo(() => {
        return !!repeatType && Object.values(RepeatTypeStatus).includes(repeatType)
            ? `由于该智能体${repeatTypeTips[repeatType]}与其他智能体相同，影响了流量效果及使用，建议修改`
            : duplicate
            ? '由于该智能体与被复制的智能体相似度过高，影响了流量效果及使用，建议修改智能体头像/名称/人设与回复逻辑'
            : '';
    }, [repeatType, duplicate]);

    const [, setSearchParams] = useSearchParams();

    const changeTab = useCallback(() => {
        setSearchParams(
            () => {
                const res: Record<string, string> = {};
                const search = window.location.search || '';
                const prev = new URLSearchParams(search) || {};
                res.activeTab = AgentTab.Create;
                const appId = prev.get('appId');
                appId && (res.appId = appId);
                return res;
            },
            {replace: true}
        );
    }, [setSearchParams]);

    return (
        <>
            <Flex align="center" className="px-6 text-sm">
                {repeatType || duplicate ? (
                    <>
                        <TextWithTooltip className="truncate">🔔 {repeatTips}</TextWithTooltip>
                        <div
                            className="ml-[22px] cursor-pointer whitespace-nowrap leading-[20px] text-primary hover:opacity-80"
                            onClick={changeTab}
                        >
                            去修改
                        </div>
                    </>
                ) : (
                    showAlert && <TextWithTooltip>🔔 {emptyData.statusDesc}</TextWithTooltip>
                )}
            </Flex>

            <Divider className="mb-[30px] mt-[22px]" />

            <Steps
                rootClassName={css`
                    .ant-steps-item-tail {
                        text-align: center !important;
                        &::after {
                            width: 64px !important;
                            background-color: #5562f2 !important;
                        }
                    }
                    .ant-steps-item-content {
                        margin-top: 6px !important;
                        .ant-steps-item-title {
                            line-height: 20px !important;
                        }
                    }
                `}
                responsive={false} // 禁止自适应变成垂直模式
                size="small"
                labelPlacement="vertical"
                items={emptyData.progress}
                className="mb-[8px] max-w-[512px] text-base font-medium"
            />
        </>
    );
}
