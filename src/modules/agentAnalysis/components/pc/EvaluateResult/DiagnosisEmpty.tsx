/**
 * @file 智能分析-无诊断报告场景
 * <AUTHOR> l<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

import {AgentDiagnosisInfo} from '@/api/agentDiagnosis/interface';
import {RepeatType} from '@/api/agentEdit/interface';
import DiagnosisStep from './DiagnosisStep';

export default function DiagnosisEmpty({
    analyzeInfo,
    repeatType,
    duplicate,
}: {
    analyzeInfo: AgentDiagnosisInfo;
    repeatType?: RepeatType;
    duplicate?: boolean;
}) {
    return (
        <DiagnosisStep
            agentStatus={analyzeInfo.agentStatus}
            agentPermission={analyzeInfo.agentPermission}
            evaluateStatus={analyzeInfo.evaluateStatus}
            repeatType={repeatType}
            duplicate={duplicate}
        />
    );
}
