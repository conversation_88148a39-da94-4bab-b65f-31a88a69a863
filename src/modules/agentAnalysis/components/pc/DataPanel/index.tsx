/**
 * @file 智能体分析-数据看板
 * <AUTHOR>
 * @update <EMAIL> 2025.1 数据统计口径修改
 */
import React, {useCallback, useEffect, useState} from 'react';
import {RangePickerProps} from 'antd/es/date-picker';
import dayjs from 'dayjs';
import classNames from 'classnames';
import {message, RadioChangeEvent} from 'antd';
import {DefaultOptionType} from 'antd/es/cascader';
import {CategoryTabs} from '@/modules/center/components/Tags';
import Loading from '@/components/Loading';
import {LoadingSize} from '@/components/Loading/interface';
import Empty from '@/components/Empty';
import api from '@/api/agentAnalysis';
import ExportFileApi from '@/utils/file';
import {AnalysisTabType, IndicatorKeyEnum} from '@/api/agentAnalysis/interface';
import {formatSourceSelectOption} from '@/modules/agentAnalysis/utils';
import {getFeatureAccess} from '@/api/agentEditV2';
import {FeatureName} from '@/api/agentEditV2/interface';
import AnalysisDatePicker, {DateRangePickerTriggerType} from '@/components/RangeDatePicker';
import useAgentStatistics from '@/modules/agentAnalysis/hooks/useAgentStatistics';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {LogAnalysisTabTypeMap} from '@/modules/agentAnalysis/interface';
import {PAGE_SIZE} from '../interface';
import {
    DefAnalysisDataTabs,
    IndicatorRadioOptions,
    AnalysisDataTabs,
    SourceOptionAll,
    SourceSelectValue,
} from './interface';
import AnalysisTable from './AnalysisTable';
import GoodsIncomeEntry from './GoodsIncomeEntry';
import AnalysisChartCard from './AnalysisChartCard';
import OverviewCards from './OverviewCards';
import {AgentTableColumns} from './config';

// 数据分析时间范围选择默认近7日
const DefaultRecentDays = 7;

export default function DataPanel({appId, containerRef}: {appId: string; containerRef: any}) {
    // 范围时间选择器值
    const [dateRangeValue, setDateRangeValue] = useState<RangePickerProps['value']>([
        dayjs().subtract(DefaultRecentDays, 'day'),
        dayjs().subtract(1, 'day'),
    ]);
    // 下拉时间选择器值
    const [recentDays, setRecentDays] = useState(`${DefaultRecentDays}`);

    const [analysisDataTabs, setAnalysisDataTabs] = useState(DefAnalysisDataTabs);
    const [activeTab, setActiveTab] = useState<AnalysisTabType>(AnalysisTabType.Conversation);

    const [sourceValue, setSourceValue] = useState<SourceSelectValue>([SourceOptionAll.value]);
    const [sourceSelectOption, setSourceSelectOption] = useState<DefaultOptionType[]>([SourceOptionAll]);
    /** 缓存数据来源渠道列表数据（商品的来源渠道） */
    const [goodsSourceOption, setGoodsSourceOption] = useState<DefaultOptionType[]>([SourceOptionAll]);

    // 数据指标(默认第一个选项)
    const [indicatorRadioKey, setIndicatorRadioKey] = useState<IndicatorKeyEnum>(
        IndicatorRadioOptions[activeTab][0].value
    );

    const {clickLog} = useUbcLogV3();

    const {
        generalSourceOptions,
        lineChartRef,
        lineChartLoading,
        lineChartData,
        getLineChartData,
        tableLoading,
        tableData,
        getTableData,
        getAnalysisData,
        renderChartByIndicatorKey,
    } = useAgentStatistics({
        appId,
        dateRangeValue,
        activeTab,
        sourceValue,
        containerRef,
        indicatorRadioKey,
        setIndicatorRadioKey,
    });

    // 下载表格数据
    const downloadTableData = useCallback(() => {
        if (!appId) {
            return;
        }

        api.downloadStatisticsDetailList(activeTab, {
            appId,
            startTime: dateRangeValue?.[0]?.unix()!,
            endTime: dateRangeValue?.[1]?.unix()!,
            firstSource: sourceValue?.[0] ? `${sourceValue[0]}` : undefined,
            secondSource: sourceValue?.[0] ? `${sourceValue[1]}` : undefined,
        })
            .then(res => {
                if (res) {
                    ExportFileApi.downloadFile({url: res});
                    message.info('文件生成中，需要等待30s-5min');
                } else {
                    message.error('下载失败');
                }
            })
            .catch(err => {
                message.error(err.msg || '下载失败');
            });

        clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_DOWNLOAD);
    }, [activeTab, appId, dateRangeValue, sourceValue, clickLog]);

    // 数据指标Radio change，重新绘制选中指标 lineChart
    const onIndicatorRadioKeyChange = useCallback(
        (e: RadioChangeEvent) => {
            setIndicatorRadioKey(e.target.value);

            renderChartByIndicatorKey(e.target.value);

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_METRIC);
        },
        [renderChartByIndicatorKey, clickLog]
    );

    // table-page 切换查询数据
    const setTablePage = useCallback(
        (page: number) => {
            getTableData(activeTab, sourceValue, {
                pageNo: page,
                pageSize: PAGE_SIZE,
            });
        },
        [getTableData, activeTab, sourceValue]
    );

    // 获取商品分析渠道列表数据
    const getGoodsStatisticsSource = useCallback(() => {
        api.getAgentGoodsStatisticsSource().then(res => {
            setGoodsSourceOption(formatSourceSelectOption(res));
        });
    }, []);

    // 是否展示商品分析
    useEffect(() => {
        (async () => {
            try {
                // 是有商品挂载白名单权益
                const {goods_mount: hasGoodsMount} = await getFeatureAccess({
                    featureName: FeatureName.GoodsMount,
                });

                if (hasGoodsMount) {
                    // 有则展示商品分析Tab
                    setAnalysisDataTabs(AnalysisDataTabs);
                    // 获取商品数据来源渠道列表
                    getGoodsStatisticsSource();
                }
            } catch (err) {
                console.error(err);
            }
        })();
    }, [getGoodsStatisticsSource]);

    // 设置来源渠道值
    useEffect(() => {
        setSourceSelectOption(activeTab === AnalysisTabType.Goods ? goodsSourceOption : generalSourceOptions);
    }, [activeTab, generalSourceOptions, goodsSourceOption]);

    // 用户分析、流量分析 等 tab 切换事件
    const onTabChange = useCallback(
        async (tabType: AnalysisTabType) => {
            const preActiveTab = activeTab;

            setActiveTab(tabType);
            setSourceValue([SourceOptionAll.value]);

            const currIndicatorRadioKey = IndicatorRadioOptions[tabType][0].value;
            setIndicatorRadioKey(currIndicatorRadioKey);

            // 从其他Tab切换到商品分析时，或者从商品分析切换到其他分析时，重新请求图表接口
            if (tabType === AnalysisTabType.Goods || preActiveTab === AnalysisTabType.Goods) {
                getLineChartData(tabType, [SourceOptionAll.value], currIndicatorRadioKey);
            }
            // 非商品分析Tab之间的切换不用请求图表接口，直接从缓存数据绘制图表即可
            else {
                renderChartByIndicatorKey(currIndicatorRadioKey);
            }

            getTableData(tabType, [SourceOptionAll.value], {
                pageNo: 1,
                pageSize: PAGE_SIZE,
            });

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_OPTION, {
                eAnalysisOption: LogAnalysisTabTypeMap[tabType],
            });
        },
        [activeTab, getLineChartData, getTableData, renderChartByIndicatorKey, clickLog]
    );

    // 渠道选择器切换事件
    const onSourceChange = useCallback(
        (value: Array<string | number>) => {
            // 在智能体中为数字，在插件中为 string
            setSourceValue(value);

            getLineChartData(activeTab, value, indicatorRadioKey);

            getTableData(activeTab, value, {
                pageNo: 1,
                pageSize: PAGE_SIZE,
            });

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_CHANNEL);
        },
        [getLineChartData, activeTab, indicatorRadioKey, getTableData, clickLog]
    );

    const getTriggerType = useCallback(
        (type: DateRangePickerTriggerType, days: string) => {
            // 1: 最近7天 2: 最近14天 3: 最近30天 7: 昨日 8: 今日 9: 自定义 10: 最近60天
            const extEDateFilterType = {'7': 1, '14': 2, '30': 3, '60': 10, '1': 7, '0': 8, '': 9} as const;

            clickLog(
                type === 'rangePicker'
                    ? EVENT_VALUE_CONST.AGENT_ANALYSIS_DYNAMIC_DATE_FILTER
                    : EVENT_VALUE_CONST.AGENT_ANALYSIS_STATIC_DATE_FILTER,
                {eDateFilterType: extEDateFilterType[days as keyof typeof extEDateFilterType]}
            );
        },
        [clickLog]
    );

    return (
        <div id="summary">
            <div>
                <div className="mb-8 flex items-center justify-between">
                    <div className="flex items-center">
                        <span className="text-gray-base text-lg font-medium leading-none">数据分析</span>
                        <span className="ml-1 text-gray-tertiary">中午12点前更新昨日数据</span>
                    </div>
                </div>
                <OverviewCards appId={appId} />
            </div>
            <div className="relative mt-8">
                <div className="absolute right-0 top-[-3px]">
                    <AnalysisDatePicker
                        dateRangeValue={dateRangeValue}
                        recentDays={recentDays}
                        setRecentDays={setRecentDays}
                        setDateRangeValue={setDateRangeValue}
                        getAnalysisData={getAnalysisData}
                        getTriggerType={getTriggerType}
                    />
                </div>
                {/* 图表/表格 */}
                <AnalysisChartCard
                    tabs={
                        <CategoryTabs
                            tabs={analysisDataTabs}
                            activeTab={activeTab}
                            switchTab={onTabChange}
                            tagBgLightBlue
                        />
                    }
                    dataIndex={indicatorRadioKey}
                    indexRadioOption={IndicatorRadioOptions[activeTab]}
                    sourceSelectOption={sourceSelectOption}
                    source={sourceValue}
                    downloadDisabled={!appId || !lineChartData?.dateList?.length}
                    downloadData={downloadTableData}
                    onIndexChange={onIndicatorRadioKeyChange}
                    onSourceChange={onSourceChange}
                    hasTable={AgentTableColumns[activeTab]?.length > 0}
                    chartChildren={
                        <>
                            <div
                                className={classNames(
                                    {
                                        hidden: !lineChartLoading,
                                    },
                                    'relative h-[290px] w-full'
                                )}
                            >
                                <Loading size={LoadingSize.small} />
                            </div>
                            <div
                                className={classNames('break-all pt-[14px] xlg:items-center xlg:justify-between', {
                                    hidden: !lineChartData?.dateList?.length || lineChartLoading,
                                    'xlg:flex': lineChartData?.dateList?.length && !lineChartLoading,
                                })}
                            >
                                <div ref={lineChartRef} className="inline-block h-[270px]"></div>
                            </div>
                            <div
                                className={classNames('relative h-[290px] w-full', {
                                    hidden: lineChartData?.dateList?.length || lineChartLoading,
                                })}
                            >
                                <Empty />
                            </div>
                        </>
                    }
                    tableChildren={
                        AgentTableColumns[activeTab]?.length ? (
                            <AnalysisTable
                                type={activeTab}
                                data={tableData}
                                setPage={setTablePage}
                                loading={tableLoading}
                            />
                        ) : null
                    }
                    thirdEntry={activeTab === AnalysisTabType.Goods ? <GoodsIncomeEntry /> : null}
                />
            </div>
        </div>
    );
}
