/**
 * 线索数据
 * @description 跳转收益-线索收集Tab页面
 * <AUTHOR> 2024.7
 */

import {useCallback, useMemo} from 'react';
import {Button} from 'antd';
import urls from '@/links';
import {Tabs<PERSON>ey} from '@/modules/income/config';
import {LJExtData} from '@/utils/loggerV2/utils';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {TabKey} from '@/modules/agentAnalysis/components/pc/interface';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';

export default function BusinessLeadAnalysis() {
    const {clickLog} = useUbcLogV3();

    const baseExt = useExtLog();
    const extLog = useMemo(() => {
        return {
            ...baseExt,
            [EVENT_EXT_KEY_CONST.ANALYSIS_TAB_NAME]: +TabKey.data,
        } as LJExtData;
    }, [baseExt]);

    const goToMyIncome = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.CLUE_FULL_FUNCTION, extLog);

        window.open(`${urls.income.raw()}?activeTab=${TabsKey.MountLead}`);
    }, [extLog, clickLog]);

    return (
        <>
            {baseExt.agent_id ? (
                <div className="rounded-[0.875rem] bg-white p-6">
                    <div className="flex items-center">
                        <span className="text-base font-medium">线索数据趋势</span>
                        <span className="ml-4 text-sm">
                            <span>前往我的收益-线索收集页面</span>
                            {/* 接口静默获取ucId，ucId获取失败 查看置灰 */}
                            <Button type="link" className="mb-0 h-auto border-0 p-0 font-normal" onClick={goToMyIncome}>
                                查看完整数据
                            </Button>
                        </span>
                    </div>
                </div>
            ) : null}
        </>
    );
}
