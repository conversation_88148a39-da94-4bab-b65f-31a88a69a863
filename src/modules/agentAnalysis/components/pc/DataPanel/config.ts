import {ColumnsType} from 'antd/es/table';
import {
    AnalysisTabType,
    BehaviorIndicators,
    ConversationIndicators,
    FlowIndicators,
    IndicatorCnNameMap,
    IndicatorKeyEnum,
    UserIndicators,
} from '@/api/agentAnalysis/interface';

/**
 * 表格列数据-用户分析
 */
const UserTableColumns: ColumnsType<UserIndicators> = [
    {
        title: '日期',
        dataIndex: 'date',
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.NewUv],
        dataIndex: IndicatorKeyEnum.NewUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.NewUvRatio],
        dataIndex: IndicatorKeyEnum.NewUvRatio,
    },
    // 留存率指标暂时不展示（后端暂时拿不到数据），后续根据需求再添加回来
    // {
    //     title: IndicatorCnNameMap[IndicatorKeyEnum.DayRetainedRatio],
    //     dataIndex: IndicatorKeyEnum.DayRetainedRatio,
    // },
    // {
    //     title: IndicatorCnNameMap[IndicatorKeyEnum.WeekRetainedRatio],
    //     dataIndex: IndicatorKeyEnum.WeekRetainedRatio,
    // },
    // {
    //     title: IndicatorCnNameMap[IndicatorKeyEnum.MonthRetainedRatio],
    //     dataIndex: IndicatorKeyEnum.MonthRetainedRatio,
    // },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.ReturningRatio],
        dataIndex: IndicatorKeyEnum.ReturningRatio,
    },
];

/**
 * 表格列数据-流量分析
 */
const TrafficTableColumns: ColumnsType<FlowIndicators> = [
    {
        title: '日期',
        dataIndex: 'date',
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.DistributePv],
        dataIndex: IndicatorKeyEnum.DistributePv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.DistributeUv],
        dataIndex: IndicatorKeyEnum.DistributeUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.ClickPv],
        dataIndex: IndicatorKeyEnum.ClickPv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.ClickUv],
        dataIndex: IndicatorKeyEnum.ClickUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.StartPv],
        dataIndex: IndicatorKeyEnum.StartPv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.StartUv],
        dataIndex: IndicatorKeyEnum.StartUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.UseTimePerson],
        dataIndex: IndicatorKeyEnum.UseTimePerson,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.UseTimeRound],
        dataIndex: IndicatorKeyEnum.UseTimeRound,
    },
];

/**
 * 表格列数据-对话分析
 */
const ConversationTableColumns: ColumnsType<ConversationIndicators> = [
    {
        title: '日期',
        dataIndex: 'date',
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.Rounds],
        dataIndex: IndicatorKeyEnum.Rounds,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.RoundsUv],
        dataIndex: IndicatorKeyEnum.RoundsUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.AvgUserRounds],
        dataIndex: IndicatorKeyEnum.AvgUserRounds,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.AvgRounds],
        dataIndex: IndicatorKeyEnum.AvgRounds,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.SatisfactionRate],
        dataIndex: IndicatorKeyEnum.SatisfactionRate,
    },
];

/**
 * 表格列数据-行为分析
 */
const ActionTableColumns: ColumnsType<BehaviorIndicators> = [
    {
        title: '日期',
        dataIndex: 'date',
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.TopNum],
        dataIndex: IndicatorKeyEnum.TopNum,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.TopUserNum],
        dataIndex: IndicatorKeyEnum.TopUserNum,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.TapNum],
        dataIndex: IndicatorKeyEnum.TapNum,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.TapUserNum],
        dataIndex: IndicatorKeyEnum.TapUserNum,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.ClickSpeechPv],
        dataIndex: IndicatorKeyEnum.ClickSpeechPv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.ClickSpeechUv],
        dataIndex: IndicatorKeyEnum.ClickSpeechUv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.AiCallPv],
        dataIndex: IndicatorKeyEnum.AiCallPv,
    },
    {
        title: IndicatorCnNameMap[IndicatorKeyEnum.AiCallUv],
        dataIndex: IndicatorKeyEnum.AiCallUv,
    },
];

const ActionTableGoods: ColumnsType<any> = [];

/**
 * 每个 tab 下的表格列数据
 */
export const AgentTableColumns = {
    [AnalysisTabType.User]: UserTableColumns,
    [AnalysisTabType.Traffic]: TrafficTableColumns,
    [AnalysisTabType.Conversation]: ConversationTableColumns,
    [AnalysisTabType.Action]: ActionTableColumns,
    [AnalysisTabType.Goods]: ActionTableGoods,
};
