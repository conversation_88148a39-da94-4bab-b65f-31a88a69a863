/**
 * @file 智能体数据分析相关类型定义
 * <AUTHOR>
 */

import echarts from '@/utils/echarts';
import themeConfig from '@/styles/lingjing-light-theme';
import {IndicatorCnNameMap, IndicatorKeyEnum} from '@/api/agentAnalysis/interface';

/**
 * 数据分析 tab 类型，便于从 AnalysisTabTypeCn 中直接获取，因此值改为大写开头
 */
export enum AnalysisTabType {
    /**
     * 数据概览
     */
    Summary = 'Summary',
    /**
     * 流量分析
     */
    Traffic = 'Traffic',
    /**
     * 用户分析
     */
    User = 'User',
    /**
     * 对话分析
     */
    Conversation = 'Conversation',
    /**
     * 行为分析
     */
    Action = 'Action',
    /**
     * 商品分析
     */
    Goods = 'Goods',
}

export const AnalysisTabTypeCn = {
    [AnalysisTabType.Summary]: '数据概览',
    [AnalysisTabType.Traffic]: '流量分析',
    [AnalysisTabType.User]: '用户分析',
    [AnalysisTabType.Conversation]: '对话分析',
    [AnalysisTabType.Action]: '行为分析',
    [AnalysisTabType.Goods]: '商品分析',
};

// todo根据具体形态计算
export const TabScrollHeight = {
    [AnalysisTabType.Summary]: 245,
    [AnalysisTabType.Traffic]: 425,
    [AnalysisTabType.User]: 845,
    [AnalysisTabType.Conversation]: 0,
    [AnalysisTabType.Action]: 0,
    [AnalysisTabType.Goods]: 0,
};

/**
 * 智能体曲线图样式
 */
export const AgentLineChartLineStyle = {
    lineStyle: {
        color: themeConfig.token.colorPrimary,
        width: 4,
        type: 'solid',
    },
    itemStyle: {
        color: themeConfig.token.colorPrimary, // 设置点的颜色，与线的颜色一致
        borderColor: '#fff',
        borderWidth: 2,
        emphasis: {
            borderColor: themeConfig.token.colorPrimary,
            color: '#fff',
        },
    },
    symbol: 'circle',
    symbolSize: 8,
    areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
                offset: 0,
                color: 'rgba(26, 70, 255, 0.1)',
            },
            {
                offset: 1,
                color: 'rgba(26, 70, 255, 0)',
            },
        ]),
    },
    showAllSymbol: false,
};

export interface TabItem {
    id: AnalysisTabType;
    description: string;
}

/**
 * 智能体数据分析 tab（对话分析、流量分析、用户分析、行为分析）
 */
export const DefAnalysisDataTabs: TabItem[] = [
    {id: AnalysisTabType.Conversation, description: AnalysisTabTypeCn.Conversation},
    {id: AnalysisTabType.Traffic, description: AnalysisTabTypeCn.Traffic},
    {id: AnalysisTabType.User, description: AnalysisTabTypeCn.User},
    {id: AnalysisTabType.Action, description: AnalysisTabTypeCn.Action},
];

/**
 * 智能体数据分析 tab（包含商品分析）
 */
export const AnalysisDataTabs: TabItem[] = DefAnalysisDataTabs.concat([
    {
        id: AnalysisTabType.Goods,
        description: AnalysisTabTypeCn.Goods,
    },
]);

/**
 * 每个 tab 下的曲线图的指标，用于切换曲线图的指标
 */
export const IndicatorRadioOptions = {
    [AnalysisTabType.Conversation]: [
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.Rounds],
            value: IndicatorKeyEnum.Rounds,
            tips: '统计时间内，用户发起提问的次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.RoundsUv],
            value: IndicatorKeyEnum.RoundsUv,
            tips: '统计时间内，发起提问的用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.AvgUserRounds],
            value: IndicatorKeyEnum.AvgUserRounds,
            tips: '统计时间内，对话次数/对话用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.AvgRounds],
            value: IndicatorKeyEnum.AvgRounds,
            tips: '统计时间内，对话次数/智能体启动次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.SatisfactionRate],
            value: IndicatorKeyEnum.SatisfactionRate,
            tips: '统计时间内，点赞对话数/（点赞对话数+点踩对话数）',
        },
    ],
    [AnalysisTabType.Traffic]: [
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.DistributePv],
            value: IndicatorKeyEnum.DistributePv,
            tips: '统计时间内，百度域内各分发场景中，智能体的展现次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.DistributeUv],
            value: IndicatorKeyEnum.DistributeUv,
            tips: '统计时间内，百度域内各分发场景中，智能体的展现用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.ClickPv],
            value: IndicatorKeyEnum.ClickPv,
            tips: '统计时间内，百度域内各分发场景中，智能体的点击次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.ClickUv],
            value: IndicatorKeyEnum.ClickUv,
            tips: '统计时间内，百度域内各分发场景中，智能体的点击用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.StartPv],
            value: IndicatorKeyEnum.StartPv,
            tips: '统计时间内，启动智能体的次数，包括打开智能体落地页调起智能体',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.StartUv],
            value: IndicatorKeyEnum.StartUv,
            tips: '统计时间内，启动智能体的用户数，包括打开智能体落地页调起智能体',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.UseTimePerson],
            value: IndicatorKeyEnum.UseTimePerson,
            tips: '统计时间内，平均每个用户使用智能体的时长，等于总时长/总启动用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.UseTimeRound],
            value: IndicatorKeyEnum.UseTimeRound,
            tips: '统计时间内，平均每一次启动智能体的时间，等于总时长/总启动次数',
        },
    ],
    [AnalysisTabType.User]: [
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.NewUv],
            value: IndicatorKeyEnum.NewUv,
            tips: '统计开始时间起，90天内首次与智能体对话的用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.NewUvRatio],
            value: IndicatorKeyEnum.NewUvRatio,
            tips: '统计时间内，新用户数/对话用户数',
        },
        // 留存率指标暂时不展示（后端暂时拿不到数据），后续根据需求再添加回来
        // {
        //     label: IndicatorCnNameMap[IndicatorKeyEnum.DayRetainedRatio],
        //     value: IndicatorKeyEnum.DayRetainedRatio,
        //     tips: '统计时间内，1天前的对话用户中，在统计当日仍然发起对话的用户占比',
        // },
        // {
        //     label: IndicatorCnNameMap[IndicatorKeyEnum.WeekRetainedRatio],
        //     value: IndicatorKeyEnum.WeekRetainedRatio,
        //     tips: '统计时间内，8天前的对话用户中，在统计当日仍然发起对话的用户占比',
        // },
        // {
        //     label: IndicatorCnNameMap[IndicatorKeyEnum.MonthRetainedRatio],
        //     value: IndicatorKeyEnum.MonthRetainedRatio,
        //     tips: '统计时间内，30天前的对话用户中，在统计当日仍然发起对话的用户占比',
        // },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.ReturningRatio],
            value: IndicatorKeyEnum.ReturningRatio,
            tips: '统计时间内，91天前与智能体对话，近90天内没有对话，但是统计当日有对话的用户数，在当日对话用户数中的占比',
        },
    ],
    [AnalysisTabType.Action]: [
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.TopNum],
            value: IndicatorKeyEnum.TopNum,
            tips: '统计时间内，点击点赞按钮次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.TopUserNum],
            value: IndicatorKeyEnum.TopUserNum,
            tips: '统计时间内，点击过点赞按钮的用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.TapNum],
            value: IndicatorKeyEnum.TapNum,
            tips: '统计时间内，点击点踩按钮次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.TapUserNum],
            value: IndicatorKeyEnum.TapUserNum,
            tips: '统计时间内，点击过点踩的用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.ClickSpeechPv],
            value: IndicatorKeyEnum.ClickSpeechPv,
            tips: '统计时间内，点击语音播报次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.ClickSpeechUv],
            value: IndicatorKeyEnum.ClickSpeechUv,
            tips: '统计时间内，点击语音播报的用户数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.AiCallPv],
            value: IndicatorKeyEnum.AiCallPv,
            tips: '统计时间内，点击AI通话按钮次数',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.AiCallUv],
            value: IndicatorKeyEnum.AiCallUv,
            tips: '统计时间内，点击AI通话按钮的用户数',
        },
    ],
    [AnalysisTabType.Goods]: [
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.Sales],
            value: IndicatorKeyEnum.Sales,
            tips: '',
        },
        {
            label: IndicatorCnNameMap[IndicatorKeyEnum.Amount],
            value: IndicatorKeyEnum.Amount,
            tips: '',
        },
    ],
};

export type SourceSelectValue = Array<string | number>;

/**
 * 渠道来源下拉选项中的 "全部" 选项
 */
export const SourceOptionAll = {
    label: '全部',
    value: '',
};
