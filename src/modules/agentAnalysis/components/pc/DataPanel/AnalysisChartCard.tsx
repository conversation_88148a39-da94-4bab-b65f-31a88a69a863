/* eslint-disable complexity */
/**
 * @file 数据分析图表卡片
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import {Button, ConfigProvider, Radio, Space, Tooltip} from 'antd';
import type {RadioChangeEvent} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {DefaultOptionType} from 'antd/es/cascader';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {SourceSelectValue} from '@/modules/agentAnalysis/components/pc/DataPanel/interface';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import Cascader from '@/components/Cascader';
import {ChartTableType} from '../interface';

const StyledRadioGroup = styled(Radio.Group)`
    .ant-radio-button-wrapper {
        border-color: #bfc3cd !important;
    }
    .ant-radio-button-wrapper-checked {
        border-color: ${ThemeConfig.token.colorPrimary} !important;
    }
`;

const getButtons = (
    chartTableType: ChartTableType,
    downloadData: () => void,
    onChartTableChange: (value: RadioChangeEvent) => void,
    /** 下载按钮是否置灰 */
    downloadDisabled: boolean
) => (
    <div className="z-0">
        {chartTableType === ChartTableType.Table && (
            <Button
                onClick={downloadData}
                className="mr-3 h-[30px] rounded-full px-[15px] py-[5px] leading-none"
                disabled={downloadDisabled}
            >
                下载
            </Button>
        )}
        <ConfigProvider theme={{token: {borderRadius: 9}}}>
            <StyledRadioGroup onChange={onChartTableChange} value={chartTableType}>
                <Radio.Button value={ChartTableType.Chart}>图表</Radio.Button>
                <Radio.Button value={ChartTableType.Table}>表格</Radio.Button>
            </StyledRadioGroup>
        </ConfigProvider>
    </div>
);

export default function AnalysisChartCard({
    cardTitle,
    tabs,
    title,
    dataIndex,
    source,
    indexRadioOption,
    sourceSelectOption,
    downloadDisabled,
    downloadData,
    onIndexChange,
    onSourceChange,
    chartChildren,
    tableChildren,
    hasTable = true,
    thirdEntry,
}: {
    cardTitle?: string;
    tabs?: React.ReactNode;
    title?: string;
    dataIndex: string;
    source?: SourceSelectValue;
    indexRadioOption: Array<{value: string; label: string; tips?: string}>;
    sourceSelectOption?: DefaultOptionType[];
    /** 下载按钮是否置灰，无图表数据时置灰 */
    downloadDisabled: boolean;
    downloadData: () => void;
    onSourceChange?: (value: SourceSelectValue) => void;
    onIndexChange: (value: RadioChangeEvent) => void;
    chartChildren?: React.ReactNode;
    tableChildren?: React.ReactNode;
    // 是否有表格
    hasTable?: boolean;
    thirdEntry?: React.ReactNode;
}) {
    const [chartTableType, setChartTableType] = useState(ChartTableType.Chart);

    const {clickLog} = useUbcLogV3();

    // 图表切换tab
    const onChartTableChange = useCallback(
        (e: RadioChangeEvent) => {
            setChartTableType(e.target.value as ChartTableType);

            clickLog(EVENT_VALUE_CONST.AGENT_ANALYSIS_DISPLAY, {
                eAnalysisDisplayType: e.target.value === ChartTableType.Table ? 2 : 1,
            });
        },
        [setChartTableType, clickLog]
    );

    useEffect(() => {
        // 如果不存在表格，重置图表切换tab为选中图表
        if (!hasTable) {
            setChartTableType(ChartTableType.Chart);
        }
    }, [hasTable]);

    return (
        <div className={`rounded-[0.875rem] bg-white`}>
            {cardTitle && <div className="mb-6 text-base font-medium">{cardTitle}</div>}
            <div className="flex items-center justify-between">
                {tabs && <div className="ml-[-4px] font-medium">{tabs}</div>}
                {title && title.length > 0 && <span className="mb-4 font-medium">{title}</span>}
            </div>
            {/* 图表切换内容区 */}
            {/* 1.图表筛选区 */}
            {/* 数据指标-每个card不同 */}
            <div className="flex justify-between">
                <div>
                    <div
                        className={classNames('mt-8 flex', {
                            hidden: chartTableType === ChartTableType.Table,
                        })}
                    >
                        <span className="mr-6 flex-shrink-0 text-sm text-gray-tertiary">数据指标</span>
                        <Radio.Group onChange={onIndexChange} value={dataIndex}>
                            <Space size={[0, 18]} wrap>
                                {indexRadioOption.map(radio => (
                                    <Radio value={radio.value} key={radio.label}>
                                        <Space size={3} align="center" className="leading-none">
                                            {radio.label}
                                            {radio.tips && (
                                                <Tooltip title={radio.tips} arrow={false}>
                                                    <span className="iconfont icon-questionCircle text-[15px] leading-none text-gray-tertiary"></span>
                                                </Tooltip>
                                            )}
                                        </Space>
                                    </Radio>
                                ))}
                            </Space>
                        </Radio.Group>
                    </div>
                    {sourceSelectOption?.length ? (
                        <div
                            className={classNames('', {
                                'mt-[18px]': chartTableType === ChartTableType.Chart,
                                'mt-6': chartTableType === ChartTableType.Table,
                            })}
                        >
                            <span className="mr-6 text-sm text-gray-tertiary">来源渠道</span>
                            <Cascader
                                value={source}
                                options={sourceSelectOption}
                                placeholder="请选择来源渠道"
                                onChange={onSourceChange}
                                allowClear={false}
                            />
                        </div>
                    ) : null}
                </div>
                {hasTable && (
                    <div className="mt-7 flex-shrink-0">
                        {getButtons(chartTableType, downloadData, onChartTableChange, downloadDisabled)}
                    </div>
                )}
                {thirdEntry}
            </div>
            <div className={chartTableType === ChartTableType.Chart ? '' : 'hidden'}>
                {/* 图表容器 */}
                {chartChildren}
            </div>
            <div
                className={classNames('pt-6', {
                    hidden: chartTableType === ChartTableType.Chart,
                })}
            >
                {/* 表格容器 */}
                {tableChildren}
            </div>
        </div>
    );
}
