/**
 * 商品挂载-带货收益-查看入口
 */
import {Button} from 'antd';
import {useCallback, useMemo} from 'react';
import {LJExtData} from '@/utils/loggerV2/utils';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {TabKey} from '@/modules/agentAnalysis/components/pc/interface';
import {EVENT_EXT_KEY_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';

export default function GoodsThirdAnalysis() {
    const {clickLog} = useUbcLogV3();

    const baseExt = useExtLog();
    const extLog = useMemo(() => {
        return {
            ...baseExt,
            [EVENT_EXT_KEY_CONST.ANALYSIS_TAB_NAME]: +TabKey.data,
        } as LJExtData;
    }, [baseExt]);

    // 百家号-收益广场-内容电商 无线下地址
    const entryUrl = 'https://baijiahao.baidu.com/builder/rc/income/personal/content_goods';

    const handleToBaijiahao = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.PRODUCT_FULL_FUNCTION, extLog);
        window.open(entryUrl);
    }, [extLog, clickLog]);

    return (
        <span className="ml-4 mt-8 text-sm">
            <span>前往百家号</span>
            <Button
                type="link"
                target="_blank"
                href={entryUrl}
                onClick={handleToBaijiahao}
                disabled={!entryUrl}
                className="mb-0 h-auto border-0 p-0 font-normal"
            >
                查看完整功能
            </Button>
        </span>
    );
}
