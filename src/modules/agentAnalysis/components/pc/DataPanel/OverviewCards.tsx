/**
 * @file 数据分析-累计数据概览
 * <AUTHOR>
 */

import React from 'react';
import {Flex, Tag, Tooltip} from 'antd';
import styled from '@emotion/styled';
import useStatisticsOverview from '@/modules/agentAnalysis/hooks/useStatisticsOverview';
import {OverviewCardInfo, TrendType} from '@/modules/agentAnalysis/interface';

const DATA_STATUS_COLOR_MAP = {
    [TrendType.Up]: 'success',
    [TrendType.Down]: 'error',
    [TrendType.Same]: 'default',
};

export const StyledTag = styled(Tag)`
    &.ant-tag {
        margin-inline-end: 0;
    }
`;

/**
 * 数据分析-概览卡片
 *
 * @param cardInfo 卡片数据
 * @constructor
 */
function OverviewCard({cardInfo}: {cardInfo: OverviewCardInfo}) {
    return (
        <div className="h-auto flex-1 self-stretch rounded-[0.75rem] border border-gray-border-secondary p-4 text-gray-secondary [&:nth-child(4n)]:mr-[0px]">
            <Flex className="mb-[6px]">
                <span className="text-base">{cardInfo.name}</span>
                <Tooltip title={cardInfo.tips} arrow={false}>
                    <span className="iconfont icon-questionCircle ml-1 text-flow-hover"></span>
                </Tooltip>
            </Flex>
            <div>
                <div className="flex items-center justify-between">
                    <Flex className="whitespace-nowrap leading-none" align="center">
                        <span className="mr-1 text-[30px] font-medium leading-[36px] text-black">
                            {cardInfo.value || '--'}
                        </span>
                        <span className="text-sm">{cardInfo.unit}</span>
                    </Flex>
                    <Flex className="text-sm">
                        <span className="text-gray-tertiary">周：</span>
                        {/* 状态，cardInfo.value 有数据、但环比没数据时显示 "暂无环比" */}
                        {cardInfo.trendValue || cardInfo.value ? (
                            <Tooltip title="环比展示设定为与上周进行对比">
                                <StyledTag
                                    color={DATA_STATUS_COLOR_MAP[cardInfo.trend]}
                                    bordered={false}
                                    className="font-medium"
                                >
                                    {cardInfo.trendValue ? cardInfo.trendValue : '暂无环比'}
                                </StyledTag>
                            </Tooltip>
                        ) : null}
                    </Flex>
                </div>
                <div className="mt-4">{cardInfo.rankValue}</div>
            </div>
        </div>
    );
}

export default function OverviewCards({appId}: {appId: string}) {
    const {overviewCardData} = useStatisticsOverview(appId);

    return (
        <Flex gap={16}>
            {overviewCardData.map(item => (
                <OverviewCard key={item.name} cardInfo={item} />
            ))}
        </Flex>
    );
}
