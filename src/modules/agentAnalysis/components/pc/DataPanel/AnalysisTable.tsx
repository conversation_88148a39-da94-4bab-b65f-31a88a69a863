/**
 * @file 数据分析-用户分析表格数据
 * <AUTHOR>
 */
import React from 'react';
import Table from '@/components/Table';
import {AllIndicatorsData, AnalysisTabType} from '@/api/agentAnalysis/interface';
import {AgentTableColumns} from './config';

export default function AnalysisTable({
    type,
    loading,
    data,
    setPage,
}: {
    type: AnalysisTabType;
    loading: boolean;
    data: {
        dataList: AllIndicatorsData[];
        total: number;
        pageSize: number;
        pageNo: number;
    };
    setPage: (page: number) => void;
}) {
    return (
        <Table
            rowKey="date"
            columns={AgentTableColumns[type]}
            loading={loading}
            dataSource={data.dataList}
            pagination={{
                total: data.total,
                pageSize: data.pageSize,
                current: data.pageNo,
                onChange: setPage,
                hideOnSinglePage: true,
                // 不展示pageSize设置（组件默认超过total>50展示）
                showSizeChanger: false,
            }}
            scroll={{
                x: 'max-content',
            }}
        />
    );
}
