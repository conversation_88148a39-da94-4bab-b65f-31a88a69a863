/**
 * @file 高频检索问题分析
 * <AUTHOR>
 */

import React, {useCallback, useState} from 'react';
import {Collapse, ConfigProvider, Flex, Tooltip, message} from 'antd';
import type {CollapseProps} from 'antd';
import styled from '@emotion/styled';
import isEmpty from 'lodash/isEmpty';
import {useNavigate} from 'react-router-dom';
import {QueryAnalysisItem} from '@/api/datasetRecallTest/interface';
import Tag from '@/components/Tag';
import urls from '@/links';
import api from '@/api/datasetRecallTest';
import ExportFileApi from '@/utils/file';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';
import {ToastType} from '@/modules/activity/masterRecruitment/hooks/useToast';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {IsCover, IsExpand} from '@/utils/loggerV2/interface';
import TextWithTooltip from './TextWithTooltip';
import TextAnalysisItem from './TextAnalysisItem';

// 高频检索问题显示下载按钮的最小数量
const MIN_ITEMS_FOR_DOWNLOAD = 10;
// 来源分析页面的标志
const FROM_ANALYSIS_FLAG = '1';
interface Props {
    queryAnalysis: QueryAnalysisItem[];
    appId?: string;
    setOpen?: (flag: boolean) => void;
}

const StyledCollapse = styled(Collapse)`
    .ant-collapse-header {
        padding: 4px 10px !important;
        border-radius: 9px !important;
        &:hover {
            background-color: #f8faff !important;
        }
    }
    .ant-collapse-header-text {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .ant-collapse-item {
        margin-bottom: 14px !important;
    }
    .ant-collapse-content-box {
        padding-top: 8px !important;
    }
`;

export default function QueryAnalysis({queryAnalysis, appId, setOpen}: Props) {
    const navigate = useNavigate();
    const {clickLog} = useUbcLogV3();
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

    // 跳转到知识库分段管理页
    const handleGoToDetail = useCallback((datasetId: string, fileId: string, textId: string) => {
        return () => {
            const basePath = urls.datasetParagraphDetail.fill({id: datasetId});
            const searchParams = new URLSearchParams({
                fileId,
                textId,
                fromAnalysis: FROM_ANALYSIS_FLAG, // 来源标志
            });
            // 在新标签页中打开
            window.open(`${basePath}?${searchParams?.toString() || ''}`, '_blank');
        };
    }, []);

    // 下载全部数据
    const downloadAllData = useCallback(() => {
        if (!appId) {
            return;
        }

        // 点击下载埋点
        clickLog(EVENT_VALUE_CONST.DOWNLOAD_REPOSITORY_QUESTION);

        api.downloadDatasetDetail({appId})
            .then(res => {
                if (res) {
                    ExportFileApi.downloadFile({url: res});
                    message.success('文件已下载');
                } else {
                    message.error('下载失败');
                }
            })
            .catch(err => {
                message.error(err.msg || '下载失败');
            });
    }, [appId, clickLog]);

    // 处理问题标题点击事件
    const handleQuestionClick = useCallback(
        (item: QueryAnalysisItem, index: number) => {
            return () => {
                const key = index.toString();
                const isCurrentlyExpanded = expandedKeys.includes(key);

                clickLog(EVENT_VALUE_CONST.REPOSITORY_QUESTION_EXPAND, {
                    isCover: item.cover ? IsCover.Covered : IsCover.NotCovered,
                    isExpand: isCurrentlyExpanded ? IsExpand.No : IsExpand.Yes,
                });
            };
        },
        [expandedKeys, clickLog]
    );

    // Collapse 展开/收起状态管理
    const handleCollapseChange = useCallback((activeKeys: string | string[]) => {
        const keys = Array.isArray(activeKeys) ? activeKeys : [activeKeys];
        setExpandedKeys(keys.filter(key => key !== undefined));
    }, []);

    // 跳转到编辑页面并打开创建知识库弹窗
    const handleUploadFile = useCallback(() => {
        if (!appId) return;

        navigate(`${urls.agentPromptEdit.raw()}?appId=${appId}&activeTab=${AgentTab.Create}`, {
            state: {
                openCreateDatasetModal: true, // 打开创建知识库弹窗的标志
                toastConfig: {
                    msg: '请补充知识库相关内容',
                    type: ToastType.INFO,
                },
            },
        });
        setOpen?.(false);
    }, [appId, navigate, setOpen]);

    // 自定义展开图标
    const renderExpandIcon = useCallback(
        ({isActive}: {isActive?: boolean}) => (
            <span className={`iconfont ${isActive ? 'icon-Up' : 'icon-Down'} text-base`} />
        ),
        []
    );

    const items: CollapseProps['items'] = queryAnalysis.map((item, idx) => ({
        key: idx.toString(),
        label: (
            <Flex align="center" gap={9} onClick={handleQuestionClick(item, idx)}>
                <TextWithTooltip className="text-black-base truncate">
                    Top{idx + 1}. {item.query}
                </TextWithTooltip>
                {!item.cover && (
                    <Tag className="text-[#E87400]" color="#FF82001A">
                        未覆盖
                    </Tag>
                )}
            </Flex>
        ),
        children: isEmpty(item?.textAnalysis) ? (
            <div className="rounded-xl bg-gray-card p-4 text-gray-tertiary">
                知识库暂未覆盖该问题，快去
                <span className="cursor-pointer text-primary" onClick={handleUploadFile}>
                    上传文件
                </span>
                补充内容吧～
            </div>
        ) : (
            <div className="rounded-xl bg-gray-card p-4">
                {(item?.textAnalysis || []).map((text, index) => (
                    <TextAnalysisItem
                        key={text.textId}
                        text={text}
                        index={index}
                        totalLength={item?.textAnalysis.length}
                        handleGoToDetail={handleGoToDetail}
                    />
                ))}
            </div>
        ),
    }));

    return (
        <ConfigProvider
            theme={{
                components: {
                    Collapse: {
                        headerBg: 'rgba(0, 0, 0, 0)',
                        headerPadding: '6px 0px',
                        contentPadding: '0px',
                        colorBorder: 'rgba(0, 0, 0, 0)',
                    },
                },
            }}
        >
            <Flex justify="space-between" align="center" className="pb-3 pt-6">
                <div className="text-lg font-medium leading-[26px]">高频检索问题分析</div>
                {items.length >= MIN_ITEMS_FOR_DOWNLOAD && (
                    <Tooltip title="点击可下载top100条高频检索问题" placement="topLeft">
                        <span
                            className="ml-1 cursor-pointer text-primary hover:text-primaryHover"
                            onClick={downloadAllData}
                        >
                            下载全部
                        </span>
                    </Tooltip>
                )}
            </Flex>
            <StyledCollapse
                items={items}
                bordered={false}
                activeKey={expandedKeys}
                onChange={handleCollapseChange}
                expandIcon={renderExpandIcon}
            />
        </ConfigProvider>
    );
}
