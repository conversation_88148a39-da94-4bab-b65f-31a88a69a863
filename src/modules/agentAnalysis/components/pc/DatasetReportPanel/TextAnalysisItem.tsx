/**
 * @file 文本分析项组件
 * @description 用于显示知识库文本分析结果的单个条目，包含相关性评分、引用次数、文件信息等
 * <AUTHOR>
 */

import React from 'react';
import {Flex, Tag, Divider, Tooltip} from 'antd';
import {TextAnalysisItem as TextAnalysisItemType} from '@/api/datasetRecallTest/interface';
import {useTextOverflow} from '../../../hooks/useTextOverflow';

interface TextAnalysisItemProps {
    text: TextAnalysisItemType;
    index: number;
    totalLength: number;
    handleGoToDetail: (datasetId: string, fileId: string, textId: string) => () => void;
}

const ScoreTag = ({score}: {score: number}) => {
    const getScoreMeta = (score: number) => {
        if (score <= 0.5) {
            return {
                label: '弱相关',
                textColor: '#E87400',
                bgColor: '#FF82001A',
            };
        } else if (score >= 0.8) {
            return {
                label: '强相关',
                textColor: '#3FC746',
                bgColor: '#3FC7461A',
            };
        } else {
            return {
                label: '相关',
                textColor: '#5562F2',
                bgColor: '#5562F21A',
            };
        }
    };

    const {label, textColor, bgColor} = getScoreMeta(score);
    return (
        <Tag className={`mr-0 text-[${textColor}]`} color={bgColor}>
            {label} {Math.floor(score * 1000) / 1000}
        </Tag>
    );
};

const TextAnalysisItem: React.FC<TextAnalysisItemProps> = ({text, index, totalLength, handleGoToDetail}) => {
    const {ref, isOverflow} = useTextOverflow(text.text, true);

    return (
        <>
            <Flex vertical gap={12} key={text.textId}>
                <Flex align="center" gap={4}>
                    <ScoreTag score={text.score} />
                    {text.hitCount > 0 && (
                        <Tag className="mr-0 font-medium text-[#272933]" color="#F5F6FA">
                            引用{text.hitCount <= 99 ? text.hitCount : '99+'}次
                        </Tag>
                    )}
                    <span className="truncate">
                        {text.datasetName}-{text.fileName}
                    </span>
                </Flex>

                <Flex
                    onClick={handleGoToDetail(text.datasetId, text.fileId, text.textId)}
                    align="end"
                    justify="space-between"
                    className="text-gray-tertiary"
                >
                    <Tooltip title={isOverflow ? text.text : ''} placement="top">
                        <span ref={ref} className="line-clamp-2 cursor-pointer">
                            {text.text}
                        </span>
                    </Tooltip>
                    {isOverflow && <span className="cursor-pointer whitespace-nowrap hover:text-primary">详情</span>}
                </Flex>
            </Flex>
            {index !== totalLength - 1 && <Divider className="my-3 h-[0.5px] border-t-0  bg-[#ECEEF0]" />}
        </>
    );
};

export default TextAnalysisItem;
