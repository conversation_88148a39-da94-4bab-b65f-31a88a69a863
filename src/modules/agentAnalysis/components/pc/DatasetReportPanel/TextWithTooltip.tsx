/**
 * @file 带 tooltip 的文本组件
 * <AUTHOR>
 */

import React from 'react';
import {Tooltip} from 'antd';
import {useTextOverflow} from '../../../hooks/useTextOverflow';

interface TextWithTooltipProps {
    children: React.ReactNode;
    className?: string;
    isMultiLine?: boolean;
}

/**
 * 带 tooltip 的文本组件
 *
 * @param children 要显示的文本内容
 * @param className 自定义样式类名，支持 truncate、line-clamp-* 等截断样式
 * @param isMultiLine 是否为多行文本模式，默认 false。设为 true 时适用于 line-clamp-* 样式
 * @returns 带有溢出检测和 tooltip 功能的文本组件
 *
 */
const TextWithTooltip: React.FC<TextWithTooltipProps> = ({children, className, isMultiLine = false}) => {
    const textContent = React.useMemo(() => {
        if (typeof children === 'string') {
            return children;
        }

        // 解析jsx时候，children有可能是数组
        if (Array.isArray(children)) {
            return children.map(child => (typeof child === 'string' ? child : String(child))).join('');
        }
        return String(children);
    }, [children]);

    const {ref, isOverflow} = useTextOverflow(textContent, isMultiLine);

    return (
        <Tooltip title={isOverflow ? textContent : ''} placement="top">
            <span ref={ref} className={className}>
                {children}
            </span>
        </Tooltip>
    );
};

export default TextWithTooltip;
