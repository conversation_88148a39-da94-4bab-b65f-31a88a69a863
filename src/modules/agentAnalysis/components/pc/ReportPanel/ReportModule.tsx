/**
 * @file 给诊断报告展示的每个模块封装的组件
 * <AUTHOR>
 */
import {Button, Flex, Tooltip} from 'antd';
import {useEffect} from 'react';
import {AnalysisDimensionType} from '@/api/agentDiagnosis/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';

export function ReportModule({
    title,
    optimizeUrl,
    showQuestionCircle,
    tooltipTitle,
    passedItems,
    failPassedItems,
    showFailedTitle = true,
    type,
}: {
    title: string; // 报告模块标题
    optimizeUrl: string; // 优化指导URL
    showQuestionCircle?: boolean; // 是否展示问号气泡
    tooltipTitle?: string; // 问号气泡提示内容
    passedItems?: React.ReactNode; // 达标项节点内容
    failPassedItems?: React.ReactNode; // 可优化项目节点内容
    showFailedTitle?: boolean; // 是否展示可优化的标题
    /** 智能体质量分析维度类型 */
    type: AnalysisDimensionType;
}) {
    const {showLog} = useUbcLogV3();

    useEffect(() => {
        if (type === AnalysisDimensionType.ReplyEffect) {
            showLog(EVENT_VALUE_CONST.REPLY_EFFECT_BOX, {cOptimiseType: failPassedItems ? 1 : 2});
        }
    }, [type, failPassedItems, showLog]);

    return (
        <div className="mb-6">
            {/* 标题部分 */}
            <Flex justify="space-between" className="mb-3">
                <div className="flex items-center">
                    <span className="text-lg font-medium leading-[26px]">{title}</span>
                    {showQuestionCircle && (
                        <Tooltip title={<span>{tooltipTitle}</span>} placement="top">
                            <span className="iconfont icon-questionCircle ml-1 text-base text-gray-tertiary" />
                        </Tooltip>
                    )}
                </div>
                {optimizeUrl && (
                    <Button
                        type="link"
                        target="_blank"
                        href={optimizeUrl}
                        className="mb-0 h-auto border-0 p-0 font-normal"
                    >
                        <Flex align="center">
                            <span className="iconfont icon-reason mr-1 leading-none"></span>
                            优化指导
                        </Flex>
                    </Button>
                )}
            </Flex>
            {passedItems && (
                <div className="mb-3 rounded-[9px] bg-[#F7F8FF] p-[18px]">
                    <div className="flex h-[20px] leading-[20px]">
                        <span className="iconfont icon-check-circle-fill mr-1 text-[#39B362]"></span>
                        <span className="font-medium">达标项</span>
                    </div>
                    {passedItems}
                </div>
            )}
            {failPassedItems && (
                <div className="rounded-[9px] bg-[#F7F8FF] p-[18px]">
                    {showFailedTitle && (
                        <div className="flex h-[20px] leading-[20px]">
                            <span className="iconfont icon-info-circle-fill mr-1 text-[#FF8200]"></span>
                            <span className="font-medium">可优化项目</span>
                        </div>
                    )}

                    {failPassedItems}
                </div>
            )}
        </div>
    );
}
