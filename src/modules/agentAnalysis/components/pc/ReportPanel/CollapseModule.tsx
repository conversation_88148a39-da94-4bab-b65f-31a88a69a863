import styled from '@emotion/styled';
import {Collapse, CollapseProps, ConfigProvider} from 'antd';
import {useCallback, useRef} from 'react';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {PassType} from '../interface';

const StyledCollapse = styled(Collapse)`
    .ant-collapse-item .ant-collapse-header {
        line-height: 20px !important;
    }
    .ant-collapse-item .ant-collapse-expand-icon {
        height: 20px !important;
    }

    .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
        padding-block: 0 !important;
    }
`;

/**
 * 不同分析项key对应展开收起打点logValue
 * 如果有新增项需要展开收起打点，需在这里加
 */
const collapseClickLogMap: {[key in string]: EVENT_VALUE_CONST} = {
    // 智能体基本信息完整
    basicInfoOmission: EVENT_VALUE_CONST.CONFIG_BASIC_INFO,
    // 智能体耗时
    timeCost: EVENT_VALUE_CONST.CONSUME_TIME,
    // 智能体回复稳定
    answerStability: EVENT_VALUE_CONST.REPLY_STABLE,
};

export default function CollpaseModule({items, type}: {items: CollapseProps['items']; type: PassType}) {
    const {clickLog} = useUbcLogV3();
    // 记录每次折叠器点击，展开项的key list
    const expandedKeys = useRef<string[]>([]);

    const onChange = useCallback(
        (key: string | string[]) => {
            const currentExpandedKeys = Array.isArray(key) ? key : [key];
            const preExpandedKeys = expandedKeys.current;

            expandedKeys.current = currentExpandedKeys;

            const addExpandedKeys: string[] = [];
            const addCollapseKeys: string[] = [];

            // 查找本次展开的key
            currentExpandedKeys.forEach(item => {
                if (!preExpandedKeys.includes(item)) {
                    addExpandedKeys.push(item);
                }
            });

            // 查找本次收起的key
            preExpandedKeys.forEach(item => {
                if (!currentExpandedKeys.includes(item)) {
                    addCollapseKeys.push(item);
                }
            });

            // 本次点击展开/收起的key对应的logValue
            const logValue = collapseClickLogMap[addExpandedKeys[0] || addCollapseKeys[0]];

            if (logValue) {
                clickLog(logValue, {
                    cIsReachStandard: type, // 是否达标
                    eIsExpand: addExpandedKeys[0] ? 1 : 0, // 展开/收起类型
                });
            }
        },
        [clickLog, type]
    );

    return (
        <ConfigProvider
            theme={{
                components: {
                    Collapse: {
                        paddingSM: 0,
                        contentPadding: '9px 24px 0 !important',
                        headerPadding: '12px 0 0',
                    },
                },
            }}
        >
            <StyledCollapse ghost items={items} onChange={onChange} />
        </ConfigProvider>
    );
}
