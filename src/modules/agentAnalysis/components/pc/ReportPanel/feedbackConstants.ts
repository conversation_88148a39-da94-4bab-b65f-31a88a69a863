/**
 * @file 反馈的公用数据
 * <AUTHOR>
 */
import {createContext, useContext} from 'react';

export enum FeedbackReason {
    评分不准确 = '评分不准确',
    案例与待优化项无关 = '案例与待优化项无关',
    案例无问题 = '案例无问题',
    不理解优化建议 = '不理解优化建议',
    优化建议不相关 = '优化建议不相关',
    优化建议无效 = '优化建议无效',
    其他问题 = '其他问题',
}

// 诊断报告反馈时的默认选项
export const defaultOptionsInReport = [
    {value: FeedbackReason.评分不准确},
    {value: FeedbackReason.案例与待优化项无关},
    {value: FeedbackReason.案例无问题},
];

// 在问答详情中反馈时的默认选项
export const defaultOptionsInDetail = [{value: FeedbackReason.案例与待优化项无关}, {value: FeedbackReason.案例无问题}];

// 存在优化建议时的选项
export const optionsForOptimization = [
    {value: FeedbackReason.不理解优化建议},
    {value: FeedbackReason.优化建议不相关},
    {value: FeedbackReason.优化建议无效},
];

interface FeedbackBaseContextType {
    appId: string;
    reportId: string;
}

// 诊断报告反馈的公用数据context
export const FeedbackBaseContext = createContext<FeedbackBaseContextType | null>(null);

export const useFeedbackBaseData = () => {
    const context = useContext(FeedbackBaseContext);
    if (!context) {
        throw new Error('useFeedbackBase must be used within a FeedbackProvider');
    }
    return context;
};
