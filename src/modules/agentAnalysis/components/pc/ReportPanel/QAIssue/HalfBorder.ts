import {css} from '@emotion/css';
import themeConfig from '@/styles/lingjing-light-theme';

export const HalfBorderCss = css`
    position: relative;
    border-radius: 12px;
    &::before {
        content: '';
        width: 200%;
        height: 200%;
        border: 1px solid ${themeConfig.token.colorBorderSecondary};
        position: absolute;
        left: 0;
        top: 0;
        transform-origin: 0 0;
        transform: scale(0.5);
        border-radius: 24px;
    }
`;

export const HalfBottomBorderCss = css`
    position: relative;
    &::after {
        content: '';
        width: 200%;
        height: 0;
        border-bottom: 1px solid ${themeConfig.token.colorBorderSecondary};
        position: absolute;
        left: 0;
        bottom: 0;
        transform-origin: 0 0;
        transform: scale(0.5);
    }
`;
