/**
 * @file 对话问题列表
 * <AUTHOR> 2024.08
 */

import {Flex, Space, Tooltip} from 'antd';
import React, {useCallback, useState} from 'react';
import classNames from 'classnames';
import {AgentDiagnosisIssue, AgentDiagnosisIssueSample} from '@/api/agentDiagnosis/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import ConversationIssueModal from './ConversationIssueModal';
import ConversationAnswer from './ConversationAnswer';
import {HalfBottomBorderCss} from './HalfBorder';

export default function ConversationIssues({issueList}: {issueList: AgentDiagnosisIssue[]}) {
    const [modalOpen, setModalOpen] = useState(false);
    const [curIssue, setCurIssue] = useState<AgentDiagnosisIssueSample>();
    // 当前点击的sample的索引, 格式为 issueIndex-sampleIndex
    const [curIndex, setCurIndex] = useState<string | null>(null);
    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();

    const onIssueCardClick = useCallback(
        (issue: AgentDiagnosisIssueSample, sampleIndex: number, issueIndex: number) => {
            setModalOpen(true);
            setCurIssue(issue);
            setCurIndex(`${issueIndex}-${sampleIndex}`);
            clickLog(EVENT_VALUE_CONST.REPORT_OPTIMIZE_ADVICE, EVENT_PAGE_CONST.AGENT_ANALYSIS, {...extLog});
        },
        [clickLog, extLog]
    );

    return (
        <div className="-mt-6">
            {modalOpen && curIssue && curIndex !== null && (
                <ConversationIssueModal open={modalOpen} issue={curIssue} setOpen={setModalOpen} caseId={curIndex} />
            )}
            {issueList.map((issue, issueIndex) => (
                <>
                    <div className="mt-6 text-sm font-medium leading-none">
                        <Space size={[4, 0]}>
                            <span className="iconfont icon-info-circle-fill mr-1 text-[#FF8200]"></span>
                            <span>可优化：</span>
                            {issue.agentDiagnosisIssueLabel}
                            {issue.analysis && (
                                <Tooltip title={issue.analysis} placement="top">
                                    <span className="iconfont icon-questionCircle cursor-pointer text-sm leading-none text-gray-tertiary"></span>
                                </Tooltip>
                            )}
                        </Space>
                    </div>
                    {issue.sampleList.map((item, index) => (
                        <div
                            // eslint-disable-next-line react/no-array-index-key
                            key={index}
                            className={classNames(
                                'cursor-pointer pt-3 first:mt-0',
                                index < issue.sampleList.length - 1 ? HalfBottomBorderCss : '',
                                {
                                    'pb-3': index < issue.sampleList.length - 1,
                                    'pb-0': index === issue.sampleList.length - 1,
                                }
                            )}
                            onClick={() => onIssueCardClick(item, index, issueIndex)}
                        >
                            <Flex className="truncate" align="center">
                                <span className="mr-2 flex h-4 w-4 items-center justify-center rounded-[3px] bg-[#93aaf7] px-[3px] text-xs leading-none text-white">
                                    问
                                </span>
                                {item.sample.length > 1 && (
                                    <span className="mr-1 flex h-4 items-center justify-center rounded-[3px] bg-[#5562F21A] px-[3px] text-xs font-medium leading-none text-primary">
                                        多轮问答
                                    </span>
                                )}
                                <span className="truncate">{item.sample[0].query}</span>
                            </Flex>
                            <Flex className="mt-2">
                                <span className="mr-2 mt-[3px] flex h-4 w-4 items-center justify-center rounded-[3px] bg-[#5562F21A] px-[3px] text-xs leading-none text-primary">
                                    答
                                </span>
                                <ConversationAnswer answer={item.sample[0].response} />
                            </Flex>
                        </div>
                    ))}
                </>
            ))}
        </div>
    );
}
