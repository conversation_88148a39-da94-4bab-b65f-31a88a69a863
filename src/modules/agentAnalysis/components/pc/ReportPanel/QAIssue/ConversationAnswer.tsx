/**
 * @file 对话answer-是否展示【详情】
 * <AUTHOR> 2024.08
 */

import classNames from 'classnames';
import {useEffect, useRef, useState} from 'react';

/**
 * 显示【详情】按钮的最小高度限制
 * 行高22px，即超过2行显示
 */
const SHOW_FOLD_LIMIT_HEIGHT = 44;

export default function ConversationAnswer({answer}: {answer: string}) {
    // dom容器
    const domContainer = useRef<HTMLDivElement>(null);
    // dom展开时高度
    const domExpandHeightRef = useRef<number>(0);
    // 是否展示【详情】按钮
    const [showFold, setShowFold] = useState(false);
    // 是否展开，默认展开，以便计算dom高度。判断是否展示【详情】按钮，以及用来计算按钮展示位置
    const [expand, setExpand] = useState(true);
    const [domLoaded, setDomLoaded] = useState(false);

    // dom渲染完
    useEffect(() => {
        if (domContainer.current) {
            // 判断是否展示【详情】，小于2行不展示
            setShowFold(domContainer.current.clientHeight > SHOW_FOLD_LIMIT_HEIGHT);
            // dom渲染完记录展开时dom高度
            domExpandHeightRef.current = domContainer.current.clientHeight;
        }
        // dom渲染完，收起Dom
        setExpand(false);
        setDomLoaded(true);
    }, []);

    return (
        <div
            ref={domContainer}
            className={classNames('break-all text-[#848691]', {
                'max-h-[44px] overflow-hidden leading-[22px]': domLoaded && !expand,
            })}
        >
            {/* 占位浮动元素，用来推【详情】按钮 */}
            <span className="float-right inline-block h-[22px] w-0"></span>
            {/* 【详情】按钮 */}
            <span
                className={classNames(
                    'relative float-right clear-both ml-[18px] flex h-[22px] cursor-pointer leading-[22px] text-gray-tertiary hover:text-primary',
                    {
                        hidden: !showFold,
                        'before:absolute before:-left-[6px] before:-translate-x-full before:text-gray-tertiary before:content-["..."] before:hover:text-gray-tertiary':
                            showFold && !expand,
                    }
                )}
            >
                详情
            </span>
            {answer}
        </div>
    );
}
