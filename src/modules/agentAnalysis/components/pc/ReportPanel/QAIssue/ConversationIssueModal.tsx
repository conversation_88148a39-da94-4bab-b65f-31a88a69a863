/**
 * @file 对话问题&优化建议弹窗列表
 * <AUTHOR> 2024.08
 */

import {Modal} from 'antd';
import React, {useCallback} from 'react';
import {AgentDiagnosisIssueSample, FeedbackContext, FeedbackEntry} from '@/api/agentDiagnosis/interface';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import QAList from '../../QADetail';
import {FeedbackTrigger} from '../FeedbackTrigger';
import {useFeedbackBaseData} from '../feedbackConstants';

interface Props {
    open: boolean;
    setOpen: (isOpen: boolean) => void;
    issue: AgentDiagnosisIssueSample;
    caseId: FeedbackContext['caseId'];
}

export default function ConversationIssues({open, setOpen, issue, caseId}: Props) {
    const feedbackBaseData = useFeedbackBaseData();

    // 关闭弹窗事件
    const closeModal = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    return (
        <Modal title="问答详情" open={open} centered forceRender width={750} onCancel={closeModal} footer={null}>
            <ScrollContainer scrollY scrollbarWidth={4} className="-mr-5 max-h-[492px] pr-4">
                {/* 对话列表 */}
                <QAList qaList={issue.sample} />
            </ScrollContainer>
            <p className="mt-4 pb-1">
                <span className="mr-1 pl-4 text-gray-tertiary">若有任何问题, 请跟我们</span>
                <FeedbackTrigger
                    feedbackContext={{
                        ...feedbackBaseData,
                        type: FeedbackEntry.OptimizationSuggestion,
                        caseId,
                    }}
                >
                    <span className="font-normal">反馈</span>
                </FeedbackTrigger>
            </p>
        </Modal>
    );
}
