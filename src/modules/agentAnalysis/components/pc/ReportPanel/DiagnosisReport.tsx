/**
 * @file 诊断报告-右侧抽屉
 * <AUTHOR> lian<PERSON><PERSON><PERSON>@baidu.com
 */

import {Avatar, CollapseProps, ConfigProvider, Drawer, Flex} from 'antd';
import {useCallback, useEffect, useMemo} from 'react';
import {css} from '@emotion/css';
import {
    AgentDiagnosisInfo,
    AgentDiagnosisReport,
    AnalysisDimensionType,
    FeedbackEntry,
} from '@/api/agentDiagnosis/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants/page';
import {EVENT_VALUE_CONST} from '@/utils/loggerV2/constants/value';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import DICTS from '@/dicts/home';
import LogoAnalysisReport from '../../../assets/logo-analysis-report.png';
import {PassType} from '../interface';
import ConversationIssues from './QAIssue/ConversationIssues';
import {drawerStyles, formatReportUpdateTime} from './utils';
import {FeedbackTrigger} from './FeedbackTrigger';
import {useFeedbackBaseData} from './feedbackConstants';
import {ReportModule} from './ReportModule';
import CollpaseModule from './CollapseModule';

export const DrawerRootStyle = css`
    .ant-drawer-content-wrapper {
        box-shadow: 0px 15px 80px 0px #1d225214 !important;
    }
`;

export interface LevelData {
    /** 等级文字 */
    level: string;
    /** 等级进度图片 */
    levelProgressBar: string;
    /** 报告中分发说明文案 */
    distributionDesc?: string;
    /** 报告优化项为0时，分发说明文案 */
    emptyDistributionDesc?: string;
}

interface Props {
    open: boolean;
    reportTime: AgentDiagnosisInfo['reportTime'];
    report: AgentDiagnosisReport;
    setOpen: (open: boolean) => void;
}

/**
 * 耗时ms转成s，四舍五入保留1位小数，最少0.1s
 * @param timeNum 耗时 ms
 * @returns timeNumString 耗时s
 */
const formatCostTimeMsToS = (timeNum: number): string => {
    if (isNaN(timeNum)) {
        return '0.1';
    }

    const time = +(timeNum / 1000).toFixed(1);
    return Math.max(time, 0.1).toFixed(1);
};

export default function DiagnosisReport({reportTime, report, open, setOpen}: Props) {
    const {showLog} = useUbcLogV2();
    const extLog = useExtLog();
    const feedbackBaseData = useFeedbackBaseData();

    const handleCancel = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    const getHeaderTitle = useCallback(() => {
        if (report.agentDistributeScene) {
            return '智能体质量优秀，继续保持哦～';
        }

        if (report.issueKeys.length === 0) {
            return '智能体未发现可优化项，继续保持哦～';
        }
        return `发现${report.issueKeys.length}个可优化项，快来优化吧～`;
    }, [report.agentDistributeScene, report.issueKeys.length]);

    /** 获取配置完备度的评估数据 */
    const getBasicInfoOmissioPassedInfo = useMemo(() => {
        const passedItems: CollapseProps['items'] = []; // 达标项
        const failedPassedItem: CollapseProps['items'] = []; // 不达标项

        /** 基本信息完整度 */
        if (report.agentDiagnosisBasicInfoOmissionIssue.length === 0) {
            passedItems.push({
                key: 'basicInfoOmission',
                label: <p className="font-normal">基本信息完整</p>,
                children: (
                    <p className="text-sm leading-5 text-gray-tertiary">智能体头像、简介、开场白、引导示例配置完整</p>
                ),
            });
        } else {
            failedPassedItem.push({
                key: 'basicInfoOmission',
                label: <p className="font-normal">基本信息待补充</p>,
                children: report.agentDiagnosisBasicInfoOmissionIssue.map(item => (
                    <p className="text-sm leading-5 text-gray-tertiary" key={item}>
                        {item}
                    </p>
                )),
            });
        }

        /** 配置完整度 */
        if (report.showConfigCompleteIssue) {
            if (report.configCompleteIssue.length === 0) {
                passedItems.push({
                    key: 'configComplete',
                    label: <p className="font-normal">配置完整</p>,
                    children: (
                        <p className="text-sm leading-5 text-gray-tertiary">智能体完整配置了完成任务所需的插件和能力</p>
                    ),
                });
            } else {
                failedPassedItem.push({
                    key: 'configComplete',
                    label: <p className="font-normal">配置不完整</p>,
                    children: report.configCompleteIssue.map(item => (
                        <p className="text-sm leading-5 text-gray-tertiary" key={item}>
                            {item}
                        </p>
                    )),
                });
            }
        }

        /** 信息冲突 */
        if (report.showInfoConflictIssue) {
            if (report.infoConflictIssue.length === 0) {
                passedItems.push({
                    key: 'infoConflict',
                    label: <p className="font-normal">信息无冲突</p>,
                    children: (
                        <p className="text-sm leading-5 text-gray-tertiary">
                            未发现名称、开场白、简介、人物设定冲突问题
                        </p>
                    ),
                });
            } else {
                failedPassedItem.push({
                    key: 'infoConflict',
                    label: <p className="font-normal">信息存在冲突</p>,
                    children: report.infoConflictIssue.map(item => (
                        <p className="text-sm leading-5 text-gray-tertiary" key={item}>
                            {item}
                        </p>
                    )),
                });
            }
        }
        return {
            passed: passedItems.length > 0 ? <CollpaseModule items={passedItems} type={PassType.Passed} /> : null,
            failPassed:
                failedPassedItem.length > 0 ? <CollpaseModule items={failedPassedItem} type={PassType.Failed} /> : null,
        };
    }, [
        report.agentDiagnosisBasicInfoOmissionIssue,
        report.configCompleteIssue,
        report.infoConflictIssue,
        report.showConfigCompleteIssue,
        report.showInfoConflictIssue,
    ]);

    /** 获取耗时与稳定性的评估数据 */
    const getTimeCostAndStablityPassedInfo = useMemo(() => {
        const passedItems: CollapseProps['items'] = []; // 达标项
        const failedPassedItem: CollapseProps['items'] = []; // 不达标项

        /** 各阶段耗时详情 */
        const costDetail = report.agentDiagnosisTimeCostDetail.map(item => (
            <div key={item.avgCost} className="text-sm leading-5 text-gray-tertiary">
                {item.name}耗时：{formatCostTimeMsToS(item.avgCost)}s
            </div>
        ));

        /** 耗时 */
        if (report.agentDiagnosisTimeCostIssue.length === 0) {
            // 理解系统可能产出耗时为0，前端报告过滤不展示
            if (report.agentDiagnosisTimeCost > 0) {
                passedItems.push({
                    key: 'timeCost',
                    label: (
                        <p className="font-normal">
                            智能体平均响应耗时：{formatCostTimeMsToS(report.agentDiagnosisTimeCost)}s
                        </p>
                    ),
                    children: <p>{costDetail}</p>,
                });
            }
        } else {
            failedPassedItem.push({
                key: 'timeCost',
                label: (
                    <p className="font-normal">
                        智能体响应速度慢（平均耗时：{formatCostTimeMsToS(report.agentDiagnosisTimeCost)}s）
                    </p>
                ),
                children: (
                    <>
                        <p className="mb-2">{costDetail}</p>
                        {report.agentDiagnosisTimeCostIssue.map(item => (
                            <div key={item} className="text-sm leading-5 text-gray-tertiary">
                                {item}
                            </div>
                        ))}
                    </>
                ),
            });
        }

        /** 稳定性 */
        if (report.agentDiagnosisAnswerStabilityIssue.length === 0) {
            passedItems.push({
                key: 'answerStability',
                label: <p className="font-normal">智能体回复稳定</p>,
                children: <p className="text-sm leading-5 text-gray-tertiary">未发现连续报错、连续拒答等问题</p>,
            });
        } else {
            failedPassedItem.push({
                key: 'answerStability',
                label: <p className="font-normal">智能体回复不稳定</p>,
                children: report.configCompleteIssue.map(item => (
                    <div className="text-sm leading-5 text-gray-tertiary" key={item}>
                        {item}
                    </div>
                )),
            });
        }

        return {
            passed: passedItems.length > 0 ? <CollpaseModule items={passedItems} type={PassType.Passed} /> : null,
            failPassed:
                failedPassedItem.length > 0 ? <CollpaseModule items={failedPassedItem} type={PassType.Failed} /> : null,
        };
    }, [
        report.agentDiagnosisAnswerStabilityIssue.length,
        report.agentDiagnosisTimeCost,
        report.agentDiagnosisTimeCostDetail,
        report.agentDiagnosisTimeCostIssue,
        report.configCompleteIssue,
    ]);

    /** 获取问答效果评估详情 */
    const getQAIssue = useMemo(() => {
        const passedItems: CollapseProps['items'] = []; // 达标项
        let failedPassedItem = null; // 不达标项

        if (report.agentDistributeScene || report.agentDiagnosisIssue?.length === 0) {
            passedItems.push({
                key: 'qaIssu',
                label: <>未发现明显问答效果问题</>,
                children: (
                    <p className="text-sm leading-5 text-gray-tertiary">智能体回答质量无明显问题，可满足用户需求</p>
                ),
            });
        } else {
            failedPassedItem = <ConversationIssues issueList={report.agentDiagnosisIssue!} />;
        }

        return {
            passed: passedItems.length > 0 ? <CollpaseModule items={passedItems} type={PassType.Passed} /> : null,
            failPassed: failedPassedItem,
        };
    }, [report.agentDiagnosisIssue, report.agentDistributeScene]);

    useEffect(() => {
        if (open) {
            showLog(EVENT_VALUE_CONST.ANALYSIS_REPORT_BOX, EVENT_PAGE_CONST.AGENT_ANALYSIS, {...extLog});
        }
    }, [extLog, open, showLog]);

    return (
        <ConfigProvider
            theme={{
                token: {
                    // TODO：后续全平台升级黑色主题色
                    colorTextBase: '#000311',
                },
            }}
        >
            <Drawer
                placement="right"
                width={480}
                styles={drawerStyles}
                rootClassName={DrawerRootStyle}
                open={open}
                onClose={handleCancel}
                // TODO：后续全平台升级黑色主题色
                className="text-colorTextDefault"
            >
                <div className="h-screen w-full p-6">
                    <header className="mb-4 flex items-center">
                        <h1 className="text-xl font-medium">分析报告</h1>
                        <span className="ml-[9px] text-sm text-gray-tertiary">
                            更新时间：{formatReportUpdateTime(reportTime)}
                        </span>
                    </header>
                    <ScrollContainer
                        scrollY
                        scrollbarWidth={4}
                        className="-mr-5 h-[calc(100%-20px)] overflow-y-auto pr-4"
                    >
                        <div className="mb-6 rounded-[9px] bg-[#F7F8FF] p-6">
                            <Flex className="text-base font-medium leading-[26px] text-primary" align="center">
                                <Avatar src={LogoAnalysisReport} size={30} className="mr-3" />
                                {getHeaderTitle()}
                            </Flex>
                        </div>
                        <div>
                            <ReportModule
                                title="配置完备度"
                                type={AnalysisDimensionType.ConfigCompleteness}
                                passedItems={getBasicInfoOmissioPassedInfo.passed}
                                failPassedItems={getBasicInfoOmissioPassedInfo.failPassed}
                                optimizeUrl={DICTS.URL_DOC_TUNING_AGENT_CONF}
                            />
                            <ReportModule
                                title="耗时与稳定性"
                                type={AnalysisDimensionType.ConsumeTimeAndStable}
                                passedItems={getTimeCostAndStablityPassedInfo.passed}
                                failPassedItems={getTimeCostAndStablityPassedInfo.failPassed}
                                optimizeUrl={DICTS.URL_DOC_TUNING_AGENT_STABLE}
                            />
                            {(report.agentDiagnosisIssue || []).length > 0 && (
                                <ReportModule
                                    title="回答效果"
                                    type={AnalysisDimensionType.ReplyEffect}
                                    showQuestionCircle
                                    tooltipTitle="平台将抽取部分智能体进行问答效果分析，帮助您深入优化智能体体验"
                                    passedItems={getQAIssue.passed}
                                    failPassedItems={getQAIssue.failPassed}
                                    showFailedTitle={false}
                                    optimizeUrl={DICTS.URL_DOC_TUNING_AGENT_OTHER}
                                />
                            )}
                        </div>
                        <p className="-mt-2 mb-6 leading-none">
                            <span className="mr-1 pl-4  text-gray-tertiary">若内容有任何问题, 请跟我们</span>
                            <FeedbackTrigger
                                feedbackContext={{
                                    ...feedbackBaseData,
                                    type: FeedbackEntry.DiagnosisReport,
                                }}
                            >
                                <span className="font-normal">反馈</span>
                            </FeedbackTrigger>
                        </p>
                    </ScrollContainer>
                </div>
            </Drawer>
        </ConfigProvider>
    );
}
