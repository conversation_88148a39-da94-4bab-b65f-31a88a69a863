/**
 * @file 诊断报告反馈浮层组件
 * <AUTHOR>
 */
import React, {memo, useCallback, useMemo, useState} from 'react';
import {Button, message} from 'antd';
import Popover from '@/components/FeedbackPopover/FeedbackStyledPopover';
import {FeedbackForm} from '@/components/FeedbackPopover/FeedbackForm';
import {FeedbackContext, FeedbackData, FeedbackEntry} from '@/api/agentDiagnosis/interface';
import api from '@/api/agentDiagnosis';
import datasetRecallTestApi from '@/api/datasetRecallTest';
import {DatasetRecallFeedbackParams} from '@/api/datasetRecallTest/interface';
import {FormValue} from '@/components/FeedbackPopover/interface';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {ImageUploadProps} from '../interface';
import {FeedbackReason} from './feedbackConstants';

interface ReportFeedbackFormProps {
    // 用户提交回调
    onSubmit: (feedbackContent: FormValue<FeedbackReason>) => Promise<void>;
    // 用户取消回调
    onCancel: () => void;
    imageUploadProps?: ImageUploadProps;
}

const ReportFeedbackForm: React.FC<ReportFeedbackFormProps> = memo(({onSubmit, onCancel, imageUploadProps}) => {
    const submitFeedback = useCallback(
        async (formValue: FormValue<FeedbackReason>) => {
            await onSubmit(formValue);
            return true;
        },
        [onSubmit]
    );

    return (
        <FeedbackForm
            onCancel={onCancel}
            onFinish={submitFeedback}
            detailProps={{
                title: '问题描述',
                placeHolder: '请描述具体的问题',
                required: true,
                textareaClassName: 'h-[108px]',
            }}
            imageUploadProps={imageUploadProps}
        />
    );
});

// 反馈表单触发器
export function FeedbackTrigger({
    feedbackContext,
    imageUploadProps,
    children,
}: {
    feedbackContext: FeedbackContext;
    imageUploadProps?: ImageUploadProps;
    children?: React.ReactNode;
}) {
    const [popoverVisible, setPopoverVisible] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const {clickLog} = useUbcLogV2();
    const ext = useExtLog();
    // 需要传递是否在诊断报告中的信息， 以区分诊断报告和仪表盘两种反馈入口
    const extData = useMemo(() => {
        // TODO 新增类型打点
        if (feedbackContext.type === FeedbackEntry.DatasetFeedback) {
            return {};
        }

        return feedbackContext.type === FeedbackEntry.OptimizationSuggestion
            ? {}
            : {
                  [EVENT_EXT_KEY_CONST.IS_IN_REPORT]: feedbackContext.type === FeedbackEntry.DiagnosisReport ? 1 : 0,
              };
    }, [feedbackContext]);

    const handleOpenChange = useCallback(
        (newOpen: boolean) => {
            // 新增的 DatasetFeedback 类型不参与原有的打点逻辑
            if (feedbackContext.type !== FeedbackEntry.DatasetFeedback) {
                // 反馈按钮点击打点
                const logValue =
                    feedbackContext.type === FeedbackEntry.OptimizationSuggestion
                        ? EVENT_VALUE_CONST.QA_FEEDBACK
                        : EVENT_VALUE_CONST.REPORT_FEEDBACK;
                clickLog(logValue, EVENT_PAGE_CONST.AGENT_ANALYSIS, {
                    ...ext,
                    ...extData,
                });
            }

            setPopoverVisible(newOpen);
        },
        [clickLog, ext, extData, feedbackContext]
    );

    const hidePopover = useCallback(() => {
        setPopoverVisible(false);
    }, []);

    // 反馈成功toast
    const feedbackSuccess = useCallback(() => {
        messageApi.open({
            type: 'success',
            content: '感谢您的反馈',
            duration: 3,
        });
    }, [messageApi]);

    // 提交反馈数据
    const onSubmit = useCallback(
        async (feedbackContent: FormValue<FeedbackReason>) => {
            // 根据反馈类型选择不同的接口
            if (feedbackContext.type === FeedbackEntry.DatasetFeedback) {
                // 使用数据集召回反馈接口
                const datasetFeedbackParams: DatasetRecallFeedbackParams = {
                    source: 1, // 知识库召回报告
                    agentId: feedbackContext.appId,
                    description: feedbackContent.detail || '',
                    pictures: feedbackContent.pictures,
                };
                await datasetRecallTestApi.datasetRecallFeedback(datasetFeedbackParams);
            } else {
                // 反馈提交按钮点击打点
                const logValue =
                    feedbackContext.type === FeedbackEntry.OptimizationSuggestion
                        ? EVENT_VALUE_CONST.QA_FEEDBACK_SUBMIT
                        : EVENT_VALUE_CONST.REPORT_FEEDBACK_SUBMIT;
                clickLog(logValue, EVENT_PAGE_CONST.AGENT_ANALYSIS, {
                    ...ext,
                    ...extData,
                });
                const feedbackData: FeedbackData = {
                    ...feedbackContext,
                    content: feedbackContent.detail,
                    options: feedbackContent.reasonList,
                };
                // 使用原有的诊断反馈接口
                await api.diagnosisFeedback(feedbackData);
            }

            feedbackSuccess();
            hidePopover();
        },
        [feedbackSuccess, feedbackContext, hidePopover, clickLog, ext, extData]
    );

    return (
        <>
            {contextHolder}
            <Popover
                content={
                    <ReportFeedbackForm
                        imageUploadProps={imageUploadProps}
                        onSubmit={onSubmit}
                        onCancel={hidePopover}
                    />
                }
                destroyTooltipOnHide
                rootClassName="w-[343px]"
                open={popoverVisible}
                trigger="click"
                onOpenChange={handleOpenChange}
            >
                <Button
                    type="link"
                    className="inline-flex h-6 items-center px-0 py-0 text-primaryActive hover:text-[#3934DB]"
                >
                    {children}
                </Button>
            </Popover>
        </>
    );
}
