/**
 * utils.ts 工具函数
 */
import {DrawerStyles} from 'antd/es/drawer/DrawerPanel';
import dayjs from 'dayjs';

/**
 * 报告更新时间格式化
 * @param time millisecond 毫秒时间戳
 * @returns string 时间字符串
 */
export const formatReportUpdateTime = (time: number) => {
    if (!time) return '';

    return dayjs(time).format('YYYY-MM-DD HH:mm');
};

/** 插件耗时「毫秒」转成「秒」，四舍五入保留2位小数，小数位为0时省略，比如：10.85、10.7、10 */
export const formatPluginConsumeTime = (time: number): number => {
    if (!time) {
        return 0;
    }

    return Math.round(+time / 10) / 100;
};

export const drawerStyles: DrawerStyles = {
    mask: {
        background: 'transparent',
    },
    header: {
        position: 'absolute',
        border: 'none',
        top: '0px',
        right: '0px',
        paddingRight: '10px',
        paddingTop: '30px',
    },
    body: {
        padding: '0px',
    },
};
