import {Button} from 'antd';
import {useCallback} from 'react';
import {useSearchParams} from 'react-router-dom';
import {AgentTab} from '@/modules/agentPromptEditV2/interface';

export default function NoData({
    content,
    subContent,
    showPrivacyEntry,
    appId,
}: {
    content: string;
    showPrivacyEntry?: boolean;
    subContent?: string;
    appId: string;
}) {
    const [, setSearchParams] = useSearchParams();
    const openPrivacy = useCallback(() => {
        // 切换回创建 Tab
        // exportConversationHint=0 标识隐私设置按钮需要展示提示气泡，引导用户允许隐私配置中的“对话记录”。
        setSearchParams({activeTab: AgentTab.Create, appId, exportConversationHint: '0'});
    }, [appId, setSearchParams]);

    return (
        <div className="flex h-full w-full flex-col items-center justify-center gap-2 py-[122px]">
            <div className="h-24 w-32 bg-no-data"></div>
            <div className="mt-[6px] text-center text-base font-medium leading-tight text-gray-tertiary">{content}</div>
            {subContent && (
                <div className="mt-[12px] text-center text-sm font-normal leading-none text-gray-quaternary">
                    {subContent}
                </div>
            )}
            {showPrivacyEntry && (
                <Button
                    shape="round"
                    className="'h-auto mt-[10px] px-[14px] py-[4px] text-sm font-medium hover:bg-primaryHover"
                    type="primary"
                    onClick={openPrivacy}
                >
                    去开启
                </Button>
            )}
        </div>
    );
}
