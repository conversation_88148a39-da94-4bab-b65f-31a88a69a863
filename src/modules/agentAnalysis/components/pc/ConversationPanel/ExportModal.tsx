/**
 * @file 智能体分析-对话看板-导出对话弹窗
 * <AUTHOR>
 */
import {ConfigProvider, DatePicker, Modal, Radio, Space, message} from 'antd';
import {useCallback, useEffect, useState} from 'react';
import locale from 'antd/es/date-picker/locale/zh_CN';
import styled from '@emotion/styled';
import {RangePickerProps} from 'antd/es/date-picker';
import dayjs from 'dayjs';
import {RadioChangeEvent} from 'antd/lib';
import api from '@/api/agentAnalysis';
import {GetConversationCountResponse} from '@/api/agentAnalysis/interface';
import ExportFileApi from '@/utils/file';
import ModalFooter from '@/modules/agentPromptEditV2/pc/components/Privacy/ModalFooter';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {TabKey} from '../interface';
import {ExportDate} from './interface';
import ExportModalTitle from './ExportModalTitle';

const {RangePicker} = DatePicker;

const StyledRadioGroup = styled(Radio.Group)`
    .ant-radio-wrapper .ant-radio-inner {
        border: #bfc3cd 1px solid !important;
    }

    .ant-radio-wrapper:hover .ant-radio-inner {
        border-color: #5562f2 !important;
    }

    .ant-radio-checked .ant-radio-inner {
        border: #5562f2 !important;
    }
`;
const StyledRangePicker = styled(RangePicker)`
    &.ant-picker-range {
        // 不展示选择日期的按钮入口，只展示选择面板
        opacity: 0 !important;
    }
`;

export default function ExportModal({
    show,
    appId,
    setShowExportModal,
    showExportModal,
    effectiveDate,
    setExporting,
}: {
    show: boolean;
    appId: string;
    setShowExportModal: (value: boolean) => void;
    showExportModal: boolean;
    effectiveDate: number;
    setExporting: (value: boolean) => void;
}) {
    // 时间选择器的值
    const [range, setRange] = useState<RangePickerProps['value']>([dayjs.unix(effectiveDate), dayjs()]);

    // 导出对话条件
    const [exportCondition, setExportCondition] = useState<GetConversationCountResponse>();

    const [allowExport, setAllowExport] = useState(true);

    const [radioValue, setRadioValue] = useState(ExportDate.all);

    const {clickLog} = useUbcLogV2();
    const extLog = useExtLog();
    /**
     * 查询对话记录条数是否超限、以及导出记录的时间
     */
    const getCondition = useCallback(
        async (startTime: number, endTime: number) => {
            const res = await api.getConversationCount({
                appId,
                startTime,
                endTime,
            });
            setExportCondition(res);

            if (res.count > res.limit || res.count === 0) {
                // 导出条数超限或数据不足时，不允许导出
                setAllowExport(false);
                return;
            }
            setAllowExport(true);
        },
        [appId, setExportCondition]
    );

    const onRadioChange = useCallback(
        (e: RadioChangeEvent) => {
            let dateRange: RangePickerProps['value'] = null;
            setRadioValue(e.target.value);

            switch (e.target.value) {
                case ExportDate.rencent1:
                    // 计算时间范围
                    dateRange = [dayjs().subtract(1, 'day'), dayjs()];
                    break;
                case ExportDate.rencent7:
                    // 计算时间范围
                    dateRange = [dayjs().subtract(7, 'day'), dayjs()];
                    break;
                case ExportDate.rencent14:
                    // 计算时间范围
                    dateRange = [dayjs().subtract(14, 'day'), dayjs()];
                    break;
                case ExportDate.all:
                    // 全部导出，时间是从隐私协议生效时间至今
                    dateRange = [dayjs.unix(effectiveDate), dayjs()];
                    break;
                case ExportDate.custom:
                    setRange(null);
                    setExportCondition(undefined);
                    return;
            }
            setRange(dateRange);
            // 校验当前选择范围的导出对话条件，自定义时间范围的导出不在此处进行条件校验
            dateRange && getCondition(dateRange[0]!.unix(), dateRange[1]!.unix());
        },
        [effectiveDate, getCondition]
    );

    const closeModal = useCallback(() => {
        setShowExportModal(false);
    }, [setShowExportModal]);

    const onRangeChange = useCallback(
        // 自定义时间选择
        async (value: RangePickerProps['value']) => {
            setRadioValue(ExportDate.custom);
            if (!value) {
                return;
            }
            const [startTime, endTime] = value;
            setRange(value);
            getCondition(startTime!.unix(), endTime!.unix());
        },
        [getCondition]
    );

    const handleCloseModal = useCallback(() => {
        setShowExportModal(false);
    }, [setShowExportModal]);

    /** 导出对话 */
    const handleExport = useCallback(async () => {
        if (!range) {
            return;
        }
        try {
            setExporting(true);
            clickLog(EVENT_VALUE_CONST.EXPORT_TIME_CONFIRM, EVENT_PAGE_CONST.AGENT_ANALYSIS, {
                ...extLog,
                [EVENT_EXT_KEY_CONST.ANALYSIS_TAB_NAME]: +TabKey.conversation,
                [EVENT_EXT_KEY_CONST.SELECT_TIME_TYPE]: radioValue,
            });
            setShowExportModal(false);
            const res = await api.exportConversation({
                appId,
                startTime: range[0]!.unix(),
                endTime: range[1]!.unix(),
            });
            setExporting(false);
            if (res) {
                ExportFileApi.downloadFile({url: res.url});
                message.success('文件已下载');
            } else {
                message.error('文件下载失败');
            }
        } catch (e) {
            message.error('文件下载失败');
            setExporting(false);
            setShowExportModal(false);
        }
    }, [range, setExporting, clickLog, extLog, radioValue, appId, setShowExportModal]);

    /** 禁用今天以后和隐私生效之前的日期 */
    const disabledDate = useCallback(
        (date: any) => {
            // 23:59:59
            const endDay = dayjs().endOf('day');

            // 00:00:00
            const startDay = dayjs.unix(effectiveDate).startOf('day');
            return date.isBefore(startDay) || date.isAfter(endDay);
        },
        [effectiveDate]
    );

    useEffect(() => {
        if (showExportModal) {
            getCondition(dayjs.unix(effectiveDate).unix(), dayjs().unix());
        }
    }, [effectiveDate, getCondition, showExportModal]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    DatePicker: {
                        boxShadowSecondary: '',
                        sizePopupArrow: 0,
                    },
                    Radio: {
                        buttonBg: '#BFC3CD',
                    },
                    Button: {
                        controlHeight: 30,
                        paddingInline: 15,
                    },
                },
            }}
        >
            <Modal
                title={<ExportModalTitle condition={exportCondition} />}
                open={showExportModal}
                centered
                forceRender
                footer={
                    <ModalFooter
                        handleConfirm={handleExport}
                        confirmBtnDisabled={!allowExport}
                        handleCancel={handleCloseModal}
                    />
                }
                okText="确定"
                cancelText="取消"
                onCancel={closeModal}
                width={720}
                transitionName="" // ATTENTION：禁用动画，不然 Modal不会同时与 rangePicker 的选择面板出现/消失，会有先后，效果不好
            >
                <div className="z-0 mt-6 flex h-[300px]">
                    <StyledRadioGroup onChange={onRadioChange} value={radioValue}>
                        <Space direction="vertical" size={16}>
                            <Radio value={ExportDate.all}>全部</Radio>
                            <Radio value={ExportDate.rencent1}>近1天</Radio>
                            <Radio value={ExportDate.rencent7}>近7天</Radio>
                            <Radio value={ExportDate.rencent14}>近14天</Radio>
                            <Radio value={ExportDate.custom}>自定义</Radio>
                        </Space>
                    </StyledRadioGroup>
                    <div className="-ml-[10px] -mt-10">
                        <StyledRangePicker
                            open={show}
                            locale={locale}
                            onChange={onRangeChange}
                            value={range}
                            disabledDate={disabledDate}
                            transitionName="" // ATTENTION：禁用动画，让选择器的出现/消失时机与 Modal 相同，有动画的话会有先后出现/消失，效果不好
                        />
                    </div>
                </div>
            </Modal>
        </ConfigProvider>
    );
}
