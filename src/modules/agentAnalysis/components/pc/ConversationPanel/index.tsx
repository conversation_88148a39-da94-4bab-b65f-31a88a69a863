/**
 * @file 智能体分析-对话看板
 * <AUTHOR>
 */
import styled from '@emotion/styled';
import {Button, ConfigProvider, Modal, Popover, Spin, Table, Tooltip} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {css} from '@emotion/css';
import {LoadingOutlined} from '@ant-design/icons';
import api from '@/api/agentAnalysis';
import {AgentStatus, ConversationRecord, GetConversationResponse, QAItem} from '@/api/agentAnalysis/interface';
import {RenderError} from '@/components/CommonErrorBoundary/renderError';
import {ScrollContainer} from '@/components/ScrollContainer/ScrollComponent';
import {useUbcLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {useExtLog} from '@/modules/agentPromptEditV2/hooks/useExtLog';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import QAList from '../QADetail';
import {TabKey} from '../interface';
import {LoadStatus, TableColumnTitle} from './interface';
import NoData from './NoData';
import ExportModal from './ExportModal';

const StyledTable = styled(Table<ConversationRecord>)`
    &.ant-table-wrapper {
        border: 1px solid #ECEEF380 !important;
        border-radius: 9px;
    }
    .ant-table-body {
        &::-webkit-scrollbar {
            display: none; !important;
        }
    }

    .ant-table-header {
        .ant-table-cell {
            font-weight: 500;
        }
    }
`;

const customPopoverStyle = css`
    z-index: 999 !important;
    max-width: 966px;
    border-radius: 12px;
    box-shadow: 0px 30px 200px 0px #1d225233;
    background: white;
    padding: 16px;

    .ant-popover-content {
        padding: 16px;
        max-height: 406px;
        overflow-y: auto;
        .ant-popover-inner {
            padding: 0 !important;
            box-shadow: none !important;
        }
    }

    &::-webkit-scrollbar {
        width: 0.375rem;
        height: 0.375rem;
    }

    &::-webkit-scrollbar-thumb {
        background: #d3d9e6;
        border-radius: 3px;
    }
`;

const getColumns = (viewQADetail: (qaList: QAItem[]) => void): ColumnsType<ConversationRecord> => [
    {
        title: (
            <div>
                {TableColumnTitle.id}
                <Tooltip
                    title="一个对话流包含用户一次访问智能体时进行的所有对话。用户退出后再次访问或点击清空历史将开启新的对话流。"
                    placement="bottomRight"
                >
                    <span className="iconfont icon-questionCircle ml-1.5 cursor-pointer text-sm font-normal text-gray-400"></span>
                </Tooltip>
            </div>
        ),
        dataIndex: 'id',
        ellipsis: true,
        align: 'center',
        width: '112px',
        render: text => text || '--',
    },
    {
        title: TableColumnTitle.query,
        width: '40%',
        ellipsis: {
            showTitle: true,
        },
        render: (_, record) => (
            <div className="flex">
                {record.qaList.length > 1 && (
                    <div className="mr-1 rounded-[3px] bg-colorBgFormList px-[3px] font-medium">多轮问答</div>
                )}

                <Popover
                    overlayClassName={customPopoverStyle}
                    content={record.qaList[0].query}
                    placement="top"
                    autoAdjustOverflow={false}
                >
                    <p className="cursor-default overflow-hidden overflow-ellipsis">{record.qaList[0].query}</p>
                </Popover>
            </div>
        ),
    },
    {
        title: TableColumnTitle.answer,
        ellipsis: {
            showTitle: false,
        },
        width: '60%',
        render: (_, record) => (
            <Popover
                overlayClassName={customPopoverStyle}
                content={record.qaList[0].response}
                placement="top"
                autoAdjustOverflow={false}
            >
                <p className="cursor-default overflow-hidden overflow-ellipsis">{record.qaList[0].response}</p>
            </Popover>
        ),
    },
    {
        title: TableColumnTitle.date,
        width: '143px',
        render: (_, record) => <>{record.qaList[0].time}</>,
    },
    {
        title: TableColumnTitle.operate,
        dataIndex: 'authFlag',
        width: '86px',
        render: (_, record) => (
            <div className="cursor-pointer text-primary" onClick={() => viewQADetail(record.qaList)}>
                <span className={'iconfont icon-visual mr-1'}></span>
                查看
            </div>
        ),
    },
];

const emptyListData = {
    status: 0,
    privacyOptions: [],
    effectiveDate: 0,
    pageInfo: {dataList: [], total: 0, pageNo: 1, pageSize: 1},
};

const PAGE_SIZE = 10;

export default function ConversationPanel({appId}: {appId: string}) {
    const [pageNo, setPageNo] = useState(1);
    const [loadStatus, setLoadStatus] = useState<LoadStatus>(LoadStatus.default);
    const [requestError, setRequestError] = useState<any>();

    const {clickLog} = useUbcLogV2();

    const baseExt = useExtLog();
    const extLog = useMemo(() => {
        return {
            ...baseExt,
            [EVENT_EXT_KEY_CONST.ANALYSIS_TAB_NAME]: +TabKey.conversation,
        };
    }, [baseExt]);

    const [qaList, setQAList] = useState<QAItem[]>([]);
    const [showQAListModal, setShowQAListModal] = useState(false);
    const [showExportModal, setShowExportModal] = useState(false);

    const [listData, setListData] = useState<GetConversationResponse>(emptyListData);

    const [exporting, setExporting] = useState(false); // 是否正在导出对话

    const panelLoading = useMemo(() => {
        return (
            (loadStatus === LoadStatus.default || loadStatus === LoadStatus.loading) &&
            listData?.pageInfo?.dataList.length === 0
        );
    }, [listData?.pageInfo?.dataList.length, loadStatus]);

    const getConversationList = useCallback(() => {
        setLoadStatus(LoadStatus.loading);
        setListData(emptyListData);

        api.getConversation({
            appId,
            pageNo,
            pageSize: PAGE_SIZE,
        }).then(
            data => {
                setListData(data);
                setLoadStatus(LoadStatus.success);
            },
            e => {
                setListData(emptyListData);
                setLoadStatus(LoadStatus.error);
                setRequestError(e);
            }
        );
    }, [appId, pageNo]);

    const renderEmpty = useCallback(() => {
        switch (loadStatus) {
            case LoadStatus.error:
                return <RenderError error={requestError} onBtnClick={getConversationList} />;
            case LoadStatus.loading:
                return <Spin size="large" />;
        }
    }, [getConversationList, requestError, loadStatus]);

    /** 查看某条对话记录（一个session）的问答详情（可能是多轮问答） */
    const viewQADetail = useCallback(
        (qaList: QAItem[]) => {
            clickLog(EVENT_VALUE_CONST.CHECK_DIALOGUE, EVENT_PAGE_CONST.AGENT_ANALYSIS, extLog);
            setQAList(qaList);
            setShowQAListModal(true);
        },
        [clickLog, extLog]
    );

    const closeQAListModal = useCallback(() => {
        setShowQAListModal(false);
    }, []);

    const columns = useMemo(() => getColumns(viewQADetail), [viewQADetail]);

    const exportConversation = useCallback(() => {
        clickLog(EVENT_VALUE_CONST.DIALOGUE_RECORD_EXPORT, EVENT_PAGE_CONST.AGENT_ANALYSIS, extLog);
        setShowExportModal(true);
    }, [clickLog, extLog]);

    const handlePageChange = useCallback(
        (pageNo: number) => {
            setPageNo(pageNo);
            clickLog(EVENT_VALUE_CONST.NEXT_PAGE, EVENT_PAGE_CONST.AGENT_ANALYSIS, extLog);
        },
        [clickLog, extLog]
    );

    const exportDisabled = useMemo(() => {
        return !listData?.pageInfo || listData?.pageInfo?.dataList.length === 0 || exporting;
    }, [exporting, listData?.pageInfo]);

    useEffect(() => {
        (async () => {
            getConversationList();
        })();
    }, [getConversationList]);

    return (
        <ConfigProvider
            renderEmpty={renderEmpty}
            theme={{
                components: {
                    Table: {
                        headerColor: '#1E1F24',
                        headerBg: '#ECEEF34D',
                        rowHoverBg: '#ECEEF34D',
                    },
                    Button: {
                        controlHeight: 30,
                    },
                },
            }}
        >
            <div>
                <div className="mb-4 flex items-center justify-between">
                    <div className="flex items-center">
                        <p className="text-black-base text-lg font-medium">对话详情</p>
                        {(listData?.pageInfo?.total || 0) >= 100 && (
                            <p className="ml-2 text-gray-tertiary">仅展示最近100条对话记录，如需查看更多请选择导出</p>
                        )}
                    </div>
                    <Button
                        className={`rounded-[100px] border border-solid border-[#EDEEF0] text-center font-medium  ${
                            exportDisabled ? '' : 'hover:text-gray-tertiary'
                        }`}
                        disabled={exportDisabled}
                        onClick={exportConversation}
                    >
                        {exporting ? (
                            <>
                                导出中
                                <Spin
                                    indicator={<LoadingOutlined spin />}
                                    size="small"
                                    className="ml-1 text-[#1e1f2499]"
                                />
                            </>
                        ) : (
                            '导出对话'
                        )}
                    </Button>
                </div>
                {panelLoading ? (
                    <div className="flex h-[367px] items-center justify-center">
                        <Spin size="large" />
                    </div>
                ) : listData.status === AgentStatus.neverPublish ? (
                    <NoData content="发布智能体后即可查看对话记录哦~" appId={appId} />
                ) : !listData?.pageInfo || listData?.pageInfo?.dataList.length === 0 ? (
                    <NoData content="暂无对话记录" subContent="对话产生后将在24小时内更新至分析看板" appId={appId} />
                ) : (
                    <>
                        <StyledTable
                            columns={columns}
                            dataSource={listData?.pageInfo?.dataList}
                            rowClassName="border-0 font-normal"
                            sticky
                            scroll={{x: 'fit-content'}}
                            pagination={{
                                total: listData?.pageInfo?.total,
                                pageSize: listData?.pageInfo?.pageSize,
                                current: listData?.pageInfo?.pageNo,
                                onChange: handlePageChange,
                                hideOnSinglePage: true,
                                showSizeChanger: false,
                            }}
                        />
                        <Modal
                            title="问答详情"
                            open={showQAListModal}
                            centered
                            forceRender
                            width={750}
                            onCancel={closeQAListModal}
                            footer={null}
                        >
                            <ScrollContainer scrollY scrollbarWidth={4} className="-mr-5 mt-4 max-h-[490px] pr-4">
                                <QAList qaList={qaList} />
                            </ScrollContainer>
                        </Modal>
                        <ExportModal
                            show={showExportModal}
                            appId={appId}
                            showExportModal={showExportModal}
                            setShowExportModal={setShowExportModal}
                            effectiveDate={listData.effectiveDate!}
                            setExporting={setExporting}
                        />
                    </>
                )}
            </div>
        </ConfigProvider>
    );
}
