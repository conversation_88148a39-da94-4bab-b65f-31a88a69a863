/**
 * @file 智能体分析-对话看板-导出对话弹窗标题
 * <AUTHOR>
 */

import {useCallback} from 'react';
import {GetConversationCountResponse} from '@/api/agentAnalysis/interface';

export default function ExportModalTitle({condition}: {condition: GetConversationCountResponse | undefined}) {
    const getSubTitle = useCallback(() => {
        if (!condition) {
            return '';
        }
        if (condition.count === 0) {
            return '未查询到对话记录，请重新选择时间范围';
        }
        if (condition.count > condition.limit) {
            return `对话记录过多，请缩短时间范围后重试`;
        }
        return `已选择${condition.count}条对话记录`;
    }, [condition]);

    return (
        <div className="relative z-10 flex items-center bg-white">
            <p className="text-lg font-medium">请选择导出的时间范围</p>
            <p className="ml-2 text-sm font-normal text-gray-tertiary">{getSubTitle()}</p>
        </div>
    );
}
