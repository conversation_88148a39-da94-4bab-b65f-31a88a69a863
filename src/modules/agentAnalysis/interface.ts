import {AnalysisTabType} from '@/api/agentAnalysis/interface';

/**
 * 单位类型
 */
export enum UnitType {
    Num = '次',
    People = '人',
    Percent = '%',
    Round = '轮',
}

/**
 * 趋势类型 1为上升趋势，-1为下降趋势，0为持平
 */
export enum TrendType {
    /** 1为上升趋势 */
    Up = 1,
    /** -1为下降趋势 */
    Down = -1,
    /** 0为持平 */
    Same = 0,
}

/** 概览卡片数据类型 */
export interface OverviewCardInfo {
    /** 卡片名称 */
    name: string;
    /** 提示信息 */
    tips: string;
    /** 当前值 */
    value: string;
    /** 当前值在后端返回数据中的字段名 */
    valueKey: string;
    /** 当前单位 */
    unit: UnitType;
    /** 趋势类型，1为上升趋势，-1为下降趋势，0为持平 */
    trend: TrendType;
    /** 趋势值 */
    trendValue: string;
    /** 趋势值在后端返回数据中的字段名 */
    trendValueKey: string;
    /** 智能体排名 */
    rankValue: string;
    /** 智能体排名在后端返回数据中的字段名 */
    rankValueKey: string;
}

/**
 * 数据概览模块的卡片渲染数据
 */
export const DefaultOverviewCards: OverviewCardInfo[] = [
    {
        name: '累计对话次数',
        tips: '智能体首次上线后，用户累计对话次数',
        value: '',
        valueKey: 'pv',
        unit: UnitType.Num,
        trend: TrendType.Up,
        trendValue: '',
        trendValueKey: 'pvGrowth',
        rankValue: '',
        rankValueKey: 'pvRank',
    },
    {
        name: '累计对话用户数',
        tips: '智能体首次上线后，累计对话用户数',
        value: '',
        valueKey: 'uv',
        unit: UnitType.People,
        trend: TrendType.Up,
        trendValue: '',
        trendValueKey: 'uvGrowth',
        rankValue: '',
        rankValueKey: 'uvRank',
    },
    {
        name: '对话满意度',
        tips: '智能体首次上线后，累计点赞对话数/（累计点赞对话数+累计点踩对话数）',
        value: '',
        valueKey: 'userSatisfactionRatio',
        unit: UnitType.Percent,
        trend: TrendType.Up,
        trendValue: '',
        trendValueKey: 'userSatisfactionRatioGrowth',
        rankValue: '',
        rankValueKey: 'userSatisfactionRank',
    },
    {
        name: '累计曝光次数',
        tips: '智能体首次上线后，百度内各分发场景中，智能体的曝光次数，百度内分发场景包括百度APP、百度网页版、文心智能体平台官网',
        value: '',
        valueKey: 'searchDistributeNum',
        unit: UnitType.Num,
        trend: TrendType.Up,
        trendValue: '',
        trendValueKey: 'searchDistributeNumGrowth',
        rankValue: '',
        rankValueKey: 'searchDistributeRank',
    },
];

/** 智能体分析页面-分析类型 打点枚举值：1-对话分析 2-流量分析 3-用户分析 4-行为分析 5-商业分析 */
export const LogAnalysisTabTypeMap = {
    [AnalysisTabType.Conversation]: 1,
    [AnalysisTabType.Traffic]: 2,
    [AnalysisTabType.User]: 3,
    [AnalysisTabType.Action]: 4,
    [AnalysisTabType.Goods]: 5,
} as const;
