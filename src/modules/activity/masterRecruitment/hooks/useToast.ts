import {message} from 'antd';
import Toast from 'antd-mobile/es/components/toast';
import {useIsMobileStore} from '@/store/home/<USER>';

export enum ToastType {
    SUCCESS = 'success',
    ERROR = 'error',
    INFO = 'info',
    WARNING = 'warning',
}

interface ToastProps {
    type?: ToastType;
    msg: string;
}
export const useToast = () => {
    const isMobile = useIsMobileStore(store => store.isMobile);

    const showToast = ({msg, type}: ToastProps) => {
        if (isMobile) {
            Toast.show(msg);
        } else {
            switch (type) {
                case ToastType.SUCCESS:
                    message.success(msg);
                    break;
                case ToastType.ERROR:
                    message.error(msg);
                    break;
                case ToastType.WARNING:
                    message.warning(msg);
                    break;
                case ToastType.INFO:
                    message.info(msg);
                    break;
                default:
                    message.info(msg);
            }
        }
    };

    return {
        showToast,
    };
};
