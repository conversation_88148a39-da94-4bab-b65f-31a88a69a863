/**
 * @file 认证页面 - 已认证 & 认证步骤
 * <AUTHOR>
 */

import {Button} from 'antd';
import classNames from 'classnames';
import {useCallback, useMemo, useState} from 'react';
import {CareerAuthStatus, IdentityType, QualificationInfo} from '@/api/activity/masterRecruitment/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import Header from './components/Header';
import AuthTipsCard from './components/authTipsCard/index';
import {useMasterRecruitmentStore} from './store/index';
import {OperationInstructions, RetiredOperationInstructions} from './interface';

// 未授权模块
const UnauthorizedPromptModule = ({isMobile, identityType}: {isMobile: boolean; identityType: IdentityType | null}) => {
    const operations = useMemo(
        () => (identityType === IdentityType.RETIRED ? RetiredOperationInstructions : OperationInstructions),
        [identityType]
    );

    return (
        <div className={classNames(isMobile ? 'px-3 pb-4' : 'w-[820px]', 'min-h-[100vh]')}>
            <Header title="资质认证" />
            <div
                className={classNames(
                    'rounded-xl',
                    'bg-white',
                    isMobile ? 'flex flex-col gap-[9px] p-3' : 'flex h-[711px] flex-wrap gap-6 p-[31px]'
                )}
            >
                {operations.map(item => (
                    <AuthTipsCard item={item} key={item.step} isMobile={isMobile} />
                ))}
            </div>
        </div>
    );
};

const InfoCard = ({title, children}: {title: string; children: React.ReactNode}) => (
    <div className="mt-[9px] w-full rounded-xl bg-white py-4 pl-3 text-base">
        <div className="font-semibold">{title}</div>
        <div className="flex flex-col gap-[9px] pt-4 font-normal text-gray-tertiary">{children}</div>
    </div>
);

// 已授权模块
const AuthorizationInfoModule = ({
    qualificationInfo,
    identityType,
    setShowUnauthPromptModule,
}: {
    qualificationInfo: QualificationInfo;
    identityType: IdentityType | null;
    setShowUnauthPromptModule: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
    const setShowBasicInfoForm = useMasterRecruitmentStore(store => store.setShowBasicInfoForm);

    const backUnAuth = useCallback(() => setShowUnauthPromptModule(true), [setShowUnauthPromptModule]);

    // 确认信息 到 资质表单
    const goAuthform = useCallback(() => {
        setShowBasicInfoForm(true);
    }, [setShowBasicInfoForm]);

    return (
        <div className="relative min-h-[100vh]">
            <Header title="资质信息" />
            <div className="px-3">
                <InfoCard title="实名认证">
                    <div>真实姓名：{qualificationInfo.realName}</div>
                    <div>身份证号：{qualificationInfo.certCode}</div>
                </InfoCard>
                {identityType !== IdentityType.RETIRED && (
                    <InfoCard title="职业认证">
                        <div>职业/称号：{qualificationInfo.profession}</div>
                    </InfoCard>
                )}
            </div>
            <div className="absolute bottom-[36px] w-full px-4">
                <div className="flex gap-2">
                    <Button
                        onClick={backUnAuth}
                        className="h-[40px] flex-1 rounded-full border-none bg-[#E8F3FF] font-medium text-[#4E6EF2]"
                    >
                        重新认证
                    </Button>
                    <Button type="primary" className="h-[40px] flex-1 rounded-full font-medium" onClick={goAuthform}>
                        确认信息
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default function Index() {
    const {identityType, qualificationInfo} = useMasterRecruitmentStore(store => ({
        identityType: store.baseInfo.teacherType,
        qualificationInfo: store.qualification,
    }));
    const isMobile = useIsMobileStore(store => store.isMobile);

    // (真实性、职业信息)未通过
    // 是否展示 未授权模块
    const showUnauthPromptModuleDefault: boolean =
        identityType === IdentityType.RETIRED
            ? !qualificationInfo.realStatus
            : qualificationInfo.careerAuthStatus !== CareerAuthStatus.Pass;

    const [showUnauthPromptModule, setShowUnauthPromptModule] = useState<boolean>(showUnauthPromptModuleDefault);

    return (
        <div
            className={classNames('m-auto', {
                'max-w-[820px]': !isMobile,
            })}
        >
            {showUnauthPromptModule && <UnauthorizedPromptModule identityType={identityType} isMobile={isMobile} />}

            {!showUnauthPromptModule && (
                <AuthorizationInfoModule
                    identityType={identityType}
                    qualificationInfo={qualificationInfo}
                    setShowUnauthPromptModule={setShowUnauthPromptModule}
                />
            )}
        </div>
    );
}
