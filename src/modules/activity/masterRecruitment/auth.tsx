/**
 * @file 资质信息 - 资质信息表单 & 资质审核
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import classNames from 'classnames';
import {Button} from 'antd';
import {ClockCircleFilled, CloseCircleFilled} from '@ant-design/icons';
import {useIsMobileStore} from '@/store/home/<USER>';
import {CareerInfoStatus} from '@/api/activity/masterRecruitment/interface';
import Header from './components/Header';
import {useMasterRecruitmentStore} from './store';
import AuthForm from './authForm';
import CustomerServiceCard from './components/CustomerServiceCard';
import AuditCard from './components/AuditCard';
import AuditPanel from './components/AuditPanel';

const AuditStatusTextMap = {
    [CareerInfoStatus.Waiting]: {
        title: '',
        desc: '',
        subDesc: '',
        icon: '',
    },
    [CareerInfoStatus.Auditing]: {
        title: '资质审核中',
        desc: '预计1天内完成审核，审核结果将以短信通知您',
        subDesc: '审核时间为工作日10:00-19:00',
        icon: <ClockCircleFilled style={{fontSize: 18, color: '#FF8200'}} />,
    },
    [CareerInfoStatus.Failed]: {
        title: '审核不通过',
        desc: '很抱歉资质审核未通过，可修改资质信息或联系客服沟通',
        subDesc: '',
        icon: <CloseCircleFilled style={{fontSize: 18, color: '#FF3333'}} />,
    },
    [CareerInfoStatus.Pass]: {
        title: '',
        desc: '',
        subDesc: '',
        icon: '',
    },
};

const BasicAuditModule = ({
    basicStatus,
    setShowBasicAuditModule,
}: {
    basicStatus: CareerInfoStatus | null;
    setShowBasicAuditModule: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
    const basicStatusText = AuditStatusTextMap[basicStatus || CareerInfoStatus.Waiting];

    const goAuthform = useCallback(() => {
        setShowBasicAuditModule(false);
    }, [setShowBasicAuditModule]);

    return (
        <div>
            <div className="text-white">
                <Header title="资质审核" />
            </div>
            <div className="mx-3">
                <div className="mb-2 rounded-xl bg-white py-4 text-center">
                    <div className="pb-3 text-lg font-semibold">
                        <span className="mr-2">{basicStatusText.icon}</span>
                        {basicStatusText.title}
                    </div>
                    <div className={classNames('text-sm text-[#1F1F1F]', {'mb-6': !basicStatusText.subDesc})}>
                        {basicStatusText.desc}
                    </div>
                    {basicStatusText.subDesc && (
                        <div className="my-3 text-sm text-gray-quaternary">{basicStatusText.subDesc}</div>
                    )}
                    <div>
                        <Button className="rounded-full border-none bg-[#E8F3FF] text-[#4E6EF2]" onClick={goAuthform}>
                            修改资质信息
                        </Button>
                    </div>
                </div>

                <AuditCard className="flex flex-col items-center justify-center">
                    <CustomerServiceCard isWhiteBg />
                </AuditCard>
            </div>
        </div>
    );
};

export default function Index() {
    const {basicStatus} = useMasterRecruitmentStore(store => ({
        basicStatus: store.baseInfo.status,
    }));
    const isMobile = useIsMobileStore(store => store.isMobile);

    const [showBasicAuditModule, setShowBasicAuditModule] = useState<boolean>(
        basicStatus === CareerInfoStatus.Auditing || basicStatus === CareerInfoStatus.Failed
    );

    useEffect(() => {
        setShowBasicAuditModule(basicStatus === CareerInfoStatus.Auditing || basicStatus === CareerInfoStatus.Failed);
    }, [basicStatus]);

    return (
        <>
            {!showBasicAuditModule && (
                <div className={classNames('m-auto ', {'max-w-[820px]': !isMobile})}>
                    <AuthForm setShowBasicAuditModule={setShowBasicAuditModule} />
                </div>
            )}
            {showBasicAuditModule && (
                <AuditPanel>
                    <BasicAuditModule basicStatus={basicStatus} setShowBasicAuditModule={setShowBasicAuditModule} />
                </AuditPanel>
            )}
        </>
    );
}
