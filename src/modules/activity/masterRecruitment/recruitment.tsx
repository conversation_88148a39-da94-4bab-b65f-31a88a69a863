/**
 * @file 主活动页
 * <AUTHOR>
 */

import {useCallback, useEffect} from 'react';
import {Anchor, FloatButton} from 'antd';
import classNames from 'classnames';
import styled from '@emotion/styled';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useIsMobileStore} from '@/store/home/<USER>';
import UserAvatar from '@/modules/activity/developerEarning/components/center/UserAvatar';
import ExportFileApi from '@/utils/file';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {useMasterRecruitmentStore} from './store/index';
import {useToast} from './hooks/useToast';
import {HomeImages, RuleType} from './interface';

const StyledAnchor = styled(Anchor)`
    font-size: 1.25rem !important;
    background: #73a6ff;
    width: 100%;
    &::before {
        border: none !important;
    }
    .ant-anchor-ink {
        display: none;
    }
    .ant-anchor-ink-visible {
        display: none;
    }
    .ant-anchor-link-title {
        color: #fff !important;
        font-size: 1.125rem !important;
        font-weight: 500;
    }
    .ant-anchor-link {
        color: #fff !important;
        &.ant-anchor-link-active {
            .ant-anchor-link-title {
                color: #5562f2 !important;
            }
        }
    }
    &.pc-styled-anchor {
        .ant-anchor-link {
            padding-inline: 48px 0 !important;
        }
    }
    &.wise-styled-anchor {
        .ant-anchor-link-title {
            font-size: 1rem !important;
        }
    }
`;

export default function Index() {
    const isLogin = useUserInfoStore(store => store.isLogin);
    // 点击个人，未登陆展示登陆，已登陆展示弹窗组件
    const uniformLogin = useUniformLogin();
    const isMobile = useIsMobileStore(store => store.isMobile);

    const {setShowIdentityModal, setShowActivityRuleModal, setRuleModalType} = useMasterRecruitmentStore(store => ({
        setShowIdentityModal: store.setShowIdentityModal,
        setShowActivityRuleModal: store.setShowActivityRuleModal,
        setRuleModalType: store.setRuleModalType,
    }));

    const {displayLog, clickLog} = useUbcLogV3();

    // 个人提报
    const handleSingleSubmit = useCallback(() => {
        if (isLogin) {
            // 点击教师个人提报 打点
            clickLog(EVENT_VALUE_CONST.TEACHER_SUBMIT, {}, EVENT_PAGE_CONST.LIGHTHOUSE_RECRUIT_ACTIVITY);
            // 展示弹窗组件
            setShowIdentityModal(true);
        } else {
            // 展示登陆
            uniformLogin();
        }
    }, [isLogin, clickLog, setShowIdentityModal, uniformLogin]);

    // 机构提报
    const handleInstitutionSubmit = useCallback(() => {
        if (isLogin) {
            // 点击机构提报 打点
            clickLog(EVENT_VALUE_CONST.SCHOOL_SUBMIT, {}, EVENT_PAGE_CONST.LIGHTHOUSE_RECRUIT_ACTIVITY);
            // 跳转机构提报页
            window.location.href =
                'https://agents.baidu.com/activity/form/detail?formId=b3c9caa8f13a4ad0a80117520b1397f5';
        } else {
            // 展示登陆
            uniformLogin();
        }
    }, [clickLog, isLogin, uniformLogin]);

    const {showToast} = useToast();

    // 下载资料包
    const handleDownload = useCallback(async () => {
        try {
            showToast({
                msg: '下载中...',
            });
            await ExportFileApi.downloadFileByBlob({
                url: HomeImages.downloadFile,
                fileName: '灯塔计划活动规则.pdf',
            });
        } catch (error) {
            console.error('下载失败', error);
        }
    }, [showToast]);

    // 处理活动规则
    const handleActivityRule = useCallback(() => {
        setRuleModalType(RuleType.ActivityAgreement);
        setShowActivityRuleModal(true);
    }, [setRuleModalType, setShowActivityRuleModal]);

    useEffect(() => {
        // 活动招募页 曝光 打点
        displayLog(EVENT_PAGE_CONST.LIGHTHOUSE_RECRUIT_ACTIVITY);
    }, [displayLog]);

    return (
        <div className="relative bg-[#E1EFFF]">
            <div
                className={classNames('fixed left-0 top-0 flex w-full items-center justify-center bg-[#73A6FF]', {
                    'h-[46px]': isMobile,
                    'h-[52px]': !isMobile,
                })}
            >
                <StyledAnchor
                    direction="horizontal"
                    className={classNames('text-lg text-white', {
                        'pc-styled-anchor': !isMobile,
                        'wise-styled-anchor': isMobile,
                    })}
                    items={[
                        {
                            key: 'home',
                            href: '#home',
                            title: '首页',
                        },
                        {
                            key: 'description',
                            href: '#description',
                            title: '项目介绍',
                        },
                        {
                            key: 'equity',
                            href: '#equity',
                            title: '项目权益',
                        },
                    ]}
                />
                <div
                    className={classNames('absolute right-0 ', {
                        'top-[-1rem]': !isMobile,
                        'top-0': isMobile,
                    })}
                >
                    <UserAvatar />
                </div>
            </div>
            <div id="scrollable-container">
                <div
                    id="home"
                    className={classNames('', {
                        'pt-[46px]': isMobile,
                        'pt-[52px]': !isMobile,
                    })}
                    style={{
                        width: '100vw',
                        textAlign: 'center',
                        background: 'rgba(0,255,0,0.02)',
                    }}
                >
                    <img src={HomeImages.banner} alt="首页" className="w-full" />
                </div>
                <div
                    className={classNames('m-auto', {
                        'max-w-[860px]': !isMobile,
                    })}
                >
                    <div
                        id="description"
                        style={{
                            textAlign: 'center',
                            background: 'rgba(0,255,0,0.02)',
                        }}
                    >
                        <img src={HomeImages.projectDesc} alt="项目介绍" className="w-full" />
                    </div>
                    <div
                        id="equity"
                        className="pb-10"
                        style={{
                            textAlign: 'center',
                            background: 'rgba(0,255,0,0.02)',
                        }}
                    >
                        <img src={HomeImages.ai} alt="AI赋能" className="w-full" />
                        <img src={HomeImages.OfficialHonor} alt="官方荣誉" className="w-full" />
                        <img src={HomeImages.millionFlow} alt="亿级流量" className="w-full" />
                    </div>
                </div>
            </div>
            <div>
                <div
                    className={classNames('fixed bottom-4 flex justify-center gap-4', {
                        'm-auto ml-[50%] max-w-[860px] translate-x-[-50%]': !isMobile,
                        'w-full': isMobile,
                    })}
                >
                    <div
                        onClick={handleInstitutionSubmit}
                        className={classNames('cursor-pointer', {
                            'w-[15.625rem]': !isMobile,
                        })}
                    >
                        <img src={HomeImages.institutionSubmit} alt="学校机构提报" />
                    </div>
                    <div
                        onClick={handleSingleSubmit}
                        className={classNames('cursor-pointer', {
                            'w-[15.625rem]': !isMobile,
                        })}
                    >
                        <img src={HomeImages.singleSubmit} alt="教师个人提报" />
                    </div>
                </div>
            </div>

            {/* 悬浮按钮 */}
            <FloatButton.Group shape="circle" style={{insetInlineEnd: 24, bottom: 120, right: 12}}>
                <FloatButton
                    className="border shadow-md"
                    icon={
                        <span className="iconfont icon-download ml-[-3px] text-2xl">
                            {/* <img src={HomeImages.downloadIcon} alt="下载" className="w-full" /> */}
                        </span>
                    }
                    onClick={handleDownload}
                />
                <FloatButton
                    className="border shadow-md"
                    icon={<span className="iconfont icon-reason ml-[-3px] text-2xl"></span>}
                    onClick={handleActivityRule}
                />
            </FloatButton.Group>
        </div>
    );
}
