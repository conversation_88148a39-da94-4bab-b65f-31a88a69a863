import {
    IdentityType,
    IUnitType,
    IGradeType,
    ISubjectType,
    IHonor,
    IFansSection,
} from '@/api/activity/masterRecruitment/interface';

// 身份信息内容
export interface IdentityInfo {
    type: IdentityType;
    name: string;
    imageUrl?: string;
    desc: string;
}

// 身份信息映射
export const IdentityMap: IdentityInfo[] = [
    {
        type: IdentityType.EMPLOYED,
        name: '在职教师',
        imageUrl: '',
        desc: '学校/教育机构老师、教育事业单位/教研机构',
    },
    {
        type: IdentityType.RETIRED,
        name: '退休教师',
        imageUrl: '',
        desc: '已退休的学校/单位/机构老师',
    },
    {
        type: IdentityType.KOL,
        name: '教育博主',
        imageUrl: '',
        desc: '内容平台教育博主、教材作者、独立讲师',
    },
];

export interface EquityInfo {
    title: string;
    desc: React.ReactNode | string;
    imageUrl?: string;
    imgDesc?: React.ReactNode | string;
    showImgBorder?: boolean;
}

export const HomeImages = {
    banner: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E5%A4%B4%E5%9B%BE2%E5%80%8D.png',
    projectDesc:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E9%A1%B9%E7%9B%AE%E4%BB%8B%E7%BB%8D.png',
    ai: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/AI%E8%B5%8B%E8%83%BD%E5%85%8D%E8%B4%B9%E6%95%99%E5%AD%A6%E8%B5%84%E6%BA%90.png',
    millionFlow:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E4%BA%BF%E7%BA%A7%E6%B5%81%E9%87%8F.png',
    OfficialHonor:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E5%AE%98%E6%96%B9%E8%8D%A3%E8%AA%89.png',
    singleSubmit:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E6%95%99%E5%B8%88%E4%B8%AA%E4%BA%BA.png',
    institutionSubmit:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E5%AD%A6%E6%A0%A1%E6%9C%BA%E6%9E%84.png',
    downloadIcon: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/Object.png',
    downloadFile:
        'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E7%81%AF%E5%A1%94%E8%AE%A1%E5%88%92-%E8%AF%B4%E6%98%8E%E6%89%8B%E5%86%8C.pdf',
};

export interface IOperationInstructions {
    step: number;
    title: string;
    desc: React.ReactNode | string;
    imageUrl?: string;
    imgDesc?: string;
    downloadFileName?: string;
}

// 认证步骤-在职教师&kol博主
export const OperationInstructions: IOperationInstructions[] = [
    {
        step: 1,
        title: '扫码认证',
        imageUrl: 'https://auth.baidu.com/authplus/career?from=wenxinagent&type=0',
        desc: '请使用百度APP扫码完成身份职业认证',
        imgDesc: '下载二维码',
        downloadFileName: '职业资质认证二维码',
    },
    {
        step: 2,
        title: '实名认证',
        imageUrl: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/实名认证.png',
        desc: '扫码后完成实名认证和刷脸认证',
    },
    {
        step: 3,
        title: '选择职称',
        imageUrl: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/选择职称.png',
        desc: '职位/称号选择“教育科研/科普教学”',
    },
    {
        step: 4,
        title: '资料上传',
        imageUrl: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/资料上传.png',
        desc: '填写单位&上传证明',
    },
];

// 实名认证
// http://dev.passport.baidu.com/v1/#/solution/view/81/177
const realAuthPath = `https://wappass.baidu.com/v6/realName?tpl=wise&adapter=3&okU=${encodeURIComponent(
    window.location.href
)}`;

// 认证步骤-退休教师
export const RetiredOperationInstructions: IOperationInstructions[] = [
    {
        step: 1,
        title: '扫码认证',
        imageUrl: realAuthPath,
        desc: '请使用百度APP扫码完成身份职业认证',
        imgDesc: '下载二维码',
        downloadFileName: '实名认证二维码',
    },
    {
        step: 2,
        title: '实名认证',
        imageUrl: 'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/实名认证.png',
        desc: '扫码后完成实名认证和刷脸认证',
    },
];

interface IOptions {
    label: string;
    value: IUnitType | IGradeType | ISubjectType | IHonor | IFansSection;
}

// 所属类型 - 在职教师 & 退休教师
export const TCHUnitTypeOptions: IOptions[] = [
    {
        label: '公立学校',
        value: IUnitType.PublicSchool,
    },
    {
        label: '教育行政事业单位、教研机构',
        value: IUnitType.EducationalInstitution,
    },
    {
        label: '民办私立、国际学校',
        value: IUnitType.PrivateInternationalSchool,
    },
    {
        label: '教材教辅出版公司',
        value: IUnitType.TextbookPublisher,
    },
    {
        label: '教培机构',
        value: IUnitType.TrainingOrganization,
    },
    {
        label: '教育领域MCN/KOL',
        value: IUnitType.EducationMCN,
    },
    {
        label: '其他',
        value: IUnitType.Other,
    },
];

// 所属类型 - 教育博主
export const KOLUnitTypeOptions: IOptions[] = [
    {
        label: '教育领域MCN/KOL',
        value: IUnitType.EducationMCN,
    },
    {
        label: '教材教辅出版公司',
        value: IUnitType.TextbookPublisher,
    },
    {
        label: '教培机构',
        value: IUnitType.TrainingOrganization,
    },
    {
        label: '独立讲师',
        value: IUnitType.IndependentLecturer,
    },
    {
        label: '独立教材作者',
        value: IUnitType.IndependentTextbookAuthor,
    },
    {
        label: '其他',
        value: IUnitType.Other,
    },
];

// 年级
export const IGradeOptions: IOptions[] = [
    {
        label: '小学',
        value: IGradeType.PRIMARY,
    },
    {
        label: '初中',
        value: IGradeType.JUNIORHIGH,
    },
    {
        label: '高中',
        value: IGradeType.SENIORHIGH,
    },
];

// 学科
export const ISubjectOptions: IOptions[] = [
    {
        label: '语文',
        value: ISubjectType.CHINESE,
    },
    {
        label: '数学',
        value: ISubjectType.MATH,
    },
];

// 荣誉选项
export const HonorOptions: IOptions[] = [
    {
        label: '国家级教育、教研相关机构成员',
        value: IHonor.NationalEducationMember,
    },
    {
        label: '国家级官方、权威荣誉奖项',
        value: IHonor.NationalAward,
    },
    {
        label: '国家级学术成果认证',
        value: IHonor.NationalAcademicCertification,
    },
    {
        label: '省级含直辖市教育、教研相关机构成员',
        value: IHonor.ProvincialEducationMember,
    },
    {
        label: '省级官方、权威荣誉奖项',
        value: IHonor.ProvincialAward,
    },
    {
        label: '省级学术成果认证',
        value: IHonor.ProvincialAcademicCertification,
    },
    {
        label: '全国头部教培机构s级名师',
        value: IHonor.TopTrainingInstructor,
    },
    {
        label: '市区县教育、教研相关机构成员',
        value: IHonor.DistrictEducationMember,
    },
    {
        label: '市区县官方、权威荣誉奖项',
        value: IHonor.DistrictAward,
    },
    {
        label: '其他',
        value: IHonor.Other,
    },
    {
        label: '暂无以上荣誉',
        value: IHonor.None,
    },
];

export enum RuleType {
    // 合作协议
    CooperationAgreement = 1,
    // 活动规则
    ActivityAgreement = 2,
}

// 粉段
export const fansSectionOptions: IOptions[] = [
    {
        label: '未运营账号',
        value: -1,
    },
    {
        label: '1万粉以下',
        value: IFansSection.Under10K,
    },
    {
        label: '1万~5万粉',
        value: IFansSection.TenTo50K,
    },
    {
        label: '5万~10万粉',
        value: IFansSection.FiftyTo100K,
    },
    {
        label: '10万~20万粉',
        value: IFansSection.HundredTo200K,
    },
    {
        label: '20万粉以上',
        value: IFansSection.Over200K,
    },
];
