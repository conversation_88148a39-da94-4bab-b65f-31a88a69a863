/* eslint-disable complexity */
import {immer} from 'zustand/middleware/immer';
import {createWithEqualityFn} from 'zustand/traditional';
import {shallow} from 'zustand/shallow';
import api from '@/api/activity/masterRecruitment/index';
import {createAgent} from '@/api/createCityAgent/index';
import {AgentSource} from '@/api/createCityAgent/interface';
import {
    ActivityInfo,
    CareerInfoStatus,
    AuthStatus,
    CareerAuthStatus,
    IdentityType,
    AgentAuditStatus,
    SourceType,
} from '@/api/activity/masterRecruitment/interface';
import {AgentCreateRes} from '@/api/createCityAgent/interface';
import {publishTestedAgent} from '@/api/agentEdit';
import {AgentPermission} from '@/api/agentEdit/interface';
import {RuleType} from '../interface';

interface State extends ActivityInfo {
    showIdentityModal: boolean;
    showActivityRuleModal: boolean;
    ruleModalType: RuleType;
    showBasicInfoForm: boolean; // 是否展示资质信息表单 -- 主要用于认证页点击确认信息后，页面展示逻辑
}

const initState = {
    baseInfo: {
        unitType: null,
        platform: '',
        userName: '',
        phone: '',
        garde: null,
        subject: null,
        honor: null,
        honorDetail: '',
        teacherCertification: '',
        status: CareerInfoStatus.Waiting,
        teacherType: null,
        fansSection: null,
        indexLink: '',
        msg: '',
    },
    qualification: {
        realStatus: AuthStatus.Failed,
        realName: '',
        certCode: '',
        careerAuthStatus: CareerAuthStatus.UnAuth,
        profession: '',
    },
    agentInfo: {
        appId: '',
        status: null,
        name: '',
        overview: '',
        logoUrl: '',
        versionCodeOnSave: 0,
    },
    showIdentityModal: false,
    showActivityRuleModal: false,
    ruleModalType: RuleType.ActivityAgreement,
    showBasicInfoForm: false, // 是否展示资质信息表单 -- 主要用于认证页点击确认信息后，页面展示逻辑
};

interface Action {
    // 获取活动信息
    fetchActivityInfo: () => void;
    // 创建智能体
    createTeacherAgent: () => Promise<AgentCreateRes>;
    // 发布智能体
    publishTeacherAgent: () => void;
    // 设置身份类型
    setIdentityType: (teacherType: IdentityType) => void;
    // 设置智能体id
    setAgentId: (agentId: string) => void;
    // 显示身份选择弹窗
    setShowIdentityModal: (show: boolean) => void;
    // 关闭身份选择弹窗
    setShowActivityRuleModal: (show: boolean) => void;
    // 设置规则弹窗类型
    setRuleModalType: (type: RuleType) => void;
    // 设置展示资质信息表单 -- 主要用于认证页点击【确认信息】后
    setShowBasicInfoForm: (show: boolean) => void;
}

export const useMasterRecruitmentStore = createWithEqualityFn<State & Action>()(
    immer((set, get) => ({
        ...initState,
        async fetchActivityInfo() {
            return api.getActivityDetail({source: SourceType.TeacherRecruitAgent}).then(res => {
                set(state => {
                    state.baseInfo = res.careerBasicInfo || initState.baseInfo;
                    state.qualification = res.personInfo || initState.qualification;
                    state.agentInfo = res.agentInfo || initState.agentInfo;
                });
                return res;
            });
        },
        async createTeacherAgent() {
            return createAgent({source: AgentSource.TeacherRecruitAgent}).then(res => {
                const status = get().agentInfo.status;
                set(state => {
                    state.agentInfo = {...res, status};
                });
                return res;
            });
        },
        async publishTeacherAgent() {
            publishTestedAgent(get().agentInfo.appId, AgentPermission.PUBLIC).then(() => {
                set(state => {
                    state.agentInfo.status = AgentAuditStatus.AUDITING;
                });
            });
        },
        setIdentityType(teacherType: IdentityType) {
            set(state => {
                state.baseInfo.teacherType = teacherType;
            });
        },
        setAgentId(agentId: string) {
            set(state => {
                state.agentInfo.appId = agentId;
            });
        },

        setShowIdentityModal(show: boolean) {
            set(state => {
                state.showIdentityModal = show;
            });
        },
        setShowActivityRuleModal(show: boolean) {
            set(state => {
                state.showActivityRuleModal = show;
            });
        },

        setRuleModalType(type: RuleType) {
            set(state => {
                state.ruleModalType = type;
            });
        },

        setShowBasicInfoForm(show: boolean) {
            set(state => {
                state.showBasicInfoForm = show;
            });
        },
    })),
    shallow
);
