/**
 * @file 活动智能体编辑页面
 * <AUTHOR>
 */

import {useEffect, useState} from 'react';
import {ConfigProvider, Form} from 'antd';
import {usePromptEditStoreV2} from '@/store/agent/promptEditStoreV2';
import Loading from '@/components/Loading/LingJingLoading';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
// import AudioSelect from '@/modules/agentPromptEditV2/mobile/components/AudioSelect/index';
import AgentForm from '@//modules/agentPromptEditV2/mobile/components/AgentForm';
import AgentName from '@/components/mobile/AgentName';
import {useMasterRecruitmentStore} from './store/index';
import Header from './components/Header';
import PublishButton from './components/PublishButton';
import Avatar from './components/Avatar';
import Overview from './components/Overview';
// import DigitalManFigure from './components/DigitalManFigure';

function BaseLayout() {
    const {appId, createTeacherAgent} = useMasterRecruitmentStore(store => ({
        appId: store.agentInfo.appId,
        createTeacherAgent: store.createTeacherAgent,
    }));

    const {agentConfig, reset, fetchAgentConfig, updateWithServerAgentConfig} = usePromptEditStoreV2(store => ({
        reset: store.reset,
        fetchAgentConfig: store.fetchAgentConfig,
        updateWithServerAgentConfig: store.updateWithServerAgentConfig,
        agentConfig: store.agentConfig,
    }));

    const form = Form.useFormInstance<AgentConfigV2>();

    const [loading, setLoading] = useState(true);

    useEffect(() => {
        (async () => {
            if (appId) {
                setLoading(true);
                await fetchAgentConfig({appId, formInstance: form, isInit: true});
                setLoading(false);
            } else {
                // 初始化创建，获取appid
                setLoading(true);
                const res = await createTeacherAgent();
                const agentInfo = Object.assign(form.getFieldValue('agentInfo') || {}, res);
                form.setFieldValue('agentInfo', agentInfo);
                agentConfig.agentInfo = Object.assign({}, agentConfig.agentInfo, res);
                agentConfig.versionCodeOnSave = res.versionCodeOnSave || 0;
                updateWithServerAgentConfig(Object.assign(agentConfig));
                setLoading(false);
            }
        })();

        return () => {
            reset();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="relative h-[100vh]">
            <Header title="配置智能体" />
            <div className="px-3 pt-4">
                {/* 头像 */}
                <Avatar appId={appId} />
                {/* 名称 */}
                <AgentName name={['agentInfo', 'name']} />
                {/* 简介 */}
                <Overview />
                {/* 数字人 */}
                {/* <DigitalManFigure /> */}
                {/* 声音 */}
                {/* <AudioSelect /> */}
                {/* 快捷插件保留FormValue便于移动端更新不要漏掉参数 */}
                <Form.Item noStyle hidden name={['agentInfo', 'shortcuts']} />
                {/* 移动端暂不支持商业化能力分发分成授权编辑，数据透传 */}
                <Form.Item name={['agentJson', 'businessComponent']} noStyle />
                <Form.Item name={['agentInfo', 'appId']} noStyle />
            </div>
            <div className="absolute bottom-4 w-full px-3">
                <PublishButton />
            </div>
        </div>
    );
}

export default function MasterAgentEdit() {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        fontSize: 14,
                        fontWeight: 400,
                    },
                    Input: {
                        colorBorder: 'transparent',
                        fontSize: 16,
                        colorErrorText: 'red',
                        colorTextPlaceholder: '#848691',
                        colorText: '#1E1F24',
                    },
                },
            }}
        >
            <AgentForm>
                <CommonErrorBoundary>
                    <BaseLayout />
                </CommonErrorBoundary>
            </AgentForm>
        </ConfigProvider>
    );
}
