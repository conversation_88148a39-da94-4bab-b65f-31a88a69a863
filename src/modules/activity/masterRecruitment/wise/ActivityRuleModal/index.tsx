/**
 * @file 活动规则弹窗
 * <AUTHOR>
 */
import {Button} from 'antd';
import PopupM from '@/components/mobile/Popup';
import ActivityRule from '../../components/ActivityRule/index';
import {useMasterRecruitmentStore} from '../../store/index';
import {RuleType} from '../../interface';

export default function PcActivityRuleModal({
    showActivityRuleModal,
    handleOk,
    handleClose,
}: {
    showActivityRuleModal: boolean;
    handleOk: () => void;
    handleClose: () => void;
}) {
    const {ruleModalType} = useMasterRecruitmentStore(store => ({
        ruleModalType: store.ruleModalType,
    }));
    return (
        <div>
            <PopupM
                visible={showActivityRuleModal}
                title={ruleModalType === RuleType.ActivityAgreement ? '灯塔计划活动规则' : '合作协议'}
                bodyStyle={{height: '90%'}}
                headerClassName="leading-[18px] -mb-[6px]"
                showCloseButton
                onClose={handleClose}
                closeOnMaskClick
            >
                <div className="pb-8">
                    <div className="h-[calc(90vh-86px)] overflow-y-auto rounded-xl bg-colorBgFormList p-3">
                        {ruleModalType === RuleType.ActivityAgreement ? <ActivityRule /> : <div>合作协议</div>}
                        {ruleModalType === RuleType.CooperationAgreement && (
                            <div className="py-4">
                                <Button
                                    type="primary"
                                    onClick={handleOk}
                                    className="h-[40px] w-full rounded-full font-medium"
                                >
                                    阅读并同意
                                </Button>
                            </div>
                        )}
                    </div>
                    {/* <div className='pb-3'></div> */}
                </div>
            </PopupM>
        </div>
    );
}
