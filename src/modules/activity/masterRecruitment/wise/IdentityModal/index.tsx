/**
 * @file 身份选择弹窗
 * <AUTHOR>
 */

import {Button} from 'antd';
import PopupM from '@/components/mobile/Popup';
import {IdentityType} from '@/api/activity/masterRecruitment/interface';
import IdentitySelect from '../../components/IdentitySelect/index';

export default function WiseIdentityModal({
    showIdentityModal,
    identityType,
    setIdentityType,
    handleOk,
    handleClose,
}: {
    showIdentityModal: boolean;
    identityType: IdentityType | null;
    setIdentityType: (e: IdentityType) => void;
    handleOk: () => void;
    handleClose: () => void;
}) {
    return (
        <div>
            <PopupM
                visible={showIdentityModal}
                title="身份类型"
                headerClassName="leading-[18px] -mb-[6px]"
                showCloseButton
                onClose={handleClose}
                closeOnMaskClick
            >
                <>
                    <IdentitySelect value={identityType} setValue={setIdentityType} />
                    <div className="grid grid-cols-2 gap-2 pb-8 pt-4">
                        <Button
                            onClick={handleClose}
                            className="h-[40px] rounded-full border-none bg-[#E8F3FF] font-medium text-[#4E6EF2]"
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={handleOk}
                            className="h-[40px] rounded-full font-medium"
                            disabled={identityType === null}
                        >
                            确认
                        </Button>
                    </div>
                </>
            </PopupM>
        </div>
    );
}
