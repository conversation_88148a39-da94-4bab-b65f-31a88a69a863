export const setMetaTag = (property: string, content: string) => {
    const meta = document.createElement('meta');
    meta.setAttribute('property', property);
    meta.setAttribute('content', content);
    document.head.appendChild(meta);
};

export const initInboxShare = () => {
    const isAndroid = window.navigator.userAgent.includes('Android');
    const title = '灯塔计划 | 百度AI数字人教育普惠计划';
    // const titleStr = encodeURIComponent('灯塔计划 | 百度AI数字人教育普惠计划');
    window.BoxShareData = {
        options: {
            type: isAndroid ? 1 : 'url',
            mediaType: 'all',
            title: title,
            content: title,
            linkUrl: location.href,
            source: '1240000000000000',
            imageUrl:
                'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/lingjing-icon.png',
            iconUrl: 'https://now.bdstatic.com/store/v2/43bbe5a/lingjing-fe/b2e2b23/favicon.ico',
            wbtitle: title,
        },
    };
};
