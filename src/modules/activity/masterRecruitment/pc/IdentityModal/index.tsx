/**
 * @file 身份选择弹窗
 * <AUTHOR>
 */
import {Modal} from 'antd';
import {IdentityType} from '@/api/activity/masterRecruitment/interface';
import IdentitySelect from '../../components/IdentitySelect/index';

export default function PcIdentityModal({
    showIdentityModal,
    identityType,
    setIdentityType,
    handleOk,
    handleClose,
}: {
    showIdentityModal: boolean;
    identityType: IdentityType | null;
    setIdentityType: (e: IdentityType) => void;
    handleOk: () => void;
    handleClose: () => void;
}) {
    return (
        <div>
            <Modal
                title="身份选择"
                centered
                open={showIdentityModal}
                okText="确认"
                okButtonProps={{
                    disabled: identityType === null,
                    className: 'rounded-full',
                }}
                cancelText="取消"
                cancelButtonProps={{
                    className: 'rounded-full',
                    style: {borderColor: 'rgba(237, 238, 240, 1)'},
                }}
                onOk={handleOk}
                onCancel={handleClose}
                width={600}
            >
                <div className="pt-4">
                    <IdentitySelect value={identityType} setValue={setIdentityType} />
                </div>
            </Modal>
        </div>
    );
}
