/**
 * @file 活动规则弹窗
 * <AUTHOR>
 */
import {Modal, Button} from 'antd';
import ActivityRule from '../../components/ActivityRule/index';
import {useMasterRecruitmentStore} from '../../store/index';
import {RuleType} from '../../interface';

export default function PcActivityRuleModal({
    showActivityRuleModal,
    handleOk,
    handleClose,
}: {
    showActivityRuleModal: boolean;
    handleOk: () => void;
    handleClose: () => void;
}) {
    const {ruleModalType} = useMasterRecruitmentStore(store => ({
        ruleModalType: store.ruleModalType,
    }));
    return (
        <div>
            <Modal
                title={ruleModalType === RuleType.ActivityAgreement ? '灯塔计划活动规则' : '合作协议'}
                centered
                open={showActivityRuleModal}
                okText="确认"
                okButtonProps={{
                    className: 'rounded-full',
                }}
                cancelText="取消"
                cancelButtonProps={{
                    className: 'rounded-full',
                    style: {borderColor: 'rgba(237, 238, 240, 1)'},
                }}
                onOk={handleOk}
                onCancel={handleClose}
                width={600}
                footer={[
                    <Button key="submit" type="primary" onClick={handleOk} className="rounded-full">
                        {ruleModalType === RuleType.ActivityAgreement ? '已知晓' : '阅读并同意'}
                    </Button>,
                ]}
            >
                <div className="h-[400px] overflow-y-auto rounded-xl bg-colorBgFormList p-3">
                    {ruleModalType === RuleType.ActivityAgreement ? <ActivityRule /> : <div>合作协议</div>}
                </div>
            </Modal>
        </div>
    );
}
