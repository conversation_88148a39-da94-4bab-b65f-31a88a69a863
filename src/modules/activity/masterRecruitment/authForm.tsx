/**
 * @file 资质信息 - 资质信息表单 & 资质审核
 * <AUTHOR>
 */

import {Alert, ConfigProvider, Form} from 'antd';
import React, {useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {CareerInfoStatus, IdentityType, IHonor} from '@/api/activity/masterRecruitment/interface';
import InputWithLabel from '@/components/mobile/InputWithLabel';
import {InputTextAlign} from '@/components/mobile/AgentName/interface';
import {getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
import {getStringLength} from '@/utils/text';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import {validatePhone} from '@/utils/formValidator';
import RadioWithLabel from './components/RadioWithLabel';
import {
    fansSectionOptions,
    HonorOptions,
    IGradeOptions,
    ISubjectOptions,
    KOLUnitTypeOptions,
    TCHUnitTypeOptions,
} from './interface';
import SelectWidthLabel from './components/SelectWithLabel';
import UploadFileForm from './components/uploadFile/uploadFileForm';
import Header from './components/Header';
import {useMasterRecruitmentStore} from './store';
import BasicFormSubButtons from './components/BasicFormButtons';
import UploadImageWithLabel from './components/uploadImage/index';

const StyledAlert = styled(Alert)`
    .ant-alert-message {
        margin-bottom: 4px !important;
    }
    .ant-alert-description {
        color: #848691 !important;
    }
`;

function BasicFormLayout({
    setShowBasicAuditModule,
}: {
    setShowBasicAuditModule: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    const {baseInfo, identityType} = useMasterRecruitmentStore(store => ({
        baseInfo: store.baseInfo,
        identityType: store.baseInfo.teacherType,
    }));

    const form = Form.useFormInstance();
    const honor = Form.useWatch(['careerBasicInfo', 'honor'], form);

    const submitBtnRef = useRef<any>(null);

    // 是否是教育博主
    const isKol = identityType === IdentityType.KOL;

    // 是否是退休教师
    const isRetired = identityType === IdentityType.RETIRED;

    // 所属类型 的选项
    const unitTypeOpts = isKol ? KOLUnitTypeOptions : TCHUnitTypeOptions;

    // 字数校验
    const validateCount = (count: number, message: string = '已达可输入字数上限') => {
        return (rule: any, value: string) => {
            if (!value || +(getStringLength(value) / 2).toFixed(0) <= count) {
                return Promise.resolve();
            }
            return Promise.reject(new Error(message));
        };
    };

    useEffect(() => {
        if (baseInfo) {
            // server接口未运营账号枚举是0，前端组件选项不支持0选中，因此这里转下
            form.setFieldValue('careerBasicInfo', {
                ...baseInfo,
                fansSection: baseInfo.fansSection === 0 ? -1 : baseInfo.fansSection,
                honorDetail: baseInfo.honorDetail
                    ? [
                          {
                              name: '荣誉材料',
                              uid: '1',
                              response: {
                                  url: baseInfo.honorDetail,
                              },
                              status: 'done',
                          },
                      ]
                    : [],
            });
        }
    }, [baseInfo, form]);

    // 审核不通过原因展示
    const errorMsg = () => {
        return (
            baseInfo.status === CareerInfoStatus.Failed && (
                <StyledAlert
                    message="审核不通过"
                    description={baseInfo.msg}
                    type="error"
                    showIcon
                    className="mb-[15px]"
                />
            )
        );
    };

    return (
        <div>
            <Header title="资质信息" />
            <div className="px-3">
                {errorMsg()}
                <Form.Item
                    name={['careerBasicInfo', 'unitType']}
                    rules={[
                        {
                            required: true,
                            message: '请选择所属类型',
                        },
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <SelectWidthLabel label="所属类型" options={unitTypeOpts} required placeholder="请选择所属类型" />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'platform']}
                    rules={[
                        {
                            required: true,
                            message: '请填写所属的学校/机构/单位全称',
                        },
                        {
                            validateTrigger: ['onBlur', 'onChange'],
                            validator: validateCount(16),
                        },
                        getAuditSpaceRules('名称', 'onBlur'),
                        getAuditTextRules('名称', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur', 'onChange']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="名称"
                        required
                        placeholder="请填写所属的学校/机构/单位全称"
                        inputTextAlign={InputTextAlign.END}
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'userName']}
                    rules={[
                        {
                            required: true,
                            message: '请输入您的真实姓名',
                        },
                        getAuditSpaceRules('姓名', 'onBlur'),
                        getAuditTextRules('姓名', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="姓名"
                        required
                        placeholder="请输入您的真实姓名"
                        inputTextAlign={InputTextAlign.END}
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'phone']}
                    rules={[
                        {required: true, message: '请输入真实手机号'},
                        {
                            validateTrigger: ['onBlur', 'onChange'],
                            validator: validatePhone('手机号格式有误，请重新输入'),
                        },
                    ]}
                    validateTrigger={['onChange', 'onBlur']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="手机号"
                        required
                        placeholder="请输入真实手机号"
                        inputTextAlign={InputTextAlign.END}
                        type="tel"
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'grade']}
                    rules={[
                        {
                            required: true,
                            message: '请选择您主要执教年级',
                        },
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <RadioWithLabel label="年级" required options={IGradeOptions} />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'subject']}
                    rules={[
                        {
                            required: true,
                            message: '请选择您主要执教学科',
                        },
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <RadioWithLabel label="学科" required options={ISubjectOptions} className="mb-[15px]" />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'introduction']}
                    rules={[
                        {
                            required: true,
                            message: '请填写您最高职称、荣誉身份信息',
                        },
                        {
                            validateTrigger: ['onBlur', 'onChange'],
                            validator: validateCount(32),
                        },
                        getAuditSpaceRules('对外介绍', 'onBlur'),
                        getAuditTextRules('对外介绍', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur', 'onChange']}
                    noStyle
                >
                    <InputWithLabel.Textarea
                        label="对外介绍"
                        required
                        placeholder={`请填写您最高职称、荣誉身份信息。如xx省级名师、${
                            isKol ? '《xxx》作者、xx机构优秀教师' : 'xx市骨干教师、国家级数学课一等奖'
                        }`}
                        rows={4}
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'title']}
                    rules={[
                        {
                            required: true,
                            message: '请填写您的对外title',
                        },
                        {
                            validateTrigger: ['onBlur', 'onChange'],
                            validator: validateCount(10),
                        },
                        getAuditSpaceRules('职业title', 'onBlur'),
                        getAuditTextRules('职业title', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur', 'onChange']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="职业title"
                        required
                        placeholder="如小学数学教研组长"
                        inputTextAlign={InputTextAlign.END}
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                {isRetired && (
                    <Form.Item name={['careerBasicInfo', 'teacherCertification']} validateTrigger={['onBlur']} noStyle>
                        <UploadImageWithLabel
                            label="资格证上传"
                            required
                            subTitle="请上传您的教师资格证，便于平台核验执业资格"
                        />
                    </Form.Item>
                )}
                <Form.Item
                    name={['careerBasicInfo', 'honor']}
                    rules={[
                        {
                            required: true,
                            message: '请选择您所获得的最高荣誉',
                        },
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <SelectWidthLabel
                        className="mt-[15px]"
                        label="最高荣誉"
                        options={HonorOptions}
                        required
                        placeholder="请选择您所获得的最高荣誉"
                    />
                </Form.Item>
                {/* 上传荣誉压缩包 */}
                {honor && honor !== IHonor.None && <UploadFileForm />}
                <Form.Item name={['careerBasicInfo', 'photoUrl']} validateTrigger={['onBlur']} noStyle>
                    <UploadImageWithLabel
                        label="职业照片"
                        required
                        subTitle="请上传纯色背景的职业形象照，露出腰部以上和完整双肩"
                    />
                </Form.Item>
                {isKol && (
                    <Form.Item
                        name={['careerBasicInfo', 'topFansPlatform']}
                        rules={[
                            {
                                required: true,
                                message: '请填写您粉丝量最高平台',
                            },
                            getAuditSpaceRules('最高粉平台', 'onBlur'),
                            getAuditTextRules('最高粉平台', 'onBlur'),
                        ]}
                        validateTrigger={['onBlur']}
                        noStyle
                    >
                        <InputWithLabel
                            className="mt-[15px]"
                            label="最高粉平台"
                            placeholder="请填写您粉丝量最高平台"
                            inputTextAlign={InputTextAlign.END}
                            required
                            onBlur={submitBtnRef?.current?.synchronizeCanOk}
                        />
                    </Form.Item>
                )}
                <Form.Item
                    name={['careerBasicInfo', 'fansSection']}
                    rules={[
                        {
                            required: isKol,
                            message: '请选择您的平台粉段',
                        },
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <SelectWidthLabel
                        className="mt-[15px]"
                        label={`${isKol ? '该' : '单'}平台粉段`}
                        options={fansSectionOptions}
                        required={isKol}
                        placeholder="请选择您的平台粉段"
                    />
                </Form.Item>
                <Form.Item
                    name={['careerBasicInfo', 'indexLink']}
                    rules={[
                        {
                            required: isKol,
                            message: '填写最高粉量链接',
                        },
                        isKol ? getAuditSpaceRules('账号主页或出版链接', 'onBlur') : {},
                        getAuditTextRules('账号主页或出版链接', 'onBlur'),
                    ]}
                    validateTrigger={['onBlur']}
                    noStyle
                >
                    <InputWithLabel
                        className="mt-[15px]"
                        label="账号主页或出版链接"
                        placeholder="填写最高粉量链接"
                        inputTextAlign={InputTextAlign.END}
                        required={isKol}
                        onBlur={submitBtnRef?.current?.synchronizeCanOk}
                    />
                </Form.Item>
                <BasicFormSubButtons ref={submitBtnRef} setShowBasicAuditModule={setShowBasicAuditModule} />
            </div>
        </div>
    );
}

export default function AuthForm({
    setShowBasicAuditModule,
}: {
    setShowBasicAuditModule: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Alert: {
                        withDescriptionPadding: '12px 16px',
                    },
                },
            }}
        >
            <Form scrollToFirstError>
                <CommonErrorBoundary>
                    <BasicFormLayout setShowBasicAuditModule={setShowBasicAuditModule} />
                </CommonErrorBoundary>
            </Form>
        </ConfigProvider>
    );
}
