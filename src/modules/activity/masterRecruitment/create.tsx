import {useCallback} from 'react';
import {Button} from 'antd';
import {ClockCircleFilled, CloseCircleFilled, CheckCircleFilled} from '@ant-design/icons';
import classNames from 'classnames';
import {useNavigate} from 'react-router-dom';
import urls from '@/links/index';
import {AgentAuditStatus} from '@/api/activity/masterRecruitment/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import Header from './components/Header';
import AuditCard from './components/AuditCard';
import CustomerServiceCard from './components/CustomerServiceCard';
import AuditPanel from './components/AuditPanel';
import MasterAgentEdit from './agentEditForm';
import {useMasterRecruitmentStore} from './store/index';

const AuditStatusTextMap = {
    [AgentAuditStatus.onLine]: {
        title: '智能体已成功上线',
        desc: '您创建的智能体已上线，可在文心智能体平台继续优化',
        icon: <CheckCircleFilled style={{fontSize: 18, color: '#39B362'}} />,
    },
    [AgentAuditStatus.DRAFT]: {
        title: '',
        desc: '',
        icon: '',
    },
    [AgentAuditStatus.AUDITING]: {
        title: '创建完成！平台加急审核中',
        desc: '审核预计需要3小时，完成后将通过短信通知您',
        icon: <ClockCircleFilled style={{fontSize: 18, color: '#FF8200'}} />,
    },
    [AgentAuditStatus.FAILURE]: {
        title: '审核失败',
        desc: '请注意官方联系信息或联系运营同学，前往平台修改',
        icon: <CloseCircleFilled style={{fontSize: 18, color: '#FF3333'}} />,
    },
    [AgentAuditStatus.SUCCESS]: {
        title: '审核成功',
        desc: '您创建的智能体已审核通过，可在文心智能体平台继续优化',
        icon: <CheckCircleFilled style={{fontSize: 18, color: '#39B362'}} />,
    },
};

const AgentAuditModule = ({auditStatus}: {auditStatus: AgentAuditStatus | null}) => {
    const auditStatusText = AuditStatusTextMap[auditStatus || AgentAuditStatus.FAILURE];

    const navigate = useNavigate();

    const goPlatform = useCallback(() => {
        navigate(urls.agentList.raw());
    }, [navigate]);

    return (
        <>
            <div className="text-white">
                <Header title="配置智能体" />
            </div>
            <div className="mx-3">
                <AuditCard className="mb-2 text-center">
                    <div className="pb-3 text-lg font-semibold text-[#1F1F1F]">
                        <span className="mr-2">{auditStatusText.icon}</span>
                        {auditStatusText.title}
                    </div>
                    <div className="pb-4 text-sm text-[#1F1F1F]">{auditStatusText.desc}</div>
                    <div>
                        <Button
                            className="cursor-pointer rounded-full border-none bg-[#E8F3FF] text-[#4E6EF2]"
                            onClick={goPlatform}
                        >
                            前往文心智能体平台
                        </Button>
                    </div>
                </AuditCard>

                <AuditCard>
                    <div className="flex flex-col items-center justify-center">
                        <CustomerServiceCard isWhiteBg />
                    </div>
                </AuditCard>
            </div>
        </>
    );
};

export default function Index() {
    const {appId, auditStatus} = useMasterRecruitmentStore(store => ({
        auditStatus: store.agentInfo.status,
        appId: store.agentInfo.appId,
    }));
    const isMobile = useIsMobileStore(store => store.isMobile);

    const showAgentAuditModule = appId && auditStatus && auditStatus !== AgentAuditStatus.DRAFT;

    // const showAgentAuditModule = true;
    return (
        <>
            {!showAgentAuditModule && (
                <div
                    className={classNames('m-auto', {
                        'max-w-[820px]': !isMobile,
                    })}
                >
                    <MasterAgentEdit />
                </div>
            )}
            {showAgentAuditModule && (
                <AuditPanel>
                    <AgentAuditModule auditStatus={auditStatus} />
                </AuditPanel>
            )}
        </>
    );
}
