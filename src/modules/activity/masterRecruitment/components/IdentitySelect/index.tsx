/**
 * @file 身份选择列表
 * <AUTHOR>
 */

import React, {useMemo, useCallback} from 'react';
import {Radio} from 'antd';
import type {RadioChangeEvent} from 'antd';
import styled from '@emotion/styled';
import {IdentityType} from '@/api/activity/masterRecruitment/interface';
import {IdentityMap, IdentityInfo} from '../../interface';

function IdentityItem({item}: {item: IdentityInfo}) {
    return (
        <div>
            <div className="text-black-base text-sm font-medium">{item.name}</div>
            <div className="text-sm text-gray-tertiary">{item.desc}</div>
        </div>
    );
}

const style: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: 10,
    // backgroundColor: '#F5F6F9',
    // borderRadius: 9,
    // padding: '12px 15px',
};

const StyledRadioWrapper = styled(Radio.Group)`
    .ant-radio-wrapper {
        background-color: #f5f6f9;
        border-radius: 9px !important;
        padding: 12px 15px !important;
        &.ant-radio-wrapper-checked {
            background-color: rgba(78, 110, 242, 0.09) !important;
        }
        &:hover {
            background-color: rgba(78, 110, 242, 0.09) !important;
        }
    }
`;

export default function IdentitySelect({
    value,
    setValue,
}: {
    value: IdentityType | null;
    setValue: (e: IdentityType) => void;
}) {
    const onChange = useCallback(
        (e: RadioChangeEvent) => {
            setValue(e.target.value);
        },
        [setValue]
    );

    const options = useMemo(() => {
        return IdentityMap.map(item => ({
            label: <IdentityItem item={item} />,
            value: item.type,
        }));
    }, []);

    return (
        <>
            <StyledRadioWrapper style={style} onChange={onChange} value={value} options={options} />
        </>
    );
}
