import {Form} from 'antd';
import {getAuditSpaceRules, getAuditTextRules} from '@/api/audit';
// import {getCountConfig} from '@/utils/text';
import InputWithLabel from '@/components/mobile/InputWithLabel';

export default function Overview() {
    return (
        <Form.Item
            name={['agentInfo', 'overview']}
            rules={[
                {
                    required: true,
                    message: '请输入智能体简介',
                },
                getAuditSpaceRules('智能体简介', 'onBlur'),
                getAuditTextRules('智能体简介', 'onBlur'),
            ]}
            validateTrigger={['onBlur']}
            noStyle
        >
            <InputWithLabel.Textarea
                className="mt-[15px]"
                label="简介"
                // count={getCountConfig(50, false)}
                required
                placeholder="简短的介绍"
                rows={4}
            />
        </Form.Item>
    );
}
