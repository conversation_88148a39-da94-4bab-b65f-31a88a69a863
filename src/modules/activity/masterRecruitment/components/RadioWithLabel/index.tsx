import {ConfigProvider, Form, RadioGroupProps} from 'antd';
import {ReactNode} from 'react';
import styled from '@emotion/styled';
import {Radio} from 'antd';
import Card from '@/components/mobile/Card';

const StyledRadioGroup = styled(Radio.Group)`
    .ant-radio-button-wrapper {
        border-radius: 9px !important;
        border-color: #bfc3cd !important;
        border: none !important;
        background-color: #f5f6fa !important;
        color: #000311 !important;
        height: 30px !important;
        line-height: 30px !important;
        padding: 0px 30px !important;
    }
    .ant-radio-button-wrapper-checked {
        color: #4e6ef2 !important;
        background-color: #e8f3ff !important;
        font-weight: 500 !important;
    }
    .ant-radio-button-wrapper:not(:first-of-type)::before {
        width: 0px !important;
    }
    .ant-radio-button-wrapper:not(:last-of-type) {
        margin-right: 8px !important;
    }
`;

interface RadioWithLabelProps extends RadioGroupProps {
    label: string;
    required?: boolean;
    errorMsgNode?: ReactNode;
}

export default function RadioWithLabel(props: RadioWithLabelProps) {
    const {status, errors} = Form.Item.useStatus();
    const {options, onChange, onBlur, value, disabled, errorMsgNode} = props;

    return (
        <ConfigProvider wave={{disabled: true}}>
            <Card title={props.label} required className={props.className}>
                <StyledRadioGroup optionType="button" {...{options, onChange, value, onBlur, disabled, key: 'radio'}}>
                    {props.children}
                </StyledRadioGroup>
            </Card>
            {errorMsgNode || (
                <div
                    className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                    style={{
                        transition: 'all 0.2s linear',
                    }}
                >
                    {errors}
                </div>
            )}
        </ConfigProvider>
    );
}
