/**
 * @file 权益卡片
 * <AUTHOR>
 */
import classNames from 'classnames';
import {useIsMobileStore} from '@/store/home/<USER>';
import {EquityInfo} from '../../interface';

export default function EquityCard({item}: {item: EquityInfo}) {
    const isMobile = useIsMobileStore(store => store.isMobile);

    return (
        <div className="flex w-full max-w-[600px] items-center justify-start rounded-xl py-4">
            <div className="bg-gray-bg-base">
                <div
                    className={classNames('', {
                        'rounded-xl border-8 border-white': item.showImgBorder,
                        'w-[128px]': isMobile,
                    })}
                >
                    <img src={item.imageUrl} className="h-[100px] w-[100px]"></img>
                    {isMobile && <div>{item.imgDesc}</div>}
                </div>
            </div>
            <div className="pl-4">
                <div className="text-black-base text-black-base pb-4 text-base font-semibold">{item.title}</div>
                <div className="text-xs text-gray-tertiary">{item.desc}</div>
            </div>
        </div>
    );
}
