import {useCallback, useEffect, useMemo} from 'react';
import {Form} from 'antd';
import {AgentConfigV2} from '@/api/agentEdit/interface';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {LJExtData} from '@/utils/loggerV2/utils';
import {useMasterRecruitmentStore} from '../../store/index';

export default function PublishButton() {
    const {agentInfo, publishTeacherAgent} = useMasterRecruitmentStore(store => ({
        agentInfo: store.agentInfo,
        publishTeacherAgent: store.publishTeacherAgent,
    }));

    const form = Form.useFormInstance<AgentConfigV2>();
    const values = Form.useWatch([], form);

    const {displayLog, clickLog} = useUbcLogV3();

    const publishBtnEnabled = useMemo(() => {
        const agentInfo = values?.agentInfo;
        return agentInfo?.name && agentInfo?.logoUrl && agentInfo?.overview;
    }, [values]);

    const handlePublishClick = useCallback(async () => {
        // 发布智能体按钮 点击打点
        clickLog(
            EVENT_VALUE_CONST.PUBLISH,
            {
                [EVENT_EXT_KEY_CONST.AGENT_ID]: agentInfo?.appId,
                [EVENT_EXT_KEY_CONST.AGENT_NAME]: values?.agentInfo?.name,
            } as LJExtData,
            EVENT_PAGE_CONST.LIGHTHOUSE_CODELESS_CREATE
        );

        // 发布智能体
        if (publishBtnEnabled) {
            publishTeacherAgent();
        }
    }, [agentInfo?.appId, clickLog, publishBtnEnabled, publishTeacherAgent, values?.agentInfo?.name]);

    useEffect(() => {
        // 智能体创建 曝光 打点
        displayLog(EVENT_PAGE_CONST.LIGHTHOUSE_CODELESS_CREATE, {
            [EVENT_EXT_KEY_CONST.AGENT_ID]: agentInfo?.appId,
            [EVENT_EXT_KEY_CONST.AGENT_NAME]: agentInfo?.name,
        } as LJExtData);
    }, [agentInfo?.appId, agentInfo?.name, displayLog]);

    return (
        <div
            className={`flex-grow cursor-pointer rounded-full py-3 text-center text-sm font-normal text-white ${
                publishBtnEnabled ? 'bg-primary active:bg-[#DDE0FC]' : 'bg-[#bbc0fa]'
            }`}
            onClick={handlePublishClick}
        >
            发布智能体
        </div>
    );
}
