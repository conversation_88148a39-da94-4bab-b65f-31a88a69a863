import {css} from '@emotion/css';
import classNames from 'classnames';
import {useIsMobileStore} from '@/store/home/<USER>';

const bgWiseGradient = css`
    background: linear-gradient(180deg, #4e6ef2 0%, #4e6ef2 16.64%, #f5f6f9 29.22%, #f5f6f9 100%);
`;

const bgPcGradient = css`
    background: linear-gradient(180deg, #4e6ef2 0%, #506ff3 16.64%, #c8d3ff 38.02%, #f5f6f9 55.52%, #f5f6f9 100%);
`;

export default function Index({children}: {children: React.ReactNode}) {
    const {isMobile} = useIsMobileStore.getState();

    return (
        <div
            className={classNames('min-h-[100vh]', {
                [bgPcGradient]: !isMobile,
                [bgWiseGradient]: isMobile,
            })}
        >
            <div
                className={classNames('m-auto', {
                    'max-w-[820px]': !isMobile,
                })}
            >
                {children}
            </div>
        </div>
    );
}
