/**
 * @file 客服联系卡片
 * <AUTHOR>
 */
import {useCallback} from 'react';
import classNames from 'classnames';
import ExportFileApi from '@/utils/file';
// import {useIsMobileStore} from '@/store/home/<USER>';

// 企业客服二维码
const QrCodeUrl =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/%E7%81%AF%E5%A1%94%E8%AE%A1%E5%88%92%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.jpg';

export default function CustomerServiceCard({isWhiteBg}: {isWhiteBg?: boolean}) {
    // const isMobile = useIsMobileStore(store => store.isMobile);
    const downloadQrCode = useCallback(() => {
        ExportFileApi.downloadFileByBlob({
            url: QrCodeUrl,
            fileName: '灯塔计划客服二维码.jpg',
        });
    }, []);

    return (
        <div className="flex w-full max-w-[600px] items-center justify-start rounded-xl">
            <div
                className={classNames('flex h-[146px] w-[128px] shrink-0 items-center justify-center rounded-xl', {
                    'bg-[#F5F6F9]': isWhiteBg,
                    'bg-white': !isWhiteBg,
                })}
            >
                <div
                    className={classNames({
                        'border-[#F5F6F9]': isWhiteBg,
                        'border-white': !isWhiteBg,
                    })}
                >
                    <img src={QrCodeUrl} className="h-[90px] w-[90px]"></img>
                    <div className="w-full pt-2 text-center">
                        <span className="cursor-pointer text-xs text-primary" onClick={downloadQrCode}>
                            下载二维码
                        </span>
                    </div>
                </div>
            </div>
            <div className="pl-4">
                <div className="text-black-base text-black-base text-black-base pb-4 text-base font-semibold">
                    添加企业微信
                </div>
                <div className="text-xs text-gray-tertiary">官方客服1v1对接您的视频效果，跟进权益上线 </div>
            </div>
        </div>
    );
}
