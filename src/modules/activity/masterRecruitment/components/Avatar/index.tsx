import {useCallback, useRef} from 'react';
import {Form} from 'antd';
import UploadLogoDragger from '@/modules/agentPromptEditV2/pc/components/UploadLogoDragger';
import {getAuditImageRules} from '@/api/audit';

const defaultImage =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/activity-master/teacher-agent-avatar.png';

interface Props {
    value?: string;
    disabled?: boolean;
    onChange?: (value: string) => void;
    appId?: string;
}
function AvatarFormItem(props: Props) {
    const {value, appId} = props;
    // const [form] = Form.useForm();
    const uploadLogoRef = useRef<HTMLDivElement>(null);
    const onClickUpload = useCallback(() => {
        // 触发图片区域点击
        uploadLogoRef?.current?.click();
    }, []);
    // const logoUrl = form.getFieldValue(['agentInfo', 'logoUrl']);

    return (
        <div className="flex items-center justify-center">
            <div className="relative">
                {/* 图片区域 */}
                <UploadLogoDragger
                    {...props}
                    uploadRef={uploadLogoRef}
                    styleType="solid"
                    size={130}
                    defaultImage={defaultImage}
                    agentId={appId}
                    hideReUpload
                />

                <div
                    onClick={onClickUpload}
                    className="absolute bottom-[-5px] left-[20px] box-content flex h-[30px] w-[88px] cursor-pointer items-center justify-center  rounded-full border-[1px] border-white bg-primary"
                >
                    <span className="text-sm font-semibold text-white">{value ? '更换形象' : '个人形象'}</span>
                </div>

                {/* <div
                    onClick={onClickUpload}
                    className="absolute bottom-0 right-0 box-content flex h-[30px] w-[30px] items-center justify-center rounded-full border-[2px] border-white bg-primary"
                >
                    <span className="iconfont icon-plus text-[18px] font-semibold text-white" />
                </div> */}

                <span className="absolute left-[130px] top-[5px] text-sm text-error" style={{fontFamily: 'SimSong'}}>
                    *
                </span>
            </div>
        </div>
    );
}

export default function Avatar({appId}: {appId: string}) {
    return (
        <Form.Item
            className="mb-[33px]"
            required
            name={['agentInfo', 'logoUrl']}
            rules={[getAuditImageRules('', 'onChange')]}
        >
            <AvatarFormItem appId={appId} />
        </Form.Item>
    );
}
