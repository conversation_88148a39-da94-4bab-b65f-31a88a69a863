import {ConfigProvider, Form} from 'antd';
import {ReactNode} from 'react';
import Card from '@/components/mobile/Card';
import UploadImageDragger from './UploadImageDragger';

interface UploadFileWithLabelProps {
    label: string;
    subTitle: string;
    required?: boolean;
    errorMsgNode?: ReactNode;
}

export default function UploadImageWithLabel(props: UploadFileWithLabelProps) {
    const {status, errors} = Form.Item.useStatus();
    const {errorMsgNode} = props;

    return (
        <ConfigProvider wave={{disabled: true}}>
            <Card title={props.label} required>
                <div className="mb-[10px] text-[14px] leading-[21px] text-gray-tertiary">{props.subTitle}</div>
                <UploadImageDragger {...props} styleType="solid" rounded={9} />
            </Card>
            {errorMsgNode || (
                <div
                    className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                    style={{
                        transition: 'all 0.2s linear',
                    }}
                >
                    {errors}
                </div>
            )}
        </ConfigProvider>
    );
}
