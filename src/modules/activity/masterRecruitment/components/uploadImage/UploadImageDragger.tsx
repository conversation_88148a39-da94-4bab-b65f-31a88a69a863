/* eslint-disable complexity */
/**
 * @file 图片上传
 */
import React, {MouseEvent, useCallback, useState, useEffect, useRef, useMemo} from 'react';
import {Upload, Progress} from 'antd';
import type {UploadChangeParam} from 'antd/es/upload';
import type {AxiosProgressEvent} from 'axios';
import type {RcFile, UploadFile, UploadProps} from 'antd/es/upload/interface';
import {UploadRequestOption} from 'rc-upload/lib/interface';
import classNames from 'classnames';
const {Dragger} = Upload;
import {getBase64} from '@/utils/image';
import auditManagementApi from '@/api/auditManagement';
import {FileType} from '@/api/auditManagement/interface';
import ThemeConfig from '@/styles/lingjing-light-theme';
import {useIsMobileStore} from '@/store/home/<USER>';
import {useToast, ToastType} from '../../hooks/useToast';

export interface UploadImageProps extends Omit<UploadProps, 'value' | 'onChange'> {
    value?: string;
    onChange?: (value: string) => void;
    onClickUpload?: (e: MouseEvent) => void;
    styleType?: 'normal' | 'solid';
    sizeW?: number;
    sizeH?: number;
    rounded?: number;
    disabled?: boolean;
    bordered?: boolean;
}

export default function UploadImageDragger({
    value,
    onChange,
    className,
    onClickUpload,
    sizeW,
    sizeH,
    rounded,
    styleType = 'normal',
    disabled = false,
    bordered = true,
    ...props
}: UploadImageProps) {
    const [loading, setLoading] = useState(false);
    const [loadPercent, setLoadPercent] = useState<number | undefined>(0);
    const [base64ImgUrl, setBase64ImageUrl] = useState<string>('');
    const [isLongPic, setIsLongPic] = useState<boolean>(false);
    const waitUpload = !value && !loading;
    const isUploaded = !!value && !loading;
    const isMobile = useIsMobileStore(store => store.isMobile);
    const {showToast} = useToast();

    const abortControllerRef = useRef<AbortController | null>(null);
    const handleChange = useCallback(
        async (info: UploadChangeParam<UploadFile>) => {
            if (info.file.status === 'uploading') {
                setLoading(true);
                const percent = Math.min(Math.floor(info.file.percent || 0), 99);
                setLoadPercent(percent);
            } else if (info.file.status === 'done' && info.file?.response?.url) {
                setLoading(false);
                onChange && onChange(info.file.response.url);
            } else if (info.file.status === 'error') {
                setLoading(false);
                showToast({msg: '图片上传失败，请重新上传!', type: ToastType.ERROR});
                onChange && onChange('');
                setBase64ImageUrl('');
            }
        },
        [onChange, showToast]
    );

    const customRequest = useCallback((info: UploadRequestOption<any>) => {
        (async () => {
            abortControllerRef.current = new AbortController();
            const file = info.file as File;
            const onUploadProgress = (ev: AxiosProgressEvent) => {
                const percent = (ev.loaded / ev.total!) * 100;
                info.onProgress && info.onProgress({percent});
            };

            try {
                const response = await auditManagementApi.uploadFileToBos(
                    {
                        file: file,
                        type: FileType.qaImage,
                    },
                    abortControllerRef.current?.signal,
                    {
                        forbiddenToast: true,
                        onUploadProgress: onUploadProgress,
                    }
                );

                info.onSuccess && info.onSuccess(response);
                setLoading(false);
            } catch {
                setLoading(false);
            }
        })();
    }, []);

    const draggerAreaClick = useCallback(
        (e: MouseEvent) => {
            if (loading || disabled) {
                e.stopPropagation();
            }
        },
        [disabled, loading]
    );

    const removeUploadImage = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            onChange && onChange('');
        },
        [onChange]
    );

    const cancelRequest = useCallback((e?: MouseEvent) => {
        abortControllerRef.current?.abort();
        e && e.stopPropagation();
    }, []);

    useEffect(() => {
        (async () => {
            if (value && !base64ImgUrl.startsWith('http')) {
                // const {width, height} = await getImgWH(value);
                setIsLongPic(true);
                setBase64ImageUrl('');
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const beforeUpload = useCallback(
        async (file: RcFile) => {
            const isImageFormat = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'].some(
                (type: string) => file.type === type
            );
            if (!isImageFormat) {
                showToast({msg: '图片格式仅支持png、jpeg、jpg、webp，请重新上传!', type: ToastType.ERROR});
                return false;
            }

            // 通过bos自动裁切&压缩图片，参考文档：https://cloud.baidu.com/doc/BOS/s/Yldh5wq5b
            const isSizeLimit = file.size < 20 * 1024 * 1024;
            if (!isSizeLimit) {
                showToast({msg: '图片应该小于20M，请重新上传!', type: ToastType.ERROR});
                return false;
            }

            const url = await getBase64(file);
            // const {width, height} = await getImgWH(url);
            // if (width > 30000 || height > 30000) {
            //     showToast({msg: '头像大于30000像素*30000像素，请重新上传!', type: ToastType.ERROR});
            //     return false;
            // }

            setBase64ImageUrl(url);

            return isSizeLimit;
        },
        [showToast]
    );

    const sizeStyle = useMemo(
        () => ({
            width: sizeW || 121,
            height: sizeH || 80,
            borderRadius: rounded || '50%',
        }),
        [sizeW, sizeH, rounded]
    );

    return (
        <div className="relative">
            <Dragger
                {...props}
                customRequest={customRequest}
                showUploadList={false}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                style={{...sizeStyle, border: 'none'}}
                className={classNames('block', className)}
                accept="image/jpeg,image/png,image/jpg,image/webp"
            >
                <div
                    className={classNames('relative flex flex-col items-center', {
                        'hover:cursor-pointer': !loading && !value,
                        'hover:cursor-default': loading || !!value,
                    })}
                    onClick={draggerAreaClick}
                >
                    <div onClick={onClickUpload} className="flex items-center justify-center">
                        {/* 图片不存在且未上传中时展示默认图标、上传按钮和提示 */}
                        {waitUpload && (
                            <div className="flex flex-col items-center">
                                <div
                                    style={sizeStyle}
                                    className={classNames('relative flex items-center justify-center', {
                                        'border-[rgba(220, 221, 224, 0.3)] border border-dashed hover:border-primary':
                                            styleType === 'normal',
                                        'bg-[#F5F6F9]': styleType === 'solid',
                                    })}
                                >
                                    <span className="iconfont icon-plus text-[24px] text-gray-quaternary"></span>
                                </div>
                            </div>
                        )}

                        {/* 图片上传时展示图片+百分比 */}
                        {loading && (
                            <div
                                style={sizeStyle}
                                className="group relative flex items-center justify-center overflow-hidden border border-[#BFC3CD]  bg-[#f1f1ff]"
                            >
                                <img
                                    className={classNames('object-cover', {
                                        'w-full': isLongPic,
                                        'h-full': !isLongPic,
                                    })}
                                    src={base64ImgUrl || value}
                                />
                                {/* 上传进度条 */}
                                <div className="absolute left-0 right-0 flex h-full w-full items-center justify-center rounded-[9px] bg-black/[.4] text-white group-hover:hidden">
                                    <Progress
                                        type="circle"
                                        percent={loadPercent}
                                        strokeColor={ThemeConfig.token.colorPrimary}
                                        trailColor="#FFFFFF"
                                        strokeWidth={16}
                                        size={24}
                                        showInfo={false}
                                    />
                                </div>
                                {/* 取消上传按钮 */}
                                <div className="absolute left-0 right-0 hidden h-full w-full items-center justify-around rounded-[9px] bg-black/[.4] group-hover:flex">
                                    <span
                                        className="iconfont icon-delete text-[18px] text-white hover:cursor-pointer"
                                        onClick={cancelRequest}
                                    ></span>
                                </div>
                            </div>
                        )}

                        {/* 图片上传成功展示图片 */}
                        {isUploaded && (
                            <div
                                style={sizeStyle}
                                className={classNames(
                                    'group relative flex items-center justify-center overflow-hidden border-[#000] border-opacity-5',
                                    {
                                        'border-[1px]': bordered,
                                    }
                                )}
                            >
                                <img
                                    className={classNames('object-cover', {
                                        'w-full': isLongPic,
                                        'h-full': !isLongPic,
                                    })}
                                    src={base64ImgUrl || value}
                                    alt="avatar"
                                />
                                {/* 重新上传图片 */}
                                {!isMobile && !disabled && (
                                    <div
                                        className="absolute left-0 right-0 hidden h-full w-full items-center justify-around bg-black/[.4] group-hover:flex"
                                        onClick={removeUploadImage}
                                    >
                                        <div className="flex flex-col text-xs font-medium leading-5 text-white">
                                            <span className={classNames('iconfont icon-delete m-auto text-lg')} />
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </Dragger>
            {isMobile && isUploaded && !disabled && (
                <span
                    onClick={removeUploadImage}
                    className="absolute bottom-0 right-0 inline-block cursor-pointer text-[14px] text-primary"
                >
                    删除
                </span>
            )}
        </div>
    );
}
