import {ConfigProvider, Form, UploadProps} from 'antd';
import {ReactNode, useState} from 'react';
import Card from '@/components/mobile/Card';
import {FileType} from '@/api/auditManagement/interface';
import UploadFile from './uploadFile';

interface UploadFileWithLabelProps extends UploadProps {
    value?: any;
    onChange?: any;
    label: string;
    subTitle: string;
    required?: boolean;
    errorMsgNode?: ReactNode;
}

function UploadFileWithLabel(props: UploadFileWithLabelProps) {
    const {status, errors} = Form.Item.useStatus();
    const {errorMsgNode} = props;

    const [, setUploadFileLoading] = useState(false);
    const [, setUploadFileFail] = useState(false);

    return (
        <ConfigProvider wave={{disabled: true}}>
            <Card title={props.label} required>
                <div className="mb-[10px] text-[14px] leading-[21px] text-gray-tertiary">{props.subTitle}</div>
                <UploadFile
                    value={props.value}
                    onChange={props.onChange}
                    fileType={FileType.operActivityZipInfo}
                    setUploadFileLoading={setUploadFileLoading}
                    setUploadFileFail={setUploadFileFail}
                />
            </Card>
            {errorMsgNode || (
                <div
                    className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                    style={{
                        transition: 'all 0.2s linear',
                    }}
                >
                    {errors}
                </div>
            )}
        </ConfigProvider>
    );
}

export default function UploadFileForm() {
    return (
        <Form.Item
            name={['careerBasicInfo', 'honorDetail']}
            rules={[
                {
                    required: true,
                    message: '请上传您的最高荣誉材料',
                },
            ]}
            validateTrigger={['onBlur']}
            noStyle
        >
            <UploadFileWithLabel
                label="荣誉/证书"
                subTitle="请上传您的最高荣誉材料（如荣誉证书，或官媒/主流媒体/单位门户网站报道文章截图）"
                required
            />
        </Form.Item>
    );
}
