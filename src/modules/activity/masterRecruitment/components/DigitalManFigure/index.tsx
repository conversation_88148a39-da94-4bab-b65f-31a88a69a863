/**
 * @file 数字形象
 * <AUTHOR>
 */
// import {useCallback, useEffect, useState} from 'react';
import {Form} from 'antd';
// import {useIsMobileStore} from '@/store/home/<USER>';
// import PcAddFigureModal from '@/modules/agentPromptEditV2/pc/components/DynamicFigure/AddFigureModal.tsx'
// 待补充wise

export default function DigitalManFigure() {
    // const {isMobile} = useIsMobileStore.getState();
    // // 数字人视频弹窗
    // const [open, setOpen] = useState(false);
    // const openAddFigureModal = useCallback(() => {
    //     setOpen(true);
    // }, []);

    return (
        <div>
            <Form.Item className="mb-[18px]" required name={['agentInfo', 'logoUrl']}>
                <div>
                    <div>
                        <span>数字分身</span>
                        <span
                            className="absolute left-[80px] top-[5px] text-sm text-error"
                            style={{fontFamily: 'SimSong'}}
                        >
                            *
                        </span>
                    </div>
                    <div>请添加绿幕环境下拍摄的视频，生成专属数字分身，仅支持竖版</div>
                </div>

                {/* {isMobile ? <PcAddFigureModal open={open} setOpen={openAddFigureModal}/> : <PcAddFigureModal setOpen={openAddFigureModal}/>} */}
            </Form.Item>
        </div>
    );
}
