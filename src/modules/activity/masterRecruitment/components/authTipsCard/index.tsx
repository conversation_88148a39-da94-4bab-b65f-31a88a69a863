/**
 * @file 认证操作提示卡片
 */
import classNames from 'classnames';
import {QRCode} from 'antd';
import DownloadQR from '../downloadQR';
import {IOperationInstructions} from '../../interface';

export default function AuthTipsCard({item, isMobile}: {item: IOperationInstructions; isMobile: boolean}) {
    return (
        <div
            className={classNames(
                'flex items-center rounded-xl bg-gray-bg-base pl-[14px]',
                isMobile ? 'w-full' : 'h-[305px] w-[367px] flex-col'
            )}
        >
            <div className={isMobile ? 'flex-1' : 'mt-9'}>
                <div className="mb-[1px] font-semibold">
                    <span className="inline-block h-[22px] w-[22px] rounded-[50%] bg-[rgba(82,64,255,0.1)] text-center text-[17px] leading-[22px]">
                        {item.step}
                    </span>
                    <span className="ml-[7px] text-base">{item.title}</span>
                </div>
                {isMobile && <div className="ml-[29px] text-xs">{item.desc}</div>}
            </div>
            {!isMobile && <div className="text-xs">{item.desc}</div>}
            {item.step === 1 ? (
                <div className="flex w-[210px] flex-col items-center py-[15px]">
                    <QRCode value={item.imageUrl || ''} bordered={false} bgColor="#fff" size={140} />
                    <div className="mt-[10px]">
                        <DownloadQR
                            url={item.imageUrl || ''}
                            hasLogo={false}
                            downloadText={item.imgDesc || ''}
                            fileName={item.downloadFileName}
                            className="text-xs"
                        />
                    </div>
                </div>
            ) : (
                <img src={item.imageUrl} className="h-[191px] w-[210px] object-cover"></img>
            )}
        </div>
    );
}
