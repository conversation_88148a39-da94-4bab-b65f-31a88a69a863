import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Button, Form} from 'antd';
import debounce from 'lodash/debounce';
// import Toast from 'antd-mobile/es/components/toast';
import {useIsMobileStore} from '@/store/home/<USER>';
import {IdentityType, SourceType} from '@/api/activity/masterRecruitment/interface';
import api from '@/api/activity/masterRecruitment/index';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST, EVENT_VALUE_CONST} from '@/utils/loggerV2/constants';
import {LJExtData} from '@/utils/loggerV2/utils';
import {useMasterRecruitmentStore} from '../../store/index';
import {ModifyIdentityConfirmModal} from '../ModifyIdentityConfirmModal/index';

const notRequiredFieldsMap = {
    [IdentityType.EMPLOYED]: ['fansSection', 'indexLink'],
    [IdentityType.RETIRED]: ['fansSection', 'indexLink'],
    [IdentityType.KOL]: [''],
};

interface IProps {
    setShowBasicAuditModule: React.Dispatch<React.SetStateAction<boolean>>;
}

const BasicFormSubButtons = forwardRef(({setShowBasicAuditModule}: IProps, ref) => {
    const {basicStatus, identityType, setShowIdentityModal, fetchActivityInfo} = useMasterRecruitmentStore(store => ({
        basicStatus: store.baseInfo.status,
        identityType: store.baseInfo.teacherType,
        setShowIdentityModal: store.setShowIdentityModal,
        fetchActivityInfo: store.fetchActivityInfo,
    }));
    const [showEditConfirmModal, setShowEditConfirmModal] = useState<boolean>(false);
    const [canOk, setCanOk] = useState(false);

    const isMobile = useIsMobileStore(store => store.isMobile);

    const form = Form.useFormInstance();
    const fields = Form.useWatch([], form);

    const {displayLog, clickLog} = useUbcLogV3();

    const editIdentityTypeConfirm = useCallback(() => {
        setShowEditConfirmModal(true);
    }, []);

    const closeEditConfirmModal = useCallback(() => {
        setShowEditConfirmModal(false);
    }, []);

    const onOk = useCallback(() => {
        setShowIdentityModal(true);
        setShowEditConfirmModal(false);
    }, [setShowIdentityModal]);

    // 监听formValuesChange，延迟300ms，判断按钮是否置灰
    const synchronizeCanOk = useMemo(
        () =>
            debounce(() => {
                // 获取当前所有表单项已检验结果list
                // 如果某表单项没有编辑过，则该表单项对应校验结果errors会返回[]
                const fieldsError = form.getFieldsError();

                // 遍历所有表单项已检验结果list
                for (const item of fieldsError) {
                    const name = item.name;

                    // 如果errors.length > 0，则有表单项校验失败，跳出遍历
                    // 设置提交按钮置灰
                    if (item.errors.length > 0) {
                        setCanOk(false);
                        return;
                    }
                    // 如果errors.length === 0，我们只需要考虑是否存在表单项必填但value为空
                    // 存在表单项必填但value为空，跳出遍历，设置提交按钮置灰
                    else if (!notRequiredFieldsMap[identityType || IdentityType.EMPLOYED].includes(name[1] as string)) {
                        const value = form.getFieldValue(name);
                        if (name[1] === 'honorDetail' && (!value || !value.length)) {
                            setCanOk(false);
                            return;
                        }

                        if (!value) {
                            setCanOk(false);
                            return;
                        }
                    }
                }

                // 遍历完所有表单项，无校验失败项无必填value为空表单项，则设置提交按钮可用
                setCanOk(true);
            }, 300),
        [form, identityType]
    );

    useEffect(() => {
        fields && synchronizeCanOk();
    }, [fields, synchronizeCanOk]);

    // 向父组件暴露方法
    useImperativeHandle(ref, () => ({
        synchronizeCanOk,
    }));

    // 提交资质信息
    const submitAuthForm = useCallback(() => {
        // 提交信息按钮点击 打点
        clickLog(
            EVENT_VALUE_CONST.SUBMIT,
            {[EVENT_EXT_KEY_CONST.IDENTITY_TYPE]: identityType} as LJExtData,
            EVENT_PAGE_CONST.LIGHTHOUSE_QUALIFICATION_SUBMIT
        );

        if (!canOk) {
            return;
        }

        form.validateFields()
            .then(async () => {
                try {
                    const values = form.getFieldsValue();
                    let careerBasicInfo = {
                        ...values.careerBasicInfo,
                        fansSection: values.careerBasicInfo.fansSection === -1 ? 0 : values.careerBasicInfo.fansSection,
                        teacherType: identityType,
                    };
                    if (careerBasicInfo.honorDetail?.length > 0) {
                        careerBasicInfo = {
                            ...careerBasicInfo,
                            honorDetail: careerBasicInfo?.honorDetail[0]?.response?.url,
                        };
                    }

                    const params = {
                        source: SourceType.TeacherRecruitAgent,
                        careerBasicInfo: careerBasicInfo,
                    };

                    await api.submitAuthInfo(params);
                    await fetchActivityInfo();
                    setShowBasicAuditModule(true);
                } catch (error) {
                    console.error(error);
                }
            })
            .catch(() => {
                setCanOk(false);
            });
    }, [canOk, clickLog, fetchActivityInfo, form, identityType, setShowBasicAuditModule]);

    useEffect(() => {
        // 资质信息 曝光打点
        displayLog(EVENT_PAGE_CONST.LIGHTHOUSE_QUALIFICATION_SUBMIT, {
            [EVENT_EXT_KEY_CONST.IDENTITY_TYPE]: identityType,
        } as LJExtData);
    }, [displayLog, identityType]);

    return (
        <div className="flex w-full gap-2 pb-[36px] pt-4">
            <ModifyIdentityConfirmModal
                showEditModal={showEditConfirmModal}
                closeEditModal={closeEditConfirmModal}
                onOk={onOk}
                isMobile={isMobile}
            />
            {!basicStatus && (
                <Button
                    onClick={editIdentityTypeConfirm}
                    className="h-[40px] flex-1 rounded-full border-none bg-[#E8F3FF] font-medium text-[#4E6EF2]"
                >
                    修改身份
                </Button>
            )}
            <Button
                disabled={!canOk}
                onClick={submitAuthForm}
                type="primary"
                className="h-[40px] flex-1 rounded-full font-medium"
            >
                提交信息
            </Button>
        </div>
    );
});

export default BasicFormSubButtons;
