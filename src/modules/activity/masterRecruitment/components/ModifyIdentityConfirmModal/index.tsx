import {Modal} from 'antd';
import classNames from 'classnames';
import ModalM from '@/components/mobile/Modal/index';

interface ModalProps {
    isMobile: boolean;
    showEditModal: boolean;
    closeEditModal: () => void;
    onOk: () => void;
}

export const ModifyIdentityConfirmModal = ({isMobile, showEditModal, closeEditModal, onOk}: ModalProps) => {
    const ModalComponent = isMobile ? ModalM : Modal;
    const pcModalTitle = isMobile ? '' : '修改确认';
    const modalContentClassName = isMobile ? 'px-6 text-center' : '';
    return (
        <ModalComponent open={showEditModal} onOk={onOk} onCancel={closeEditModal} title={pcModalTitle} okText={'修改'}>
            <div className={classNames('flex flex-col', modalContentClassName)}>
                {isMobile && <div className="mb-[10px] text-[19px] font-medium leading-[19px]">修改确认</div>}
                <div>修改身份后，所填信息无法恢复，确认修改吗？</div>
            </div>
        </ModalComponent>
    );
};
