import {Form} from 'antd';
import {Fragment, ReactNode, useCallback, useRef} from 'react';
import styled from '@emotion/styled';
import {SelectProps as PcSelectProps} from 'antd';
import SelectM, {Props as WiseSelectProps} from '@/components/Select/index-m';
import Select from '@/components/Select/index';
import {useIsMobileStore} from '@/store/home/<USER>';

const StyledSelectWise = styled(SelectM)`
    .mobile-select-selector {
        font-size: 16px !important;
        width: unset !important;

        .mobile-select-value {
            margin-left: 0px !important;
        }

        .iconfont {
            font-size: 1.125rem !important;
            color: #848691 !important;
            line-height: 19px !important;
            margin-right: 0px !important;
        }
    }
`;

type selectProps = WiseSelectProps & PcSelectProps;

interface SelectWithLabelProps extends selectProps {
    label: string;
    placeholder: string;
    required?: boolean;
    errorMsgNode?: ReactNode;
}

const WiseSelect = (props: SelectWithLabelProps) => {
    return (
        <StyledSelectWise
            options={props.options}
            value={props.value}
            onChange={props.onChange}
            className="min-h-0 w-full justify-end bg-white p-0 leading-none"
            placeholder={props.placeholder}
            suffixIcon="right"
        />
    );
};

const PCSelect = (props: PcSelectProps) => {
    return (
        <Select
            options={props.options}
            placeholder={props.placeholder}
            value={props.value}
            onChange={props.onChange}
            themeToken={{
                colorTextPlaceholder: '#848691',
                controlHeight: 19,
                fontSize: 16,
                optionHeight: 30,
                optionLineHeight: '30px',
                selectorBg: '#fff',
                optionPadding: '4px 12px',
            }}
            className="w-full text-right"
            getPopupContainer={props.getPopupContainer}
            dropdownAlign={{
                points: ['tl', 'bl'],
                offset: [0, 22],
            }}
            dropdownStyle={{
                width: '100%', // 继承 section 宽度
                position: 'absolute',
                left: 0,
            }}
        />
    );
};

export default function SelectWithLabel(props: SelectWithLabelProps) {
    const sectionRef = useRef<HTMLElement>(null);
    const {status, errors} = Form.Item.useStatus();
    const {errorMsgNode} = props;
    const isMobile = useIsMobileStore(store => store.isMobile);

    const getPopupContainer = useCallback(() => {
        return sectionRef?.current as HTMLElement;
    }, []);

    return (
        <Fragment>
            <section
                ref={sectionRef}
                className={`relative flex items-center rounded-[12px] bg-white px-[13px] py-[14px] leading-none ${
                    props.className || ''
                }`}
            >
                <div className="flex-shrink-0 text-base font-semibold" key="label">
                    {props.label}
                </div>
                {props.required ? (
                    <span
                        className="mt-1 w-6 flex-shrink-0 pl-1 font-semibold text-error"
                        style={{fontFamily: 'SimSong'}}
                        key="require"
                    >
                        *
                    </span>
                ) : (
                    <div className="w-6 flex-shrink-0" />
                )}
                {isMobile ? <WiseSelect {...props} /> : <PCSelect {...props} getPopupContainer={getPopupContainer} />}
            </section>
            {errorMsgNode || (
                <div
                    className={`pl-3 leading-none text-error ${status === 'error' ? 'pt-3' : 'h-0'}`}
                    style={{
                        transition: 'all 0.2s linear',
                    }}
                >
                    {errors}
                </div>
            )}
        </Fragment>
    );
}
