/* eslint-disable complexity */
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ConfigProvider} from 'antd';
import CommonErrorBoundary from '@/components/CommonErrorBoundary';
import LoadError from '@/components/Loading/LoadError';
import Loading from '@/components/Loading';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {CareerInfoStatus} from '@/api/activity/masterRecruitment/interface';
import {useIsMobileStore} from '@/store/home/<USER>';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {useUbcLogV3} from '@/utils/loggerV2/useUbcLoggerV3';
import {SERVER_ID} from '@/utils/loggerV2/useUbcLoggerV2';
import {LJExtData} from '@/utils/loggerV2/utils';
import {LogContextProvider} from '@/utils/loggerV2/context';
import {useMasterRecruitmentStore} from './store/index';
import {setMetaTag, initInboxShare} from './utils/index';
import PcIdentityModal from './pc/IdentityModal';
import WiseIdentityModal from './wise/IdentityModal';
import WiseActivityRuleModal from './wise/ActivityRuleModal';
import PcActivityRuleModal from './pc/ActivityRuleModal';
import Create from './create';
import Auth from './auth';
import Recruitment from './recruitment';
import AuthInfo from './authInfo';

function MasterRecruitment() {
    const isLogin = useUserInfoStore(store => store.isLogin);
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 测试
    const {
        identityType,
        baseInfoStatus,
        showIdentityModal,
        showActivityRuleModal,
        showBasicInfoForm,
        setIdentityType,
        fetchActivityInfo,
        setShowIdentityModal,
        setShowActivityRuleModal,
        setShowBasicInfoForm,
    } = useMasterRecruitmentStore(store => ({
        identityType: store.baseInfo.teacherType,
        baseInfoStatus: store.baseInfo.status,
        showIdentityModal: store.showIdentityModal,
        showActivityRuleModal: store.showActivityRuleModal,
        showBasicInfoForm: store.showBasicInfoForm,
        setIdentityType: store.setIdentityType,
        fetchActivityInfo: store.fetchActivityInfo,
        setShowIdentityModal: store.setShowIdentityModal,
        setShowActivityRuleModal: store.setShowActivityRuleModal,
        setShowBasicInfoForm: store.setShowBasicInfoForm,
    }));

    const [loading, setLoading] = useState(true);

    const {showLog, clickLog} = useUbcLogV3();

    // 未登陆展示招募页
    // 查询初始化数据（如有填写：认证信息、基础信息、智能体信息）展示“创建页”
    useEffect(() => {
        (async () => {
            try {
                if (isLogin) {
                    setLoading(true);
                    await fetchActivityInfo();
                    setLoading(false);
                } else {
                    setLoading(false);
                }
            } catch (err) {
                setLoading(false);
                console.error(err);
            }
        })();
    }, [isLogin, fetchActivityInfo]);

    const pageType = useMemo(() => {
        // console.log('pageType');
        // return 'agentCreate';
        if (loading) {
            return '';
        }

        // 未登陆或未提交身份信息时展示招募页
        if (!isLogin || !identityType) {
            return 'recruitment';
        }
        // 身份已选择，基础信息未填写 展示认证页
        else if (identityType && !baseInfoStatus && !showBasicInfoForm) {
            return 'authInfo';
        }
        // 基础信息审核不成功 展示资质信息页
        else if (showBasicInfoForm ? showBasicInfoForm : baseInfoStatus !== CareerInfoStatus.Pass) {
            return 'baseInfo';
        }
        // 基础信息审核成功
        else if (baseInfoStatus === CareerInfoStatus.Pass) {
            return 'agentCreate';
        }
        // 首页重新
        else {
            return 'recruitment';
        }
    }, [loading, isLogin, identityType, baseInfoStatus, showBasicInfoForm]);

    // 关闭身份选择弹窗
    const closeIdentityModal = useCallback(() => {
        setShowIdentityModal(false);
    }, [setShowIdentityModal]);

    // 关闭活动规则弹窗
    const closeActivityRuleModal = useCallback(() => {
        setShowActivityRuleModal(false);
    }, [setShowActivityRuleModal]);

    const [modalIdentityType, setModalIdentityType] = useState<any>(identityType || null);

    // 提交身份类型
    const submitIdentityType = useCallback(() => {
        setShowIdentityModal(false);

        // 提交身份类型 打点
        clickLog(
            EVENT_VALUE_CONST.CONFIRM,
            {[EVENT_EXT_KEY_CONST.IDENTITY_TYPE]: modalIdentityType} as LJExtData,
            EVENT_PAGE_CONST.LIGHTHOUSE_RECRUIT_ACTIVITY
        );

        // 确认提提交身份选择 将身份类型选择保存到store中
        setIdentityType(modalIdentityType);

        // 当在资质表单 提交身份类型 回到认证页
        if (pageType === 'baseInfo') {
            setShowBasicInfoForm(false);
        }
    }, [setShowIdentityModal, clickLog, modalIdentityType, setIdentityType, pageType, setShowBasicInfoForm]);

    useEffect(() => {
        if (showIdentityModal) {
            // 身份选择 曝光 打点
            showLog(EVENT_VALUE_CONST.IDENTITY_TYPE, {}, EVENT_PAGE_CONST.LIGHTHOUSE_RECRUIT_ACTIVITY);
        }
    }, [showIdentityModal, showLog]);

    return (
        <div className="bg-gray-bg-base">
            {loading ? (
                <div className="relative h-[100vh] w-[100vw]">
                    <Loading />
                </div>
            ) : (
                <>
                    {/* 招募页 */}
                    {pageType === 'recruitment' && <Recruitment />}
                    {/* 授权信息展示页 （授权信息+未授权提示页） */}
                    {pageType === 'authInfo' && <AuthInfo />}
                    {/* 基础信息提交页（表单+审核页） */}
                    {pageType === 'baseInfo' && <Auth />}
                    {/* 智能体创建页（创建表单+审核页） */}
                    {pageType === 'agentCreate' && <Create />}
                </>
            )}

            {/* 身份选择弹窗 */}
            {isMobile ? (
                <WiseIdentityModal
                    showIdentityModal={showIdentityModal}
                    handleClose={closeIdentityModal}
                    handleOk={submitIdentityType}
                    identityType={modalIdentityType}
                    setIdentityType={setModalIdentityType}
                />
            ) : (
                <PcIdentityModal
                    showIdentityModal={showIdentityModal}
                    handleClose={closeIdentityModal}
                    handleOk={submitIdentityType}
                    identityType={modalIdentityType}
                    setIdentityType={setModalIdentityType}
                />
            )}

            {/* 活动规则弹窗 */}
            {isMobile ? (
                <WiseActivityRuleModal
                    showActivityRuleModal={showActivityRuleModal}
                    handleClose={closeActivityRuleModal}
                    handleOk={closeActivityRuleModal}
                />
            ) : (
                <PcActivityRuleModal
                    showActivityRuleModal={showActivityRuleModal}
                    handleClose={closeActivityRuleModal}
                    handleOk={closeActivityRuleModal}
                />
            )}
        </div>
    );
}

export default function Index() {
    const loadError = useCallback(() => <LoadError />, []);

    useEffect(() => {
        document.title = '灯塔计划 | 百度AI数字人教育普惠计划';
        setMetaTag('og:image', 'https://yourdomain.com/path/to/share.jpg');
        setMetaTag('og:title', '灯塔计划 | 百度AI数字人教育普惠计划');
        setMetaTag('og:description', '灯塔计划 | 百度AI数字人教育普惠计划');
        initInboxShare();
    }, []);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Input: {
                        colorBorder: 'transparent',
                        fontSize: 16,
                        colorErrorText: 'red',
                        colorTextPlaceholder: '#848691',
                    },
                },
            }}
        >
            <CommonErrorBoundary renderError={loadError}>
                <LogContextProvider serverId={SERVER_ID.Activity}>
                    <MasterRecruitment />
                </LogContextProvider>
            </CommonErrorBoundary>
        </ConfigProvider>
    );
}
