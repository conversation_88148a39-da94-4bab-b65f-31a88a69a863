/**
 * @file 「开发者赚钱季」 提交作品页面
 * <AUTHOR>
 */

// TODO: 实际表单项需要更改
import React, {useCallback, useState, useEffect} from 'react';
import {Button, Form, Input, Radio, Row, message} from 'antd';
import classNames from 'classnames';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {validatePhone, validateSpecialCharacter} from '@/utils/formValidator';
import {ContactType, QuestionnaireType} from '@/api/activity/interface';
import api from '@/api/activity';
import urls from '@/links';
import {useIsMobileStore} from '@/store/home/<USER>';
import {formItemStyle, COMPETITION_TYPE, CompetitionTypeFormQrCodeMap} from './constants';
import {validateNotEmpty} from './utils';
const {TextArea} = Input;
// 初始数据
const initialValues = {
    name: '',
    contactType: ContactType.Phone,
    contactNumber: '',
    agentName: '',
    agentId: '',
    agentDesc: '',
};

const SubmitWorkForm: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    const navigate = useNavigate();
    const fields = Form.useWatch([], form);

    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));
    const competitionType = searchParams.get('type') || COMPETITION_TYPE.grass;

    const isMobile = useIsMobileStore(store => store.isMobile);
    // 如果缺少参数，则重定向到中心页面
    useEffect(() => {
        if (!(competitionId && competitionType)) {
            message.info('访问参数不正确');
            navigate(urls.activityDpCenter.raw(), {replace: true});
        }
    }, [navigate, competitionId, competitionType]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        setLoading(true);
        const fields = await form.validateFields().catch(() => setLoading(false));

        try {
            if (fields && competitionId) {
                await api.questionnaireSubmit({
                    competitionId,
                    questionnaireType: QuestionnaireType.SubmitWork,
                    questionnaireDetail: JSON.stringify(fields),
                });
            }
            setLoading(false);
            message.success('提交成功');
            // 跳转至创建智能体页面
            navigate(urls.center.raw());
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [navigate, form, competitionId]);

    const onSelectChange = useCallback(() => {
        form.validateFields(['contactNumber']);
    }, [form]);

    return (
        <div className={`flex min-h-[100vh] justify-center bg-questionnaire-bg pb-[50px] pt-[50px] mlg:pt-[83px]`}>
            <div className="box-content flex max-w-[800px] px-6">
                <div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">
                        提交作品表
                    </div>
                    <div className="mb-2 max-w-[441px] text-justify text-xs leading-[20px] text-neutral-800">
                        欢迎参与智能体赛道，您正在提交「{competitionType}」作品
                    </div>
                    <Form
                        labelCol={{
                            span: 12,
                        }}
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        initialValues={initialValues}
                        className={classNames(formItemStyle, 'w-[22.5rem]')}
                    >
                        <Form.Item
                            name="name"
                            label="您的姓名"
                            rules={[
                                {
                                    validator(rule, value) {
                                        return validateNotEmpty(value, '姓名不能为空，请输入');
                                    },
                                },
                                {validator: validateSpecialCharacter('请输入您的真实姓名')},
                            ]}
                        >
                            <Input maxLength={16} showCount placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item name="contactType" label="您的联系方式">
                            <Radio.Group onChange={onSelectChange}>
                                <Radio value={ContactType.Phone}>联系电话</Radio>
                                <Radio value={ContactType.WeChat}>微信</Radio>
                                <Radio value={ContactType.Other}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>

                        {fields?.contactType === ContactType.Phone ? (
                            <Form.Item
                                name="contactNumber"
                                label="联系电话"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '联系电话不能为空，请输入');
                                        },
                                    },
                                    {validator: validatePhone('联系电话格式有误，请重新输入')},
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : fields?.contactType === ContactType.Other ? (
                            <Form.Item
                                name="contactNumber"
                                label="其他"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '联系方式不能为空，请输入');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="contactNumber"
                                label="微信号"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '微信号不能为空，请输入');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        )}

                        <Form.Item
                            name="agentName"
                            label="提交的智能体名称"
                            rules={[
                                {
                                    validator(rule, value) {
                                        return validateNotEmpty(value, '请填写智能体名称');
                                    },
                                },
                            ]}
                        >
                            <Input placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item
                            name="agentId"
                            label="提交的智能体ID"
                            rules={[
                                {
                                    validator(rule, value) {
                                        return validateNotEmpty(value, '请填写智能体ID');
                                    },
                                },
                            ]}
                        >
                            <div>
                                <Row align={'middle'} className="mb-[10px] leading-none text-gray-tertiary">
                                    <span>个人空间-我的智能体-选中参赛智能体-复制ID</span>
                                </Row>
                                <Input placeholder="请输入" autoComplete="off" />
                            </div>
                        </Form.Item>

                        <Form.Item
                            name="agentDesc"
                            label="智能体简介"
                            rules={[
                                {
                                    validator(rule, value) {
                                        return validateNotEmpty(value, '请填写智能体简介');
                                    },
                                },
                            ]}
                        >
                            <div>
                                <Row align={'middle'} className="mb-[10px] leading-none text-gray-tertiary">
                                    <span>请简述您的智能体应用场景及核心商业价值。</span>
                                </Row>
                                <TextArea placeholder="请输入" autoComplete="off" />
                            </div>
                        </Form.Item>

                        {isMobile && (
                            <div className="h-[160px] w-full pb-3">
                                <span className="text-xs">手机端打开请保存二维码，微信扫码进群</span>
                                <img
                                    className="mt-2 h-[120px] pb-1"
                                    src={CompetitionTypeFormQrCodeMap[competitionType]}
                                />
                            </div>
                        )}

                        <Button type="primary" htmlType="submit" loading={loading}>
                            提交
                        </Button>
                    </Form>
                </div>
                {!isMobile && (
                    <div
                        className="ml-[8.125rem] box-border h-[307px] w-[247px] shrink-0 rounded-3xl border border-black border-opacity-10
                    bg-white p-[18px]"
                    >
                        <span className="text-sm">扫码加入赛事交流群，获取更多大赛信息</span>
                        <img className="mt-2" src={CompetitionTypeFormQrCodeMap[competitionType]} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default SubmitWorkForm;
