/**
 * @file 开发者报名页面
 * <AUTHOR>
 */

// TODO:实际的表单项目
import React, {useCallback, useState, useEffect} from 'react';
import {Button, Form, Input, Space, Radio, message, Select} from 'antd';
import classNames from 'classnames';
import {useNavigate, useSearchParams} from 'react-router-dom';
import styled from '@emotion/styled';
import {validatePhone, validateSpecialCharacter} from '@/utils/formValidator';
import {
    CustomerType,
    ExperienceType,
    ContactType,
    QuestionnaireType,
    SubmitStatus,
    ParticipateOther,
    AgentType,
} from '@/api/activity/interface';
import api from '@/api/activity';
import urls from '@/links';
import {useIsMobileStore} from '@/store/home/<USER>';
import {getPopupContainer} from '@/utils/getPopupContainer';
import {formItemStyle, COMPETITION_TYPE, WELCOME_TITLE, INFO_SOURCES, CompetitionTypeFormQrCodeMap} from './constants';
import {validateNotEmpty} from './utils';

// 初始数据
const initialValues = {
    name: '',
    contactType: ContactType.Phone,
    contactNumber: '',
    playerType: CustomerType.Enterprise,
    agentDevExperience: ExperienceType.AgentExperience,
    agentDomain: AgentType.Health,
    participateOther: ParticipateOther.YES,
};
const StyledSelect = styled(Select)`
    -webkit-tap-highlight-color: transparent;
`;
// eslint-disable-next-line complexity
const DeveloperApplyForm: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    const navigate = useNavigate();
    const fields = Form.useWatch([], form);
    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));
    const competitionType = searchParams.get('type') || COMPETITION_TYPE.grass;
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 防止手输url直接访问该页面，如果缺少参数，则重定向到中心页面
    useEffect(() => {
        if (!(competitionId && competitionType)) {
            message.info('访问参数不正确');
            navigate(urls.activityDpCenter.raw(), {replace: true});
        }
    }, [competitionId, competitionType, navigate]);

    // 请求问卷状态
    const getQuestionnaireStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                });

                // 填写过则重定向值赛事中心
                if (res.submitStatus === SubmitStatus.Submitted) {
                    message.info('您已提交过报名申请');
                    navigate(urls.activityDpCenter.raw(), {replace: true});
                }
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId, navigate]);

    // 进入表单页面，获取问卷填写状态
    useEffect(() => {
        getQuestionnaireStatus();
    }, [getQuestionnaireStatus]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        setLoading(true);
        const fields = await form.validateFields().catch(() => setLoading(false));

        try {
            if (fields && competitionId) {
                await api.questionnaireSubmit({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                    questionnaireDetail: JSON.stringify(fields),
                });
                message.success('报名成功');
                // 跳转至创建智能体页面
                navigate(urls.center.raw());
            }
            setLoading(false);
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [navigate, form, competitionId]);

    const onSelectChange = useCallback(() => {
        form.validateFields(['contactNumber']);
    }, [form]);

    return (
        <div className={`flex min-h-[100vh] justify-center bg-questionnaire-bg pb-[50px] pt-[50px] mlg:pt-[83px]`}>
            <div className="box-content flex max-w-[800px] px-6">
                <div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">
                        AgentBuilder商领计划
                    </div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">报名表</div>
                    <div className="mb-2 max-w-[441px] text-justify text-xs leading-[20px] text-neutral-800">
                        {WELCOME_TITLE[competitionType]}
                    </div>
                    <Form
                        labelCol={{
                            span: 12,
                        }}
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        initialValues={initialValues}
                        className={classNames(formItemStyle, 'w-[22.5rem]')}
                    >
                        <Form.Item
                            name="name"
                            label="您的姓名"
                            rules={[
                                {
                                    validator(rule, value) {
                                        return validateNotEmpty(value, '姓名不能为空，请输入');
                                    },
                                },
                                {validator: validateSpecialCharacter('请输入您的真实姓名')},
                            ]}
                        >
                            <Input maxLength={16} showCount placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item name="contactType" label="您的联系方式">
                            <Radio.Group onChange={onSelectChange}>
                                <Radio value={ContactType.Phone}>联系电话</Radio>
                                <Radio value={ContactType.WeChat}>微信</Radio>
                                <Radio value={ContactType.Other}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>

                        {fields?.contactType === ContactType.Phone ? (
                            <Form.Item
                                name="contactNumber"
                                label="联系电话"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '联系电话不能为空，请输入');
                                        },
                                    },
                                    {validator: validatePhone('联系电话格式有误，请重新输入')},
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : fields?.contactType === ContactType.Other ? (
                            <Form.Item
                                name="contactNumber"
                                label="其他"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '联系方式不能为空，请输入');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="contactNumber"
                                label="微信号"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '微信号不能为空，请输入');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        )}
                        {(competitionType === COMPETITION_TYPE.reward ||
                            competitionType === COMPETITION_TYPE.advertising) && (
                            <Form.Item
                                name="agentId"
                                label="报名的智能体ID"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '请输入报名的智能体ID,我们将此id开通白名单');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="我们将此id开通白名单" autoComplete="off" />
                            </Form.Item>
                        )}
                        <Form.Item
                            name="playerType"
                            label="您是学生/个人/企业开发者"
                            rules={[{required: true, message: '请选择您的主体类型'}]}
                            className={classNames({hidden: competitionType === COMPETITION_TYPE.service})}
                        >
                            <Radio.Group>
                                <Space direction="vertical">
                                    <Radio value={CustomerType.Student}>学生</Radio>
                                    <Radio value={CustomerType.Personal}>个人</Radio>
                                    <Radio value={CustomerType.Enterprise}>企业</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>

                        {fields?.playerType === CustomerType.Enterprise && (
                            <Form.Item
                                name="industry"
                                label="您企业所在行业是"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            return validateNotEmpty(value, '请输入您所在行业');
                                        },
                                    },
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        )}

                        {competitionType === COMPETITION_TYPE.reward && (
                            <Form.Item
                                name="agentDomain"
                                label="您智能体所属领域"
                                rules={[{required: true, message: '请输入您智能体所属领域'}]}
                            >
                                <Radio.Group>
                                    <Space direction="vertical">
                                        <Radio value={AgentType.Health}>健康</Radio>
                                        <Radio value={AgentType.Emotion}>情感</Radio>
                                        <Radio value={AgentType.Law}>法律</Radio>
                                    </Space>
                                </Radio.Group>
                            </Form.Item>
                        )}
                        <Form.Item
                            name="agentDevExperience"
                            label="您是否有过智能体开发经验"
                            rules={[{required: true, message: '请选择您的智能体开发经验'}]}
                        >
                            <Radio.Group>
                                <Space direction="vertical">
                                    <Radio value={ExperienceType.AgentExperience}>是，有0代码智能体开发经验</Radio>
                                    <Radio value={ExperienceType.NoExperience}>否</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>

                        <Form.Item
                            name="participateOther"
                            label="您是否还会参加其他商业化方向"
                            rules={[{required: true, message: '请选择是否还会参加其他商业化方向'}]}
                        >
                            <Radio.Group>
                                <Space direction="vertical">
                                    <Radio value={ParticipateOther.YES}>是</Radio>
                                    <Radio value={ParticipateOther.NO}>否</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            name="infoSource"
                            label="请问您从哪里了解到的报名信息"
                            rules={[{required: true, message: '请选择您从哪里了解到的报名信息'}]}
                        >
                            <StyledSelect
                                options={INFO_SOURCES}
                                getPopupContainer={getPopupContainer}
                                placeholder="请选择"
                            />
                        </Form.Item>

                        {isMobile && (
                            <>
                                <div className="h-[150px] w-full ">
                                    <span className="text-xs">手机端打开请保存二维码，微信扫码进群</span>
                                    <img
                                        className="mt-2 h-[100px]"
                                        src={CompetitionTypeFormQrCodeMap[competitionType]}
                                    />
                                </div>
                            </>
                        )}

                        <Button type="primary" htmlType="submit" loading={loading}>
                            提交
                        </Button>
                    </Form>
                </div>

                {!isMobile && (
                    <div
                        className="ml-[8.125rem] box-border h-[307px] w-[247px] shrink-0 rounded-xl border border-black border-opacity-10
                    bg-white p-[28px]"
                    >
                        <span className="text-sm">扫码加入赛事交流群，获取更多大赛信息</span>
                        <img className="mt-2" src={CompetitionTypeFormQrCodeMap[competitionType]} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default DeveloperApplyForm;
