/**
 * @file 「开发者赚钱季」赛事详情页面
 * <AUTHOR>
 */
import React, {useCallback, useEffect, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import urls from '@/links';
import api from '@/api/activity/index';
import {QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import {sendLogV2, SERVER_ID} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_EXT_KEY_CONST, EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {EVENT_TYPE} from '@/utils/loggerV2/ubcLoggerV2';
import {useIsMobileStore} from '@/store/home/<USER>';
import ActivityButton from './components/ActivityButton';
import ActivityTitle from './components/ActivityTitle';
import BlockContainer from './components/BlockContainer';
import {CompetitionDetailsBgMap, CompetitionTypeLogExtMap, CompetitionTypeQrCodeMap, DETAILLIST} from './constants';
import Detail from './components/Detail';
import {QrCode} from './components/QrCode';

const ActivityDetail = () => {
    const uniformLogin = useUniformLogin();
    const isLogin = useUserInfoStore(store => store.isLogin);

    const [searchParams] = useSearchParams();
    const competitionType = searchParams.get('type') || 'game';
    const competitionId = Number(searchParams.get('id'));
    // 该赛事是否报名
    const [hasApplied, setHasApplied] = useState<boolean>(false);
    const isMobile = useIsMobileStore(store => store.isMobile);

    const getDeveloperApplyStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                });
                setHasApplied(res.submitStatus === SubmitStatus.Submitted);
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId]);

    // 查询报名状态
    useEffect(() => {
        if (isLogin) {
            getDeveloperApplyStatus();
        }
    }, [getDeveloperApplyStatus, isLogin]);

    // 提交作品回调
    const handleClickSubmitWork = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activityDpSubmitWork.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    // 打开报名表单页面
    const handleClickSignUp = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            event.stopPropagation(); // 阻止事件冒泡到外层容器
            if (isLogin) {
                window.open(`${urls.activityDpDeveloperApply.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    useEffect(() => {
        window.scrollTo(0, 0); // 重置滚动位置到顶部
    }, []);

    // 页面展现点位记录
    useEffect(() => {
        sendLogV2(
            EVENT_TYPE.DISPLAY,
            '',
            EVENT_PAGE_CONST.BUSINESS_LEAD_DETAIL,
            {
                [EVENT_EXT_KEY_CONST.BUSINESS_LEAD_TOPIC]: CompetitionTypeLogExtMap[competitionType],
            },
            SERVER_ID.Activity
        );
    }, [competitionType]);

    return (
        <div className="flex w-full flex-col items-center bg-[#e6e9fb] text-white/80 mlg:min-w-[1400px] mlg:text-[24px]">
            {/* 第一屏 */}
            <div className="flex w-full justify-center mlg:min-w-[1200px]">
                {/* 第一屏背景 - 赛事海报 */}
                <img className="w-full mlg:min-w-[1200px]" src={CompetitionDetailsBgMap[competitionType]} />
            </div>
            {/* 详情页面的主要内容 */}
            <main className="mt-4 flex w-full flex-col lg:mt-6">
                {/* 赛题介绍区域 */}
                {DETAILLIST[competitionType] && <Detail info={DETAILLIST[competitionType]} />}

                {/* 赛题交流群区域 */}
                <BlockContainer>
                    <div className="flex w-full flex-col items-center gap-6 mlg:gap-12">
                        {/* 赛题交流群标题 */}

                        <ActivityTitle>赛题交流群</ActivityTitle>
                        {isMobile && (
                            <div className="mt-[10px] text-[#2c44d3]">手机端打开请保存二维码，微信扫码进群</div>
                        )}
                        {/* 赛题交流群二维码 */}
                        <div className="max-mlg:w-[50%] ">
                            <QrCode url={CompetitionTypeQrCodeMap[competitionType]} />
                        </div>
                        {/* 报名按钮 */}
                        {hasApplied ? (
                            <ActivityButton onClick={handleClickSubmitWork} size="large">
                                提交作品
                            </ActivityButton>
                        ) : (
                            <ActivityButton onClick={handleClickSignUp} size="large">
                                立即报名
                            </ActivityButton>
                        )}
                    </div>
                </BlockContainer>
            </main>
        </div>
    );
};

export default ActivityDetail;
