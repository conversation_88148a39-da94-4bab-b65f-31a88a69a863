/**
 * @file 常量
 * <AUTHOR>
 */
import {css} from '@emotion/css';
import {BUSINESS_LEAD_TOPIC_TYPE} from '@/utils/loggerV2/interface';
import {InfoType} from '@/api/activity/interface';
// Card背景图
import grassCardBg from './assets/CardBg/2-1.png';
import serviceCardBg from './assets/CardBg/2-2.png';
import rewardCardBg from './assets/CardBg/2-3.png';
import advertisingCardBg from './assets/CardBg/2-4.png';
// detail顶部图
import grassTop from './assets/top/top1.jpg';
import serviceTop from './assets/top/top2.jpg';
import rewardTop from './assets/top/top3.jpg';
import advertisingTop from './assets/top/top4.jpg';

// 赛程
import subject1 from './assets/schedule-card-bg/4-1.png';
import subject2 from './assets/schedule-card-bg/4-2.png';
import subject3 from './assets/schedule-card-bg/4-3.png';
import subject4 from './assets/schedule-card-bg/4-4.png';

// 课程
import class1 from './assets/class/5-1.png';
import class2 from './assets/class/5-2.png';
import class3 from './assets/class/5-3.png';
import class4 from './assets/class/5-4.png';

// 奖项
import price1 from './assets/price/3-1.png';
import price2 from './assets/price/3-2.png';
import price3 from './assets/price/3-3.png';
import price4 from './assets/price/3-4.png';

// 二维码
import normalQrcode from './assets/qrcode/normal.png';
import serviceQrcode from './assets/qrcode/service.png';
import bnormalQrcode from './assets/qrcode/normal.jpg';
import bserviceQrcode from './assets/qrcode/service.jpg';

// 赛程配置
export const SUBJECT_CONFIG = [
    {
        id: '4-1',
        img: subject1,
    },
    {
        id: '4-2',
        img: subject2,
    },
    {
        id: '4-3',
        img: subject3,
    },
    {
        id: '4-4',
        img: subject4,
    },
];
// 可曾配置
export const CLASS_CONFIG = [
    {
        id: '5-1',
        img: class1,
    },
    {
        id: '5-2',
        img: class2,
    },
    {
        id: '5-3',
        img: class3,
    },
    {
        id: '5-4',
        img: class4,
    },
];

// 奖项配置
export const TECH_AWARD_CONFIG = [
    {
        id: '3-1',
        img: price1,
    },
    {
        id: '3-2',
        img: price2,
    },
    {
        id: '3-3',
        img: price3,
    },
    {
        id: '3-4',
        img: price4,
    },
];

// 表单项样式
export const formItemStyle = css`
    .ant-form-item-label .ant-form-item-required::before {
        display: none !important;
    }
    .ant-form-item-label .ant-form-item-required::after {
        display: inline-block !important;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 1;
        visibility: visible !important;
        top: 3px;
        content: '*' !important;
    }
    .ant-form-item-label label {
        font-weight: 500;
    }
    .ant-col-12 {
        max-width: 80% !important;
    }
    .ant-input-disabled.ant-input[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
    .ant-picker .ant-picker-input > input[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
    .ant-input-affix-wrapper-disabled,
    .ant-input-affix-wrapper[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
`;

// 赛题类型配置
export const COMPETITION_TYPE = {
    grass: '商品种草转化',
    service: '企业服务表单',
    reward: '服务咨询打赏',
    advertising: '联盟广告变现',
};

// 不同类型比赛的背景kv配置
export const CompetitionTypeBgMap: Record<string, string> = {
    [COMPETITION_TYPE.grass]: grassCardBg,
    [COMPETITION_TYPE.service]: serviceCardBg,
    [COMPETITION_TYPE.reward]: rewardCardBg,
    [COMPETITION_TYPE.advertising]: advertisingCardBg,
};

// 不同类型比赛的详情页背景kv配置
export const CompetitionDetailsBgMap: Record<string, string> = {
    [COMPETITION_TYPE.grass]: grassTop,
    [COMPETITION_TYPE.service]: serviceTop,
    [COMPETITION_TYPE.reward]: rewardTop,
    [COMPETITION_TYPE.advertising]: advertisingTop,
};

// 不同类型比赛的群二维码配置
export const CompetitionTypeQrCodeMap: Record<string, string> = {
    [COMPETITION_TYPE.grass]: normalQrcode,
    [COMPETITION_TYPE.service]: normalQrcode,
    [COMPETITION_TYPE.reward]: serviceQrcode,
    [COMPETITION_TYPE.advertising]: serviceQrcode,
};

// 不同类型比赛的表单二维码
export const CompetitionTypeFormQrCodeMap: Record<string, string> = {
    [COMPETITION_TYPE.grass]: bnormalQrcode,
    [COMPETITION_TYPE.service]: bnormalQrcode,
    [COMPETITION_TYPE.reward]: bserviceQrcode,
    [COMPETITION_TYPE.advertising]: bserviceQrcode,
};
export const PP_PAGE_URL = 'https://aistudio.baidu.com/competition/detail/1235/0/introduction';

// 不同类型比赛的的点位ext值
export const CompetitionTypeLogExtMap: Record<string, BUSINESS_LEAD_TOPIC_TYPE> = {
    [COMPETITION_TYPE.grass]: BUSINESS_LEAD_TOPIC_TYPE.GRASS,
    [COMPETITION_TYPE.service]: BUSINESS_LEAD_TOPIC_TYPE.SERVICE,
    [COMPETITION_TYPE.reward]: BUSINESS_LEAD_TOPIC_TYPE.REWARD,
    [COMPETITION_TYPE.advertising]: BUSINESS_LEAD_TOPIC_TYPE.AD,
};

// 报名信息来源配置
export const INFO_SOURCES = [
    {
        value: InfoType.Agent,
        label: '文心智能体官方平台',
    },
    {
        value: InfoType.CSDN,
        label: 'CSDN',
    },
    {
        value: InfoType.CTO,
        label: '51CTO',
    },
    {
        value: InfoType.Jixing,
        label: '极星会',
    },
    {
        value: InfoType.Renren,
        label: '人人都是产品经理',
    },
    {
        value: InfoType.Other,
        label: '其他',
    },
];

export const DETAILLIST = {
    [COMPETITION_TYPE.grass]: {
        intruction:
            '智能体配置商品链接组件，通过智能体实现商品成单，进而开发者可获得分佣。目前商品组件功能已上线，可在活动提交周期内随时提交智能体。若提交智能体未按要求挂载商品链接组件，则默认视为提交无效。',
        time: '10.23日起即可报名，针对平台全量开发者均可参加提交作品',
        rule: [
            '智能体质量：对话内容质量优、流畅性好、角色设定明确、组件挂载准确，可以满足能力范围内绝大多数要求',
            '用户认可度：智能体在搜索场景、站外分享等渠道对话次数',
            '商业价值：考虑智能体商业组件的召回、点击、成交量等关键经营数据指标的达成情况',
        ],
    },
    [COMPETITION_TYPE.service]: {
        intruction:
            '智能体配置线索转化组件，通过智能体实现表单留资，进而达成开发者商业诉求。目前线索组件功能已上线，需要开发者完成企业认证后可在活动提交周期内随时提交智能体。若提交智能体未按要求挂载线索转化组件，则默认视为提交无效。',
        time: '10.23日起即可报名，在平台完成企业认证均可参加提交作品',
        rule: [
            '智能体质量：对话内容质量优、流畅性好、角色设定明确、组件挂载准确，可以满足能力范围内绝大多数要求',
            '智能体分发量：智能体在搜索场景、站外分享等渠道对话次数',
            '智能体转化量：考虑智能体商业组件的召回、点击、线索收集量等关键经营数据指标的达成情况',
        ],
    },
    [COMPETITION_TYPE.reward]: {
        intruction:
            '智能体配置垂类组件，如咨询支付、打赏功能等，通过智能体实现咨询对话，进而达成开发者商业诉求。特殊垂类组件需完成白名单开通，并在学习完对应方向课程可随时提交智能体。若提交智能体未按要求挂载相关垂类组件，则默认视为提交无效。',
        instructionRuls: [
            '健康垂类：仅面向大健康方向科普、医疗医美服务、四品一械等开发者，参与需保证健康行业资质合规。11月11日开启智能体提交通道，提交后视为有效。',
            '法律垂类：仅面向法律方向从业者，参与需保证行业职业资质合规。需入驻百度律临。11月11日开启智能体提交通道，提交后视为有效。',
            '情感垂类：面向强烈同理心、高超沟通技巧、丰富情感智慧、扎实心理知识等特征的开发者人群。需入驻百度问一问。12月2日开启智能体提交通道，提交后视为有效。',
        ],
        time: '目前处于邀请内测阶段，本次活动采用白名单参与方式，我们将收集大家填写的智能体id陆续为大家开通白名单，最晚12月1日开通完成',
        rule: [
            '智能体质量：对话内容质量优、流畅性好、角色设定明确、组件挂载准确，可以满足能力范围内绝大多数要求',
            '智能体分发量：智能体在搜索场景、站外分享等渠道对话次数',
            '智能体转化量：考虑智能体商业组件的召回、点击、咨询量等关键经营数据指标的达成情况',
        ],
    },
    [COMPETITION_TYPE.advertising]: {
        intruction:
            '智能体配置联盟广告组件，通过智能体实现联盟广告分润，进而达成变现目的。可提前进行方向报名，学习完对应方向课程后可随时提交智能体。若提交智能体未按要求挂载联盟广告组件，则默认视为提交无效。',
        time: '目前处于邀请内测阶段，本次活动采用白名单参与方式，我们将收集大家填写的智能体id，T+3个工作日内为大家开通白名单，优先星座命理、旅游出行、生活娱乐行业。',
        rule: [
            '智能体质量：对话内容质量优、流畅性好、角色设定明确，可以满足能力范围内绝大多数要求',
            '智能体分发量：智能体在搜索场景、站外分享等渠道对话次数',
            '智能体转化量：分润额度等关键经营数据指标的达成情况',
        ],
    },
};

export const WELCOME_TITLE = {
    [COMPETITION_TYPE.grass]: '欢迎报名智能体赛道，您正在报名的是「商品种草转化」方向，具体组件使用请查看选题详情',
    [COMPETITION_TYPE.service]:
        '欢迎报名智能体赛道，您正在报名的是「企业服务表单」方向，本赛道进面向企业认证的智能体开放，具体组件使用请查看选题详情',
    [COMPETITION_TYPE.reward]:
        '欢迎报名智能体赛道，您正在报名的是「服务咨询打赏」方向，本方向需要你是入驻百度问一问的答主，才可参与（非问一问答主无法使用服务咨询组件），具体组件使用请查看选题详情',
    [COMPETITION_TYPE.advertising]:
        '欢迎报名智能体赛道，您正在报名的是「联盟广告变现」方向，具体组件使用请查看选题详情',
};
