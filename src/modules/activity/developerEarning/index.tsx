/**
 * @file 「开发者赚钱季」赛事中心
 * <AUTHOR>
 */
import {useCallback, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import api from '@/api/activity/index';
import {ActivityType} from '@/api/activity/interface';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {sendLogV2, SERVER_ID} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_TYPE} from '@/utils/loggerV2/ubcLoggerV2';
import {useIsMobileStore} from '@/store/home/<USER>';
import ActivityTitle from './components/ActivityTitle';
import BlockContainer from './components/BlockContainer';
import UserAvatar from './components/center/UserAvatar';
import AwardsCard from './components/center/AwardsCard';
import ScheduleCard from './components/center/ScheduleCard';
import centerBg from './assets/center-bg.jpg';
import SubjectCard from './components/SubjectCard';
import {CLASS_CONFIG, SUBJECT_CONFIG, TECH_AWARD_CONFIG} from './constants';
import normalQrCode from './assets/qrcode/normal.png';
import serviceQrCode from './assets/qrcode/service.png';
import {QrCode} from './components/QrCode';

const CustomDiv = styled.div`
    -webkit-tap-highlight-color: transparent;
`;

const ActivityCenter = () => {
    const [competitionList, setCompetitionList] = useState<
        Array<{
            id: number;
            competitionName: string;
        }>
    >([]);
    // 请求赛题列表
    const getCompetitionList = useCallback(async () => {
        const res = (await api.getCompetitionList({competitionType: ActivityType.DeveloperEanring})) || [];
        setCompetitionList(res);
    }, []);
    const isMobile = useIsMobileStore(store => store.isMobile);

    useEffect(() => {
        getCompetitionList();
    }, [getCompetitionList]);

    // 页面展现点位记录
    useEffect(() => {
        sendLogV2(EVENT_TYPE.DISPLAY, '', EVENT_PAGE_CONST.BUSINESS_LEAD_HOME, {}, SERVER_ID.Activity);
    }, []);

    return (
        <CustomDiv className="w-full bg-[#e5e9fd]">
            <div className="flex w-full flex-col items-center justify-center">
                {/* 第一屏 */}
                <div className="overflow-clip">
                    {/* 登录 & 用户信息 相关 */}
                    <UserAvatar />
                    {/* 第一屏背景 - 赛事海报 */}
                    <img className="w-full mlg:min-w-[1000px]" src={centerBg} />
                </div>

                {/* 大赛介绍 */}
                <BlockContainer>
                    <ActivityTitle>活动介绍</ActivityTitle>
                    <div className="mt-4 text-[#2c44d3] mlg:mt-[30px] mlg:w-[1000px]">
                        <p className="text-justify text-xs leading-[24px] mlg:text-[20px] mlg:leading-[38px]">
                            随着智能体的持续发展，商业变现成为每个智能体在这一阶段的重要挑战。文心智能体平台积极构建商业闭环能力，致力于打造一个“可流通、可变现、可盈利”的智能体应用生态。
                            本次商领计划旨在通过商业化专项训练营和丰富的权益，帮助大家通过智能体实现盈利。训练营将为个人、企业和创作者等群体提供多种组件能力培训，诚邀开发者们积极参与！
                        </p>
                    </div>
                </BlockContainer>

                {/* 赛题列表 */}
                <BlockContainer>
                    <ActivityTitle>商业化应用方向</ActivityTitle>
                    {/* 赛题卡片 */}
                    <div className="mt-5 flex flex-wrap items-center justify-center gap-2 mlg:mt-10 mlg:w-[1000px] mlg:gap-[14px]">
                        {competitionList.map(item => (
                            <div key={item.id}>
                                <SubjectCard
                                    key={item.id}
                                    competitionType={item.competitionName}
                                    competitionId={item.id}
                                />
                            </div>
                        ))}
                    </div>
                </BlockContainer>

                {/* 奖项区域 */}
                <BlockContainer>
                    <ActivityTitle>活动奖励</ActivityTitle>
                    {/* 奖项卡片 */}
                    <div className="mt-5 flex flex-row flex-wrap items-center justify-center gap-x-2 gap-y-3 mlg:mt-10 mlg:w-[1000px] mlg:gap-x-4 mlg:gap-y-[25px]">
                        {TECH_AWARD_CONFIG.map(item => (
                            <AwardsCard key={item.id} info={item} isTech className="basis-auto mlg:w-[238px]" />
                        ))}
                    </div>
                </BlockContainer>

                {/* 赛程 */}
                <BlockContainer noneBackground>
                    <ActivityTitle>活动流程</ActivityTitle>
                    {/* 赛程卡片 */}
                    <div className="mt-5 columns-1 gap-10  mlg:mt-8 ">
                        <div className="flex flex-row flex-wrap items-center justify-center gap-x-2 gap-y-3  mlg:w-[1000px] mlg:columns-4 mlg:gap-x-4 ">
                            {SUBJECT_CONFIG.map(item => (
                                <ScheduleCard key={item.id} info={item.img} />
                            ))}
                        </div>
                    </div>
                </BlockContainer>

                {/* 赛程 */}
                <BlockContainer noneBackground>
                    <ActivityTitle>教育课程预告</ActivityTitle>
                    {/* 赛程卡片 */}
                    <div className="mt-5 columns-1 gap-10  mlg:mt-8 ">
                        <div className=" flex flex-row flex-wrap items-center justify-center gap-x-2 gap-y-3 mlg:w-[1000px] mlg:columns-4 mlg:gap-x-4 ">
                            {CLASS_CONFIG.map(item => (
                                <ScheduleCard key={item.id} info={item.img} />
                            ))}
                        </div>
                    </div>
                </BlockContainer>

                {/* 活动交流群 */}
                <BlockContainer>
                    <ActivityTitle>活动交流群</ActivityTitle>
                    {isMobile && <div className="mt-[10px] text-[#2c44d3]">手机端打开请保存二维码，微信扫码进群</div>}
                    <div className="mt-5 flex justify-center gap-5 text-[#2c44d3] mlg:gap-10">
                        <div className="rounded-3xl bg-white p-3 text-center">
                            <p className="text-sm font-medium mlg:text-lg">商品&表单方向训练营交流群</p>
                            <QrCode url={normalQrCode} />
                        </div>
                        <div className="rounded-3xl bg-white p-3 text-center">
                            <p className="text-sm font-medium mlg:text-lg">咨询&广告方向训练营交流群</p>
                            <QrCode url={serviceQrCode} />
                        </div>
                    </div>
                </BlockContainer>
            </div>
        </CustomDiv>
    );
};

export default ActivityCenter;
