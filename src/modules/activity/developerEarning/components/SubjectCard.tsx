/**
 * @file 赛事简介卡片
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import classNames from 'classnames';
import urls from '@/links';
import ActivityButton from '@/modules/activity/developerEarning/components/ActivityButton';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import api from '@/api/activity/index';
import {QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import {CompetitionTypeBgMap} from '../constants';

interface SubjectCardProps {
    className?: string;
    competitionType?: string;
    competitionId?: number;
}

const SubjectCard: React.FC<SubjectCardProps> = ({className, competitionType, competitionId}) => {
    const isLogin = useUserInfoStore(store => store.isLogin);
    // 该赛事是否报名
    const [hasApplied, setHasApplied] = useState<boolean>(false);

    const getDeveloperApplyStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                });
                setHasApplied(res.submitStatus === SubmitStatus.Submitted);
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId]);

    // 请求问卷状态
    useEffect(() => {
        if (isLogin) {
            getDeveloperApplyStatus();
        }
    }, [isLogin, getDeveloperApplyStatus]);

    // 打开赛事详情页
    const openActivityDetail = useCallback(() => {
        window.open(`${urls.activityDpDetail.raw()}?type=${competitionType}&id=${competitionId}`);
    }, [competitionType, competitionId]);

    // 调登录
    const uniformLogin = useUniformLogin();

    // 打开报名表单页面
    const handleClickSignUp = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            event.stopPropagation(); // 阻止事件冒泡到外层容器
            if (isLogin) {
                window.open(`${urls.activityDpDeveloperApply.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    const handleClickSubmitWork = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activityDpSubmitWork.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    return (
        // 智能体应用赛题 赛题卡片
        <div
            style={{backgroundImage: `url(${CompetitionTypeBgMap[competitionType!]})`}}
            className={classNames(
                'relative h-[22.6vw] w-[42vw] cursor-pointer rounded-md bg-cover bg-center bg-no-repeat mlg:h-[267px] mlg:w-[493px]',
                className
            )}
            onClick={openActivityDetail}
        >
            {/* 底部按钮区域 */}
            <footer className="absolute bottom-[10px] z-10 flex w-full items-end justify-center gap-3 px-[20px] mlg:bottom-10 ">
                {hasApplied ? (
                    <ActivityButton onClick={handleClickSubmitWork}>提交作品</ActivityButton>
                ) : (
                    <ActivityButton onClick={handleClickSignUp}>立即报名</ActivityButton>
                )}
            </footer>
        </div>
    );
};

export default SubjectCard;
