/**
 * @file 统一封装 活动按钮
 * <AUTHOR>
 */

import React from 'react';
import {Button} from 'antd';
import {ButtonProps} from 'antd/lib';
import styled from '@emotion/styled';
import classNames from 'classnames';
import buttonBg from '@/modules/activity/developerEarning/assets/button-bg.png';

const CustomButton = styled(Button)`
    background-image: url(${buttonBg}) !important;
    background-color: transparent !important;
    &:disabled {
        background-image: none !important;
        color: #f0f1f5 !important;
    }
`;

enum ButtonSize {
    Small = 'small',
    Middle = 'middle',
    Large = 'large',
}

interface ActivityButtonProps extends ButtonProps {
    size?: 'small' | 'middle' | 'large';
    className?: string;
}

/**
 * 活动按钮组件
 *
 * @param size 按钮大小，可选值为：'Large'、'Middle'、'Small'，默认为 'Small'
 * @param className 自定义样式类名
 * @param props 按钮属性
 * @returns 返回 React 组件
 */

const ActivityButton: React.FC<ActivityButtonProps> = ({size = ButtonSize.Small, className, ...props}) => {
    return (
        <CustomButton
            {...props}
            type="primary"
            className={classNames(
                'rounded-full border-none bg-cover font-semibold text-white',
                {
                    'h-[45px] w-[168px] text-lg mlg:h-[74px] mlg:w-[261px] mlg:text-[24px]': size === ButtonSize.Large,
                    'h-[22px] w-[80px] text-[8px] leading-[8px] mlg:h-[55px] mlg:w-[200px] mlg:text-[28px] mlg:leading-[14px]':
                        size === ButtonSize.Small,
                },
                className
            )}
        />
    );
};

export default ActivityButton;
