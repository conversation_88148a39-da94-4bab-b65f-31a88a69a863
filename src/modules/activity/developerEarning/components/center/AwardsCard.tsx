/**
 * @file 官网首页-奖项设置卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';

const CustomDiv = styled.div<{awardsCardBg: string}>`
    background-image: url(${props => props.awardsCardBg}) !important;
`;

interface AwardsCardProps {
    className?: string;
    isLong?: boolean;
    isTech?: boolean;
    info: any;
}

const AwardsCard: React.FC<AwardsCardProps> = ({className, info}) => {
    return (
        <CustomDiv
            awardsCardBg={info.img}
            className={classNames(
                'relative flex h-[18.14vw] w-[19.71vw] flex-col items-center rounded bg-cover bg-no-repeat mlg:h-[219px] mlg:w-[238px] mlg:rounded-xl',
                className
            )}
        />
    );
};

export default AwardsCard;
