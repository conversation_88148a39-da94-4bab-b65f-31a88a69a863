/**
 * @file 首页-赛程卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';

const CustomDiv = styled.div<{imgUrl: string}>`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${props => props.imgUrl}) !important;
`;

interface ScheduleCardProps {
    className?: string;
    info: any;
}

const ScheduleCard: React.FC<ScheduleCardProps> = ({className, info}) => {
    return (
        <CustomDiv
            imgUrl={info}
            className={classNames(
                'relative flex h-[120px] w-[140px] flex-col items-center overflow-visible rounded-xl bg-contain bg-center bg-no-repeat mlg:h-[179px] mlg:w-[230px]',
                className
            )}
        />
    );
};

export default ScheduleCard;
