/**
 * @file 区块容器
 * <AUTHOR>
 */

import React from 'react';
import classNames from 'classnames';
interface BlockContainerProps {
    children: React.ReactNode;
    className?: string;
    noneBackground?: boolean;
    isBack?: boolean;
}

const BlockContainer: React.FC<BlockContainerProps> = ({children, className, noneBackground = false}) => {
    return (
        <div
            className={classNames(
                'flex w-full flex-col items-center bg-[#e5e9fd] bg-contain bg-no-repeat px-6 py-5 mlg:min-w-[1000px] mlg:p-0 mlg:py-[30px]',
                {
                    'bg-none': noneBackground,
                },
                className
            )}
        >
            {children}
        </div>
    );
};

export default BlockContainer;
