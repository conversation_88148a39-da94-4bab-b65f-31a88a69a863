import classNames from 'classnames';
import {useIsMobileStore} from '@/store/home/<USER>';
import rightBg from '@/modules/activity/developerEarning/assets/left.png';
import leftBg from '@/modules/activity/developerEarning/assets/right.png';
import ActivityTitle from './ActivityTitle';
import BlockContainer from './BlockContainer';
interface Props {
    info: Info;
}

interface Info {
    intruction: string;
    instructionRuls?: string[];
    time: string;
    rule: string[];
}

const Detail = ({info}: Props) => {
    const {intruction, instructionRuls, time, rule} = info;
    const isMobile = useIsMobileStore(store => store.isMobile);

    return (
        <div className="relative">
            <BlockContainer isBack>
                {/* 赛题介绍标题 */}
                <ActivityTitle>方向介绍</ActivityTitle>

                {/* 赛题描述 */}
                <div
                    className=" flex flex-col gap-5 pt-5 text-sm leading-7 mlg:mx-0 mlg:w-[1000px] mlg:pt-8 mlg:text-[20px] mlg:leading-[38px]"
                    id="content"
                >
                    <p className="text-center text-[#2c44d3]">{intruction}</p>
                    {instructionRuls &&
                        instructionRuls.map(item => (
                            <li className=" text-[#2c44d3]" key={item}>
                                {item}
                            </li>
                        ))}
                </div>
            </BlockContainer>
            <BlockContainer>
                {/* 赛题介绍标题 */}
                <ActivityTitle>开放时间</ActivityTitle>

                {/* 赛题描述 */}
                <div
                    className=" flex flex-col gap-5 pt-5 text-sm leading-7 mlg:mx-0 mlg:w-[1000px] mlg:pt-8 mlg:text-[20px] mlg:leading-[38px]"
                    id="content"
                >
                    <p className="text-center text-[#2c44d3]"> {time}</p>
                </div>
            </BlockContainer>

            {/* 评审规则区域 */}
            <BlockContainer>
                {/* 评审规则标题 */}
                <ActivityTitle>评审规则</ActivityTitle>
                {/* 评审规则内容 */}
                <div
                    className=" flex flex-col gap-5 pt-5 text-sm leading-7 text-[#2c44d3] mlg:mx-0 mlg:w-[1000px] mlg:pt-8 mlg:text-[20px]  mlg:leading-[38px]"
                    id="content"
                >
                    按照以下纬度综合评审:
                    {rule.map(item => (
                        <li className="ml-3" key={item}>
                            {item}
                        </li>
                    ))}
                </div>
            </BlockContainer>
            <img
                className={classNames('absolute right-0 top-0', {
                    'w-[150px]': !isMobile,
                    'w-[80px]': isMobile,
                })}
                src={leftBg}
            ></img>
            <img
                className={classNames('absolute left-0 top-0', {
                    'w-[150px]': !isMobile,
                    'w-[80px]': isMobile,
                })}
                src={rightBg}
            ></img>
        </div>
    );
};
export default Detail;
