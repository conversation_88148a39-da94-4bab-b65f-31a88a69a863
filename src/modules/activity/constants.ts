/**
 * @file 常量
 * <AUTHOR>
 */
import {css} from '@emotion/css';
import creativityCardBg from '@/modules/activity/assets/creativity-subject-card-bg.png';
import expertCardBg from '@/modules/activity/assets/expert-subject-card-bg.png';
import gameCardBg from '@/modules/activity/assets/game-subject-card-bg.png';
import aiCardBg from '@/modules/activity/assets/ai-subject-card-bg.png';
// 详情页banner图
import creativityBg from '@/modules/activity/assets/creativity-bg.jpg';
import expertBg from '@/modules/activity/assets/expert-bg.jpg';
import gameBg from '@/modules/activity/assets/game-bg.jpg';
import aiBg from '@/modules/activity/assets/ai-bg.jpg';
// 二维码图片
import gameQrCode from '@/modules/activity/assets/qrCode/game.png';
import aiQrCode from '@/modules/activity/assets/qrCode/ai.png';
import creativityQrCode from '@/modules/activity/assets/qrCode/creativity.png';
import expertQrCode from '@/modules/activity/assets/qrCode/expert.jpg';
// logo
import logo1 from '@/modules/activity/assets/logo/1-1.jpg';
import logo2 from '@/modules/activity/assets/logo/2-1.jpg';
import logo31 from '@/modules/activity/assets/logo/3-1.jpg';
import logo32 from '@/modules/activity/assets/logo/3-2.jpg';
import logo41 from '@/modules/activity/assets/logo/4-1.jpg';
import logo42 from '@/modules/activity/assets/logo/4-2.jpg';
import logo51 from '@/modules/activity/assets/logo/5-1.jpg';
import logo52 from '@/modules/activity/assets/logo/5-2.jpg';
import logo53 from '@/modules/activity/assets/logo/5-3.jpg';
import logo54 from '@/modules/activity/assets/logo/5-4.jpg';
import logo55 from '@/modules/activity/assets/logo/5-5.jpg';
import logo56 from '@/modules/activity/assets/logo/5-6.jpg';
import logo57 from '@/modules/activity/assets/logo/5-7.jpg';
import logo58 from '@/modules/activity/assets/logo/5-8.jpg';

// 专家嘉宾
import expert1 from '@/modules/activity/assets/expert/1.png';
import expert2 from '@/modules/activity/assets/expert/2.png';
import expert3 from '@/modules/activity/assets/expert/3.png';
import expert4 from '@/modules/activity/assets/expert/4.jpg';
import expert5 from '@/modules/activity/assets/expert/5.png';
import expert6 from '@/modules/activity/assets/expert/6.jpg';
import expert7 from '@/modules/activity/assets/expert/7.png';
import expert8 from '@/modules/activity/assets/expert/8.png';
import expert9 from '@/modules/activity/assets/expert/9.png';
import expert10 from '@/modules/activity/assets/expert/10.png';
import expert11 from '@/modules/activity/assets/expert/11.jpg';
import expert12 from '@/modules/activity/assets/expert/12.jpg';
import expert13 from '@/modules/activity/assets/expert/13.jpg';
import expert14 from '@/modules/activity/assets/expert/14.png';
import expert15 from '@/modules/activity/assets/expert/15.png';
import expert16 from '@/modules/activity/assets/expert/16.png';
import expert17 from '@/modules/activity/assets/expert/17.png';
import expert18 from '@/modules/activity/assets/expert/18.png';
import expert19 from '@/modules/activity/assets/expert/19.png';
import expert20 from '@/modules/activity/assets/expert/20.jpg';
import expert21 from '@/modules/activity/assets/expert/21.jpg';
import expert22 from '@/modules/activity/assets/expert/22.png';
import expert23 from '@/modules/activity/assets/expert/23.png';
import expert24 from '@/modules/activity/assets/expert/24.png';

// 课程
import class1 from '@/modules/activity/assets/class/1.png';
import class2 from '@/modules/activity/assets/class/2.png';
import class3 from '@/modules/activity/assets/class/3.png';
import class4 from '@/modules/activity/assets/class/4.png';
import class5 from '@/modules/activity/assets/class/5.png';
import class6 from '@/modules/activity/assets/class/6.png';
import class7 from '@/modules/activity/assets/class/7.png';
import class8 from '@/modules/activity/assets/class/8.png';
import class9 from '@/modules/activity/assets/class/9.png';

import {CompetitionTypeLogExt} from '@/utils/loggerV2/interface';
import {InfoType} from '@/api/activity/interface';

// 搜索技术创新大赛介绍
export const COMPLETION_INTRO =
    '“百度搜索创新大赛”是自2022年起由百度搜索发起，联合全国各大高校、技术社区、科研组织共同举办的一项全国性科技竞赛。赛事围绕人工智能、搜索等技术赛题面向全国企业、高校、科研机构、项目团队及个人广发英雄帖，着力挖掘和培育人工智能人才，促进工作交流、产教融合，并推动产品、算法和技术应用创新。2022年、2023年从搜索场景中的高频需求出发，设置包括“搜索智能问答、搜索模型推理优化”在内的技术、产品类赛题。2024年赛事方向为聚焦“AI大模型”与“智能体应用”，以探索AI时代最有应用价值的智能体为出发点，与开发者群体一同打造真正“有分发、有消费价值、有钱赚”的智能体应用。';

// 赛程配置
export const SUBJECT_CONFIG = [
    {
        step: '报名',
        date: '8月29日～10月06日',
        desc: '活动正式发布，赛题、评选规则公示，赛事报名正式开启',
    },
    {
        step: '初赛',
        date: '8月29日～10月07日',
        desc: '系列直播与视频课，5场线下沙龙，按初赛要求提交作品',
    },
    {
        step: '决赛',
        date: '10月18日～10月25日',
        desc: '入围选手通知，按决赛要求提交参赛作品',
    },
    {
        step: '颁奖',
        date: '11月中旬',
        desc: '获奖选手现场奖项授予',
    },
];

// 智能体奖项配置
export const AGENT_AWARD_CONFIG = [
    {
        name: '智能体总冠军',
        num: 1,
        reward: '40,000',
    },
    {
        name: '一等奖',
        num: 1,
        reward: '30,000',
    },
    {
        name: '二等奖',
        num: 2,
        reward: '10,000',
    },
    {
        name: '三等奖',
        num: 3,
        reward: '5,000',
    },
    {
        name: '优胜奖',
        num: 10,
        reward: '1000',
    },
    {
        name: '智能体人气奖',
        num: 50,
        reward: '限量周边',
    },
];

// 技术奖项配置
export const TECH_AWARD_CONFIG = [
    {
        name: '一等奖',
        num: 1,
        reward: '80,000',
    },
    {
        name: '二等奖',
        num: 1,
        reward: '30,000',
    },
    {
        name: '三等奖',
        num: 1,
        reward: '10,000',
    },
    {
        name: '优胜奖',
        num: 7,
        reward: '1000',
    },
];

// 组委会配置
export const COMMITTEE_CONFIG = [
    {
        image: expert1,
        name: '赵世奇',
        title: '百度副总裁\n百度搜索总经理',
    },
    {
        image: expert2,
        name: '何涛',
        title: 'NVIDIA全球副总裁',
    },
    {
        image: expert3,
        name: '杨明璐',
        title: '百度搜索产品&AI生态平台总经理',
    },
    {
        image: expert4,
        name: '殷大伟',
        title: '百度搜索策略高级总监',
    },
];
// 专家评审配置
export const GUEST_CONFIG = [
    {
        image: expert5,
        name: '李炯明',
        title: 'CSDN高级副总裁',
    },
    {
        image: expert6,
        name: '李建忠',
        title: 'CSDN高级副总裁',
    },
    {
        image: expert7,
        name: '胡毅',
        title: '51CTO联合创始人\n首席战略官',
    },
    {
        image: expert8,
        name: '刘川',
        title: 'NVIDIA解决方案架构总监',
    },
    {
        image: expert9,
        name: '张雷',
        title: '混沌学园合伙人\n混沌在线业务负责人',
    },
    {
        image: expert10,
        name: '辜斯缪',
        title: '百度搜索策略首席架构师',
    },
    {
        image: expert11,
        name: '吴永巍',
        title: '搜索架构首席研发架构师\nMEG技术委员会联席主席',
    },
    {
        image: expert12,
        name: '杨文博',
        title: '百度搜索平台杰出架构师',
    },
    {
        image: expert13,
        name: '范彪',
        title: '搜索技术平台研发部总监',
    },
    {
        image: expert14,
        name: '彭先生',
        title: '百度文心智能体平台\n策略负责人',
    },
    {
        image: expert15,
        name: '卜英',
        title: '印象笔记高级副总裁',
    },
    {
        image: expert16,
        name: '李根',
        title: '量子位总编辑',
    },
    {
        image: expert17,
        name: '陈思',
        title: '猎聘品牌公关总监',
    },
    {
        image: expert18,
        name: '张子豪',
        title: '中国图象图形学学会\n科普与教育工作委员会委员',
    },
    {
        image: expert19,
        name: '司玉鑫',
        title: 'Datawhale开发者生态\n负责人',
    },
    {
        image: expert20,
        name: '容薇',
        title: '百度百科负责人\n百度搜索品牌运营负责人',
    },
    {
        image: expert21,
        name: '颜先生',
        title: '百度搜索资深工程师',
    },
    {
        image: expert22,
        name: '杨烨',
        title: '百度文心智能体平台',
    },
    {
        image: expert23,
        name: '张妍',
        title: '百度文心智能体平台',
    },
    {
        image: expert24,
        name: '魏耀祖',
        title: '百度文心智能体平台',
    },
];

// 赛事课程配置
export const CLASS_CONFIG = [
    {
        image: class1,
        url: ' https://mbd.baidu.com/newspage/data/videolanding?nid=sv_9108381006012357369',
        describe: '「智能体基础开发课程」之0-1开发创建开发教程',
    },
    {
        image: class2,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_1321148759025884378',
        describe: '「智能体基础开发课程」之人物设定写作技巧',
    },
    {
        image: class3,
        url: ' https://mbd.baidu.com/newspage/data/videolanding?nid=sv_8813213540215540497',
        describe: '「智能体调优系列课程」之人物设定优化',
    },
    {
        image: class4,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_10624184041054723579',
        describe: '「智能体进阶开发课程」之 模型配置',
    },
    {
        image: class5,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_1179575692409308735',
        describe: '「智能体进阶开发课程」之 知识库的设置和调用 ',
    },
    {
        image: class6,
        url: ' https://mbd.baidu.com/newspage/data/videolanding?nid=sv_1438773151170680098',
        describe: '「智能体进阶开发课程」之插件调用',
    },
    {
        image: class7,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_7413115564504290916',
        describe: '「智能体进阶开发课程」之 数据库的配置和调用',
    },
    {
        image: class8,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_12466591685587317230',
        describe: '「智能体进阶开发课程」之 工作流基础概念',
    },
    {
        image: class9,
        url: 'https://mbd.baidu.com/newspage/data/videolanding?nid=sv_10156475982337764216',
        describe: '「智能体进阶开发课程」之 工作流基础编排和调用指南',
    },
];

// 合作伙伴配置
export const PARTNERS_CONFIG = [
    {
        category: '大赛主办方',
        image: [logo1],
    },
    {
        category: '大赛技术合作伙伴',
        image: [logo2],
    },
    {
        category: '大赛承办',
        image: [logo31, logo32],
    },
    {
        category: '跨界合作平台',
        image: [logo41, logo42],
    },
    {
        category: '合作社区/媒体',
        image: [logo51, logo52, logo53, logo54, logo55, logo56, logo57, logo58],
    },
];

// 表单项样式
export const formItemStyle = css`
    .ant-form-item-label .ant-form-item-required::before {
        display: none !important;
    }
    .ant-form-item-label .ant-form-item-required::after {
        display: inline-block !important;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 1;
        visibility: visible !important;
        top: 3px;
        content: '*' !important;
    }
    .ant-form-item-label label {
        font-weight: 500;
    }
    .ant-col-12 {
        max-width: 80% !important;
    }
    .ant-input-disabled.ant-input[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
    .ant-picker .ant-picker-input > input[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
    .ant-input-affix-wrapper-disabled,
    .ant-input-affix-wrapper[disabled] {
        color: #aaa !important;
        background: #f7f7f7 !important;
    }
`;

// 赛题类型配置
export const COMPETITION_TYPE = {
    game: '生活娱乐大师',
    ai: 'AI商业MVP',
    expert: '垂直领域专家',
    creativity: 'OPEN创意',
};

// 不同类型比赛的背景kv配置
export const CompetitionTypeBgMap: Record<string, string> = {
    [COMPETITION_TYPE.game]: gameCardBg,
    [COMPETITION_TYPE.ai]: aiCardBg,
    [COMPETITION_TYPE.expert]: expertCardBg,
    [COMPETITION_TYPE.creativity]: creativityCardBg,
};

// 不同类型比赛的详情页背景kv配置
export const CompetitionDetailsBgMap: Record<string, string> = {
    [COMPETITION_TYPE.game]: gameBg,
    [COMPETITION_TYPE.ai]: aiBg,
    [COMPETITION_TYPE.expert]: expertBg,
    [COMPETITION_TYPE.creativity]: creativityBg,
};

// 不同类型比赛的群二维码配置
export const CompetitionTypeQrCodeMap: Record<string, any> = {
    [COMPETITION_TYPE.game]: gameQrCode,
    [COMPETITION_TYPE.ai]: aiQrCode,
    [COMPETITION_TYPE.expert]: expertQrCode,
    [COMPETITION_TYPE.creativity]: creativityQrCode,
};

export const PP_PAGE_URL = 'https://aistudio.baidu.com/competition/detail/1235/0/introduction';

// 不同类型比赛的的点位ext值
export const CompetitionTypeLogExtMap: Record<string, CompetitionTypeLogExt> = {
    [COMPETITION_TYPE.game]: CompetitionTypeLogExt.GAME,
    [COMPETITION_TYPE.ai]: CompetitionTypeLogExt.AI,
    [COMPETITION_TYPE.expert]: CompetitionTypeLogExt.EXPERT,
    [COMPETITION_TYPE.creativity]: CompetitionTypeLogExt.CREATIVITY,
};

// 报名信息来源配置
export const INFO_SOURCES = [
    {
        value: InfoType.Agent,
        label: '文心智能体官方平台',
    },
    {
        value: InfoType.CSDN,
        label: 'CSDN',
    },
    {
        value: InfoType.CTO,
        label: '51CTO',
    },
    {
        value: InfoType.Jixing,
        label: '极星会',
    },
    {
        value: InfoType.Renren,
        label: '人人都是产品经理',
    },
    {
        value: InfoType.Other,
        label: '其他',
    },
];

// aimvp赛道决赛名单
const AI_MVP_LIST = [
    {
        agentName: '小省导购员',
        agentId: 'Ssas9lYEUE8rEtCBKmGr4TyQaY0LxhVE',
        preliminaryScore: 86,
    },
    {
        agentName: '李善友',
        agentId: 'sgzYPPzRZoy9JXKK4yMKcXL3xgOqDmit',
        preliminaryScore: 86,
    },
    {
        agentName: '雅思学姐Danny',
        agentId: 'NqBmEH3MdlU4LARydY5YhoNeP6IcWNsh',
        preliminaryScore: 85,
    },
    {
        agentName: '39AI医生',
        agentId: 'TTHCeCg4PDmljGFmFBPDP8AUJap1bD2e',
        preliminaryScore: 74.5,
    },
    {
        agentName: '发型设计器',
        agentId: 'OkrmmW561GAgjRbr3hoQNSMoqrb4Lp1D',
        preliminaryScore: 58,
    },
    {
        agentName: '公司上市小助手',
        agentId: 'f6IORALbmkbpNuajNkp5tiyXndXeXHx5',
        preliminaryScore: 57.5,
    },
    {
        agentName: '购物比价',
        agentId: 'K8yfgboOCvosR4avRgWeuV6ss4z4qW3f',
        preliminaryScore: 56.5,
    },
    {
        agentName: '数字化营销转型专家',
        agentId: 'UKb4PzUHkYAPhVXxY6c7oC3wA9UzQkJB',
        preliminaryScore: 56,
    },
    {
        agentName: '商务智谋',
        agentId: 'GnebuJ77TXSkBXUeHhJGy4BTiKeK4NtA',
        preliminaryScore: 55.25,
    },
    {
        agentName: '生活回答助手',
        agentId: 'NOMQgNdhSRaOnGQGukhuNPhGxykbMXu6',
        preliminaryScore: 55,
    },
    {
        agentName: '课后作业辅导助手',
        agentId: '3a0ife91wOM2ANW1HwEOeImd83pkeHbW',
        preliminaryScore: 54.75,
    },
    {
        agentName: '超智能的化妆品导购',
        agentId: 'PpaguLDzKPT12N98EuXQcmrM0eyCPGxW',
        preliminaryScore: 65,
    },
    {
        agentName: '大学生考研咨询',
        agentId: 'qINy7yvYVjG6zWXe7wPg0pchW0usiq41',
        preliminaryScore: 55,
    },
    {
        agentName: '收纳小助手',
        agentId: 'FluFJXYCRv6Z0Vrn0aVR7M0nETCN2MG2',
        preliminaryScore: 53,
    },
    {
        agentName: '公考面试助手',
        agentId: 'DDaayrdz3lgI9VqR12cq2DVTnKsAvvt5',
        preliminaryScore: 51,
    },
    {
        agentName: '采购比价智能计算器',
        agentId: 'o3xlBSuFMXRwH5SNIRfG2zRBuEsTVtge',
        preliminaryScore: 90,
    },
];

// 创意赛道决赛名单
const CREATIVITY_LIST = [
    {
        agentName: '绿色生活管家',
        agentId: 'cYMTwVSbNzlTV1i2O1nNnat3AAfa79Za',
        preliminaryScore: 86.25,
    },
    {
        agentName: '拯救拖延症',
        agentId: 'gwUKsZ2IZYRHcv09hUtO5woVqv1WhynK',
        preliminaryScore: 74,
    },
    {
        agentName: '情侣网名设计大师',
        agentId: 'YjHkTU9ULDuAiS6fAAdZEdauZov0bUOC',
        preliminaryScore: 71.5,
    },
    {
        agentName: '校园霸凌预防检测',
        agentId: 'kOyYl0T6FkVJrcnwLIhqlLxQmwf4XLPY',
        preliminaryScore: 68,
    },
    {
        agentName: '网盘搜索',
        agentId: 'AiwhJqBglpNLAw8fMgm9LXWEbuAFB3Z0',
        preliminaryScore: 67,
    },
    {
        agentName: '国庆头像',
        agentId: 'TJPaDE35mCB9lSQW9oGH62KOYKdsMG39',
        preliminaryScore: 67,
    },
    {
        agentName: '中国海关博物馆讲解员',
        agentId: 'OoyJGUID4ej1JXnSEIzXU0kNjnRRVkhM',
        preliminaryScore: 63.75,
    },
    {
        agentName: '林黛玉林怼怼',
        agentId: '2ssLUSmW7zduiob83lBYL9yJbjVMlDfN',
        preliminaryScore: 59.25,
    },
    {
        agentName: '城市印象之漫行北京',
        agentId: 'YvfHJUJ4OnHTuLaVNjTXpAgEDw1gzKME',
        preliminaryScore: 59.25,
    },
    {
        agentName: '老北京陪你逛北京',
        agentId: 'g8FjcbS7UFA62gBrXvDad7eIMXe3LyF0',
        preliminaryScore: 58.75,
    },
    {
        agentName: '忆守者',
        agentId: 'ibtysDQq8XpCMz1iziKZXLWPzZqrbEJa',
        preliminaryScore: 58,
    },
    {
        agentName: '躺平之城推荐官',
        agentId: '04HOPSY3WXIzsFHbm6M2IGBMcNxWftrm',
        preliminaryScore: 57.25,
    },
    {
        agentName: '手机性能对比助手',
        agentId: 'pCERiQAJ7UbqX91KQfRt8ndoDUvJOm70',
        preliminaryScore: 57,
    },
    {
        agentName: 'IT认证超级讲师',
        agentId: 'vhSha2WRtnTKWn6ZjT2xDTWjrB87AUlg',
        preliminaryScore: 56.5,
    },
    {
        agentName: '00后急需的100个生存技能卡片',
        agentId: 'a53OM96haMoRMFaoawezBAAN1SvN9GjP',
        preliminaryScore: 55,
    },
    {
        agentName: '青春期能量小助手',
        agentId: 'cfaV7bx5UYpYcEltQpe29x5YtiOfI8Dj',
        preliminaryScore: 55,
    },
];

// 生活娱乐赛道决赛名单
const ENTERTAINMENT_LIST = [
    {
        agentName: '橘子分享初中课本',
        agentId: 'e6vDYxMUrdATAU47VjY40viU4bQHSZ26',
        preliminaryScore: 88,
    },
    {
        agentName: '超级奶爸训练营',
        agentId: 'xxnUc9WpZ78lgDTwEXilZ4DGJPR4VFka',
        preliminaryScore: 87,
    },
    {
        agentName: '头像大师',
        agentId: 'cx5TXWpt9tLy02Wn6yE4ZAZ5q34PjTCc',
        preliminaryScore: 85.5,
    },
    {
        agentName: '颜值测试',
        agentId: 'hAvWuFkQaJE6MgirpXZibbSC4IYoDWM5',
        preliminaryScore: 84.5,
    },
    {
        agentName: '多多教剪辑',
        agentId: 'ACzvDeS6wOL8sbIciTOsecju8zqX5knz',
        preliminaryScore: 76.75,
    },
    {
        agentName: '刘备谈古论今',
        agentId: 'Il34pPhLQw7NlWGz3idKRQpMAirlNkUk',
        preliminaryScore: 65.5,
    },
    {
        agentName: '神奇的答案之书',
        agentId: 'QAfJi338grnjPDwHZdDCucUi7PNVR5Fl',
        preliminaryScore: 62.5,
    },
    {
        agentName: '汉服精灵',
        agentId: 'x33vBCrQUZDb18snawv1L9UX0LDZEZTg',
        preliminaryScore: 62.25,
    },
    {
        agentName: '法医的一天',
        agentId: '5sznRCF0NIlOMnq6bDtx0yg9gLryXE6I',
        preliminaryScore: 61.5,
    },
    {
        agentName: '模拟辩手',
        agentId: 'KnmXlKqSUH4l5Vaz44Cfg34YPEru7cej',
        preliminaryScore: 59.5,
    },
    {
        agentName: '乐趣激励大师',
        agentId: 'DhpcLZ14u7YHXSWxYYJv3oNBHpHaxWKJ',
        preliminaryScore: 58.5,
    },
    {
        agentName: '摄影智能体助手',
        agentId: '88G4F2LT0SoE5vW3mepAyItHlgXA0bcw',
        preliminaryScore: 57.75,
    },
    {
        agentName: '做你一天女儿',
        agentId: 'sMB61oJPv56rAVogDPcyxrWxi0UsT9fI',
        preliminaryScore: 57.5,
    },
    {
        agentName: '北岳智导精灵',
        agentId: 'UgVtoW6HcB6vbtrE0w5cSr2MJV9xowpH',
        preliminaryScore: 57,
    },
    {
        agentName: '进化吧原始人',
        agentId: 'hCSjQ8nmxLhMtx4QENyCeTGD9vteT9zq',
        preliminaryScore: 57,
    },
    {
        agentName: '每日热点播报',
        agentId: 'HX28DZfrJsw36doJdN6YN3TWfWg3gLPu',
        preliminaryScore: 55,
    },
];

// 专家赛道决赛名单
const EXPERT_LIST = [
    {
        agentName: '情感咨询若涵老师',
        agentId: 'WyMgEpUfhIR89mJNVUT9LmCbYLqs88nW',
        preliminaryScore: 90,
    },
    {
        agentName: '方志远讲明史',
        agentId: 'WIixEnpS6j8q3U5Rsfnl9Mua4pOyKtgE',
        preliminaryScore: 85,
    },
    {
        agentName: '儿科李丽嫱医生',
        agentId: 'txrGv4xh7gjqTYSfG8XWLDKfqGJCRSeG',
        preliminaryScore: 82,
    },
    {
        agentName: '大榜情感格格挽回专家',
        agentId: '3TyKPtOOF3XkrCWfqcrDRnlPCH4Hqq5q',
        preliminaryScore: 81,
    },
    {
        agentName: '儿科武守恭医生',
        agentId: 'pFCFkkzaEkedOQflo7WVSRZSMVDHY70q',
        preliminaryScore: 81,
    },
    {
        agentName: '胸外科乔贵宾医生',
        agentId: 'aQTQ4XivsPKBNDwb3158FZxDxDerkuLZ',
        preliminaryScore: 80,
    },
    {
        agentName: '祝东升医生',
        agentId: 'e97SZOl7dmONO3idphJIWyJpQusyJ3JT',
        preliminaryScore: 79,
    },
    {
        agentName: '李胜龙医生',
        agentId: 'w2nxLibF4FcEEAW1GoN97bvYJINdUC8h',
        preliminaryScore: 78,
    },
    {
        agentName: '余世存',
        agentId: 'y6kD41dao6HSte2MoXwVeNLChvo2LoFZ',
        preliminaryScore: 78,
    },
    {
        agentName: '王海洋医生',
        agentId: 'xU56M3NYYZAzW2mR1WqStFvUxqdDHxmn',
        preliminaryScore: 78,
    },
    {
        agentName: '肖进医生',
        agentId: '6v73czlmm9IcXG3z6nw5MZlCF6PMN7Yb',
        preliminaryScore: 77,
    },
    {
        agentName: '面部抗衰医生任冲',
        agentId: '9Msz9SLrPFwhiMiQn5SuDia60Q3Nnx8O',
        preliminaryScore: 77,
    },
    {
        agentName: '协和手外陈江海',
        agentId: '8xcgyY8mSbw7rcBB3k38YRvy4XLFzStw',
        preliminaryScore: 76,
    },
    {
        agentName: '全科医学科杨彬博士诊室',
        agentId: 'oWkbsoCrTLR7sQjh5FlPkJ3EfejAPFrp',
        preliminaryScore: 75,
    },
    {
        agentName: '钟永圣',
        agentId: 'omqCZHJwCCyYJbFDulTD9uCrPlPc3eK6',
        preliminaryScore: 75,
    },
    {
        agentName: '脂肪医生韦元强',
        agentId: 'CjWC3f7MyXUfpGLFeZkEuxQL4OX7R57e',
        preliminaryScore: 75,
    },
    {
        agentName: '路会聊医美',
        agentId: 'c1fcq6js7wA1lIH340oIi5rL9EI5uUy0',
        preliminaryScore: 75,
    },
    {
        agentName: '许刚医生',
        agentId: 'DgTX3OD3laJIrwTs55ZU1ltYKlKbxN6z',
        preliminaryScore: 75,
    },
    {
        agentName: '王健医生',
        agentId: 'M4JwhIjE8bCZQUdf5VDV2CsyYL2rvfDC',
        preliminaryScore: 74,
    },
    {
        agentName: '婚姻情感专家潘幸知',
        agentId: 'MqXMBaA8vDdv259yxSe4yQlCur4V2q39',
        preliminaryScore: 73,
    },
    {
        agentName: '龙腾飞医生',
        agentId: 'mlybWALBesEBxGfnQeIcVFvlEwCho5sD',
        preliminaryScore: 73,
    },
    {
        agentName: '张余医生',
        agentId: 'nBDNWMofEEj82y8EciodAJGu9gwp2bpF',
        preliminaryScore: 73,
    },
    {
        agentName: '曾昭华医生',
        agentId: 'SJWNtWyCGZzolYyCLaUCv2HsS5nVS3pC',
        preliminaryScore: 73,
    },
    {
        agentName: '尤长宣医生',
        agentId: 'FnkojlQ5z3LHXNPnyBHVIAXSDKoqzRA7',
        preliminaryScore: 72,
    },
    {
        agentName: '认知觉醒王阳明心学',
        agentId: 'IGz10IA4fSwHToRq21dpnIA97hwpRanM',
        preliminaryScore: 72,
    },
    {
        agentName: '上海九院周双白',
        agentId: 'lDjcOLccS618QpQcgEeqhHBgqkSwCoNy',
        preliminaryScore: 71,
    },
    {
        agentName: '情感天使杨老师',
        agentId: '6BXKHm91S2UGTnWcPvs01gib0YfzEcJk',
        preliminaryScore: 71,
    },
    {
        agentName: '大榜情感咨询',
        agentId: 'fOmqIPFc15eF6bqAuqpKjczUHkSFzhaN',
        preliminaryScore: 71,
    },
    {
        agentName: '心血管内科郝宝顺博士的诊室',
        agentId: 'GbtQjyPf9RVQPKIMdS6GBwntgjPAqZAS',
        preliminaryScore: 71,
    },
    {
        agentName: '情感咨询导师晓晓',
        agentId: 'jcHWacJe8s6H4QCoqtiqXFzcCyn190av',
        preliminaryScore: 70,
    },
    {
        agentName: '情感引路人燕老师',
        agentId: '0gM9XusHDHSzmvY6jDBpnURGbTGHNuuH',
        preliminaryScore: 70,
    },
    {
        agentName: '上海九院张路医生',
        agentId: 'Q6ZxloMtu85fDOb5hN2G9cQIb0UnO4PN',
        preliminaryScore: 70,
    },
    {
        agentName: '上海九院梁奕敏',
        agentId: 'ZjT84ShhupLvyDpO5313ZPhmZLg3ErI2',
        preliminaryScore: 70,
    },
    {
        agentName: '战舰波将金历史科普',
        agentId: 'd23SVCvH7UeI97riFquYjmsCTvaHUop2',
        preliminaryScore: 68,
    },
    {
        agentName: '翟老师心理咨询',
        agentId: 'uqsiFeCvp9g4qPjAkqv2RVIXPQfk4IQC',
        preliminaryScore: 68,
    },
    {
        agentName: '闲聊太平天国',
        agentId: 'nfFKhBsEw71Blb5pDMQWbamVEm3rwvDX',
        preliminaryScore: 68,
    },
    {
        agentName: '上海九院王文进医生',
        agentId: 'g5xQq0qjZzm0CXAiNbUFrkXVhE3WXtTP',
        preliminaryScore: 68,
    },
    {
        agentName: '卢艳霞动机心理学专家',
        agentId: 'wuI6tsuNad1TOJd3z3Di6Fr95LJIGqpR',
        preliminaryScore: 60,
    },
    {
        agentName: 'Renton历史科普',
        agentId: 'YbbJJOnpC7joRbdDANssoa6N9zTlxo0a',
        preliminaryScore: 55,
    },
    {
        agentName: '情感咨询师',
        agentId: '8M9DKk9lzzsHIBpcgaLvSuP55dTGM8f8',
        preliminaryScore: 55,
    },
];

// 不同类型赛题的决赛名单
export const FINALS_LIST_MAP: Record<string, any> = {
    [COMPETITION_TYPE.game]: ENTERTAINMENT_LIST,
    [COMPETITION_TYPE.ai]: AI_MVP_LIST,
    [COMPETITION_TYPE.expert]: EXPERT_LIST,
    [COMPETITION_TYPE.creativity]: CREATIVITY_LIST,
};
