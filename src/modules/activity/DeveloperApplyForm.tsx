/**
 * @file 开发者报名页面
 * <AUTHOR>
 */

import React, {useCallback, useState, useEffect} from 'react';
import {Button, Form, Input, Space, Radio, message, Select} from 'antd';
import classNames from 'classnames';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {validatePhone, validateSpecialCharacter} from '@/utils/formValidator';
import {getPopupContainer} from '@/utils/getPopupContainer';
import qrCodeImg from '@/modules/activity/assets/qrCodeImg.png';
import {CustomerType, ExperienceType, ContactType, QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import api from '@/api/activity';
import urls from '@/links';
import {CompetitionTypeQrCodeMap, INFO_SOURCES, formItemStyle} from '@/modules/activity/constants';
import {useIsMobileStore} from '@/store/home/<USER>';

// 初始数据
const initialValues = {
    name: '',
    contactType: ContactType.Phone,
    contactNumber: '',
    playerType: CustomerType.Personal,
    agentDevExperience: ExperienceType.AgentExperience,
};

const DeveloperApplyForm: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    const navigate = useNavigate();
    const fields = Form.useWatch([], form);
    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));
    const competitionType = searchParams.get('type');
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 防止手输url直接访问该页面，如果缺少参数，则重定向到中心页面
    useEffect(() => {
        if (!(competitionId && competitionType)) {
            message.info('访问参数不正确');
            navigate(urls.activityCenter.raw(), {replace: true});
        }
    }, [competitionId, competitionType, navigate]);

    // 请求问卷状态
    const getQuestionnaireStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                });

                // 填写过则重定向值赛事中心
                if (res.submitStatus === SubmitStatus.Submitted) {
                    message.info('您已提交过报名申请');
                    navigate(urls.activityCenter.raw(), {replace: true});
                }
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId, navigate]);

    // 进入表单页面，获取问卷填写状态
    useEffect(() => {
        getQuestionnaireStatus();
    }, [getQuestionnaireStatus]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        setLoading(true);
        const fields = await form.validateFields().catch(() => setLoading(false));

        try {
            if (fields && competitionId) {
                await api.questionnaireSubmit({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                    questionnaireDetail: JSON.stringify(fields),
                });
                message.success('报名成功');
                // 跳转至创建智能体页面
                navigate(urls.center.raw());
            }
            setLoading(false);
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [navigate, form, competitionId]);

    return (
        <div className={`flex min-h-[100vh] justify-center bg-questionnaire-bg pb-[50px] pt-[50px] mlg:pt-[83px]`}>
            <div className="box-content flex max-w-[800px] px-6">
                <div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">
                        搜索技术创新大赛报名表
                    </div>
                    <div className="mb-2 max-w-[441px] text-justify text-xs leading-[20px] text-neutral-800">
                        欢迎报名智能体赛道，您正在报名的是「{competitionType}」赛道
                    </div>
                    <Form
                        labelCol={{
                            span: 12,
                        }}
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        initialValues={initialValues}
                        className={classNames(formItemStyle, 'w-[22.5rem]')}
                    >
                        <Form.Item
                            name="name"
                            label="您的姓名"
                            rules={[
                                {required: true, message: '姓名不能为空，请输入'},
                                {validator: validateSpecialCharacter('请输入您的真实姓名')},
                            ]}
                        >
                            <Input maxLength={16} showCount placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item name="contactType" label="您的联系方式">
                            <Radio.Group>
                                <Radio value={ContactType.Phone}>联系电话</Radio>
                                <Radio value={ContactType.WeChat}>微信</Radio>
                                <Radio value={ContactType.Other}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>

                        {fields?.contactType === ContactType.Phone ? (
                            <Form.Item
                                name="contactNumber"
                                label="联系电话"
                                rules={[
                                    {required: true, message: '联系电话不能为空，请输入'},
                                    {validator: validatePhone('联系电话格式有误，请重新输入')},
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : fields?.contactType === ContactType.Other ? (
                            <Form.Item
                                name="contactNumber"
                                label="其他"
                                rules={[{required: true, message: '联系方式不能为空，请输入'}]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="contactNumber"
                                label="微信号"
                                rules={[{required: true, message: '微信号不能为空，请输入'}]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        )}

                        <Form.Item
                            name="playerType"
                            label="您是学生/个人/企业开发者"
                            rules={[{required: true, message: '请选择您的主体类型'}]}
                        >
                            <Radio.Group>
                                <Space direction="vertical">
                                    <Radio value={CustomerType.Student}>学生</Radio>
                                    <Radio value={CustomerType.Personal}>个人</Radio>
                                    <Radio value={CustomerType.Enterprise}>企业</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>

                        <Form.Item
                            name="agentDevExperience"
                            label="您是否有过智能体开发经验"
                            rules={[{required: true, message: '请选择您的智能体开发经验'}]}
                        >
                            <Radio.Group>
                                <Space direction="vertical">
                                    <Radio value={ExperienceType.AgentExperience}>是，有0代码智能体开发经验</Radio>
                                    <Radio value={ExperienceType.WorkflowPluginExperience}>
                                        是，有0代码、工作流、插件等开发经验
                                    </Radio>
                                    <Radio value={ExperienceType.NoExperience}>否</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>

                        <Form.Item
                            name="infoSource"
                            label="请问您从哪里了解到的报名信息"
                            rules={[{required: true, message: '请选择您从哪里了解到的报名信息'}]}
                        >
                            <Select options={INFO_SOURCES} getPopupContainer={getPopupContainer} placeholder="请选择" />
                        </Form.Item>

                        {isMobile && (
                            <div className="h-[150px] w-full ">
                                <span className="text-xs">扫码加入赛事交流群，获取更多大赛信息</span>
                                <img
                                    className="mt-2 h-[100px]"
                                    src={competitionType ? CompetitionTypeQrCodeMap[competitionType] : qrCodeImg}
                                />
                            </div>
                        )}

                        <Button type="primary" htmlType="submit" loading={loading}>
                            提交
                        </Button>
                    </Form>
                </div>

                {!isMobile && (
                    <div
                        className="ml-[8.125rem] box-border h-[307px] w-[247px] shrink-0 rounded-xl border border-black border-opacity-10
                    bg-white p-[28px]"
                    >
                        <span className="text-sm">扫码加入赛事交流群，获取更多大赛信息</span>
                        <img
                            className="mt-2"
                            src={competitionType ? CompetitionTypeQrCodeMap[competitionType] : qrCodeImg}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default DeveloperApplyForm;
