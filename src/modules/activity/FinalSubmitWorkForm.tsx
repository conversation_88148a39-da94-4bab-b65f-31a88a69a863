/**
 * @file 决赛提交作品页面，决赛前(9月)另外提需、上线。
 * <AUTHOR>
 */

import React, {useCallback, useState, useEffect} from 'react';
import {Button, Form, Input, Row, message} from 'antd';
import classNames from 'classnames';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {Rule, RuleObject} from 'antd/es/form';

import {validateSpecialCharacter} from '@/utils/formValidator';
import {QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import api from '@/api/activity';
import urls from '@/links';
import {formItemStyle, FINALS_LIST_MAP} from '@/modules/activity/constants';
import UploadFiled from '@/modules/activity/components/UploadFiled';
// 初始数据
const initialValues = {
    name: '',
    agentName: '',
    agentId: '',
    // 作品附件
    workAttachment: '',
};

// 智能体id是否在决赛名单里校验函数
const isAgentIdInList = (agentId: string, competitionType: string) => {
    return FINALS_LIST_MAP[competitionType].some((item: any) => item.agentId === agentId);
};

const FinalSubmitWorkForm: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    // 上传附件loading状态
    const [uploadFileLoading, setUploadFileLoading] = useState<boolean>(false);
    // 上传附件失败状态
    const [uploadFileFail, setUploadFileFail] = useState<boolean>(false);
    // agentId是否存在与决赛list中
    const [agentIdExist, setAgentIdExist] = useState<boolean>(false);

    const navigate = useNavigate();

    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));
    const competitionType = searchParams.get('type');

    // 如果缺少参数，则重定向到中心页面
    useEffect(() => {
        if (!(competitionId && competitionType)) {
            message.info('访问参数不正确');
            navigate(urls.activityCenter.raw(), {replace: true});
        }
    }, [navigate, competitionId, competitionType]);

    // 请求问卷状态
    const getQuestionnaireStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.FinalsSubmitWork,
                });

                // 填写过则重定向值赛事中心
                if (res.submitStatus === SubmitStatus.Submitted) {
                    message.info('您已提交过决赛作品');
                    navigate(urls.activityCenter.raw(), {replace: true});
                }
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId, navigate]);

    // 进入表单页面，获取问卷填写状态
    useEffect(() => {
        getQuestionnaireStatus();
    }, [getQuestionnaireStatus]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        setLoading(true);
        const fields = await form.validateFields().catch(() => setLoading(false));

        try {
            if (fields && competitionId) {
                await api.questionnaireSubmit({
                    competitionId,
                    questionnaireType: QuestionnaireType.FinalsSubmitWork,
                    questionnaireDetail: JSON.stringify(fields),
                });
            }
            setLoading(false);
            message.success('提交成功');
            // 跳转至创建智能体页面
            navigate(urls.center.raw());
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [navigate, form, competitionId]);

    // 智能体id表单校验
    const getIdValidate = (trigger?: string | string[]): Rule => {
        return {
            validateTrigger: trigger,
            validator: async (rule: RuleObject, id: string) => {
                if (id) {
                    if (id.includes(' ')) {
                        return Promise.reject(new Error('请检查是否有多余空格'));
                    }

                    if (isAgentIdInList(id, competitionType!)) {
                        setAgentIdExist(true);
                        return Promise.resolve();
                    } else {
                        setAgentIdExist(false);
                        return Promise.reject(new Error('智能体ID不存在于决赛名单中'));
                    }
                }
            },
        };
    };

    return (
        <div className={`flex min-h-[100vh] justify-center bg-questionnaire-bg pb-[50px] pt-[83px]`}>
            <div className="box-content flex max-w-[800px] px-6">
                <div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">
                        决赛作品提交
                    </div>
                    <div className="mb-2 max-w-[441px] text-justify text-xs leading-[30px] text-neutral-800">
                        欢迎参与智能体赛道，您正在提交「{competitionType}」作品
                    </div>
                    <Form
                        labelCol={{
                            span: 12,
                        }}
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        initialValues={initialValues}
                        className={classNames(formItemStyle, 'w-[22.5rem]')}
                    >
                        <Form.Item
                            name="name"
                            label="您的姓名"
                            rules={[
                                {required: true, message: '姓名不能为空，请输入'},
                                {validator: validateSpecialCharacter('请输入您的真实姓名')},
                            ]}
                        >
                            <Input maxLength={16} showCount placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item
                            name="agentName"
                            label="提交的智能体名称"
                            rules={[{required: true, message: '请填写智能体名称'}]}
                        >
                            <Input placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item
                            name="agentId"
                            label="提交的智能体ID"
                            rules={[{required: true, message: '请填写智能体ID'}, getIdValidate(['onChange', 'onBlur'])]}
                        >
                            <div>
                                <Row align={'middle'} className="mb-[10px] leading-none text-gray-tertiary">
                                    <span>个人空间-我的智能体-选中参赛智能体-复制ID</span>
                                </Row>
                                <Input placeholder="请输入" autoComplete="off" />
                            </div>
                        </Form.Item>

                        <Form.Item
                            name="workAttachment"
                            label="作品附件"
                            rules={[{required: true, message: '请上传作品附件'}]}
                        >
                            <div className="h-[110px]">
                                <UploadFiled
                                    setUploadFileLoading={setUploadFileLoading}
                                    setUploadFileFail={setUploadFileFail}
                                />
                            </div>
                        </Form.Item>

                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            disabled={uploadFileLoading || uploadFileFail || !agentIdExist}
                        >
                            提交
                        </Button>
                    </Form>
                </div>
            </div>
        </div>
    );
};

export default FinalSubmitWorkForm;
