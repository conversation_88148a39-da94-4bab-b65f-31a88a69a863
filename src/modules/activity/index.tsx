/**
 * @file 赛事中心
 * <AUTHOR>
 */
import {useCallback, useEffect, useRef, useState} from 'react';
import classNames from 'classnames';
import styled from '@emotion/styled';
import ActivityTitle from '@/modules/activity/components/ActivityTitle';
import BlockContainer from '@/modules/activity/components/BlockContainer';
import SubjectCard from '@/modules/activity/components/center/SubjectCard';
import centerBg from '@/modules/activity/assets/center-bg.jpg';
import {
    AGENT_AWARD_CONFIG,
    CLASS_CONFIG,
    COMMITTEE_CONFIG,
    COMPLETION_INTRO,
    GUEST_CONFIG,
    PARTNERS_CONFIG,
    SUBJECT_CONFIG,
    TECH_AWARD_CONFIG,
} from '@/modules/activity/constants';
import api from '@/api/activity/index';
import UserAvatar from '@/modules/activity/components/center/UserAvatar';
import AwardsCard from '@/modules/activity/components/center/AwardsCard';
import ScheduleCard from '@/modules/activity/components/center/ScheduleCard';
import GuestCard from '@/modules/activity/components/center/GuestCard';
import PartnersCard from '@/modules/activity/components/center/PartnersCard';
import ClassCard from '@/modules/activity/components/center/ClassCard';
import {EVENT_PAGE_CONST} from '@/utils/loggerV2/constants';
import {sendLogV2, SERVER_ID} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_TYPE} from '@/utils/loggerV2/ubcLoggerV2';
import {useIsMobileStore} from '@/store/home/<USER>';

const CompetitionTypeIndex = {
    TECH: '0',
    SMART_APP: '1',
};

const CustomDiv = styled.div`
    -webkit-tap-highlight-color: transparent;
`;

const ActivityCenter = () => {
    const defaultTab = localStorage.getItem('currentTabIndex') || CompetitionTypeIndex.SMART_APP;
    const [currentTabIndex, setCurrentTabIndex] = useState(defaultTab);

    useEffect(() => {
        localStorage.setItem('currentTabIndex', currentTabIndex.toString());
    }, [currentTabIndex]);

    const clickTechType = useCallback(() => {
        setCurrentTabIndex(CompetitionTypeIndex.TECH);
    }, [setCurrentTabIndex]);

    const clickAppType = useCallback(() => {
        setCurrentTabIndex(CompetitionTypeIndex.SMART_APP);
    }, [setCurrentTabIndex]);

    const [competitionList, setCompetitionList] = useState<
        Array<{
            id: number;
            competitionName: string;
        }>
    >([]);

    // 请求赛题列表
    const getCompetitionList = useCallback(async () => {
        const res = (await api.getCompetitionList()) || [];
        setCompetitionList(res);
    }, []);

    useEffect(() => {
        getCompetitionList();
    }, [getCompetitionList]);

    const scheduleRef = useRef<HTMLDivElement>(null);
    const [tabSticky, setTabSticky] = useState(true);
    const isMobile = useIsMobileStore(store => store.isMobile);
    const [tabDistance, setTabDistance] = useState(300);

    // 根据滚动距离，判断是否固定
    const handleScroll = useCallback(() => {
        const scheduleElement = scheduleRef.current;

        if (!scheduleElement) return;
        const scheduleTop = scheduleElement.getBoundingClientRect().top;
        if (scheduleTop < tabDistance) {
            setTabSticky(false);
        } else {
            setTabSticky(true);
        }
    }, [tabDistance]);

    // 滚动监听
    useEffect(() => {
        window.addEventListener('scroll', handleScroll);
        if (isMobile) {
            setTabDistance(150);
        }
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [handleScroll, isMobile]);

    // 页面展现点位记录
    useEffect(() => {
        sendLogV2(EVENT_TYPE.DISPLAY, '', EVENT_PAGE_CONST.NV_HOME, {}, SERVER_ID.Activity);
    }, []);

    return (
        <CustomDiv className="w-full bg-[#06061C]">
            <div className="flex w-full flex-col items-center justify-center">
                {/* 第一屏 */}
                <div className="overflow-clip">
                    {/* 登录 & 用户信息 相关 */}
                    <UserAvatar />
                    {/* 第一屏背景 - 赛事海报 */}
                    <img className="w-full mlg:min-w-[1000px]" src={centerBg} />
                </div>

                {/* 赛题类型tab */}
                <div
                    className={classNames(
                        'z-50 flex h-[45px] cursor-pointer justify-center text-center text-sm font-semibold text-white mlg:h-[100px] mlg:text-[30px]',
                        {
                            sticky: tabSticky,
                            'top-0': tabSticky,
                            relative: !tabSticky,
                        }
                    )}
                >
                    <div
                        className={classNames('w-[50vw] leading-[45px] mlg:w-[43vw] mlg:leading-[100px]', {
                            'bg-[#E84466]': currentTabIndex === CompetitionTypeIndex.TECH,
                            'bg-[#0F1933]': currentTabIndex === CompetitionTypeIndex.SMART_APP,
                        })}
                        onClick={clickTechType}
                    >
                        技术赛题
                    </div>
                    <div
                        className={classNames('w-[50vw] leading-[45px] mlg:w-[43vw] mlg:leading-[100px]', {
                            'bg-[#E84466]': currentTabIndex === CompetitionTypeIndex.SMART_APP,
                            'bg-[#0F1933]': currentTabIndex === CompetitionTypeIndex.TECH,
                        })}
                        onClick={clickAppType}
                    >
                        智能体应用赛题
                    </div>
                </div>

                {/* 大赛介绍 */}
                <BlockContainer>
                    <ActivityTitle>大赛介绍</ActivityTitle>
                    <div className="mt-4 text-[#FFFFFFCC] mlg:mt-[30px] mlg:w-[1000px]">
                        <p className="text-justify text-xs leading-[24px] mlg:text-[20px] mlg:leading-[38px]">
                            {COMPLETION_INTRO}
                        </p>
                    </div>
                </BlockContainer>

                {/* 赛题列表 */}
                <BlockContainer>
                    <ActivityTitle>
                        {currentTabIndex === CompetitionTypeIndex.TECH ? '技术赛题' : '智能体应用赛题'}
                    </ActivityTitle>
                    {/* 赛题卡片 */}
                    {currentTabIndex === CompetitionTypeIndex.TECH ? (
                        <div className="mt-5 mlg:mt-10">
                            <SubjectCard isTechType />
                        </div>
                    ) : (
                        <div className="mt-5 flex flex-wrap items-center justify-center gap-2 mlg:mt-10 mlg:w-[1000px] mlg:gap-[14px]">
                            {competitionList.map(item => (
                                <SubjectCard
                                    key={item.id}
                                    competitionType={item.competitionName}
                                    competitionId={item.id}
                                />
                            ))}
                        </div>
                    )}
                </BlockContainer>

                {/* 奖项区域 */}
                <BlockContainer>
                    <ActivityTitle>
                        {currentTabIndex === CompetitionTypeIndex.TECH ? '技术赛题奖项' : '智能体应用赛题奖项'}
                    </ActivityTitle>
                    {/* 奖项卡片 */}
                    {currentTabIndex === CompetitionTypeIndex.TECH ? (
                        <div className="mt-5 flex flex-row flex-wrap items-center justify-center gap-x-2 gap-y-3 mlg:mt-10 mlg:w-[1000px] mlg:gap-x-4 mlg:gap-y-[25px]">
                            {TECH_AWARD_CONFIG.map(item => (
                                <AwardsCard key={item.name} info={item} isTech className="basis-auto mlg:w-[238px]" />
                            ))}
                        </div>
                    ) : (
                        <div className="mt-5 flex flex-row flex-wrap items-center justify-center gap-x-2 gap-y-3 mlg:mt-10 mlg:w-[1002px] mlg:gap-x-4 mlg:gap-y-[25px]">
                            {AGENT_AWARD_CONFIG.map((item, index) =>
                                index <= 1 ? (
                                    <AwardsCard
                                        key={item.name}
                                        info={item}
                                        isLong
                                        className="basis-auto mlg:w-[493px]"
                                    />
                                ) : (
                                    <AwardsCard key={item.name} info={item} className="basis-auto mlg:w-[238px]" />
                                )
                            )}
                        </div>
                    )}
                </BlockContainer>

                {/* 赛程 */}
                <BlockContainer noneBackground>
                    <ActivityTitle>赛程</ActivityTitle>
                    {/* 赛程卡片 */}
                    <div
                        className="mt-5 columns-2 gap-10 bg-[#06061C] mlg:mt-8 mlg:columns-4 mlg:gap-[86px]"
                        ref={scheduleRef}
                    >
                        {SUBJECT_CONFIG.map(item => (
                            <ScheduleCard key={item.date} info={item} />
                        ))}
                    </div>
                </BlockContainer>

                {/* 嘉宾介绍 */}
                {/* 组委会评审 */}
                <BlockContainer>
                    <ActivityTitle>评审组委会</ActivityTitle>
                    <div className="mt-2 flex w-full flex-row flex-wrap items-center justify-center gap-x-20 gap-y-8 mlg:w-[1000px]">
                        {COMMITTEE_CONFIG.map(item => (
                            <GuestCard key={item.name} info={item} />
                        ))}
                    </div>
                </BlockContainer>
                <BlockContainer>
                    <ActivityTitle>专家评审</ActivityTitle>
                    {/* 专家评审 */}
                    <div className="mt-2 flex w-full flex-row flex-wrap items-center justify-center gap-x-[50px] gap-y-8 mlg:w-[1000px] mlg:gap-x-[78px]">
                        {GUEST_CONFIG.map(item => (
                            <GuestCard key={item.name} info={item} />
                        ))}
                    </div>
                </BlockContainer>

                {/* 赛事课程 */}
                <BlockContainer>
                    <ActivityTitle>赛事课程</ActivityTitle>
                    {/* 课程卡片   TODO: 先占位，确定信息后遍历 */}
                    <div className="mt-5 flex w-full flex-row flex-wrap justify-center gap-x-3 gap-y-3 mlg:mt-[30px] mlg:w-[1000px] mlg:gap-y-5">
                        {CLASS_CONFIG.map(item => (
                            <ClassCard key={item.describe} info={item} />
                        ))}
                    </div>
                </BlockContainer>

                {/* 合作伙伴 */}
                <BlockContainer>
                    <ActivityTitle>合作伙伴</ActivityTitle>
                    <div className="mt-5 flex w-full flex-wrap items-center justify-center gap-4 bg-[#06061C] mlg:mt-10 mlg:w-[1000px]">
                        {PARTNERS_CONFIG.map(item =>
                            item.image.length > 1 ? (
                                <PartnersCard key={item.category} info={item} ranking />
                            ) : (
                                <PartnersCard key={item.category} info={item} />
                            )
                        )}
                    </div>
                </BlockContainer>
            </div>
        </CustomDiv>
    );
};

export default ActivityCenter;
