/**
 * @file 专家应征页面
 * <AUTHOR>
 */

import React, {useCallback, useState, useEffect} from 'react';
import {Button, Form, Input, Radio, Col, Row, Checkbox, message, Select} from 'antd';
import classNames from 'classnames';
import {useNavigate} from 'react-router-dom';
import {useSearchParams} from 'react-router-dom';
import {getPopupContainer} from '@/utils/getPopupContainer';
import expertQrCode from '@/modules/activity/assets/qrCode/expert.jpg';
import {validatePhone, validateSpecialCharacter} from '@/utils/formValidator';
import {ContactType, QuestionnaireType} from '@/api/activity/interface';
import api from '@/api/activity';
import urls from '@/links';
import {INFO_SOURCES, formItemStyle} from '@/modules/activity/constants';
import {useIsMobileStore} from '@/store/home/<USER>';

// 初始数据
const initialValues = {
    name: '',
    contactType: ContactType.Phone,
    contactNumber: '',
    highestEducation: '',
    graduatedSchool: '',
    technicalField: [],
    competitionWinningExperience: '奖项名称：\n获奖时间：\n简要描述：',
    implementation: '',
};

const ExpertApplyForm: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    const navigate = useNavigate();
    const fields = Form.useWatch([], form);
    // 获取参数
    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('competitionId'));
    const expertId = Number(searchParams.get('expertId'));
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 如果缺少参数，则重定向到上一个页面：组队页面
    useEffect(() => {
        if (!(competitionId && expertId)) {
            message.info('访问参数不正确');
            navigate(urls.activityTeam.raw(), {replace: true});
        }
    }, [competitionId, expertId, navigate]);

    // 提交表单
    const handleSubmit = useCallback(async () => {
        setLoading(true);

        const fields = await form.validateFields().catch(() => setLoading(false));
        try {
            if (fields && competitionId) {
                await api.questionnaireSubmit({
                    expertId,
                    competitionId,
                    questionnaireType: QuestionnaireType.ExpertApply,
                    questionnaireDetail: JSON.stringify(fields),
                });
                message.success('提交成功');
                // 跳转至智能体平台
                navigate(urls.center.raw());
            }
            setLoading(false);
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [navigate, expertId, form, competitionId]);

    return (
        <div className="flex min-h-[100vh] justify-center bg-questionnaire-bg pb-[50px] pt-[50px] mlg:pt-[83px]">
            <div className="box-content flex max-w-[800px]">
                <div>
                    <div className="pb-2.5 text-xl font-medium leading-10 text-neutral-800 mlg:text-4xl">
                        开发者应征表单
                    </div>
                    <Form
                        labelCol={{
                            span: 12,
                        }}
                        form={form}
                        layout="vertical"
                        onFinish={handleSubmit}
                        initialValues={initialValues}
                        className={classNames(formItemStyle, 'w-[22.5rem]')}
                    >
                        <Form.Item
                            name="name"
                            label="您的姓名"
                            rules={[
                                {required: true, message: '姓名不能为空，请输入'},
                                {validator: validateSpecialCharacter('请输入您的真实姓名')},
                            ]}
                        >
                            <Input maxLength={16} showCount placeholder="请输入" autoComplete="off" />
                        </Form.Item>

                        <Form.Item name="contactType" label="您的联系方式">
                            <Radio.Group>
                                <Radio value={ContactType.Phone}>联系电话</Radio>
                                <Radio value={ContactType.WeChat}>微信</Radio>
                                <Radio value={ContactType.Other}>其他</Radio>
                            </Radio.Group>
                        </Form.Item>

                        {fields?.contactType === ContactType.Phone ? (
                            <Form.Item
                                name="contactNumber"
                                label="联系电话"
                                rules={[
                                    {required: true, message: '联系电话不能为空，请输入'},
                                    {validator: validatePhone('联系电话格式有误，请重新输入')},
                                ]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : fields?.contactType === ContactType.Other ? (
                            <Form.Item
                                name="contactNumber"
                                label="其他"
                                rules={[{required: true, message: '联系方式不能为空，请输入'}]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                name="contactNumber"
                                label="微信号"
                                rules={[{required: true, message: '微信号不能为空，请输入'}]}
                            >
                                <Input placeholder="请输入" autoComplete="off" />
                            </Form.Item>
                        )}

                        <Form.Item
                            name="highestEducation"
                            label="您的最高学历"
                            rules={[{required: true, message: '请填写您的最高学历'}]}
                        >
                            <Input placeholder="请输入" />
                        </Form.Item>

                        <Form.Item
                            name="graduatedSchool"
                            label="您的最高学历毕业院校"
                            rules={[{required: true, message: '请填写您的最高学历毕业院校'}]}
                        >
                            <Input placeholder="请输入" />
                        </Form.Item>

                        <Form.Item
                            name="technicalField"
                            label="您的技术领域"
                            rules={[{required: true, message: '请填写您的技术领域'}]}
                        >
                            <Checkbox.Group>
                                <Row gutter={[0, 6]}>
                                    <Col span={8}>
                                        <Checkbox value="移动开发">移动开发</Checkbox>
                                    </Col>
                                    <Col span={8}>
                                        <Checkbox value="Web开发">Web开发</Checkbox>
                                    </Col>
                                    <Col span={8}>
                                        <Checkbox value="AI/机器学习">AI/机器学习</Checkbox>
                                    </Col>
                                    <Col span={8}>
                                        <Checkbox value="区块链">区块链</Checkbox>
                                    </Col>
                                    <Col span={8}>
                                        <Checkbox value="其他">其他</Checkbox>
                                    </Col>
                                </Row>
                            </Checkbox.Group>
                        </Form.Item>

                        <Form.Item name="competitionWinningExperience" label="过往开发比赛获奖经历">
                            <Input.TextArea autoSize={{minRows: 4, maxRows: 8}} />
                        </Form.Item>

                        <Form.Item
                            name="implementation"
                            label="合作专家智能体实现方案"
                            rules={[{required: true, message: '请填写合作专家智能体实现方案'}]}
                        >
                            <div>
                                <Row align={'middle'} className="mb-[10px] leading-none text-gray-tertiary">
                                    <span>
                                        请根据该专家所预期的智能体效果，进行简单的方案描述。包括智能体创意、技术实现、商业化价值等。
                                    </span>
                                </Row>
                                <Input.TextArea autoSize={{minRows: 4, maxRows: 8}} placeholder="请填写" />
                            </div>
                        </Form.Item>

                        <Form.Item
                            name="infoSource"
                            label="请问您从哪里了解到的报名信息"
                            rules={[{required: true, message: '请选择您从哪里了解到的报名信息'}]}
                        >
                            <Select options={INFO_SOURCES} getPopupContainer={getPopupContainer} placeholder="请选择" />
                        </Form.Item>

                        {isMobile && (
                            <div className="h-[140px] w-full pb-3 ">
                                <div className="text-xs">添加小助手二维码，及时获取专家组队信息。</div>
                                <img className="-ml-4 h-[120px]" src={expertQrCode} />
                            </div>
                        )}

                        <Button type="primary" onClick={handleSubmit} loading={loading}>
                            提交
                        </Button>
                    </Form>
                </div>

                {!isMobile && (
                    <div
                        className="ml-[8.125rem] box-border h-[307px] w-[247px] shrink-0 rounded-xl border border-black border-opacity-10
                    bg-white p-[28px]"
                    >
                        <span className="text-sm">添加小助手二维码，及时获取专家组队信息。</span>
                        <img className="mt-2" src={expertQrCode} />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ExpertApplyForm;
