/**
 * @file 组队页面
 * <AUTHOR>
 */

import React, {useEffect, useState, useCallback} from 'react';
import {useSearchParams} from 'react-router-dom';
import ExpertCard from '@/modules/activity/components/expert/ExpertCard';
import BlockContainer from '@/modules/activity/components/BlockContainer';
import api from '@/api/activity/index';
import {ExpertInfoList} from '@/api/activity/interface';
import ActivityTitle from '@/modules/activity/components/ActivityTitle';
import Loading from '@/components/Loading/LingJingLoading';
import expertBg from '@/modules/activity/assets/expert-bg.jpg';

const ActivityTeam: React.FC = () => {
    const [expertList, setExpertList] = useState<ExpertInfoList>([]);
    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));
    const [loading, setLoading] = useState(false);

    const getExpertList = useCallback(async () => {
        try {
            setLoading(true);
            const res = await api.getExpertList({competitionId});
            setExpertList(res);
            setLoading(false);
        } catch (e) {
            setLoading(false);
            throw e;
        }
    }, [competitionId]);

    useEffect(() => {
        getExpertList();
    }, [getExpertList]);

    return (
        <>
            {loading ? (
                <div className="relative h-8 w-52">
                    <Loading />
                </div>
            ) : (
                <div className="flex w-full flex-col items-center bg-[#06061c] mlg:min-w-[1400px]">
                    <div className="flex justify-center">
                        {/* 第一屏背景 - 赛事海报 */}
                        <img className="w-full mlg:min-w-[1200px]" src={expertBg} />
                    </div>
                    <BlockContainer>
                        <ActivityTitle>组队规则</ActivityTitle>
                        <div className="mt-4 flex w-full flex-col gap-4 px-[22px] text-justify text-sm leading-7 text-white/60 mlg:mt-6 mlg:w-[1000px] mlg:gap-8 mlg:text-xl mlg:leading-[32px]">
                            <div>
                                <h1 className="mb-4 text-base font-semibold text-white mlg:text-2xl">一、组队目标</h1>
                                <p>
                                    本赛道需要专家与开发者进行双向选择，完成组队后参与比赛。专家提供专业知识与数据集，开发者提供技术支持，合作完成智能体开发。
                                </p>
                            </div>
                            <div>
                                <h1 className="mb-4 text-base font-semibold text-white mlg:text-2xl">二、组队流程</h1>
                                <ul className="font-normal text-white/60">
                                    <li className="mb-3">
                                        <p className="mb-2 font-medium text-white/80">Step1 开发者应征：</p>
                                        开发者根据个人兴趣及项目需求，选择意向合作的专家。每位开发者可选择1位专家进行应征。
                                    </li>
                                    <li className="mb-3">
                                        <p className="mb-2 font-medium text-white/80">Step2 提交个人信息表单：</p>
                                        开发者在选定专家后，需填写并提交个人信息表单。请注意添加表单中的小助手企业微信，获取组队信息。
                                    </li>
                                    <li>
                                        <p className="mb-2 font-medium text-white/80">Step3 专家反选：</p>
                                        <p className="mb-1">
                                            专家收到应征信息后，对开发者的个人信息及合作意向进行查看，专家可根据自身需求及开发者信息，选择组队开发者。
                                        </p>
                                        若专家接受邀请，则组队成功；若拒绝，则开发者可继续选择其他赛道进行参赛。
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h1 className="mb-4 text-base font-semibold text-white mlg:text-2xl">三、沟通协作</h1>
                                <p>
                                    组队成功后，开发者与专家需保持良好沟通，共同推进项目进展。如遇问题或分歧，应积极协商解决。
                                </p>
                            </div>
                            <div>
                                <h1 className="mb-4 text-base font-semibold text-white mlg:text-2xl">四、组队期限</h1>
                                <p>
                                    组队活动自平台发布专家名单之日起算。15个工作日内未完成组队的专家，可由官方工作人员安排组队。
                                </p>
                            </div>
                            <div>
                                <h1 className="mb-4 text-base font-semibold text-white/80 mlg:text-2xl">
                                    五、其他事项
                                </h1>
                                <p>
                                    本规则由平台制定并解释。 如遇特殊情况或争议，平台有权进行裁决。
                                    请根据实际情况调整和完善上述规则内容。
                                </p>
                            </div>
                        </div>
                    </BlockContainer>
                    <BlockContainer>
                        <ActivityTitle>专家组队</ActivityTitle>
                        <div className="mx-[22px] flex w-full flex-row flex-wrap justify-center gap-3 pt-[36px] mlg:w-[1000px]">
                            {expertList?.length > 0 &&
                                expertList.map(epxertInfo => <ExpertCard key={epxertInfo.id} info={epxertInfo} />)}
                        </div>
                    </BlockContainer>
                </div>
            )}
        </>
    );
};

export default ActivityTeam;
