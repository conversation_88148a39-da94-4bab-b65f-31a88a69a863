/**
 * @file 赛事详情页面
 * <AUTHOR>
 */
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useSearchParams} from 'react-router-dom';
import ActivityButton from '@/modules/activity/components/ActivityButton';
import AgreementModal from '@/modules/activity/components/AgreementModal';
import BlockContainer from '@/modules/activity/components/BlockContainer';
import {
    COMPETITION_TYPE,
    CompetitionDetailsBgMap,
    CompetitionTypeLogExtMap,
    FINALS_LIST_MAP,
} from '@/modules/activity/constants';
import EntertainmentDetail from '@/modules/activity/components/detailContent/EntertainmentDetail';
import CreativityDetail from '@/modules/activity/components/detailContent/CreativityDetail';
import AiMvpDetail from '@/modules/activity/components/detailContent/AiMvpDetail';
import ExpertDetail from '@/modules/activity/components/detailContent/ExpertDetail';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import urls from '@/links';
import api from '@/api/activity/index';
import {QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import {sendLogV2, SERVER_ID} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {EVENT_TYPE} from '@/utils/loggerV2/ubcLoggerV2';
import Finalist from '@/modules/activity/components/Finalist';

// eslint-disable-next-line complexity
const ActivityDetail = () => {
    const uniformLogin = useUniformLogin();
    const isLogin = useUserInfoStore(store => store.isLogin);

    const [searchParams] = useSearchParams();
    const agreementModalRef = useRef<any>(null);
    const competitionType = searchParams.get('type') || 'game';
    const competitionId = Number(searchParams.get('id'));
    // const qrCodeImg = CompetitionTypeQrCodeMap[competitionType];

    // 该赛事是否报名
    const [hasApplied] = useState<boolean>(false);
    // 是否已经提交决赛作品
    const [hasSubmittedWork, setHasSubmittedWork] = useState<boolean>(false);

    // const getDeveloperApplyStatus = useCallback(async () => {
    //     if (competitionId) {
    //         try {
    //             const res = await api.getQuestionnaireStatus({
    //                 competitionId,
    //                 questionnaireType: QuestionnaireType.DeveloperApply,
    //             });
    //             setHasApplied(res.submitStatus === SubmitStatus.Submitted);
    //         } catch (e) {
    //             throw e;
    //         }
    //     }
    // }, [competitionId]);

    // const getExpertApplyStatus = useCallback(async () => {
    //     if (competitionId) {
    //         try {
    //             const res = await api.getQuestionnaireStatus({
    //                 competitionId,
    //                 questionnaireType: QuestionnaireType.ExpertApply,
    //             });
    //             setHasApplied(res.submitStatus === SubmitStatus.Submitted);
    //         } catch (e) {
    //             throw e;
    //         }
    //     }
    // }, [competitionId]);

    const getFinalWorkStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.FinalsSubmitWork,
                });
                setHasSubmittedWork(res.submitStatus === SubmitStatus.Submitted);
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId]);

    // 查询决赛作品提交状态
    useEffect(() => {
        if (isLogin) {
            // if (competitionType === COMPETITION_TYPE.expert) {
            //     getExpertApplyStatus();
            // } else {
            //     getDeveloperApplyStatus();
            // }
            getFinalWorkStatus();
        }
    }, [isLogin, getFinalWorkStatus]);

    // 打开组队页
    const openTeam = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            window.open(`${urls.activityTeam.raw()}?id=${competitionId}`);
            event.stopPropagation();
        },
        [competitionId]
    );

    // 提交作品回调
    const handleClickSubmitWork = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activitySubmitWork.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    // 提交决赛作品回调
    const handleSubmitFinalWork = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activityFinalWork.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }
            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    // 打开报名阅读协议弹窗
    const openAgreementModal = useCallback(() => {
        agreementModalRef.current && agreementModalRef.current.showModal(competitionType, competitionId);
    }, [competitionId, competitionType]);

    useEffect(() => {
        window.scrollTo(0, 0); // 重置滚动位置到顶部
    }, []);

    // 页面展现点位记录
    useEffect(() => {
        sendLogV2(
            EVENT_TYPE.DISPLAY,
            '',
            EVENT_PAGE_CONST.NV_TOPIC_DETAIL,
            {
                [EVENT_EXT_KEY_CONST.NV_TOPIC_NAME]: CompetitionTypeLogExtMap[competitionType],
            },
            SERVER_ID.Activity
        );
    }, [competitionType]);

    return (
        <div className="flex w-full flex-col items-center bg-[#06061c] pb-10 text-white/80 mlg:min-w-[1400px] mlg:text-[24px]">
            {/* 第一屏 */}
            <div className="flex w-full justify-center mlg:min-w-[1200px]">
                {/* 第一屏背景 - 赛事海报 */}
                <img className="w-full mlg:min-w-[1200px]" src={CompetitionDetailsBgMap[competitionType]} />
            </div>

            {/* 决赛名单 */}
            <Finalist list={FINALS_LIST_MAP[competitionType]} />

            {/* 详情页面的主要内容 */}
            <main className="mt-4 flex w-full flex-col lg:mt-6">
                {/* 赛题介绍区域 */}
                {competitionType === COMPETITION_TYPE.game ? (
                    <EntertainmentDetail />
                ) : competitionType === COMPETITION_TYPE.ai ? (
                    <AiMvpDetail />
                ) : competitionType === COMPETITION_TYPE.expert ? (
                    <ExpertDetail />
                ) : competitionType === COMPETITION_TYPE.creativity ? (
                    <CreativityDetail />
                ) : null}

                {/* 赛题交流群区域 */}
                <BlockContainer>
                    <div className="flex w-full flex-col items-center gap-6 mlg:gap-12">
                        {/* 赛题交流群标题 */}

                        {/* {competitionType === COMPETITION_TYPE.expert ? (
                            <ActivityTitle>添加小助手</ActivityTitle>
                        ) : (
                            <ActivityTitle>赛题交流群</ActivityTitle>
                        )} */}

                        {/* 赛题交流群二维码 */}
                        {/* <div>
                            <img
                                src={competitionType ? CompetitionTypeQrCodeMap[competitionType] : qrCodeImg}
                                className="h-[190px] rounded-xl mlg:h-[310px]"
                            ></img>
                        </div> */}

                        {/* 报名按钮 */}
                        {/* 10.10 决赛屏蔽按钮 */}
                        {false &&
                            (hasApplied ? (
                                <ActivityButton onClick={handleClickSubmitWork} size="large">
                                    提交作品
                                </ActivityButton>
                            ) : competitionType === COMPETITION_TYPE.expert ? (
                                <ActivityButton onClick={openTeam} size="large">
                                    我要组队
                                </ActivityButton>
                            ) : (
                                <ActivityButton onClick={isLogin ? openAgreementModal : uniformLogin} size="large">
                                    立即报名
                                </ActivityButton>
                            ))}

                        {hasSubmittedWork ? (
                            <ActivityButton disabled size="large">
                                已提交作品
                            </ActivityButton>
                        ) : (
                            <ActivityButton onClick={handleSubmitFinalWork} size="large">
                                决赛作品提交
                            </ActivityButton>
                        )}
                    </div>
                </BlockContainer>
                <AgreementModal ref={agreementModalRef} />
            </main>
        </div>
    );
};

export default ActivityDetail;
