/**
 * @file 统一封装 统一标题
 * <AUTHOR>
 */

import React from 'react';
import titleLeft from '@/modules/activity/assets/title-left.png';
import titleRight from '@/modules/activity/assets/title-right.png';

/**
 * 活动标题组件
 *
 * @param children 子组件或文本内容
 * @returns 返回一个React元素
 */
const ActivityTitle: React.FC<{children: React.ReactNode}> = ({children}) => {
    return (
        <div className="flex w-full items-center justify-center">
            <span>
                <img src={titleLeft} className="h-[8px] xs:h-[9px] md:h-[17px]" />
            </span>
            <span className="no-wrap mx-[6px] text-[18px] font-semibold text-[#43BFE5] xs:mx-[10px] mlg:mx-7 mlg:text-[32px]">
                {children}
            </span>
            <span>
                <img src={titleRight} className="h-[8px] xs:h-[9px] md:h-[17px]" />
            </span>
        </div>
    );
};

export default ActivityTitle;
