import BlockContainer from '@/modules/activity/components/BlockContainer';
import ActivityTitle from '@/modules/activity/components/ActivityTitle';

const EntertainmentDetail = () => {
    return (
        <>
            {/* 评审规则区域 */}
            <BlockContainer>
                {/* 评审规则标题 */}
                <ActivityTitle>提交材料及评分参考</ActivityTitle>
                {/* 评审规则内容 */}
                <div
                    className="text-md pt-5 mlg:mx-0 mlg:w-[1000px] mlg:pt-10 mlg:text-[23.5px] mlg:leading-[37px]"
                    id="content"
                >
                    <p className="font-semibold text-white"> 评选流程：</p>

                    <ul className="ml-6 mt-3 list-disc text-justify text-sm font-normal leading-7 text-white/60 mlg:mt-5 mlg:text-[19.5px] mlg:leading-[35px]">
                        <li>根据决赛评分50%+初赛评分50%综合打分，评选出各赛道一、二、三等奖和优胜奖。</li>
                        <li>全赛道冠军为综合评分最高选手，并由评委表决投票确认。</li>
                    </ul>
                    <ul className="ml-6 mt-3 list-disc text-justify text-sm font-normal leading-7 text-white/60 mlg:mt-5 mlg:text-[19.5px] mlg:leading-[35px]">
                        在进入决赛后需在规定时间内完成智能体调优，并提交智能体说明文档和录屏视频。
                    </ul>

                    {/* 评审规则列表 */}
                    <ol className="list-decimal">
                        <li className="ml-6 mt-4 font-semibold text-white mlg:mb-9 mlg:mt-8">
                            决赛材料：文档和视频（60分）
                            <ul className="mt-3 list-disc text-justify text-sm font-normal leading-7 text-white/60 mlg:mt-5 mlg:text-[19.5px] mlg:leading-[35px]">
                                <li>
                                    评分维度：
                                    <ul className="mb-2 ml-4 list-disc mlg:ml-8">
                                        <li>智能体搭建创意背景、解决方案、核心价值描述以及商业化潜力（20分）</li>
                                    </ul>
                                </li>
                                <li>
                                    评估过程说明：
                                    <ul className="ml-4 list-disc mlg:ml-8">
                                        <li>
                                            构建评估集（20分）：针对要解决的需求构建评估集，分为单轮问题和多轮问题，尽可能覆盖智能体的所有功能。
                                        </li>
                                        <li>
                                            评估视频：开发者针对构建的评估集进行体验录屏（20分）：主要评估开发者是否根据评测集的预期完成智能体的搭建并对最终的结果进行测试和质量把控。
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>

                        {/* 评审规则2 */}
                        <li className="ml-6 mt-4 font-semibold text-white mlg:mb-9 mlg:mt-8">
                            评委打分（40分）
                            <ul className="mt-3 list-disc text-justify text-sm font-normal leading-7 text-white/60 mlg:mt-5 mlg:text-[19.5px] mlg:leading-[35px]">
                                <li>
                                    用户满足度（20分）：结合智能体的描述和评估集进行测试，评估返回内容的满足度是否满足用户需求。
                                </li>
                                <li>
                                    稳定性（20分）：评估智能体的回复效果是否稳定，例如生成结果的质量、交互的稳定等。
                                </li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </BlockContainer>
        </>
    );
};
export default EntertainmentDetail;
