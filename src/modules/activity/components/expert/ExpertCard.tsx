/**
 * @file 专家队伍卡片
 * <AUTHOR>
 */

import React, {useCallback, useRef} from 'react';
import {Tooltip} from 'antd';
import styled from '@emotion/styled';
import classNames from 'classnames';
import {useSearchParams} from 'react-router-dom';
import expertCardBg from '@/modules/activity/assets/expert-card-bg.png';
import urls from '@/links';
import ActivityButton from '@/modules/activity/components/ActivityButton';
import ExpertInfoModal from '@/modules/activity/components/expert/ExpertInfoModal';
import {TeamStatus, ExpertInfo} from '@/api/activity/interface';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import defaultAvatar from '@/assets/default-usert-avatar.png';
import {useIsMobileStore} from '@/store/home/<USER>';

const CustomDiv = styled.div`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${expertCardBg}) !important;
`;

interface ExpertCardProps {
    className?: string;
    info: ExpertInfo;
}

const ExpertCard: React.FC<ExpertCardProps> = ({className, info}) => {
    const expertInfoModalRef = useRef<any>(null);
    const [searchParams] = useSearchParams();
    const competitionId = Number(searchParams.get('id'));

    const isLogin = useUserInfoStore(store => store.isLogin);
    const uniformLogin = useUniformLogin();
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 打开专家详情页弹窗
    const openExpertInfoModal = useCallback(() => {
        expertInfoModalRef.current?.showModal(competitionId, info);
    }, [competitionId, info]);

    const handleClickExpertApply = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activityExpertApply.raw()}?competitionId=${competitionId}&expertId=${info?.id}`);
            } else {
                uniformLogin();
            }
            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, uniformLogin, competitionId, info?.id]
    );

    return (
        <>
            <CustomDiv
                className={classNames(
                    'relative flex h-[180px] w-[135px] cursor-pointer flex-col items-center rounded-xl bg-cover bg-no-repeat xs:h-[220px] xs:w-[165px] mlg:h-[297px] mlg:w-[238px]',
                    className
                )}
                onClick={openExpertInfoModal}
            >
                <div className="mb-2 mt-[18px] flex w-[115px] flex-col items-center justify-center gap-1 text-center text-white xs:w-[135px] xs:gap-2 mlg:mb-5 mlg:w-[205px] mlg:gap-[11px]">
                    {/* 头像区域 */}
                    <div className="h-[40px] w-[40px] overflow-hidden rounded-full mlg:h-[66px] mlg:w-[66px]">
                        <img src={info?.expertPortrait || defaultAvatar}></img>
                    </div>
                    {/* 姓名区域 */}
                    <span className="line-clamp-1 h-[22px] whitespace-normal text-base font-semibold mlg:h-[28px] mlg:text-xl">
                        {info?.expertName || ''}
                    </span>
                    {/* 领域区域 */}
                    <span className="mt-[-4px] line-clamp-1 whitespace-normal text-xs leading-[19.6px] mlg:h-[20px] mlg:text-sm">
                        {info?.knowledgeArea || ''}
                    </span>
                    {/* 简介区域 */}
                    <Tooltip title={isMobile ? '' : info?.introduction || ''}>
                        <span className="line-clamp-2 h-[30px] w-full whitespace-normal text-[10px] leading-[15px] text-white/80 xs:h-[40px] xs:leading-[19.6px] mlg:mt-1 mlg:line-clamp-3 mlg:h-[60px] mlg:text-sm">
                            {info?.introduction || ''}
                        </span>
                    </Tooltip>
                </div>
                {/* 底部按钮区域 */}
                <footer className="mlg:mt-[1px]">
                    <ActivityButton
                        disabled={info?.teamStatus === TeamStatus.CanNotTeamUp}
                        onClick={handleClickExpertApply}
                        size="middle"
                    >
                        {info?.teamStatus === TeamStatus.CanNotTeamUp ? '组队成功' : '我要应征'}
                    </ActivityButton>
                </footer>
            </CustomDiv>
            <ExpertInfoModal ref={expertInfoModalRef} />
        </>
    );
};

export default ExpertCard;
