/**
 * @file 专家详细信息弹窗
 * <AUTHOR>
 */

import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useState} from 'react';
import {Modal} from 'antd';
import styled from '@emotion/styled';
import {CloseCircleOutlined} from '@ant-design/icons';
import urls from '@/links';
import Popup from '@/components/mobile/Popup';
import ActivityButton from '@/modules/activity/components/ActivityButton';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import {TeamStatus, ExpertInfo} from '@/api/activity/interface';
import defaultAvatar from '@/assets/default-usert-avatar.png';
import {useIsMobileStore} from '@/store/home/<USER>';

const StyleModal = styled(Modal)`
    width: 608px !important;
    .ant-modal-content {
        background-color: #06061c !important;
        color: #fff;
        width: 608px !important;
        border-radius: 6px !important;
        .ant-modal-header {
            margin-bottom: 24px;
        }
        .ant-modal-close {
            color: #848691;
        }
        .ant-modal-close:hover {
            color: rgba(110, 110, 110, 1);
        }
    }
    .ant-modal-mask {
        background-color: rgba(128, 128, 128, 0.8) !important;
    
    .ant-modal-footer {
        margin-top: 25px;
        .ant-btn {
            font-weight: 400;
        }
    }
`;

const ExpertInfoModal = forwardRef((_, ref) => {
    const [modalOpen, setModalOpen] = useState(false);
    const [competitionId, setCompetitionId] = useState<number>();
    const [expertInfo, setExpertInfo] = useState<ExpertInfo>();
    const isLogin = useUserInfoStore(store => store.isLogin);
    const isMobile = useIsMobileStore(store => store.isMobile);
    const uniformLogin = useUniformLogin();

    const ExpertInfoList = [
        {label: '姓名', value: expertInfo?.expertName},
        {label: '领域', value: expertInfo?.knowledgeArea},
        {label: '简介', value: expertInfo?.introduction},
        {label: '开发方向', value: expertInfo?.devDirection},
        {label: '预期效果', value: expertInfo?.expect},
    ];

    useEffect(() => {
        if (modalOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    }, [modalOpen]);

    // 对外暴露打开弹窗事件
    useImperativeHandle(ref, () => {
        return {
            showModal(competitionId: number, info: ExpertInfo) {
                setModalOpen(true);
                setCompetitionId(competitionId);
                setExpertInfo(info);
            },
            closeModal() {
                setModalOpen(false);
            },
            open: modalOpen,
        };
    });

    const handleCancel = useCallback(() => {
        setModalOpen(false);
    }, []);

    // 打开应征报名表单
    const handleClickExpertApply = useCallback(() => {
        if (isLogin) {
            window.open(`${urls.activityExpertApply.raw()}?competitionId=${competitionId}&expertId=${expertInfo?.id}`);
        } else {
            setModalOpen(false);
            uniformLogin();
        }
    }, [isLogin, competitionId, expertInfo, uniformLogin]);

    return isMobile ? (
        <Popup
            title="专家组队"
            onClose={handleCancel}
            visible={modalOpen}
            suffix={
                <span
                    className="iconfont icon-close mr-[18px] text-[20px] text-gray-quaternary"
                    onClick={handleCancel}
                ></span>
            }
            bodyStyle={{
                paddingLeft: '17px',
                paddingRight: '17px',
                height: '85%',
                backgroundColor: '#06061c',
                color: '#fff',
            }}
        >
            {/* 专家介绍 */}
            <div className="flex flex-col items-center justify-center gap-2 pb-8">
                {/* 头像 */}
                <div className="mt-[9px] h-[80px] w-[80px] overflow-hidden rounded-full">
                    <img src={expertInfo?.expertPortrait || defaultAvatar} />
                </div>
                {/* 文字内容 */}
                <div className="mb-4 mt-6 flex min-h-[40vh] w-11/12 flex-col items-center gap-2 text-sm">
                    {ExpertInfoList.map(item => (
                        <div key={item.label} className="flex-raw flex justify-between gap-[14px]">
                            <span className="w-[15vw] min-w-[56px] text-right text-white/60">{item.label}</span>
                            <span className="w-[65vw] text-justify">{item.value || ''}</span>
                        </div>
                    ))}
                </div>
                {/* 应征按钮 */}
                <ActivityButton
                    disabled={expertInfo?.teamStatus === TeamStatus.CanNotTeamUp}
                    onClick={handleClickExpertApply}
                    size="large"
                >
                    {expertInfo?.teamStatus === TeamStatus.CanNotTeamUp ? '组队成功' : '我要应征'}
                </ActivityButton>
            </div>
        </Popup>
    ) : (
        <StyleModal open={modalOpen} footer={null} onCancel={handleCancel} closeIcon={<CloseCircleOutlined />}>
            {/* 专家介绍 */}
            <div className="my-4 flex flex-col items-center justify-center gap-4">
                <h1 className="text-[22px] font-medium leading-7">专家组队</h1>
                {/* 头像 */}
                <div className="mt-[18px] h-[120px] w-[120px] overflow-hidden rounded-full">
                    <img src={expertInfo?.expertPortrait || defaultAvatar} />
                </div>
                {/* 文字内容 */}
                <div className="mb-10 mt-6 flex w-[542px] flex-col items-center gap-5 text-sm">
                    {ExpertInfoList.map(item => (
                        <div key={item.label} className="flex-raw flex justify-between gap-[19px]">
                            <span className="w-[56px] text-right text-white/60">{item.label}</span>
                            <span className="w-[467px] text-justify">{item.value || ''}</span>
                        </div>
                    ))}
                </div>
                {/* 应征按钮 */}
                <ActivityButton
                    disabled={expertInfo?.teamStatus === TeamStatus.CanNotTeamUp}
                    onClick={handleClickExpertApply}
                >
                    {expertInfo?.teamStatus === TeamStatus.CanNotTeamUp ? '组队成功' : '我要应征'}
                </ActivityButton>
            </div>
        </StyleModal>
    );
});

export default ExpertInfoModal;
