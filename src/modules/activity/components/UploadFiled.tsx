/**
 * @file 决赛提交 附件上传组件，决赛前(9月)另外提需、上线。
 * <AUTHOR>
 */

import React, {useMemo, useState, useCallback} from 'react';
import styled from '@emotion/styled';
import {Upload} from 'antd';
import classNames from 'classnames';
import type {UploadFile} from 'antd/es/upload/interface';
import {AxiosProgressEvent} from 'axios';
import isNumber from 'lodash/isNumber';
import {FileIcon} from '@/modules/dataset/components/Common';
import api from '@/api/activity';

// 如果有样式文件或图片资源，可以在这里引入
const Dragger = styled(Upload.Dragger)`
    .ant-upload-list-picture-card {
        background: #f8f9fc;

        &::before {
            display: none !important;
            content: none;
        }

        &:has(.ant-upload-list-item-container) {
            padding: 12px !important;
            border-width: 1px;
            border-radius: 8px;
        }

        .ant-upload-list-item-container {
            height: auto !important;
            margin: 0px !important;
        }
    }
    .ant-upload-drag {
        border-width: 0px !important;
    }
`;

const StyledDiv = styled.div`
    #warper {
        #mask {
            visibility: hidden;
        }
        &:hover {
            #mask {
                visibility: visible;
            }
        }
    }
`;

const SUBMIT_ATTR_RANGE = {
    // name 属性的最大长度
    name: 20,
    // description 属性的最大长度
    description: 30,
    // 文档的大小 50M
    fileSize: 50,
    // 一次上传文档的个数
    onceUploadFileNumber: 10,
    // 文档的个数
    fileNumber: 100,
    // 支持的类型
    acceptType: '.zip,.rar',
};

interface FileCardProps {
    file: UploadFile;
    onRemove?: () => void;
}

const formateFileSize = (sizeBit: number) => {
    let size = sizeBit;
    const units = ['B', 'KB', 'MB', 'GB'];
    for (const unit of units) {
        if (size < 1024) {
            return `${size.toFixed(1)}${unit}`;
        }

        size = size / 1024;
    }
    return `${size}GB`;
};

// 展示已上传文件的文件信息
function UploadFileCard({file, onRemove}: FileCardProps) {
    const hasError = file.status === 'error';
    const status = file.status;
    const suffix = file.name.split('.').reverse()[0].toUpperCase();
    const isPicture = ['PNG', 'JPG', 'JPEG'].includes(suffix);
    // 文件上传后，后端需要进行处理，耗时较长，最后 10% 待后端返回后完成，防止出现进度条满了却迟迟上传不成功
    const percent = file.percent! > 90 ? (status === 'done' ? 100 : 90) : file.percent;

    const imageSrc = useMemo(
        () => (isPicture ? URL.createObjectURL(file.originFileObj as File) : ''),
        [file.originFileObj, isPicture]
    );

    return (
        <>
            <StyledDiv
                className={classNames('relative w-[300px] overflow-hidden rounded-lg bg-white', {
                    'border border-[#FFA7AA] border-opacity-40 bg-[#F8F0F3]': hasError,
                    'border-gray-border-secondary': !hasError,
                })}
            >
                <div id="warper" className="flex content-center items-center overflow-hidden">
                    <FileIcon name={file.name} className="m-[6px] h-12 w-12" src={imageSrc} />
                    <div className="ml-[.375rem] flex w-[calc(100%-66px)] flex-col items-start">
                        <span className="inline-block w-full overflow-hidden overflow-ellipsis whitespace-nowrap pr-3 text-sm leading-[22px]">
                            {file.name}
                        </span>
                        <span className="text-xs leading-[18px] text-flow-hover">
                            {suffix && suffix + ' · '}
                            {formateFileSize(file.size || 0)}
                        </span>
                    </div>
                    <div
                        id="mask"
                        className="absolute flex h-full w-full cursor-pointer rounded-lg bg-black bg-opacity-60 text-lg text-white"
                        onClick={onRemove}
                    >
                        <span className={classNames('iconfont icon-delete m-auto text-lg')} />
                    </div>
                    {status === 'uploading' && (
                        <div
                            className="absolute flex h-full overflow-hidden bg-[#F0F2F8] mix-blend-multiply"
                            style={{width: `${percent}%`, transition: 'width 0.1s ease'}}
                            onClick={onRemove}
                        />
                    )}
                </div>
            </StyledDiv>
            {hasError && (
                <p className="max-h-10 w-[300px] translate-y-1 overflow-hidden overflow-ellipsis text-xs leading-5 text-error">
                    {file.error}
                </p>
            )}
        </>
    );
}

// eslint-disable-next-line react-refresh/only-export-components
export const handleRenderFileItem = (
    originalNode: React.ReactNode,
    file: UploadFile,
    fileList: UploadFile[],
    {remove}: {remove: () => void}
) => {
    return <UploadFileCard file={file} onRemove={remove} />;
};

interface UploadFiledProps {
    setUploadFileLoading: (value: boolean) => void;
    setUploadFileFail: (value: boolean) => void;
}

// 定义 UploadFiled 组件
const UploadFiled: React.FC<UploadFiledProps> = ({setUploadFileLoading, setUploadFileFail}) => {
    // 组件逻辑（如果有）
    const [fileList, setFileList] = useState<UploadFile[] | undefined>([]);

    const handleCustomUpload = useCallback(
        async (option: any) => {
            setUploadFileLoading(true);
            const file = option.file as File;
            const handleUploadProgress = (e: AxiosProgressEvent) => {
                isNumber(e.progress) && option.onProgress({percent: (e.progress * 100).toFixed(1)});
            };

            try {
                const result = await api.uploadFile({file, onUploadProgress: handleUploadProgress});
                option.onSuccess(result);
                setUploadFileLoading(false);
                setUploadFileFail(false);
            } catch (error: any) {
                console.error('上传文件出错:', error);
                option.onError(error && error?.msg ? error?.msg : '网络错误');
                setUploadFileLoading(false);
                setUploadFileFail(true);
            }
        },
        [setUploadFileFail, setUploadFileLoading]
    );

    const handleBeforeUpload = useCallback(
        (file: UploadFile, currentFileList: UploadFile[]) => {
            const fileLength = fileList?.length ?? 0;
            // 第一个不合法的下标（超出单次或总共文件数量的限制）
            const firstIllegalIndex = Math.min(1 - fileLength, SUBMIT_ATTR_RANGE.onceUploadFileNumber);

            // 当前文件在文件列表中的位置
            const fileIndex = currentFileList.findIndex(currentFile => currentFile.uid === file.uid);

            // 根据下标决定该文件是否保留（超出单次或总共文件数量的限制）
            if (firstIllegalIndex <= fileIndex) {
                return Upload.LIST_IGNORE;
            }

            return true;
        },
        [fileList]
    );

    const handleUploadChange = useCallback((info: {file: UploadFile; fileList: UploadFile[]}) => {
        const newFileList = [...info.fileList];
        setFileList(newFileList);
    }, []);

    const fileUploaded = !!fileList?.length;

    return (
        <Dragger
            accept={SUBMIT_ATTR_RANGE.acceptType}
            multiple
            listType="picture-card"
            itemRender={handleRenderFileItem}
            fileList={fileList}
            beforeUpload={handleBeforeUpload}
            customRequest={handleCustomUpload}
            onChange={handleUploadChange}
        >
            <div
                className={classNames('rounded-md border border-dashed border-[#b7b9c1] py-6', {
                    hidden: fileUploaded,
                })}
            >
                <p className="mb-2 flex items-center justify-center text-sm font-normal leading-snug text-black">
                    <span className="iconfont icon-a-Maskgroup mr-2" />
                    <span>点击或将文件拖拽到这里上传</span>
                </p>

                <div className="mt-[0.3125rem] text-center text-xs font-normal leading-none text-black text-opacity-25">
                    <span>支持.zip, .rar格式文件</span>
                </div>
            </div>
        </Dragger>
    );
};

export default UploadFiled;
