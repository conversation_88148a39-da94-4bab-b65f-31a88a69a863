/**
 * @file 阅读参赛协议弹窗
 * <AUTHOR>
 */

import React, {forwardRef, useCallback, useEffect, useImperativeHandle, useState} from 'react';
import {Modal, Checkbox} from 'antd';
import {InfoCircleFilled, CloseCircleOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import urls from '@/links';
import {ScrollContainer} from '@/components/ScrollContainer';
import {useIsMobileStore} from '@/store/home/<USER>';
import Popup from '@/components/mobile/Popup';
import ActivityButton from './ActivityButton';

const StyleModal = styled(Modal)`
    width: 608px !important;
    .ant-modal-content {
        background-color: #06061c !important;
        color: #fff;
        width: 608px !important;
        border-radius: 6px !important;
        .ant-modal-header {
            margin-bottom: 24px;
        }
        .ant-modal-close {
            color: #848691;
        }
        .ant-modal-close:hover {
            color: rgba(110, 110, 110, 1);
        }
    }
    .ant-modal-mask {
        background-color: rgba(128, 128, 128, 0.8) !important;
    
    .ant-modal-footer {
        margin-top: 25px;
        .ant-btn {
            font-weight: 400;
        }
    }
`;

const StyleCheckBox = styled(Checkbox)`
    .css-dev-only-do-not-override-7azskz.ant-checkbox-checked .ant-checkbox-inner {
        background-color: #4e6ef2;
    }
`;

const AgreementModal = forwardRef((_, ref) => {
    const [modalOpen, setModalOpen] = useState<boolean>(false);
    const [checked, setChecked] = useState<boolean>(false);
    const [competitionType, setCompletionType] = useState<string>('');
    const [competitionId, setCompetitionId] = useState<number>();
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 对外暴露打开弹窗事件
    useImperativeHandle(ref, () => {
        return {
            showModal(competitionType: string, competitionId: number) {
                setCompletionType(competitionType);
                setCompetitionId(competitionId);
                setModalOpen(true);
            },
            closeModal() {
                setModalOpen(false);
            },
            open: modalOpen,
        };
    });

    useEffect(() => {
        if (modalOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    }, [modalOpen]);

    const handleCancel = useCallback(() => {
        setModalOpen(false);
    }, []);

    // 判断是否同意协议
    const handleAgree = useCallback((e: any) => {
        setChecked(e.target.checked);
    }, []);

    // 打开报名表单页面
    const handleSignUp = useCallback(() => {
        window.open(`${urls.activityDeveloperApply.raw()}?type=${competitionType}&id=${competitionId}`);
        setModalOpen(false);
    }, [competitionType, competitionId]);

    // 阻止整个弹窗的外层冒泡
    const handleClickInsideModal = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        event.stopPropagation();
    };

    return (
        <div onClick={handleClickInsideModal}>
            {isMobile ? (
                <Popup
                    title={competitionType}
                    onClose={handleCancel}
                    visible={modalOpen}
                    suffix={
                        <span
                            className="iconfont icon-close mr-[18px] text-[20px] text-gray-quaternary"
                            onClick={handleCancel}
                        ></span>
                    }
                    bodyStyle={{
                        paddingLeft: '17px',
                        paddingRight: '17px',
                        height: '85%',
                        backgroundColor: '#06061c',
                        color: '#fff',
                    }}
                >
                    <div className="flex flex-col items-center justify-center gap-[2vh]">
                        {/* 协议标题 */}
                        <div className="flex h-[5vh] w-11/12 items-center bg-[#e8f4ff] pl-4">
                            <InfoCircleFilled className="text-[20px] text-[#3073f2]" />
                            <span className="ml-2 text-[#141414]">请仔细阅读以下参赛者协议</span>
                        </div>

                        {/* 协议内容 */}
                        <ScrollContainer className="text-size-[14px] h-[50vh] w-11/12 overflow-auto rounded-lg bg-white/[0.1] p-4">
                            <p className="font-medium">参赛要求</p>
                            <ol className="ml-4 mt-1 list-decimal">
                                <li className="mb-1">
                                    参与者保证参赛行为不得侵犯百度公司及任何第三方的合法权益，不得涉及不健康、淫秽、色情或毁谤第三方等内容。
                                </li>
                                <li className="mb-1">
                                    参赛者在文心智能体平台上传内容授权文心智能体平台及其关联方、合作方使用，包括授权分发转载、复制、改编、在agent上添加文心智能体及其关联方商业标识以用于宣传、推广本比赛及所需的全部权利。
                                </li>
                                <li className="mb-1">
                                    不得使用已发布智能体参赛，且参赛周期内，参赛智能体及相关内容不得在其他平台上传、发布或做类似使用。
                                </li>
                                <li className="mb-1">
                                    垂直领域专家赛道涉及专家IP、肖像等授权，参赛者不得用于比赛以外任何目的。
                                </li>
                                <li className="mb-1">
                                    OPEN赛道若涉及合作方数据包提供，参赛者需保证数据安全，不得使用于比赛以外任何目的，不得在任何第三方平台发布工具包数据相关内容。
                                </li>
                                <li className="mb-1">
                                    赛事合作方基于其授权的IP/肖像/数据等内容，可在自身业务经营范围内使用参赛智能体。
                                </li>
                                <li className="mb-1">
                                    参赛者使用文心智能体应遵守《文心智能体平台协议》及相关平台规范要求。
                                </li>
                            </ol>
                            <p className="mt-4 font-medium">通用规则</p>
                            <ul className="ml-4 mt-1 list-decimal">
                                <li className="mb-1">参赛者需保证参赛内容均系原创，禁止抄袭。</li>
                                <li className="mb-1">
                                    主办方保留根据实际情况对未尽事宜进行解释、更新和处理的权利，包括比赛规则、比赛流程、评估标准、奖项设定等。参赛者可随时通过官方网站或其他公告，以获取最新的比赛信息。
                                </li>
                                <li className="mb-1">参赛过程不得违反法律、法规。</li>
                            </ul>
                        </ScrollContainer>

                        {/* 协议勾选框 */}
                        <StyleCheckBox
                            checked={checked}
                            onChange={handleAgree}
                            className="flex h-[3vh] w-11/12 items-center pl-4 text-white/60"
                        >
                            我已阅读并理解以上协议
                        </StyleCheckBox>
                        {/* 报名按钮 */}
                        <div className="flex h-[8vh] items-center justify-center">
                            <ActivityButton onClick={handleSignUp} disabled={!checked} size="large">
                                立即报名
                            </ActivityButton>
                        </div>
                    </div>
                </Popup>
            ) : (
                <StyleModal open={modalOpen} footer={null} onCancel={handleCancel} closeIcon={<CloseCircleOutlined />}>
                    <div className="my-4 flex flex-col items-center justify-center gap-4">
                        {/* 标题 */}
                        <h1 className="mb-1 text-center text-[22px] font-medium leading-7">{competitionType}</h1>
                        {/* 协议标题 */}
                        <div className="flex h-9 w-[528px] items-center bg-[#e8f4ff] pl-4">
                            <InfoCircleFilled className="text-[20px] text-[#3073f2]" />
                            <span className="ml-2 text-[#141414]">请仔细阅读以下参赛者协议</span>
                        </div>

                        {/* 协议内容 */}
                        <ScrollContainer className="text-size-[14px] h-[304px] w-[528px] overflow-auto rounded-lg bg-white/[0.1] p-4">
                            <p className="font-medium">参赛要求</p>
                            <ol className="ml-4 mt-1 list-decimal">
                                <li className="mb-1">
                                    参与者保证参赛行为不得侵犯百度公司及任何第三方的合法权益，不得涉及不健康、淫秽、色情或毁谤第三方等内容。
                                </li>
                                <li className="mb-1">
                                    参赛者在文心智能体平台上传内容授权文心智能体平台及其关联方、合作方使用，包括授权分发转载、复制、改编、在agent上添加文心智能体及其关联方商业标识以用于宣传、推广本比赛及所需的全部权利。
                                </li>
                                <li className="mb-1">
                                    不得使用已发布智能体参赛，且参赛周期内，参赛智能体及相关内容不得在其他平台上传、发布或做类似使用。
                                </li>
                                <li className="mb-1">
                                    垂直领域专家赛道涉及专家IP、肖像等授权，参赛者不得用于比赛以外任何目的。
                                </li>
                                <li className="mb-1">
                                    OPEN赛道若涉及合作方数据包提供，参赛者需保证数据安全，不得使用于比赛以外任何目的，不得在任何第三方平台发布工具包数据相关内容。
                                </li>
                                <li className="mb-1">
                                    赛事合作方基于其授权的IP/肖像/数据等内容，可在自身业务经营范围内使用参赛智能体。
                                </li>
                                <li className="mb-1">
                                    参赛者使用文心智能体应遵守《文心智能体平台协议》及相关平台规范要求。
                                </li>
                            </ol>
                            <p className="mt-4 font-medium">通用规则</p>
                            <ul className="ml-4 mt-1 list-decimal">
                                <li className="mb-1">参赛者需保证参赛内容均系原创，禁止抄袭。</li>
                                <li className="mb-1">
                                    主办方保留根据实际情况对未尽事宜进行解释、更新和处理的权利，包括比赛规则、比赛流程、评估标准、奖项设定等。参赛者可随时通过官方网站或其他公告，以获取最新的比赛信息。
                                </li>
                                <li className="mb-1">参赛过程不得违反法律、法规。</li>
                            </ul>
                        </ScrollContainer>

                        {/* 协议勾选框 */}
                        <StyleCheckBox
                            checked={checked}
                            onChange={handleAgree}
                            className="flex h-[10px] w-[528px] items-center pl-4 text-white/60"
                        >
                            我已阅读并理解以上协议
                        </StyleCheckBox>
                        {/* 报名按钮 */}
                        <ActivityButton onClick={handleSignUp} disabled={!checked}>
                            立即报名
                        </ActivityButton>
                    </div>
                </StyleModal>
            )}
        </div>
    );
});

export default AgreementModal;
