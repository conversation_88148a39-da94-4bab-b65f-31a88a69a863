/**
 * @file 首页-嘉宾介绍-嘉宾卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';
import guestCardBg from '@/modules/activity/assets/guest-card-bg.png';

const CustomDiv = styled.div`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${guestCardBg}) !important;
`;

interface GuestCardProps {
    className?: string;
    info?: any;
}

const GuestCard: React.FC<GuestCardProps> = ({className, info}) => {
    return (
        <>
            <CustomDiv
                className={classNames(
                    'relative flex h-[30vw] w-[17.98vw] flex-col items-center rounded-xl bg-contain bg-center bg-no-repeat mlg:h-[216px] mlg:w-[129.46px]',
                    className
                )}
            >
                {/* 信息展示区 */}
                <div className="mt-[10px] flex w-[17.98vw] flex-col items-center justify-center gap-[11px] text-center text-white mlg:mt-[24px] mlg:h-[216px] mlg:w-[129.46px] ">
                    {/* 头像 */}
                    <div className="h-[68.5px] w-[46.5px] overflow-hidden rounded-t-[4px] rounded-bl-[4px] mlg:h-[137px] mlg:w-[93px] mlg:rounded-t-[10px] mlg:rounded-bl-[10px]">
                        <img src={info.image}></img>
                    </div>
                    {/* 姓名 */}
                    <p className="text-sm mlg:text-[21.5px]">{info.name}</p>
                    {/* 职位 */}
                    <p className="mt-[-8px] w-[100px] whitespace-pre-wrap text-[8px] font-light text-white/60 mlg:w-[200px] mlg:text-[16px]">
                        {info.title}
                    </p>
                </div>
            </CustomDiv>
        </>
    );
};

export default GuestCard;
