/**
 * @file 头像组件
 * <AUTHOR>
 */

import React, {useCallback, useRef, useState} from 'react';
import {Avatar, Dropdown, Button, DropdownProps} from 'antd';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import defaultAvatar from '@/assets/default-usert-avatar.png';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useIsMobileStore} from '@/store/home/<USER>';
import LogoutWarningModal, {LogoutWarningModalRef} from '@/components/Login/LogoutModal';

const UserAvatar = () => {
    const [userInfoData, isLogin] = useUserInfoStore(store => [store.userInfoData, store.isLogin]);
    const isMobile = useIsMobileStore(store => store.isMobile);

    const [dropdownOpen, setDropdownOpen] = useState(false);
    const logoutRef = useRef<LogoutWarningModalRef>(null);

    const logoutConfirm = useCallback(() => {
        logoutRef?.current?.logoutClickHandler();
        setDropdownOpen(false);
    }, []);

    // 弹窗开启时关闭下拉框
    const handleOpenChange: DropdownProps['onOpenChange'] = useCallback((dropdownOpen: boolean) => {
        setDropdownOpen(dropdownOpen);
    }, []);

    const uniformLogin = useUniformLogin();

    const dropdown = useCallback(() => {
        return (
            <div className="text-[16px] font-medium">
                <div className="z-10 flex w-fit min-w-[129px] flex-col justify-center rounded-[9px] bg-white px-[4px] py-[4px] shadow-[0_0px_40px_0px_rgba(29,34,82,0.2)]">
                    <div
                        className="h-[38px] cursor-pointer rounded-[6px] px-[10px] leading-[38px] hover:bg-colorAgentItemHoverBg"
                        onClick={logoutConfirm}
                    >
                        <span className="text-black-base">退出登录</span>
                    </div>
                </div>
            </div>
        );
    }, [logoutConfirm]);
    return (
        <>
            <Dropdown dropdownRender={dropdown} onOpenChange={handleOpenChange} open={dropdownOpen} disabled={!isLogin}>
                {isLogin ? (
                    <div className="absolute right-3 top-3 flex cursor-pointer items-center mlg:right-10 mlg:top-6">
                        <Avatar
                            src={userInfoData?.userInfo.portrait || defaultAvatar}
                            size={isMobile ? 24 : 36}
                            className="mr-2 flex-shrink-0 border-none"
                        />
                        {!isMobile && (
                            <span className="float-right inline-block overflow-hidden overflow-ellipsis whitespace-nowrap text-[16px] font-medium text-white">
                                {userInfoData?.userInfo.name || ''}
                            </span>
                        )}
                    </div>
                ) : (
                    <div>
                        <Button
                            onClick={uniformLogin}
                            className="absolute right-3 top-3 text-[12px] mlg:right-5 mlg:top-6 mlg:text-base"
                            type="primary"
                            size={isMobile ? 'small' : 'middle'}
                        >
                            登录
                        </Button>
                    </div>
                )}
            </Dropdown>
            <LogoutWarningModal ref={logoutRef} />
        </>
    );
};

export default UserAvatar;
