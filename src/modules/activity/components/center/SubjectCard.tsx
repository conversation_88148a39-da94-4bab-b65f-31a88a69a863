/**
 * @file 统一封装 赛事简介卡片
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import classNames from 'classnames';
import techSubjectBg from '@/modules/activity/assets/tech-subject-card-bg.png';
import urls from '@/links';
import ActivityButton from '@/modules/activity/components/ActivityButton';
import AgreementModal from '@/modules/activity/components/AgreementModal';
import {COMPETITION_TYPE, CompetitionTypeBgMap, PP_PAGE_URL} from '@/modules/activity/constants';
import {useUniformLogin} from '@/components/Login/utils/uniformLogin';
import {useUserInfoStore} from '@/store/login/userInfoStore';
import api from '@/api/activity/index';
import {QuestionnaireType, SubmitStatus} from '@/api/activity/interface';
import {sendLogV2} from '@/utils/loggerV2/useUbcLoggerV2';
import {EVENT_PAGE_CONST, EVENT_VALUE_CONST, EVENT_EXT_KEY_CONST} from '@/utils/loggerV2/constants';
import {CompetitionTypeLogExt} from '@/utils/loggerV2/interface';
import {EVENT_TYPE} from '@/utils/loggerV2/ubcLoggerV2';
import {useIsMobileStore} from '@/store/home/<USER>';

interface SubjectCardProps {
    className?: string;
    competitionType?: string;
    competitionId?: number;
    isTechType?: boolean;
}

const SubjectCard: React.FC<SubjectCardProps> = ({className, competitionType, competitionId, isTechType = false}) => {
    const agreementModalRef = useRef<any>(null);
    const isLogin = useUserInfoStore(store => store.isLogin);
    const isMobile = useIsMobileStore(store => store.isMobile);

    // 该赛事是否报名
    const [hasApplied, setHasApplied] = useState<boolean>(false);

    const getDeveloperApplyStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.DeveloperApply,
                });
                setHasApplied(res.submitStatus === SubmitStatus.Submitted);
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId]);

    const getExpertApplyStatus = useCallback(async () => {
        if (competitionId) {
            try {
                const res = await api.getQuestionnaireStatus({
                    competitionId,
                    questionnaireType: QuestionnaireType.ExpertApply,
                });
                setHasApplied(res.submitStatus === SubmitStatus.Submitted);
            } catch (e) {
                throw e;
            }
        }
    }, [competitionId]);

    // 请求问卷状态
    useEffect(() => {
        if (isLogin) {
            if (competitionType === COMPETITION_TYPE.expert) {
                getExpertApplyStatus();
            } else {
                getDeveloperApplyStatus();
            }
        }
    }, [isLogin, competitionType, getDeveloperApplyStatus, getExpertApplyStatus]);

    // 打开赛事详情页
    const openActivityDetail = useCallback(() => {
        window.open(`${urls.nvidiaActivityDetail.raw()}?type=${competitionType}&id=${competitionId}`);
    }, [competitionType, competitionId]);

    // 打开组队页
    const openTeam = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            window.open(`${urls.activityTeam.raw()}?id=${competitionId}`);
            event.stopPropagation();
        },
        [competitionId]
    );

    // 调登录
    const uniformLogin = useUniformLogin();

    // 打开阅读协议弹窗
    const handleAgreementModal = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                agreementModalRef.current && agreementModalRef.current.showModal(competitionType, competitionId);
            } else {
                uniformLogin();
            }

            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    const handleClickSubmitWork = useCallback(
        (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
            if (isLogin) {
                window.open(`${urls.activitySubmitWork.raw()}?type=${competitionType}&id=${competitionId}`);
            } else {
                uniformLogin();
            }

            event.stopPropagation(); // 阻止事件冒泡到外层容器
        },
        [isLogin, competitionType, competitionId, uniformLogin]
    );

    // 跳转PP飞浆开发者社区
    const openPaddle = useCallback(() => {
        sendLogV2(EVENT_TYPE.CLICK, EVENT_VALUE_CONST.NV_TOPIC, EVENT_PAGE_CONST.NV_TOPIC_DETAIL, {
            [EVENT_EXT_KEY_CONST.NV_TOPIC_NAME]: CompetitionTypeLogExt.Tech,
        });
        window.open(PP_PAGE_URL);
    }, []);

    return isTechType ? (
        // 技术赛题 赛题卡片
        <div
            style={{backgroundImage: `url(${techSubjectBg})`}}
            className={classNames(
                'relative h-[48.7vw] w-[90vw] cursor-pointer rounded-md bg-cover bg-center bg-no-repeat mlg:h-[541px] mlg:w-[1000px]',
                className
            )}
            onClick={openPaddle}
        >
            {/* 底部按钮区域 */}
            <footer className="absolute bottom-3 flex w-full items-end justify-center gap-6 mlg:bottom-[30px] mlg:w-[1000px]">
                <ActivityButton size={isMobile ? 'middle' : 'large'}>查看入围决赛名单</ActivityButton>
            </footer>

            <AgreementModal ref={agreementModalRef} />
        </div>
    ) : (
        // 智能体应用赛题 赛题卡片
        <div
            style={{backgroundImage: `url(${CompetitionTypeBgMap[competitionType!]})`}}
            className={classNames(
                'relative h-[22.6vw] w-[42vw] cursor-pointer rounded-md bg-cover bg-center bg-no-repeat mlg:h-[267px] mlg:w-[493px]',
                className
            )}
            onClick={openActivityDetail}
        >
            {/* 10.17 决赛不展示赛事详情 */}
            {/* {!isMobile && (
                <div className="absolute right-3 top-2 text-xs font-semibold text-white">
                    赛事详情
                    <span className="iconfont icon-right ml-1 text-xs"></span>
                </div>
            )} */}

            {/* 底部按钮区域 */}
            <footer className="absolute bottom-[2px] z-10 flex w-full items-end justify-center gap-3 px-[20px] mlg:bottom-4 mlg:w-[493px] mlg:gap-6">
                {/* 10.10 决赛关闭入口 */}
                {false &&
                    (competitionType === COMPETITION_TYPE.expert ? (
                        hasApplied ? (
                            <>
                                <ActivityButton onClick={openTeam}>我要组队</ActivityButton>
                                <ActivityButton onClick={handleClickSubmitWork}>提交作品</ActivityButton>
                            </>
                        ) : (
                            <ActivityButton onClick={openTeam}>我要组队</ActivityButton>
                        )
                    ) : hasApplied ? (
                        <ActivityButton onClick={handleClickSubmitWork}>提交作品</ActivityButton>
                    ) : (
                        <ActivityButton onClick={handleAgreementModal}>立即报名</ActivityButton>
                    ))}
                <ActivityButton>查看入围决赛名单</ActivityButton>
            </footer>
            <AgreementModal ref={agreementModalRef} />
        </div>
    );
};

export default SubjectCard;
