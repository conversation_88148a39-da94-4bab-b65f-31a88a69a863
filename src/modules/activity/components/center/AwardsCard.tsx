/**
 * @file 官网首页-奖项设置卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';
import awardsCardBg from '@/modules/activity/assets/awards-card-bg.png';
import awardsCardBgLong from '@/modules/activity/assets/awards-card-bg-l.png';

const CustomDiv = styled.div`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${awardsCardBg}) !important;
`;
const CustomLongDiv = styled.div`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${awardsCardBgLong}) !important;
`;

interface AwardsCardProps {
    className?: string;
    isLong?: boolean;
    isTech?: boolean;
    info: any;
}

const AwardsCard: React.FC<AwardsCardProps> = ({className, info, isLong = false, isTech = false}) => {
    return (
        <>
            {isLong ? (
                <CustomLongDiv
                    className={classNames(
                        'relative flex h-[20.5vw] w-[41.5vw] flex-col items-center rounded bg-cover bg-no-repeat mlg:h-[192px] mlg:w-[493px] mlg:rounded-xl',
                        className
                    )}
                >
                    {/* 信息展示区 */}
                    <div className="mt-[2px] flex flex-col items-center justify-center text-center text-white sm:gap-[18px] mlg:mt-[17px]">
                        {/* 奖项名称 */}
                        <h1 className="text-xs font-semibold leading-[30px] mlg:text-[24px] mlg:leading-[33.6px]">
                            {info.name}
                        </h1>
                        {/* 奖励人数 */}
                        <div className="flex h-[18px] w-[8.71vw] min-w-[58px] items-start justify-center rounded-full bg-white/[0.09] mlg:h-[35.5px] mlg:min-w-[99px] mlg:px-4">
                            <span className="text-[8px] leading-[18px] mlg:text-[14px] mlg:leading-[40px]">
                                {isTech ? '' : info.name === '智能体总冠军' ? '' : '每赛题'}
                            </span>
                            <span className="text-[8px] leading-[18px] mlg:text-[24px] mlg:leading-[35px]">
                                {info.num}
                            </span>
                            <span className="text-[8px] leading-[18px] mlg:text-[14px] mlg:leading-[40px]">名</span>
                        </div>
                        {/* 底部奖励金额 */}
                        <p className="mt-[-4px] font-['baidunumber_Medium'] text-base font-medium leading-[36px] text-[#2bb9e1] mlg:text-[42px] mlg:leading-[67.5px]">
                            {info.reward}
                            <span className="text-[8px] leading-[30px] mlg:text-[18px]">元</span>
                        </p>
                    </div>
                </CustomLongDiv>
            ) : (
                <CustomDiv
                    className={classNames(
                        'relative flex h-[18.14vw] w-[19.71vw] flex-col items-center rounded bg-cover bg-no-repeat mlg:h-[219px] mlg:w-[238px] mlg:rounded-xl',
                        className
                    )}
                >
                    {/* 信息展示区 */}
                    <div className="mt-[3vw] flex flex-col items-center justify-center gap-[4px] text-center text-white mlg:mt-[25px] mlg:gap-[12px] ">
                        {/* 奖项名称 */}
                        <h1 className="text-[10px] font-semibold mlg:text-[24px] mlg:leading-[34px]">{info.name}</h1>
                        {/* 奖励人数 */}
                        <div className="flex h-[18px] w-[8.71vw] min-w-[50px] items-start justify-center rounded-full bg-white/[0.09] mlg:h-[35.5px] mlg:w-fit mlg:min-w-[99px] mlg:px-4">
                            <span className="text-[8px] leading-[18px] mlg:text-[14px] mlg:leading-[40px]">
                                {isTech ? '' : info.name === '智能体人气奖' ? '' : '每赛题'}
                            </span>
                            <span className="text-[8px] leading-[18px] mlg:text-[24px] mlg:leading-[35px]">
                                {info.num}
                            </span>
                            <span className="text-[8px] leading-[18px] mlg:text-[14px] mlg:leading-[40px]">名</span>
                        </div>
                        {/* 底部奖励金额 */}
                        <p className="leading-[30px] text-[#2bb9e1] xs:leading-[30px] mlg:mt-4 mlg:leading-[67px]">
                            {info.name === '智能体人气奖' ? (
                                <span className="font-['baidunumber_Medium'] text-[13px] font-medium leading-[16px] mlg:text-[36px] mlg:leading-[42px]">
                                    {info.reward}
                                </span>
                            ) : (
                                <>
                                    <span className="font-['baidunumber_Medium'] text-base font-medium mlg:text-[42px]">
                                        {info.reward}
                                    </span>
                                    <span className="text-[8px] mlg:text-[18px]">元</span>
                                </>
                            )}
                        </p>
                    </div>
                </CustomDiv>
            )}
        </>
    );
};

export default AwardsCard;
