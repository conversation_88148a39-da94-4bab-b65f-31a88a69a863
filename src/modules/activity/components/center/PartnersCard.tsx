/**
 * @file 合作伙伴卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';

const CustomDiv = styled.div`
    border: 1px solid !important;
    border-radius: 10px !important;
    border-image-source: linear-gradient(
        176.48deg,
        #535f83 -16.77%,
        rgba(83, 95, 131, 0) 30.34%,
        rgba(83, 95, 131, 0.1) 96.81%
    ) !important;
    background: linear-gradient(180deg, rgba(78, 85, 100, 0.4) 9.97%, rgba(18, 20, 26, 0.4) 90.03%) !important;
    box-shadow: 0px 13.68px 20.51px 0px #00000066 !important;
`;

interface PartnersCardProps {
    className?: string;
    info?: any;
    ranking?: boolean;
}

const PartnersCard: React.FC<PartnersCardProps> = ({className, info, ranking = false}) => {
    return (
        <>
            <CustomDiv
                className={classNames(
                    'relative flex w-full flex-col items-start rounded-xl bg-center bg-no-repeat px-[18px] pb-3 mlg:w-[1000px] mlg:items-center mlg:p-0',
                    className
                )}
            >
                {/* 信息展示区 */}
                <div className="mr-8 flex-col justify-center text-white mlg:my-[17px] mlg:ml-[42px] mlg:flex mlg:flex-row mlg:gap-[15px] ">
                    {/* 描述 */}
                    <div className="flex items-center mlg:h-[40px] mlg:w-[134px]">
                        <p className="text-[11px] leading-[30px] mlg:text-[16.7px] mlg:leading-[24px]">
                            {info.category}
                        </p>
                    </div>
                    {/* 详细logo */}
                    <div className="flex w-full flex-wrap items-center gap-2 mlg:w-[780px] mlg:gap-x-5 mlg:gap-y-4">
                        {info.image.map((item: string) => (
                            <div
                                key={item}
                                className="h-[5.6vw] w-[21vw] overflow-hidden rounded mlg:h-[40px] mlg:w-[140px]"
                            >
                                <img src={item}></img>
                            </div>
                        ))}
                        {ranking && (
                            <p className="mt-auto text-right text-[10px] text-white/40 mlg:text-sm">*排名不分先后</p>
                        )}
                    </div>
                </div>
            </CustomDiv>
        </>
    );
};

export default PartnersCard;
