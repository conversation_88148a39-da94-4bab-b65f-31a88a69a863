/**
 * @file 首页-赛程卡片
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';
import scheduleCardBg from '@/modules/activity/assets/schedule-card-bg.png';

const CustomDiv = styled.div`
    background: linear-gradient(180.43deg, #373c60 -1.43%, #000000 99.63%);
    background-image: url(${scheduleCardBg}) !important;
`;

interface ScheduleCardProps {
    className?: string;
    info: any;
}

const ScheduleCard: React.FC<ScheduleCardProps> = ({className, info}) => {
    return (
        <>
            <CustomDiv
                className={classNames(
                    'relative flex h-[120px] w-[140px] flex-col items-center overflow-visible rounded-xl bg-contain bg-center bg-no-repeat mlg:h-[179px] mlg:w-[179px]',
                    className
                )}
            >
                {/* 信息展示区 */}
                <div className="mt-[8px] flex w-[140px] flex-col items-center justify-center gap-1 overflow-visible text-center text-white mlg:w-[190px] mlg:gap-5 ">
                    {/* 阶段 */}
                    <h1 className="text-base font-semibold mlg:text-[32px] mlg:leading-[44.8px]">{info.step}</h1>
                    {/* 日期 */}
                    <p className="mt-[1px] text-xs font-medium mlg:text-[20px] mlg:leading-[28px]">{info.date}</p>
                    {/* 描述 */}
                    <p className="text-[8px] text-white/60 mlg:mt-[-12px] mlg:text-[14px] mlg:leading-[19.6px]">
                        {info.desc}
                    </p>
                </div>
            </CustomDiv>
        </>
    );
};

export default ScheduleCard;
