/**
 * @file 课程卡片
 * <AUTHOR>
 */

import React, {useCallback} from 'react';
import classNames from 'classnames';

interface ClassCardProps {
    className?: string;
    info?: any;
}

const ClassCard: React.FC<ClassCardProps> = ({className, info}) => {
    const openClass = useCallback(() => {
        window.open(info.url);
    }, [info.url]);

    return (
        <>
            <div
                className={classNames(
                    'relative flex w-[40vw] cursor-pointer flex-col gap-2 text-white xs:w-[42vw] mlg:w-[240px] mlg:gap-4',
                    className
                )}
                onClick={openClass}
            >
                {/* 图片 */}
                <img src={info.image} className="rounded-lg object-cover mlg:h-[126px]"></img>
                {/* 标题 */}
                <span className="line-clamp-2 whitespace-normal text-[10px] mlg:text-[12px]">
                    {info?.describe || ''}
                </span>
            </div>
        </>
    );
};

export default ClassCard;
