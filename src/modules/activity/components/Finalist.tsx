/**
 * @file 决赛入围名单 组件（纯静态展示）
 * <AUTHOR>
 * @date 2024/10/16
 */

import React from 'react';
import {Table} from 'antd';
import styled from '@emotion/styled';
import type {ColumnsType} from 'antd/es/table';
import BlockContainer from '@/modules/activity/components/BlockContainer';
import ActivityTitle from '@/modules/activity/components/ActivityTitle';
import {FinalsListItem} from '@/api/activity/interface';

const StyledTable = styled(Table)`
    .ant-table-cell {
        color: rgba(255, 255, 255, 0.8) !important;
        border: none !important;

        &::before {
            display: none !important;
        }
    }
    .ant-table-cell-row-hover {
        background-color: #06061b !important;
    }

    .ant-table-thead {
        background-color: #1f1f32;
    }
    .ant-table-tbody {
        background-color: #0d0d22;
    }

    .ant-pagination {
        .ant-pagination-item {
            background-color: #17172b;
            a {
                color: rgba(255, 255, 255, 0.8) !important;
            }
        }

        .ant-pagination-item-active {
            border: none !important;
            font-weight: 400 !important;
            background-color: #383848;
        }
        .ant-pagination-item-link {
            color: rgba(255, 255, 255, 0.8) !important;
        }
        .ant-pagination-prev {
            background-color: #17172b;
        }
        .ant-pagination-next {
            background-color: #17172b;
        }
    }
` as unknown as typeof Table;

const FINAL_LIST_TABLE_TITLE = {
    NAME: '智能体名称',
    LINK: '智能体链接',
    PRELIMINARY_SCORE: '智能体初赛分数',
};

const columns: ColumnsType<FinalsListItem> = [
    {
        title: FINAL_LIST_TABLE_TITLE.NAME,
        dataIndex: 'agentName',
        key: 'agentName',
        ellipsis: true,
        align: 'center',
    },
    {
        title: FINAL_LIST_TABLE_TITLE.LINK,
        dataIndex: 'agentId',
        key: 'agentId',
        // 跳转智能体预览
        render: (agentId: string) => (
            <a
                href={`https://agents.baidu.com/center/agent/preview/${agentId}`}
                className="text-[#68BCE1]"
                target="_blank"
                rel="noreferrer"
            >
                点击体验
            </a>
        ),
        align: 'center',
    },
];

/**
 * 活动标题组件
 *
 * @param list 入围决赛名单信息
 * @returns 返回一个React元素
 */
const Finalist: React.FC<{list: FinalsListItem[]}> = ({list}) => {
    return (
        <BlockContainer>
            <ActivityTitle>决赛入围名单</ActivityTitle>
            <StyledTable
                className="static mt-10 max-w-[800px] text-sm"
                dataSource={list}
                columns={columns}
                rowKey="datasetId"
                bordered={false}
                pagination={{
                    position: ['bottomCenter'],
                }}
            />
        </BlockContainer>
    );
};

export default Finalist;
