/**
 * @file 区块容器
 * <AUTHOR>
 */

import React from 'react';
import styled from '@emotion/styled';
import classNames from 'classnames';
import rightBg from '@/modules/activity/assets/right-block-container-bg.png';
import leftBg from '@/modules/activity/assets/left-block-container-bg.png';
import {useIsMobileStore} from '@/store/home/<USER>';
interface BlockContainerProps {
    children: React.ReactNode;
    className?: string;
    noneBackground?: boolean;
}

const BlockContainer: React.FC<BlockContainerProps> = ({children, className, noneBackground = false}) => {
    const isMobile = useIsMobileStore(store => store.isMobile);

    const CustomDiv = styled.div`
        background-image: url(${rightBg}), url(${leftBg});
        background-size: ${isMobile ? '80px' : '150px'} auto !important;
        background-position:
            left center,
            right center; /* 左侧图片居左，右侧图片居右 */
    `;

    return (
        <CustomDiv
            className={classNames(
                'flex w-full flex-col items-center bg-[#06061B] bg-contain bg-no-repeat px-6 py-5 mlg:min-w-[1000px] mlg:p-0 mlg:py-[30px]',
                {
                    'bg-none': noneBackground,
                },
                className
            )}
        >
            {children}
        </CustomDiv>
    );
};

export default BlockContainer;
