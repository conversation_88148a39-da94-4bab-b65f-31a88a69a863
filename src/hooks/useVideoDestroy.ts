import {useEffect, useRef} from 'react';
/**
 * 使用<video>标签后，组件卸载时需要销毁
 * 避免浏览器The play() request was interrupted报错
 * @returns ref
 */
export function useVideoDestroy() {
    const videoRef = useRef<HTMLVideoElement>(null);
    const video = videoRef.current;

    useEffect(() => {
        return () => {
            if (video) {
                !video.paused && video.pause();
                video.src = '';
                video.load();
            }
        };
    }, [video]);

    return videoRef;
}
