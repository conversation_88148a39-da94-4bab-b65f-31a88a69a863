import {useCallback, useContext} from 'react';
import PreLoadContext, {Context} from './context';

export const usePreLoadContext = () => useContext(PreLoadContext);

export const PreLoadContextProvider = PreLoadContext.Provider;

export const usePreLoadPage = () => {
    const pageModule = usePreLoadContext().module;
    return useCallback(
        (page: keyof Required<Context>['module']) => {
            if (!pageModule) return;
            pageModule[page].preload();
        },
        [pageModule]
    );
};
