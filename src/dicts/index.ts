/**
 * 项目共用的基础常量
 */

// 布局最大宽度
export const LAYOUT_MAX_WIDTH = 1680;

// 布局最小宽度
export const LAYOUT_MIN_WIDTH = 1280;

// 侧边栏宽度
export const SIDEBAR_WIDTH = 280;

// 内容区padding
export const CONTENT_PADDING = 32;

// loading图标
export const LingJingLoadingIcon =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/lingjing-loading-icon.png';

// lingjing平台logo
export const LingJingIcon =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/lingjing-platform/lingjing-icon.png';

export const AgentDefaultAvatar =
    'https://now.bdstatic.com/stash/v1/6f672d5/lingjing-fe/07ccbd4/agent-default-avatar.png';

export const enum LuiSdkScene {
    /**
     * 新建
     */
    Create = 1,
    /**
     * 编辑
     */
    Edit = 2,
    /**
     * 从复制新建
     */
    NewDuplicate = 3,
    /**
     * 查看公开配置
     */
    ViewConfig = 4,
    /**
     * 巧舱分流入口
     */
    QiaoCangEntry = 5,
    /**
     * 工作流模式
     */
    Workflow = 6,
}
