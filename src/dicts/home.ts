/* 文档中心url */
const DOCS_URL = 'https://agents.baidu.com/docs';
/* 开发者社区 */
const COMMUNITY_URL = 'https://agents.baidu.com/community';
/** 文心智能体平台 - 成长中心 */
const AGENT_ACTIVITY_URL = `https://agents.baidu.com/activity/list`;

const PLUGIN_HOST = 'plugin.baidu.com';

const AGENT_HOST = 'agents.baidu.com';

const CREATEDOC_URL = `${DOCS_URL}/intelligent-agent/zero_code_develop/`;

const DEVELOPDOC_URL = `${DOCS_URL}/develop/devtools/history/`;

const INDUSTRYDOC_URL = `${DOCS_URL}/develop/introduction/industry_qualification/`;

const DATASETDOC_URL = `${DOCS_URL}/dataset/dataset_docs/`;

/* 知识库上传文件说明 */
const DATASET_UPLOAD_URL = `${DOCS_URL}/develop/dataset/dataset_upload/`;

/* 知识库召回优化指导 */
const DATASET_SEARCH_URL = `${DOCS_URL}/develop/dataset/application_search/`;

const DATASETEXAMPLEDOC_URL = `${DOCS_URL}/dataset/dataset_docs_example/`;

const DEVELOPFROMSCRATCHDOC_URL = `${DOCS_URL}/develop/plugin/ability-plugin/basic/quick_pass/`;

const SERVICE_AGREEMENT = `${DOCS_URL}/operations/service_agreement/`;

const VIDEO_PRODUCT_INTRODUCTION_URL = `${DOCS_URL}/tutorial/video_zero_agents/`;

const BAIJIAHAO_SERVICE_AGREEMENT = 'https://baijiahao.baidu.com/docs/#/markdownsingle/BaiJiaHaoFuWuXieYi';

const BAIJIAHAO_HOME = 'https://baijiahao.baidu.com/builder/rc/home';

/** 百家号-官网首页 */
const BJH_OFFICIAL_WEB = 'https://baijiahao.baidu.com';

/** 百家号-变现中心-结算中心 */
const BJH_ACCOUNT_PAY = 'https://baijiahao.baidu.com/builder/rc/accountcenter/month';

/** 线索售卖协议 */
const LEAD_SELL_AGREEMENT = `${DOCS_URL}/operations/clue_sales_agreement/`;
/** 提现规则说明(联盟分成) */
const DOC_WITHDRAWAL_RULES = `${DOCS_URL}/develop/component/division/withdrawal_rules/`;

/** 提现规则说明(广告分成/线索售卖) */
const DOC_AD_LEAD_WITHDRAWAL_RULES = `${DOCS_URL}/develop/component/adLead/withdraw_rules/`;

/** 百度 CEP 支付结算页面 */
const CEP_SETTLEMENT_PAYMENT_URL = 'https://cep.baidu.com/new/settlement/payment/11?periodType=1';

/** 企业用户提现流程说明 */
const DOC_WITHDRAWAL_PROCESS = `${DOCS_URL}/develop/component/division/enterprise_users_flow/`;

const AGREEMENT_URL = 'https://agents.baidu.com/agreement';

/** 插件效果评估集规范文档 */
const URL_DOC_EVALUATION_RULES = `${DOCS_URL}/operations/plugin_issue_rule/`;

/** 数字形象上传接口路径 */
const UPLOAD_DIGITAL_FIGTURE_ACTION = '/lingjing/agent/uploadImage';
/** 智能体对外输出-API调用-文档 */
const URL_DOC_OUTPUT_AGENT_API = `${DOCS_URL}/external-deploy/API_calls/`;

/** 智能体对外输出-JsSDK-文档 */
const URL_DOC_OUTPUT_AGENT_JS_SDK = `${DOCS_URL}/external-deploy/js_code_insert/`;

/** Agent配置完整度优化指导 */
const URL_DOC_TUNING_AGENT_CONF = `${DOCS_URL}/develop/agent/tuning/#%E9%85%8D%E7%BD%AE%E5%AE%8C%E6%95%B4%E5%BA%A6`;
/** Agent耗时与稳定性优化指导 */
const URL_DOC_TUNING_AGENT_STABLE = `${DOCS_URL}/develop/agent/tuning/#%E8%80%97%E6%97%B6%E4%B8%8E%E7%A8%B3%E5%AE%9A%E6%80%A7`;
/** Agent其他调优优化指导 */
const URL_DOC_TUNING_AGENT_OTHER = `${DOCS_URL}/develop/agent/tuning/#%E5%85%B6%E4%BB%96%E8%B0%83%E4%BC%98%E5%8F%82%E8%80%83`;

const URL_DOC_DATASET_QA = `${DOCS_URL}/develop/dataset/dataset_docs_QA/`;

const URL_DOC_TUTORIAL_CENTER = `${DOCS_URL}/tutorial/video_productintroduction/`;

/** 灯塔协议文档 */
const LIGHTHOUSE_RULE = `${DOCS_URL}/develop/agent/lighthouse_rule/`;

const COMMUNITY_QR_CODE_KEY = 'community';

const SMART_SERVICE_KEY = 'smart-service';

const XINXIANG_HOME = 'https://xinxiang-homepage.now.baidu.com/';
const XINXIANG_HOST = 'xinxiang.baidu.com';
const XINXIANG_PROXY_HOST = 'xinxiang-proxy.baidu.com';

export default {
    DOCS_URL,
    COMMUNITY_URL,
    AGENT_ACTIVITY_URL,
    PLUGIN_HOST,
    AGENT_HOST,
    CREATEDOC_URL,
    DEVELOPDOC_URL,
    DEVELOPFROMSCRATCHDOC_URL,
    SERVICE_AGREEMENT,
    VIDEO_PRODUCT_INTRODUCTION_URL,
    BAIJIAHAO_SERVICE_AGREEMENT,
    BAIJIAHAO_HOME,
    BJH_OFFICIAL_WEB,
    BJH_ACCOUNT_PAY,
    LEAD_SELL_AGREEMENT,
    DOC_WITHDRAWAL_RULES,
    DOC_AD_LEAD_WITHDRAWAL_RULES,
    DOC_WITHDRAWAL_PROCESS,
    CEP_SETTLEMENT_PAYMENT_URL,
    INDUSTRYDOC_URL,
    DATASETDOC_URL,
    DATASET_UPLOAD_URL,
    DATASETEXAMPLEDOC_URL,
    DATASET_SEARCH_URL,
    AGREEMENT_URL,
    URL_DOC_EVALUATION_RULES,
    UPLOAD_DIGITAL_FIGTURE_ACTION,
    URL_DOC_OUTPUT_AGENT_API,
    URL_DOC_OUTPUT_AGENT_JS_SDK,
    URL_DOC_TUNING_AGENT_CONF,
    URL_DOC_TUNING_AGENT_STABLE,
    URL_DOC_TUNING_AGENT_OTHER,
    URL_DOC_DATASET_QA,
    URL_DOC_TUTORIAL_CENTER,
    COMMUNITY_QR_CODE_KEY,
    SMART_SERVICE_KEY,
    XINXIANG_HOME,
    XINXIANG_HOST,
    XINXIANG_PROXY_HOST,
    LIGHTHOUSE_RULE,
};
