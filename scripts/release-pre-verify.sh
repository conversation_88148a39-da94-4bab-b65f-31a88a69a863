#!/bin/bash
echo "# 发布前验证本地代码是否落后于远程master"

# 如果远程master分支有领先于本地分支的commit，npm run release 失败，提示需先合并远程master到目标分支
git fetch origin
DIFF_COMMIT_NUM="$(git log HEAD..origin/master --oneline | wc -l | awk '{print $1}')"

if [[ "$DIFF_COMMIT_NUM" != "0" ]]; then
    echo "\033[31m远程master分支领先于本地分支{$DIFF_COMMIT_NUM}个commit，请先合并远程master到目标分支\033[0m"

    exit 1
else
    echo "# 获取最近一次发布的语义化Tag"

    # 从远程拉取所有Tag强制覆盖本地
    git fetch --tags -f

    # 获取最近的lingjing-fe_x-x-x-x_PD_BL格式的Tag，存为变量LAST_RELEASE_TAG
    # 假设最近的Tag为：lingjing-fe_1-0-1-1_PD_BL
    LAST_RELEASE_TAG="$(git tag -l "lingjing-fe_*" | sort -V | tail -n 1)"

    # 如果LAST_RELEASE_TAG存在，在本地相同commitId上打上语义规范Tag：v1.0.1
    # 以使 semantic-release 获取到下一个语义化Tag版本
    if [[ "$LAST_RELEASE_TAG" != "" ]]; then
        # 最近一次发布的Tag（lingjing-fe_1-0-1-1_PD_BL）的 commitId
        LAST_RELEASE_TAG_COMMIT="$(git rev-list -n 1 $LAST_RELEASE_TAG)"

        # 从lingjing-fe_1-0-1-1_PD_BL截取并替换取出符合语义规范的Tag: 1.0.1
        # 即lingjing-fe_1-0-1-1_PD_BL中4位版本号1-0-1-1的前3位
        V_LAST_RELEASE_TAG=$(echo "$LAST_RELEASE_TAG" | awk -F '_' '{print $2}' | awk -F'-' '{print $1"."$2"."$3}')

        # 在本地相同commitId上打上语义规范Tag：v1.0.1
        git tag -a "v$V_LAST_RELEASE_TAG"  -m "v$V_LAST_RELEASE_TAG" $LAST_RELEASE_TAG_COMMIT -f
    fi

    exit 0
fi