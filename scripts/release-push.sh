#!/bin/bash
echo "# generate tag from package version and push changelog commit & tags"

# 查看最近一条commit header message是否匹配 "^build(release): v\d\+.\d\+.\d\+" 正则格式
# 不匹配提示需要先 npm run release 生成发布版本
git log -1 --pretty="%s" | grep "^build(release): v\d\+.\d\+.\d\+"

LAST_COMMIT_MATCH_RESULT=$?

if [ $LAST_COMMIT_MATCH_RESULT != 0 ]; then
    echo "\033[31m上线发布前，请先运行命令 npm run release 生成发布版本\033[0m"
    exit $LAST_COMMIT_MATCH_RESULT
fi

# 更改semantic-release自动生成的commit author=semantic-release-bot 为当前用户
git commit --amend --reset-author --no-edit

# push 本地 changelog commit
git push origin HEAD:refs/for/$(git branch --show-current)