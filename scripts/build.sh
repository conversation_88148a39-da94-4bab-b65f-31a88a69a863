#!/usr/bin/env bash

set -e

echo "node $(node -v)"
echo "pnpm $(pnpm -v)"

# 核心代码：编译目标
stage=${1:-offline}

revision=${AGILE_REVISION}
buildId=$(date '+%Y-%m-%d')-${revision:0:7}

rm -rf dist output

# 核心代码：替换 <buildid> 为当前构建版本，要在打包前替换
sed -i "s/<buildid>/${buildId}/g" ./src/weirwood.json
sed -i "s/<buildid>/${buildId}/g" ./src/utils/monitor/injectHtmlWeirwoodSdk.ts

NODE_ENV=development pnpm install --frozen-lockfile
NODE_ENV=production pnpm run build

# copy静态文件到dist目录
cp -r ./src/v3Jump.html ./dist
cp scripts/deploy.json ./dist
cp -r ./src/weirwood.json ./dist

mkdir output
cd dist

# 检测如果是编译线上环境，需要上传 sourcemap
if [ "$stage" = "online" ]; then
    cd assets
    # 核心代码：压缩所有 sourcemap，注意不能包含文件夹，必须扁平压缩
    tar -czf sourcemap.tar.gz *.js *.js.map
    mv sourcemap.tar.gz ../
    rm *.js.map
    cd ..

    # 核心代码：使用 npx 下载 @baidu/weirwood-cli 进行上传，这里需要注意 sourcemap.tar.gz 和 weirwood.json 在同一个目录
    npx @baidu/weirwood-cli -c weirwood.json

    # 核心代码： 删除所有 map 文件和 map 压缩包
    rm sourcemap.tar.gz

fi

# 打包压缩
tar czf ../output/bundle.tar.gz ./

cd ..

echo "build success"
