# E2E 测试目录
## 简介

E2E 测试使用 Playwright 测试框架，测试范围覆盖整个应用程序的所有核心功能的用户路径。

## 快速开始

### 首次运行安装依赖

```bash
pnpm exec playwright install
```

### 运行测试

```bash
pnpm e2e
```

### 运行 UI 模式

```bash
pnpm e2e:ui
```

### 运行 codegen 生成测试用例

```bash
pnpm e2e:codegen
```


## 目录结构

```md
.
|-- user-flows/                            # 用户交互测试用例：每一个核心模块为一个单独的文件夹
|   |-- home                               # 首页模块
|       |-- home-pom.ts                    # pom
|       |-- home.spec.ts                   # 测试用例
|   |-- dataset                            # 知识库模块
|       |-- create-pom.ts                  # 创建知识库页（dataset/create）的 pom
|       |-- create.spec.ts                 # 创建知识库页面的测试用例
|       |-- list-pom.ts                    # 知识库列表页（dataset/list）的 pom
|       |-- list.spec.ts                   # 知识库列表页的测试用例
|   |-- ...   
|-- helpers/                               # 辅助工具函数
|-- api-mocking/                           # mock api 调用
|-- results/                               # 测试结果（主要是 trace 信息）
|-- report/                                # 测试报告
```
