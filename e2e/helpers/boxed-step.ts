/**
 * @file 封装 playwright 的 test.step 方法为 @boxedStep 装饰器，方便在测试报告中更清晰地展示测试的不同步骤
 * @see https://playwright.dev/docs/api/class-test#test-step
 * <AUTHOR>
 *
 */
import {test} from '@playwright/test';

export default function boxedStep<T extends {constructor: {name: string}}>(
    target: (...args: any[]) => any,
    context: ClassMethodDecoratorContext
) {
    return function replacementMethod(this: T, ...args: any[]) {
        const name = `${this.constructor.name}.${context.name as string}`;

        return test.step(
            name,
            async () => {
                return await target.call(this, ...args);
            },
            {box: true}
        );
    };
}
