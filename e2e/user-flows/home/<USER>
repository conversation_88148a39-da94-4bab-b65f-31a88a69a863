/**
 * @file 首页 Page Object Model
 * <AUTHOR>
 *
 */
import {Page} from '@playwright/test';

export class Home {
    constructor(private readonly page: Page) {}

    /**
     * 导航到首页
     */
    async goto() {
        await this.page.goto('/');
    }

    /**
     * 点击「立即创建」按钮
     */
    async clickCreateBtn() {
        await this.page.getByText('立即创建').click();
    }

    /**
     * 点击「文档」按钮
     */
    async clickDocsBtn() {
        await this.page.getByRole('link', {name: '文档'}).click();
    }

    /**
     * 点击「管理中心」按钮
     */
    async clickManagementCenterBtn() {
        await this.page.getByRole('link', {name: '管理中心'}).click();
    }
}
