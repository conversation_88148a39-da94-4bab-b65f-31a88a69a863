/**
 * @file 首页测试用例
 * <AUTHOR>
 *
 */

import {test, expect} from '@playwright/test';
import {Home} from '@e2e/user-flows/home/<USER>';

test.describe('Home Page Tests', () => {
    test.beforeEach(async ({page}) => {
        const pom = new Home(page);
        await pom.goto();
    });

    test('should open docs page in a new tab when clicking the docs button', async ({page}) => {
        const pom = new Home(page);
        // 监听浏览器打开新标签页
        const newTabPagePromise = page.waitForEvent('popup');
        await pom.clickDocsBtn();
        const docsPage = await newTabPagePromise;
        await expect(docsPage.getByRole('link', {name: '文档中心'})).toBeVisible();
    });

    test('should navigate to plugin center when clicking the create button', async ({page}) => {
        const pom = new Home(page);
        await pom.clickCreateBtn();
        await expect(page.getByRole('banner').getByRole('link', {name: '插件中心'})).toBeVisible();
    });

    test('should navigate to plugin center when clicking the management center button', async ({page}) => {
        const pom = new Home(page);
        await pom.clickManagementCenterBtn();
        await expect(page.getByRole('banner').getByRole('link', {name: '插件中心'})).toBeVisible();
    });
});
